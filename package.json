{"name": "shop-boss-pro", "version": "1.0.0", "description": "", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "scss": "node-sass --watch -r src/public/scss -o src/public/css", "build:modern": "rimraf src/public/wc/build-modern && rollup -c src/public/wc/rollup-modern.js", "build:universal": "rimraf src/public/wc/build-universal && rollup -c src/public/wc/rollup-universal.js", "build": "npm run build:modern && npm run build:universal", "start:wc": "webpack-dev-server --progress --config src/public/wc/webpack.dev.conf.js", "build:wc": "node src/public/wc/build.js", "start:boss_board": "webpack-dev-server --progress --config src/public/wc/boss_board/webpack.dev.conf.js", "build:boss_board": "node src/public/wc/boss_board/build.js", "start:boss_inspect": "webpack-dev-server --progress --config src/public/wc/boss_inspect/webpack.dev.conf.js", "build:boss_inspect": "node src/public/wc/boss_inspect/build.js"}, "repository": {"type": "git", "url": "git+ssh://*****************/shopbossdev/shop-boss-pro.git"}, "author": "", "license": "ISC", "homepage": "https://bitbucket.org/shopbossdev/shop-boss-pro#readme", "devDependencies": {"@babel/cli": "^7.12.1", "@babel/core": "^7.12.3", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/preset-env": "^7.12.1", "@webpack-cli/serve": "^2.0.1", "autoprefixer": "^10.0.1", "babel-loader": "^8.1.0", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-module-resolver": "^4.0.0", "babel-plugin-transform-imports": "^2.0.0", "babel-preset-vue": "^2.0.2", "chalk": "^4.1.0", "content-hashed-module-ids-webpack-plugin": "^1.1.1", "copy-webpack-plugin": "^6.2.1", "css-loader": "^5.2.7", "friendly-errors-webpack-plugin": "^1.7.0", "mini-css-extract-plugin": "^1.6.2", "node-notifier": "^8.0.0", "npm-force-resolutions": "^0.0.10", "optimize-css-assets-webpack-plugin": "^6.0.1", "ora": "^5.1.0", "postcss": "^8.3.6", "postcss-import": "^13.0.0", "postcss-loader": "^4.3.0", "postcss-url": "^10.1.1", "rimraf": "^3.0.2", "rollup": "^2.32.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-copy": "^3.3.0", "rollup-plugin-filesize": "^9.0.2", "rollup-plugin-terser": "^7.0.2", "sass": "^1.66.1", "sass-loader": "^10.4.1", "semver": "^7.3.2", "shelljs": "^0.8.4", "style-loader": "^1.3.0", "terser-webpack-plugin": "^4.2.3", "url-loader": "^4.1.1", "vue-loader": "^15.9.3", "vue-style-loader": "^4.1.2", "vue-template-compiler": "^2.6.12", "webpack": "^4.46.0", "webpack-bundle-analyzer": "^4.7.0", "webpack-bundle-tracker": "^1.0.0-alpha.1", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.11.1", "webpack-manifest-plugin": "^2.2.0", "webpack-merge": "^5.2.0"}, "dependencies": {"@babel/runtime": "^7.12.1", "@dafcoe/vue-collapsible-panel": "^0.2.0", "@rollup/plugin-node-resolve": "^9.0.0", "@webcomponents/webcomponentsjs": "^2.5.0", "babel": "^6.23.0", "bootstrap-vue-dialog": "^0.1.0", "bulma": "^0.9.2", "core-js": "^3.6.5", "expander": "^0.2.0", "fabric": "^4.5.0", "i": "^0.3.6", "jquery": "^3.6.0", "lit-element": "^2.4.0", "lodash": "^4.17.21", "mdb-ui-kit": "git+https://oauth2:<EMAIL>/mdb/standard/mdb-ui-kit-pro-essential", "mdb-vue-ui-kit": "^3.1.1", "mdbvue": "^6.7.2", "moment": "^2.29.1", "node-sass": "^8.0.0", "numeral": "^2.0.6", "portal-vue": "^2.1.7", "regenerator-runtime": "^0.13.7", "systemjs": "^6.7.1", "vue": "^2.6.12", "vue-badger-accordion": "^2.0.2", "vue-bulma-accordion": "^0.5.2", "vue-image-markup": "^3.2.0", "vue-simple-accordion": "^0.1.0", "vue-slim-accordion": "^0.1.3", "vuedraggable": "^2.24.3"}, "resolutions": {"lodash": "4.17.20"}}