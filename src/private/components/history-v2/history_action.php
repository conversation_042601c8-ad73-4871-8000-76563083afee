<?php

date_default_timezone_set('America/Phoenix');

require CONN;
$shopid = $_GET['shopid'];

if (isset($_GET['sf'])) {
    $sf = $_GET['sf'];
} else {
    $sf = "";
}

$joined = false;
$shopstr = " and (shopid = '" . $shopid . "' ";
$stmt = "select shopid,joinedshopid from joinedshops where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $result = $query->get_result();
    while ($row = $result->fetch_array()) {
        $shopstr .= "|| shopid = '" . $row['joinedshopid'] . "' ";
        $joined = true;
    }
}

$shopstr .= ")";

?>

<div id="table-container" class="table-responsive">
    <table id="wiplist-table" class="sbdatatable w-100">
        <thead>
            <tr class="header">
                <th>RO</th>
                <th>Status</th>
                <th>Date</th>
                <th>Customer</th>
                <th>Phone</th>
                <th>Vehicle</th>
                <th class=" text-right">Total</th>
                <th>License</th>
                <?php if ($joined) { ?><th class="licell">Shop ID</th><?php } ?>
                <th>Issues</th>
            </tr>
        </thead>
        <tbody>
            <?php

            if ($sf != '') {
                $c = 1;
                $stmt = "select totalprts,totallbr,totalsublet,salestax,discountamt,totalfees,gp,totalro,statusdate,vin,fleetno,customerid,roid,status as stat,datein,tagnumber,customer,"
                    . " customerphone as home,customerwork as work,cellphone as cell,vehinfo,totalro,vehlicense,rotype,shopid"
                    . " from repairorders where (roid like '$sf%' or customer like '%$sf%' or vin like '%$sf%' or vehlicense like '%$sf%' or fleetno like '%$sf%' or customerphone like '$sf%'"
                    . " or customerwork like '$sf%' or cellphone like '$sf%' or vehinfo like '%$sf%' or convert(totalro,char) like '$sf%') {$shopstr} order by roid desc, datein desc limit 100";
                $result = $conn->query($stmt);

                // output data of each row
                if ($result->num_rows > 0) {
                    while ($row = $result->fetch_array()) {
                    $home = $row["home"];
                    $work = $row["work"];
                    $cell = $row["cell"];
                    $status = $row["stat"];
                    $statvar = substr($status, 0, 1);
                    $statdate = new DateTime($row["statusdate"]);
                    if (is_numeric($statvar)) {
                        $status = substr($status, 1);
                    }
                    if (strlen($row["home"]) > 0) {
                        $home = "H: (" . substr($row["home"], 0, 3) . ") " . substr($row["home"], 3, 3) . "-" . substr($row["home"], 6);
                    }
                    if (strlen($row["work"]) > 0) {
                        $work = "W: (" . substr($row["work"], 0, 3) . ") " . substr($row["work"], 3, 3) . "-" . substr($row["work"], 6);
                    }
                    if (strlen($row["cell"]) > 0) {
                        $cell = "C: &nbsp;(" . substr($row["cell"], 0, 3) . ") " . substr($row["cell"], 3, 3) . "-" . substr($row["cell"], 6);
                    }
                    if ($mymod == 1) {
                        $bgcolor = '#f5f5f5';
                    } else {
                        $bgcolor = 'white';
                    }
                    if (strtolower($row['stat']) != 'closed') {
                        $golink = "location.href='" . SBP . "ro.asp?roid=" . $row["roid"] . "'";
                    } else if (strtolower($row['stat']) == 'closed') {
                        $golink = "location.href='" . SBP . "viewro.asp?roid=" . $row["roid"] . "'";
                    }
                    if ($row['shopid'] != $shopid)
                        $golink = "location.href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?jshopid=" . $row['shopid'] . "&roid=" . $row['roid'] . "'";
                    else
                        $golink = "location.href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?roid=" . $row['roid'] . "'";
            ?>
                    <tr onclick="<?php echo $golink; ?>" class="history-row">
                        <td><strong><?php echo $row["roid"]; ?></strong></td>
                        <td><strong><?php echo $status . " "; ?>
                            </strong>
                        </td>
                        <td><strong><?php echo $statdate->format('m/d/Y'); ?>
                            </strong></td>
                        <td><strong><?php echo $row["customer"]; ?></strong></td>
                        <?php
                        $h = "";
                        if (strlen($home) > 0) {
                            $h = $h . $home;
                            if (strlen($work) > 0 || strlen($cell) > 0) {
                                $h = $h . "<br>";
                            }
                        }
                        if (strlen($work) > 0) {
                            $h = $h . $work;
                            if (strlen($cell) > 0) {
                                $h = $h . "<br>";
                            }
                        }
                        if (strlen($cell) > 0) {
                            $h = $h . $cell;
                        }

                        ?>
                        <td>
                            <div data-html="true" data-toggle="tooltip" title="<?php echo $h; ?>" style="max-width:117px;overflow:hidden;white-space: nowrap;">
                                <strong>
                                    <?php
                                    $h = "";
                                    if (strlen($home) > 0) {
                                        echo $home;
                                        if (strlen($work) > 0 || strlen($cell) > 0) {
                                            echo " ";
                                        }
                                    }
                                    if (strlen($work) > 0) {
                                        echo $work;
                                        if (strlen($cell) > 0) {
                                            echo " ";
                                        }
                                    }
                                    if (strlen($cell) > 0) {
                                        echo $cell;
                                    }

                                    ?></strong>
                            </div>
                        </td>
                        <td>
                            <strong>
                                <?php
                                if (strlen($row['fleetno']) > 0) {
                                    echo "<b>#" . $row['fleetno'] . "</b> ";
                                }
                                echo substr($row["vehinfo"], 0, 30);
                                ?>
                            </strong>
                        </td>
                        <td class="text-right"><strong><?php echo number_format($row["totalro"], 2); ?>
                            </strong></td>
                        <td><strong><?php echo $row["vehlicense"]; ?>
                            </strong></td>
                        <?php if ($joined) { ?><td class="licell"><strong><?= $row['shopid'] ?></strong></td><?php } ?>

                        <?php
                        $roid = $row["roid"];
                        $sstmt = "select complaint from complaints where shopid = '" . $row['shopid'] . "' and roid = $roid and cstatus = 'no'";
                        $sresult = $conn->query($sstmt);
                        $srow = $sresult->fetch_array();

                        if (!$srow) {
                            echo "<td></td>";
                        } else {
                        ?>
                            <td>
                                <div style="margin-left:20px;">VEHICLE ISSUES:
                                <?php
                                echo $srow['complaint'] . " | ";
                            }

                            while ($srow = $sresult->fetch_array()) {
                                echo $srow['complaint'] . " | ";
                            }

                            echo "</div>";

                            // now get the recommendations
                            $tstmt = "select id,`desc`,totalrec from recommend where shopid = '" . $row['shopid'] . "' and roid = $roid";
                            $tresult = $conn->query($tstmt);

                            while ($trow = $tresult->fetch_array()) {
                                $recid = $trow['id'];
                                $rec = $trow['desc'];
                                $totalrec = "$" . number_format($trow['totalrec'], 2);
                                echo "<div class='' style='cursor:pointer;margin-left:30px;' onclick='$(\"#ulrec$recid\").fadeToggle()'><i class='fa fa-plus'></i> <b>RECOMMENDED</b>: $totalrec - $rec</div>";
                                echo "<ul style='margin-left:40px;' id='ulrec$recid' class='nav nav-list collapse'>";

                                // now get the labor for the recommend
                                $lstmt = "select `desc`, hours,id from recommendlabor where shopid = '" . $row['shopid'] . "' and roid = $roid and recid = $recid";
                                $lresult = $conn->query($lstmt);

                                while ($lrow = $lresult->fetch_array()) {
                                    $ldesc = $lrow['desc'];
                                    $lhrs = $lrow['hours'];
                                    $id = $lrow['id'];
                                    echo "<li>LABOR: $ldesc - $lhrs</li>";
                                }

                                $pstmt = "select `partdesc`, partnumber,id,partprice,quantity from recommendparts where shopid = '" . $row['shopid'] . "' and roid = $roid and recid = $recid";
                                $presult = $conn->query($pstmt);

                                while ($prow = $presult->fetch_array()) {
                                    $pdesc = $prow['partnumber'] . ' ' . $prow['partdesc'];
                                    $phrs = $prow['quantity'] . ' @ ' . number_format($prow['partprice'], 2);
                                    echo "<li>PART: $pdesc - $phrs</li>";
                                }

                                echo "</ul>";
                            }

                            echo "</div></td>";
                                ?>
                    </tr>
            <?php
                    $tcroid = "no";
                    $c++;
                }
                }
            }
            mysqli_close($conn);
            ?>
        </tbody>
    </table>
</div>


<script>
    $(document).ready(function() {
        const groupColumn = <?= $joined ?> ? 9 : 8;

        let dtable = $(".sbdatatable").dataTable({
            responsive: true,
            retrieve: true,
            fixedHeader: true,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            searching: false,
            columnDefs: [{
                visible: false,
                targets: groupColumn
            }],
            order: [
                [groupColumn, 'asc']
            ],
            displayLength: 25,
            drawCallback: function(settings) {
                var api = this.api();
                var rows = api.rows({
                    page: 'current'
                }).nodes();
                var last = null;

                api
                    .column(groupColumn, {
                        page: 'current'
                    })
                    .data()
                    .each(function(group, i) {
                        if (last !== group) {
                            $(rows)
                                .eq(i)
                                .after(`<tr class="group"><td colspan="${<?= $joined ?> ? 9 : 8}">${group}</td></tr>`);

                            last = group;
                        }
                    });
            },
        });
    });
</script>
