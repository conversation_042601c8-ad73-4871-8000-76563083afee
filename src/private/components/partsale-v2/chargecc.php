<?php
require CONN;
require(INTEGRATIONS_PATH."/apination/postpaymentwebhook.php");
$shopid = $_GET['shopid'];
$psid = $_GET['psid'];
$t = $_GET['t'];
$amt = $_GET['amt'];
$cid = $_GET['cid'];
$pdate = date_create($_GET['pdate']);
$pdate = $pdate->format("Y-m-d");

if ($t == "cardswipe"){
	$cardstring = str_replace("%","",$_GET['cardstring']);
	$cardarray = explode("?",$cardstring);
	$sendcardstring = $cardarray[0];
	$stmt = "select merchantid,merchantpassword from company where shopid = '$shopid'";
	if ($query = $conn->prepare($stmt)){
	    $query->execute();
	   	$query->bind_result($merchantid,$merchantpassword);
	   	$query->fetch();
	    $query->close();
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
	if ($shopid != "8515"){
		$postdata = "x_Login=$merchantid&x_test_request=FALSE&x_Version=3.1&x_Tran_Key=$merchantpassword&x_ADC_Delim_Data=TRUE&x_ADC_URL=FALSE&x_type=AUTH_CAPTURE&x_market_type=2&x_device_type=10"
		. "&x_response_format=1&x_track1=$sendcardstring&x_amount=$amt";
	}else{
		$postdata = "x_Login=$merchantid&x_test_request=TRUE&x_Version=3.1&x_Tran_Key=$merchantpassword&x_ADC_Delim_Data=TRUE&x_ADC_URL=FALSE&x_type=AUTH_CAPTURE&x_market_type=2&x_device_type=10"
		. "&x_response_format=1&x_track1=$sendcardstring&x_amount=$amt";
	}
	
	$url = "https://cardpresent.authorize.net/gateway/transact.dll";
	
	$opts = array('http' =>
	    array(
	        'method'  => 'POST',
	        'header'  => 'Content-type: application/x-www-form-urlencoded',
	        'content' => $postdata
	    )
	);
	
	$context  = stream_context_create($opts);
	$result = file_get_contents($url, false, $context);
	if ($shopid != "8515"){echo $result;}
	/*
		1 = This transaction has been approved.
		2 = This transaction has been declined.
		3 = There has been an error processing this transaction.
	*/
	$rar = explode(",",$result);
	$approvalcode	 			= $rar[1];
	$subcode		        	= $rar[2];
	$reasoncode			    	= $rar[3];
	$authcode               	= $rar[4];    //6 digit approval code
	$avscode                	= $rar[6];
	$transid                	= $rar[7];    //transaction id
	
	if ($approvalcode == "1"){
		$firstdigit = substr($sendcardstring,1,1);
		switch($firstdigit){
			case "4":
				$ptype = "VISA";
				break;
			case "5":
				$ptype = "MASTERCARD";
				break;
			case "6":
				$ptype = "DISCOVER";
				break;
			case "3":
				$ptype = "AMERICAN EXPRESS";
				break;
		}

		// post payment to the ro
		$stmt = "insert into `accountpayments-ps` (shopid,psid,amt,pdate,ptype,pnumber,cid) values (?,?,?,?,?,?,?)";
		//printf(str_replace("?","%s",$stmt),$shopid,$roid,$amt,$pdate,$ptype,$transid,$cid);
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sidsssi",$shopid,$psid,$amt,$pdate,$ptype,$transid,$cid);
			if ($query->execute()){
				$conn->commit();
				postPaymentToQB("ro",$psid,$amt,$pdate,$ptype,$transid,$cid,"","");
				echo "success";
			}else{
				echo $conn->errno;
			}
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$stmt = "UPDATE ps SET balance = total - ? WHERE shopid = ? AND psid = ? LIMIT 1";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("dsi",$amt,$shopid,$psid);
			if ($query->execute()){
				$conn->commit();
				echo "success";
			}else{
				echo $conn->errno;
			}
		}else{
			echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
		
	}
	
	if ($approvalcode == "2"){
		echo "The transaction was declined";
	}
	
	if ($approvalcode == "3"){
		echo "There was an error processing the transaction.";
	}
	
}























?>
<?php if(isset($conn)){mysqli_close($conn);} ?>