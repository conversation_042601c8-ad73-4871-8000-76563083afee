<?php

ini_set("display_errors", "1");
error_reporting(E_ALL);

require_once CONNWOSHOPID;

$shopid = !empty($_REQUEST['shopid']) ? filter_var($_REQUEST['shopid'], FILTER_SANITIZE_STRING) : $_COOKIE['shopid'];
$psid = isset($_REQUEST['psid']) ? filter_var(($_REQUEST['psid']), FILTER_SANITIZE_STRING) : "";
$price = isset($_REQUEST['price']) ? filter_var(($_REQUEST['price']), FILTER_SANITIZE_STRING) : "";


$stmt = "select logo, defaulttaxrate, barno, UCASE(companyname), UCASE(companyaddress), UCASE(companycity), UCASE(companystate), UCASE(companyzip), companyphone, COALESCE(psdisclosure, ''), hst, gst, pst, qst, chargehst, chargepst, chargegst, chargeqst, timezone, showpartnumberonprintedps  from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($logo, $trate, $licnum, $ss, $sa, $companycity, $companystate, $companyzip, $sph, $ps, $hst, $gst, $pst, $qst, $chargehst, $chargepst, $chargegst, $chargeqst, $timezone, $showpartnumberonprintedps);
    $query->fetch();
    $query->close();
} else {
    echo "Prepare failed " . $conn->error;
}

$scsz = $companycity . ", " . $companystate . ". " . $companyzip;

$cantax = 0;
if ($chargehst == "yes" && $hst > 0) {
    $cantax = doubleval($cantax + $hst);
}

if ($chargegst == "yes" && $gst > 0) {
    $cantax = doubleval($cantax + $gst);
}

if ($chargepst == "yes" && $pst > 0) {
    $cantax = doubleval($cantax + $pst);
}

if ($chargeqst == "yes" && $qst > 0) {
    $cantax = doubleval($cantax + $qst);
}

$stmt = "SELECT psid, cid, psdate, comments, ponumber, status, statusdate, closed, writer, tax, canadiantax FROM ps WHERE shopid = ? AND psid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $psid);
    $query->execute();
    $query->bind_result($psid, $cid, $pdate, $c, $po, $status, $statusdate, $closed, $writer, $tax, $canadiantax);
    $query->fetch();
    $query->close();
}

// Now get the customer info
$stmt = "SELECT lastname, firstname, address, city, state, zip, homephone, workphone, cellphone, userdefined2, userdefined3 FROM customer WHERE shopid = ? AND customerid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $cid);
    $query->execute();
    $query->bind_result($lname, $fname, $addr, $city, $state, $zip, $homephone, $workphone, $cellphone, $treatyno, $pstexempt);
    if ($query->fetch()) {
        $name = $lname . ", " . $fname;
        $csz = $city . ", " . $state . ". &nbsp;" . $zip;
        $ph = $homephone;
        $wp = $workphone;
        $cp = $cellphone;
    }
    $query->close();
}

$statusdate = date("m/d/Y", strtotime($statusdate));
$strPhone = "";
if (strlen($ph) >= 7) {
    $strPhone = "Home: " . formatPhone($ph);
    if (strlen($wp) >= 7) {
        $strPhone .= "<br>Work: " . formatPhone($wp);
        if (strlen($cp) >= 7) {
            $strPhone .= "<br>Cell: " . formatPhone($cp);
        }
    }
}

if (strlen($strPhone) == 0 && strlen($wp) >= 7) {
    $strPhone = "Work: " . formatPhone($wp);
    if (strlen($cp) >= 7) {
        $strPhone .= "<br>Cell: " . formatPhone($cp);
    }
}
if (strlen($strPhone) == 0 && strlen($cp) >= 7) {
    $strPhone = "Cell: " . formatPhone($cp);
}

if ($shopid == "4256") {
    $pdate = $statusdate;
}

$surchargemessage = "";

$surchargestmt = "select surchargemsg from `accountpayments-ps` where shopid = ? and psid = ? and surchargemsg is not null and surchargemsg != ''";
if ($squery = $conn->prepare($surchargestmt)) {
    $squery->bind_param("si", $shopid, $psid);
    $squery->execute();
    $squery->bind_result($surchargemessage);
    $squery->fetch();
    $squery->close();
}
    $rd = "0";
    $pcost = "0";

?>
<!DOCTYPE html>
<head>
    <style>
        A {
            text-decoration: none;
        }

        A:link {
            text-decoration: none;
        }

        A:visited {
            text-decoration: none;
        }

        A:hover {
            text-decoration: none;
        }

        p, td, th, li, body {
            font-size: 10pt;
        }

        .style2 {
            color: #000000;
            background-color: #C0C0C0;
        }

        .style3 {
            text-align: right;
        }

        .style4 {
            color: #000000;
            text-align: right;
            background-color: #C0C0C0;
        }

        .style5 {
            text-align: right;
        }

        .style6 {
            color: #000000;
            text-align: right;
            background-color: #C0C0C0;
        }

        .style9 {

            font-size: medium;
        }

        .style10 {

            font-size: medium;
        }

        .innerBox2 {
            background-color: #FFFFFF;
            text-align: center;
        }
    </style>
</head>
<body>
<table cellpadding="0" cellspacing="0" style="width: 100%">
    <tr>
        <td class="style10" style="width: 30%; height: 35px;"><?php
            if ($shopid != "3029") {
                echo $ss . "<br>";
                echo $sa . "<br>";
                echo $scsz . "<br>";
                echo formatPhone($sph) . "<br>";
            }
            ?>
        </td>
        <td class="style10" style="width: 40%; height: 35px; text-align: center">
            <?php
            if (strlen($logo) > 2) {
                $serverName = $_SERVER["SERVER_NAME"];
                $imageSrc = "upload/" . $shopid . "/" . $logo;

                switch ($shopid) {
                    case "1526":
                        echo "<img width='200' height='109' src='$imageSrc' />";
                        break;
                    case "1734":
                        echo "<img width='250' height='100' src='$imageSrc' />";
                        break;
                    case "2839":
                        echo "<img width='150' height='110' src='$imageSrc' />";
                        break;
                    case "4075":
                        echo "<img max-height='80' src='$imageSrc' />";
                        break;
                    case "9177":
                        echo "<img style='width:250px; height:110px;' src='http://$serverName/sbp/$imageSrc' />";
                        break;
                    case "9833":
                        echo "<img style='width:250px; height:30px;' src='http://$serverName/sbp/$imageSrc' />";
                        break;
                    case "8829":
                        echo "<img style='width:200px; height:80px;' src='http://$serverName/sbp/$imageSrc' />";
                        break;
                    case "10118":
                        echo "<img style='width:150px; height:80px;' src='http://$serverName/sbp/$imageSrc' />";
                        break;
                    case "15194":
                        echo "<img style='width:150px; height:80px;' src='http://$serverName/sbp/$imageSrc' />";
                        break;
                    case "12871":
                        echo "<img style='width:250px; height:90px;' src='http://$serverName/sbp/$imageSrc' />";
                        break;
                    case "19220":
                        echo "<img style='width:250px; height:90px;' src='http://$serverName/sbp/$imageSrc' />";
                        break;
                    case "10077":
                        echo "<img style='max-height:90px; max-width:150px;' src='http://$serverName/sbp/$imageSrc' />";
                        break;
                    case "19175":
                        echo "<img style='width:250px; height:90px;' src='http://$serverName/sbp/$imageSrc' />";
                        break;
                    default:
                        echo "<img style='max-height:90px; max-width:150px;' src='http://$serverName/sbp/$imageSrc' />";
                        break;
                }
            } else {
                echo "<img style='max-height:90px;' src='newimages/invoicelogo.png' />";
            }
            ?>
        </td>
        <td align="center" class="style9" style="width: 30%; height: 35px;" valign="top">

            Invoice #PS-<?php echo $psid; ?>

            <?php
            if (strlen($licnum) > 0) {
                echo "<br>Number: " . $licnum;
            }

            if ($shopid == "10077") {
                $rowspan = "5";
            } else {
                $rowspan = "4";
            }
            ?>

        </td>
    </tr>
    <tr>
        <td colspan="3">
            <table cellspacing="0" cellpadding="2" style="width: 98%">
                <tr>
                    <td style="border:1px black solid; width: 20%" rowspan="<?php echo $rowspan; ?>">Customer</td>
                    <td style="width: 40%;border:1px black solid" rowspan="<?php echo $rowspan; ?>">
                        <?php
                        echo $name . "<br>" . $addr . "<br>" . $csz . "<br>" . $strPhone . "<br><br>";

                        if ($shopid == "19394") {
                            if ($treatyno != "") {
                                echo "Treaty No. " . $treatyno . "<br>";
                            }
                            if ($pstexempt != "") {
                                echo "PST Exempt: " . $pstexempt;
                            }
                        }
                        ?>
                    </td>
                    <td style="border:1px black solid; width: 15%" valign="top">Date</td>
                    <td style="width: 25%;border:1px black solid" valign="top"><?php echo date('m/d/Y', strtotime($pdate)); ?></td>
                </tr>
                <?php
                if ($shopid == "10077") {
                    ?>
                    <tr>
                        <td style="border:1px black solid" valign="top">
                            Date Closed
                        </td>
                        <td style="border:1px black solid" valign="top">
                            <?php
                            if ($status == "Closed") {
                                echo $statusdate;
                            }
                            ?>&nbsp;
                        </td>
                    </tr>
                    <?php
                }
                ?>
                <tr>
                    <td style="border:1px black solid">Comments
                    </td>
                    <td style="border:1px black solid" valign="top"><?php echo strtoupper($c); ?>&nbsp;</td>
                </tr>
                <tr>
                    <td style="border:1px black solid" valign="top">Service Writer
                    </td>
                    <td style="border:1px black solid" valign="top"><?php echo strtoupper($writer); ?>
                        &nbsp;
                    </td>
                </tr>
                <tr>
                    <td style="border:1px black solid" valign="top">PO #
                    </td>
                    <td style="border:1px black solid" valign="top"><?php echo $po; ?>&nbsp;</td>
                </tr>
            </table>

        </td>
    </tr>
</table>
<?php
$ttlprice = 0;
$taxable = 0;
$nontax = 0;
$stmt = "select * from psdetail where shopid = ? and psid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $psid);
    $query->execute();
    $results = $query->get_result();
    $query->close();
}
if ($results->num_rows > 0){
?>
<p></p>
<table style="width: 100%" cellspacing="0" cellpadding="0">
    <thead>
    <tr>
        <?php if (strtolower($showpartnumberonprintedps) == "yes") { ?>
            <td class="style2"><strong>Part Number</strong></td>
        <?php } ?>
        <td class="style2"><strong>Description</strong></td>
        <td class="style4"><strong>Qty<?php if ($price != "no") { ?>/Price<?php } ?></strong></td>
        <?php if ($price != "no") { ?>
            <td class="style6"><strong>Net</strong></td>
        <?php } ?>
    </tr>
    </thead>
    <tbody>
    <?php
    while ($rs = $results->fetch_assoc()) {

        $ttlprice = $ttlprice + doubleval($rs["ext"]);
        $d = ($rs["qty"] * doubleval($rs["price"])) - doubleval($rs["ext"]);
        $rd = $rd + $d;
        $pcost += doubleval($rs["cost"]);
        ?>
        <tr>
            <?php if (strtolower($showpartnumberonprintedps) == "yes") { ?>
                <td ><?= strtoupper($rs["pnumber"]) ?>&nbsp;</td>
            <?php } ?>
            <td ><?= strtoupper($rs["desc"]) ?>&nbsp;</td>
            <td  class="style3"><?= $rs["qty"] ?><?php if ($price != "no") { ?>&nbsp;@&nbsp;<?= asDollars($rs["price"]) ?><?php } ?>
                <?php
                if (strtolower($rs["tax"]) == "yes") {
                    $taxable += doubleval($rs["ext"]);
                } else {
                    $nontax += doubleval($rs["ext"]);
                }
                ?>
            </td>
            <?php if ($price != 'no') { ?>
                <td  class="style5"><?= asDollars($rs["ext"]) ?>&nbsp;</td>
            <?php } ?>
        </tr>
        <?php
    }
    }
    ?>

    </tbody>
</table>
<?php
$ttl = $ttlprice + $tax;
$dtaxable = asDollars($taxable);
$dnontax = asDollars($nontax);
$dtax = asDollars($tax);
$dttl = asDollars($ttl);
$dttlprice = asDollars(floatval($taxable) + floatval($nontax));

if (!is_numeric($pcost) || is_null($pcost) || strlen($pcost) == 0) {
    $pcost = "0";
}
if (!is_numeric($ttlprice) || is_null($ttlprice) || strlen($ttlprice) == 0) {
    $ttlprice = "0";
}
if (!is_numeric($rd) || is_null($rd) || strlen($rd) == 0) {
    $rd = "0";
}
if (!is_numeric($ttl) || is_null($ttl) || strlen($ttl) == 0) {
    $ttl = "0";
}
?>
<h5>Payments Received:</h5>
<table style="width: 100%">
    <?php
    $totalsurcharge = 0;
    $tpmts = 0;
    $stmt = "select pnumber, amt, surcharge, last4, pdate, ptype from `accountpayments-ps` where shopid = ? and psid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $psid);
        $query->execute();
        $query->bind_result($pnumber, $amt, $surcharge, $last4, $pdate, $ptype);
        $query->store_result();
        if ($query->num_rows > 0) {
            while ($query->fetch()) {

                $pdate = date("m/d/Y", strtotime($pdate));
                $pnum = "";
                if (strlen($pnumber) == 16) {
                    $pnum = "";
                } else {
                    $pnum = $pnumber;
                }
                $tpmts += $amt;

                $payamt = asDollars($amt + $surcharge);
                if ($surcharge > 0) {
                    $payamt = $payamt . "*";
                    $totalsurcharge = doubleval($totalsurcharge) + doubleval($surcharge);
                }

                if (left($pnumber, 3) == "360" && strpos($pnumber, "~") !== false) {
                    $payar = explode("~", $pnumber);
                    $apprcode = $payar[sizeof($payar) - 1];
                    if ($apprcode != "") {
                        $pnum = "AuthCode " . $apprcode;
                    }
                }

                if (!empty($last4)) {
                    $pnum .= " - CC# " . $last4;
                }
                ?>
                <tr>
                    <td><?= $pdate ?>&nbsp;</td>
                    <td><?= $ptype ?>&nbsp;</td>
                    <td><?= $pnum ?>&nbsp;</td>
                    <td style="text-align:right"><?= $payamt ?>&nbsp;</td>
                </tr>
                <?php
            }
        } else {
            $tpmts = 0;
            ?>
            <tr>
                <td colspan="4">No Payments received&nbsp;</td>
            </tr>
            <?php
        }
        $query->close();
    }
    ?>
</table>
<?php
$hst_val = 0;
$pst_val = 0;
$gst_val = 0;
$qst_val = 0;
$cantaxstr = "";

if (strlen($canadiantax) > 0) {
    $cantxarr = explode(",", $canadiantax);
    $hst_val = floatval($cantxarr[0]);
    $pst_val = floatval($cantxarr[1]);
    $gst_val = floatval($cantxarr[2]);
    $qst_val = floatval($cantxarr[3]);

    if ($hst_val > 0) {
        $hst_amount = $taxable * ($hst_val / 100);
        $cantaxstr = "<tr>
<td style='height:25px;width:75%; text-align:right'>HST " . number_format($hst_val) . "%</td>
<td style='width:20%;text-align:right'>
    " . asDollars($hst_amount) . "
</td></tr>
";
    }

    if ($pst_val > 0) {
        $pst_amount = $taxable * ($pst_val / 100);
        $cantaxstr .= "<tr>
<td style='height:25px;width:75%; text-align:right'>PST " . number_format($pst_val) . "%</td>
<td style='width:20%;text-align:right'>
    " . asDollars($pst_amount) . "
</td></tr>
";
    }

    if ($gst_val > 0) {
        $gst_amount = $taxable * ($gst_val / 100);
        $cantaxstr .= "<tr>
<td style='height:25px;width:75%; text-align:right'>GST " . number_format($gst_val) . "%</td>
<td style='width:20%;text-align:right'>
    " . asDollars($gst_amount) . "
</td></tr>
";
    }

    if ($qst_val > 0) {
        $qst_amount = $taxable * ($qst_val / 100);
        $cantaxstr .= "<tr>
<td style='height:25px;width:75%; text-align:right'>QST " . number_format($qst_val) . "%</td>
<td style='width:20%;text-align:right'>
    " . asDollars($qst_amount) . "
</td></tr>
";
    }
}
?>

<table id="footer" style="width: 100%; border: 1pt solid black" cellspacing="2" cellpadding="2">
    <?php if ($price != "no") { ?>
        <tr>
            <td style="width:75%; text-align:right">Total Taxable</td>
            <td style="width:25%;text-align:right;">
                <?= $dtaxable ?>
            </td>
        </tr>
        <tr>
            <td style="text-align:right">Total Non-Taxable</td>
            <td style="text-align:right">
                <?= $dnontax ?>
            </td>
        </tr>
        <tr>
            <td style="text-align:right">Subtotal</td>
            <td style="text-align:right">
                <?= $dttlprice ?>
            </td>
        </tr>
        <?php if ($shopid == "20487") { ?>
            <tr>
                <td>
                    <?= $cantaxstr ?>
                </td>
            </tr>
        <?php } ?>
        <tr>
            <td style="text-align:right">Sales Tax</td>
            <td style="text-align:right">
                <?= $dtax ?>
            </td>
        </tr>
        <tr>
            <td style="text-align:right">Total</td>
            <td style="text-align:right;">
                <?= $dttl ?>
            </td>
        </tr>
        <tr>
            <td style="text-align:right">Total Payments</td>
            <td style="text-align:right">
                <?= asDollars($tpmts) ?>
            </td>
        </tr>
        <?php if ($totalsurcharge != 0) { ?>
            <tr>
                <td style="text-align:right">Credit Card Fee</td>
                <td style="text-align:right">
                    <?= asDollars($totalsurcharge) ?>*
                </td>
            </tr>
        <?php } ?>
        <tr>
            <td style="text-align:right;">
                Balance
            </td>
            <td style="text-align:right;">
                <?= asDollars($ttl - $tpmts) ?>
            </td>
        </tr>
        <tr>
            <td style="font-size:large;font-weight:bold;text-align:left">X</td>
        </tr>
    <?php } ?>
    <tr aria-colspan="2">
        <td style="font-size:10px;"><?= $ps ?? "" ?></td>
    </tr>


    <?php if ($surchargemessage != "") { ?>
        <tr>
            <td style="font-size: 12px; text-align: left;position: fixed; bottom: 0px; width: 100%; padding-top: 10%">
                *<?= $surchargemessage ?? "" ?>
            </td>
        </tr>
    <?php } ?>
</table>
<div style="width: 100%; padding-top: 20%; text-align: right">
    <br><br>
    Date :
    <?php
    $lcts = localTimeStamp($shopid);
    echo date("m/d/Y h:i:s A", strtotime($lcts))
    ?>
</div>

</body>

</html>