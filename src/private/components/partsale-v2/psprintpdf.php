<?php

include INTEGRATIONS_PATH . "/TCPDF/tcpdf.php";

$shopid = !empty($_REQUEST['shopid']) ? filter_var($_REQUEST['shopid'], FILTER_SANITIZE_STRING) : $_COOKIE['shopid'];
$psid = isset($_REQUEST['psid']) ? filter_var($_REQUEST['psid'], FILTER_SANITIZE_STRING) : "";
$fullpath = isset($_REQUEST['fullpath']) ? filter_var($_REQUEST['fullpath'], FILTER_SANITIZE_STRING) : "no";

$newfilename = "TCPS_" . $shopid . "_" . $psid . "_" . date('Y') . "_" . date('n') . "_" . date('j') . "_" . date('H') . "_" . date('i') . "_" . date('s') . "_" . time() . ".pdf";

$save_path = $_SERVER['DOCUMENT_ROOT'] . DS . "temp" . DS . $newfilename;

if ($shopid == "2704") {

    $path = "https://" . $_SERVER["SERVER_NAME"] . "/partsale/psprint.php?shopid=$shopid&psid=$psid&price=no";

    $websiteContent = file_get_contents($path);

    $path2 = "https://" . $_SERVER["SERVER_NAME"] . "/partsale/psprint.php?shopid=$shopid&psid=$psid";

    $websiteContent2 = file_get_contents($path2);

    $pdf = new TCPDF();
    $pdf->setPrintHeader(false);
    $pdf->AddPage();
    $pdf->writeHTML($websiteContent);
    $pdf->AddPage();
    $pdf->writeHTML($websiteContent2);
    $pdf->Output($save_path, 'F');

} else {
    $path = "https://" . $_SERVER["SERVER_NAME"] . "/partsale/psprint.php?shopid=$shopid&psid=$psid";

    $websiteContent = file_get_contents($path);

    $pdf = new TCPDF();
    $pdf->setPrintHeader(false);
    $pdf->AddPage();

    $pdf->writeHTML(utf8_encode($websiteContent));
    $pdf->Output($save_path, 'F');
}
if ($fullpath == "yes") {
    echo $save_path;
} else {
    echo $newfilename;
}
?>
