<?php
$component = "partsale-v2";
$shopid = $_COOKIE['shopid'];
$applyDiscount = $_COOKIE['applydiscounts'] ? strtolower($_COOKIE['applydiscounts']) :'no';
require CONN;

include getHeadGlobal($component);

$psid = $_GET['psid'];
$psdid = $_GET['psdid'];

$stmt = "select soldby,overridematrix,pnumber,`desc`,price,qty,discount,ext,supplier,partcode,cost,tax,category,partid,ponumber,invoicenumber "
    . "from psdetail where shopid = ? and psdid = ?";
//printf(str_replace("?","%s",$stmt),$shopid,$partid);
$overridematrix = "no";
$pnumber = "";
$desc = "";
$price = 0;
$qty = 0;
$discount = 0;
$ext = 0;
$supplier = "";
$partcode = "";
$cost = 0;
$tax = "";
$category = "";
$partid = 0;
$ponumber = "";
$invoicenumber = "";
if ($query = $conn->prepare($stmt)) {

    $query->bind_param("ss", $shopid, $psdid);
    $query->execute();
    $query->store_result();
    $pn_num_rows = $query->num_rows;
    //echo $pn_num_rows;
    if ($pn_num_rows > 0) {
        $query->bind_result($soldby, $overridematrix, $pnumber, $desc, $price, $qty, $discount, $ext, $supplier, $partcode, $cost, $tax, $category, $partid, $ponumber, $invoicenumber);
        $query->fetch();
    }

    $query->close();

} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$net = $price - ($price * ($discount / 100));

$stmt = "select usepartsmatrix from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    //echo "rotype rows:".$num_roid_rows."<BR>";
    if ($num_roid_rows > 0) {
        $query->bind_result($usematrix);
        $query->fetch();
    } else {
        $usematrix = "no";
    }
    $query->close();

} else {
    echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
}

?>
<body>

<form name="mainform" id="mainform">
    <input type="hidden" name="shopid" id="shopid" value="<?php echo $shopid; ?>">
    <input type="hidden" name="psid" id="psid" value="<?php echo $psid; ?>">
    <input type="hidden" name="psdid" id="psdid" value="<?php echo $psdid; ?>">
    <div class="container-fluid">
        <div class="content-container">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-row mb-4">
                        <select class="select" id="overridematrix" name="overridematrix">
                            <option <?php if (strtolower($overridematrix) == "no") {
                                echo "selected";
                            } ?> value="no">No
                            </option>
                            <option <?php if (strtolower($overridematrix) == "yes") {
                                echo "selected";
                            } ?> value="yes">Yes
                            </option>
                        </select>
                        <label class="form-label select-label" id="vinfloatinglabel" for="material-text2">Override Parts Matrix</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" tabindex="1" type="text" id="partnum" value="<?php echo strtoupper($pnumber); ?>" name="PartNumber">
                        <label class="form-label" id="vinfloatinglabel" for="material-text2">Part Number</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" tabindex="2" type="text" id="partdesc" value="<?php echo strtoupper($desc); ?>" name="PartDesc">
                        <label class="form-label" id="vinfloatinglabel" for="material-text2">Part Description</label>
                    </div>
                    <div class="form-row mb-4">
                        <select onchange="calcPrices()" class="select" name="PartCategory" id="PartCategory">
                            <option selected value="<?php echo strtoupper($category); ?>"><?php echo strtoupper($category); ?></option>
                            <?php
                            $stmt = "select distinct category c from category where shopid = ? and category != ? order by displayorder";
                            $matrix = strtolower($_COOKIE['matrix']);
                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("ss", $shopid, $category);
                                $query->execute();
                                $result = $query->get_result();
                                $query->store_result();
                                $numrows = $result->num_rows;
                                if ($numrows > 0) {
                                    while ($row = $result->fetch_array()) {

                                        echo "<option value='" . strtoupper($row['c']) . "'>" . strtoupper($row['c']) . "</option>";
                                    }
                                } else {
                                    echo "<option value='none'>No Suppliers Entered</option>";
                                }
                            }
                            ?>
                        </select>

                        <label class="form-label select-label" id="vinfloatinglabel" for="material-text2">Matrix Category</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" onblur="calcPrices()" tabindex="4" type="text" id="qty" name="qty" value="<?php echo $qty; ?>" name="qty">
                        <label class="form-label" id="discountfloatinglabel" for="material-text2">Quantity</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" onblur="calcPrices()" tabindex="5" type="text" id="PartCost" value="<?php echo number_format($cost, 2, '.', ''); ?>" name="PartCost">
                        <label class="form-label" id="discountfloatinglabel" for="material-text2">Shop Cost</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" onblur="calcPrices()" tabindex="6" type="text" id="PartPrice" value="<?php echo number_format($price, 2, '.', ''); ?>" name="PartPrice">
                        <label class="form-label" id="discountfloatinglabel" for="material-text2">Selling Price</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" onblur="calcPrices()" onkeyup="calcPrices()" tabindex="7" type="text" id="Discount" value="<?php echo number_format($discount, 2, '.', ''); ?>" name="Discount" <?= ($applyDiscount == "yes" ? "" : "readonly") ?> >
                        <label class="form-label" id="dfloatinglabel" for="material-text2">Discount %</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" onblur="calcPrices()" tabindex="8" type="text" id="net" value="<?php echo number_format($net, 2, '.', ''); ?>" name="net">
                        <label class="form-label" id="netfloatinglabel" for="material-text2">Net Selling Price</label>
                    </div>
                    <div class="form-row mb-4">
                        <select class="select" tabindex="8" type="text" id="soldby" name="soldby">
                            <option value=''>NONE</option>
                            <?php
                            $stmt = "select employeelast,employeefirst from employees where active = 'yes' and shopid = '$shopid' order by employeelast";
                            if ($query = $conn->prepare($stmt)) {
                                $query->execute();
                                $r = $query->get_result();
                                while ($rs = $r->fetch_assoc()) {
                                    if (isset($soldby)) {
                                        if ($rs['employeelast'] . ", " . $rs['employeefirst'] == $soldby) {
                                            $s = " selected ";
                                        } else {
                                            $s = "";
                                        }
                                    } else {
                                        $s = "";
                                    }
                                    echo "<option $s value='" . $rs['employeelast'] . ", " . $rs['employeefirst'] . "'>" . $rs['employeelast'] . ", " . $rs['employeefirst'] . "</option>";
                                }
                            }
                            ?>
                        </select>
                        <label class="form-label select-label" id="soldbylabel" for="soldby">Sold By</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-row mb-4">
                        <select onkeyup="" class="select" name="PartCode" id="PartCode">
                            <option selected value="<?php echo strtoupper($partcode); ?>"><?php echo strtoupper($partcode); ?></option>
                            <?php
                            $stmt = "select codes from codes where shopid = ? and codes != ? order by codes asc";
                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("ss", $shopid, $partcode);
                                $query->execute();
                                $result = $query->get_result();
                                $query->store_result();
                                $numrows = $result->num_rows;
                                if ($numrows > 0) {
                                    while ($row = $result->fetch_array()) {
                                        echo '<option value="' . strtoupper($row['codes']) . '">' . strtoupper($row['codes']) . '</option>';
                                    }
                                } else {
                                    echo '<option value="New">New</option>';
                                }
                            }
                            ?>
                        </select>
                        <label class="form-label select-label" id="discountfloatinglabel" for="material-text2">Part Code</label>
                    </div>
                    <div class="form-row mb-4">
                        <select onkeyup="" class="select" data-mdb-filter="true" name="PartSupplier" id="PartSupplier">
                            <option selected value="<?php echo strtoupper($supplier); ?>"><?php echo strtoupper($supplier); ?></option>
                            <?php
                            $supplier = "";
                            $stmt = "select suppliername s from supplier where suppliername != ? and shopid = ? order by displayorder";
                            //printf ( str_replace('?',"'%s'",$stmt),$Supplier,$shopid);
                            $matrix = strtolower($_COOKIE['matrix']);
                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("ss", $supplier, $shopid);
                                $query->execute();
                                $result = $query->get_result();
                                $query->store_result();
                                $numrows = $result->num_rows;
                                //echo "num rows:".$numrows;
                                if ($numrows > 0) {
                                    while ($row = $result->fetch_array()) {

                                        echo "<option value='" . strtoupper($row['s']) . "'>" . strtoupper($row['s']) . "</option>";
                                    }
                                } else {
                                    echo "<option value='none'>No Suppliers Entered</option>";
                                }
                            }
                            ?>
                        </select>

                        <label class="form-label select-label" id="discountfloatinglabel" for="material-text2">Supplier</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" tabindex="11" type="text" id="ponum" value="<?php echo $ponumber; ?>" name="ponum">
                        <label class="form-label" id="discountfloatinglabel" for="material-text2">PO #</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" tabindex="13" type="text" id="invoicenumber" value="<?php echo $invoicenumber; ?>" name="invoicenumber">
                        <label class="form-label" id="discountfloatinglabel" for="material-text2">Part Invoice Number</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" tabindex="14" type="text" id="extprice" value="<?php echo number_format($ext, 2, '.', ''); ?>" name="extprice">
                        <label class="form-label" id="extpricefloatinglabel" for="material-text2">Extended Price</label>
                    </div>
                    <div class="form-row mb-4">
                        <select onkeyup="" class="select" name="tax" id="tax">
                            <?php
                            if (strtolower($tax) == "yes") {
                                $ys = ' selected="selected" ';
                                $ns = '';
                            } else {
                                $ys = '';
                                $ns = ' selected="selected" ';
                            }
                            ?>
                            <option <?php echo $ys; ?> value="yes">Yes</option>
                            <option <?php echo $ns; ?> value="no">No</option>
                        </select>
                        <label class="form-label select-label" id="totalfloatinglabel" for="material-text2">Taxable</label>
                    </div>
                     <?php
                        $core = 0;
                        $stmt = "select corecharge from cores where shopid = ? and roid = ? and partnumber = ?";
                        if ($query = $conn->prepare($stmt)){
                            $query->bind_param("sis",$shopid,$psid,$pnumber);
                            $query->execute();
                            $query->store_result();
                            $numrows = $query->num_rows();
                            if ($numrows > 0){
                                $query->bind_result($core);
                                $query->fetch();
                            }
                            
                        }


                        if ($core > 0)
                        {   
                            $ocore = $core;
                            $core = $core * $qty;
                            $core = asdollars($core,2);
                            echo "<span id='corespan'><b>Core Charge:</b> $core (".$qty." @ ".asdollars($ocore,2).")</span>";
                        }
                        ?>
                                
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 text-center">
                    <button type="button" onclick="calcPrices();savePart('no')" class="btn btn-primary btn-md">Save</button>
                </div>
                <input type="hidden" id="origpartnumber" name="origpartnumber" value="<?php echo $pnumber; ?>">
                <input type="hidden" id="origqty" name="origqty" value="<?php echo $qty; ?>">
            </div>
        </div>
    </div>
</form>
<!-- END Main Container -->

<!-- Footer -->
<!-- END Footer -->
</div>
<!-- END Page Container -->

<?php
$component = "";
include getScriptsGlobal('');
?>
<script>

    function savePart(addanother) {
        setTimeout(function () {
            partnumber = $('#partnum').val()
            partdesc = $('#partdesc').val()
            partcat = $('#PartCategory').val()
            qty = $('#qty').val()
            partcost = $('#PartCost').val()
            partprice = $('#PartPrice').val()
            partnet = $('#net').val()
            extprice = $('#extprice').val()

            if (partnumber == "") {
                sbalert("Part Number is a required field");
                $('#partnum').focus();
                return
            }
            if (partdesc == "") {
                sbalert("Part Description is a required field");
                $('#partdesc').focus();
                return
            }
            if (partcat == "") {
                sbalert("Part Category is a required field");
                $('#PartCategory').focus();
                return
            }
            if (!$.isNumeric(qty) || qty == '0') {
                sbalert("Quantity is a required field");
                $('#qty').focus();
                return
            }
            if (!$.isNumeric(partcost)) {
                sbalert("Part Cost is a required field");
                $('#PartCost').focus();
                return
            }
            if (!$.isNumeric(partprice)) {
                sbalert("Part Price is a required field");
                $('#PartPrice').focus();
                return
            }
            if (!$.isNumeric(partnet)) {
                sbalert("Net Selling Price is a required field");
                $('#net').focus();
                return
            }
            if (!$.isNumeric(extprice)) {
                sbalert("Extended Price is a required field");
                $('#extprice').focus();
                return
            }

            // post the information
            ds = $('#mainform').serialize()
            showLoader();
            $.ajax({
                data: ds,
                type: "get",
                url: "editpartaction.php",
                success: function (r) {
                    if (r == "success") {

                        parent.location.reload()

                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    hideLoader();
                },

            });
        }, 500);
    }

    $(document).ready(function () {
        let qty_input = document.getElementById("qty");
        qty_input.focus();
        qty_input.select()
    });

    <?php
    if (strtolower($usematrix) == "yes"){
    ?>
    function calcPrices() {

        if ($('#partnum').val() == "JOB") {
            if (parseFloat($('#qty').val()) > 0 && parseFloat($('#PartPrice').val()) > 0) {
                if (parseFloat($('#Discount').val()) > 0) {
                    disc = parseFloat($('#Discount').val()) / 100
                    qty = $('#qty').val()
                    extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                    extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())) * disc).toFixed(2)
                    baseprice = $('#PartPrice').val()
                    discamt = baseprice * disc
                    netprice = ($('#PartPrice').val() - discamt)
                    $('#net').val(netprice.toFixed(2)).addClass("active")
                    $('#extcost').val(extcost).addClass("active")
                    $('#discountamt').val(discamt.toFixed(2)).addClass("active")
                    $('#extprice').val((netprice * qty).toFixed(2)).addClass("active")
                    $('#dfloatinglabel').html("Discount % " + "<span id='discpercentlabel text-primary ms-3'>(In Dollars $" + discamt.toFixed(2) + " ea.)</span>")
                } else {
                    qty = $('#qty').val()
                    extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                    extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val()))).toFixed(2)
                    baseprice = parseFloat($('#PartPrice').val())
                    //discamt = baseprice * disc
                    netprice = parseFloat($('#PartPrice').val())
                    $('#net').val(baseprice.toFixed(2)).addClass("active")
                    $('#extcost').val(extcost).addClass("active")
                    //$('#discountamt').val(discamt.toFixed(2))
                    $('#extprice').val((netprice * qty).toFixed(2)).addClass("active")
                }
            }
        }

        <?php

        $stmt = "select category,factor,start,end from category where shopid = ? order by Category, Start";

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $result = $query->get_result();
            $rs = array();
            while ($row = $result->fetch_assoc()) {
                $rs[] = $row;
            }
            echo "var clist = " . json_encode($rs) . "\r\n";
        }
        ?>
        if ($('#overridematrix').val() == "yes") {
            // regular calculations
            /*if (parseFloat($('#qty').val()) > 0 && parseFloat($('#PartPrice').val()) > 0 && parseFloat($('#PartCost').val()) > 0){
                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                $('#extcost').val(extcost)
                $('#extprice').val(extprice)
                $('#netfloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
               $('#extcostfloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
               $('#extpricefloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
            }*/
            if (parseFloat($('#qty').val()) > 0 && parseFloat($('#PartPrice').val()) > 0 && parseFloat($('#PartCost').val()) > 0) {
                if (parseFloat($('#Discount').val()) > 0) {

                    disc = parseFloat($('#Discount').val()) / 100
                    qty = $('#qty').val()
                    extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                    extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())) * disc).toFixed(2)
                    baseprice = $('#PartPrice').val()
                    discamt = baseprice * disc
                    netprice = ($('#PartPrice').val() - discamt)
                    $('#net').val(netprice.toFixed(2)).addClass("active")
                    $('#extcost').val(extcost).addClass("active")
                    $('#discountamt').val(discamt.toFixed(2)).addClass("active")
                    $('#extprice').val((netprice * qty).toFixed(2)).addClass("active")
                    $('#dfloatinglabel').html("Discount % " + "<span id='discpercentlabel ms-3 text-primary'>(In Dollars $" + discamt.toFixed(2) + " ea.)</span>")
                } else {
                    $('#discountamt').val('0')
                    extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                    extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                    $('#net').val($('#PartPrice').val())
                    $('#extcost').val(extcost).addClass("active")
                    $('#extprice').val(extprice).addClass("active")
                }
            }
        } else {
            // matrix calculations
            srch = $('#PartCategory').val().toLowerCase()
            amt = $('#PartCost').val()
            $.each(clist, function (i, v) {
                if (v.category.toUpperCase() === srch.toUpperCase() && v.start <= amt && v.end >= amt) {
                    $('#PartPrice').val(Math.round((amt * v.factor) * 100) / 100)
                }
                if (parseFloat($('#qty').val()) > 0 && parseFloat($('#PartPrice').val()) > 0 && parseFloat($('#PartCost').val()) > 0) {
                    if (parseFloat($('#Discount').val()) > 0) {

                        disc = parseFloat($('#Discount').val()) / 100
                        qty = $('#qty').val()
                        extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                        extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())) * disc).toFixed(2)
                        baseprice = $('#PartPrice').val()
                        discamt = baseprice * disc
                        netprice = ($('#PartPrice').val() - discamt)
                        $('#net').val(netprice.toFixed(2)).addClass("active")
                        $('#extcost').val(extcost).addClass("active")
                        $('#discountamt').val(discamt.toFixed(2)).addClass("active")
                        $('#extprice').val((netprice * qty).toFixed(2)).addClass("active")
                        $('#dfloatinglabel').html("Discount % " + "<span id='discpercentlabel me-3 text-primary'>(In Dollars $" + discamt.toFixed(2) + " ea.)</span>")
                    } else {
                        $('#discountamt').val('0')
                        extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                        extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                        $('#net').val($('#PartPrice').val())
                        $('#extcost').val(extcost).addClass("active")
                        $('#extprice').val(extprice).addClass("active")
                    }
                }

            });
        }
    }
    <?php
    }else{
    ?>
    function calcPrices() {
        if ($('#partnum').val() == "JOB") {
            if (parseFloat($('#qty').val()) > 0 && parseFloat($('#PartPrice').val()) > 0) {
                if (parseFloat($('#Discount').val()) > 0) {
                    disc = parseFloat($('#Discount').val()) / 100
                    qty = $('#qty').val()
                    extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                    extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())) * disc).toFixed(2)
                    baseprice = $('#PartPrice').val()
                    discamt = baseprice * disc
                    netprice = ($('#PartPrice').val() - discamt)
                    $('#net').val(netprice.toFixed(2)).addClass("active")
                    $('#extcost').val(extcost).addClass("active")
                    $('#discountamt').val(discamt.toFixed(2)).addClass("active")
                    $('#extprice').val((netprice * qty).toFixed(2)).addClass("active")
                    $('#dfloatinglabel').html("Discount % " + "<span id='discpercentlabel ms-3 text-primary'>(In Dollars $" + discamt.toFixed(2) + " ea.)</span>")
                } else {
                    qty = $('#qty').val()
                    extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                    extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val()))).toFixed(2)
                    baseprice = parseFloat($('#PartPrice').val())
                    //discamt = baseprice * disc
                    netprice = parseFloat($('#PartPrice').val())
                    $('#net').val(baseprice.toFixed(2)).addClass("active")
                    $('#extcost').val(extcost).addClass("active")
                    //$('#discountamt').val(discamt.toFixed(2))
                    $('#extprice').val((netprice * qty).toFixed(2)).addClass("active")
                }
            }
        }
        if (parseFloat($('#qty').val()) > 0 && parseFloat($('#PartPrice').val()) > 0 && parseFloat($('#PartCost').val()) > 0) {
            if (parseFloat($('#Discount').val()) > 0) {
                disc = parseFloat($('#Discount').val()) / 100

                qty = $('#qty').val()
                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())) * disc).toFixed(2)
                baseprice = $('#PartPrice').val()
                discamt = baseprice * disc
                netprice = ($('#PartPrice').val() - discamt).toFixed(2)
                extprice = netprice * qty

                $('#net').val(netprice).addClass("active")
                $('#extcost').val(extcost).addClass("active")
                $('#extprice').val(extprice).addClass("active")
            } else {
                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                $('#extcost').val(extcost).addClass("active")
                $('#extprice').val(extprice).addClass("active")
            }
        }

    }
    <?php
    }
    ?>

</script>
</body>
</html>
