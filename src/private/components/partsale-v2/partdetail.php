<?php
require CONN;
include getHeadGlobal('');

$shopid = $_COOKIE['shopid'];
$psid = $_GET['psid'];
$partid = $_GET['partid'];
if (isset($_GET['invtype'])) {
    $invtype = $_GET['invtype'];
} else {
    $invtype = "";
}
$applyDiscount = $_COOKIE['applydiscounts'] ? strtolower($_COOKIE['applydiscounts']) :'no';

if ($invtype == "NON" || $invtype == "") {
    if ($shopid == '1238' || $shopid == "1073" || $shopid == "1191" || $shopid == "1305") {
        $partreg = "`partsregistry-$shopid`";
    } else {
        $partreg = "partsregistry";
    }

    $stmt = "select PartNumber,PartDesc,PartPrice,PartCode,PartCost,PartSupplier,OnHand,Allocatted,NetOnHand,ReOrderLevel,MaxOnHand,MaintainStock,OrderStatus,TransitStatus,PartCategory,bin,tax,overridematrix,partid "
        . "from $partreg where shopid = ? and partid = ?";

} elseif ($invtype == "INV") {
    $partreg = "partsinventory";

    $stmt = "select PartNumber,PartDesc,PartPrice,PartCode,PartCost,PartSupplier,OnHand,Allocatted,NetOnHand,ReOrderLevel,MaxOnHand,MaintainStock,OrderStatus,TransitStatus,PartCategory,bin,tax,overridematrix,partid "
        . "from $partreg where shopid = ? and partid = ?";


}


//echo $stmt;

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("si", $shopid, $partid);
    $query->execute();
    $query->store_result();
    $pn_num_rows = $query->num_rows;
    if ($pn_num_rows > 0) {
        $query->bind_result($PartNumber, $PartDesc, $PartPrice, $PartCode, $PartCost, $PartSupplier, $OnHand, $Allocatted, $NetOnHand, $ReOrderLevel, $MaxOnHand, $MaintainStock, $OrderStatus, $TransitStatus, $PartCategory, $bin, $tax, $overridematrix, $partid);
        $query->fetch();
    } else {
        $PartNumber = '';
        $PartDesc = '';
        $PartPrice = '';
        $PartCode = '';
        $PartCost = '';
        $PartSupplier = '';
        $OnHand = '';
        $Allocatted = '';
        $NetOnHand = '';
        $ReOrderLevel = '';
        $MaxOnHand = '';
        $MaintainStock = '';
        $OrderStatus = '';
        $TransitStatus = '';
        $PartCategory = '';
        $discount = '';
        $net = '';
        $bin = '';
        $tax = '';
        $overridematrix = '';
        $partid = '';
    }

    $query->close();

} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "select usepartsmatrix,updateinvonadd from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    //echo "rotype rows:".$num_roid_rows."<BR>";
    if ($num_roid_rows > 0) {
        $query->bind_result($usematrix, $updateinvonadd);
        $query->fetch();
    } else {
        $usematrix = "no";
    }
    $query->close();

} else {
    echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
}

if ($shopid == '1238') {
    $overridematrix = "no";
}
?>
<body>
<form name="mainform" id="mainform">
    <input type="hidden" name="shopid" id="shopid" value="<?php echo $shopid; ?>">
    <input type="hidden" name="psid" id="psid" value="<?php echo $psid; ?>">
    <input type="hidden" name="partid" id="partid" value="<?php echo $partid; ?>">
    <input type="hidden" name="discountamt" id="discountamt" value="0">
    <div class="container-fluid">
        <div class="content-container">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-row mb-4">
                        <select class="select" name="overridematrix" id="overridematrix">
                            <?php
                            if (strtolower($overridematrix) == "yes") {
                                $ys = ' selected="selected" ';
                                $ns = '';
                            } else {
                                $ys = '';
                                $ns = ' selected="selected" ';
                            }
                            ?>
                            <option <?php echo $ys; ?> value="yes">Yes</option>
                            <option <?php echo $ns; ?> value="no">No</option>
                        </select>
                        <label class="form-label select-label" id="vinfloatinglabel" for="material-text2">Override Matrix Price</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" tabindex="1" type="text" id="partnum" value="<?php echo $PartNumber; ?>" name="PartNumber">
                        <label class="form-label" id="vinfloatinglabel" for="material-text2">Part Number</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" tabindex="2" type="text" id="partdesc" value="<?php echo str_replace('"', ' in ', $PartDesc); ?>" name="PartDesc">
                        <label class="form-label" id="vinfloatinglabel" for="material-text2">Part Description</label>
                    </div>
                    <div class="form-row mb-4">
                        <select onchange="calcPrices()" class="select" tabindex="3" name="PartCategory" id="PartCategory">
                            <option selected value="<?php echo $PartCategory; ?>"><?php echo $PartCategory; ?></option>
                            <?php
                            $stmt = "select distinct category c from category where shopid = ? order by displayorder";
                            $matrix = strtolower($_COOKIE['matrix']);
                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("s", $shopid);
                                $query->execute();
                                $result = $query->get_result();
                                $query->store_result();
                                $numrows = $result->num_rows;
                                if ($numrows > 0) {
                                    while ($row = $result->fetch_array()) {

                                        echo "<option value='" . $row['c'] . "'>" . $row['c'] . "</option>";
                                    }
                                } else {
                                    echo "<option value='none'>No Suppliers Entered</option>";
                                }
                            }
                            ?>
                        </select>

                        <label class="form-label select-label" id="vinfloatinglabel" for="material-text2">Matrix Category</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" onblur="calcPrices()" tabindex="4" type="text" id="qty" name="qty" value="1" name="qty">
                        <label class="form-label" id="discountfloatinglabel" for="material-text2">Quantity</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" onblur="calcPrices()" tabindex="5" type="text" id="PartCost" value="<?php echo number_format($PartCost, 2, '.', ''); ?>" name="PartCost">
                        <label class="form-label" id="discountfloatinglabel" for="material-text2">Shop Cost</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" onblur="calcPrices()" tabindex="6" type="text" id="PartPrice" value="<?php echo number_format((float)($PartPrice), 2, '.', ''); ?>" name="PartPrice">
                        <label class="form-label" id="discountfloatinglabel" for="material-text2">Selling Price</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" onblur="calcPrices()" onkeyup="calcPrices()" tabindex="7" type="text" id="Discount" value="0" name="Discount" <?= ($applyDiscount == "yes" ? "" : "readonly") ?>>
                        <label class="form-label" id="dfloatinglabel" for="material-text2">Discount %</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" onblur="calcPrices()" tabindex="8" type="text" id="net" name="net">
                        <label class="form-label" id="netfloatinglabel" for="material-text2">Net Selling Price</label>
                    </div>
                    <div class="form-row mb-4">
                        <select class="select" tabindex="8" type="text" id="soldby" name="soldby">
                            <option value=''>NONE</option>
                            <?php
                            $stmt = "select employeelast,employeefirst from employees where active = 'yes' and shopid = '$shopid' order by employeelast";
                            if ($query = $conn->prepare($stmt)) {
                                $query->execute();
                                $r = $query->get_result();
                                while ($rs = $r->fetch_assoc()) {
                                    if (isset($soldby)) {
                                        if ($rs['employeelast'] . ", " . $rs['employeefirst'] == $soldby) {
                                            $s = " selected ";
                                        } else {
                                            $s = "";
                                        }
                                    } else {
                                        $s = "";
                                    }
                                    echo "<option $s value='" . $rs['employeelast'] . ", " . $rs['employeefirst'] . "'>" . $rs['employeelast'] . ", " . $rs['employeefirst'] . "</option>";
                                }
                            }
                            ?>
                        </select>
                        <label class="form-label select-label" id="soldbylabel" for="soldby">Sold By</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-outline mb-4">
                        <input class="form-control" tabindex="9" type="text" id="core" name="core">
                        <label class="form-label" id="discountfloatinglabel" for="material-text2">Core Charge</label>
                    </div>
                    <div class="form-row mb-4">
                        <select onkeyup="" class="select" tabindex="10" name="PartCode" id="PartCode">
                            <option selected value="<?php echo $PartCode; ?>"><?php echo $PartCode; ?></option>
                            <?php
                            $stmt = "select codes from codes where shopid = ? and codes != ? order by codes asc";
                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("ss", $shopid, $PartCode);
                                $query->execute();
                                $result = $query->get_result();
                                $query->store_result();
                                $numrows = $result->num_rows;
                                if ($numrows > 0) {
                                    while ($row = $result->fetch_array()) {
                                        echo '<option value="' . $row['codes'] . '">' . $row['codes'] . '</option>';
                                    }
                                } else {
                                    echo '<option value="New">New</option>';
                                }
                            }
                            ?>
                        </select>
                        <label class="form-label select-label" id="discountfloatinglabel" for="material-text2">Part Code</label>
                    </div>
                    <div class="form-row mb-4">
                        <select onkeyup="" class="select" data-mdb-filter="true" tabindex="11" name="PartSupplier" id="PartSupplier">
                            <option selected value="<?php echo $PartSupplier; ?>"><?php echo $PartSupplier; ?></option>
                            <?php
                            $supplier = "";
                            $stmt = "select suppliername s from supplier where suppliername != ? and shopid = ? order by displayorder";
                            //printf ( str_replace('?',"'%s'",$stmt),$PartSupplier,$shopid);
                            $matrix = strtolower($_COOKIE['matrix']);
                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("ss", $PartSupplier, $shopid);
                                $query->execute();
                                $result = $query->get_result();
                                $query->store_result();
                                $numrows = $result->num_rows;
                                if ($numrows > 0) {
                                    while ($row = $result->fetch_array()) {

                                        echo "<option value='" . ($row['s']) . "'>" . ($row['s']) . "</option>";
                                    }
                                } else {
                                    echo "<option value='none'>No Suppliers Entered</option>";
                                }
                            }
                            ?>
                        </select>
                        <label class="form-label select-label" id="discountfloatinglabel" for="material-text2">Supplier</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" tabindex="12" type="text" id="ponum" name="ponum">
                        <label class="form-label" id="discountfloatinglabel" for="material-text2">PO #</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" tabindex="13" type="text" id="bin" name="bin">
                        <label class="form-label" id="discountfloatinglabel" for="material-text2">Bin</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" tabindex="14" type="text" id="invoicenumber" name="invoicenumber">
                        <label class="form-label" id="discountfloatinglabel" for="material-text2">Part Invoice Number</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" tabindex="15" type="text" id="extprice" name="extprice">
                        <label class="form-label" id="extpricefloatinglabel" for="material-text2">Extended Price</label>
                    </div>
                    <div class="form-outline mb-4">
                        <input class="form-control" tabindex="16" type="text" id="extcost" name="extcost">
                        <label class="form-label" id="extcostfloatinglabel" for="material-text2">Extended Cost</label>
                    </div>
                    <div class="form-row mb-4">
                        <select onkeyup="" class="select" tabindex="17" name="tax" id="tax">
                            <?php
                            if (strtolower($tax) == "yes") {
                                $ys = ' selected="selected" ';
                                $ns = '';
                            } else {
                                $ys = '';
                                $ns = ' selected="selected" ';
                            }
                            ?>
                            <option <?php echo $ys; ?> value="yes">Yes</option>
                            <option <?php echo $ns; ?> value="no">No</option>
                        </select>
                        <label class="form-label select-label" id="totalfloatinglabel" for="material-text2">Taxable</label>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-row text-center">
                        <button style="" type="button" onclick="calcPrices();savePart('no')" class="btn btn-primary btn-md">Save</button>
                        <button style="" type="button" onclick="calcPrices();savePart('yes')" class="btn btn-secondary btn-md">Save and Add Another</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>


<?php
$component = "";
include getScriptsGlobal('');
?>
<script>

    function savePart(addanother) {
        showLoader();
        setTimeout(function () {
            partnumber = $('#partnum').val()
            partdesc = $('#partdesc').val()
            partcat = $('#PartCategory').val()
            qty = $('#qty').val()
            partcost = $('#PartCost').val()
            partprice = $('#PartPrice').val()
            partnet = $('#net').val()
            extcost = $('#extcost').val()
            extprice = $('#extprice').val()

            if (partnumber == "") {
                sbalert("Part Number is a required field");
                $('#partnum').focus();
                return
            }
            if (partdesc == "") {
                sbalert("Part Description is a required field");
                $('#partdesc').focus();
                return
            }
            if (partcat == "") {
                sbalert("Part Category is a required field");
                $('#PartCategory').focus();
                return
            }
            if (!$.isNumeric(qty) || qty == '0') {
                sbalert("Quantity is a required field");
                $('#qty').focus();
                return
            }
            if (!$.isNumeric(partcost)) {
                sbalert("Part Cost is a required field");
                $('#PartCost').focus();
                return
            }
            if (!$.isNumeric(partprice)) {
                sbalert("Part Price is a required field");
                $('#PartPrice').focus();
                return
            }
            if (!$.isNumeric(partnet)) {
                sbalert("Net Selling Price is a required field");
                $('#net').focus();
                return
            }
            if (!$.isNumeric(extcost)) {
                sbalert("Extended Cost is a required field");
                $('#extcost').focus();
                return
            }
            if (!$.isNumeric(extprice)) {
                sbalert("Extended Price is a required field");
                $('#extprice').focus();
                return
            }

            // post the information
            ds = $('#mainform').serialize() + "&updateinv=<?php echo $updateinvonadd; ?>"
            if ($('#tax').val() == "no") {
                sbconfirm("Are you sure?", "You have this part marked as NON-TAXABLE.  Are you sure", function () {
                    showLoader();
                    $.ajax({
                        data: ds,
                        type: "post",
                        url: "addnewpartaction.php",
                        success: function (r) {
                            if (addanother == "yes") {
                                location.href = 'addpart.php?shopid=<?php echo $shopid; ?>&psid=<?php echo $psid; ?>'
                            } else {
                                parent.location.reload()
                            }
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            hideLoader();
                        }
                    });
                });
            } else {
                showLoader();
                $.ajax({
                    data: ds,
                    type: "post",
                    url: "addnewpartaction.php",
                    success: function (r) {
                        if (r == "success") {
                            if (addanother == "yes") {
                                location.href = 'addpart.php?shopid=<?php echo $shopid; ?>&psid=<?php echo $psid; ?>'
                            } else {
                                parent.location.reload()
                            }
                        }
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        hideLoader();
                    }
                });
            }
        }, 500);
    }

    $(document).ready(function () {
        var qty_elm = document.getElementById("qty");
        qty_elm.focus();
        qty_elm.select();
        <?php echo "// " . $usematrix . "\r\n"; ?>
    });

    <?php

    if (strtolower($usematrix) == "yes"){
    ?>
    function calcPrices() {

        <?php

        $stmt = "select category,factor,start,end from category where shopid = ? order by Category, Start";

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $result = $query->get_result();
            $rs = array();
            while ($row = $result->fetch_assoc()) {
                $rs[] = $row;
            }
            echo "var clist = " . json_encode($rs) . "\r\n";
        }
        ?>
        if ($('#overridematrix').val() == "yes") {
            if (parseFloat($('#Discount').val()) > 0) {

                disc = parseFloat($('#Discount').val()) / 100
                qty = $('#qty').val()
                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())) * disc).toFixed(2)
                baseprice = $('#PartPrice').val()
                discamt = baseprice * disc
                netprice = ($('#PartPrice').val() - discamt)
                $('#net').val(netprice.toFixed(2))
                $('#extcost').val(extcost).addClass("active")
                $('#discountamt').val(discamt.toFixed(2)).addClass("active")
                $('#extprice').val((netprice * qty).toFixed(2)).addClass("active")
                $('#dfloatinglabel').html("Discount % " + "<span id='discpercentlabel' style=''>(In Dollars $" + discamt.toFixed(2) + " ea.)</span>")
            } else {
                $('#discountamt').val('0')
                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                $('#net').val($('#PartPrice').val()).addClass("active")
                $('#extcost').val(extcost).addClass("active")
                $('#extprice').val(extprice).addClass("active")
            }
        } else {
            // matrix calculations
            srch = $('#PartCategory').val().toLowerCase()
            amt = $('#PartCost').val()
            $.each(clist, function (i, v) {
                if (v.category.toUpperCase() === srch.toUpperCase() && v.start <= amt && v.end >= amt) {
                    $('#PartPrice').val(Math.round((amt * v.factor) * 100) / 100)
                }
                if (parseFloat($('#qty').val()) > 0 && parseFloat($('#PartPrice').val()) > 0 && parseFloat($('#PartCost').val()) > 0) {
                    if (parseFloat($('#Discount').val()) > 0) {

                        disc = parseFloat($('#Discount').val()) / 100
                        qty = $('#qty').val()
                        extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                        extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())) * disc).toFixed(2)
                        baseprice = $('#PartPrice').val()
                        discamt = baseprice * disc
                        netprice = ($('#PartPrice').val() - discamt)
                        $('#net').val(netprice.toFixed(2)).addClass("active")
                        $('#extcost').val(extcost).addClass("active")
                        $('#discountamt').val(discamt.toFixed(2)).addClass("active")
                        $('#extprice').val((netprice * qty).toFixed(2)).addClass("active")
                        $('#dfloatinglabel').html("Discount % " + "<span id='discpercentlabel' style=''>(In Dollars $" + discamt.toFixed(2) + " ea.)</span>")
                    } else {
                        $('#discountamt').val('0')
                        extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                        extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                        $('#net').val($('#PartPrice').val())
                        $('#extcost').val(extcost).addClass("active")
                        $('#extprice').val(extprice).addClass("active")
                    }
                }

            });
        }
    }
    <?php
    }else{
    ?>
    function calcPrices() {
        if (parseFloat($('#qty').val()) > 0 && parseFloat($('#PartPrice').val()) > 0 && parseFloat($('#PartCost').val()) > 0) {
            if (parseFloat($('#Discount').val()) > 0) {

                disc = parseFloat($('#Discount').val()) / 100
                qty = $('#qty').val()
                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())) * disc).toFixed(2)
                baseprice = $('#PartPrice').val()
                discamt = baseprice * disc
                netprice = ($('#PartPrice').val() - discamt)
                $('#net').val(netprice.toFixed(2)).addClass("active")
                $('#extcost').val(extcost).addClass("active")
                $('#discountamt').val(discamt.toFixed(2)).addClass("active")
                $('#extprice').val((netprice * qty).toFixed(2)).addClass("active")
                $('#dfloatinglabel').html("Discount % " + "<span id='discpercentlabel'>(In Dollars $" + discamt.toFixed(2) + " ea.)</span>")

            } else {
                $('#discountamt').val('0').addClass("active")
                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                $('#net').val($('#PartPrice').val()).addClass("active")

                $('#extcost').val(extcost).addClass("active")
                $('#extprice').val(extprice).addClass("active")

            }
        }

    }
    <?php
    }
    ?>
</script>
</body>
</html>
