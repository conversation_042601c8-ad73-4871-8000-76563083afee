﻿<?php
/**@var mysqli $conn */
require_once CONNWOSHOPID;

$shopid = !empty($_REQUEST['shopid']) ? filter_var($_REQUEST['shopid'], FILTER_SANITIZE_STRING) : $_COOKIE['shopid'];
$type = isset($_REQUEST['type']) ? filter_var($_REQUEST['type'], FILTER_SANITIZE_STRING) : "";
$ln = isset($_REQUEST['ln']) ? filter_var($_REQUEST['ln'], FILTER_SANITIZE_STRING) : "";
$r = isset($_REQUEST['r']) ? filter_var($_REQUEST['r'], FILTER_SANITIZE_STRING) : "";
$sd = isset($_REQUEST['sd']) ? filter_var($_REQUEST['sd'], FILTER_SANITIZE_STRING) : "";
$ed = isset($_REQUEST['ed']) ? filter_var($_REQUEST['ed'], FILTER_SANITIZE_STRING) : "";
$color = isset($_REQUEST['color']) ? filter_var($_REQUEST['color'], FILTER_SANITIZE_STRING) : "";
$id = isset($_REQUEST['id']) ? filter_var($_REQUEST['id'], FILTER_SANITIZE_STRING) : "";

$ln = str_replace("'", "''", $ln);
$r = str_replace("'", "''", $r);

$sdTs = strtotime($sd);
$edTs = strtotime($ed);

$sd = date('Y-m-d H:i:s', $sdTs);
$ed = date('Y-m-d H:i:s', $edTs);

if ($type == "add") {
    $stmt = "insert into schedule_employee (lastname,reason,startdate,enddate,colorcode,shopid) value (?,?,?,?,?,?)";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ssssss", $ln, $r, $sd, $ed, $color, $shopid);
        if ($query->execute()) {
            $conn->commit();
        } else {
            echo $conn->error;
            return;
        }
        $query->close();
    } else {
        echo $conn->error;
        return;
    }

    $stmt = "select id from schedule_employee where shopid = ? and colorcode = ? and startdate = ? and enddate = ? and lastname = ? and reason = ?";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ssssss", $shopid, $color, $sd, $ed, $ln, $r);
        $query->execute();
        $query->bind_result($id);
        $query->fetch();
        $query->close();
    }

    $id = "|" . $id;
}

if ($type == "delete") {
    $stmt = "delete from schedule_employee where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        if ($query->execute()) {
            $conn->commit();
        } else {
            echo $conn->error;
            return;
        }
        $query->close();
    } else {
        echo $conn->error;
        return;
    }
    $id = "";
}

if ($type == "update") {
    $stmt = "update schedule_employee set lastname = ?, reason = ?, startdate = ?, enddate = ?, colorcode = ? where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ssssssi", $ln, $r, $sd, $ed, $color, $shopid, $id);
        if ($query->execute()) {
            $conn->commit();
        } else {
            echo $conn->error. " " . sprintf(str_replace("?", "'%s'", $stmt));
            return;
        }
        $query->close();
    } else {
        echo $conn->error;
        return;
    }
    $id = "";
}
if ($type == "move") {
    $stmt = "update schedule_employee set startdate = ?, enddate = ? where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("sssi", $sd, $ed, $shopid, $id);
        if ($query->execute()) {
            $conn->commit();
        } else {
            echo $conn->error;
            return;
        }
        $query->close();
    } else {
        echo $conn->error;
        return;
    }
    $id = "";
}

echo "success" . $id;
?>