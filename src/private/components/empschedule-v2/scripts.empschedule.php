<link href='<?= ASSETS ?>/calendar/lib/fullcalendar.min.css' rel='stylesheet'/>
<link href='<?= ASSETS ?>/calendar/lib/fullcalendar.print.min.css' rel='stylesheet' media='print'/>
<link href='<?= ASSETS ?>/calendar/scheduler.min.css' rel='stylesheet'/>

<script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
<script src='<?= SCRIPT ?>/plugins/moment/moment.js'></script>
<script src='<?= ASSETS ?>/calendar/lib/fullcalendar.min.js'></script>
<script src='<?= ASSETS ?>/calendar/scheduler.min.js'></script>

<style>
    .fc-nonbusiness {
        opacity: 1;
    }

    .tooltip-inner {
        text-align: left;
    }

    .fc-license-message {
        display: none
    }


    .tooltip-inner {
        text-align: left;
    }

    .fc-day-header {
        padding: 5px;
        color: var(--primary);
        font-weight: bold;
        vertical-align: middle !important;
    }

    .fc-button {
        text-transform: capitalize !important;
    }

    #calendar tr,#calendar th {
        height: auto !important
    }

    .fc-time-grid .fc-slats td {
        height: 1.5em !important;
    }

    #calendar {
        max-height: 100vh !important;
        overflow-y: auto !important;
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }

    #calendar::-webkit-scrollbar {
        display: none;
    }

    .fc-content {
        color: var(--secondary) !important;
    }

    .fc-day-header {
        padding: 5px;
        color: var(--primary);
        font-weight: bold;
        vertical-align: middle !important;
    }
    th.fc-day-header {
        background: transparent !important;
    }
    thead{
        background-color: transparent !important;
    }
    .fc-day-top{
        color: var(--tableheadertext) !important;
        background: var(--tableheaderbackground) !important;
    }
</style>


<script>

    $(document).ready(function () {

        var isMobile = window.innerWidth < 768;

        $('#calendar').fullCalendar({
            header: {
                left: 'prev,next today',
                center: 'title',
                right: 'agendaDay,agendaWeek,month'
            },
            defaultView: 'agendaWeek',
            defaultDate: '<?= date("Y-m-d") ?>',
            editable: <?= $_COOKIE['mode']=='full'?'true':'false' ?>,
            eventLimit: true,
            droppable: true,
            minTime: "06:00:00",
            maxTime: "21:00:00",
            columnFormat: isMobile ? 'ddd\nM/D' : 'ddd M/D/Y',
            events: [
                <?php
                $stmt = "select * from schedule_employee where shopid = ?";
                if ($query = $conn->prepare($stmt)) {
                $query->bind_param("s", $shopid);
                $query->execute();
                $results = $query->get_result();
                while ($rs = $results->fetch_assoc()) {
                $date1 = date_create($rs['startdate']);
                $date2 = date_create($rs['enddate']);
                $diff = date_diff($date1, $date2);
                $hours = hours_diff($rs['startdate'], $rs['enddate']);
                $startDateTS = strtotime($rs["startdate"]);
                $endDateTs = strtotime($rs['enddate']);
                $colorcode = $rs["colorcode"];
                if (strpos($colorcode, "#") === false){
                    $colorcode = "#".$colorcode;
                }
                if($hours > 24){
                ?>
                {
                    id: <?= $rs["ID"] ?>,
                    backgroundColor: '<?= $colorcode ?>',
                    textColor: "#000000",
                    title: '<?= str_replace("'", "\'", $rs["LastName"]) . " " . str_replace("'", "\'", $rs["Reason"]) ?>',
                    lastname: '<?= str_replace("'", "\'", $rs["LastName"]) ?>',
                    reason: '<?= str_replace("'", "\'", $rs["Reason"]); ?>',
                    start: '<?= $rs["startdate"] ?>',
                    end: '<?= $rs['enddate'] ?>',
                    allDay: true
                },
                <?php
                } else {
                ?>
                {
                    id: <?= $rs["ID"] ?>,
                    backgroundColor: '<?= $colorcode ?>',
                    textColor: "#000000",
                    title: '<?= str_replace("'", "\'", $rs["LastName"]) . " " . str_replace("'", "\'", $rs["Reason"]) ?>',
                    lastname: '<?= str_replace("'", "\'", $rs["LastName"]) ?>',
                    reason: '<?= str_replace("'", "\'", $rs["Reason"]) ?>',
                    start: '<?= $rs["startdate"] ?>',
                    end: '<?= $rs['enddate'] ?>',
                    ctype: '<?= $rs['startdate'] == $rs['enddate'] ? 'same' : ''?>'
                },
                <?php
                }
                }
                }
                ?>
            ],
            <?php if($_COOKIE['mode'] == 'full'){?>

            eventClick: function (event) {
                $('#editlastname').val(event.lastname).addClass("active")
                $('#editcomments').val(event.reason)
                $('#editsd').val(event.start.format('MM/DD/YYYY, hh:mm A'))
                $('#eventid').val(event.id)

                if (event.ctype == 'same') {
                    $('#edited').val(event.start.format('MM/DD/YYYY, hh:mm A'))
                } else {
                    $('#edited').val(event.end.format('MM/DD/YYYY, hh:mm A'))
                }
                bc = event.backgroundColor
                if (bc.length > 2) {
                    $('#editcolor').val(bc)
                } else {
                    setEmpColor($('#editcolor'))
                }
                $('#editmodal').modal('show')
            },
            dayClick: function (date, jsEvent, view) {
                $('#addreason').val("")
                setEmpColor($('#addlastname'))
                $('#addsd').val(date.format('MM/DD/YYYY, hh:mm A'))
                $('#added').val(date.format('MM/DD/YYYY, hh:mm A'))
                $('#addmodal').modal('show')
            },
            eventDrop: function (event, delta, revertFunc) {
                console.log(event);
                sd = event.start.format()
                ed = event.end.format()
                id = event.id
                rid = event.resourceId
                moveevent(id, sd, ed)
            },
            eventResize: function (event, jsEvent, ui, view) {
                ed = event.end.format()
                sd = event.start.format()
                id = event.id
                moveevent(id, sd, ed);
            },
            <?php }?>
            viewRender: function (view) {

                $('.fc-prev-button,.fc-next-button,.fc-today-button,.fc-month-button,.fc-agendaWeek-button,.fc-agendaDay-button').removeClass().addClass('fc-button btn btn-secondary me-1');

            }


        });

        setTimeout(function () {
            $('#calendar').fullCalendar('refetchEvents');
        }, 60000);

        $('.fc-button').hover(function () {
            $(this).removeClass('fc-state-hover');
        });


    });

    function moveevent(id, sd, ed) {
        $.ajax({

            data: "type=move&shopid=&sd=" + sd + "&ed=" + ed + "&id=" + id,
            url: "updateevent.php",
            type: "post",
            success: function (r) {
                if (r != "success") {
                    console.log(r);
                }
                $('#calendar').fullCalendar('refetchEvents');
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });
    }


    function addEvent() {
        var lastname = $('#addlastname').val()
        var comments = $('#addcomments').val()
        var sd = $('#addsd').val()
        var ed = $('#added').val()
        var color = $('#addcolor').val()

        if (lastname.length > 0 && sd.length > 0 && ed.length > 0) {

            showLoader()

            mydata = "type=add&shopid=<?= $shopid ?>&sd=" + sd + "&ed=" + ed + "&ln=" + encodeURIComponent(lastname) + "&r=" + encodeURIComponent(comments) + "&color=" + encodeURIComponent(color)
            $.ajax({
                url: "updateevent.php",
                data: mydata,
                success: function (result) {

                    if (result.indexOf("success|") >= 0) {
                        tar = result.split("|")
                        aid = tar[1]
                        sd1 = new Date(sd)
                        ed1 = new Date(ed)
                        timediff = Math.abs(sd1 - ed1)
                        timediff = (timediff / 60000) / 60
                        if (timediff > 24) {
                            ad = true
                        } else {
                            ad = false
                        }
                        var newEvent = {
                            title: lastname + " " + comments,
                            start: sd,
                            end: ed,
                            backgroundColor: color,
                            textColor: "#000000",
                            lastname: lastname,
                            reason: comments,
                            id: aid,
                            allDay: ad,
                            ctype: ((sd == ed) ? "same" : "")
                        };
                        $('#calendar').fullCalendar('renderEvent', newEvent, 'stick');
                    }
                    $('#addmodal').modal('hide')
                    hideLoader()
                }
            });

        } else {
            sbalert("Last name, start and end date and time are required.")
        }
    }


    function deleteEvent() {
        sbconfirm("Delete Appointment", "This will delete this appointment.  Are you sure?",

            function () {

                var eventid = $('#eventid').val()

                mydata = "type=delete&shopid=<?= $shopid ?>&id=" + eventid

                showLoader()

                $.ajax({
                    url: "updateevent.php",
                    data: mydata,
                    success: function (result) {
                        if (result == "success") {
                            $('#calendar').fullCalendar('removeEvents', eventid);
                            $('#editmodal').modal('hide')
                            hideLoader()
                        }

                    }
                });

            })

    }

    function editEvent() {
        var lastname = $('#editlastname').val()
        var comments = $('#editcomments').val()
        var sd = $('#editsd').val()
        var ed = $('#edited').val()
        var color = $('#editcolor').val()
        var eventid = $('#eventid').val()
        console.log("start", color);
        showLoader()

        mydata = "type=update&id=" + eventid + "&shopid=<?= $shopid ?>&sd=" + sd + "&ed=" + ed + "&ln=" + encodeURIComponent(lastname) + "&r=" + encodeURIComponent(comments) + "&color=" + encodeURIComponent(color)
        $.ajax({
            url: "updateevent.php",
            data: mydata,
            success: function (result) {

                if (result == "success") {
                    $('#calendar').fullCalendar('removeEvents', eventid);

                    sd1 = new Date(sd)
                    ed1 = new Date(ed)
                    timediff = Math.abs(sd - ed)
                    timediff = (timediff / 60000) / 60
                    if (timediff > 24) {
                        ad = true
                    } else {
                        ad = false
                    }
                    console.log("after", color);
                    var newEvent = {
                        title: lastname + " " + comments,
                        start: sd,
                        end: ed,
                        backgroundColor: color,
                        textColor: "#000000",
                        lastname: lastname,
                        reason: comments,
                        id: eventid,
                        allDay: ad,
                        ctype: ((sd == ed) ? "same" : "")
                    };
                    $('#calendar').fullCalendar('renderEvent', newEvent, 'stick');
                    $('#editmodal').modal('hide')
                    hideLoader()
                }

            }
        });

    }

    /*
    const datetimepickerElements = document.querySelectorAll('.dtpicker');

    datetimepickerElements.forEach(function (dt) {
        new mdb.Datetimepicker(dt, {
            datepicker: { format: 'mm/dd/yyyy' },
            timepicker: { format24: false },
            inline: true
        });
    });
    */

    function setEmpColor(elem) {
        var color = $(elem).find("option:selected").data('color');
        console.log(color);
        if (color != '') {
            //find the color pallete on same modal
            var modal = $(elem).closest(".modal-body");
            $(modal).find(".form-control-color").val(color)
        }
    }

</script>
    