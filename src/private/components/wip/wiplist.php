<?php
require CONN;

$stime = microtime(true);
date_default_timezone_set('America/Phoenix');
$tdate = date('Y-m-d');
$usr = isset($_COOKIE['usr'])?$_COOKIE['usr']:'';
$empid = isset($_COOKIE['empid']) ? $_COOKIE['empid'] : '';
//$usr = $_COOKIE['usr'];

function seconds2human($ss) {

	$time=$ss; //whatever
	$seconds = $time%60;
	$mins = floor($time/60)%60;
	$hours = floor($time/60/60)%24;
	$days = floor($time/60/60/24);

	if ($days > 0){
		return "$days days, $hours hrs";
	}else{
		return "$hours hrs";
	}
}

//$tdate = date_create($tdate);
//$tdate = date_format($tdate, 'Y-m-d');

function getContrastColor($hexcolor) {
    $r = hexdec(substr($hexcolor, 1, 2));
    $g = hexdec(substr($hexcolor, 3, 2));
    $b = hexdec(substr($hexcolor, 5, 2));
    $yiq = (($r * 299) + ($g * 587) + ($b * 114)) / 1000;
    return ($yiq >= 128) ? 'black' : 'white';
}


function validateDate1($date, $format = 'Y-m-d H:i:s'){
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) == $date;
}

$shopid = $_GET['shopid'];
$writerfilter = isset($_GET['w'])?filter_var($_GET['w'], FILTER_SANITIZE_STRING):'';

if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])){
	$ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
}
elseif (isset($_SERVER['HTTP_X_CLUSTER_CLIENT_IP'])){
	$ip = $_SERVER['HTTP_X_CLUSTER_CLIENT_IP'];
}elseif (!empty($_SERVER['HTTP_CLIENT_IP'])){
	$ip = $_SERVER['HTTP_CLIENT_IP'];
}else{
	$ip = $_SERVER['REMOTE_ADDR'];
}

$stmt = "select sortwipbylastfirst,firstlastonwip,showpromiseonwip,nexpartpassword as showcommlog,mailpassword as showbalanceonwip,showemailestimateonwip, showemailinvoiceonwip, showtimeclockonwipdata, showpaymentonwip, showinspectiononwip, showinpectionemailonwip, showtechoverhours,nexpartusername showpics,shopmgr,showtextemail,requirepayments,gponwiplist,showwaiting,showwtk,showvehcolor,showhrsonwip,profitboost,showsigonwip from company where shopid = ?";
if($query = $conn->prepare($stmt)){
	$query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($sortwipbylastfirst,$firstlastonwip,$showpromiseonwip,$showcommlog,$showbalanceonwip,$showestemail,$showinvemail,$showtechtime,$showmoney,$showinsp,$showinspemail,$showtechoverhours,$showpics,$showelapsed,$showtextemail,$requirepayments,$gponwiplist,$showwaiting,$showwtk,$showvehcolor,$showhrsonwip,$isprofitboost,$showsigonwip);
	$query->fetch();
    $query->close();
}else{
	echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "select lower(pph) from settings where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($hasPph);
    $query->fetch();
    $query->close();
}

$pphAccess = 'yes';

if (!empty($empid))
{
    $stmt = "select lower(pphaccess) from employees where id = ? and shopid = ?";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("is", $empid, $shopid);
        $query->execute();
        $query->bind_result($pphAccess);
        $query->fetch();
        $query->close();
    }
}

$pphtarget = '';

if($isprofitboost=='yes')
{
 $stmt = "select target from profitboost where shopid = '$shopid'";
 if ($query = $conn->prepare($stmt)) 
 {
    $query->execute();
    $query->bind_result($pphtarget);
    $query->fetch();
    $query->close();
 }
}
?>
<!DOCTYPE html>
<html>
<head>

        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">

        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css">


        <!-- Page JS Plugins CSS -->
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">

        <!-- Bootstrap and OneUI CSS framework -->
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
        <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
        <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
        <meta name="robots" content="noindex, nofollow">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
        <script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
        <script src="<?= SCRIPT ?>/tipped.js"></script>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

        <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
        <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
        <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
        <script src="<?= SCRIPT ?>/app.js"></script>
				<!-- <script src="<?= SCRIPT ?>/sbp-pageresize.js"></script> -->
        <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
        <script src="<?= SCRIPT ?>/emodal.js"></script>
        <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
        <script src="<?= SCRIPT ?>/plugins/sorttable/jquery.tablesorter.min.js"></script>

        <style>
    	.rocell, .stcell, .dacell, .cucell,.ocucell, .phcell, .vecell, .tocell, .licell, .tycell{
			padding:0px;
			font-size:9pt;
		}
    	.rocell2{
			padding:0px;
			font-size:9pt;
		}
		.licell{
			color:#336699;
			font-weight:bold
		}
		.cucell{
			color:#336699;
			font-weight:bold
		}

		.header{
			padding:1px;
		}
        .auto-style2 {
			text-align: left;
			font-size: small;
			font-weight: bold;
			border-top: 1px ridge gray;
			border-bottom: 1px ridge gray;
		}

		.tooltip-inner{
		    min-width: 250px;  /*the minimum width */
			max-height: 200px;
			overflow-y: scroll;
		}
		.table-responsive .table {
			overflow-x:scroll;
		    max-width: 100%;
		    -webkit-overflow-scrolling: touch !important;
		}

		@media only screen and (max-width: 640px) {
			#wiplist-table{
				font-size:large
			}

		}

		#wiplist-table thead tr th.headerSortUp{
		  background: #5C90D2 url('<?= SCRIPT ?>/plugins/sorttable/up-arrow.png') no-repeat
		}
		#wiplist-table thead tr th.headerSortDown{
		  background: #5C90D2 url('<?= SCRIPT ?>/plugins/sorttable/down-arrow.png') no-repeat
		}
		#wiplist-table thead tr th{
			cursor:pointer
		}
        </style>

</head>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
	<form style="padding:0px;margin:0px" class="form-horizontal push-10-t" action="base_forms_elements_modern.php" method="post" onsubmit="return false;">
		<div style="padding:0px;margin:0px" class="form-group">
		<div style="padding:0px;margin:0px; left: 0px; top: 0px;" class="col-sm-12">
		<div style="padding:0px;margin:0px" class="text-left">
			<input class="form-control" style="" type="text" placeholder="Search WIP AND History by any Displayed Value ( RO Number, Customer, Date, Phone, Vehicle)" id="srch" name="srch">
			<div style="display:none;text-align:center;padding:10px;border:2px red solid;position:fixed;top:-10px;left:0px;width:100%;height:70px;background-color:red;color:white;z-index:99999999" id="massupdates">
				<strong>Please select a bulk change to make to the selected RO's</strong>
				<select style="border:1px silver solid;border-radius:5px;padding:8px;color:black" id="massaction">
					<option value="close">Mark Selected as CLOSED</option>
					<option value="final">Mark Selected as FINAL</option>
					<option value="noapproval">Mark Selected as NO APPROVAL</option>
				</select>
				<span class="btn btn-warning" onclick="massUpdate()" >Update Selected</span>
				<span class="btn btn-default" onclick="cancelMassUpdates()" >Cancel</span>
			</div>
		</div>
		</div>
		</div>
	</form>



<div id="table-container-wip" class="table-responsive">
<table id="wiplist-table" class="table table-striped table-hover table-condensed table-header-bg">
	<thead>
		<tr class="header">

			<th id="roheader" style="padding:1px 1px 1px 5px;" onclick="setSort('ro')" class="rocell2">RO</th>
			<th id="statusheader" style="padding:1px;" onclick="setSort('status')" class="stcell">Status</th>
			<th id="dateheader" style="padding:1px;" onclick="setSort('date')" class="dacell">Date</th>
			<?php if($showpromiseonwip=='yes'){?>
			<th id="promiseheader" style="padding:1px;" onclick="setSort('promised')" class="promisecell">Promised</th>
			<?php
		    }
			if ($showelapsed == "yes"){
		 	?>
			<th id="elapsedheader" style="padding:1px;" onclick="setSort('elapsed')" class="dacell">Elapsed</th>
			<?php
			}
			?>
			<th id="customerheader" style="padding:1px;" onclick="setSort('customer')" class="cucell">Customer</th>
			<th id="phoneheader" style="padding:1px;" onclick="setSort('phone')" class="phcell">Phone</th>
			<th id="vehicleheader" style="padding:1px;" onclick="setSort('vehicle')" class="vecell">Vehicle</th>
			<th id="totalroheader" style="padding:1px 15px 1px 1px;" onclick="setSort('totalro')" class="tocell text-right" style="padding-right:20px;">Total</th>
			<?php if($showhrsonwip=='yes'){?><th id="hrsheader" style="padding:1px;" onclick="setSort('hrs')" class="licell text-right">Lab. Hrs</th><?php }?>
			<th id="licenseheader" style="padding:1px;" onclick="setSort('license')" class="licell">License</th>
			<?php if($gponwiplist=='yes'){?><th id="gpheader" style="padding:1px;" onclick="setSort('gp')" class="gpcell">GP</th><?php }?>
			<?php if($hasPph=='yes' && $pphAccess == 'yes'){?><th id="pphheader" style="padding:1px;" onclick="setSort('pph')" class="pphcell">PPH</th><?php }?>
			<?php if($showwtk=='yes'){?><th id="writerheader" style="padding:1px;" onclick="setSort('writer')" class="licell">W+TK</th><?php }?>
			<?php if($showvehcolor=='yes'){?><th id="colorheader" style="padding:1px;" onclick="setSort('color')" class="licell">Color</th><?php }?>
			<th id="typeheader" style="padding:1px;" onclick="setSort('type')" class="tycell">Type</th>
			<th>&nbsp;</th>
		</tr>
	</thead>
	<tbody>
<?php
// check for any prero's

$stmt = "select p.id,p.shopid,p.customerid,p.vehid,concern1 c1,concern2 c2,concern3 c3,concern4 c4,concern5 c5,concat(firstname,' ',lastname) n, concat(`year`,' ',`make`,' ',`model`) veh from prero p left join"
. " customer c on p.shopid = c.shopid and p.customerid = c.customerid left join vehicles v on p.shopid = v.shopid and p.vehid = v.vehid and p.customerid = v.customerid where p.shopid = "
. "'" . $shopid . "' and daterequested = '" . date("Y-m-d") . "'";

$result = $conn->query($stmt);
while($row = $result->fetch_array()) {
	$displayconcern = "VEHICLE ISSUES: " . $row["c1"] . "; " . $row["c2"] . "; " . $row["c3"] . "; " . $row["c4"] . "; " . $row["c5"];
?>
         <tr>
           <td onclick="showPreRO(<?php echo $row['id']; ?>)" style="border-top:1px gray ridge;border-bottom:1px gray ridge; height: 5px;" bgcolor="#CCCCFF" width="5%" valign="middle" align="center" class="style1">
		   #<?php echo $row['id']; ?></td>
           <td onclick="showPreRO(<?php echo $row['id']; ?>)" style="border-top:1px gray ridge;border-bottom:1px gray ridge; height: 5px;" bgcolor="#CCCCFF" valign="middle" class="auto-style8" colspan="1">
			<strong>Customer: <?php echo $row['n']; ?></strong></td>
           <td onclick="showPreRO(<?php echo $row['id']; ?>)" style="border-top:1px gray ridge;border-bottom:1px gray ridge;text-align:left; height: 5px;" bgcolor="#CCCCFF" align="center" valign="middle" class="style1" colspan="2">
            <b>Vehicle: <?php echo substr($row['veh'],0,30); ?></b></td>
           <td onclick="showPreRO(<?php echo $row['id']; ?>)" style="width: 8%; height: 5px;" bgcolor="#CCCCFF" align="center" valign="middle" class="auto-style2">
            Pre-RO from MyVehicleRepairs.net</td>
           <td onclick="showPreRO(<?php echo $row['id']; ?>)" style="border-top:1px gray ridge;border-bottom:1px gray ridge; text-align:left; height: 5px;" bgcolor="#CCCCFF" align="center" valign="middle" class="style1" colspan="6">
            <b><?php echo $displayconcern; ?></b></td>
          </tr>

<?php
}


$stmt = "select customerfirst,customerlast,r.datetimepromised,r.totalprts,r.totallbr,r.totalsublet,r.salestax,r.discountamt,r.totalfees,r.gp,r.statusdate,r.vin,r.fleetno,r.customerid,r.roid,r.status as stat,r.datein,r.tagnumber,r.customer,"
	. "r.customerphone as home,r.customerwork as work,r.cellphone as cell,r.vehinfo,r.totalro,r.vehlicense,r.rotype,coalesce(r.inspectioncomplete,'') inspectioncomplete,coalesce(r.inspectionemailed,'') inspectionemailed,coalesce(r.estimateemailed,'') estimateemailed,coalesce(r.invoiceemailed,'') invoiceemailed"
	. ",ro.colorcode,r.comments,r.balance,r.writer,r.timein,r.gp,r.waiter,r.vehid,r.pph,r.signed,r.origshopid,r.recall from repairorders r left join rotype ro on r.shopid = ro.shopid and r.rotype = ro.rotype where r.shopid = '" . $shopid . "' and "
	. "status != 'CLOSED' and r.ROType != 'No Approval'";

if (!empty($writerfilter))
$stmt .= " and writer = ? ";


if (isset($_GET['o'])){
	$ostat = $_GET['o'];
	if ($ostat == "status"){
		if ($sortwipbylastfirst == "no"){
			$stmt .= " order by status, roid desc, datein desc";
		}else{
			$stmt .= " order by status, lastfirst, roid desc, datein desc";
		}
	}elseif ($ostat == "type"){
		$stmt .= " order by rotype, status, roid desc, datein desc";
	}elseif ($ostat == "ro"){
		$stmt .= " order by roid desc, status, datein desc";
	}elseif ($ostat == "date"){
		$stmt .= " order by datein desc, timein desc";
	}

}

$query = $conn->prepare($stmt);
if (!empty($writerfilter))
$query->bind_param("s",$writerfilter);
$query->execute();
$result = $query->get_result();
$opencounter = 0;
$compcountinsp = 0;
$startcountinsp = 0;

    // output data of each row
    while($row = $result->fetch_array()) {
    	$opencounter += 1;
    	$home = $row["home"];
    	$work = $row["work"];
    	$cell = $row["cell"];
    	$status = $row["stat"];
    	$statvar = substr($status,0,1);
    	$statdate = new DateTime($row["statusdate"]);
    	if (strlen($row['datetimepromised']) > 0){
	    	$datetimepromised = date("m/d/Y h:i a",strtotime($row['datetimepromised']));
	    }else{
	    	$datetimepromised = "";
	    }

    	if( is_numeric($statvar) ){
    		$status = substr($status,1);
    	}

    	if ($showelapsed == "yes"){
	    	$startdatetime = strtotime($row['datein']." ".$row['timein']);
	    	$currdatetime = strtotime(localTimeStamp($shopid));
	    	$numsecs = $currdatetime - $startdatetime;
	    	$displaytime = seconds2human($numsecs);
	    }else{
	    	$displaytime = "";
	    }

		switch(strtoupper($status)){
			case "INSPECTION";
				$statflag = "<img style='width:20px;height:20px;' src='". IMAGE ."/newimages/yellowflag.gif' alt=''>";
				$flagcolor = "yellow";
				$rowclass = "";
				break;
			case "APPROVAL";
				$statflag = "<img style='width:20px;height:20px;' src='". IMAGE ."/newimages/orangeflag.gif' alt=''>";
				$rowclass = "";
				$flagcolor = "orange";
				break;
			case "PARTS";
				$statflag = "<img style='width:20px;height:20px;' src='". IMAGE ."/newimages/redflag.gif' alt=''>";
				$rowclass = "";
				$flagcolor = "red";
				break;
			case "ASSEMBLY";
				$statflag = "<img style='width:20px;height:20px;' src='". IMAGE ."/newimages/greenflag.gif' alt=''>";
				$rowclass = "";
				$flagcolor = "green";
				break;
			case "Q-CHECK";
				$statflag = "<img style='width:20px;height:20px;' src='". IMAGE ."/newimages/yellowflag.gif' alt=''>";
				$rowclass = "";
				$flagcolor = "pink";
				break;
			case "FINAL";
				$statflag = "<img style='width:20px;height:20px;' src='". IMAGE ."/newimages/blueflag.gif' alt=''>";
				$rowclass = "";
				$flagcolor = "blue";
				break;
		}
    	if(strlen($row["home"]) > 0){
    		$home = "H: (".substr($row["home"], 0, 3).") ".substr($row["home"], 3, 3)."-".substr($row["home"],6);
    	}
    	if(strlen($row["work"]) > 0){
    		$work = "W: (".substr($row["work"], 0, 3).") ".substr($row["work"], 3, 3)."-".substr($row["work"],6);
    	}
    	if(strlen($row["cell"]) > 0){
    		$cell = "C: &nbsp;(".substr($row["cell"], 0, 3).") ".substr($row["cell"], 3, 3)."-".substr($row["cell"],6);
    	}


    	$cc = "#FFFFFF";
    	if (strlen($row['colorcode']) > 0){
    		$cc = $row['colorcode'];
    		$fontcolor = getContrastColor($cc);
			if (strtoupper($cc) != "#FFFFFF"){
				$cc .= ";color:$fontcolor;font-weight:normal";
			}else{
				$cc .= ";color:$fontcolor;font-weight:normal";
			}
    	}


		$clockedtechs = "";
		if (strtoupper($showtechtime) == "YES"){
			$tcstmt = "select GROUP_CONCAT(tech SEPARATOR '<br>') as techs from labortimeclock where shopid = ? and roid = ? and enddatetime is null";
			if($tcquery = $conn->prepare($tcstmt)){
				$tcquery->bind_param("si",$shopid,$row['roid']);
			    $tcquery->execute();
			    $tcquery->bind_result($clockedtechs);
				$tcquery->fetch();
			    $tcquery->close();
			}else{
				echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}

		$moneyrecd = 0;
		if (strtoupper($showmoney) == "YES" || strtoupper($requirepayments)=='YES'){
			$mstmt = "select count(*) as c from accountpayments where shopid = ? and roid = ?";
			if($mquery = $conn->prepare($mstmt)){
				$mquery->bind_param("si",$shopid,$row['roid']);
			    $mquery->execute();
			    $mquery->bind_result($moneyrecd);
				$mquery->fetch();
			    $mquery->close();
			}else{
				echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}

		if (strtoupper($showpics) == "YES"){
			$mstmt = "select count(*) as c from repairorderpics where shopid = ? and roid = ?";
			if($mquery = $conn->prepare($mstmt)){
				$mquery->bind_param("si",$shopid,$row['roid']);
			    $mquery->execute();
			    $mquery->bind_result($countpics);
				$mquery->fetch();
			    $mquery->close();
			}else{
				echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}

		$locationstring = "parent.location.href='". COMPONENTS_PRIVATE ."/ro/ro.php?roid=".$row["roid"]."'";
		if ($firstlastonwip == "yes"){
			$currcustomer = $row["customer"];
		}else{
			$currcustomer = strtoupper($row["customerlast"].", ".$row['customerfirst']);
		}
		$comms = strtoupper(str_replace("#","",$row['comments']));


		// check for an inspection started
		$startcountinsp = $startcountnewinsp = 0;
		
		if (strtoupper($showinsp) == "YES")
		{

		$isstmt = "select count(*) c from roinspection where shopid = '$shopid' and roid = " . $row['roid'];
		if ($isquery = $conn->prepare($isstmt)){
			$isquery->execute();
			$isquery->bind_result($startcountinsp);
			$isquery->fetch();
			$isquery->close();
		}

		$isstmt = "select count(*) c from dvi where shopid = '$shopid' and roid = " . $row['roid'];
		if ($isquery = $conn->prepare($isstmt)){
			$isquery->execute();
			$isquery->bind_result($startcountnewinsp);
			$isquery->fetch();
			$isquery->close();
		}

		$startcountinsp += $startcountnewinsp;


		// check for an inspection completed
		$compcountinsp = $newdvicom = 0;
		$icstmt = "select count(*) c from roinspectionheader where shopid = '$shopid' and completiondate != '0000-00-00 00:00:00' and roid = " . $row['roid'] ;
		if ($icquery = $conn->prepare($icstmt)){
			$icquery->execute();
			$icquery->bind_result($compcountinsp);
			$icquery->fetch();
			$icquery->close();
		}
		$isstmt = "select count(*) c from dvi where shopid = '$shopid' and roid = " . $row['roid']." and estimate_complete='1'";
		if ($isquery = $conn->prepare($isstmt)){
			$isquery->execute();
			$isquery->bind_result($newdvicom);
			$isquery->fetch();
			$isquery->close();
		}

		$compcountinsp += $newdvicom;

	    }


		$totalbox = number_format($row["totalro"],2);
		if ($showbalanceonwip == "yes"){
			$totalbox = "Balance:".number_format($row["balance"],2);
		}

		$sa = $row['writer'];
		if (strpos($sa," ") > 0){
			$sar = explode(" ",$sa);
			$firstinit = strtoupper(substr($sar[0],0,1));
			if (isset($sar[1])){
				$lastinit = strtoupper(substr($sar[1],0,1));
			}else{
				$lastinit = "";
			}
			if (isset($sar[2])){
				$finalinit = strtoupper(substr($sar[2],0,1));
			}else{
				$finalinit = "";
			}
			$writer = $firstinit.$lastinit.$finalinit;
		}else{
			$writer = substr($row['writer'],0,4);
		}

		$techlist = "";
		$laborhours = 0;
		$techarr = array();
    	$tstmt = "select tech,laborhours from labor where tech != 'discount, discount' and deleted = 'no' and shopid = '".$shopid."' and roid = ".strtoupper($row["roid"]);
    	//echo $tstmt;
    	$tresult = $conn->query($tstmt);
		while($trow = $tresult->fetch_array()) {
			$laborhours+= $trow['laborhours'];
			if(in_array($trow['tech'], $techarr))continue;
			$techarr[] = $trow['tech'];
			if (strpos($trow['tech'],",") > 0){
				$tname1 = $trow['tech'];
				$tar = explode(",",$tname1);
				$tfirst = trim($tar[1]);
				$tlast = trim($tar[0]);
				//echo $tfirst.$tlast;
				$tfirstinit = substr($tfirst,0,1);
				$tlastinit = substr($tlast,0,1);
				$techname = $tfirstinit.$tlastinit;
				$techlist = $techlist.$techname.",";
			}
		}
		//echo $techlist;
		if (substr($techlist,-1) == ","){
			$techlist = substr($techlist,0,strlen($techlist)-1);
		}
		if (strlen($techlist) > 0){
			$writer .= ",".$techlist;
		}

		$commstr = "";
		if ($showcommlog == "showcommlogyes"){

			// check for comms
			$commstmt = "select `datetime`,replace(`comm`,'\"',\"'\") as comm from repairordercommhistory where `comm` != 'ADVISOR COMMENTS UPDATED' and `comm` != 'TECH STORY UPDATED' and shopid = '$shopid' and roid = ".$row['roid']." order by datetime desc limit 5;";
			if ($commquery = $conn->prepare($commstmt)){
				$commquery->execute();
				$commr = $commquery->get_result();
				while ($commrs = $commr->fetch_array()){
					$commstr .= date("m/d/Y",strtotime($commrs['datetime']))." - ".$commrs['comm']."<hr style='padding:0px;margin:4px;border:1px black solid'>";
				}
			}

		}
		if (strlen($row['tagnumber']) > 0){
			$roid = $row['roid']."-".$row['tagnumber'];
		}else{
			$roid = $row['roid'];
		}

		if(strtoupper($showtextemail)=='YES')
		{
			$testmt="select id from repairordercommhistory where shopid='$shopid' and roid='".$row['roid']."' and (comm like 'Sent via Text Message:%' || comm like '%An Email Update%') limit 1";
			$teresult = $conn->query($testmt);
            $tecount=$teresult->num_rows;
		}
		else
		$tecount=0;

	    if($showwaiting=='yes' && $row['waiter']=='waiting'){
			$wback = "background-color:#FFAEAE";
		}else{
			$wback = "";
		}

		$vehcolor = '';

		if($showvehcolor=='yes' && !empty($row['vehid']))
		{
			$cstmt = "select color from repairorders r,vehicles v where r.shopid=v.shopid and r.vehid=v.vehid and r.shopid = '$shopid' and r.roid = " . $row['roid'];
		    if ($cquery = $conn->prepare($cstmt))
		    {
			 $cquery->execute();
			 $cquery->bind_result($vehcolor);
			 $cquery->fetch();
			 $cquery->close();
		    }
		}

		if(!empty($pphtarget) && $row['pph']>0)
		$pphcolor = ($row['pph']>=$pphtarget?'green':'red');
	    else
	    $pphcolor = '';

?>
		<tr>
			<td style="<?php echo $wback; ?>" class="rocell" id="<?php echo $row["roid"];?>"><?php if (strlen($comms)>0){?><div data-html="true" data-toggle="tooltip" title="<?php echo $comms; ?>" style="max-width:100%;overflow:hidden;white-space: nowrap;" ><?php }?><?php echo strtoupper($roid);?><?php if (strlen($comms)>0){?></div><?php }?></td>
			<td style="<?php echo $wback; ?>" class="stcell" onclick="<?php echo $locationstring; ?>"><?php if (strlen($comms)>0){?><div data-html="true" data-toggle="tooltip" title="<?php echo $comms; ?>"><?php }?><i class='fa fa-flag fa-lg wipflag_<?php echo $flagcolor; ?>'></i> <?php echo strtoupper($status)." ";?>
				<?php if (strtoupper($showmoney) == "YES" && $moneyrecd > 0){ echo '<i id="moneyrecd" style="color:green;font-weight:bold" data-html="true" data-toggle="tooltip" title="Money Collected from Customer" class="fa fa-usd fa-md"></i>'; } ?>
				<?php if (strtoupper($showpics) == "YES" && $countpics > 0){ echo '<i id="showpics" style="color:green;font-weight:bold" data-html="true" data-toggle="tooltip" title="Pictures Uploaded to this RO" class="fa fa-camera fa-md"></i>'; } ?>
				<?php if (strtoupper($showtechtime) == "YES" && !empty($clockedtechs)){ echo '<i id="techclock" data-html="true" data-toggle="tooltip" title="'.strtoupper($clockedtechs).' Clocked In" style="color:orange;" class="fa fa-clock-o fa-md"></i>'; }?>
				<?php if (strtoupper($showtechoverhours) == "YES"){ echo '<i style="color:red;display:none" id="techclockover'.$row["roid"].'" data-html="true" data-toggle="tooltip" title="Tech Time Clock has exceeded the number of hours sold on this RO. You can disable this in Settings->Custom->Misc" style="color:red;" class="fa fa-exclamation-triangle fa-md"></i>'; }?>
				<?php if (strtoupper($showinsp) == "YES" && $startcountinsp > 0 && $compcountinsp == 0){ echo '<i id="inspstarted" style="color:black;"  data-html="true" data-toggle="tooltip" title="Inspection Started" class="fa fa-clipboard fa-md"></i>'; }?>
				<?php if (strtoupper($showinsp) == "YES" && $compcountinsp > 0){ echo '<i id="inspcomplete" style="color:blue;"  data-html="true" data-toggle="tooltip" title="Inspection Completed" class="fa fa-clipboard fa-md"></i>'; }?>
				<?php if (strtoupper($showinspemail) == "YES" && validateDate1($row["inspectionemailed"]) == 1){ echo '<i id="inspemail" data-html="true" data-toggle="tooltip" title="Inspection Emailed" style="color:red;" class="fa fa-envelope fa-md"></i>'; }?>
				<?php if (strtoupper($showinvemail) == "YES" && validateDate1($row["invoiceemailed"]) == 1){ echo '<i id="invemail" data-html="true" data-toggle="tooltip" title="Invoice Emailed" style="color:navy;" class="fa fa-envelope fa-md"></i>'; }?>
				<?php if (strtoupper($showestemail) == "YES" && validateDate1($row["estimateemailed"]) == 1){ echo '<i id="estemail" data-html="true" data-toggle="tooltip" title="Estimate Emailed" class="fa fa-envelope-o fa-md"></i>'; }?>
				<?php if (strtoupper($showtextemail) == "YES" && $tecount>0){ echo '<i id="textemailsent" data-html="true" data-toggle="tooltip" title="Text/Email Sent" class="fa fa-paper-plane fa-md"></i>'; }?>
				<?php if (strtoupper($showsigonwip) == "YES" && $row['signed']=='yes'){ echo '<i id="rosigned" data-html="true" data-toggle="tooltip" title="RO Signed" class="fa fa-bars fa-md"></i>'; }?>
				<?php if (!empty($row['recall'])){ echo '<i id="recall" data-html="true" data-toggle="tooltip" title="Recall" class="fa fa-car fa-md"></i>'; }?>
				<?php
				if (strlen($commstr) > 0){
					$commstr = substr($commstr,0,strlen($commstr)-4);
					echo '<i id="showpics" style="color:#c0f;font-weight:bold;" data-content="'.$commstr.'" data-html="true" data-toggle="tooltip" title="" class="fa fa-comment fa-lg pop"></i>';
				}
				?>
				<?php if (strlen($comms)>0){?></div><?php }?>
				</td>
			<td style="<?php echo $wback; ?>" class="dacell" onclick="<?php echo $locationstring; ?>" ><?php if (strlen($comms)>0){?><div data-html="true" data-toggle="tooltip" title="<?php echo $comms; ?>" style="max-width:101px;overflow:hidden;white-space: nowrap;" ><?php }?><?php echo $statdate->format('m/d/Y');?><?php if (strlen($comms)>0){?></div><?php }?></td>
			<?php if($showpromiseonwip=='yes'){?><td style="<?php echo $wback; ?>" class="promisecell dacell" onclick="<?php echo $locationstring; ?>"><?php echo $datetimepromised;?></td><?php }?>
			<?php
			if ($showelapsed == "yes"){
			?>
			<td style="<?php echo $wback; ?>" class="dacell" onclick="<?php echo $locationstring; ?>" ><?php if (strlen($comms)>0){?><div data-html="true" data-toggle="tooltip" title="<?php echo $comms; ?>" style="max-width:101px;overflow:hidden;white-space: nowrap;" ><?php }?><?php echo $displaytime;?><?php if (strlen($comms)>0){?></div><?php }?></td>
			<?php
			}
			?>
			<td <?php if($row['origshopid']==$shopid){?>class="cucell" onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>/customer/customer-edit.php?cid=<?php echo $row["customerid"];?>'"<?php }else{?>class="ocucell"<?php }?>><?php if (strlen($comms)>0){?><div data-html="true" data-toggle="tooltip" title="<?php echo $comms; ?>" style="max-width:101px;overflow:hidden;white-space: nowrap;" ><?php }?><?php echo strtoupper($currcustomer);?><?php if (strlen($comms)>0){?></div><?php }?></td>
				<?php
					$h = "";
					if(strlen($home) > 0){
						$h = $h.$home;
						if(strlen($work) > 0 || strlen($cell) > 0){$h = $h."<br>";}
					}
					if(strlen($work) > 0){
						$h = $h.$work;
						if(strlen($cell) > 0){$h = $h."<br>";}
					}
					if(strlen($cell) > 0){$h = $h.$cell;}

				?>
			<td class="phcell" onclick="<?php echo $locationstring; ?>"><div data-html="true" data-toggle="tooltip" title="<?php echo $h;?>" style="max-width:101px;overflow:hidden;white-space: nowrap;" >
				<?php
					$h = "";
					if(strlen($home) > 0){
						echo $home;
						if(strlen($work) > 0 || strlen($cell) > 0){echo " ";}
					}
					if(strlen($work) > 0){
						echo $work;
						if(strlen($cell) > 0){echo " ";}
					}
					if(strlen($cell) > 0){echo $cell;}

				?></div></td>
			<td class="vecell" onclick="<?php echo $locationstring; ?>" ><div data-html="true" data-toggle="tooltip" title="<?php echo strtoupper($row['vin']); ?>" style="overflow:hidden;white-space: nowrap;" ></span>
			<?php
			 if (strlen($row['fleetno']) > 0){
			 	echo "<b>#".strtoupper($row['fleetno'])."</b> ";
			 }
			 echo substr(strtoupper($row["vehinfo"]),0,30);
			 ?><?php if (strlen($comms)>0){?></div><?php }?></td>
			<td class="tocell text-right" onclick="<?php echo $locationstring; ?>" style="padding-right:20px;"><?php if (strlen($comms)>0){?><div data-html="true" data-toggle="tooltip" title="<?php echo $comms; ?>" style="max-width:101px;overflow:hidden;white-space: nowrap;" ><?php }?><?php echo $totalbox;?><?php if (strlen($comms)>0){?></div><?php }?></td>
			<?php if($showhrsonwip=='yes'){?><td class="vecell text-right"><?= $laborhours ?></td><?php }?>
			<td class="licell" onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>/history/history.php?vin=<?php echo $row["vin"]; ?>'" ><?php if (strlen($comms)>0){?><div data-html="true" data-toggle="tooltip" title="<?php echo $comms; ?>" style="max-width:101px;overflow:hidden;white-space: nowrap;" ><?php }?><?php echo strtoupper($row["vehlicense"]);?><?php if (strlen($comms)>0){?></div><?php }?></td>
			<?php if($gponwiplist=='yes'){?><td class="gpcell"><?= round($row['gp']*100)?></td><?php }?>
			<?php if($hasPph=='yes' && $pphAccess == 'yes'){?><td class="pphcell" <?= !empty($pphcolor)?"style='color:".$pphcolor.";font-weight:bold;'":''?>><?= round($row['pph'])?></td><?php }?>
			<?php if($showwtk=='yes'){?><td class="vecell" onclick="<?php echo $locationstring; ?>" ><?php echo $writer; ?></td><?php }?>
			<?php if($showvehcolor=='yes'){?><td class="vecell"><?= strtoupper($vehcolor) ?></td><?php }?>
			<td class="tycell" style="background-color:<?php echo $cc; ?>;" onclick="<?php echo $locationstring; ?>" ><?php if (strlen($comms)>0){?><div data-html="true" data-toggle="tooltip" title="<?php echo $comms; ?>" style="overflow:hidden;white-space: nowrap;" ><?php }?><?php echo strtoupper($row["rotype"]);?><?php if (strlen($comms)>0){?></div><?php }?></td>
			<td style="padding:0px;"><input onclick="showMassUpdates()" data-rec='<?= $moneyrecd?>' id="checkbox<?php echo $row['roid']; ?>" type="checkbox"></td>
			<td style="display: none;"><?php echo strtoupper($row['vin']); ?></td>
		</tr>
<?php

	}

$stmt = "select datetimepromised,totalprts,totallbr,totalsublet,salestax,discountamt,totalfees,gp,statusdate,vin,fleetno,customerid,roid,status as stat,datein,tagnumber,customer,"
	. "customerphone as home,customerwork as work,cellphone as cell,vehinfo,totalro,vehlicense,rotype,coalesce(inspectioncomplete,'') inspectioncomplete,coalesce(inspectionemailed,'') inspectionemailed,coalesce(estimateemailed,'') estimateemailed,coalesce(invoiceemailed,'') invoiceemailed,"
	. " comments,gp,writer,pph,recall from repairorders where shopid = '" . $shopid . "' and statusdate = '".$tdate."' and "
	. "status = 'CLOSED' and ROType != 'No Approval' order by status, roid desc, datein desc";
$result = $conn->query($stmt);
//echo $result->num_rows;
//if ($result->num_rows > 0) {
    // output data of each row
    while($row = $result->fetch_array()) {
    	$home = $row["home"];
    	$work = $row["work"];
    	$cell = $row["cell"];
    	$status = $row["stat"];
    	$statvar = substr($status,0,1);
    	$statdate = new DateTime($row["statusdate"]);
		$statflag = "<img style='width:20px;height:20px;' src='". IMAGE ."/newimages/blueflag.gif' alt=''>";
		$rowclass = "";
		$flagcolor = "blue";
    	if (strlen($row['datetimepromised']) > 0){
	    	$datetimepromised = date("m/d/Y h:i a",strtotime($row['datetimepromised']));
	    }else{
	    	$datetimepromised = "";
	    }

    	if(strlen($row["home"]) > 0){
    		$home = "H: (".substr($row["home"], 0, 3).") ".substr($row["home"], 3, 3)."-".substr($row["home"],6);
    	}
    	if(strlen($row["work"]) > 0){
    		$work = "W: (".substr($row["work"], 0, 3).") ".substr($row["work"], 3, 3)."-".substr($row["work"],6);
    	}
    	if(strlen($row["cell"]) > 0){
    		$cell = "C: &nbsp;(".substr($row["cell"], 0, 3).") ".substr($row["cell"], 3, 3)."-".substr($row["cell"],6);
    	}

    	$cc = "#FFFFFF";
    	$ccstmt = "select colorcode from rotype where shopid = '".$shopid."' and ucase(rotype) = '".strtoupper(str_replace("'","''",$row["rotype"]))."'";
    	$ccresult = $conn->query($ccstmt);
		while($crow = $ccresult->fetch_array()) {
			$cc = "#".$crow['colorcode'];
		}

		$sa = $row['writer'];
		if (strpos($sa," ") > 0){
			$sar = explode(" ",$sa);
			$firstinit = strtoupper(substr($sar[0],0,1));
			if (isset($sar[1])){
				$lastinit = strtoupper(substr($sar[1],0,1));
			}else{
				$lastinit = "";
			}
			if (isset($sar[2])){
				$finalinit = strtoupper(substr($sar[2],0,1));
			}else{
				$finalinit = "";
			}
			$writer = $firstinit.$lastinit.$finalinit;
		}else{
			$writer = substr($row['writer'],0,4);
		}

		$techlist = "";
		$laborhours = 0;
		$techarr = array();
    	$tstmt = "select tech,laborhours from labor where tech != 'discount, discount' and deleted = 'no' and shopid = '".$shopid."' and roid = ".strtoupper($row["roid"]);
    	//echo $tstmt;
    	$tresult = $conn->query($tstmt);
		while($trow = $tresult->fetch_array()) {
			$laborhours+= $trow['laborhours'];
			if(in_array($trow['tech'], $techarr))continue;
			$techarr[] = $trow['tech'];
			if (strpos($trow['tech'],",") > 0){
				$tname1 = $trow['tech'];
				$tar = explode(",",$tname1);
				$tfirst = trim($tar[1]);
				$tlast = trim($tar[0]);
				//echo $tfirst.$tlast;
				$tfirstinit = substr($tfirst,0,1);
				$tlastinit = substr($tlast,0,1);
				$techname = $tfirstinit.$tlastinit;
				$techlist = $techlist.$techname.",";
			}
		}
		//echo $techlist;
		if (substr($techlist,-1) == ","){
			$techlist = substr($techlist,0,strlen($techlist)-1);
		}
		if (strlen($techlist) > 0){
			$writer .= ",".$techlist;
		}


		$clockedtechs = "";
		if (strtoupper($showtechtime) == "YES"){
			$tcstmt = "select GROUP_CONCAT(tech SEPARATOR '<br>') as techs from labortimeclock where shopid = ? and roid = ? and enddatetime is null";
			if($tcquery = $conn->prepare($tcstmt)){
				$tcquery->bind_param("si",$shopid,$row['roid']);
			    $tcquery->execute();
			    $tcquery->bind_result($clockedtechs);
				$tcquery->fetch();
			    $tcquery->close();
			}else{
				echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}

		$moneyrecd = 0;
		if (strtoupper($showmoney) == "YES"){
			$mstmt = "select count(*) as c from accountpayments where shopid = ? and roid = ?";
			if($mquery = $conn->prepare($mstmt)){
				$mquery->bind_param("si",$shopid,$row['roid']);
			    $mquery->execute();
			    $mquery->bind_result($moneyrecd);
				$mquery->fetch();
			    $mquery->close();
			}else{
				echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}
		$comms = strtoupper(str_replace("#","",$row['comments']));

		if (strlen($row['tagnumber']) > 0){
			$roid = $row['roid']."-".$row['tagnumber'];
		}else{
			$roid = $row['roid'];
		}

		if(strtoupper($showtextemail)=='YES')
		{
			$testmt="select id from repairordercommhistory where shopid='$shopid' and roid='".$row['roid']."' and (comm like 'Sent via Text Message:%' || comm like '%An Email Update%') limit 1";
			$teresult = $conn->query($testmt);
            $tecount=$teresult->num_rows;
		}
		else
		$tecount=0;

	    $vehcolor = '';

		if($showvehcolor=='yes' && !empty($row['vehid']))
		{
			$cstmt = "select color from repairorders r,vehicles v where r.shopid=v.shopid and r.vehid=v.vehid and r.shopid = '$shopid' and r.roid = " . $row['roid'];
		    if ($cquery = $conn->prepare($cstmt))
		    {
			 $cquery->execute();
			 $cquery->bind_result($vehcolor);
			 $cquery->fetch();
			 $cquery->close();
		    }
		}

		if(!empty($pphtarget) && $row['pph']>0)
		$pphcolor = ($row['pph']>=$pphtarget?'green':'red');
	    else
	    $pphcolor = '';
?>
		<tr style="background-color:#C0C0C0">
			<td class="rocell" id="<?php echo $row["roid"];?>"><?php if (strlen($comms)>0){?><div data-html="true" data-toggle="tooltip" title="<?php echo $comms; ?>" style="max-width:100%;overflow:hidden;white-space: nowrap;" ><?php }?><?php echo strtoupper($roid);?><?php if (strlen($comms)>0){?></div><?php }?></td>
			<td class="stcell" onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid=<?php echo $row["roid"];?>'"><i class='fa fa-flag fa-lg wipflag_<?php echo $flagcolor; ?>'></i> <?php echo $status." ";?>
				<?php if (strtoupper($showmoney) == "YES" && $moneyrecd > 0){ echo '<i id="moneyrecd" style="color:green;font-weight:bold" data-html="true" data-toggle="tooltip" title="Money Collected from Customer" class="fa fa-usd fa-md"></i>'; } ?>
				<?php if (strtoupper($showtechtime) == "YES" && !empty($clockedtechs)){ echo '<i id="techclock" data-html="true" data-toggle="tooltip" title="'.strtoupper($clockedtechs).' Clocked In" style="color:orange;" class="fa fa-clock-o fa-md"></i>'; }?>
				<?php if (strtoupper($showinsp) == "YES" && validateDate1($row["inspectioncomplete"]) == 1 && $compcountinsp == 0) { echo '<i id="inspstarted" style="color:black;"  data-html="true" data-toggle="tooltip" title="Inspection Started" class="fa fa-clipboard fa-md"></i>'; }?>
				<?php if (strtoupper($showinsp) == "YES" && $compcountinsp > 0){ echo '<i id="inspcomplete" style="color:blue;"  data-html="true" data-toggle="tooltip" title="Inspection Completed" class="fa fa-clipboard fa-md"></i>'; }?>
				<?php if (strtoupper($showinspemail) == "YES" && validateDate1($row["inspectionemailed"]) == 1){ echo '<i id="inspemail" data-html="true" data-toggle="tooltip" title="Inspection Emailed" style="color:red;" class="fa fa-envelope fa-md"></i>'; }?>
				<?php if (strtoupper($showinvemail) == "YES" && validateDate1($row["invoiceemailed"]) == 1){ echo '<i id="invemail" data-html="true" data-toggle="tooltip" title="Invoice Emailed" style="color:navy;" class="fa fa-envelope fa-md"></i>'; }?>
				<?php if (strtoupper($showestemail) == "YES" && validateDate1($row["estimateemailed"]) == 1){ echo '<i id="estemail" data-html="true" data-toggle="tooltip" title="Estimate Emailed" class="fa fa-envelope-o fa-md"></i>'; }?>
				<?php if (strtoupper($showtextemail) == "YES" && $tecount>0){ echo '<i id="textemailsent" data-html="true" data-toggle="tooltip" title="Text/Email Sent" class="fa fa-paper-plane fa-md"></i>'; }?>
				<?php if (!empty($row['recall'])){ echo '<i id="recall" data-html="true" data-toggle="tooltip" title="Recall" class="fa fa-car fa-md"></i>'; }?>

			</td>
			<td class="dacell" onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid=<?php echo $row["roid"];?>'" ><?php echo $statdate->format('m/d/Y');?></td>
			<?php if($showpromiseonwip=='yes'){?><td class="promisecell dacell" onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid=<?php echo $row["roid"];?>'"><?php echo $datetimepromised;?></td>
			<?php
		    }
			if ($showelapsed == "yes"){
			?>
			<td></td>
			<?php
			}
			?>
			<td class="cucell" ><?php echo strtoupper($row["customer"]);?></td>
				<?php
					$h = "";
					if(strlen($home) > 0){
						$h = $h.$home;
						if(strlen($work) > 0 || strlen($cell) > 0){$h = $h."<br>";}
					}
					if(strlen($work) > 0){
						$h = $h.$work;
						if(strlen($cell) > 0){$h = $h."<br>";}
					}
					if(strlen($cell) > 0){$h = $h.$cell;}

				?>
			<td class="phcell" onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid=<?php echo $row["roid"];?>'"><div data-html="true" data-toggle="tooltip" title="<?php echo $h;?>" style="max-width:117px;overflow:hidden;white-space: nowrap;" >
				<?php
					$h = "";
					if(strlen($home) > 0){
						echo $home;
						if(strlen($work) > 0 || strlen($cell) > 0){echo " ";}
					}
					if(strlen($work) > 0){
						echo $work;
						if(strlen($cell) > 0){echo " ";}
					}
					if(strlen($cell) > 0){echo $cell;}

				?></div></td>
			<td class="vecell" onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid=<?php echo $row["roid"];?>'" ><div data-html="true" data-toggle="tooltip" title="<?php echo strtoupper($row['vin']);?>" style="max-width:117px;overflow:hidden;white-space: nowrap;" ><?php echo substr(strtoupper($row["vehinfo"]),0,30);?></div></td>
			<td class="tocell text-right" onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid=<?php echo $row["roid"];?>'" style="padding-right:20px;"><?php echo number_format($row["totalro"],2);?></td>
			<?php if($showhrsonwip=='yes'){?><td class="vecell text-right"><?= $laborhours ?></td><?php }?>
			<td class="licell" onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid=<?php echo $row["roid"];?>'" ><a style="font-weight:bold" id="roid-<?php echo $row['roid'];?>" onclick="loadHistory(event,this);" href="#"><?php echo strtoupper($row["vehlicense"]);?></a></td>
			<?php if($gponwiplist=='yes'){?><td class="gpcell" onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid=<?php echo $row["roid"];?>'"><?= round($row['gp']*100)?></td><?php }?>
			<?php if($hasPph=='yes' && $pphAccess == 'yes'){?><td class="pphcell" <?= !empty($pphcolor)?"style='color:".$pphcolor.";font-weight:bold;'":''?> onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid=<?php echo $row["roid"];?>'"><?= round($row['pph'])?></td><?php }?>
            <?php if($showwtk=='yes'){?><td class="vecell" onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid=<?php echo $row["roid"];?>'" ><?php echo $writer; ?></td><?php }?>
			<?php if($showvehcolor=='yes'){?><td class="vecell" onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>ro/roclosed.php?roid=<?php echo $row["roid"];?>'"><?= strtoupper($vehcolor) ?></td><?php }?>
			<td colspan="2" class="tycell" onclick="parent.location.href='<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid=<?php echo $row["roid"];?>'" ><?php echo strtoupper($row["rotype"]);?></td>
			<td style="display: none;"><?php echo strtoupper($row['vin']); ?></td>
		</tr>
<?php
	}

	$stmt = "select ps.shopid,psid,`status`,psdate,concat(customer.firstname,' ',customer.LastName) as customer,`total`,cid,'','ps',statusdate"
		. " from ps left join customer on ps.shopid = customer.shopid and ps.cid = customer.CustomerID where "
		. " ps.`status` != 'CLOSED' and ps.`status` != 'DEAD' and ps.shopid = '" . $shopid . "'";

	if (!empty($writerfilter))
	$stmt .= " and writer = ?";
    
    $query = $conn->prepare($stmt);
	if (!empty($writerfilter))
	$query->bind_param("s",$writerfilter);
	$query->execute();
	$result = $query->get_result();

	while($row = $result->fetch_array()) {
		$statdate = new DateTime($row["statusdate"]);
		//echo "Location String " . $locationstr;
			$locationstr = "parent.location.href='". COMPONENTS_PRIVATE . "/partsale/partsale.php?psid=".$row["psid"]."'";
?>
		<tr style="background-color:#E8FFE8">
			<td class="pscell" onclick="<?php echo $locationstr; ?>" >PS - <?php echo $row["psid"];?></td>
			<td class="stcell" onclick="<?php echo $locationstr; ?>"><?php echo strtoupper($row['status']);?></td>
			<td class="dacell" onclick="<?php echo $locationstr; ?>" ><?php echo $statdate->format('m/d/Y');?></td>
			<?php if($showpromiseonwip=='yes'){?><td class="promisecell dacell"></td><?php }?>
			<?php
			if ($showelapsed == "yes"){
			?>
			<td></td>
			<?php
			}
			?>
			<td onclick="<?php echo $locationstr; ?>" style="font-size:12px;" ><?php echo $row["customer"];?></td>
			<td onclick="<?php echo $locationstr; ?>" class="phcell"></td>
			<td onclick="<?php echo $locationstr; ?>" class="vecell"></td>
			<td class="tocell text-right" onclick="<?php echo $locationstr; ?>" style="padding-right:20px;"><?php echo number_format($row["total"],2);?></td>
			<?php if($showhrsonwip=='yes'){?><td class="vecell"></td><?php }?>
			<td onclick="<?php echo $locationstr; ?>" class="licell"></td>
			<?php if($gponwiplist=='yes'){?><td>&nbsp;</td><?php }?>
			<?php if($isprofitboost=='yes'){?><td>&nbsp;</td><?php }?>			
			<?php if($showwtk=='yes'){?><td>&nbsp;</td><?php }?>
			<?php if($showvehcolor=='yes'){?><td>&nbsp;</td><?php }?>
			<td colspan="2" onclick="<?php echo $locationstr; ?>" class="tycell">PART SALE</td>
		</tr>

<?php
	}

mysqli_close($conn);
?>
	</tbody>
</table>
<?php
$etime = (microtime(true) - $stime) / 60;
//echo "WIP Data Load: ".number_format($etime,4)." seconds";
?>
</div>

<script>

function setSort(sorttype){

	//console.log(sorttype)
	Cookies.set('wipsort', sorttype, { expires: 365 });

	// loop through the header to find sort type
	//console.log(sorttype)
	sortorder = ""
	setTimeout(function(){
		$('#wiplist-table  thead  tr th').each(function(){
			classname = $(this).attr("class")
			//console.log(classname)
			if (classname.indexOf("headerSort") > 1){
				if (classname.indexOf("headerSortDown") > 1){
					sortorder = "0"
				}else if(classname.indexOf("headerSortUp") > 1){
					sortorder = "1"
				}
				return
			}
		})

		localStorage.setItem("wipsortorder", sortorder);
		//console.log("set sort order:"+sortorder)
	},1000)

}

function showPreRO(id){

	t = "Convert PreRO"
	p = "<?= COMPONENTS_PRIVATE ?>/prero/prero.php?shopid=<?php echo $shopid; ?>&id="+id
	eModal.iframe({
		title:t,
		url: p,
		size: eModal.size.xl,
		buttons: [
			{text: 'Close', style: 'warning', close:true}
    	]
	});

}

function cancelMassUpdates(){

	$(":checkbox").each(function(){

		$(this).attr('checked', false)

	});

	$('#massupdates').fadeOut()

}

function massUpdate(){

	//close final noapproval
	r = $('#massaction').val()

	roids = ""
	zeroroid = ""

	$(":checkbox").each(function(){

		if ($(this).is(':checked')){
				roid = $(this).attr("id")
				roid = roid.replace("checkbox","")
				roids = roids + roid + "|"
				if($(this).attr("data-rec")==0)
				zeroroid = $(this).attr("data-rec")
				return
		}

	});

	if (r == "close"){

		if('<?= $requirepayments?>'=='yes' && zeroroid!='')
		{
        msg = "One or more ROs cannot be closed as payment is required. Would you still like to continue?"
		btn = "Yes"
		}
		else
		{
		msg = "Are you sure you want to CLOSE the selected repair orders?"
		btn = "Yes, Close Them"
	    }
	}else if (r == "final"){
		msg = "Are you sure you want to FINALIZE the selected repair orders?"
		btn = "Yes, Finalize Them"
	}else if (r == "noapproval"){
		msg = "Are you sure you want to mark the selected repair orders as NO APPROVAL?"
		btn = "Yes, mark them No Approval"
	}

	swal({
		title: "Are you sure?",
		text: msg,
		type: "warning",
		showCancelButton: true,
		confirmButtonClass: "btn-danger",
		confirmButtonText: btn,
		closeOnConfirm: false
	},
	function(){

		// get the ro numbers that are checked
		parent.$('#spinner').show().css("z-index","9999999999")

		ds = "r="+r+"&shopid=<?php echo $shopid; ?>&roids="+roids
		//console.log(ds)

		$.ajax({

			data: ds,
			url: "massupdate.php",
			type: "post",
			success: function(r){
				//console.log(r)
				if (r == "success"){
					parent.location.reload()
				}
			},
			error: function (xhr, ajaxOptions, thrownError) {
				console.log(xhr.status);
				console.log(xhr.responseText);
				console.log(thrownError);
				parent.$('#spinner').hide()
			}
		})
	});

}

function showMassUpdates(){

	// loop all checkboxes, if any are checked show the massupdate div

	cntr = 0
	$(":checkbox").each(function(){

		if ($(this).is(':checked')){
			cntr = cntr + 1
			return
		}

	});

	if (cntr > 0){
		$('#massupdates').fadeIn()
	}else{
		$('#massupdates').fadeOut()
	}

}


$(document).ready(function(){

	$(".pop").popover({ trigger: "manual" , html: true, animation:false})
	    .on("mouseenter", function () {
	        var _this = this;
	        $(this).popover("show");
	        $(".popover").on("mouseleave", function () {
	            $(_this).popover('hide');
	        });
	    }).on("mouseleave", function () {
	        var _this = this;
	        setTimeout(function () {
	            if (!$(".popover:hover").length) {
	                $(_this).popover("hide");
	            }
	        }, 300);
	});



	$('#srch').focus()
	top.document.getElementById("opencounter").value = "<?php echo $opencounter; ?>";
	if("<?php echo $opencounter; ?>">=1000 && sessionStorage.getItem("hideovercount") === null)
	{
	 parent.$('.head-alert').hide()
	 parent.$('#overcount').show()
	}

	<?php
	if (strtoupper($showtechoverhours) == "YES"){
	?>
	setTimeout(function(){
		$.ajax({
			url: "techclockchecks.php",
			data: "shopid=<?php echo $shopid; ?>",
			type: "post",
			success: function(r){

				if (r!=''){
					rar = r.split(",")
					for (i=0; i < rar.length; i++){
						elemname = 'techclockover'+rar[i]
						document.getElementById(elemname).style.display = "inline-block"
					}
				}
			},
			error: function (xhr, ajaxOptions, thrownError) {
				console.log(xhr.status);
				console.log(xhr.responseText);
				console.log(thrownError);
			}

		});
	},60000)
	<?php
	}
	?>
	x = Cookies.get('wipsort');
	if (localStorage.getItem("wipsortorder")){
		sortorder = localStorage.getItem("wipsortorder")
	}else{
		sortorder = "0"
	}

	if (typeof x == "undefined"){
		$('#wiplist-table').tablesorter().bind('updateComplete', function(e, table){
		})

	}else{

		var counter = 0;
		if (x == "ro"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;
		if (x == "status"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;
		if (x == "date"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;
		<?php if($showpromiseonwip=='yes'){?>if (x == "promised"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;<?php }?>
		<?php if($showelapsed=='yes'){?>if (x == "elapsed"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;<?php }?>
		if (x == "customer"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;
		if (x == "phone"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;
		if (x == "vehicle"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;
		if (x == "totalro"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;
		<?php if($showhrsonwip=='yes'){?>if (x == "hrs"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;<?php }?>
    	if (x == "license"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;
		<?php if($gponwiplist=='yes'){?>if (x == "gp"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;<?php }?>
		<?php if($hasPph=='yes' && $pphAccess == 'yes'){?>if (x == "pph"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;<?php }?>
		<?php if($showwtk=='yes'){?>if (x == "writer"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;<?php }?>
		<?php if($showvehcolor=='yes'){?>if (x == "color"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;<?php }?>
		if (x == "type"){$('#wiplist-table').tablesorter({sortList: [[counter,sortorder]]}); }counter++;
	}
});

$('.rocell').on('click touchstart',function(){

	parent.location.href='<?= COMPONENTS_PRIVATE ?>/ro/ro.php?roid='+$(this).attr("id")

});

$('#srch').keyup(function(){
	$('#spinner').show()
	searchTable($(this).val());
	searchClosed($(this).val());
});

function searchTable(inputVal){

	var table = $('#wiplist-table');
   // inputVal = inputVal.replace(/[()\s/-]/g,'')
    //console.log(inputVal)

	table.find('tr').each(function(index, row){
		var allCells = $(row).find('td');
		if(allCells.length > 0){
			var found = false;
			allCells.each(function(index, td){
				var regExp = new RegExp(inputVal, 'i');

                var col_val = $(td).text()
                var phone_space = col_val.replace(/[()]/g,'')
                phone_space = phone_space.replace("-", ' ');
                //console.log(phone_space);
				if(regExp.test(col_val) || col_val.indexOf(inputVal)!== -1 || col_val.replace(/[()\s/-]/g,'').indexOf(inputVal) !== -1 || phone_space.indexOf(inputVal) !== -1){
					found = true;
					return false;
				}
			});
			if(found == true){
				$(row).show();
				$('#spinner').hide()
			}else {
				$(row).hide();
				$('#hide').toggle()
			}
		}
	});

}

function searchClosed(v){
	ds = "shopid=<?php echo $shopid; ?>&sf="+encodeURIComponent(v)+"&gp=<?= $gponwiplist?>"
	//console.log(ds)
	$.ajax({
		data: ds,
		url: "wipsearchclosed.php",
		success: function(r){
			//console.log(r)
			$('#table-container').remove()
			$('#table-container-wip').append(r)
		},
		error: function (xhr, ajaxOptions, thrownError) {
		           console.log(xhr.status);
		           console.log(xhr.responseText);
		           console.log(thrownError);
		}
	});
}

function clearSearch(){
	$('#srch').val('')
	searchTable('')
}

</script>
</body>
</html>
