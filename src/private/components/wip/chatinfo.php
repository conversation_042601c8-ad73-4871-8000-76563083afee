<?php

require CONN;

$shopid = $_POST['shopid'];
$usr = $_POST['usr'];
$t = $_POST['t'];

if ($t == "sendmsg"){

	$to = $_POST['to'];
	$msg = $_POST['msg'];
	$ts = localTimeStamp($shopid);

	if (strpos($to,",") > 0){
		$tar = explode(",",$to);
		foreach ($tar as $v){

			$stmt = "insert into msnger (shopid,`from`,`to`,message,ts) values (?,?,?,?,?)";
			if ($query = $conn->prepare($stmt)){

				$query->bind_param("sssss",$shopid,$usr,$v,$msg,$ts);
				$query->execute();
				$conn->commit();
				$query->close();

			}
		}
	}else{
		$stmt = "insert into msnger (shopid,`from`,`to`,message,ts) values (?,?,?,?,?)";
		if ($query = $conn->prepare($stmt)){

			$query->bind_param("sssss",$shopid,$usr,$to,$msg,$ts);
			$query->execute();
			$conn->commit();
			$query->close();

		}
	}


}elseif ($t == "getmsg"){

	$stmt = "select `from`,`to`,message,ts from msnger where shopid = ? and (`from` = ? or `to` = ?) order by ts desc limit 50";
	//echo $stmt."<BR>";
	$msgbox = "";
	if ($query = $conn->prepare($stmt)){
        $query->bind_param("sss",$shopid,$usr,$usr);
		$query->execute();
		$query->store_result();
		$rowcount = $query->num_rows;
		if ($rowcount > 0){
			$query->free_result();
			$query->execute();
			$result = $query->get_result();
			$result_array = $result->fetch_all();
			$row = array_reverse($result_array,true);
			foreach ($row as $k => $v){
				if (htmlspecialchars_decode($v[0],ENT_QUOTES) == htmlspecialchars_decode($usr,ENT_QUOTES)){
					$msgbox .= "<div class='alert alert-success' style='text-align:right;font-size:12px;width:75%;margin-left:25%'><b>".strtoupper($usr)." -&gt; ".strtoupper($v[1])."</b>:<br>".strtoupper($v[2])."<br><span style='font-size:8pt'>".$v[3]."</span></div>";
				}else{
					$msgbox .= "<div class='alert alert-info' style='text-align:left;font-size:12px;width:75%;margin-right:25%'><b>".strtoupper($v[0])." -&gt; ".strtoupper($v[1])."</b>:<br>".strtoupper($v[2])."<br><span style='font-size:8pt'>".$v[3]."</span></div>";
				}

			}


		}else{
			$msgbox .= "<div class='alert alert-warning' style='text-align:center'>You have no Instant Messages. Select an employee below and type a message to them</div>";
		}


		$query->close();
	}

	echo $msgbox;

	$stmt = "update msnger set viewed = 'yes' where shopid = '$shopid' and `to` = '$usr' and viewed = 'no'";
	//echo $stmt;
	if ($query = $conn->prepare($stmt)){

		$query->execute();
		$conn->commit();
		$query->close();

	}



}elseif ($t == "countmsg"){

	$stmt = "select count(*) c from msnger where shopid = '$shopid' and `to` = '$usr' and viewed = 'no'";
	if ($query = $conn->prepare($stmt)){

		$query->execute();
		$query->bind_result($rowcount);
		$query->fetch();
		echo $rowcount;
		$query->close();
	}


}
elseif ($t == "markread"){

	$stmt = "update msnger set viewed = 'yes' where shopid = '$shopid' and (`from` = '$usr' or `to` = '$usr') and viewed = 'no'";
	if ($query = $conn->prepare($stmt)){

		$query->execute();
		$conn->commit();
		$query->close();

	}


}

mysqli_close($conn);
?>
