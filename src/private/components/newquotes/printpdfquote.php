<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

include INTEGRATIONS_PATH . "/TCPDF/tcpdf.php";

class MYPDF extends TCPDF
{
    public $footerHTML = "";

    public function Header()
    {
        $headerData = $this->getHeaderData();
        $this->writeHTML($headerData['string']);
    }

    // Page footer
    public function Footer()
    {
        $this->writeHTML($this->footerHTML);
        $this->SetFont('helvetica', 'B', 8);
        $this->SetXY($this->getPageWidth() - 90, $this->getPageHeight() - 25);
        $pageNumTBL = '<table style="width: 100pt"><tr><td>Page ' . $this->getAliasNumPage() . ' OF ' . $this->getAliasNbPages() . '</td></tr></table>';
        $this->writeHTML($pageNumTBL, true, false, true, false, 'R');
    }
}


require(INTEGRATIONS_PATH . "/mandrill/src/Mandrill.php");
require CONNWOSHOPID;


$shopid = isset($_REQUEST['shopid']) ? filter_var($_REQUEST['shopid'], FILTER_SANITIZE_STRING) : "";
$quoteid = isset($_REQUEST['quoteid']) ? filter_var($_REQUEST['quoteid'], FILTER_SANITIZE_NUMBER_INT) : 0;
$emailto = $_REQUEST['emailto'] ?? '';

if (file_exists("custom/" . $shopid . "/printpdfquote.php")) {
    header("location:custom/" . $shopid . "/printpdfquote.php?shopid=" . $shopid . "&quoteid=" . $quoteid);
    exit;
}

$stmt = "select * from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $results = $query->get_result();
    $rs = $results->fetch_assoc();
    $query->close();
} else {
    echo "company Prepare failed: (" . $conn->errno . ") " . $conn->error;

}

$shopname = $rs["CompanyName"];
$feesonquote = strtolower($rs["feesonquote"]);
$shopaddress = $rs["CompanyAddress"];
$shopcity = $rs["CompanyCity"];
$shopzip = $rs["CompanyZip"];
$shopcsz = $rs["CompanyCity"] . ", " . $rs["CompanyState"] . ". " . $rs["CompanyZip"];
$shopphone = formatPhone($rs["CompanyPhone"]);
$shopemail = $rs["CompanyEMail"];
$shopurl = $rs["CompanyURL"];
$showPartNumbers = strtolower($rs['showpartnumberonprintedro']);
$shopepa = $shopbar = "";
$quotelabel = strtoupper($rs['quotelabel']);

if (strlen($rs['EPANo']) > 2) {
    $shopepa = "EPA# " . $rs['EPANo'];
}
if (strlen($rs['BarNo']) > 2) {
    $shopbar = "BAR# " . $rs['BarNo'];
}

$warrdisc = $rs["rowarrdisclosure"];
$sigdisc = $rs["rodisclosure"];
$showpayments = strtolower($rs["showpayments"]);
$techstory = strtolower($rs["printtechstory"]);
$showlaborhours = strtolower($rs["showlaborhoursonro"]);
$milesinlabel = $rs["milesinlabel"];
$milesoutlabel = $rs["milesoutlabel"];
$replacerowithtag = strtolower($rs["replacerowithtag"]);
$quotedisclosure = strtoupper($rs["quotedisclosure"]);
$logo = $rs["logo"];
$logo_img = "";
$logo_path = "https://" . $_SERVER['SERVER_NAME'] . "/sbp/newimages/quoteheader.png";
$logos_dir = UPLOAD_PATH . $shopid . DIRECTORY_SEPARATOR;

$logoURL = "";
$logoPath = "";
$hasLogo = true;
if (!empty($logo)) {
    $logoPath = "\\fs.shopboss.aws\share\upload\\$shopid\\$logo";
    $logoURL = "https://" . $_SERVER['SERVER_NAME'] . "/sbp/upload/$shopid/$logo";
} else {
    if ($rostatus == "FINAL" || $rostatus == "CLOSED") {
        if (strlen($invoicetitle) > 1) {
            $logoURL = IMAGE . "/newimages/invoicelogo.png";
            $logoPath = $logoURL;
        }
    } else {
        if (strlen($estimatetitle) > 1) {
            if (strtolower($estimatetitle) == "estimate") {
                $logoURL = IMAGE . "/newimages/estimate.png";
                $logoPath = $logoURL;
            }
        }
    }
    $hasLogo = false;
}

$printroid = $quoteid;

$stmt = "select * from quotes where shopid = ? and id = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $printroid);
    $query->execute();
    $results = $query->get_result();
    $rs = $results->fetch_assoc();
    $query->close();
} else {
    echo "quotes Prepare failed: (" . $conn->errno . ") " . $conn->error;

}

$cust = $rs["customer"];
$addr = $rs["address"];
$csz = $rs["city"] . ", " . $rs["state"] . ". " . $rs["zip"];
$cphone = "Phone: " . $rs["phone"];
$veh = $rs["year"] . " " . $rs["make"] . " " . $rs["model"];
$notes = $rs["notes"];


if ($shopid == "3880" || $shopid == "5638" || $shopid == "19394" || $shopid == "19893") {
    $quotedisclosure .= "<br /><br /><p><strong><u>Notes: </u></strong>
" . strtoupper($notes) . "</p>";
}
if ($shopid == "22627"){
    $quotedisclosure .= '<br><br><div style="text-align: center">ASK US ABOUT OUR INTEREST FREE PAYMENT PROGRAM !!</div>';
}

$emailline = "<br /><br />" . $shopemail;
if (!empty($shopurl)) {
    $emailline .= " <br />" . $shopurl;
}
$quoteDateTs = strtotime($rs['quotedate']);
$quoteDate = date('n/j/Y', $quoteDateTs);
$emailline .= "<br />".ucfirst(strtolower($quotelabel))." Date: " . $quoteDate . "<br />Date Printed: " . date('n/j/Y');

if ($feesonquote == "yes") {
    if (!empty($rs["fee1label"])) {
        if (!empty($rs["fee1percent"])) {
            $fee1amount = number_format(($rs["totalparts"] + $rs["totallabor"] + $rs["totalsublet"]) * ($rs["fee1percent"] / 100), 2);
        } else {
            $fee1amount = number_format($rs["fee1amount"], 2);
        }
    }
    if (!empty($rs["fee2label"])) {
        if ($rs["fee2percent"]) {
            $fee2amount = number_format(($rs["totalparts"] + $rs["totallabor"] + $rs["totalsublet"]) * ($rs["fee2percent"] / 100), 2);
        } else {
            $fee2amount = number_format($rs["fee2amount"], 2);
        }
    }
    if (!empty($rs["fee3label"])) {
        if (!empty($rs["fee3percent"])) {
            $fee3amount = number_format(($rs["totalparts"] + $rs["totallabor"] + $rs["totalsublet"]) * ($rs["fee3percent"] / 100), 2);
        } else {
            $fee3amount = number_format($rs["fee3amount"], 2);
        }
    }
}

$address = str_replace("'", "''", $shopaddress);
$taxable = "yes";
$stmt = "select taxexempt from customer where shopid = ? and address = ? and city = ? and zip = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("ssss", $shopid, $address, $shopcity, $shopzip);
    $query->execute();
    $query->store_result();
    $query->bind_result($taxexempt);
    $query->fetch();
    if ($query->num_rows > 0) {
        if (strtolower($taxexempt) == "yes") {
            $taxable = "no";
        }
    }
    $query->close();
} else {
    echo "customer Prepare failed: (" . $conn->errno . ") " . $conn->error;

}


$totallabor = asDollars($rs['totallabor']);
$totalparts = asDollars($rs['totalparts']);
$totalsublet = asDollars($rs['totalsublet']);
$subtotal = asDollars($rs['totallabor'] + $rs['totalparts'] + $rs['totalsublet']);
$totalfees = asDollars($rs['totalfees']);
$salestax = $rs['salestax'];
$psalestax = asDollars($salestax);
$total = asDollars($rs['totalquote']);

$totalquote = asDollars($rs['totalquote']);
$totalro = $rs['totalquote'];

$tlines = 0;
$issueno = 1;
$complaintHTML = "";
$cstmt = "SELECT complaintid, UCASE(complaint), UCASE(issue), displayorder FROM quotecomplaints WHERE shopid = ? AND quoteid = ? ORDER BY displayorder";
if ($cquery = $conn->prepare($cstmt)) {
    $cquery->bind_param("si", $shopid, $quoteid);
    $cquery->execute();
    $cquery->bind_result($complaintid, $complaint, $issue, $displayorder);
    $cquery->store_result();
    while ($cquery->fetch()) {
        $complaint = $complaint ?? "QUOTE";
        $complaintHTML .= '<tr><td colspan="4" style="font-size: 10pt; font-weight: bold; border-bottom: 1px solid black; padding-top: 2pt; padding-bottom: 2pt;">'.$issueno.'.'.$complaint.'</td></tr>';

        $part_lines = "";
        $pstmt = "select * from quoteparts where shopid = ? and quoteid = ? and complaintid = ?";
        if ($pquery = $conn->prepare($pstmt)) {
            $pquery->bind_param("sii", $shopid, $printroid, $complaintid);
            $pquery->execute();
            $presults = $pquery->get_result();
            while ($prs = $presults->fetch_assoc()) {
                $partnumber = ($showPartNumbers=='yes') ? strtoupper($prs['partnumber']) : "";
                $part_lines .= '<tr style="font-size: 8pt;">';
                $part_lines .= '<td style="width: 30%; font-size: 8pt;"><table><tr><td style="width: 20%;">PART</td><td style="width: 80%">: ' . $partnumber . '</td></tr></table></td>';
                $part_lines .= '<td style="width: 40%; font-size: 8pt;">' . strtoupper($prs['part']) . '</td>';
                $part_lines .= '<td style="width: 15%; text-align: right">' . $prs['qty'] . " @ " . asDollars($prs['price']) . '</td>';
                $part_lines .= '<td style="width: 15%; text-align: right">' . asDollars($prs['extprice']) . '</td>';
                $part_lines .= '</tr>';             
                $tlines++;
            }
            $pquery->close();
        } else {
            echo "quotes prts Prepare failed: (" . $conn->errno . ") " . $conn->error;

        }


        $labor_lines = "";
        $lstmt = "select * from quotelabor where shopid = ? and quoteid = ? and complaintid = ?";
        if ($lquery = $conn->prepare($lstmt)) {
            $lquery->bind_param("sii", $shopid, $printroid, $complaintid);
            $lquery->execute();
            $lresults = $lquery->get_result();
            while ($lrs = $lresults->fetch_assoc()) {
                if ($showlaborhours == "yes") {
                    $laborhours = $lrs['hours'] . " hours";
                } else {
                    $laborhours = "";
                }
                $labor_lines .= '<tr style="font-size: 8pt">';
                $labor_lines .= '<td style="width: 30%; text-align: left"><table><tr><td style="width: 30pt;">LABOR </td><td style="width: 110pt;">:</td></tr></table></td>';
                $labor_lines .= '<td style="width: 40%;">' . strtoupper($lrs['labor']) . '</td>';
                $labor_lines .= '<td style="width: 15%; text-align: right">' . $laborhours . '</td>';
                $labor_lines .= '<td style="width: 15%; text-align: right">' . asDollars($lrs['extlabor']) . '</td>';
                $labor_lines .= '</tr>';
                $tlines++;
            }
            $lquery->close();
        } else {
            echo "quotes prts Prepare failed: (" . $conn->errno . ") " . $conn->error;

        }

        $sublet_lines = "";
        $sstmt = "select * from quotesublet where shopid = ? and quoteid = ? and complaintid = ?";
        if ($squery = $conn->prepare($sstmt)) {
            $squery->bind_param("sii", $shopid, $printroid, $complaintid);
            $squery->execute();
            $sresults = $squery->get_result();
            while ($srs = $sresults->fetch_assoc()) {
                $sublet_lines .= '<tr style="font-size: 8pt">';
                $sublet_lines .= '<td style="width: 30%; text-align: left"><table><tr><td style="width: 30pt;">SUBLET </td><td style="width: 110pt;">:</td></tr></table></td>';
                $sublet_lines .= '<td style="width: 40%;">' . strtoupper($srs['description']) . '</td>';
                $sublet_lines .= '<td style="width: 15%; text-align: right"> &nbsp; </td>';
                $sublet_lines .= '<td style="width: 15%; text-align: right">' . asDollars($srs['price']) . '</td>';
                $sublet_lines .= '</tr>';
                $tlines++;
            }
            $squery->close();
        } else {
            echo "quotes prts Prepare failed: (" . $conn->errno . ") " . $conn->error;

        }

        $complaintHTML .= $part_lines . $labor_lines . $sublet_lines;
        $issueno++;
    }
    $cquery->close();
}

$body = '<table style="width:100%" cellspacing="4">' . $complaintHTML . '</table>';

$headerTable = <<<HEADERTABLE
    <table>
        <tr>
            <td style="text-align: left; font-size: 10pt;"><span>$shopname</span><br/><br/>$shopaddress<br/>$shopcsz<br/>$shopphone</td>
            <td style="text-align: center;">
                <img src="$logoPath" style="height:75pt; padding-top:20px" />
            </td>
            <td style="text-align: right;">
                <span style="font-size: 16pt; font-weight: bold;">$quotelabel # $printroid&nbsp;</span>
                <span style="font-size: 10pt">$emailline &nbsp;</span>
            </td>
        </tr>
    </table>
    <hr/>
    <table cellspacing="4" style="font-size: 8pt;">
        <tr>
            <td>$cust<br/>$addr<br/> $csz<br/>$cphone</td>
            <td style="padding-top: 4pt; text-align: right;">$veh</td>
        </tr>
    </table>
    <hr/>
HEADERTABLE;

$footerTable = <<<FOOTERTABLE
    <table width="100%" cellspacing="0" cellpadding="4" border="0">
        <tr>
            <td style="font-size: 7pt; width: 70%; border:1px solid black; vertical-align: top;"><strong>Disclosure:</strong> $quotedisclosure</td>
            <td style="width: 30%;">
                <table width="100%" cellspacing="0" cellpadding="2" border="1">
                    <tr>
                        <td style="font-size: 7pt; text-align: left;">Labor</td>
                        <td style="font-size: 7pt; text-align: right;">$totallabor</td>
                    </tr>
                    <tr>
                        <td style="font-size: 7pt; text-align: left;">Parts</td>
                        <td style="font-size: 7pt; text-align: right;">$totalparts</td>
                    </tr>
                    <tr>
                        <td style="font-size: 7pt; text-align: left;">Sublet</td>
                        <td style="font-size: 7pt; text-align: right;">$totalsublet</td>    
                    </tr>
                    <tr>
                        <td style="font-size: 7pt; text-align: left;">Subtotal</td>
                        <td style="font-size: 7pt; text-align: right;">$subtotal</td>    
                    </tr>
                    <tr>
                        <td style="font-size: 7pt; text-align: left;">Fees</td>
                        <td style="font-size: 7pt; text-align: right;">$totalfees</td>
                    </tr>
                    <tr>
                        <td style="font-size: 7pt; text-align: left;">Tax</td>
                        <td style="font-size: 7pt; text-align: right;">$psalestax</td>
                    </tr>
                    <tr>
                        <td style="font-size: 7pt; text-align: left;">Total</td>
                        <td style="font-size: 7pt; text-align: right;">$total</td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
FOOTERTABLE;


$header_margin = 160;
$footer_margin = 120;

$pdf = new MYPDF(PDF_PAGE_ORIENTATION, 'pt', array(612, 780), true, 'UTF-8', false);

$pdf->setHeaderData('', 0, '', $headerTable, array(0, 0, 0), array(0, 0, 0));
$pdf->footerHTML = $footerTable;

$pdf->setPrintHeader(true);
$pdf->setPrintFooter(false);

$pdf->setCellPaddings(0, 0, 0, 0);
$pdf->SetMargins(30, $header_margin, 30);
$pdf->SetHeaderMargin(10);

$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

$pdf->AddPage();

$pdf->writeHTML(utf8_encode($body), true, false, true, false, '');

if ($pdf->GetY() > 660){
    $pdf->endPage();
    $pdf->AddPage();
}
$pdf->footerHTML = $footerTable;
$pdf->setPrintHeader(false);
$pdf->setPrintFooter(true);
$pdf->SetFooterMargin($footer_margin);
$pdf->SetAutoPageBreak(true, $footer_margin + 10);

$newfilename = "TP_" . $shopid . "_" . $roid . "_" . date('Y') . "_" . date('n') . "_" . date('j') . "_" . date('H') . "_" . date('i') . "_" . date('s') . "_" . time() . ".pdf";

ob_end_clean();


if (!empty($emailto)) {
    $pdf = $pdf->output('', 'S');
    $subject = "Repair Quote from " . $shopname;

    $check=true;$ct=1;$rand='';
    while($check && $ct<=20)
    {
    $rand=generateRandomStr(5);
    $stmt = "select id from urlshort where url = ? limit 1";
    if ($query = $conn->prepare($stmt))
    {
    $query->bind_param("s",$rand);
    $query->execute();
    $query->store_result();
    $num = $query->num_rows();
    $query->close();
    if($num<1)$check=false;
    }
    $ct++;
    }

    if(empty($rand))die();

    $stmt = "Insert into `urlshort` set `shopid`=?,`roid`=?,`url`=?,`type`='quote'";
    if ($query = $conn->prepare($stmt))
    {
    $query->bind_param("sss",$shopid,$quoteid,$rand);
    $query->execute();
    $conn->commit();
    $query->close();
    }

    $url="https://sbpt.io/".$rand;

    //$url = "https://".$_SERVER['SERVER_NAME']."/quotestatus.php?".base64_encode("quoteid=$quoteid&shopid=$shopid");
    
    $message = "Please visit the following link to view the Quote. <a href='$url'>$url</a> <br><br>Attached is a copy of a quote from " . $shopname . ".<br><br> If you have any questions or would like to schedule this repair, just give us a call.<br><br>" . $shopname . "<br>" . $shopaddress . "<br>" . $shopcsz . "<br>" . $shopphone . "<br>" . $shopemail;

    $res = sendEmailMandrill($emailto, $subject, $message, $shopname, $shopemail, array('string' => $pdf, 'name' => 'Quote.pdf'));

    if (empty($res)) {
        echo 'Message could not be sent.';
    } else {
        recordAudit("EMail Sent", "Send Quote: An Email Update was sent to $emailto for Quote id: $quoteid");
        echo "success";
    }
} else {
    $pdf->Output($newfilename, 'I');
}