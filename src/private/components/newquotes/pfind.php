﻿<?php
require_once CONN;

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$sf = isset($_GET['sf']) ? filter_var($_GET['sf'], FILTER_SANITIZE_STRING) : "";
$quoteid = isset($_GET['quoteid']) ? filter_var($_GET['quoteid'], FILTER_SANITIZE_NUMBER_INT) : 0;
$sf_rep = str_replace(" ", "", str_replace("-", "", $sf));
$comid = isset($_GET['comid']) ? filter_var($_GET['comid'], FILTER_SANITIZE_NUMBER_INT) : 0;
if(in_array($shopid, array('13445','22957'))) $shopid = '22865';
?>
<table class="table table-condensed table-striped table-header-bg">
    <thead>
    <tr class="style2">
        <td style="width: 20%; height: 30;" class="style1">Part #</td>
        <td width="4%" align="center" class="style1" style="height: 30">Qty</td>
        <td width="38%" class="style1" style="height: 30">Description</td>
        <td style="width: 28%; height: 30;" class="style1">Supplier</td>
        <td width="10%" align="right" class="style1" style="height: 30">Price</td>
    </tr>
    </thead>
    <?php
    $stmt = sprintf("select PartNumber, PartDesc, PartPrice, PartSupplier, netonhand, partid from partsinventory where shopid = '%s' and (partnumber Like '%s' or partdesc like '%s' or replace(replace(partnumber,'-',''),' ','') like '%s') LIMIT 100", $shopid, "%" . $sf . "%","%".$sf."%", "%" . $sf_rep . "%");
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $results = $query->get_result();
        while ($rs = $results->fetch_assoc()) {
            ?>
            <tr onclick="location.href='partdetail.php?pid=<?php echo $rs["partid"]; ?>&type=inv&quoteid=<?php echo $quoteid; ?>&comid=<?= $comid?>'">
                <td style="width: 20%">INV: <?php echo $rs["PartNumber"]; ?></td>
                <td width="4%" align="center"><?php echo $rs["netonhand"]; ?>
                </td>
                <td width="38%">
                    <p align="left"><?php echo $rs["PartDesc"]; ?></td>
                <td style="width: 28%"><?php echo $rs["PartSupplier"]; ?>
                </td>
                <td width="10%" align="right"><?php echo asDollars($rs["PartPrice"]); ?></td>
            </tr>
            <?php
        }
        $query->close();
    } else {
        echo "pi Prepare failed: (" . $conn->errno . ") " . $conn->error;

    }
    $preg = "partsregistry";
    $stmt = "SELECT table_name FROM information_schema.tables WHERE table_schema = 'shopboss' AND table_name = 'partsregistry-" . $shopid . "' LIMIT 1";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $query->bind_result($tablename);
        $query->fetch();
        if ($query->num_rows > 0) {
            $preg = "`partsregistry-" . $shopid . "`";
        }
        $query->close();
    } else {
        echo "info schema Prepare failed: (" . $conn->errno . ") " . $conn->error;

    }

    $stmt = sprintf("Select PartNumber, PartDesc, PartPrice, PartSupplier, partid from %s where shopid = '%s' and (partnumber Like '%s' or partdesc like '%s' or replace(replace(partnumber,'-',''),' ','') like '%s') LIMIT 100", $preg, $shopid, "%" . $sf . "%","%".$sf."%", "%" . $sf_rep . "%");
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $results = $query->get_result();
        $query->store_result();
        if ($results->num_rows > 0) {
            while ($rs = $results->fetch_assoc()) {
                ?>
            <tr onclick="location.href='partdetail.php?pid=<?php echo $rs["partid"]; ?>&type=non&quoteid=<?php echo $quoteid ?>&comid=<?= $comid?>'">
                <td style="width: 20%">NON: <?php echo $rs["PartNumber"]; ?></td>
                <td width="4%" align="center">
                <?php
                $qstmt = "select NetOnHand from partsinventory where shopid = ? and PartNumber = ?";
                if ($piquery = $conn->prepare($qstmt)) {
                    $partnumber = $rs['PartNumber'];
                    $piquery->bind_param("ss", $shopid, $partnumber);
                    $piquery->execute();
                    $piquery->store_result();
                    $piquery->bind_result($NetOnHand);
                    $piquery->fetch();
                    if ($piquery->num_rows > 0) {
                        echo $NetOnHand;
                        $instock = "yes";
                    } else {
                        echo "0";
                        $instock = "no";
                    }
                    ?>
                    </td>
                    <td width="38%">
                        <p align="left"><?php echo $rs["PartDesc"]; ?></td>
                    <td style="width: 28%"><?php echo $rs["PartSupplier"]; ?>
                    </td>
                    <td width="10%" align="right"><?php echo asDollars($rs['PartPrice']); ?></td></tr>
                    <?php
                } else {
                    echo "pi Prepare failed: (" . $conn->errno . ") " . $conn->error;

                }
            }
        } 
        $query->close();
    } else {
        echo $preg . " Prepare failed: (" . $conn->errno . ") " . $conn->error;

    }
    ?>
</table>
