﻿<!DOCTYPE html>
<html>
<?php
require_once CONN;

$shopid = $oshopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$quoteid = isset($_GET['quoteid']) ? filter_var($_GET['quoteid'], FILTER_SANITIZE_NUMBER_INT) : 0;
$comid = isset($_GET['comid']) ? filter_var($_GET['comid'], FILTER_SANITIZE_NUMBER_INT) : 0;
if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';
?>
<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus">
<!--<![endif]-->

<head>
    <meta charset="utf-8">
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
    <link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.css">
    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <style>
        .col-md-6 {
            border: 1px black solid
        }

        .col-md-8 {
            border: 1px black solid
        }

        .col-md-4 {
            border: 1px black solid
        }
    </style>
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
    
    <div id="page-container">

        <!-- Main Container -->
        <main class="container-fluid" id="main-container" style="display:block;">
            <button class="btn btn-warning btn-lg" type="button" onclick="addNewPart()" style="float:right">Add New Part</button>
            <br>
            <form style="padding:0px;margin:0px" class="form-horizontal push-10-t" onsubmit="return false;">
                <div style="padding:0px;margin:0px" class="form-group">
                    <div style="padding:0px;margin:0px" class="col-sm-12">
                        <div style="padding:0px;margin:0px" class="form-material">
                            <input class="form-control" style="border:1px silver solid;border-radius:3px;padding-left:10px;text-transform:uppercase" type="text" placeholder="Search by Part Number or Description" id="srch" name="srch">
                        </div>
                    </div>
                </div>
            </form>

            <div id="results">
                <table class="table table-condensed table-striped table-header-bg">
                    <thead>
                        <tr>
                            <td style="width: 20%; height: 30;" class="style1">Part #</td>
                            <td width="4%" align="center" class="style1" style="height: 30">Qty</td>
                            <td width="38%" class="style1" style="height: 30">Description</td>
                            <td style="width: 28%; height: 30;" class="style1">Supplier</td>
                            <td width="10%" align="right" class="style1" style="height: 30">Price</td>
                        </tr>
                    </thead>
                    <?php
                    $stmt = "select ignorespaceslashdashpartsearch from company where shopid = ?";
                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("s", $shopid);
                        $query->execute();
                        $query->store_result();
                        $query->bind_result($ig);
                        $query->fetch();
                        $query->close();
                    } else {
                        echo "company prts prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }
                    $preg = "partsregistry";
                    $stmt = sprintf("SELECT table_name FROM information_schema.tables WHERE table_schema = 'shopboss' AND table_name = 'partsregistry-%s' LIMIT 1", $oshopid);
                    if ($query = $conn->prepare($stmt)) {
                        $query->execute();
                        $query->store_result();
                        $query->bind_result($tablename);
                        $query->fetch();
                        if ($query->num_rows > 0) {
                            $preg = "`partsregistry-" . $oshopid . "`";
                        }
                        $query->close();
                    } else {
                        echo "Info schema prts prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    $stmt = "Select PartNumber, PartDesc, PartPrice, PartSupplier, partid from $preg where shopid = ? limit 100";

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("s", $oshopid);
                        $query->execute();
                        $results = $query->get_result();
                        $query->store_result();
                        if ($results->num_rows > 0) {
                            while ($rs = $results->fetch_assoc()) {
                    ?>
                                <tr onclick="location.href='partdetail.php?pid=<?php echo $rs["partid"]; ?>&type=non&quoteid=<?php echo $quoteid; ?>&comid=<?= $comid?>'">
                                    <td style="width: 20%; height: 39px;"><?php echo $rs["PartNumber"]; ?></td>
                                    <td width="4%" align="center" style="height: 39px">
                                        <?php
                                        $qstmt = "select NetOnHand from partsinventory where shopid = ? and PartNumber = ?";
                                        if ($qquery = $conn->prepare($qstmt)) {
                                            $qquery->bind_param("ss", $oshopid, $rs['PartNumber']);
                                            $qquery->execute();
                                            $qquery->store_result();
                                            $qquery->bind_result($NetOnHand);
                                            $qquery->fetch();
                                            if ($qquery->num_rows > 0) {
                                                echo $NetOnHand;
                                            } else {
                                                echo "0";
                                            }
                                            $qquery->close();
                                        } else {
                                            echo "pi prepare failed: (" . $conn->errno . ") " . $conn->error;
                                        }
                                        ?>
                                    </td>
                                    <td width="38%" style="height: 39px">
                                        <p align="left"><?php echo $rs["PartDesc"]; ?>
                                    </td>
                                    <td  style="width: 28%; height: 39px;"><?php echo $rs["PartSupplier"]; ?>
                                    </td>
                                    <td  width="10%" align="right" style="height: 39px"><?php echo asDollars($rs['PartPrice']); ?></td>
                                </tr>

                            <?php
                            }
                        } else {
                            ?>
                            <tr>
                                <td style="width: 20%">No Results</td>
                                <td width="4%" align="center"></td>
                                <td width="38%"></td>
                                <td style="width: 28%"></td>
                                <td width="10%" align="right"></td>
                                <td width="10%" align="right">&nbsp;</td>
                            </tr>
                    <?php
                        }
                        $query->close();
                    } else {
                        echo $preg . " prts prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    ?>
                </table>
            </div>
        </main>
        <!-- END Main Container -->

        <!-- Footer -->
        <!-- END Footer -->
    </div>
    <!-- END Page Container -->

    <script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
    <script src="<?= SCRIPT ?>/tipped.js"></script>

    <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
    <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
    <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
    <script src="<?= SCRIPT ?>/app.js"></script>
    <script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
    <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
    <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="<?= SCRIPT ?>/emodal.js?v=6"></script>
    <script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
    <script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>
    <script src="<?= SCRIPT ?>/plugins/underscorejs/underscore.js"></script>

    <!-- Page Plugins -->

    <!-- Page JS Code
        <script src="assets/js/pages/base_pages_dashboard.js"></script>-->
    <script>
        function addNewPart() {

            newpn = $("#srch").val();
            location.href = 'addnewpart.php?quoteid=<?php echo $quoteid; ?>&comid=<?= $comid?>&pn=' + newpn;

        }

        $("#srch").bind('keyup', _.debounce(function() {
            v = $(this).val();
            ds = "sf=" + v + "&shopid=<?php echo $shopid; ?>&quoteid=<?php echo $quoteid; ?>&comid=<?= $comid?>";
            $.ajax({
                data: ds,
                url: "pfind.php",
                success: function(r) {
                    $('#results').html(r)
                }
            })

        }, 300));

        $('#srch').focus()
    </script>
    <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>

</html>