<!DOCTYPE html>
<html>
<?php
require_once CONN;

$shopid = $oshopid = isset($_COOKIE['shopid']) ? filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING) : "";
$matrix = isset($_COOKIE['matrix']) ? filter_var($_COOKIE['matrix'], FILTER_SANITIZE_STRING) : "";
$pid = isset($_GET['pid']) ? filter_var($_GET['pid'], FILTER_SANITIZE_STRING) : "";
$quoteid = isset($_GET['quoteid']) ? filter_var($_GET['quoteid'], FILTER_SANITIZE_STRING) : 0;
$type = isset($_GET['type']) ? filter_var($_GET['type'], FILTER_SANITIZE_STRING) : "";
$comid = isset($_GET['comid']) ? filter_var($_GET['comid'], FILTER_SANITIZE_STRING) : "";
$quotepid = $_GET['quotepid']??'';
if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

$stmt = "select usepartsmatrix from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $query->bind_result($userpartsmatrix);
    $query->fetch();
    $query->close();
} else {
    echo "company prepare failed: (" . $conn->errno . ") " . $conn->error;
}
    

if(empty($quotepid))
{
    if($type=='non')
    {
        $stmt = sprintf("SELECT table_name FROM information_schema.tables WHERE table_schema = 'shopboss' AND table_name = 'partsregistry-%s' LIMIT 1", $oshopid);
        $preg = "partsregistry";
        if ($query = $conn->prepare($stmt)) {
            $query->execute();
            $query->store_result();
            $query->bind_result($table_name);
            $query->fetch();
            if ($query->num_rows > 0) {
                $preg = "`partsregistry-" . $oshopid . "`";
            }
            $query->close();
        } else {
            echo "info schema prepare failed: (" . $conn->errno . ") " . $conn->error;
        }
    }
    else
    $preg = "partsinventory";

    $strsql = sprintf("Select PartNumber,PartDesc,coalesce(PartPrice,0),PartCode,coalesce(PartCost,0),PartSupplier,OnHand,Allocatted,NetOnHand,ReOrderLevel,MaxOnHand,MaintainStock,OrderStatus,TransitStatus,PartCategory,bin,tax,overridematrix,partid From %s where shopid = ? and partid = ?", $preg);
    if ($query = $conn->prepare($strsql)) {
        $query->bind_param("ss", $oshopid, $pid);
        $query->execute();
        $query->store_result();
        $query->bind_result($PartNumber,$PartDesc,$PartPrice,$PartCode,$PartCost,$PartSupplier,$OnHand,$Allocatted,$NetOnHand,$ReOrderLevel,$MaxOnHand,$MaintainStock,$OrderStatus,$TransitStatus,$PartCategory,$bin,$tax,$overridematrix,$partid);
        $query->fetch();
        $query->close();
    } 

    $qty = 1;
    $discount = 0;
}

else
{
    $strsql = "Select partnumber,part,coalesce(price,0),partcode,coalesce(partcost,0),supplier,matrixcat,bin,taxable,overridematrix,qty,discount From quoteparts where shopid = ? and quoteid = ? and id = ?";
    if ($query = $conn->prepare($strsql)) {
        $query->bind_param("sii", $shopid, $quoteid, $quotepid);
        $query->execute();
        $query->store_result();
        $query->bind_result($PartNumber,$PartDesc,$PartPrice,$PartCode,$PartCost,$PartSupplier,$PartCategory,$bin,$tax,$overridematrix,$qty,$discount);
        $query->fetch();
        $query->close();
    } 
}

?>
<!--[if IE 9]>
<html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus">
<!--<![endif]-->

<head>
    <meta charset="utf-8">
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css?v=1.2">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
    <link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.css">
</head>

<body style="background-color:white">
    <form id="mainform">
        <input type="hidden" name="shopid" id="shopid" value="<?php echo $shopid;?>">
        <input type="hidden" name="quoteid" id="quoteid" value="<?php echo $quoteid;?>">
        <input type="hidden" name="comid" id="comid" value="<?php echo $comid;?>">
        <input type="hidden" name="discountamt" id="discountamt" value="0">
        <input type="hidden" name="quotepartid" id="quotepartid" value="<?= $quotepid?>">
        <main class="container" style="background-color:white">

            <div style="padding:20px;text-align:center" class="row"><h3>Part Details</h3></div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="col-sm-12">
                            <div class="form-material floating">
                                <select style="text-transform:uppercase" tabindex="1" class="form-control sbp-form-control" name="overridematrix" id="overridematrix">
                                <?php
                                $yselect = $nselect = "";
                                if (strtolower($overridematrix) == "yes") {
                                    $yselect = "selected";
                                }
                                if (strtolower($overridematrix) == "no") {
                                    $nselect = "selected";
                                }
                                ?>
                                <option <?php echo $nselect; ?> value="no">No</option>
                                <option <?php echo $yselect; ?> value="yes">Yes</option>
                            </select>
                                <label id="vinfloatinglabel" for="material-text2">Override Matrix Price</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <div class="form-material floating">
                                <input type="text" name="PartNumber" size="22" style="text-transform:uppercase" class="form-control sbp-form-control" value="<?php echo $PartNumber; ?>" tabindex="2">
                                <label id="vinfloatinglabel" for="material-text2">Part Number</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <div class="form-material floating">
                            <input type="text" name="PartDesc" size="56" style="text-transform:uppercase" class="form-control sbp-form-control" value="<?php echo htmlentities($PartDesc); ?>" tabindex="3">                             
                            <label id="vinfloatinglabel" for="material-text2">Part Description</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <div class="form-material floating">
                                <select onchange="calcPrices()" class="form-control sbp-form-control" tabindex="4" name="PartCategory" id="PartCategory">
                                    <?php
                                    $stmt = "select distinct category c from category where shopid = ? order by displayorder";
                                    $matrix = strtolower($_COOKIE['matrix']);
                                    if($query = $conn->prepare($stmt)){
                                        $query->bind_param("s",$shopid);
                                        $query->execute();
                                        $result = $query->get_result();
                                        $query->store_result();
                                        $numrows = $result->num_rows;
                                        if ($numrows > 0){
                                            while($row = $result->fetch_array()) {
                                                if (strtoupper($PartCategory) == strtoupper($row['c'])){
                                                    $s = "selected";
                                                }else{
                                                    $s = "";
                                                }
                                                echo "<option $s value='".strtoupper($row['c'])."'>".strtoupper($row['c'])."</option>";
                                            }
                                        }else{
                                            echo "<option value='none'>No Suppliers Entered</option>";
                                        }
                                    }
                                    ?>
                                </select>

                                <label id="vinfloatinglabel" for="PartCategory">Matrix Category</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" onblur="calcPrices()" tabindex="5" type="text" id="qty" name="qty" value="<?= $qty?>" name="qty">
                                <label id="discountfloatinglabel" for="qty">Quantity</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" onblur="calcPrices()" tabindex="6" type="text" id="PartCost" value="<?php echo number_format($PartCost,2,'.',''); ?>" name="PartCost">
                                <label id="discountfloatinglabel" for="PartCost">Shop Cost</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" onblur="calcPrices()" tabindex="7" type="text" id="PartPrice" value="<?php echo number_format($PartPrice,2,'.',''); ?>" name="PartPrice">
                                <label id="discountfloatinglabel" for="PartPrice">Selling Price</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" onblur="calcPrices()" onkeyup="calcPrices()" tabindex="8" type="text" id="Discount" value="<?= $discount?>" name="Discount">
                                <label id="dfloatinglabel" for="Discount">Discount %</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" onblur="calcPrices()" tabindex="9" type="text" id="net" name="net">
                                <label id="netfloatinglabel" for="net">Net Selling Price</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <div class="col-sm-12">
                            <div class="form-material floating">
                                <select onkeyup="" class="form-control sbp-form-control" tabindex="10" name="PartCode" id="PartCode">
                                    <option selected value="<?php echo strtoupper($PartCode); ?>"><?php echo strtoupper($PartCode); ?></option>
                                    <?php
                                    $stmt = "select codes from codes where shopid = ? and codes != ? order by codes asc";
                                    if($query = $conn->prepare($stmt)){
                                        $query->bind_param("ss",$shopid,$PartCode);
                                        $query->execute();
                                        $result = $query->get_result();
                                        $query->store_result();
                                        $numrows = $result->num_rows;
                                        if ($numrows > 0){
                                            while($row = $result->fetch_array()) {
                                                echo '<option value="'.strtoupper($row['codes']).'">'.strtoupper($row['codes']).'</option>';
                                            }
                                        }else{
                                            echo '<option value="New">New</option>';
                                        }
                                    }
                                    ?>
                                </select>
                                <label id="discountfloatinglabel" for="PartCode">Part Code</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <div class="form-material floating">
                                <select onkeyup="" class="form-control sbp-form-control" tabindex="11" name="PartSupplier" id="PartSupplier">
                                    <option selected value="<?php echo strtoupper($PartSupplier); ?>"><?php echo strtoupper($PartSupplier); ?></option>
                                    <?php
                                    $supplier = "";
                                    $stmt = "select suppliername s from supplier where suppliername != ? and shopid = ? and active = 'YES' order by suppliername";
                                    printf ( str_replace('?',"'%s'",$stmt),$PartSupplier,$shopid);
                                    $matrix = strtolower($_COOKIE['matrix']);
                                    if($query = $conn->prepare($stmt)){
                                        $query->bind_param("ss",$PartSupplier,$shopid);
                                        $query->execute();
                                        $result = $query->get_result();
                                        $query->store_result();
                                        $numrows = $result->num_rows;
                                        if ($numrows > 0){
                                            while($row = $result->fetch_array()) {
                                                if (strtoupper($PartSupplier) == strtoupper($row['s'])){
                                                    $s = "selected";
                                                }else{
                                                    $s = "";
                                                }
                                                echo "<option $s value=\"".strtoupper($row['s'])."\">".strtoupper($row['s'])."</option>";
                                            }
                                        }else{
                                            echo "<option value='none'>No Suppliers Entered</option>";
                                        }
                                    }
                                    ?>
                                </select>

                                <label id="discountfloatinglabel" for="PartSupplier">Supplier</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" tabindex="12" type="text" id="bin" name="bin" value="<?php echo $bin; ?>">
                                <label id="discountfloatinglabel" for="bin">Bin</label>
                            </div>
                        </div>
                    </div>
                     <div class="form-group">
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control" tabindex="13" type="text" id="extprice" name="extprice">
                                    <label id="extpricefloatinglabel" for="extprice">Total Price</label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control" tabindex="14" type="text" id="extcost" name="extcost">
                                    <label id="extcostfloatinglabel" for="extcost">Total Cost</label>
                                </div>
                            </div>
                        </div>

                        <div id="g6" class="form-group">
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <select onkeyup="" class="form-control sbp-form-control" tabindex="15" name="tax" id="tax">
                                        <?php
                                            if (strtolower($tax) == "yes"){
                                                $ys = ' selected="selected" ';
                                                $ns = '';
                                            }else{
                                                $ys = '';
                                                $ns = ' selected="selected" ';
                                            }
                                        ?>
                                        <option <?php echo $ys; ?> value="yes">Yes</option>
                                        <option <?php echo $ns; ?> value="no">No</option>
                                    </select>
                                    <label id="totalfloatinglabel" for="tax">Taxable</label>
                                </div>
                            </div>
                        </div>


                        <div class="form-group">
                            <div class="col-sm-12">
                        <br>
                        <br>

                        <?php if(!empty($quotepid)){?>
                        <input value="Save" onclick="calcPrices();submitForm('savepart','no')" class="btn btn-primary btn-md" type="button" name="Abutton5">
                        <?php }else{?>
                        <input value="Add Part" onclick="calcPrices();submitForm('addpart','no')" class="btn btn-primary btn-md" type="button" name="Abutton5">
                        <input value="Save & Add Another" onclick="calcPrices();submitForm('addpart','yes')" class="btn btn-warning btn-md" type="button" name="Abutton5">
                        <input type="button" value="Search Again" class="btn btn-default btn-md" onclick='location.href="addpart.php?&quoteid=<?php echo $quoteid; ?>&comid=<?= $comid?>"' name="Abutton1">
                        <?php }?>
                          </div>
                        </div>

                </div>
            </div>
        </main>


        <!-- END Main Container -->

        <!-- Footer -->
        <!-- END Footer -->
    </div>
    <!-- END Page Container -->

    <script src="https://code.jquery.com/jquery-1.11.0.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>
    <script src="<?= SCRIPT ?>/tipped.js"></script>

    <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
    <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
    <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
    <script src="<?= SCRIPT ?>/app.js"></script>
    <script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
    <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
    <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="<?= SCRIPT ?>/emodal.js?v=6"></script>
    <script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
    <script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?= SCRIPT ?>/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/1.5.16/clipboard.min.js"></script>

    <!-- Page Plugins -->

    <!-- Page JS Code-->
    <script>
       <?php if (strtolower($userpartsmatrix) == "yes"){ ?>
            function calcPrices(){

                <?php

                $stmt = "select category,factor,start,end from category where shopid = ? order by Category, Start";

                if($query = $conn->prepare($stmt)){
                    $query->bind_param("s",$shopid);
                    $query->execute();
                    $result = $query->get_result();
                    $rs = array();
                    while($row = $result->fetch_assoc()) {
                        $rs[] = $row;
                    }
                    echo "var clist = ".json_encode($rs)."\r\n";
                }
                ?>
                if ($('#overridematrix').val() == "yes"){
                    if (parseFloat($('#Discount').val()) > 0){

                                disc = parseFloat($('#Discount').val()) / 100
                                qty = $('#qty').val()
                                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                                extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val()))*disc).toFixed(2)
                                baseprice = $('#PartPrice').val()
                                discamt = baseprice * disc
                                netprice = ($('#PartPrice').val()-discamt)
                                $('#net').val(netprice.toFixed(2))
                                $('#extcost').val(extcost)
                                $('#discountamt').val(discamt.toFixed(2))
                                $('#extprice').val((netprice * qty).toFixed(2))
                                $('#netfloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                                $('#dfloatinglabel').html("Discount % "+"<span id='discpercentlabel' style='color:red;margin-left:20px;'>(In Dollars $"+discamt.toFixed(2)+" ea.)</span>")
                                $('#extcostfloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                                $('#extpricefloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                            }else{
                                $('#discountamt').val('0')
                                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                                extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                                $('#net').val($('#PartPrice').val())
                                $('#netfloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                                $('#extcost').val(extcost)
                                $('#extprice').val(extprice)
                                $('#extcostfloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                                $('#extpricefloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                            }
                }else{
                    // matrix calculations
                    //console.log("calculating")
                    srch = $('#PartCategory').val().toLowerCase()
                    console.log("cat:"+srch)
                    amt = $('#PartCost').val()
                    $.each(clist, function(i, v){
                        //console.log(JSON.stringify(i)+":"+JSON.stringify(v))
                        if (v.category.toUpperCase() === srch.toUpperCase() && v.start <= amt && v.end >= amt){
                            //console.log(JSON.stringify(i)+":"+JSON.stringify(v))
                            $('#PartPrice').val( Math.round((amt * v.factor)*100)/100 )
                        }
                        if (parseFloat($('#qty').val()) > 0 && parseFloat($('#PartPrice').val()) >= 0 && parseFloat($('#PartCost').val()) >= 0){
                            if (parseFloat($('#Discount').val()) > 0){

                                disc = parseFloat($('#Discount').val()) / 100
                                qty = $('#qty').val()
                                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                                extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val()))*disc).toFixed(2)
                                baseprice = $('#PartPrice').val()
                                discamt = baseprice * disc
                                netprice = ($('#PartPrice').val()-discamt)
                                $('#net').val(netprice.toFixed(2))
                                $('#extcost').val(extcost)
                                $('#discountamt').val(discamt.toFixed(2))
                                $('#extprice').val((netprice * qty).toFixed(2))
                                $('#netfloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                                $('#dfloatinglabel').html("Discount % "+"<span id='discpercentlabel' style='color:red;margin-left:20px;'>(In Dollars $"+discamt.toFixed(2)+" ea.)</span>")
                                $('#extcostfloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                                $('#extpricefloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                            }else{
                                $('#discountamt').val('0')
                                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                                extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                                $('#net').val($('#PartPrice').val())
                                $('#netfloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                                $('#extcost').val(extcost)
                                $('#extprice').val(extprice)
                                $('#extcostfloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                                $('#extpricefloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                            }
                        }

                    });
                }
            }
            <?php
            }else{
            ?>
            function calcPrices(){
                if (parseFloat($('#qty').val()) > 0 && parseFloat($('#PartPrice').val()) >= 0 && parseFloat($('#PartCost').val()) >= 0){
                    if (parseFloat($('#Discount').val()) > 0){

                        disc = parseFloat($('#Discount').val()) / 100
                        qty = $('#qty').val()
                        extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                        extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val()))*disc).toFixed(2)
                        baseprice = $('#PartPrice').val()
                        discamt = baseprice * disc
                        netprice = ($('#PartPrice').val()-discamt)
                        $('#net').val(netprice.toFixed(2))
                        $('#extcost').val(extcost)
                        $('#discountamt').val(discamt.toFixed(2))
                        $('#extprice').val((netprice * qty).toFixed(2))
                        $('#netfloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                        $('#dfloatinglabel').html("Discount % "+"<span id='discpercentlabel' style='color:red;margin-left:20px;'>(In Dollars $"+discamt.toFixed(2)+" ea.)</span>")
                        $('#extcostfloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                        $('#extpricefloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                    }else{
                        $('#discountamt').val('0')
                        extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                        extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                        $('#net').val($('#PartPrice').val())
                        $('#netfloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                        $('#extcost').val(extcost)
                        $('#extprice').val(extprice)
                        $('#extcostfloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                        $('#extpricefloatinglabel').css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")
                    }
                }

            }
            <?php
            }
            ?>

    function submitForm(t,addmore) {

        $('.btn-md').attr('disabled','disabled')
        
        ds = $('#mainform').serialize()+"&t="+t+"&addmore="+addmore

        $.ajax({
            data: ds,
            type: "post",
            url:  "quoteactions.php",
            success: function(r){
                if(r !== 'success'){
                    swal({
                        title: "Error Saving Part",
                        text: r,
                        type: "warning",
                        confirmButtonClass: "btn-danger",
                        confirmButtonText: "Ok",
                        closeOnConfirm: true
                    },  function(){
                        parent.location.href="quote.php?quoteid=<?= $quoteid?>&recalc=y"
                    })
                } else {
                    if(addmore=='no')
                    parent.location.href="quote.php?quoteid=<?= $quoteid?>&recalc=y"
                    else
                    location.href="addpart.php?&quoteid=<?php echo $quoteid; ?>&comid=<?= $comid?>"
                }
            }
        });
    }

    $(document).ready(function(){
    $('#qty').focus().select()
    calcPrices()

    });
    </script>
    <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>

</html>