<?php
ini_set('max_execution_time', '300'); //300 seconds = 5 minutes

require CONN;
$shopid = $_POST['shopid'];
$laborids = $laborhours = array();
// now use the ROID list to see what labortimeclock punches exceed hours sold
$stmt = "select l.laborid,l.laborhours from labor l,repairorders r where l.shopid=r.shopid and l.roid=r.roid and l.laborhours > 0 and r.shopid = '$shopid' and r.status != 'CLOSED' and r.ROType != 'No Approval'";

if ($query = $conn->prepare($stmt)){
	$query->execute();
	$result = $query->get_result();
	while ($row = $result->fetch_array()){
		$laborids[] = $row['laborid'];
		$laborhours[$row['laborid']] = $row['laborhours'];
	}
}

$rolist = "";

if(!empty($laborids))
{
	$stmt = "select sum(TIMESTAMPDIFF(minute,startdatetime,enddatetime))/60 as timediff, roid,laborid from labortimeclock where '$shopid' and laborid in (".implode(',', $laborids).") and not isnull(enddatetime) group by laborid";

	if ($query = $conn->prepare($stmt)){
		$query->execute();
		$result = $query->get_result();
	    while ($row = $result->fetch_array()){
	    	$timediff = $row['timediff'] - $laborhours[$row['laborid']];
	        if ($timediff > 0.1)
			$rolist .= $row['roid'].",";
	    }
	}
	
	
	// now look for open time clocks that as of the current local date time are over the sold hours
	$currenddatetime = localTimeStamp($shopid);

	$stmt = "select sum(TIMESTAMPDIFF(minute,startdatetime,'$currenddatetime'))/60 as timediff, roid,laborid from labortimeclock where '$shopid' and laborid in (".implode(',', $laborids).") and isnull(enddatetime) group by laborid";

	if ($query = $conn->prepare($stmt)){
		$query->execute();
		$result = $query->get_result();
	    while ($row = $result->fetch_array()){
	    	$timediff = $row['timediff'] - $laborhours[$row['laborid']];
	        if ($timediff > 0.1)
			$rolist .= $row['roid'].",";
	    }
	}
}

$roarray = explode(",",substr($rolist,0,strlen($rolist)-1));
$result = array_unique($roarray);
$final = "";
foreach ($result as $v){
	$final .= $v.",";
}
echo substr($final,0,strlen($final)-1);


mysqli_close($conn);
?>
