<?php
include_once("rules.wip.php");
if($component == 'wip-v2'){?>
<li>
    <!-- Icons -->
    <ul class="navbar-nav flex-row mt-1 pe-2 w-100">
        <li class="d-flex align-items-center justify-content-around ms-4">
            <?php if (isset($showstatsonwip) && $showstatsonwip == 'YES' && $_COOKIE['mode']=='full') { ?>
            <li class="nav-item col-2 d-flex justify-content-center">
                <button type="button" class="border-0 bg-transparent" data-mdb-toggle="collapse" data-mdb-target="#shopStats-collapseOneX" aria-expanded="true" aria-controls="shopStats-collapseOneX">
                    <a class="nav-link" href="javascript:void(null)" data-mdb-toggle="tooltip" data-mdb-placement="bottom" title="Shop Stats">
                        <i class="fas fa-line-chart fa-lg"></i></a>
                </button>
            </li>
            <?php } ?>
            <?php if($_COOKIE['mode']=='full'){?>
            <li class="nav-item col-2 d-flex justify-content-center">
                <a class="nav-link" href="javascript:void(null)" onclick="$('#confirmtechmodal').modal('show')" data-mdb-toggle="tooltip" data-mdb-placement="bottom" title="Tech Mode"><i class="fas fa-tachometer-alt fa-lg"></i></a>
            </li>
            <?php }?>
            <li class="nav-item col-2 d-flex justify-content-center">
                <a class="nav-link" href="javascript:void(null)" onclick="showIM()" data-mdb-toggle="tooltip" data-mdb-placement="bottom" title="Messages"><i class="fas fa-comment fa-lg"></i><span class="badge rounded-pill badge-notification bg-danger" id="message_badge"></span></a>
            </li>
            <li class="nav-item col-2 d-flex justify-content-center">
                <a class="nav-link" id="reminderbutton" href="javascript:void(null)" onclick="changeIconColor()" data-mdb-toggle="tooltip" data-mdb-placement="bottom" title="Reminders"><i class="fas fa-calendar-alt fa-lg"></i></a>
            </li>
            <?php if($_COOKIE['mode']=='full'){?>
            <li class="nav-item col-2 d-flex justify-content-center">
                <a class="nav-link" href="javascript:void(null)" onclick="get_notifications('panel')" data-mdb-toggle="tooltip" data-mdb-placement="bottom" title="Notifications"><i class="fas fa-bell fa-lg"></i><span class="badge rounded-pill badge-notification bg-danger" id="notification_badge"></span></a>
            </li>
            <?php }?>
        </li>
    </ul>
</li>
<?php }?>

<form id="loginform" method="post" action="<?= COMPONENTS_PUBLIC ?>/login/loginsettings.php">
    <input type="hidden" name="loginshopid" value="<?= $shopid?>">
    <input type="hidden" name="loginempid" value="<?= isset($_COOKIE['ownerid']) ? $_COOKIE['ownerid'] : '' ?>">
    <input type="hidden" name="ownerid" value="<?= isset($_COOKIE['ownerid']) ? $_COOKIE['ownerid'] : '' ?>">
    <input type="hidden" name="newui" value="yes">
</form>

<!-- Shop Stats -->
<div class="accordion accordion-borderless" id="shopStats">
    <div class="accordion-item">
        <div id="shopStats-collapseOneX" class="accordion-collapse collapse" aria-labelledby="shopStats-headingOneX" data-mdb-parent="#shopStats">
            <div class="accordion-body m-0 p-0">
                <table class="table table-borderless shop-stats ms-1 shadow-3 table-sm align-middle ms-lg-2">
                    <tbody id="shopstatsbody">
                        <tr>
                            <td colspan="2" class="text-center">
                                <h4>Shop Stats</h4>
                                <hr>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php if(isset($_COOKIE['ownerid']) && $_COOKIE['ownerid'] != $_COOKIE['empid']){?>
    <li class="sidenav-item sidenav-selected">
        <a class="sidenav-link ripple-surface-primary" href="javascript:void(null)" onclick="$('#loginform').submit()">
            <i class="fas fa-user fa-lg"></i><span class="ms-2 text-primary">Back as Owner</span>
        </a>
    </li>

<?php }?>
<li class="sidenav-item <?php echo $component == 'wip-v2' ? 'sidenav-selected' : ''; ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/wip/wip.php">
        <i class="fas fa-wrench fa-lg"></i><span class="ms-2">Work In Process</span>
    </a>
</li>

<?php if($_COOKIE['mode']=='full' && !$onboardingCompleted){?>
    <li class="sidenav-item <?= $onboardingClass; ?> <?= $component == 'onboarding-wizard-v2' ? 'sidenav-selected' : ''; ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/onboarding-wizard/onboarding-wizard.php">
            <i class="fas fa-user-cog"></i><span class="ms-2">Onboarding Wizard</span>
        </a>
    </li>
<?php } ?>

<?php
if ($readonly == 'no' && $_COOKIE['createro'] == 'yes' && $_COOKIE['mode'] == 'full') {
    ?>

    <li class="sidenav-item">
        <a class="sidenav-link ripple-surface-primary" href="javascript:void(null)"><i class="far fa-plus-square fa-lg"></i><span class="ms-2">Create RO</span></a>
        <ul class="sidenav-collapse <?php echo ($component == 'customers-v2') ? 'show' : ''; ?>">
            <li class="sidenav-item <?php echo ($component == 'customers-v2') ? 'sidenav-selected' : ''; ?>">
                <a class="sidenav-link" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/customer/customer-search.php"><i class="fa fa-xxs fa-circle"></i> Search</a>
            </li>
            <li class="sidenav-item">
                <a class="sidenav-link" onclick="launchBossVin()" href="javascript:void(null)"><i class="fa fa-xxs fa-circle"></i> BOSS Vin</a>
            </li>
        </ul>
    </li>
<?php
}
?>

<?php if($_COOKIE['mode']=='full'){?>
<li class="sidenav-item">
    <a class="sidenav-link ripple-surface-primary" href="https://<?php echo ROOT; ?>/inspections/dashboard.php" target="_blank"><i class="fas fa-stethoscope fa-lg"></i><span class="ms-2">BOSS Inspect</span></a>
</li>
<?php
}

if ($dashboardaccess == 'YES' && $_COOKIE['mode'] == 'full') {
    ?>
    <li class="sidenav-item">
        <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/dashboard/dashboard.php" target="_blank"><i class="fas fa-tachometer fa-lg"></i><span class="ms-2">BOSS Board</span></a>
    </li>
<?php
}
?>

<?php if($_COOKIE['mode']=='full'){?>
<li class="sidenav-item <?php echo $component == 'customer-v2' ? 'sidenav-selected' : ''; ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/customer/customer.php"><i class="fas fa-user-friends fa-lg"></i><span class="ms-2">Customers</span></a>
</li>
<?php }

if (strtolower($shopid) == 'matco' && $_COOKIE['mode'] == 'full') { ?>
    <li class="sidenav-item">
        <a class="sidenav-link ripple-surface-primary" class="nav-submenu" data-toggle="nav-submenu" href="javascript:void(null)"><i class="fas fa-barcode fa-lg"></i><span class="ms-2">Matco Scan Tool</span></a>
        <ul class="sidenav-collapse">
            <li class="sidenav-item">
                <a class="sidenav-link" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/scan_tool_dashboard/index.php"><i class="fa fa-xxs fa-circle"></i> Scan Tool Dashboard</a>
            </li>
            <li class="sidenav-item">
                <a class="sidenav-link" href="https://www.matcotools.com/catalog/product/mdmax5cl/maximus-5-0-diagnostic-scan-tool-with-passenger-carline-software" target="_blank"><i class="fa fa-xxs fa-circle"></i> Scan Tool Demo</a>
            </li>
            <li class="sidenav-item">
                <a class="sidenav-link" href="javascript:void(null)" onclick="$('#matcoAccModal').modal('show')"><i class="fa fa-xxs fa-circle"></i> Accessories</a>
            </li>
        </ul>
    </li>
<?php } elseif ($matco == 'yes' && $_COOKIE['mode'] == 'full') { ?>
    <li class="sidenav-item <?php echo $component == 'matcodash-v2' ? 'sidenav-selected' : ''; ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/scan_tool_dashboard/index.php"><i class="fas fa-barcode fa-lg"></i><span class="ms-2">Matco Scan Tool</span></a>
    </li>
<?php } ?>


<li class="sidenav-item">
    <a class="sidenav-link ripple-surface-primary" href="javascript:void(null)"><i class="far fa-calendar-check fa-lg"></i><span class="ms-2">Schedule</span></a>
    <ul class="sidenav-collapse <?php echo ($component == 'calendar-v2' || $component == 'empschedule-v2') ? 'show' : ''; ?>">
        <?php if($viewschedule == 'yes'){?>
        <li class="sidenav-item <?php echo ($component == 'calendar-v2') ? 'sidenav-selected' : ''; ?>">
            <a class="sidenav-link" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/calendar/calendar.php"><i class="fa fa-xxs fa-circle"></i>Customer</a>
        </li>
        <?php }?>
        <li class="sidenav-item <?php echo ($component == 'empschedule-v2') ? 'sidenav-selected' : ''; ?>">
            <a class="sidenav-link" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/empschedule/calendar.php"><i class="fa fa-xxs fa-circle"></i>Employee</a>
        </li>
    </ul>
</li>

<?php if($_COOKIE['mode']=='full'){?>
<li class="sidenav-item <?php echo $component == 'quotes-v2' ? 'sidenav-selected' : ''; ?>">
    <a class="sidenav-link ripple-surface-primary" href="https://<?php echo ROOT; ?>/v2/quotes/quotes.php"><i class="fas fa-dollar-sign fa-lg"></i><span class="ms-2">Quotes</span></a>
</li>
<?php }?>

<li class="sidenav-item <?php echo $component == 'dispatch-v2' ? 'sidenav-selected' : ''; ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/dispatch/dispatch.php"><i class="fas fa-share-square fa-lg"></i><span class="ms-2">Dispatch</span></a>
</li>

<?php if($_COOKIE['mode']=='full'){?>
<li class="sidenav-item <?php echo $component == 'findro-v2' ? 'sidenav-selected' : ''; ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/findro/findro.php"><i class="fas fa-search-dollar fa-lg"></i><span class="ms-2">Find RO</span></a>
</li>
<li class="sidenav-item <?php echo $component == 'history-v2' ? 'sidenav-selected' : ''; ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/history/history.php"><i class="fas fa-history fa-lg"></i><span class="ms-2">History</span></a>
</li>
<li class="sidenav-item <?php echo $component == 'cores-v2' ? 'sidenav-selected' : ''; ?>">
    <a class="sidenav-link ripple-surface-primary cores" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/cores/cores.php"><i class="fas fa-exclamation-triangle fa-lg"></i><span class="ms-2 cores">Cores</span></a>
</li>
<li class="sidenav-item <?php echo $component == 'follow-v2' ? 'sidenav-selected' : ''; ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/follow/campaigns.php"><i class="fas fa-user fa-lg"></i><span class="ms-2">Follow Up</span></a>
</li>
<li class="sidenav-item <?php echo $component == 'po-v2' ? 'sidenav-selected' : ''; ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/po/po.php"><i class="fas fa-download fa-lg"></i><span class="ms-2">Receive PO</span></a>
</li>

<?php if ($EditInventory == 'YES') { ?>
    <li class="sidenav-item <?php echo $component == 'inventory-v2' ? 'sidenav-selected' : ''; ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/inventory/inventory.php"><i class="fas fa-box-open fa-lg"></i><span class="ms-2">Inventory</span></a>
    </li>
<?php }
if ($reportsaccess == 'YES') { ?>
    <li class="sidenav-item <?php echo $component == 'reports-v2' ? 'sidenav-selected' : ''; ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/reports/reports.php"><i class="fas fa-file-contract fa-lg"></i><span class="ms-2">Reports</span></a>
    </li>
<?php }
if ($accountingaccess == 'YES') { ?>
    <li class="sidenav-item <?php echo $component == 'accounting-v2' ? 'sidenav-selected' : ''; ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/accounting/accounting.php"><i class="fas fa-calculator fa-lg"></i><span class="ms-2">Accounting</span></a>
    </li>
<?php } ?>
<li class="sidenav-item">
    <a class="sidenav-link ripple-surface-primary" href="<?php echo INTEGRATIONS; ?>/happyfox/jwtlogin.php" target="_blank"><i class="fas fa-question fa-lg"></i><span class="ms-2">Support</span></a>
</li>
<li class="sidenav-item <?php echo $curr_page == 'videos.php' ? 'sidenav-selected' : ''; ?>">
    <!-- adding pages path -->
    <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/videos/videos.php"><i class="far fa-play-circle fa-lg"></i><span class="ms-2">Training Videos</span></a>
</li>

<?php if ($sbpackage != 'Trial') { ?>
    <li class="sidenav-item">
        <a class="sidenav-link ripple-surface-primary" href="<?php echo $cannyurl; ?>" target="_blank"><i class="far fa-comments fa-lg"></i><span class="ms-2">Feedback</span></a>
    </li>
<?php
}
if ($settingsaccess == 'YES') { ?>
    <li class="sidenav-item <?php echo $component == 'settings-v2' ? 'sidenav-selected' : ''; ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?php echo COMPONENTS_PRIVATE; ?>/v2/settings/settings.php"><i class="fas fa-cog fa-lg"></i><span class="ms-2">Settings</span></a>
    </li>
<?php } ?>

<?php
if ($rocount <= 10) {
    ?>
    <li class="sidenav-item">
        <a class="sidenav-link border-dashed-primary" href="javascript:void(null)" onclick="openQuickStartVideo(this)">Quick Start Video</a>
    </li>
<?php
}
?>

<?php
if (($shopid <= 5623 && $shopid != 'demo' && $shopid != '1734') || $shopid == '5974' || $shopid == '6517' || $shopid == '6942' || $shopid == '14083') {
    if (strtolower($masterinterface) == 'classic') {
        ?>
        <li class="sidenav-item text-center">
            <a class="btn btn-primary " href="<?php echo SBP; ?>wip.asp?interface=1&override=yes">Classic Shop Boss</a>
        </li>
<?php
    }
}

?>

<li class="sidenav-item text-center mt-4" id="cannyversion">
</li>

<?php }?>

<?php
if ($techreport == "yes") {
    ?>
    <li class="sidenav-item">
        <a class="sidenav-link ripple-surface-primary" href="/v2/wip/dateprompt.php"><i class="fas fa-file fa-lg"></i><span class="ms-2">Tech Report</span></a>
    </li>
    <?php
}

if($_COOKIE['mode'] == 'tech2' && $isfullmode == 'YES')
{
?>
    <li class="sidenav-item">
        <a class="sidenav-link ripple-surface-primary" href="javascript:void(null)" onclick="openFullMode()"><i class="fas fa-arrow-left fa-lg"></i><span class="ms-2">Full Mode</span></a>
    </li>
<?php
}
?>
