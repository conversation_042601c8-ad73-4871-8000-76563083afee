<style>

    body{
        -ms-overflow-style: none;  /* Internet Explorer 10+ */
        scrollbar-width: none;  /* Firefox */
        height : auto !important;
    }
    body::-webkit-scrollbar {
        display: none;  /* Safari and Chrome */
    }  

    .toast
    {
        width: fit-content; 
        min-width: 200px;
        max-width: 50% !important;
    }

    #livetext_badge {
        margin-left: 3px;
    }

    .cursor-pointer {
        cursor: pointer;
    }

    .filter_box {
        font-size: 14.4px;
        width: 120px;
        border-color: var(--textColor) !important;
    }

    .filter_dropDownMenu {
        min-width: 30vw;
        max-width: 60vw;
        max-height: 60vh;
        overflow-y: scroll;
    }
    .filter_MADownMenu {
        min-width: 40vw;
        max-width: 60vw;
        max-height: 40vh;
        overflow-y: scroll;
    }
    .MA_link{
        color: var(--textColor) !important;
    }
    .fixed-below-nav {
        position: fixed;
        margin: 0;
        top: 50px; /* Set this to the height of the #nav-sb */
        z-index: 999; /* Ensure it's below the nav */
        border: 0;
        border-radius: 0;
        box-shadow: 0 2px 0px rgba(0, 0, 0, 0.2); /* Optional: adds a shadow effect */
        background-color: var(--navbar-bg);
    }
    .fixed-below-nav .alert-container{
        border: none;
        margin: 0;
    }

    .fixed-footer {
        position: sticky;
        bottom: 0;
        padding-top: 15px;
    }

    #immodal .custom-modal-footer{
        padding: 16px 4px;
        border-top: var(--mdb-modal-header-border-width) solid var(--textColor)!important
    }
</style>