    <div class="modal fade" id="quickStartModal" tabindex="-1" aria-labelledby="messagesLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title" id="matcoAccModalLabel">Quick Start</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <div class="embed-responsive embed-responsive-16by9">
                        <iframe id="youtubeFrame" width="560" height="315" src="https://www.youtube.com/embed/_fpZWTqjhSc" title="YouTube video player" frameborder="0" allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="matcoAccModal" tabindex="-1" aria-labelledby="messagesLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title" id="matcoAccModalLabel">Accessories</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img src="<?= IMAGE ?>/matco-acc.png" alt="Matco Accessories">
                </div>
            </div>
        </div>
    </div>


    <div class="modal fade" id="apptmodal" tabindex="-1" aria-labelledby="messagesLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title" id="apptmodalLabel">New Appointment Scheduled <i class="fa fa-info-circle" title="Click <i class='fas fa-eye fa-sm'></i> to View<br>Click <i class='fas fa-reply-all fa-sm'></i> to Respond<br>Click <i class='fas fa-trash fa-xs'></i> to Dismiss" data-mdb-html="true" data-mdb-toggle="tooltip"></i> </h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <table class="sbdatatable w-100">
                                <thead>
                                    <tr>
                                        <th>Date/Time</th>
                                        <th>First</th>
                                        <th>Last</th>
                                        <th>Year</th>
                                        <th>Make</th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody id="apptbody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-md btn-primary btn-sm" type="button" onclick="dismissAllAppt()">Mark Read</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="pmtmodal" tabindex="-1" aria-labelledby="messagesLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title" id="pmtmodalLabel">Remote Payments Received</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <h3>You have received these remote payments. Click Mark Read to mark all of them as read</h3>
                            <table class="table table-condensed table-striped table-header-bg">
                                <thead>
                                    <tr>
                                        <td>Date</td>
                                        <td>RO</td>
                                        <td>Customer</td>
                                        <td>Amount</td>
                                    </tr>
                                </thead>
                                <tbody id="pmtbody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-md btn-primary" type="button" onclick="dismissAllRemotePayments()">Mark Read</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="confirmapptmodal" tabindex="-1" aria-labelledby="messagesLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title" id="confirmapptmodalLabel">Confirm Appointment Scheduled</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="appointment_id">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" id="emailmessageaddress" name="emailmessageaddress" type="text" value="">
                                <label class="form-label" for="emailmessageaddress">Email Address</label>
                            </div>
                        
                            <div class="form-outline mb-4">
                                <input class="form-control" tabindex="1" id="emailmessagesubject" name="emailmessagesubject" type="text" value="Your scheduled appointment with <?php echo $_COOKIE['shopname']; ?>">
                                <label class="form-label" for="emailmessagesubject">Subject</label>
                            </div>

                            <div class="form-outline mb-4">
                                <textarea class="form-control" tabindex="1" id="emailmessagemessage" name="emailmessagemessage">Thanks for setting your appointment with us online. We are able to accommodate the time slot you requested and look forward to seeing you then.</textarea>
                                <label class="form-label" for="emailmessagemessage">Message</label>
                            </div>
                        </div>
                    </div>
                </div>
                    
                <div class="modal-footer">
                    <button class="btn btn-md btn-primary" type="button" onclick="sendEmailMessage()">Send Message</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="immodal" tabindex="-1" aria-labelledby="messagesLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title" id="messagesLabel">Instant Messages</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="imlist" style="max-height:500px;overflow-y:scroll" class="col-md-12 chat-container">
                        </div>
                    </div>
                </div>
                <div class="custom-modal-footer">
                    <div class="mb-2">
                        <select class="select" id="emps" multiple>
                            <?php
                            $stmt = "select employeefirst, employeelast from employees where shopid = ? and active = 'yes' and concat(employeefirst,' ',employeelast) != ?";
                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("ss", $shopid, $usr);
                                $query->execute();
                                $result = $query->get_result();
                                $select = "";
                                while ($row = $result->fetch_array()) {
                                    $select .= "<option value='" . $row['employeefirst'] . " " . $row['employeelast'] . "'>" . $row['employeefirst'] . " " . $row['employeelast'] . "</option>";
                                }
                                echo $select;
                                $query->close();
                            }
                            ?>
                        </select>
                        <label class="form-label select-label">Send To</label>
                    </div>
                    
                    <div class="form-outline mb-2">
                        <textarea class="form-control" id="msgboxsend" rows="3" ai-writing-tool></textarea>
                        <label class="form-label" for="msgboxsend">Message</label>
                    </div>

                    <div class="d-flex justify-content-center">
                        <button type="button" class="btn btn-primary text-nowrap wave-effect btn-md" onclick="sendMessage()">Send Message</button>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div id="remindermodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
               <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="exampleModalLabel">Reminders / To Do List</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="max-height:400px;overflow-y:auto">
                    <div id="reminderlist" class="col-md-12">
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="col d-flex justify-content-center align-items-center pt-2">
                        <button type="button" class="btn btn-primary wave-effect" data-mdb-toggle="modal" data-mdb-target="#addremindermodal">Add New Reminder</button>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div id="notificationmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="notificationsLabel">Notifications</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="max-height:400px;overflow-y:auto">
                    <div id="notificationlist">
                    </div>
                </div>
                <div class="modal-footer" style="display:none;">
                    <div class="col d-flex justify-content-center align-items-center pt-2">
                        <button type="button" class="btn btn-primary btn-dismiss-all"><a href="javascript:void(null)" class=" wave-effect">Dismiss All</a></button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="addremindermodal" tabindex="-1" aria-labelledby="reminders-addLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title" id="reminders-addLabel">Add New Reminder</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row pb-4 pt-3">
                        <div class="form-outline">
                            <textarea class="form-control" id="newreminder" rows="4"></textarea>
                            <label class="form-label" for="newreminder">Message (max 300 characters)</label>
                        </div>
                    </div>
                    <div class="row pb-4">
                        <div class="form-outline" data-mdb-inline="true" >
                            <input type="text" class="form-control dateTimePickerBs" id="newreminderdate" value=" " />
                            <label for="newreminderdate" class="form-label">Select Date and Time</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <div class="col d-flex justify-content-center align-items-center pt-2">
                        <button type="button" class="btn btn-primary wave-effect btn-md" onclick="saveReminder()">Save Reminder</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="alfredmodal" tabindex="-1" aria-labelledby="messagesLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title" id="messagesLabel">Alfred Help Commands</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="alfredcommandlist text-left" class="col-md-12">
                            <ul>
                                <li>"Create Repair Order" - start a new RO</li>
                                <li>"Find Repair Order [say the RO number or name to find]" - Filter WIP List</li>
                                <li>"Open Repair Order [say the RO number to open]" - Open a Repair Order</li>
                                <li>"Manage Customers" - go to customer list</li>
                                <li>"WIP (whip)" - go to WIP List</li>
                                <li>"Cancel Help" - Close this window</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>

    <div class="modal fade" id="changepassmodal" tabindex="-1" aria-labelledby="messagesLabel" aria-hidden="true" style="z-index:99999999999">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title" id="changepassmodalLabel">Change Password</h5>
                </div>
                <div class="modal-body">
                        <p>In an effort to enhance security and protect user data, ShopBoss is requiring all users to update their passwords.<br><br>Password should be minimum eight characters long, at least one letter, at least one number.<br><br><b>If you have any issues after changing your password, clear your browser cache and cookies.</b> <br><br><NAME_EMAIL> if you have any questions. </p>
                        <div class="row">
                            <strong>New Password</strong>
                            <input class="form-control" tabindex="1" id="newpass" autocomplete="off" name="newpass" type="password">
                        </div>
                        <div class="row">
                            <strong>Confirm New Password</strong>
                            <input class="form-control" tabindex="2" id="cnewpass" autocomplete="off" name="cnewpass" type="password">
                            <br>
                        </div>
                        <div class="row" id="changepassmsg">
                        </div>
                </div>
                <div class="modal-footer">
                    <a class="btn btn-secondary btn-sm pull-left" href="<?= COMPONENTS_PUBLIC ?>/login/logoff.php">Logoff</a>
                    <button class="btn btn-primary btn-sm" id="btn-change-pass" onclick="changepass()" type="button">Save</button>
                </div>
            </div>
        </div>
    </div>

    <div id="motorModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
     <div class="modal-dialog draggable-element modal-xl">
         <div class="modal-content">

             <div class="modal-header">
                 <h4 class="modal-title">Motor</h4>
                 <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
             </div>

             <div class="modal-body">
                 <iframe id="motor-iframe" class="embed-responsive-item" frameborder=0 src="" style="width:100%;height:75vh;display:block;"></iframe>
             </div>

         </div>
     </div>
    </div>

    <div id="preromodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Convert PreRO</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <iframe id="prero-iframe" class="embed-responsive-item" frameborder=0 src=""
                            style="width:100%;height:75vh;display:block;"></iframe>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-default btn-sm" type="button" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <div id="confirmtechmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog draggable-element modal-lg" data-mdb-drag-handle=".move_icon">
            <div class="modal-content p-4">
                <div class="modal-header ps-1 pe-1">
                    <h5 class="modal-title" id="confirmtechLabel">Tech Mode</h5>
                    <div class="text-right">
                        <span class="float-right" data-mdb-toggle="tooltip" title="Drag Modal"><i class="fas fa-arrows-up-down-left-right move_icon"></i></span>
                        <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12 text-center">
                            This will log you into Tech Mode.  Are you sure?
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-center equal-width-buttons pt-2">
                    <button class="btn btn-md btn-primary wave-effect" type="button" onclick="openTechMode('old')">Log into old Tech Mode</button>
                    <button class="btn btn-md btn-secondary wave-effect" type="button" onclick="openTechMode('new')">Log into Tech Mode V2</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Tech Activities -->
    <div class="toast fade mx-auto mt-5" id="tech-activities-toast" role="alert" aria-live="assertive" aria-atomic="true" data-mdb-autohide="false" data-mdb-delay="2000" data-mdb-position="top-right" data-mdb-append-to-body="true" data-mdb-stacking="true" data-mdb-width="350px">
        <div class="toast-header">
            <strong class="me-auto text-black">Today's Tech Activities</strong>
            <button type="button" class="btn-close" data-mdb-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body techresults">
            <div class="row">
              <div class="col" style="max-height: 200px;overflow-y: auto;">
                <?php
                $tastartdate = date("Y-m-d") . " 00:00:00";
                $stmt = "select startdate,message from alerts where shopid = '$shopid' and startdate >= '$tastartdate' order by startdate desc limit 200";
                if ($query = $conn->prepare($stmt)) {
                    $query->execute();
                    $result = $query->get_result();
                    $num_rows = $result->num_rows;
                    if($num_rows > 0)
                    {
                        echo("<ul>");
                        while ($row = $result->fetch_assoc()) {
                            $sd = strtotime($row['startdate']);
                            $sd = date("m/d/Y h:i:s A", $sd);
                            $msg = $row['message'];
                            $splocation = strrpos($msg, " ");
                            $splocation = strlen($msg) - $splocation;
                            $thisroid = trim(substr($msg, -$splocation));
                            $msg = str_replace("RO " . $thisroid, "<a style='font-weight:bold' href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?roid=" . $thisroid . "'>RO " . $thisroid . "</a>", $msg);
                            ?>
                            <li><?php echo $sd; ?> - <?php echo $msg; ?></li>
                            <?php
                        }
                        echo("</ul>");
                    }
                    else echo("<div class='text-center'>You are all caught up on Tech Activities</div>");
                }
                ?>
           </div>
          </div>
        </div>
    </div>  

   <?php if ($failedpayment == 'yes') { ?>
    <div class="toast show fade mt-0 pt-0 fixed-top top-0 mx-auto d-none d-md-block" role="alert" aria-live="assertive" aria-atomic="true" data-mdb-autohide="false" data-mdb-position="...">
        <div class="toast-header">
         <span class="me-auto text-primary">ACTION REQUIRED - The card on file for your subscription failed, please click the link to enter new payment information - <a href="<?= $paybalancelink ?>" target="_blank">Click here to update.</a></span>
        </div>
    </div>

    <?php } elseif ($showQuickVideoBanner) { ?>
        <div class="toast show fade mt-0 pt-0 fixed-top top-0 mx-auto head-alert d-none d-md-block" role="alert" aria-live="assertive" aria-atomic="true" data-mdb-autohide="false" data-mdb-position="...">
            <div class="toast-header">
             <span class="me-auto"><a href="javascript:void(null)" class="text-primary" data-embed="<?= $video_url ?>" onclick="openQuickStartVideo(this)" data-days="<?= $days_started ?>">Click to View Your Quick Start Video</a></span>
            </div>
        </div>
    <?php } elseif (!empty($capitalarr)) { ?>

    <div class="toast show fade mt-0 pt-0 fixed-top top-0 mx-auto head-alert d-none d-md-block" id="alert-360" role="alert" aria-live="assertive" aria-atomic="true" data-mdb-autohide="false" data-mdb-position="...">
        <div class="toast-header">
         <span class="me-auto"><a href="<?= $capitalarr['url'] ?>" target='_blank' class='text-primary'><?= $capitalarr['message'] ?></a></span>
         <button type="button" class="btn-close" data-mdb-dismiss="toast" onclick="hideCapital()" data-mdb-tooltip title="This will hide the alert for next 7 days" aria-label="Close"></button>
        </div>
    </div>

    <?php } elseif (strlen($shopnotice) > 0) { ?>
    <!-- Shop Notice -->
    <div class="toast show fade mt-0 pt-0 top-0 fixed-top mx-auto border-0 text-primary head-alert d-none d-md-block" id="shop-notice-toast" role="alert" aria-live="assertive" aria-atomic="true" data-mdb-autohide="false" data-mdb-position="...">
          <?php echo '<div contenteditable="' . ($ChangeNotice == 'YES' ? 'true' : 'false') . '" id="' . ($ChangeNotice == 'YES' ? 'myshopnotice' : '') . '" class="shopnotice p-1 text-center">' . $shopnotice . '</div>';?>
    </div>
   <?php }?>

    <div class="toast show fade mt-0 pt-0 fixed-top top-0 mx-auto text-primary" style="display: none;" id="overcount" role="alert" aria-live="assertive" aria-atomic="true" data-mdb-autohide="false" data-mdb-position="...">
        <div class="toast-header">
         <span class="me-auto">You have over 1000 OPEN repair orders. This will cause your Work In Process list to load very slowly and reports will be inaccurate. Please close all repair orders that work has been completed on. If you have questions, please contact support</span>
         <button type="button" class="btn-close" data-mdb-dismiss="toast" onclick="hideOverCount()" aria-label="Close"></button>
        </div>
    </div>