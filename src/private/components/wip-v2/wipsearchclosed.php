<?php

date_default_timezone_set('America/Phoenix');

function validateDate($date, $format = 'Y-m-d H:i:s')
{
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) == $date;
}

require CONN;
$shopid = $_GET['shopid'];
$gponwiplist = $_GET['gp'];

$sf = $_GET['sf'];
if (strlen($sf) > 0){

$phone = str_replace(array('(', ')', '-', ' '), '', $sf);

?>

<div class="wiplist" id="table-container">
    <h3>Closed Repair Orders</h3>
    <table id="wiplist-closed-table" class="display hover responsive nowrap row-border" style="width:100%">
        <thead>
        <tr>
            <th>RO</th>
            <th>Status</th>
            <th>Date</th>
            <th>Customer</th>
            <th>Phone</th>
            <th>Vehicle</th>
            <th>Total</th>
            <th>License</th>
            <?php if ($gponwiplist == 'yes') { ?>
                <th>GP</th><?php } ?>
            <th>Type</th>
        </tr>
        </thead>
        <tbody>
        <?php
        $stmt = "select totalprts,totallbr,totalsublet,salestax,discountamt,totalfees,gp,totalro,statusdate,vin,fleetno,customerid,roid,status as stat,datein,tagnumber,customer,"
            . " customerphone as home,customerwork as work,cellphone as cell,vehinfo,totalro,vehlicense,rotype,gp"
            . " from repairorders where status = 'closed' and (roid like '" . addslashes($sf) . "%' or customer like '%" . addslashes($sf) . "%' or replace(customer,'&#39;','\'') like '%" . addslashes($sf) . "%' or vin like '%" . addslashes($sf) . "%' or vehlicense like '%" . addslashes($sf) . "%' or fleetno like '%" . addslashes($sf) . "%' or tagnumber like '%" . addslashes($sf) . "%' or customerphone like '" . addslashes($phone) . "%'"
            . " or customerwork like '" . addslashes($phone) . "%' or cellphone like '" . addslashes($phone) . "%' or vehinfo like '%" . addslashes($sf) . "%' or convert(totalro,char) like '" . addslashes($sf) . "%' or DATE_FORMAT(statusdate,'%m/%d/%Y') like '%" . addslashes($sf) . "%') and shopid = '" . $shopid
            . "' order by roid desc, datein desc limit 100";
        $result = $conn->query($stmt);
        $msc = microtime(true);
        while ($row = $result->fetch_array()) {
            $home = $row["home"];
            $work = $row["work"];
            $cell = $row["cell"];
            $status = $row["stat"];
            $statvar = substr($status, 0, 1);
            $statdate = new DateTime($row["statusdate"]);
            if (is_numeric($statvar)) {
                $status = substr($status, 1);
            }
            if (strlen($row["home"]) > 0) {
                $home = "H: (" . substr($row["home"], 0, 3) . ") " . substr($row["home"], 3, 3) . "-" . substr($row["home"], 6);
            }
            if (strlen($row["work"]) > 0) {
                $work = "W: (" . substr($row["work"], 0, 3) . ") " . substr($row["work"], 3, 3) . "-" . substr($row["work"], 6);
            }
            if (strlen($row["cell"]) > 0) {
                $cell = "C: &nbsp;(" . substr($row["cell"], 0, 3) . ") " . substr($row["cell"], 3, 3) . "-" . substr($row["cell"], 6);
            }

            if (strlen($row['tagnumber']) > 0) {
                $roid = $row['roid'] . "-" . $row['tagnumber'];
            } else {
                $roid = $row['roid'];
            }

            ?>
            <tr data-roid=<?= $row['roid'] ?>>
                <td><a href='/v2/ro/ro.php?roid=<?= $row['roid'] ?>' style='color: inherit !important;'><?php echo strtoupper($roid); ?></a></td>
                <td><?php echo ucwords(strtolower(($status))) . " "; ?></td>
                <td><?php echo $statdate->format('n/j/y'); ?></td>
                <td><?php echo $row["customer"]; ?></td>
                <?php
                $h = "";
                if (strlen($home) > 0) {
                    $h = $h . $home;
                    if (strlen($work) > 0 || strlen($cell) > 0) {
                        $h = $h . "<br>";
                    }
                }
                if (strlen($work) > 0) {
                    $h = $h . $work;
                    if (strlen($cell) > 0) {
                        $h = $h . "<br>";
                    }
                }
                if (strlen($cell) > 0) {
                    $h = $h . $cell;
                }

                ?>
                <td data-mdb-html="true" data-mdb-toggle="tooltip" data-mdb-placement="bottom"
                    title="<?php echo $h; ?>">
                    <?php
                    if (strlen($cell) > 0) echo $cell;
                    elseif (strlen($home) > 0) echo $home;
                    elseif (strlen($work) > 0) echo $work;

                    ?>
                    <span style="display:none;"><?= implode(',', $noformat) ?></span>
                </td>

                <td>
                    <?php
                    if (strlen($row['fleetno']) > 0) {
                        echo "<b>#" . $row['fleetno'] . "</b> ";
                    }
                    echo substr($row["vehinfo"], 0, 30);
                    ?>
                </td>
                <td class="text-right"><?php echo '$' . number_format($row["totalro"], 2); ?></td>
                <td><?php echo strtoupper($row["vehlicense"]); ?></td>
                <?php if ($gponwiplist == 'yes') { ?>
                    <td><?= round($row['gp'] * 100) ?></td><?php } ?>
                <td><?php echo ucwords(strtolower(($row["rotype"]))); ?></td>
            </tr>
            <?php
            $tcroid = "no";
        }
        }
        mysqli_close($conn);
        ?>
        </tbody>
    </table>

    <script type="text/javascript">
        $('#wiplist-closed-table').DataTable({
            paging: false,
            info: false,
            dom: 'lrt',
            responsive: true,
            autoWidth: false,
            order: []
        });

    </script>
