<?php

$component = getComponent();
include getRulesGlobal($component);
include getHeadGlobal($component);
?>
<body>
<?php
include getPreheaderGlobal($component);
include getHeaderGlobal($component);
include getMenuGlobal($component);
include getPostheaderGlobal($component);
?>
<main id="wiplistframe" style="display:none">
    <iframe class="myframe" id="myframe"></iframe>
</main>

<main id="wipmain" style="display:none" class="min-vh-100">
    <?php
    $shopid = $_COOKIE['shopid'];
    $stime = microtime(true);
    date_default_timezone_set('America/Phoenix');
    $tdate = date('Y-m-d');
    $usr = isset($_COOKIE['usr']) ? $_COOKIE['usr'] : '';

    function seconds2human($ss)
    {
        $time = $ss;
        $seconds = $time % 60;
        $mins = floor($time / 60) % 60;
        $hours = floor($time / 60 / 60) % 24;
        $days = floor($time / 60 / 60 / 24);

        if ($days > 0) {
            return "$days d, $hours h";
        } else {
            return "$hours h";
        }
    }

    function getContrastColor($hexcolor)
    {
        $r = hexdec(substr($hexcolor, 1, 2));
        $g = hexdec(substr($hexcolor, 3, 2));
        $b = hexdec(substr($hexcolor, 5, 2));
        $yiq = (($r * 299) + ($g * 587) + ($b * 114)) / 1000;
        return ($yiq >= 128) ? 'black' : 'white';
    }


    function validateDate1($date, $format = 'Y-m-d H:i:s')
    {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) == $date;
    }


    // SHOP SETTINGS **********************************************************************************************************************

    $stmt = "select sortwipbylastfirst,firstlastonwip,showpromiseonwip,nexpartpassword as showcommlog,mailpassword as showbalanceonwip,showemailestimateonwip, showemailinvoiceonwip, showtimeclockonwipdata, showpaymentonwip, showinspectiononwip, showinpectionemailonwip, showtechoverhours,nexpartusername showpics,shopmgr,showtextemail,requirepayments,gponwiplist,showwaiting,showwtk,showvehcolor,showhrsonwip,profitboost,showsigonwip from company where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($sortwipbylastfirst, $firstlastonwip,
            $showpromiseonwip, $showcommlog, $showbalanceonwip, $showestemail,
            $showinvemail, $showtechtime, $showmoney, $showinsp, $showinspemail,
            $showtechoverhours, $showpics, $showelapsed, $showtextemail,
            $requirepayments, $gponwiplist, $showwaiting, $showwtk,
            $showvehcolor, $showhrsonwip, $isprofitboost, $showsigonwip);
        $query->fetch();
        $query->close();
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    $pphtarget = '';

    if ($isprofitboost == 'yes') {
        $stmt = "select target from profitboost where shopid = '$shopid'";
        if ($query = $conn->prepare($stmt)) {
            $query->execute();
            $query->bind_result($pphtarget);
            $query->fetch();
            $query->close();
        }
    }

    if ($_COOKIE['mode'] == 'tech2') {
        $stmt = "select showcustonwip,techsupervisor,showwtkonwip from techpermissions where shopid = '$shopid' and empid = "
            . $_COOKIE['empid'];
        if ($query = $conn->prepare($stmt)) {
            $query->execute();
            $query->store_result();
            $query->bind_result($showcustonwip, $techsupervisor, $showwtk);
            $query->fetch();
        }
    } else {
        $showcustonwip = 'yes';
        $techsupervisor = 'yes';
    }

    $empid = $_COOKIE['empid'];
    $wipsettings = "";
    if (!empty($empid) && is_numeric($empid)) {
        $stmt = "SELECT COALESCE(wipsettings,'') as wipsettings FROM employees WHERE shopid = ? AND id = ?";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param('si', $shopid, $empid);
            $query->execute();
            $query->bind_result($wipsettings);
            $query->fetch();
            $query->close();
        }
    }

    $rostatuses = array();
    $stmt = "select colorcode,status,isdefault from rostatus where shopid = ? order by displayorder";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_array()) {
            if ($rs['isdefault'] == 'yes' && is_numeric(substr($rs['status'], 0, 1)))
                $displaystatus = substr($rs['status'], 1);
            else
                $displaystatus = $rs['status'];

            $rostatuses[strtolower($displaystatus)] = $rs['colorcode'];
        }
    }

    $rostatuses_json = json_encode($rostatuses);

    // HEADER **********************************************************************************************************************
    ?>
    <link rel="stylesheet" type="text/css"
          href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.dataTables.min.css">
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.0.1/js/dataTables.buttons.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.0.1/js/buttons.colVis.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/colreorder/1.6.2/js/dataTables.colReorder.min.js">
    </script>
    <!-- WIPLIST TITLE -->

    <div id="wip-title-container" class="row">
        <div id="wip-title" class="d-flex ">
            <h2>Work In Process</h2>
        </div>
        <div id="wip-search" class="d-flex align-items-center">
            <form onsubmit="return false;" style="width: 100%;">
                <input type='text' class="form-control" name="search" id="search" placeholder="Search">
            </form>
            <span id="dt_button" class="ms-2"></span>
        </div>
    </div>

    </div>
    <!-- // MASS UPDATE ALERT BOX **********************************************************************************************************************  -->
    <div id="massupdates" style="display: none;">
        <div class="alert-container">
            <div class="alert mb-0">
                <div class="row">
                    <div class="col-11">
                        <div class="d-flex justify-content-sm-center align-items-sm-center justify-content-md-start ps-md-4">
                            <div class="d-inline-block ps-md-5 ms-md-5">Select a bulk change to
                                make to the selected RO(s)
                            </div>
                            <div class="d-inline-block ps-2 pe-2">
                                <div class="dropdown cursor-pointer">
                                    <div id="MassAssignDropDown"
                                         class="dropdown d-flex align-items-center dropdown-toggle text-start"
                                         data-mdb-toggle="dropdown" data-mdb-auto-close="outside" aria-expanded="true"
                                         style="">
                                        <div class="cursor-pointer form-control bg-transparent filter_box">
                                            <div class="float-start pe-1" style="">
                                                Select
                                            </div>
                                            <div class="float-start pe-1" style="">
                                                <span title="" class="text-primary small fl_count"
                                                      style="font-size:11px"></span>
                                            </div>
                                            <div class="float-end pe-1">
                                                <i class="select-arrow"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <ul class="dropdown-menu p-3 " aria-labelledby="dropdownMenuButton"
                                        style="" id="MassAssignDropDownMenu">
                                        <div class="row filter_MADownMenu">
                                            <?php if ($_COOKIE['mode'] == 'full') { ?>
                                                <div class="col">
                                                    <h5>RO Status <i class="fa fa-info-circle small"
                                                                     data-mdb-toggle="tooltip"
                                                                     title="Change RO status of the selected RO(s)"></i></h5>
                                                    <?php
                                                    $techMenu = false;
                                                    $techsoptions = '';
                                                    $stmt = "select status, colorcode from rostatus WHERE shopid = ? order by displayorder";
                                                    if ($query = $conn->prepare($stmt)) {
                                                        $query->bind_param("s", $shopid);
                                                        $query->execute();
                                                        $query->bind_result($rostatus, $colorcode);
                                                        while ($query->fetch()) {
                                                            if (is_numeric(substr($rostatus, 0, 1))) {
                                                                $rostatus = substr($rostatus, 1);
                                                            }
                                                            echo '<li class="dropdown-item p-0"><div class="form-check">
                                                                    <input type="checkbox" class="ma-checkbox status-checkbox form-check-input" name="status" data-option="status" value="' . ucwords(strtolower($rostatus)) . '" id="' . ucwords(strtolower($rostatus)) . '">
                                                                    <label class="form-check-label w-100" for="' . ucwords(strtolower($rostatus)) . '">' . ucwords(strtolower($rostatus)) . '</label>
                                                                    </div></li>';
                                                        }
                                                        $query->close();
                                                    }
                                                    ?>
                                                </div>
                                            <?php }
                                            if ($_COOKIE['mode'] == 'full') {
                                                ?>
                                                <div class="col">
                                                    <h5>RO Type <i class="fa fa-info-circle small" data-mdb-toggle="tooltip"
                                                                   title="Change RO Type of the selected RO(s)"></i></h5>
                                                    <?php
                                                    $techMenu = false;
                                                    $techsoptions = '';
                                                    $stmt = "select ROType from rotype WHERE shopid = ? order by ROType";
                                                    if ($query = $conn->prepare($stmt)) {
                                                        $query->bind_param("s", $shopid);
                                                        $query->execute();
                                                        $query->bind_result($rotype);
                                                        while ($query->fetch()) {
                                                            echo '<li class="dropdown-item p-0"><div class="form-check"><input type="checkbox" name="type" class="ma-checkbox type-checkbox form-check-input" data-option="type" value="' . ucwords(strtolower($rotype)) . '" id="' . ucwords(strtolower($rotype)) . '"><label class="form-check-label w-100" for="' . ucwords(strtolower($rotype)) . '">' . ucwords(strtolower($rotype)) . '</label></div></li>';
                                                        }
                                                        $query->close();
                                                    }
                                                    ?>
                                                </div>
                                            <?php }
                                            if ($_COOKIE['mode'] == 'full') { ?>
                                                <div class="col">
                                                    <h5>Technicians <i class="fa fa-info-circle small"
                                                                       data-mdb-toggle="tooltip"
                                                                       title="Assign all labor to a technician for selected RO(s)"></i>
                                                    </h5>
                                                    <?php
                                                    $techMenu = false;
                                                    $techsoptions = '';
                                                    $stmt = "select CONCAT(employeelast, ', ', employeefirst) as tech, id,hourlyrate from employees WHERE shopid = '$shopid' AND active = 'yes' order by employeelast";
                                                    if ($tquery = $conn->prepare($stmt)) {
                                                        $tquery->execute();
                                                        $r = $tquery->get_result();
                                                        $count = 0;
                                                        while ($rs = $r->fetch_assoc()) {
                                                            $techMenu = true;
                                                            $count++;
                                                            $tech = str_replace(" ", "_", $rs['tech']);
                                                            echo '<li class="dropdown-item p-0"><div class="form-check">
                                                                    <input type="checkbox" class="ma-checkbox tech-checkbox form-check-input" name="tech" data-option="tech" data-techid="' . $rs['id'] . '" data-techrate="' . $rs['hourlyrate'] . '" value="' . ucwords(strtolower($rs['tech'])) . '" id="' . ucwords(strtolower($rs['tech'])) . '">
                                                                    <label class="form-check-label w-100" for="' . ucwords(strtolower($rs['tech'])) . '">' . ucwords(strtolower($rs['tech'])) . '</label>
                                                                    </div></li>';
                                                        }
                                                        $tquery->close();
                                                    }
                                                    ?>
                                                </div>
                                            <?php } ?>
                                        </div>
                                        <div class="row fixed-footer m-0">
                                            <div class="col text-center">
                                                <button class="btn btn-primary" id="massupdate_btn">Update
                                                </button>
                                            </div>
                                        </div>
                                    </ul>
                                </div>
                            </div>
                            <!--
                            <div class="d-inline-block ps-2 pe-2">
                                <button type="button" class="btn btn-primary">
                                    <a href="javascript:void(null)"
                                       class="wave-effect"
                                       onclick="massUpdate()">Update Selected</a>
                                </button>
                            </div> -->
                        </div>
                    </div>
                    <div class="col-1">
                        <div class="d-flex justify-content-end align-items-center h-100">
                            <button type="button" class="btn-close" data-mdb-dismiss="toast" onclick="cancelMassUpdates()"
                                    aria-label="Close"></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- WIPLIST TABLE BEGINS **********************************************************************************************************************  -->

    <div class="d-flex wiplist invisible">
        <div id="table-container-wip" class="row content-container pt-0 pe-0 ps-0 m-0">
            <table id="WIP" class="display responsive nowrap table-sm table-striped" style="width:100%">
            </table>
            <!-- TABLE CONTAINER ENDS ********************************************************************************************************************** -->
        </div>
        <!-- WIPLIST DIV ********************************************************************************************************************** -->
    </div>

    <!-- WIPLIST TABLE ENDS **********************************************************************************************************************  -->


    <!-- PREMODAL DIV -->
    <div id="preromodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Convert PreRO</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <iframe id="prero-iframe" class="embed-responsive-item" frameborder=0 src=""
                            style="width:100%;height:75vh;display:block;"></iframe>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-default btn-sm" type="button" data-dismiss="modal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

</main>

<!-- SCRIPTS BEGIN **********************************************************************************************************************  -->
<?php
include getModalsGlobal($component);
include getScriptsGlobal($component); ?>


<!-- Function to check conditions and generate table headers -->
<?php

function generateTableHeader($condition, $class, $title)
{
    if ($condition == 'yes') {
        return '<th class="' . $class . '">' . $title . '</th>';
    } else {
        return '';
    }
}

?>


<script type="text/javascript">

    // FUNCTIONS BEGIN **********************************************************************************************************************


    function showPreRO(id) {
        t = 'Convert Pre-RO <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="This Pre-RO can be converted to an actual RO or deleted"></i>'
        p = "<?= COMPONENTS_PRIVATE ?>/v2/prero/prero.php?shopid=<?= $shopid; ?>&id=" + id
        parent.eModal.iframe({
            title: t,
            url: p,
            size: eModal.size.lg
        });
    }

    function cancelMassUpdates() {
        $(":checkbox").each(function () {
            $(this).attr('checked', false)
        });
        $('#massupdates').fadeOut()
    }

    function massUpdate() {
        //close final noapproval
        r = $('#massaction').val()
        roids = ""
        zeroroid = ""
        $(":checkbox").each(function () {
            if ($(this).is(':checked')) {
                roid = $(this).attr("id")
                roid = roid.replace("checkbox", "")
                roids = roids + roid + "|"
                if ($(this).attr("data-rec") == 0) zeroroid = $(this).attr("data-rec")
                return
            }
        });
        if (r == "close") {
            if ('<?= $requirepayments?>' == 'yes' && zeroroid != '') {
                msg = "One or more ROs cannot be closed as payment is required. Would you still like to continue?"
                btn = "Yes"
            } else {
                msg = "Are you sure you want to CLOSE the selected repair orders?"
                btn = "Yes, Close Them"
            }
        } else if (r == "final") {
            msg = "Are you sure you want to FINALIZE the selected repair orders?"
            btn = "Yes, Finalize Them"
        } else if (r == "noapproval") {
            msg = "Are you sure you want to mark the selected repair orders as NO APPROVAL?"
            btn = "Yes, mark them No Approval"
        }
        sbconfirm("Mass Update", msg, function () {
            // get the ro numbers that are checked
            showLoader()
            ds = "r=" + r + "&shopid=<?= $shopid; ?>&roids=" + roids
            $.ajax({
                data: ds,
                url: "massupdate.php",
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        parent.location.reload()
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                    hideLoader()
                }
            })
        });
    }

    function showMassUpdates() {
        // loop all checkboxes, if any are checked show the massupdate div
        cntr = 0
        $(":checkbox").each(function () {
            if ($(this).is(':checked')) {
                cntr = cntr + 1
                return
            }
        });
        var fixedDiv = $('#massupdates');
        var width = $("main#wipmain").width();
        fixedDiv.width(width)

        if (cntr > 0) {
            $('#massupdates').fadeIn()
        } else {
            $('#massupdates').fadeOut()
        }
    }

    $(document).ready(function () {

        var fixedDiv = $('#massupdates');
        var navHeight = $('#nav-sb').outerHeight();
        var offset = fixedDiv.offset().top;

        $(window).scroll(function() {
            if ($(window).scrollTop() >= navHeight - offset) {
                fixedDiv.addClass('fixed-below-nav');
            } else {
                fixedDiv.removeClass('fixed-below-nav');
            }
        });

        $(window).resize(function() {
            var fixedDiv = $('#massupdates');
            var width = $("main#wipmain").width();
            fixedDiv.width(width)
        });

    })


</script>
<!-- FUNCTIONS END **********************************************************************************************************************   -->


<!-- DATA TABLES CODE BEGINS ************************************************************************************************************************************************************ -->


<script>

    <?php
    if ($_COOKIE['mode'] == 'full' && $sortwipbysa == 'yes') {
    ?>

    let filter_writer = Cookies.get('filter_writer');
    let filter_tech = Cookies.get('filter_tech');

    let writerArr = filter_writer ? filter_writer.split(":") : [];
    let techArr = filter_tech ? filter_tech.split(":") : [];

    const dropdownMenu = document.getElementById('WriterDropDownMenu');

    if (dropdownMenu) {
        function updateCookies() {
            let selectedWriters = [];
            let selectedTechs = [];

            let total_writer = 0
            let total_tech = 0;
            document.querySelectorAll('.dropdown-checkbox').forEach(cb => {
                if (cb.checked) {
                    if (cb.dataset.type === 'writer') {
                        selectedWriters.push(cb.value);
                        total_writer++;
                    } else if (cb.dataset.type === 'tech') {
                        selectedTechs.push(cb.value);
                        total_tech++;
                    }
                }
            });

            Cookies.set('filter_writer', selectedWriters.join(":"), {expires: 360});
            Cookies.set('filter_tech', selectedTechs.join(":"), {expires: 360});

            update_filter_count(total_writer, total_tech);

            const currentTab = Cookies.get('tab');

            customParams = customParams.replace(/(writerfilter=)[^&]*/, '$1' + selectedWriters.join(":")).replace(/(techfilter=)[^&]*/, '$1' + selectedTechs.join(":"));

            if (currentTab === 'kanban') changeWip('kanban')
            else
                table.ajax.reload();
        }

        function update_filter_count(total_writer, total_tech) {

            const dropdownSpan = document.getElementById('WrtierDropDown').querySelector(".fl_count");
            let writer_html = "";
            if (total_writer > 0) {
                writer_html += total_writer + "W ";
            }
            if (total_tech > 0) {
                writer_html += total_tech + "T";
            }
            dropdownSpan.innerHTML = writer_html
        }


        let total_writer = 0
        let total_tech = 0;

        dropdownMenu.querySelectorAll('.dropdown-checkbox').forEach(checkbox => {
            const value = checkbox.value;
            const type = checkbox.dataset.type;

            if ((type === 'writer' && writerArr.includes(value))) {
                checkbox.checked = true;
                total_writer++;
            }
            if ((type === 'tech' && techArr.includes(value))) {
                checkbox.checked = true;
                total_tech++;
            }

            checkbox.addEventListener('click', event => event.stopPropagation());
            checkbox.addEventListener('change', updateCookies);
        });

        update_filter_count(total_writer, total_tech)

        dropdownMenu.querySelectorAll('.dropdown-item').forEach(item => {
            item.addEventListener('click', function (e) {
                e.stopPropagation();
                const checkbox = this.querySelector('.dropdown-checkbox');
                if (checkbox) {
                    checkbox.checked = !checkbox.checked;
                    checkbox.dispatchEvent(new Event('change'));
                }
            });
        });
    }


    <?php
    }
    ?>

    let writerfilter = '';
    let techfilter = '';
    let wipinterval = '';

    // Check if the dropdown with the specified ID exists
    const dropdown = parent.document.getElementById('WriterDropDownMenu');
    if (dropdown) {
        let writerArr = [];
        let techArr = [];

        // Iterate through all checkboxes within the dropdown to find the selected ones
        dropdown.querySelectorAll('.dropdown-checkbox').forEach(function (checkbox) {
            if (checkbox.checked) {
                if (checkbox.dataset.type === 'writer') {
                    writerArr.push(checkbox.value);
                } else if (checkbox.dataset.type === 'tech') {
                    techArr.push(checkbox.value);
                }
            }
        });

        // Combine the selected values into the filter strings
        writerfilter = writerArr.join(":");
        techfilter = techArr.join(":");
    } else {
        writerfilter = '';
        techfilter = '';
    }

    rostatuses = <?php echo $rostatuses_json; ?>;

    let closedRequest = null;
    let customParams = "pphtarget=<?= $pphtarget?>&sortwipbylastfirst=<?= $sortwipbylastfirst ?>&firstlastonwip=<?= $firstlastonwip ?>&showpromiseonwip=<?= $showpromiseonwip ?>&showcommlog=<?= $showcommlog ?>&showbalanceonwip=<?= $showbalanceonwip ?>&showestemail=<?= $showestemail ?>&showinvemail=<?= $showinvemail ?>&showtechtime=<?= $showtechtime ?>&showmoney=<?= $showmoney ?>&showinsp=<?= $showinsp ?>&showinspemail=<?= $showinspemail ?>&showtechoverhours=<?= $showtechoverhours ?>&showpics=<?= $showpics ?>&showelapsed=<?= $showelapsed ?>&showtextemail=<?= $showtextemail ?>&requirepayments=<?= $requirepayments ?>&gponwiplist=<?= $gponwiplist ?>&showwaiting=<?= $showwaiting ?>&showwtk=<?= $showwtk ?>&showvehcolor=<?= $showvehcolor ?>&showhrsonwip=<?= $showhrsonwip ?>&isprofitboost=<?= $isprofitboost ?>&showsigonwip=<?= $showsigonwip ?>&showcustonwip=<?= $showcustonwip?>&techsupervisor=<?= $techsupervisor?>&writerfilter=" + writerfilter + "&techfilter=" + techfilter;
    drawdone = false;

    $(document).ready(function () {

        showLoader()

        table = $('#WIP').DataTable({
            ajax: {
                url: 'wipdata.php',
                type: 'POST',
                data: function (d) {
                    if (typeof d === 'undefined' || d === null) {
                        d = {}; // Initialize `d` if it's undefined
                    }
                    // console.log(d, customParams);
                    d.customParams = customParams;
                }
            },
            columns: [
                {
                    data: 'rodisplay',
                    title: 'RO',
                    className: 'noVis ro-wip',
                    render: function (data, type, row, meta) {

                        if (row.itemtype == 'RO') {
                            return "<a href='/v2/ro/ro.php?roid="+row.roid+"' style='color: inherit !important;' data-mdb-html=true data-mdb-toggle=tooltip data-mdb-placement=bottom title=\"" + row.comms + "\">" + data + "</a>"
                        } else return data;
                    }
                },
                {
                    data: 'status',
                    title: 'Status',
                    className: 'status-wip',
                    render: function (data, type, row, meta) {

                        if (type === 'display' && row.itemtype == 'RO') {
                            return "<i class='dot align-self-center mx-2' style='background-color:" + rostatuses[data.toLowerCase()] + "'></i>" + "<span data-mdb-html=true data-mdb-toggle=tooltip data-mdb-placement=bottom title=\"" + row.comms + "\">" + data + "</span>"
                        } else return data;
                    }
                },
                {
                    data: 'info',
                    title: 'Info',
                    className: 'info-wip',
                    orderable: false
                },
                {data: 'date', title: 'Date', className: 'date-wip'},

                <?php if ($showpromiseonwip == 'yes') { ?>
                {
                    data: 'promised',
                    title: 'Promised',
                    className: 'promised-wip'
                },
                <?php }?>

                <?php if ($_COOKIE['mode'] == 'full'
            && $showelapsed == 'yes') { ?>
                {data: 'elapsed', title: 'Elapsed', className: 'elapsed-wip'},
                <?php }?>

                <?php if ($showcustonwip == 'yes') { ?>

                {
                    data: 'customer',
                    title: 'Customer',
                    className: 'customer-wip',
                    render: function (data, type, row, meta) {

                        if ('<?= $_COOKIE['mode']?>' == 'full' && type === 'display' && row.origshopid === '<?= $shopid?>') {
                            return "<a onclick=\"parent.location.href='/v2/customer/customer-edit.php?cid=" + row.customerid + "'\">" + data.trim() + "</a>"
                        } else return data.trim();
                    }
                },
                <?php }?>

                <?php if($_COOKIE['mode'] == 'full'){?>
                {
                    data: 'phone',
                    title: 'Phone',
                    className: 'phone-wip',
                    orderable: false
                },
                <?php }?>

                {data: 'vehicle', title: 'Vehicle', className: 'vehicle-wip'},

                <?php if($_COOKIE['mode'] == 'full'){?>
                {data: 'total', title: 'Total', className: 'total-wip'},
                <?php }?>

                <?php if ($_COOKIE['mode'] == 'full'
            && $showhrsonwip == 'yes') { ?>
                {data: 'laborhours', title: 'Lab. Hrs', className: 'labor-wip'},
                <?php }?>

                {
                    data: 'license',
                    title: 'License',
                    className: 'license-wip',
                    render: function (data, type, row, meta) {

                        if (type === 'display') {
                            return "<a onclick=\"parent.location.href='/v2/history/history.php?vin=" + row.vin + "'\">" + data + "</a>"
                        } else return data;
                    }
                },

                <?php if ($_COOKIE['mode'] == 'full'
            && $gponwiplist == 'yes') { ?>
                {data: 'gp', title: 'GP', className: 'gp-wip'},
                <?php }?>

                <?php if ($_COOKIE['mode'] == 'full'
            && $hasPph == 'yes' && $pphAccess == 'yes') { ?>
                {
                    data: 'pph',
                    title: 'PPH',
                    className: 'pph-wip',
                    render: function (data, type, row, meta) {

                        if (type === 'display') {
                            return "<span style='color:" + row.pphcolor + "'>" + data + "</span>"
                        } else return data;
                    }
                },
                <?php }?>

                <?php if ($showwtk == 'yes') { ?>
                {data: 'writer', title: 'W+TK', className: 'wtk-wip'},
                <?php }?>

                <?php if ($_COOKIE['mode'] == 'full'
            && $showvehcolor == 'yes') { ?>
                {data: 'color', title: 'Color', className: 'color-wip'},
                <?php }?>

                {
                    data: 'rotype',
                    title: 'Type',
                    className: 'type-wip',
                    render: function (data, type, row, meta) {

                        if (type === 'display' && row.itemtype == 'RO') {
                            return "<i class='dot mx-2' style='background-color:" + row.typebgcolor + ";" + (row.typecolor != '' ? "color:" + row.typecolor : '') + "'></i>" + data
                        } else return data;
                    }
                },

                <?php if ($_COOKIE['mode'] == 'full') { ?>
                {
                    data: null,
                    title: 'Select',
                    className: 'select-wip',
                    orderable: false,
                    render: function (data, type, row, meta) {

                        if (type === 'display' && row.itemtype == 'RO') {
                            return '<input type="checkbox" class="form-check-input" onclick="showMassUpdates()" id="checkbox' + row.roid + '">';
                        } else return null;
                    }
                }
                <?php }?>

            ],

            initComplete: function (settings, json) {
                drawdone = true
                $('[data-mdb-toggle="tooltip"]').tooltip();
                hideLoader()
                $(".wiplist").removeClass('invisible');
                $('.dt-buttons').detach().prependTo('#dt_button');
                $('#WIP_filter').detach();
            },


            createdRow: function (row, data, dataIndex) {

                if (data.itemtype == 'RO') {
                    $(row).addClass(data.trclass);
                    $(row).attr('data-roid', data.roid);
                } else if (data.itemtype == 'PS') {
                    $(row).attr('data-psid', data.roid);
                } else if (data.itemtype == 'PreRO') {
                    $(row).addClass('prero_row');
                    $(row).attr('id', data.roid);
                }

            },

            processing: false,
            serverSide: false,
            deferRender: true,
            dom: 'Bfrti',
            fixedHeader: true,
            responsive: true,
            stateSave: true,
            autoWidth: false,
            info: false,
            buttons: [{
                extend: 'colvis',
                text: '<i class="fa fa-cog"></i>',
                columns: ':not(.noVis)', // only columns without 'noVis' class will be toggleable
                columnText: function (dt, idx, title) {
                    return '<div class="">' +
                        '<input class="form-check-input colvischeck" id="vischeck_' + idx + '" type="checkbox" value=""> ' +
                        '<label class="form-check-label">' + title + '</label>' +
                        '</div>';
                }
            }],
            colReorder: {
                fixedColumnsLeft: 1
            },
            order: [],
            pageLength: 8000,
            language: {
                search: '',
                searchPlaceholder: "Search..."
            },
            stateLoadCallback: function (settings, data) {
                <?php
                if (!empty($wipsettings)){ ?>
                data = JSON.parse('<?= $wipsettings ?>');
                //   console.log('loaded from DB', data);
                <?php
                } else {
                ?>
                data = JSON.parse(localStorage.getItem('DataTables_' + settings.sInstance + '_'));
                //  console.log("loaded form local storage : ", data);
                <?php
                }
                if(isset($_GET['reset'])){?>
                data.order = [];
                <?php }
                ?>
                return data;
            },
            /*
            stateLoadParams: function (settings, data) {
                <?php if(isset($_GET['reset'])){?>
                data.order = [];
                <?php }?>
                return data
            },
            */
            stateSaveCallback: function (settings, data) {
                data.search.search = '';
                <?php
                if (strtolower($empid) != 'admin'){
                ?>
                $.ajax({
                    url: "<?= COMPONENTS_PRIVATE ?>/shared/wip_settings.php",
                    data: {data: JSON.stringify(data), op: 'save'},
                    type: "post",
                    success: function (r) {
                        if (r != '' && r.length > 0) {

                        } else {
                            localStorage.setItem(
                                'DataTables_' + settings.sInstance + '_',
                                JSON.stringify(data)
                            );
                        }
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    }
                });
                <?php
                } else {
                ?>
                localStorage.setItem(
                    'DataTables_' + settings.sInstance + '_',
                    JSON.stringify(data)
                );
                <?php
                }
                ?>
                /*
               localStorage.setItem(
                    'DataTables_' + settings.sInstance + '_',
                    JSON.stringify(data)
                );

                 */
            },
            headerCallback: function (thead, data, start, end, display) {
                $(thead).find('th.sorting, th.sorting_disabled, th.sorting_asc, th.sorting_desc, th.sorting_asc_disabled, th.sorting_desc_disabled').css({
                    'text-align': 'center',
                    'font-weight': 'bold'
                });
            }
        });

        //  var state = table.state();

        <?php if (strtoupper($showtechoverhours) == "YES") { ?>
        setTimeout(function () {
            $.ajax({
                url: "techclockchecks.php",
                data: "shopid=<?= $shopid; ?>",
                type: "post",
                success: function (r) {
                    if (r != '') {
                        rar = r.split(",")
                        for (i = 0; i < rar.length; i++) {
                            elemname = 'techclockover' + rar[i]
                            document.getElementById(elemname).style.display = "inline-block"
                        }
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            });
        }, 60000);
        <?php } ?>

        table.on('search.dt', function () {
            var searchKey = table.search();
            if (searchKey) searchClosed(searchKey);
        });

        $('#search').on('keyup', function () {
            table.search(this.value).draw();
        });

        <?php
        // Initialize opencounter to prevent undefined variable notice
        $opencounter = isset($opencounter) ? $opencounter : 0;
        ?>
        if ("<?php echo $opencounter; ?>" >= 1000 && sessionStorage.getItem("hideovercount") === null) {
            parent.$('.head-alert').hide()
            parent.$('#overcount').show()
        }

        wipinterval = setInterval(refreshData, 50000);

        $('#search').focus();
    });


    // DATA TABLES ENDS ***************************************

    $('#wip-title-container input.colvischeck').off();

    $('body').on('click', '#WIP tr:not(.prero_row) td', function (event) {


        var posX = event.pageX - $(this).offset().left;

        if ($(this).hasClass('ro-wip') && posX >= 30)
            event.stopPropagation();

        if ($(this).attr("onClick") === undefined && (posX >= 30 || !$('#WIP').hasClass('collapsed')) && (event.target.tagName == 'TD' || event.target.tagName == 'SPAN')) {
            var $parent = $(this).parent();
            var psid = $parent.attr("data-psid");
            var roid = $parent.attr("data-roid");
            var prevParent = $parent.prev('tr');
            var prevPsid = prevParent.attr("data-psid");
            var prevRoid = prevParent.attr("data-roid");

            var redirectTo;
            if (psid !== undefined) {
                redirectTo = '<?= COMPONENTS_PRIVATE ?>/v2/partsale/partsale.php?psid=' + psid;
            } else if (roid !== undefined) {
                redirectTo = '<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?roid=' + roid;
            } else if (prevRoid !== undefined) {
                redirectTo = '<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?roid=' + prevRoid;
            } else if (prevPsid !== undefined) {
                redirectTo = '<?= COMPONENTS_PRIVATE ?>/v2/partsale/partsale.php?psid=' + prevPsid;
            }

            if (redirectTo !== undefined) {
                parent.location.href = redirectTo;
            }
        }
    });


    $('#table-container-wip').on('click', '#wiplist-closed-table tr td', function () {
        parent.location.href = '<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?roid=' + $(this).parent().attr("data-roid")
    });


    $('#dt_button').on('click', 'button input.colvischeck', function (e) {
        e.stopPropagation();
        var checkBox = $(this);
        setTimeout(function () {
            checkBox.prop("checked", !checkBox.prop("checked"));
        }, 0);
    });

    $('#wip-title-container').on('click', '#dt_button', function () {
        $(this).find("button.active input.colvischeck").prop('checked', true)
    });

    $('#WIP').on('column-visibility.dt', function (e, settings, column) {
        var checkBox = $("#vischeck_" + column);
        checkBox.prop("checked", !checkBox.prop("checked"));
    });

    function refreshData(writer = null) {
        console.log("reloading")
        if (!drawdone) return;

        drawdone = false

        table.ajax.reload();

    }

    function searchClosed(v) {
        showLoader()
        ds = "shopid=<?= $shopid; ?>&sf=" + encodeURIComponent(v) + "&gp=<?= $gponwiplist?>"

        if (closedRequest != null) {
            closedRequest.abort();
        }

        closedRequest = $.ajax({
            data: ds,
            url: "wipsearchclosed.php",
            success: function (r) {
                $('#table-container').remove()
                $('#table-container-wip').append(r)
                hideLoader()
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }
        });
    }
</script>


<!-- TOUCH EVENTS FOR MOBILE SCROLLING-->
<script>
    $(document).on('touchstart', '.prero_row td', function (e) {
        touchStartLocation = e.originalEvent.touches[0].pageY;
    });
    $(document).on('touchmove', '.prero_row td', function (e) {
        var touchEndLocation = e.originalEvent.changedTouches[0].pageY;
        if (Math.abs(touchStartLocation - touchEndLocation) > 10) {
            $(this).unbind('click');
        }
    });
    $(document).on('click', '.prero_row td', function (event) {

        var posX = event.pageX - $(this).offset().left;

        if ($(this).hasClass('ro-wip') && posX >= 30)
            event.stopPropagation();

        if ((posX >= 30 || !$('#WIP').hasClass('collapsed')) && event.target.tagName == 'TD')
            showPreRO($(this).data('id'));
    });
</script>

</body>

</html>