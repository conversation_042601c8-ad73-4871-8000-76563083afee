<div class="secondary-links">
        <div class="secondary-links-container d-flex justify-content-center w-100">
            <nav class="navbar navbar-expand-lg">
                <div class="container-fluid d-flex justify-content-center w-100">
                    <!-- Toggle button -->
                        <button class="navbar-toggler text-primary" type="button" data-mdb-toggle="collapse" data-mdb-target="#navbar02" aria-controls="navbar02" aria-expanded="false" aria-label="Toggle navigation">
                            <i class="fas fa-angle-down"></i>
                        </button>
                    <!-- Collapsible wrapper -->
                    <div class="collapse navbar-collapse secondary-links-background w-100" id="navbar02">
                        <!-- Navbar brand -->
                        <a class="navbar-brand" href="javascript:void(null)"></a>
                        <!-- Left links -->

                     <ul class="navbar-nav d-flex align-items-center">

                        <?php if($accessworkflow == 'yes'){?>
                        <li class="nav-item d-flex text-center pe-1 lightgreenBG fixed-size-button">
                            <a href="javascript:void(null)" onclick="<?= $workflownewtab == 'yes' ? "openWorkflow()" : "changeWip('kanban')" ?>">Workflow</a>
                        </li>
                        <li class="align-middle text-secondary secondary-link-divider mb-1">
                            |
                        </li>
                        <?php }?>               
                        <li class="nav-item d-flex text-center pe-1 blueBG fixed-size-button">
                            <a href="javascript:void(null)" onclick="loadWip('menu')">WIP</a>
                        </li>
                        <li class="align-middle text-secondary secondary-link-divider mb-1">
                            |
                        </li>      
                        <?php
                        if ($_COOKIE['mode'] == 'full' && $sortwipbysa == 'yes') {
                            $techMenu = false;
                            $techsoptions = '';
                            // get a list of the current advisors
                            $stmt = "select distinct l.tech from labor l JOIN repairorders r ON l.shopid = r.shopid AND l.roid = r.ROID where l.shopid = '$shopid' and r.`status` != 'CLOSED' and r.ROType != 'No Approval' AND l.deleted = 'no' AND l.Tech != 'discount, discount'";
                            if ($tquery = $conn->prepare($stmt)) {
                                $tquery->execute();
                                $r = $tquery->get_result();
                                $count = 0;
                                while ($rs = $r->fetch_array()) {
                                    $techMenu = true;
                                    $count++;
                                    $tech = str_replace(" ", "_", $rs['tech']);
                                    $techsoptions .= '<li class="dropdown-item p-0"><div class="form-check"><input type="checkbox" class="dropdown-checkbox form-check-input" data-type="tech" id="tech_' . $count . '" value="' . $rs['tech'] . '"><label class="form-check-label" for="ltech_' . $count . '">' . ucwords(strtolower($rs['tech'])) . '</label></div></li>';
                                }
                                $tquery->close();
                            } else {
                                die($conn->error);
                            }

                            $writersmenu = false;
                            $writersoptions = '';
                            // get a list of the current advisors
                            $stmt = "select distinct writer from repairorders where shopid = '$shopid' and `status` != 'CLOSED' and ROType != 'No Approval' UNION select distinct writer from ps where shopid = '$shopid' and `status` != 'CLOSED' AND `status` != 'dead' and writer!=''";
                            if ($query = $conn->prepare($stmt)){
                                $query->execute();
                                $r = $query->get_result();
                                $count = 0;
                                while ($rs = $r->fetch_array()){
                                    $writersmenu = true;
                                    $count++;
                                    $writer = str_replace(" ","_",$rs['writer']);
                                    $writersoptions .= '<li class="dropdown-item p-0"><div class="form-check"><input type="checkbox" class="dropdown-checkbox form-check-input" data-type="writer" value="' . $rs['writer'] . '" id="writer_' . $count . '"><label class="form-check-label" for="lwriter_' . $count . '">' . ucwords(strtolower($rs['writer'])) . '</label></div></li>';
                                }
                                if ($writersmenu || $techMenu) {
                                    ?>
                                    
                                    <li class="nav-item d-flex pe-1" style="">
                                        <div class="dropdown cursor-pointer">
                                            <div id="WrtierDropDown" class="dropdown d-flex align-items-center dropdown-toggle text-start" data-mdb-toggle="dropdown" aria-expanded="true" style="">
                                                <div class="cursor-pointer form-control bg-transparent filter_box">
                                                    <div class="float-start pe-1" style="">
                                                        Select
                                                    </div>
                                                    <div class="float-start pe-1" style="">
                                                        <span title="<?= $filter_count_text ?>" class="text-primary small fl_count" style="font-size:11px"><?= $filter_count_text ?></span>
                                                    </div>
                                                    <div class="float-end pe-1">
                                                        <i class="select-arrow"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            <ul class="dropdown-menu p-3 filter_dropDownMenu" aria-labelledby="dropdownMenuButton" style="" id="WriterDropDownMenu">
                                                <div class="row">
                                                    <!-- Column for Writers -->
                                                    <div class="col">
                                                        <h5>Writers</h5>
                                                        <?php
                                                        if ($writersmenu) {
                                                            echo $writersoptions;
                                                        } ?>
                                                    </div>
                                                    <!-- Column for Technicians -->
                                                    <?php
                                                    if ($techMenu) {
                                                    echo '<div class="col">';
                                                            echo "<h5>Technicians</h5>";
                                                            echo $techsoptions;
                                                    echo '</div>';
                                                            
                                                        }
                                                        ?>
                                                </div>
                                            </ul>
                                        </div>
                                    </li>
                                    <li class="align-middle text-secondary secondary-link-divider mb-1">|</li>
                                    <?php
                                }
                            }
                        }

                        ?>         
                        <li class="nav-item d-flex text-center  pe-1 orangeBG fixed-size-button">
                            <a href="javascript:void(null)" onclick="changeWip('timeclocktab')">Time Clock</a>
                        </li>
                        <li class="align-middle text-secondary secondary-link-divider mb-1">
                            |
                        </li>       
                        <?php
                        if ($readonly == "no" && ($motorfull == "yes" || $motorest == "yes")) {
                        ?>        
                        <li class="nav-item d-flex text-center  pe-1 lightblueBG fixed-size-button">
                            <a href="javascript:void(null)" onclick="loadMotor()">Motor</a>
                        </li>
                        <li class="align-middle  text-secondary secondary-link-divider mb-1">
                            |
                        </li>      
                        <?php }?> 

                        <?php if($_COOKIE['mode'] == 'full'){?>        
                        <li class="nav-item d-flex text-center pe-1 lightredBG fixed-size-button">
                            <a href="javascript:void(null)" id="smstab" onclick="changeWip('smstab')">Live Text<span class="position-absolute top-0 start-100 translate-middle badge rounded-pill badge-notification" id="livetext_badge"></span></a>
                        </li>
                        <li class="align-middle  text-secondary secondary-link-divider mb-1">
                            |
                        </li>
                        <li class="nav-item d-flex text-center lightgoldBG fixed-size-button">
                            <a href="javascript:void(null)" id="techactivities" onclick="openTA()">Tech Activities</a>
                        </li>
                        <?php }?>
                    </ul>
                </div>
            </div>
        </nav>
    </div>
</div>
