<?php
error_reporting(null);
if(empty($_REQUEST['list'])){
    return;
}
include_once INTEGRATIONS_PATH."/PDFMerger/PDFMerger.php";

use PDFMerger\PDFMerger;

$pdfmerger = new PDFMerger;

$list = $_GET['list'];
$listarr= explode(',', $list);

if(!empty($listarr))
{
    foreach($listarr as $pdf)
    {
        if(!empty($pdf))
        {
            if (file_exists(COMPONENTS_PUBLIC_PATH."/invoices/".$pdf)) {
                $pdfmerger->addPDF(COMPONENTS_PUBLIC_PATH . "/invoices/" . $pdf);
            } else if (file_exists($pdf)) {
                $pdfmerger->addPDF($pdf);
            }
        }
    }


    $pdfmerger->merge('browser');
}