<!-- <PERSON><PERSON><PERSON><PERSON><PERSON> Scripts Start -->
<script async="false">
    var beamer_config = {
        product_id: "KdwxtFFu19213", //DO NOT CHANGE: This is your product code on Beamer
        selector: 'beamerButton',
        bounce: false,
        filter: '<?php echo $filter_type; ?>',
        user_firstname: "<?php echo addslashes($userfirst); ?>",
        user_lastname: "<?php echo addslashes($userlast); ?>",
        user_id: '<?php echo $shopid; ?>',
        user_email: '<?php echo $empemail; ?>'
    };
</script>
<script type="text/javascript" src="https://app.getbeamer.com/js/beamer-embed.js" defer="defer"></script>
<script>
    !function (w, d, i, s) {
        function l() {
            if (!d.getElementById(i)) {
                var f = d.getElementsByTagName(s)[0],
                    e = d.createElement(s);
                e.type = "text/javascript", e.async = !0, e.src = "https://canny.io/sdk.js", f.parentNode.insertBefore(e, f)
            }
        }

        if ("function" != typeof w.Canny) {
            var c = function () {
                c.q.push(arguments)
            };
            c.q = [], w.Canny = c, "complete" === d.readyState ? l() : w.attachEvent ? w.attachEvent("onload", l) : w
                .addEventListener("load", l, !1)
        }
    }(window, document, "canny-jssdk", "script");
    Canny('initChangelog', {
        appID: '61a9055f38287d52f4e1e4cf',
        ssoToken: '<?= $ssoToken?>',
        position: 'bottom',
        align: 'right',
    });
</script>

<style>
    div.beamer_icon,
    .Canny_Badge {
        background-color: var(--primary) !important;
        border: 0px solid var(--primary) !important;
        width: 11px;
        height: 11px;
        top: 0px !important;
    }

    .selected-theme {

        color: var(--primary);
    }

    .resize{cursor: pointer;}

    .theme-dropdown {
        left: auto !important;
        right: auto !important;
        transform: none !important;
        position: absolute !important;
        margin-top: 0.5rem !important;
    }

</style>
<!-- Header -->

<?php if(stripos($sn, 'staging') !==false){?>

<div class="staging-banner-wrapper position-fixed top-0 start-0 w-100 text-center">
  <div class="staging-banner-line"></div>
  <div class="staging-banner-label">STAGING</div>
</div>

<?php }?>

<header class="d-print-none">
    <nav id="nav-sb" class="navbar navbar-bg navbar-expand-lg fixed-top d-block">
        <div class="container-fluid justify-content-md-between">
            <!-- Left -->
            <div id="nav-left-links" class="d-flex">
                
                <!-- Logo -->
                <div id="logo" class="navbar-brand ">
                    <a href="/v2/wip/wip.php">
                        <span class="logo-dark"><?= getLogoUIwhite()?></span>
                        <span class="logo-light"><?= getLogoUI()?></span>
                    </a>

                </div>

                <!-- Toggler -->
                <button id="toggle" data-mdb-toggle="sidenav" data-mdb-target="#main-sidenav"
                        class="btn shadow-0 p-0 d-block toggler" aria-controls="#main-sidenav" aria-haspopup="true">
                    <i class="fas fa-bars fa-md"></i>
                </button>
                <button onclick="toggleSidebar()" class="btn shadow-0 menutoggler" >
                    <i class="fas fa-bars fa-md"></i>
                </button>
            </div>
            <!-- Right links -->
            <ul id="nav-right-links" class="navbar-nav flex-row  align-items-center justify-content-center">
                <li id="shop-details">
                    <div class="d-flex ms-4 me-4 align-items-center">
                        <div id="shopname" class="d-flex align-items-center">
                            <?php if (!empty($companyLogo)) { ?>
                                <img class="rounded-circle me-1" width="30" height="30"
                                     src="<?= $companyImagesPath . $companyLogo ?>"
                                     alt="<?= $_COOKIE['username'] ?>">
                            <?php } ?>
                            <?php echo $_COOKIE['shopname']; ?> #<?php echo $_COOKIE['shopid']; ?>
                        </div>
                        <div id="shop-details-divider"
                             class="ms-4 me-4 text-secondary nav-item-border d-flex align-items-center">&nbsp;
                        </div>
                        <div class="d-flex align-items-center">
                            <?php
                                    $Global_theme = $_COOKIE['theme'] ?? '0';
                                    $currentTheme = $Global_theme;

                                        if (!empty($employeePhoto)) {
                                            $emp_photo = $companyImagesPath . $employeePhoto;
                                            if (strpos($employeePhoto, "/") !== false){
                                                $emp_photo = "https://customers-ss.s3.amazonaws.com/".$employeePhoto;
                                            }
                                            ?>
                                            <img class="rounded-circle me-1" width="30" height="30"
                                                 src="<?= $emp_photo ?>"
                                                 alt="<?= $_COOKIE['username'] ?>">
                                        <?php } ?>
                                    <?php echo ucwords(strtolower($_COOKIE['username'])); ?>

                        </div>

                    </div>
                </li>

                <li class="nav-item me-3">
                                    <a
                                            class="nav-link dropdown-toggle d-flex align-items-center"
                                            href="#"
                                            id="navbarDropdownMenuLink"
                                            role="button"
                                            data-mdb-toggle="dropdown"
                                            data-mdb-display="static"
                                            aria-expanded="false"
                                    >
                         <i id="activetheme" class="fas fa-fw fa-<?= $currentTheme == '0'?'sun':($currentTheme == '1'?'moon':'palette')?> ms-2"></i>

                                    </a>
                                    <ul class="dropdown-menu theme-dropdown" aria-labelledby="navbarDropdownMenuLink">
                                        <li>
                                            <a class="dropdown-item theme-item <?= $currentTheme == '0' ? 'selected-theme' : '' ?>"
                                               href="#" data-theme="0"><i class="fas fa-sun"></i> Light</a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item theme-item <?= $currentTheme == '1' ? 'selected-theme' : '' ?>"
                                               href="#" data-theme="1"><i class="fas fa-moon"></i> Dark</a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item theme-item <?= $currentTheme == '2' ? 'selected-theme' : '' ?>"
                                               href="#" data-theme="2"><i class="fas fa-palette"></i> Color</a>
                                        </li>
                                    </ul>

                </li>


                <!-- Canny -->
                <li id="canny" class="nav-item me-3">
                    <a class="nav-link text-secondary" href="javascript:void(null)" data-canny-changelog
                       id='cannyButton'>
                        <span class="badge rounded-pill badge-notification bg-danger"></span>
                        <i class="fas fa-rss fa-lg"></i>
                    </a>
                </li>
                <!-- beamer -->
                <li id="beamer" class="nav-item me-3">
                    <a class="nav-link text-secondary" id='beamerButton' href="javascript:void(null)">
                        <span class="badge rounded-pill badge-notification bg-danger"></span>
                        <i class="fas fa-bullhorn fa-lg"></i>
                    </a>
                </li>
                <!-- Logout-->
                <li id="logout" class="nav-item me-3">
                    <a class="nav-link text-secondary" href="https://<?php echo $_SERVER['SERVER_NAME']; ?>/logoff.php"
                       data-mdb-toggle="tooltip" data-mdb-placement="bottom" title="Logout">
                        <i class="fas fa-sign-out-alt fa-lg"></i>
                    </a>
                </li>
            </ul>
        </div>
    </nav>
</header>
<!-- Header -->

<script type="text/javascript">
    function toggleSidebar()
    {
        if ($('#customStylesheet').length) {

            $('#customStylesheet').remove();
            Cookies.remove('minisidebar')
        } else {

            $('head').append('<link rel="stylesheet" type="text/css" href="<?= CSS; ?>/menustyle.css" id="customStylesheet">');
            Cookies.set('minisidebar', 'yes', { expires: 365 });
        }
        //for letting datatables scrollhead know that the window has been resized
        setTimeout(() => $(window).trigger('resize'), 100);
        
    }

    function openFullMode()
    {
        sbconfirm("Full Mode","This will take you back to Full Mode.  Are you sure?",function()
        {
            showLoader()
            $.ajax({
                data: "t=fullmode",
                url: "<?= COMPONENTS_PRIVATE ?>/shared/changemode.php",
                type: "post",
                success: function (r) {
                   setTimeout(function(){location.reload()},2000)
                }
            }); 
        })
    }


    
</script>