<!-- MDB ESSENTIAL -->
<?php get_js_bs5(); ?>
<!-- MDB PLUGINS -->
<?php get_js_plugins_bs5(); ?>

<!-- SIDE NAV -->
<script defer src="<?= SCRIPT; ?>/sidenav.js"></script>

<script type="text/javascript">

    console.log(<?= json_encode($_SERVER['USERNAME']) ?>);
</script>

<?php
// Initialize component variable to prevent undefined variable notice
$component = isset($component) ? $component : '';

$component_scripts = getScriptsComponent($component);
if ($component_scripts) {
    include $component_scripts;
}
?>

<script>
    $(function() {
        // only on touch devices
        var isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        if (!isTouch) return;
        $(document).on('touchend', '[data-mdb-toggle="tooltip"]', function(e) {
            var $trigger = $(this);
            if ($trigger.is('i.fas.fa-circle-info')) return;
            setTimeout(function() {
                $trigger.tooltip('hide');
            }, 1500);
        });
    });
    $(document).ready(function () {
        $(document).on('click', '[data-mdb-toggle="tooltip"]', function () {
         //   $(this).tooltip('hide');
        });

        $('#main-sidenav .sidenav-menu').on('mousewheel DOMMouseScroll', function(e) {
            var scrollTo = null;
            if(e.type === 'mousewheel') {
                scrollTo = (e.originalEvent.wheelDelta * -1);
            }
            else if(e.type === 'DOMMouseScroll') {
                scrollTo = 40 * e.originalEvent.detail;
            }

            if(scrollTo) {
                e.preventDefault();
                $(this).scrollTop(scrollTo + $(this).scrollTop());
            }
        });

        function prevent_keys(e){
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();
            return false;
        }


        function key_select(e){
            var select_dropdown = $(this).find('select');
            $(select_dropdown).removeClass('active');
            var key_code = e.keyCode;
            var key = e.key.toString();
            var current_selection = $(select_dropdown).find("option:selected").text();

            if (current_selection !== '' && typeof current_selection !== 'undefined' && current_selection != null) {
                var current_selection_first = Array.from(current_selection)[0].toString().toLowerCase();
                if ($(select_dropdown).find('option:selected').next().length > 0) {
                    var next_selection = $(select_dropdown).find('option:selected').next().text();
                    var next_selection_first = Array.from(next_selection)[0].toString().toLowerCase();
                    if ((current_selection_first === next_selection_first) && key === next_selection_first) {
                        key_code = 40;
                    }
                }
            }

            var prop_flag = false;
            /*
            Need to override the arrow keys because the default behavior makes it always start from the top
             */
            if (key_code === 38 ){
                //previous arrow key pressed
                $(select_dropdown).find("option:selected").prev().prop("selected", true);
                $(select_dropdown).addClass("active")
                prop_flag = true;

            } else if (key_code === 40){
                $(select_dropdown).find("option:selected").next().prop("selected", true);
                $(select_dropdown).addClass("active")
                prop_flag = true;

            } else if ((key_code >= 65 && key_code <= 90) || (key_code >= 96 && key_code <= 105)) {
                $(select_dropdown).find('option').each(function () {
                    let option_text = $(this).text();
                    let option_index = $(this).val();

                    var first_letter = Array.from(option_text)[0].toString();
                    if (key.toLowerCase() === first_letter.toLowerCase()) {
                        $(select_dropdown).find("option:selected").removeAttr('selected')
                        $(select_dropdown).find("option[value='"+option_index+"']").prop("selected", true);
                        $(select_dropdown).val(option_index).addClass("active").change();
                        return false;
                    }
                    prop_flag = true;
                });
            }
            if (prop_flag){
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
            }
        }

        var select_elements = document.getElementsByClassName("select-wrapper");
        for (var i = 0; i < select_elements.length; i++) {
            var select_element = select_elements.item(i);
            select_element.addEventListener("keyup",prevent_keys, true);
            select_element.addEventListener("keypress",prevent_keys, true);
            select_element.addEventListener("keydown",key_select, true);
        }

        document.querySelectorAll('.sidenav-menu a').forEach(el => {
            el.addEventListener('touchstart', function (e) {
                e.stopPropagation();
                this.click(); // Forces the click event on first touch
            }, { passive: true });
    });
    });

    function setVh() {
        // Calculate 1% of the viewport height
        let vh = window.innerHeight * 0.01;
        // Set the CSS variable --vh to that value
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    // Set the value on initial load
    setVh();

    // Update the value on resize
    window.addEventListener('resize', setVh);
</script>

