<?php
require CONN;
require REDISCONN;
require(PUBLIC_PATH . DS . "API/vendor/autoload.php");

if ($_COOKIE['mode'] != 'full') {
    $globalComponent = getComponent();
    if (!in_array($globalComponent, array('wip-v2', 'ro-v2', 'calendar-v2', 'dispatch-v2', 'empschedule-v2', 'workflow-v2', 'inspection_classic-v2', 'src'))) {
        header("location:/v2/wip/wip.php");
        exit;
    }
}

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$empid = isset($_COOKIE['empid']) ? $_COOKIE['empid'] : '';


$CompanySettings = "shop:$shopid:company";
if ($redis && $redis->exists($CompanySettings)) {
    $fields = [
        'readonly',
        'shopnotice',
        'autoshowta',
        'newpackagetype',
        'alldatausername',
        'masterinterface',
        'companystate',
        'package',
        'status',
        'datestarted',
        'merchantaccount',
        'showstatsonwip',
        'companyemail',
        'logo',
        'contact',
        'ts',
        'newuidate',
    ];
    $redisValues = $redis->hMGet($CompanySettings, $fields);

} else {
    $stmt = "SELECT * FROM company WHERE shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $results = $query->get_result();
        $row = $results->fetch_assoc();
        $query->close();

        if ($row) {
            $redis->hMSet($CompanySettings, $row);
            $redis->expire($CompanySettings, $redis->longTermExpiry);
        }
        $redisValues = [
            'readonly'        => $row['readonly'],
            'shopnotice'      => $row['shopnotice'],
            'autoshowta'      => $row['autoshowta'],
            'newpackagetype'  => $row['newpackagetype'],
            'alldatausername' => $row['alldatausername'],
            'masterinterface' => $row['masterinterface'],
            'companystate'    => $row['companystate'],
            'package'         => $row['package'],
            'status'          => $row['status'],
            'datestarted'     => $row['datestarted'],
            'merchantaccount' => $row['merchantaccount'],
            'showstatsonwip'  => $row['showstatsonwip'],
            'companyemail'    => $row['companyemail'],
            'logo'            => $row['logo'],
            'contact'         => $row['contact'],
            'ts'              => $row['ts'],
            'newuidate'       => $row['newuidate'],
        ];
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
        return;
    }
}

// ✅ Common assignments from Redis values
$readonly        = $redisValues['readonly'];
$shopnotice      = $redisValues['shopnotice'];
$autoshowta      = $redisValues['autoshowta'];
$plan            = $redisValues['newpackagetype'];
$motortype       = $redisValues['alldatausername'];
$masterinterface = $redisValues['masterinterface'];
$shopstate       = $redisValues['companystate'];
$sbpackage       = $redisValues['package'];
$sbstatus        = strtolower($redisValues['status']);
$datestarted     = $redisValues['datestarted'];
$shopmerchant    = $redisValues['merchantaccount'];
$showstatsonwip  = strtoupper($redisValues['showstatsonwip']);
$companyemail    = $redisValues['companyemail'];
$logo            = $redisValues['logo'];
$contact         = $redisValues['contact'];
$lastUpdated     = $redisValues['ts'];
$newuidate       = $redisValues['newuidate'];

if ($empid == "Admin") {
    $ChangeNotice = "YES";
    $accountingaccess = "YES";
    $reportsaccess = "YES";
    $settingsaccess = "YES";
    $empemail = '<EMAIL>';
    $dashboardaccess = 'YES';
    $EditInventory = 'YES';
} else {
    $redisKey = "shop:$shopid:emp:$empid";

    if ($redis && $redis->exists($redisKey)) {
        $fields = [
            'changeshopnotice',
            'accounting',
            'reportaccess',
            'companyaccess',
            'employeeemail',
            'dashboardaccess',
            'editinventory',
            'jobdesc',
            'empname',
            'theme'
        ];

        $redisValues = $redis->hMGet($redisKey, $fields);

        $ChangeNotice     = strtoupper($redisValues['changeshopnotice']);
        $accountingaccess = strtoupper($redisValues['accounting']);
        $reportsaccess    = strtoupper($redisValues['reportaccess']);
        $settingsaccess   = strtoupper($redisValues['companyaccess']);
        $empemail         = $redisValues['employeeemail'];
        $dashboardaccess  = strtoupper($redisValues['dashboardaccess']);
        $EditInventory    = strtoupper($redisValues['editinventory']);
        $jobdesc          = strtolower($redisValues['jobdesc']);
        $empname          = $redisValues['empname'];
        $theme            = $redisValues['theme'];
        $empPohoto        = $redisValues['photo'];
    } else {
        $stmt = "select upper(changeshopnotice), upper(accounting), upper(ReportAccess), upper(CompanyAccess), EmployeeEmail, upper(DashboardAccess), upper(EditInventory), lower(jobdesc), concat(employeefirst,' ',employeelast), theme, photo from employees where id = ? and shopid = ?";

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("is", $empid, $shopid);
            $query->execute();
            $query->bind_result($ChangeNotice, $accountingaccess, $reportsaccess, $settingsaccess, $empemail, $dashboardaccess, $EditInventory, $jobdesc, $empname, $theme, $empPohoto);
            $query->fetch();
            $query->close();

            // Store in Redis for next time
            $redis->hMSet($redisKey, [
                'changeshopnotice' => strtolower($ChangeNotice),
                'accounting'       => strtolower($accountingaccess),
                'reportaccess'     => strtolower($reportsaccess),
                'companyaccess'    => strtolower($settingsaccess),
                'employeeemail'    => $empemail,
                'dashboardaccess'  => strtolower($dashboardaccess),
                'editinventory'    => strtolower($EditInventory),
                'jobdesc'          => $jobdesc,
                'empname'          => $empname,
                'theme'            => $theme,
                'photo'            => $empPohoto
            ]);
            $redis->expire($redisKey, $redis->shortTermExpiry ?? 3600); // Optional expiry
        } else {
            echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }
    }
}

$rocount = 11;
$showstatsonwip = 'NO';
$matco = $_COOKIE['matco'] ?? 'no';

$userfirst = $userlast = '';
if (isset($_COOKIE['username'])) {
    $arr = explode(' ', $_COOKIE['username']);
    $userfirst = $arr[0];
    $userlast = (isset($arr[1]) ? $arr[1] : '') . ' (' . $_COOKIE['shopname'] . ')';
}

if ($sbpackage == 'Trial' && $sbstatus == 'active') {
    $filter_type = 'Trial';
    $diff = strtotime(date('Y-m-d')) - strtotime($datestarted);
    $trialdays = 30 - abs(round($diff / 86400));
} elseif (!empty($plan) && $plan != 'none')
    $filter_type = $plan;
else
    $filter_type = 'All';

if (in_array($shopid, array('6062', '8102', '13846', '7550', '11489', '14627', '12386', '3263', '17360', '13931', '14401', '16665', '17032', '11198', '15226', '3741', '15788', '3439', '17684', '8395', '16200', '12869', '14642', '10242', '18101', '17620', '17910', '17302')))
    $filter_type = 'prodemand;' . $filter_type;

if (in_array($shopmerchant, array('360', 'tnp', 'authorize.net', 'cardknox')))
    $filter_type .= ";" . $shopmerchant;
else
    $filter_type .= ";nopayments";

if ($shopmerchant != '360') $filter_type .= ";non360";

if ($sbpackage == 'Paid' && $sbstatus == 'active' && $plan != "platinum" && $plan != "premier" && $plan != "premier plus") {
    $stmt = "select id from companyadds where shopid = '$shopid' and `name` = 'Boss Board'";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $num_roid_rows = $query->num_rows;
        if ($num_roid_rows < 1)
            $filter_type .= ";BossBoard Inactive";
    }
}

if (($plan == 'none' || empty($plan)) && $sbpackage != 'Trial')
    $filter_type .= ";Usage";

if (in_array($shopid, $dvibeta))
    $filter_type .= ";dvibeta";

if (in_array($shopid, $vipshops))
    $filter_type .= ";vip";

if ($shopid == 'demo')
    $filter_type .= ";demo";

if ($matco == 'yes')
    $filter_type .= ";matco";
else
    $filter_type .= ";nonmatco";

if (in_array($shopid, $betavin))
    $filter_type .= ";beta-vin";

if (!empty($newuidate))
    $filter_type .= ";" . $newuidate;

$PrivateKey = '447f78ce-9068-4e0d-3a92-0551a6ebc077';

if ($empid != "Admin") {
    $userData = [
        'email' => (!empty($empemail) ? $empemail : $companyemail),
        'id' => $empid,
        'name' => $_COOKIE['usr'] . ' - ' . $shopid
    ];
} else {
    $exparr = explode(',', $_COOKIE['admindata']);
    $userData = [
        'email' => $exparr[3] ?? '<EMAIL>',
        'id' => $exparr[0] ?? '0',
        'name' => (isset($exparr[1]) ? $exparr[1] . ' ' . $exparr[2] : 'Admin') . ' - ' . $shopid
    ];
}
$ssoToken = \Firebase\JWT\JWT::encode($userData, $PrivateKey, 'HS256');
$cannyurl = "https://canny.io/api/redirects/sso?companyID=61a9055f38287d52f4e1e4cf&ssoToken=" . $ssoToken . "&redirect=https://feedback.shopbosspro.com";

$curr_page = basename(parse_url($_SERVER["REQUEST_URI"], PHP_URL_PATH));

// Initialize component variable to prevent undefined variable notice
$component = isset($component) ? $component : '';

$component_rules = getRulesComponent($component);
if ($component_rules) {
    include $component_rules;
}

$companyImagesPath = UPLOAD_URL . "/" . $_COOKIE['shopid'] . "/";

$settingsKey = "shop:$shopid:settings";
$canSeePhotos = 'yes';

if ($redis && $redis->exists($settingsKey)) {
    $canSeePhotos = $redis->hGet($settingsKey, 'showphotos') ?: 'yes';
} else {
    $stmt = "SELECT * FROM settings WHERE shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $result = $query->get_result();
        if ($result && $row = $result->fetch_assoc()) {
            $canSeePhotos = $row['showphotos'] ?? 'yes';
            if ($redis) {
                $redis->hMSet($settingsKey, $row);
                $redis->expire($settingsKey, $redis->longTermExpiry);
            }
        }
        $query->close();
    }
}

if ($canSeePhotos === 'yes') {
    if (empty($logo)) {
        $stmt = "select logo from company where shopid = ? limit 1";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $_COOKIE['shopid']);
            $query->execute();
            $query->store_result();
            $query->bind_result($companyLogo);
            $query->fetch();
            $query->close();
        }
    } else {
        $companyLogo = $logo;
    }

    $employeePhoto = $empPohoto;
}

// getting shop theme preference

// $theme = 'default';

// $stmt = "SELECT theme FROM settings WHERE shopid = ?";

// if ($query = $conn->prepare($stmt)) {
//     $query->bind_param("s", $shopid);
//     $query->execute();
//     $query->bind_result($theme);
//     $query->fetch();
//     $query->close();
// } else {
//     echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
// }
//     $conn->close();
//     console_log($theme, $title = Theme);


$statesToAbbr = array(
    'Alaska' => 'AK',
    'Alabama' => 'AL',
    'Arkansas' => 'AR',
    'Arizona' => 'AZ',
    'California' => 'CA',
    'Colorado' => 'CO',
    'Connecticut' => 'CT',
    'Delaware' => 'DE',
    'Florida' => 'FL',
    'Georgia' => 'GA',
    'Hawaii' => 'HI',
    'Idaho' => 'ID',
    'Illinois' => 'IL',
    'Indiana' => 'IN',
    'Iowa' => 'IA',
    'Kentucky' => 'KY',
    'Louisiana' => 'LA',
    'Maine' => 'ME',
    'Maryland' => 'MD',
    'Massachusetts' => 'MA',
    'Michigan' => 'MI',
    'Minnesota' => 'MN',
    'Mississippi' => 'MS',
    'Missouri' => 'MO',
    'Montana' => 'MT',
    'Nebraska' => 'NE',
    'Nevada' => 'NV',
    'New Hampshire' => 'NH',
    'New Jersey' => 'NJ',
    'New Mexico' => 'NM',
    'New York' => 'NY',
    'North Carolina' => 'NC',
    'North Dakota' => 'ND',
    'Ohio' => 'OH',
    'Oklahoma' => 'OK',
    'Oregon' => 'OR',
    'Pennsylvania' => 'PA',
    'Rhode Island' => 'RI',
    'South Carolina' => 'SC',
    'South Dakota' => 'SD',
    'Tennessee' => 'TX',
    'Texas' => 'AK',
    'Utah' => 'UT',
    'Vermont' => 'VT',
    'Virginia' => 'VA',
    'Washington' => 'WA',
    'West Virginia' => 'WV',
    'Wisconsin' => 'WI',
    'Wyoming' => 'WY',
);

$shopStateAbr = (!empty($statesToAbbr[ucfirst(strtolower($shopstate))])) ? $statesToAbbr[ucfirst(strtolower($shopstate))] : $shopstate;
?>
