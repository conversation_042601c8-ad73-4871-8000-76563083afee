<!DOCTYPE html>

<?php
$Global_themecss = array(CSS."/light.css",CSS."/dark.css",CSS."/color.css"); // 0 for light mode, 1 for dark mode, 2 for color mode

$Global_themeicons = array("fa-sun","fa-moon","fa-palette");

$datatables_themecss = array(
    CSS . "/light-datatables.css",
    CSS . "/dark-datatables.css",
    CSS . "/color-datatables.css"
);

$Global_theme = $_COOKIE['theme'] ?? '0';
?>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
		<meta http-equiv="x-ua-compatible" content="ie=edge" />
		<title><?php getPageTitle(); ?></title>
		<!-- SB icon -->
		<link rel="shortcut icon" href="https://shopboss.net/wp-content/uploads/2022/04/icon2-150x150.png" type="image/x-icon" />
		<!-- Font Awesome -->
        <link rel="stylesheet" href="https://<?= $_SERVER['SERVER_NAME'] ?>/src/public/fontawesome/css/all.min.css" />
		<!-- MDB ESSENTIAL -->
		<?php get_css_bs5(); ?>
		<!-- MDB PLUGINS -->
		<?php get_css_plugins_bs5(); ?>
		<!-- Datatables -->
<script type="text/javascript" src="https://code.jquery.com/jquery-3.5.1.js"></script>
        <link rel="stylesheet" href="<?= CSS; ?>/datatables.css" type="text/css" media="screen">
        <link id="datatablesstylesheet" rel="stylesheet" href="<?= $datatables_themecss[$Global_theme] ?>" type="text/css" media="screen">
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker-mdb/bootstrap-datetimepicker.css">

        <script src="//cdnjs.cloudflare.com/ajax/libs/moment.js/2.11.2/moment.min.js"></script>
        <script src="<?= SCRIPT ?>/plugins/bootstrap-datetimepicker-mdb/bootstrap-datetimepicker.js"></script>
        <script type="text/javascript" src="https://cdn.datatables.net/v/bs5/dt-1.13.2/cr-1.6.1/fh-3.3.1/r-2.4.0/rr-1.3.2/sr-1.2.1/datatables.min.js"></script>
        <script src="https://cdn.datatables.net/plug-ins/1.10.12/sorting/datetime-moment.js"></script>

        <script src="<?= SCRIPT; ?>/core/js.cookie.min.js"></script>

        <script defer src="<?= SCRIPT ?>/emodal_mdb.js"></script>

        <!-- Custom styles -->
        <link rel="stylesheet" href="<?= CSS; ?>/global.min.css" type="text/css" media="screen">

        <!-- Themes -->
        <link id="theme-stylesheet" rel="stylesheet" href="<?= $Global_themecss[$Global_theme] ?>" type="text/css" media="screen">

        <?php if(isset($_COOKIE['minisidebar']) && $_COOKIE['minisidebar'] == 'yes'){?>
		    <link rel="stylesheet" type="text/css" href="<?= CSS; ?>/menustyle.css" id="customStylesheet">
		<?php }?>


<script>
const themeArray = <?php echo json_encode($Global_themecss); ?>;
const datatablethemeArray = <?php echo json_encode($datatables_themecss); ?>;

$(document).ready(function () {

    let theme = parseInt('0'); // 0 for light mode, 1 for dark mode, 2 for color mode
    if (typeof $.fn.dataTable !== "undefined") {
        $.fn.dataTable.moment('MM/DD/YYYY');
        $.fn.dataTable.moment('M/DD/YY');
        $.fn.dataTable.moment('M/D/YY');
        $.fn.dataTable.moment('MM/D/YY');
        $.fn.dataTable.moment('MM/DD/YY');
        $.fn.dataTable.moment('M/DD/YY h:mma');
        $.fn.dataTable.moment('MM/DD/YY h:mm:ss a');
        $.fn.dataTable.moment('M/D/YY h:mm:ss a');
        $.fn.dataTable.moment('MM/DD/YYYY h:mm:ss a');
        $.fn.dataTable.moment('M/D/YYYY h:mm:ss a');
    }
    // Function to set the theme
    function setTheme(newTheme, reloadPage) {
        theme = newTheme;

        showLoader();

        $.ajax({
            url: "<?= COMPONENTS_PRIVATE ?>/shared/savetheme.php",
            type: "post",
            data: {
                theme: theme
            },
            success: function (r) {

                $('#theme-stylesheet').attr('href', themeArray[theme] + '?v=' + new Date().getTime());
                $('#datatablesstylesheet').attr('href', datatablethemeArray[theme] + '?v=' + new Date().getTime());

	            var iframe = $('iframe');
				if (iframe.length > 0) {
				    iframe.contents().find('#theme-stylesheet').attr('href', themeArray[theme] + '?v=' + new Date().getTime());
				    iframe.contents().find('#datatablesstylesheet').attr('href', datatablethemeArray[theme] + '?v=' + new Date().getTime());
				}

                if(theme=='1')
                {
                	$('.logo-dark').show();
                	$('.logo-light').hide();
                }
                else
                {
                	$('.logo-light').show();
                	$('.logo-dark').hide();
                }

            	$('#activetheme').removeClass().addClass('fas fa-fw fa-'+(theme == '0'?'sun':(theme == '1'?'moon':'palette'))+' ms-2')

                hideLoader();
            }
        });
    }

    $('.theme-item').click(function (e) {
        e.preventDefault();
        setTheme($(this).data('theme'));
    });

});


</script>



		<script type="text/javascript">
		function showLoader()
		{
			var loaderContainer = $("<div/>", {
			  id: "loader-container",
			  class: "loadercontainer"
			}).appendTo("body");

			loaderContainer.css({
			    "position": "fixed",
			    "width": "200px",
			    "height": "200px",
			    "top": "50%",
			    "left": "50%",
			    "transform": "translate(-50%, -50%)"
			 });

			var loader = $("<div/>", {
			  "class": "loader d-flex justify-content-center",
			  role: "status"
			}).appendTo(loaderContainer);

			for (var i = 0; i < 3; i++) {
			  $("<div/>", { "class": "loader-dot" }).appendTo(loader);
			}

		}

		function hideLoader()
		{
		  $('.loadercontainer').remove()
		}

		function sbalert(msg)
		{
			var popupTemplate = '<div id="sbalertmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">' +
			   ' <div class="modal-dialog modal-sm">' +
			     '   <div class="modal-content p-4">' +
			        '    <div class="modal-header ps-1 pe-1">' +
			         '       <h5 class="modal-title" id="sbalertmodalLabel">Alert</h5>' +
			           '     <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>' +
			         '   </div>' +
			         '   <div class="modal-body mt-3 mb-3 text-center">' + msg +
			        '   </div>' +
			       ' </div>' +
			    '</div>' +
			'</div>';

			$(popupTemplate).modal('show')

		}

		function sbconfirm(title,msg,onConfirm=null,ondecline=null) {
		  var popupTemplate = '<div id="sbconfirmmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">' +
		    '<div class="modal-dialog">' +
		    '<div class="modal-content p-4">' +
		    '<div class="modal-header ps-1 pe-1">' +
		    '<h5 class="modal-title" id="sbconfirmmodalLabel">' + title + '</h5>' +
		    '<button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>' +
		    '</div>' +
		    '<div class="modal-body mt-3">' + msg +
		    '</div>' +
		    '<div class="modal-footer">' +
		    '<div class="col d-flex justify-content-center align-items-center pt-2">' +
		    '<button type="button" class="btn btn-primary">Confirm</button>' +
		    '</div>' +
		    '</div>' +
		    '</div>' +
		    '</div>' +
		    '</div>';

		  var $popup = $(popupTemplate);
		  $popup.find('.btn-primary').click(function() {
		    if (onConfirm) {
		      onConfirm();
		    }
		    $popup.modal('hide');
		  });
		  $popup.find('.btn-close').click(function() {
		    if (ondecline) {
		      ondecline();
		    }
		    $popup.modal('hide');
		  });
		  $popup.modal('show');
		}

		</script>

<script>
    !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.async=!0,p.src=s.api_host+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
    posthog.init('phc_liE0sBy7QNC9eaC6g1VeEEfO7wSrxbaZI3Mm43mQqIv',{api_host:'https://app.posthog.com'})

    // Function to get a cookie value by name
    function getCookie(name) {
        const value = "; " + document.cookie;
        const parts = value.split("; " + name + "=");
        if (parts.length == 2) return decodeURIComponent(parts.pop().split(";").shift());
    }

    const userId = getCookie("username"); // using username as unique user ID

    if (userId) {
        const shopId = getCookie("shopid");
        const username = getCookie("username");
        const package = getCookie("plan");
        const shopName = getCookie("shopname");

        posthog.identify(userId, {
            'Shop ID': shopId,
            'User Name': username,
            'Package': package,
            'Shop Name': shopName
        });
    }
</script>




		<?php
		// Initialize component variable to prevent undefined variable notice
		$component = isset($component) ? $component : '';

		$component_head = getHeadComponent($component);
		if ($component_head){
			include $component_head;
		}
		?>
		<?php
		$component_style = getStyleComponent($component);
		if ($component_style){
			include $component_style;
		}
		?>

		<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
<style>
    .logo-dark{
        display:<?= $Global_theme !='1'?'none':''?>
    }
    .logo-light{
        display:<?= $Global_theme =='1'?'none':''?>
    }
</style>
  	</head>