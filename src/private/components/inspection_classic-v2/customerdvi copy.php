<!DOCTYPE html>
<html>
<?php
require CONNWOSHOPID;

$shopid = $_GET['shopid'];
$roid = $_GET['roid'];
$iname = strtoupper($_GET['iname']);

// get relevant info from ro
$stmt = "select customer,vehinfo,fleetno from repairorders where shopid = '$shopid' and roid = $roid";
if ($query = $conn->prepare($stmt)){

	$query->execute();
	$query->bind_result($cust,$veh,$fleetno);
	$query->fetch();
	$query->close();

}

$stmt = "select companyname,companyaddress,concat(companycity,', ',companystate,'. ',companyzip) csz,companyphone,logo from company where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)){

	$query->execute();
	$query->bind_result($coname,$caddr,$ccsz,$cph,$logo);
	$query->fetch();
	$query->close();

}


?>
<head>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
<title>Shop Boss Enterprise</title>
<script type="text/javascript" src="../assets/js/jquery.js"></script>
<script type="text/javascript" src="../assets/js/bootstrap.min.js"></script>
<script src="../../sbp/php/js/emodal.js?v=6.1"></script>
<link rel="stylesheet" href="../assets/css/bootstrap.min.css"/>
<link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">
<meta name="viewport" content="width=device-width, initial-scale=1" />
<style type="text/css">
.style1 {
	color: #FFFFFF;
	background-color:#368B12;
}
.style2 {
	color: #FFFFFF;
	background-color: #368B12;
	text-align: center;
}
.auto-style4 {
	color: #FFFFFF;
	background-color: #368B12;
	text-align: left;
}
.auto-style8 {
	color: #FFFFFF;
	background-color: #368B12;
	text-align: center;
	font-weight: bold;
}
.auto-style9 {
	color: #368B12;
}

.zoom {
	display:inline-block;
	position: relative;
}

/* magnifying glass icon */
.zoom:after {
	content:'';
	display:block; 
	width:33px; 
	height:33px; 
	position:absolute; 
	top:0;
	right:0;
	/*background:url(icon.png);*/
}

.zoom img {
	display: block;
}

.zoom img::selection { background-color: transparent; }

#ex2 img:hover { cursor: url(grab.cur), default; }
#ex2 img:active { cursor: url(grabbed.cur), default; }

</style>
<script type="text/javascript">
(function($) {
var sR = {
    defaults: {
        slideSpeed: 400,
        easing: false,
        callback: false     
    },
    thisCallArgs: {
        slideSpeed: 400,
        easing: false,
        callback: false
    },
    methods: {
        up: function (arg1,arg2,arg3) {
            if(typeof arg1 == 'object') {
                for(p in arg1) {
                    sR.thisCallArgs.eval(p) = arg1[p];
                }
            }else if(typeof arg1 != 'undefined' && (typeof arg1 == 'number' || arg1 == 'slow' || arg1 == 'fast')) {
                sR.thisCallArgs.slideSpeed = arg1;
            }else{
                sR.thisCallArgs.slideSpeed = sR.defaults.slideSpeed;
            }

            if(typeof arg2 == 'string'){
                sR.thisCallArgs.easing = arg2;
            }else if(typeof arg2 == 'function'){
                sR.thisCallArgs.callback = arg2;
            }else if(typeof arg2 == 'undefined') {
                sR.thisCallArgs.easing = sR.defaults.easing;    
            }
            if(typeof arg3 == 'function') {
                sR.thisCallArgs.callback = arg3;
            }else if(typeof arg3 == 'undefined' && typeof arg2 != 'function'){
                sR.thisCallArgs.callback = sR.defaults.callback;    
            }
            var $cells = $(this).find('td');
            $cells.wrapInner('<div class="slideRowUp" />');
            var currentPadding = $cells.css('padding');
            $cellContentWrappers = $(this).find('.slideRowUp');
            $cellContentWrappers.slideUp(sR.thisCallArgs.slideSpeed,sR.thisCallArgs.easing).parent().animate({
            paddingTop: '0px',
            paddingBottom: '0px'},{
            complete: function () {
                $(this).children('.slideRowUp').replaceWith($(this).children('.slideRowUp').contents());
                $(this).parent().css({'display':'none'});
                $(this).css({'padding': currentPadding});
            }});
            var wait = setInterval(function () {
                if($cellContentWrappers.is(':animated') === false) {
                    clearInterval(wait);
                    if(typeof sR.thisCallArgs.callback == 'function') {
                        sR.thisCallArgs.callback.call(this);
                    }
                }
            }, 100);                                                                                                    
            return $(this);
        },
        down: function (arg1,arg2,arg3) {
            if(typeof arg1 == 'object') {
                for(p in arg1) {
                    sR.thisCallArgs.eval(p) = arg1[p];
                }
            }else if(typeof arg1 != 'undefined' && (typeof arg1 == 'number' || arg1 == 'slow' || arg1 == 'fast')) {
                sR.thisCallArgs.slideSpeed = arg1;
            }else{
                sR.thisCallArgs.slideSpeed = sR.defaults.slideSpeed;
            }

            if(typeof arg2 == 'string'){
                sR.thisCallArgs.easing = arg2;
            }else if(typeof arg2 == 'function'){
                sR.thisCallArgs.callback = arg2;
            }else if(typeof arg2 == 'undefined') {
                sR.thisCallArgs.easing = sR.defaults.easing;    
            }
            if(typeof arg3 == 'function') {
                sR.thisCallArgs.callback = arg3;
            }else if(typeof arg3 == 'undefined' && typeof arg2 != 'function'){
                sR.thisCallArgs.callback = sR.defaults.callback;    
            }
            var $cells = $(this).find('td');
            $cells.wrapInner('<div class="slideRowDown" style="display:none;" />');
            $cellContentWrappers = $cells.find('.slideRowDown');
            $(this).show();
            $cellContentWrappers.slideDown(sR.thisCallArgs.slideSpeed, sR.thisCallArgs.easing, function() { $(this).replaceWith( $(this).contents()); });

            var wait = setInterval(function () {
                if($cellContentWrappers.is(':animated') === false) {
                    clearInterval(wait);
                    if(typeof sR.thisCallArgs.callback == 'function') {
                        sR.thisCallArgs.callback.call(this);
                    }
                }
            }, 100);
            return $(this);
        }
    }
};

$.fn.slideRow = function(method,arg1,arg2,arg3) {
    if(typeof method != 'undefined') {
        if(sR.methods[method]) {
            return sR.methods[method].apply(this, Array.prototype.slice.call(arguments,1));
        }
    }
};
})(jQuery);

function showHideRow(rowid){

	if($('.'+rowid).css("display") == "none"){
		$('.'+rowid).fadeIn()
	}else{
		$('.'+rowid).fadeOut()
	}

}

function showImages(id){

	//console.log("getcustomerdviphotos.asp?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&id="+id)
	
	$('#showdetails').modal('show')
	$('#picbody').attr("src","getcustomerphotosdvi.phpshopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&id="+id)
	
	/*$.ajax({
		
		url: ,
		data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&id="+id,
		success: function(results){
			//console.log(results)
			rhtml = "<h2>Photos shown below.  Click larger photos to zoom in:</h2>"+results
			//console.log(rhtml)
			$('#detailbody').html(rhtml)
			
			window.scroll(0,0)
		}
	});*/
}

function showNotes(v){

	$('#showDetails').html("<img onclick='closeWin()' style='float:right;cursor:pointer' alt='' height='25' src='../../sbp/newimages/Close-icon.png' width='25' /><h2>TECHNICIAN NOTES:</h2><br/><h3>"+v+"</h3>")
	$('#showDetails').fadeIn()


}

function closeWin(){

	$('#showDetails').fadeOut()
	$('#showDetails').html("")

}

function closeSelf(){

	parent.closeInspection()
}

function imgSize(id){

	currh = $('#'+id).css("max-height");
	if (currh == "40px"){
		$('#'+id).css("max-height","");
	}
	
	if (currh == "none"){
		$('#'+id).css("max-height","40px");
	}

}
function showVideo(videocode){

	eModal.iframe({
		title:'Video',
		url: videocode,
		size: eModal.size.xl,
		buttons: [

			{text: 'Close', style: 'warning', close:true}
    	]

	});	  

}
</script>
</head>

<body style="width:99%">


<div style="width:100%;height:100%;display:none;position:absolute;left:0px;top:0px;padding:5px;border:1px black solid;border-radius:10px;background-color:white" id="imagedisplay"></div>
<div style="padding:10px;" class="row">
	<button class="btn btn-danger" onclick="parent.closePreview()" style='float:right;cursor:pointer;'>Close</button>
	<div class="col-sm-2">
		<h3>This Inspection created especially for <?php echo $cust; ?> for a <?php echo $veh; ?></h3>

		<br/><br/>
		<h3>Prepared By:</h3>
		<?php
		if (strlen($logo) > 0){
			echo "<img style='max-width:200px;max-height:200px' src='../../sbp/upload/$shopid/$logo'>";
		;
		}
		?>
		<h4><?php echo $coname; ?><br/><?php echo $caddr; ?><br/><?php echo $ccsz; ?><br/><?php echo $cph; ?></h4>
	</div>
	<div class="col-sm-10">
		<h3><?php echo $iname; ?> INSPECTION</h3>
		<p>All categories that need attention are automatically expanded, but you can expand any category 
		by clicking the (+) sign next to each category</p>

		<div class="table-responsive">
		  <table class="table">
			<tr>
				<td class="auto-style4" rowspan="2">
				<strong>&nbsp;&nbsp; Item&nbsp;</strong></td>
				<td class="style1" colspan="5">
				<strong>Condition (click a box to mark)</strong></td>
			</tr>
			<tr>
				<td  class="auto-style8">Excellent</td>
				<td  class="auto-style8"><span class="auto-style9">__</span>Good<span class="auto-style9">__</span></td>
				<td  class="auto-style8"><span class="auto-style9">__</span>Fair<span class="auto-style9">__</span></td>
				<td class="auto-style8"><span class="auto-style9">__</span>Poor<span class="auto-style9">__</span></td>
				<td  class="auto-style8"><span class="auto-style9">__</span>Bad<span class="auto-style9">__</span></td>
			</tr>
		
		  	<?php
		  	$rowclasslist = "";
		  	$visiblecategorylist = "";
		  	$stmt = "select distinct category from roinspection where inspectionname = '$iname' and shopid = '$shopid' and roid = $roid order by displayorder";
		  	if ($query = $conn->prepare($stmt)){
		  		$query->execute();
		  		$r = $query->get_result();
		  		while ($rs = $r->fetch_assoc()){
		  			$cat = strtoupper($rs['category']);
		  			$catus = str_replace("/","_",str_replace(" ","_",$cat));
		  	?>
		  	
			<tr style="background-color:#369;color:white;font-weight:bold">
				<td onclick="showHideRow('<?php echo $catus; ?>')" style="height: 20px;text-align:left;cursor:pointer" class="auto-style1" colspan="6" >+ CATEGORY: <?php echo $cat; ?></td>
			</tr>
		  	<?php
		  			// now get the items in this category
		  			$istmt = "select item,rank,comments,id from roinspection where inspectionname = '$iname' and shopid = '$shopid' and roid = $roid and category = '$cat'";
		  			if ($iquery = $conn->prepare($istmt)){
		  			
		  				$iquery->execute();
		  				$ir = $iquery->get_result();
		  				while ($irs = $ir->fetch_assoc()){
		  				
							$font5 = "black"; $font4 = "black"; $font3 = "black"; $font2 = "black"; $font1 = "black";
							if ($irs["rank"] == "5"){ 
								$back5 = "#060";
								$back5msg = "Excellent";
								$font5 = "white";
							}else{ 
								$back5 = "white";
								$back5msg = "";
								$font5 = "black";
							}
							if ($irs["rank"] == "4"){
								$back4 = "#cfc" ;
								$back4msg = "Good";
								$font4 = "black";
							}else{ 
								$back4 = "white";
								$back4msg = "";
								$font4 = "black";
							}
							if ($irs["rank"] == "3"){
								$back3 = "#ff9" ;
								$back3msg = "Fair";
								$font3 = "black";
							}else{ 
								$back3 = "white";
								$back3msg = "";
								$font3 = "black";
							}
							if ($irs["rank"] == "2"){
								$back2 = "#f60"; 
								$back2msg = "Poor";
								$font2 = "black";
							}else{ 
								$back2 = "white";
								$back2msg = "";
								$font2 = "black";
							}
							if ($irs["rank"] == "1"){
								$back1 = "#f00"; 
								$back1msg = "Bad";
								$font1 = "white";
							}else{ 
								$back1 = "white";
								$back1msg = "";
								$font1 = "black";
							}
							$rowclasslist .= $catus.",";
							
							if (strlen($irs['comments']) > 0){
								$itemdisplay = strtoupper($irs['item'])."<br><img src='../assets/img/greenarrow.jpg' style='max-height:20px'> <b>TECH NOTES:</b>".strtoupper($irs['comments']);
							}else{
								$itemdisplay = strtoupper($irs['item']);
							}

		
		  	?>
			<tr style="display:none" class="<?php echo $catus; ?>" >
				<td style="color:black;text-align:left;height:25px;border-bottom:1px black solid;width:90%" valign="middle" >
				<span class="style5" ><?php echo $itemdisplay; ?></span>&nbsp;
				<?php
				
							// check for images
							$c = 1;
							$folpath = "d:/upload/$shopid/$roid/".$irs['id'];
							$imgstmt = "select picname,id,inspitemid from repairorderpics where shopid = '$shopid' and roid = $roid and `type` = 'img' and inspitemid = ".$irs['id'];
							if ($imgquery = $conn->prepare($imgstmt)){
								$imgquery->execute();
								$im = $imgquery->get_result();
								while ($ims = $im->fetch_assoc()){
									$imgpath = $folpath."/".$ims['picname'];
									//echo $imgpath."<BR>";
									if (file_exists($imgpath)){
										echo "<img onclick='imgSize(".$ims['id'].");' id='".$ims['id']."' style='max-height:40px;cursor:pointer' src='../../sbp/upload/$shopid/$roid/".$ims['inspitemid']."/" . $ims["picname"] . "'></div>";
									}
									$c += 1;
								}
							}
							//if ($shopid == "6062")
							{
								$vstmt = "select picname,id,inspitemid from repairorderpics where shopid = '$shopid' and roid = $roid and `type` = 'vid' and inspitemid = ".$irs['id'];
								if ($vquery = $conn->prepare($vstmt)){
									$vquery->execute();
									$vr = $vquery->get_result();
									while ($vrs = $vr->fetch_assoc()){
										preg_match( "/src='([^']*)'/i",$vrs['picname'],$array);
										if(isset($array[1]))
										$vpath=$array[1];
									    else
									    $vpath='';
										echo "<span onclick=\"showVideo('$vpath')\" class='btn btn-success btn-sm'><i class='fa fa-video-camera' aria-hidden='true'></i> Watch Video</span>";
										$c += 1;
									}
								}
							}
				
				?>
				</td>
				<td id="5*<?php echo $irs['id']; ?>" style="font-weight:bold;color:<?php echo $font5; ?>;text-align:center;border:1px black solid;background-color:<?php echo $back5; ?>; height: 20px;">
				<?php echo $back5msg; ?></td>
				<td id="4*<?php echo $irs['id']; ?>" style="font-weight:bold;color:<?php echo $font5; ?>;text-align:center;border:1px black solid;background-color:<?php echo $back4; ?>; height: 20px;">
				<?php echo $back4msg; ?></td>
				<td id="3*<?php echo $irs['id']; ?>" style="font-weight:bold;color:<?php echo $font5; ?>;text-align:center;border:1px black solid;background-color:<?php echo $back3; ?>; height: 20px;">
				<?php echo $back3msg; ?></td>
				<td id="2*<?php echo $irs['id']; ?>" style="font-weight:bold;color:<?php echo $font5; ?>;text-align:center;border:1px black solid;background-color:<?php echo $back2; ?>; height: 20px;">
				<?php echo $back2msg; ?></td>
				<td id="1*<?php echo $irs['id']; ?>" style="font-weight:bold;color:<?php echo $font5; ?>text-align:center;border:1px black solid;background-color:<?php echo $back1; ?>; height: 20px;">
				<?php echo $back1msg; ?></td><input type="hidden" id="rank<?php echo $irs['id']; ?>" name="rank<?php echo $irs['id']; ?>" value="<?php echo $irs['rank']; ?>"/>
			</tr>
		<?php
							if ($irs['rank'] != "5" || strlen($irs['comments']) > 0){
								$visiblecategorylist .= $catus . ",";
							}

						}
					}
				}
			}
		?>
		  </table>
		</div>
	</div>
</div>
<div id="vidbox"></div>

<div id="showdetails" class="modal fade" role="dialog">
  <div class="modal-dialog">

    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">Pictures</h4>
      </div>
      <div id="detailbody" class="modal-body">
        <iframe id="picbody" style="width:100%;height:100%;border:0px"></iframe>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div>
    </div>

  </div>
</div>
<?php
if (strpos($visiblecategorylist,",") > 0){
?>
<script type="text/javascript">

$(document).ready(function(){
	
	<?php
	
	$vclar = explode(",",$visiblecategorylist);
	foreach ($vclar as $v){
		
		if ($v != ''){
			echo "$('.$v').show()\r\n";
		}
		
	}
	
	?>
	

});
</script>
<?php
}
?>

<?php
mysqli_close($conn);
?>
</body>

</html>
