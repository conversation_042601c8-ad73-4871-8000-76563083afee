<!DOCTYPE html>
<?php
require CONN;
$shopid = $_COOKIE['shopid'];
$roid = $_GET['roid'];

// get the customerid from ro and shopid
$stmt = "select customerid from repairorders where shopid = ? and roid = ?";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("si",$shopid,$roid);
	$query->execute();
	$query->bind_result($cid);
	$query->fetch();
	$query->close();
}

// check for AS
$stmt = "select asid from autoserveshop where shopid = ?";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("s",$shopid);
	$query->execute();
	$query->bind_result($asid);
	$query->fetch();
	$query->close();
}



?>
<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!--> <html class="no-focus"> <!--<![endif]-->
    <head>
        <meta charset="utf-8">

        <title><?= getPageTitle() ?></title>
		
        <meta name="robots" content="noindex, nofollow">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
		<link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon'/ >
        <!-- Icons -->
        <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

        <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
        <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
        <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
        <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
        <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
        <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
        <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
        <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
        <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
        <!-- END Icons -->

        <!-- Stylesheets -->
        <!-- Web fonts -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

        <!-- Page JS Plugins CSS -->
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css">

        <!-- Bootstrap and OneUI CSS framework -->
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
        <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
        <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">

        <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
        <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
        <!-- END Stylesheets -->
        <style>
		.btn-yellow{
			background-color:#FFFF66;
			color:black;
		}
		.btn-ltgreen{
			background-color:#B3FFB3;
			color:black;
		}
		.dropdown-menu > li > a:hover, .dropdown-menu > li > a:focus {
		        background-color:hsl(150,65%,50%);color:white
		}
         </style>
    </head>
    <body style="background-color:white">
        <!-- Page Container -->
        <!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->

            <!-- Main Container -->
            <main class="container-fluid" style="background-color:white">
        		<div style="padding:20px;text-align:center;width:550px;margin:auto" class="row">
        			<div style="background-color:#423E46">
						<img alt="" height="90" src="<?= SCRIPT ?>/default-logo.png" width="356"></div>
        			<h3>
					<br>Create AutoServe1 Inspection</h3>
					<p>Click a button to CREATE that Inspection</p>
				</div>
			
				<?php
					$ch = curl_init();
					$url = "https://app.autoserve1.com/v2/api/store/$asid/inspection/?page=1&size=5000";
					
					curl_setopt($ch, CURLOPT_URL, $url);
					curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
					curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
					curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
					curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);	
									
					$headers = array();
					$headers[] = 'Content-Type: application/json';
					$headers[] = 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6InNob3Bib3NzIiwidHlwZSI6ImludGVncmF0b3IiLCJuYW1lIjoiICIsImlkIjoiZDAzN2E3MzktNjQxMi00NDFhLWFkZjQtNDZjYTVkNWVhZTg4IiwibGFuZ3VhZ2UiOiJlbiIsImlhdCI6MTU2OTk2MjIzMX0.ffxN-Ty-CoMxWhM-iAaWvXRwztSzr-ceQyGzLivp6Uo';
					curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
					
					$result = curl_exec($ch);
					if (curl_errno($ch)) {
					    echo 'Error:' . curl_error($ch);
					}
					curl_close($ch);
					
					//echo $result;
					$json = json_decode($result);
					
					$far = array();
					$c = 0;
					foreach ($json->_embedded->inspections as $k => $v){
					
						$name = $v->name;
						$id = $v->_id;
					?>
					<div class="row">
						<div class="col-md-12 text-center">
							<button id="asInspection<?php echo $id; ?>" onclick="createASInspection('<?php echo $id; ?>','<?php echo $name; ?>')" style="width:500px;" class="btn btn-success btn-lg"><?php echo $name; ?></button>
						</div>
					</div>
					<br>
				<?php
					
					}
				
				?>
        			<h3 style="text-align:center">
					<br>Open AutoServe1 Inspections</h3>
					<p style="text-align:center">Click a button to OPEN that Inspection</p>
				<br>
				<?php
					$stmt = "select roid,ordernumber,asid,inspname from autoserveinspections where shopid = ? and roid = ?";
					if ($query = $conn->prepare($stmt)){
						$query->bind_param("si",$shopid,$roid);
						$query->execute();
						$r = $query->get_result();
						while ($rs = $r->fetch_assoc()){
				?>
					<div class="row">
						<div class="col-md-12 text-center">
							<button id="viewASInspection<?php echo $rs['asid']; ?>" onclick="viewASInspection('<?php echo $rs['asid']; ?>')" style="width:500px;" class="btn btn-danger btn-lg"><?php echo $rs['inspname']; ?></button>
						</div>
					</div>
					<br>
				<?php
						}
					}
				?>
				<div style="background-color:#EAEAEA">
				<h3 style="text-align:center">Previous Shop Boss DVI's</h3>
				<?php
				$stmt = "select distinct inspectionname from roinspection where shopid = '$shopid' and roid = $roid order by id asc";
				//echo $stmt;
				$result = $conn->query($stmt);
				while($row = $result->fetch_array()) {
				?>
				<div class="row">
					<div class="col-md-12 text-center">
					<?php 
					$inspectionname = $row["inspectionname"];
					$stmt = "select inspectionname,completionuser,completiondate from roinspectionheader";
					$stmt .= " where shopid = '$shopid' and roid = $roid and inspectionname = '$inspectionname' and completionuser != '' and completiondate != '0000-00-00 00:00:00'";
					$rohinspectionname = "";
					$completionuser = "";
					$completiondate = "";
					
					if ($query = $conn->prepare($stmt)){
						$query->execute();
						$query->bind_result($rohinspectionname,$completionuser,$completiondate);
						$query->fetch();
						$query->close();
					}
					
					if ($completiondate == "") {
					?>	
					<button onclick="location.href='dvi.php?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&name=<?php echo urlencode($row['inspectionname']); ?>'" style="width:500px;" class="btn btn-success btn-lg"><?php echo $row['inspectionname']; ?></button>
					<?php
					} // if not rohinspectioname	
					?>						
					</div>
				</div>
				</br>
				<?php
				}
				?>
				</br>

				<?php
				$stmt = "select distinct inspectionname,completionuser from roinspectionheader where shopid = '$shopid' and roid = $roid and completiondate is not null order by id asc";
				//echo $stmt;
				$result = $conn->query($stmt);
			    while($row = $result->fetch_array()) {
				?>
				<div class="row">
					<div class="col-md-12 text-center">
						<button type="button" style="width:500px;" onclick="inspectionCompleteAlert('<?php echo "reopen"; ?>','<?php echo $roid; ?>','<?php echo $row["inspectionname"]; ?>')" class="btn btn-danger btn-lg">SHOP BOSS CLOSED DVI: <?php echo $row['inspectionname']; ?><?php echo " (" . $row['completionuser'] . ")"; ?></button>
					</div>
				
				</div>
				<br>
				
				<?php
				}
				?>
				</div>
				</main>
            <!-- END Main Container -->

            <!-- Footer -->
            <!-- END Footer -->
        </div>
        <!-- END Page Container -->


		
        <script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
        <script src="<?= SCRIPT ?>/tipped.js"></script>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

        <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
        <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
        <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
        <script src="<?= SCRIPT ?>/app.js"></script><script src="../assets/js/sbp-pageresize.js"></script>
        <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
        <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
        <script src="<?= SCRIPT ?>/emodal.js?v=02"></script>
        <!-- Page Plugins -->

        <!-- Page JS Code 
        <script src="assets/js/pages/base_pages_dashboard.js"></script>-->
        
        <script>

		function inspectionCompleteAlert(t,roid,n){
			
			t = "reopen";
          
			swal({
				title: "Are you sure?",
				text: "Inspection Completed. Are you sure you want to update it. It will be recorded in the audit log.",
				type: "warning",
				showCancelButton: true,
				confirmButtonClass: "btn-danger",
				confirmButtonText: "Re-Open Inspection",
				closeOnConfirm: true
			},
			function(){
				$.ajax({
					data: "t="+t+"&r=reopen"+"&inspname="+n+"&roid="+roid+"&shopid=<?php echo $shopid; ?>",
					url: "updateinsp.php",
					type: "post",
					success: function(r){

						$('#inspcomp').removeClass("btn-success").addClass("btn-danger")
						
						location.href='dvi.php?roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&name='+encodeURIComponent(n)
						//window.location.reload(true)
					},
					error: function (xhr, ajaxOptions, thrownError) {
						console.log(xhr.status);
						console.log(xhr.responseText);
						console.log(thrownError);
					}
				})
			});
        }

        
		function viewASInspection(uuid){
		
			storeid = "<?php echo $asid; ?>";
			
			window.open('https://app.autoserve1.com/as1-ui/store/'+storeid+'/inspectionOrder/'+uuid)

		
		}
        
        function createASInspection(id,name){
        
        	// check 
        	$.ajax({
        		data: "inspname="+name+"&id="+id+"&t=create&shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>&roid=<?php echo $roid; ?>",
        		url: "<?= INTEGRATIONS ?>/autoserve/functions.php",
        		type: "post",
        		success: function(r){
        			console.log(r)
        			rar = r.split("|")
        			storeid = rar[0]
        			uuid = rar[1]
        			let a= document.createElement('a');
                    a.target= '_blank';
                    a.href= 'https://app.autoserve1.com/as1-ui/store/'+storeid+'/inspectionOrder/'+uuid;
                    a.click();
        		},
				error: function (xhr, ajaxOptions, thrownError) {
					console.log(xhr.status);
					console.log(xhr.responseText);
					console.log(thrownError);
				}
        	})
        
        }
        
        
        function createMSMInspection(id,name){
        
        	$.ajax({
        		data: "inspname="+name+"&id="+id+"&t=create&shopid=<?php echo $shopid; ?>&cid=<?php echo $cid; ?>&roid=<?php echo $roid; ?>",
        		url: "<?= INTEGRATIONS ?>/myshopmanager/functions.php",
        		type: "post",
        		success: function(r){
        			console.log(r)
        			//window.open(r)
        		},
				error: function (xhr, ajaxOptions, thrownError) {
					console.log(xhr.status);
					console.log(xhr.responseText);
					console.log(thrownError);
				}
        	})
        
        }
        
		
		$(document).ready(function(){
			$("*").dblclick(function(e){
				e.preventDefault();
			})
		})
		
		function createInspection(id,name){
			
			$.ajax({
			
				url: "updateinsp.php",
				data: "roid=<?php echo $roid; ?>&t=createinsp&shopid=<?php echo $shopid; ?>&id="+id,
				type: "post",
				success: function(r){
					console.log(r)
					if (r == "success"){
						location.href='dvi.php?roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&name='+name
					}
				},
				error: function (xhr, ajaxOptions, thrownError) {
					console.log(xhr.status);
					console.log(xhr.responseText);
					console.log(thrownError);
				}
			});
		
		}
		
        </script>
        <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
    </body>
</html>
<?php
mysqli_close($conn);
?>