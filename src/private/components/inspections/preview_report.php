<?php
$stmt = "select showpartnumberonprintedro from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($showpartnumberonprintedro);
    $query->fetch();
    $query->close();

}

require(COMPONENTS_PRIVATE_PATH . DS . "inspections" . DS . "inspection_navbar.php");
?>

<div id="dvi-inspection-preview" v-cloak class="container-fluid inspection-preview">
    <div class="mt-2 d-flex justify-content-center" v-if="!report">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <div class="row gx-1" v-else>
        <div class="col-xl-3 col-md-12">
            <div class="card">
                <div class="card-body" id="customer_info">
                    <sbp-dvi-report-quote :key="quoteKey" :show-employee-photo="showEmployeePhoto"
                                          assets-path="<?= IMAGE ?>"
                                          :report="report"></sbp-dvi-report-quote>
                </div>
            </div>
            <button class="btn btn-lg btn-primary btn-block text-center mt-3 no-print"
                    onclick="$('body').removeClass('no-img').printThis({ removeScripts : true, importCSS : true, loadCSS : '<?= CSS ?>/internal/dvi.media.css', printDelay : 300});">
                <i class="fas fa-print"></i> Print
            </button>
            <button class="btn btn-lg btn-primary btn-block text-center mt-3 no-print"
                    onclick="$('body').addClass('no-img').printThis({ removeScripts : true, importCSS : true, loadCSS : '<?= CSS ?>/internal/dvi.media.css', printDelay : 300});">
                <i class="fas fa-print"></i> Print (Without Images)
            </button>
            <button class="btn btn-lg btn-primary btn-block text-center mt-3 no-print"
                    v-for="(record, index) in matco_records" v-bind:key="index"
                    @click="handleOpenScanToolModal(record.id)"><i class="fas fa-file" aria-hidden="true"></i>
                {{ record.type }} - Scan Tool Report
            </button>
        </div>

        <div class="col-xl-9 col-md-12" style="" id="dvi-preview-col">
            <div class="card visit-reason">
                <div class="card-body">
                    <sbp-dvi-public-report-customer-request :report="report"></sbp-dvi-public-report-customer-request>
                </div>
            </div>
                   
            <div class="card">
                <div class="card-body">
                    <div class="border border-primary">
                    <div class="bg-primary text-white p-2">
                        Measurements
                    </div>
                    <div class="p-2">
                        <div v-for="measurement in measurementFindings">
                            <div class="review-item">
                                <h5>{{ measurement.name }}</h5>
                                <div><strong>Measurements:</strong> {{ measurement.public_measurement_name1 }}: {{ measurement.description1 }}</div>
                                <div v-if="measurement.description2"><strong>Measurements:</strong> {{ measurement.public_measurement_name2 }}: {{ measurement.description2 }}</div>
                            </div>
                            <hr>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="bg-info text-white p-2"
                         v-if="criticalItems.length>0 || warningItems.length>0">
                        {{ criticalItems.length + warningItems.length }} items have service suggested
                    </div>
                </div>
            </div>

            <span v-if="badtogood">
                <div class="card">
                    <div class="card-body">
                        <sbp-dvi-public-report class="" status="critical" :showlaborhrs="showLaborHours"
                                               :items="criticalItems"
                                               @approve-decline-issue="toggleApproveDeclineModal"
                                               :show-tax-and-fees="showDviTaxesFees"
                                               :roid="report.roid"
                                               showpartnumber="<?= strtolower($showpartnumberonprintedro) ?>"
                                               v-if="criticalItems.length > 0"></sbp-dvi-public-report>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                          <sbp-dvi-public-report class="" status="warning" :showlaborhrs="showLaborHours"
                                                 :items="warningItems"
                                                 @approve-decline-issue="toggleApproveDeclineModal"
                                                 :show-tax-and-fees="showDviTaxesFees"
                                                 :roid="report.roid"
                                                 showpartnumber="<?= strtolower($showpartnumberonprintedro) ?>"
                                                 v-if="warningItems.length > 0"></sbp-dvi-public-report>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <sbp-dvi-public-report class="" status="success" :items="goodItems"
                                               :show-tax-and-fees="showDviTaxesFees"
                                               :roid="report.roid"
                                               v-if="goodItems.length > 0"></sbp-dvi-public-report>
                    </div>
                </div>
          </span>

            <span v-else>
                <div class="card">
                    <div class="card-body">
                        <sbp-dvi-public-report class="" status="success" :items="goodItems"
                                               :show-tax-and-fees="showDviTaxesFees"
                                               :roid="report.roid"
                                               v-if="goodItems.length > 0"></sbp-dvi-public-report>
                    </div>
                </div>

                <sbp-dvi-public-report class="" status="warning" :showlaborhrs="showLaborHours" :items="warningItems"
                                       @approve-decline-issue="toggleApproveDeclineModal"
                                       :show-tax-and-fees="showDviTaxesFees"
                                       :roid="report.roid"
                                       showpartnumber="<?= strtolower($showpartnumberonprintedro) ?>"
                                       v-if="warningItems.length > 0"></sbp-dvi-public-report>
                <div class="card">
                    <div class="card-body">
                        <sbp-dvi-public-report class="" status="critical" :showlaborhrs="showLaborHours"
                                               :items="criticalItems"
                                               @approve-decline-issue="toggleApproveDeclineModal"
                                               :show-tax-and-fees="showDviTaxesFees"
                                               :roid="report.roid"
                                               showpartnumber="<?= strtolower($showpartnumberonprintedro) ?>"
                                               v-if="criticalItems.length > 0"></sbp-dvi-public-report>
                    </div>
                </div>
          </span>
        </div>
    </div>

    <sbp-modal-iframe
            id="scantool-modal"
            ref="scantool-modal"
            title="Scan Tool Report"
            :url="scantoolUrl"
    >
    </sbp-modal-iframe>

    <input type="hidden" id="complaintId">
    <input type="hidden" id="repairActionType">

    <sbp-modal-base
            id="approve-decline-modal"
            ref="approve-decline-modal"
            title="Approve or Decline this Repair"
            :show-close="false"
    >
        <template #footer>
            <sbp-button color="success" icon="thumbs-up" @click="approveOrDeclineRepair('approve')"
                        :loading="actionType=='approve'">Approve
            </sbp-button>
            <sbp-button v-if="approveDeclineModal.declineButtonVisible" color="danger" icon="thumbs-down"
                        @click="approveOrDeclineRepair('decline')" :loading="actionType=='decline'">Decline
            </sbp-button>
        </template>
    </sbp-modal-base>
</div>

<script type="text/javascript" src="<?=MDB?>/js/mdb.min.js"></script>