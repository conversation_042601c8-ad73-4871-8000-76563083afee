<?php
  header('Content-Type: application/json');

  require CONN;

$shopid = $oshopid = $_COOKIE['shopid'];
$roid = $_POST['roid'];
$comid = $_POST['comid'];
$jobid = $_POST['id'];
$tech = $_POST['tech'];
$ratearr = explode(':;',$_POST['rate']);
$rate = $ratearr[1];
$ratelabel = $ratearr[0];
$pids = $lids = array();

if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

if(empty($comid))
{
$inspection_item_id = $_POST['inspection_item_id'];
$complaint = $_POST['complaint'];
$stmt = "select coalesce(complaintid,0) from complaints where shopid = ? order by complaintid desc,roid desc limit 1";
if ($query = $conn->prepare($stmt))
{
 $query->bind_param("s",$shopid);
 $query->execute();
 $query->store_result();
 $num_roid_rows = $query->num_rows;

 if ($num_roid_rows > 0){
 $query->bind_result($comid);
 $query->fetch();
 }
 else
 $comid = 1000;

 $query->close();

 $comid++;

 $stmt = "select displayorder from complaints where shopid = ? and roid = ? order by displayorder desc limit 1";
 if ($query = $conn->prepare($stmt))
 {
  $query->bind_param("si",$shopid,$roid);
  $query->execute();
  $query->store_result();
  $num_roid_rows = $query->num_rows;

  if ($num_roid_rows > 0){
  $query->bind_result($lastdisplayorder);
  $query->fetch();
  }else{
  $lastdisplayorder = 0;
  }
  $query->close();

  $lastdisplayorder++;

 }

  $stmt = "select technician_notes from inspection_item_finding_inputs where id = ? and shopid = ?";
  if ($query = $conn->prepare($stmt)){

    $query->bind_param("is",$inspection_item_id,$shopid);
      $query->execute();
      $query->bind_result($technotes);
      $query->fetch();
      $query->close();

  }


 $stmt = "insert into complaints (shopid,roid,complaint,acceptdecline,complaintid,displayorder,inspection_item_id,techreport) values (?,?,?,'Pending',?,?,?,?)";
 if ($query = $conn->prepare($stmt)){
 $query->bind_param('sisiiis',$shopid,$roid,$complaint,$comid,$lastdisplayorder,$inspection_item_id,$technotes);
 $query->execute();
 $conn->commit();
 $query->close();
 }

}
}


$stmt = "select id,flatprice,jobname,taxable from cannedjobs where id = ? and shopid = ?";
if ($query = $conn->prepare($stmt)){

  $query->bind_param("is",$jobid,$shopid);
    $query->execute();
    $query->bind_result($cannedjobid,$flatprice,$jobname,$taxable);
    $query->fetch();
    $query->close();

}

$stmt = "select upper(updateinvonadd) from company where shopid = ?";
if ($query = $conn->prepare($stmt)){

  $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($updateinvonadd);
    $query->fetch();
    $query->close();

}

recordAudit("Add Canned Job", "Added Canned Job $jobname to RO#$roid");

if ($flatprice == 0){
  // add the labor
  $stmt = "select laborhours,labor,flatprice,nocalc from cannedlabor where shopid = '$shopid' and cannedjobsid = $jobid";
  if($query = $conn->prepare($stmt)){
      $query->execute();
      $result = $query->get_result();
      $query->store_result();
    while($row = $result->fetch_assoc()) {
      $laborhours = $row['laborhours'];
      $labor = $row['labor'];
      if ($row['flatprice'] > 0 || $row['nocalc']=='1'){
        $linetotal = $row['flatprice'];
        $scheduletext = "none";
      }else{
        $linetotal = round($laborhours * $rate,2);
        $scheduletext = "";
      }

      $lstmt = "insert into labor (shopid,roid,hourlyrate,ratelabel,laborhours,labor,tech,linetotal,complaintid,schedulelength,scheduletext,cannedjobsid) values (?,?,?,?,?,?,?,?,?,?,?,?)";
      if($lquery = $conn->prepare($lstmt)){
        $lquery->bind_param("sidsdssdissi",$shopid,$roid,$rate,$ratelabel,$laborhours,$labor,$tech,$linetotal,$comid,$taxable,$scheduletext,$jobid);
          if ($lquery->execute()){
            $lids[] = $conn->insert_id;
            $conn->commit();
            recordAudit("Add Labor", "Added Labor $labor ($laborhours hours) to RO#$roid");
          }
      }

    }
  }

  // add sublets
    $newsubletid = 1;
  $stmt = "select subletid from sublet where shopid = '$shopid' order by subletid desc limit 1";
  if($query = $conn->prepare($stmt)){
      $query->execute();
      $query->bind_result($subletid);
      if ($query->fetch()) {
        if (is_numeric($subletid)){
          $newsubletid = $subletid + 1;
        }
      }
      $query->close();
  }

  $stmt = "select * from cannedsublet where shopid = '$shopid' and cannedjobsid = $jobid";
  if($query = $conn->prepare($stmt)){
      $query->execute();
      $result = $query->get_result();
      $query->store_result();
    while($row = $result->fetch_assoc()) {
      
      $lstmt = "insert into sublet (subletid,shopid,roid,complaintid,subletdesc,subletcost,subletprice,subletsupplier,taxable) values (?,?,?,?,?,?,?,?,?)";
      if($lquery = $conn->prepare($lstmt)){
        $lquery->bind_param("isiisddss",$newsubletid,$shopid,$roid,$comid,$row['subletdesc'],$row['subletcost'],$row['subletprice'],$row['subletsupplier'],$row['taxable']);
          if ($lquery->execute()){
            $conn->commit();
            $sids[] = $newsubletid;
            recordAudit("Add Sublet", "Added Sublet ".$row['subletdesc']." to RO#$roid");
          }else{
            echo "sublet commit failed";
          }
      }else{
        echo "insert sublet failed:".$conn->error;
      }

      $newsubletid++;

    }
  }

  // add the parts
  $stmt = "select partnumber,partdescription,partprice,qty,partcost,partcode,tax,supplier,partcategory,overridematrix,bin,allocated from cannedparts where shopid = '$shopid' and cannedjobsid = $jobid";
  if($query = $conn->prepare($stmt)){
    //$query->bind_param("si",$shopid,$jobid);
      $query->execute();
      $result = $query->get_result();
      $query->store_result();
      while ($row = $result->fetch_array()){
        $partnumber = $row['partnumber'];
        $partdescription = $row['partdescription'];
        $partprice = $row['partprice'];
        $qty = $row['qty'];
        $partcost = $row['partcost'];
        $partcode = $row['partcode'];
        $tax = $row['tax'];
        $supplier = $row['supplier'];
        $partcategory = $row['partcategory'];
        $allocated = $row['allocated'];
        $linettlprice = round($qty*$partprice,2);
        $linettlcost = round($qty*$partcost,2);
        $net = $partprice;
        $pdate = date('Y-m-d');
        if ($row['overridematrix'] == ""){
          $ovrm = "no";
        }else{
          $ovrm = $row['overridematrix'];
        }
        $bin=$row['bin'];
        $pstmt = "insert into parts (overridematrix,shopid,partnumber,partdesc,partprice,quantity,roid,cost,partcode,linettlprice,linettlcost,`date`,complaintid,net,tax,supplier,partcategory,bin,allocated,cannedjobsid";
        $pstmt .= ") values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        
      if($pquery = $conn->prepare($pstmt)){
        $pquery->bind_param("ssssddidsddsidsssssi",$ovrm,$shopid,$partnumber,$partdescription,$partprice,$qty,$roid,$partcost,$partcode,$linettlprice,$linettlcost,$pdate,$comid,$net,$tax,$supplier,$partcategory,$bin,$allocated,$jobid);
          if ($pquery->execute()){
            $pids[] = $conn->insert_id;
            $conn->commit();
            recordAudit("Add Part", "Added Part Number $partnumber to RO#$roid");
          }
      }

      $stmt = "select additionalfeename,addfeetaxable,addfeeamt,addfeepercentordollar,qtyflag from partsinventoryfees where shopid = ? and partnumber = ?";
      if ($query = $conn->prepare($stmt)){

        $query->bind_param("ss",$oshopid,$partnumber);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_assoc()){

          $feepartnumber = "PARTFEE";
          $qtyflag = $rs['qtyflag'];


          $feedesc = strtoupper($rs['additionalfeename']);
          $feetype = $rs['addfeepercentordollar'];

          if ($qtyflag == 'yes') {
            $feeqty = $qty;
          }else{
            $feeqty = 1;
          }

          if (strtolower($feetype) == "dollar"){
            if ($qtyflag == 'yes') {
              $feeamt = $rs['addfeeamt'] ;
              $linettlprice = ($rs['addfeeamt'] * $qty) ;
              $linettlcost = $linettlprice;
            }else{
              $feeamt = $rs['addfeeamt'] ;
              $linettlprice = $rs['addfeeamt'] ;
              $linettlcost = $linettlprice;
            }

          }elseif (strtolower($feetype) == "percent"){
            if ($qtyflag == 'yes') {
              $feeamt = sbpround(($partprice * ($rs['addfeeamt'] / 100)),2);
              $linettlprice = $feeamt * $qty;
              $linettlcost = $feeamt * $qty;
            }else{
              $feeamt = sbpround(($partprice * ($rs['addfeeamt'] / 100)),2);
              $linettlprice = $feeamt ;
              $linettlcost = $feeamt;

            }
          }
          $feesupp = "NONE";


          $feecat = "PARTFEE";
          $feedisc = 0;
          $feetax = $rs['addfeetaxable'];
          $feeor = "yes";
          $pstatus = " ";
                    $deleted = "no";
          // add the fees to the parts table
          $stmt = "insert into parts (`shopid`,`PartNumber`,`PartDesc`,`PartPrice`,`Quantity`,`ROID`,`Supplier`,`Cost`,`LineTTLPrice`,`LineTTLCost`,`Date`,`PartCategory`,`complaintid`,`discount`,`net`,`tax`,`overridematrix`,`pstatus`,`deleted`,cannedjobsid) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
          if ($query = $conn->prepare($stmt)){
            $query->bind_param("sssddisdddssiddssssi",$shopid,$feepartnumber,$feedesc,$feeamt,$feeqty,$roid,$feesupp,$feeamt,$linettlprice,$linettlcost,$pdate,$feecat,$comid,$feedisc,$feeamt,$feetax,$feeor,$pstatus,$deleted,$jobid);
              if ($query->execute()){
                $conn->commit();
                $query->close();
            }
          }
        }
      }

      if($updateinvonadd=='YES')
      {
      // now adjust the inventory allocated
      $stmt = "update partsinventory set onhand = onhand - ?, allocatted = allocatted + ?, netonhand = netonhand - ? where shopid = ? and partnumber = ?";
      if ($query = $conn->prepare($stmt)){
        $query->bind_param("dddss",$qty,$qty,$qty,$oshopid,$partnumber);
          if ($query->execute()){
            $conn->commit();
            $query->close();
        }
      }

        }
      }
  }

}else{

  // '****  add the parts and labor without totals and modify to show as part of the
  $stmt = "select laborhours,labor from cannedlabor where shopid = '$shopid' and cannedjobsid = $jobid order by id asc";
  $result = $conn->query($stmt);
  while($row = $result->fetch_array()) {
    $lbrhours = $row['laborhours'];
    $lbr = $row['labor']." (Included in $jobname)";
    $linettl = 0;
    $lstmt = "insert into labor (shopid,roid,hourlyrate,ratelabel,laborhours,labor,tech,linetotal,complaintid,memorate,schedulelength,cannedjobsid) values (?,?,?,?,?,?,?,?,?,?,?,?)";
    if ($query = $conn->prepare($lstmt)){
      $query->bind_param("sidsdssdidsi",$shopid,$roid,$rate,$ratelabel,$lbrhours,$lbr,$tech,$linettl,$comid,$rate,$taxable,$jobid);
        if ($query->execute()){
          $lids[] = $conn->insert_id;
          $conn->commit();
          recordAudit("Add Labor", "Added Labor $lbr ($lbrhours hours) to RO#$roid");
          $query->close();
      }
    }
  }

  $newsubletid = 1;
  $stmt = "select subletid from sublet where shopid = '$shopid' order by subletid desc limit 1";
  if($query = $conn->prepare($stmt)){
      $query->execute();
      $query->bind_result($subletid);
      if ($query->fetch()) {
        if (is_numeric($subletid)){
          $newsubletid = $subletid + 1;
        }
      }
      $query->close();
  }

  $stmt = "select * from cannedsublet where shopid = '$shopid' and cannedjobsid = $jobid";
  if($query = $conn->prepare($stmt)){
      $query->execute();
      $result = $query->get_result();
      $query->store_result();
    while($row = $result->fetch_assoc()) {
      
      $lstmt = "insert into sublet (subletid,shopid,roid,complaintid,subletdesc,subletcost,subletprice,subletsupplier,taxable) values (?,?,?,?,?,?,?,?,?)";
      if($lquery = $conn->prepare($lstmt)){
        $lquery->bind_param("isiisddss",$newsubletid,$shopid,$roid,$comid,$row['subletdesc'],$row['subletcost'],$row['subletprice'],$row['subletsupplier'],$row['taxable']);
          if ($lquery->execute()){
            $conn->commit();
            $sids[] = $newsubletid;
            recordAudit("Add Sublet", "Added Sublet ".$row['subletdesc']." to RO#$roid");
          }else{
            echo "sublet commit failed";
          }
      }else{
        echo "insert sublet failed:".$conn->error;
      }

    }
  }else{
    echo "labor stmt error".$conn->errno;
  }


  //'now get the parts
  $c = 1;
  $stmt = "select partnumber,partdescription,qty,partcost,tax,coalesce(supplier,'') supp, coalesce(partcategory,'') pc,bin from cannedparts where shopid = '$shopid' and cannedjobsid = $jobid order by id asc";
  $result = $conn->query($stmt);
  while($row = $result->fetch_array()) {
    $partnumber = $row['partnumber'];
    $partdescription = $row['partdescription']." (Included in $jobname)";
    $qty = $row['qty'];
    $partcost = $row['partcost'];
    $pdate = date("Y-m-d");
    $tax = $row['tax'];
    $supplier = $row['supp'];
    $partcategory = $row['pc'];
    $partprice = 0;
    $partcode = "New";
    $extp = 0;
    $extc = $partcost * $qty;
    $bin=$row['bin'];
    $nstmt = "insert into parts (shopid,partnumber,partdesc,partprice,quantity,roid,cost,partcode,"
    . "linettlprice,linettlcost,`date`,complaintid,net,tax,supplier,partcategory,displayorder,bin,cannedjobsid) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    if ($query = $conn->prepare($nstmt)){
      $query->bind_param("sssddidsddsidsssisi",$shopid,$partnumber,$partdescription,$partprice,$qty,$roid,$partcost,$partcode,$extp,$extc,$pdate,$comid,$partprice,$tax,$supplier,$partcategory,$c,$bin,$jobid);
        if ($query->execute()){
          $pids[] = $conn->insert_id;
          $conn->commit();
          recordAudit("Add Part", "Added Part Number $partnumber to RO#$roid");
          $query->close();
      }
    }

      $stmt = "select additionalfeename,addfeetaxable,addfeeamt,addfeepercentordollar,qtyflag from partsinventoryfees where shopid = ? and partnumber = ?";
      if ($query = $conn->prepare($stmt)){

        $query->bind_param("ss",$oshopid,$partnumber);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_assoc()){

          $feepartnumber = "PARTFEE";
          $qtyflag = $rs['qtyflag'];


          $feedesc = strtoupper($rs['additionalfeename']);
          $feetype = $rs['addfeepercentordollar'];

          if ($qtyflag == 'yes') {
            $feeqty = $qty;
          }else{
            $feeqty = 1;
          }

          if (strtolower($feetype) == "dollar"){
            if ($qtyflag == 'yes') {
              $feeamt = $rs['addfeeamt'] ;
              $linettlprice = ($rs['addfeeamt'] * $qty) ;
              $linettlcost = $linettlprice;
            }else{
              $feeamt = $rs['addfeeamt'] ;
              $linettlprice = $rs['addfeeamt'] ;
              $linettlcost = $linettlprice;
            }

          }elseif (strtolower($feetype) == "percent"){
            if ($qtyflag == 'yes') {
              $feeamt = sbpround(($partprice * ($rs['addfeeamt'] / 100)),2);
              $linettlprice = $feeamt * $qty;
              $linettlcost = $feeamt * $qty;
            }else{
              $feeamt = sbpround(($partprice * ($rs['addfeeamt'] / 100)),2);
              $linettlprice = $feeamt ;
              $linettlcost = $feeamt;

            }
          }
          $feesupp = "NONE";


          $feecat = "PARTFEE";
          $feedisc = 0;
          $feetax = $rs['addfeetaxable'];
          $feeor = "yes";
          $pstatus = " ";
          $deleted = "no";
          // add the fees to the parts table
          $stmt = "insert into parts (`shopid`,`PartNumber`,`PartDesc`,`PartPrice`,`Quantity`,`ROID`,`Supplier`,`Cost`,`LineTTLPrice`,`LineTTLCost`,`Date`,`PartCategory`,`complaintid`,`discount`,`net`,`tax`,`overridematrix`,`pstatus`,`deleted`,cannedjobsid) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
          if ($query = $conn->prepare($stmt)){
            $query->bind_param("sssddisdddssiddssssi",$shopid,$feepartnumber,$feedesc,$feeamt,$feeqty,$roid,$feesupp,$feeamt,$linettlprice,$linettlcost,$pdate,$feecat,$comid,$feedisc,$feeamt,$feetax,$feeor,$pstatus,$deleted,$jobid);
              if ($query->execute()){
                $conn->commit();
                $query->close();
            }
          }
        }
      }

    if($updateinvonadd=='YES')
    {
    // now adjust the inventory allocated
    $stmt = "update partsinventory set onhand = onhand - ?, allocatted = allocatted + ?, netonhand = netonhand - ? where shopid = ? and partnumber = ?";
    if ($query = $conn->prepare($stmt)){
      $query->bind_param("dddss",$qty,$qty,$qty,$oshopid,$partnumber);
        if ($query->execute()){
          $conn->commit();
          $query->close();
      }else{
        echo "Execution Error|Inserting into Part from RecommendParts";
        exit;
      }
    }else{
      echo "Connection Error|Inserting into Parts from RecommendParts";
      exit;
    }
      }

    $c++;

  }
  // ***** now add the canned job with a price
  $nstmt = "insert into parts (shopid,partnumber,partdesc,partprice,quantity,roid,cost,partcode,"
  . "linettlprice,linettlcost,`date`,complaintid,net,tax,supplier,partcategory,displayorder,bin,cannedjobsid) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
  if ($query = $conn->prepare($nstmt)){
    $partnum = "JOB";
    $jqty = 1;
    $jcost = 0;
    $jcode = "New";
    $jcat = "GENERAL PART";
    $shopname = $_COOKIE['shopname'];
    $jorder = 0;
    $query->bind_param("sssddidsddsidsssisi",$shopid,$partnum,$jobname,$flatprice,$jqty,$roid,$jcost,$jcode,$flatprice,$jcost,$pdate,$comid,$jcost,$taxable,$shopname,$jcat,$jorder,$bin,$jobid);
      if ($query->execute()){
        $pids[] = $conn->insert_id;
        $conn->commit();
        recordAudit("Add Canned Job", "Added Canned Job $jobname to RO#$roid");
        $query->close();
    }
  }
}

$parts = $labors = $sublets = array();

if(!empty($pids))
{
  $stmt = "select partid,partnumber,partdesc,partprice,quantity,linettlprice,cost,partcategory from parts where shopid = ? and partid in (".implode(',', $pids).")";
  if ($query = $conn->prepare($stmt)) {
      $query->bind_param("s", $shopid);
      $query->execute();
      $result = $query->get_result();
      while($row = $result->fetch_assoc())
      $parts[] = $row;
      $query->close();
  }
}

if(!empty($lids))
{
  $stmt = "select laborid,tech,laborhours,hourlyrate,ratelabel,labor,linetotal,scheduletext as matrix from labor where shopid = ? and laborid in (".implode(',', $lids).")";
  if ($query = $conn->prepare($stmt)) {
      $query->bind_param("s", $shopid);
      $query->execute();
      $result = $query->get_result();
      while($row = $result->fetch_assoc())
      $labors[] = $row;
      $query->close();
  }
}

if(!empty($sids))
{
  $stmt = "select subletid,`subletdesc`,`subletprice`,`subletcost`,`subletinvoiceno`,`subletsupplier` from sublet where shopid = ? and subletid in (".implode(',', $sids).")";
  if ($query = $conn->prepare($stmt)) {
      $query->bind_param("s", $shopid);
      $query->execute();
      $result = $query->get_result();
      while($row = $result->fetch_assoc())
      $sublets[] = $row;
      $query->close();
  }
}

$stmt = "select complaintid as comid,acceptdecline as comstatus,locked from complaints where shopid = ? and inspection_item_id=?";
if ($query = $conn->prepare($stmt))
{
 $query->bind_param("ss", $shopid, $inspection_item_id);
 $query->execute();
 $comsn_result = $query->get_result();
 $query->close();
}
$comrow = $comsn_result->fetch_assoc();

echo(json_encode(array("complaint"=>$comrow,"parts"=>$parts,"labors"=>$labors,"sublets"=>$sublets)));

    