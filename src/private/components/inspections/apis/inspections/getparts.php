<?php
  header('Content-Type: application/json');

  require CONN;
	$shopid = $_COOKIE['shopid'];
  $q = trim($_POST['term']);
  $arr=array();

  if(in_array($shopid, array('13445','22957'))) $shopid = '22865';

  $stmt = "Select partnumber,partdesc,partcost,partprice,overridematrix,partcategory from partsinventory where shopid = '" . $shopid . "' and (partnumber Like '%" . addslashes($q) . "%' or partdesc like '%" . addslashes($q) . "%' or replace(replace(partnumber,'-',''),' ','') like '%" . str_replace(" ","",str_replace("-","",addslashes($q))) . "%') limit 50";

  if (!empty($q) && $query = $conn->prepare($stmt))
  {
    $query->execute();
    $result = $query->get_result();
    while ($row = $result->fetch_array()){ 
      $arr[]=array('type'=>'INV','partnumber'=>strtoupper($row['partnumber']),'partdesc'=>strtoupper($row['partdesc']),'partcost'=>asDollars($row['partcost']),'partprice'=>asDollars($row['partprice']),'overridematrix'=>strtolower($row['overridematrix']),'partcategory'=>$row['partcategory']);
    }
  }

  if ($shopid == "1238" || $shopid == "1073" || $shopid == "1191" || $shopid == "1305")
  $preg = "`partsregistry-".$shopid."`";
  else
  $preg = "`partsregistry`";

  $stmt = "Select partnumber,partdesc,partcost,partprice,overridematrix,partcategory from $preg where shopid = '" . $shopid . "' and (partnumber Like '%" . addslashes($q) . "%' or partdesc like '%" . addslashes($q) . "%' or replace(replace(partnumber,'-',''),' ','') like '%" . str_replace(" ","",str_replace("-","",addslashes($q))) . "%') limit 50";

  if (!empty($q) && $query = $conn->prepare($stmt))
  {
    $query->execute();
    $result = $query->get_result();
    while ($row = $result->fetch_array()){ 
      $arr[]=array('type'=>'NON','partnumber'=>strtoupper($row['partnumber']),'partdesc'=>strtoupper($row['partdesc']),'partcost'=>asDollars($row['partcost']),'partprice'=>asDollars($row['partprice']),'overridematrix'=>strtolower($row['overridematrix']),'partcategory'=>$row['partcategory']);
    }
  }


echo json_encode($arr);
