<?php
header('Content-Type: application/json');

require CONNWOSHOPID;
require "../functions.php";
$shopid = $_POST['shopid'] ?? $_COOKIE['shopid'];
$id = $_POST['id'];

$stmt = "select dvi.*, v.Vin vh_vin, v.Engine vh_engine, v.LicState vh_licstate, v.LicNumber vh_licnumber, ro.VehicleMiles vh_miles_in, ro.MilesOut vh_miles_out, ro.tirepressureinlf vh_tirepressureinlf, ro.tirepressureinrf vh_tirepressureinrf, ro.tirepressureinlr vh_tirepressureinlr, ro.tirepressureinrr vh_tirepressureinrr, ro.tirepressureoutlf vh_tirepressureoutlf, ro.tirepressureoutrf vh_tirepressureoutrf, ro.tirepressureoutlr vh_tirepressureoutlr, ro.tirepressureoutrr vh_tirepressureoutrr,ro.VehID as vehid,ro.vehinfo from dvi left join repairorders ro on dvi.shopid = ro.shopid and dvi.roid = ro.roid left join vehicles v on ro.shopid = v.shopid and ro.VehID = v.vehid where dvi.id = ? and dvi.shopid = ? and dvi.deleted='no'";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("ss", $id, $shopid);
    $query->execute();
    $result = $query->get_result();
    $dvi = $result->fetch_assoc();
    $query->close();
} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "select * from dvi_customer_request_images where shopid = ? and inspection_id = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("ss", $shopid, $id);
    $query->execute();
    $dcriresult = $query->get_result();
    $query->close();
}
$images = array();
while ($dcri = $dcriresult->fetch_assoc()) {
    array_push($images, $dcri);
}

$dvi['customer_request_images'] = $images;

$roimages = array();

$stmt = "select * from repairorderpics where shopid = ? and roid = ? and inspitemid = 0";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("ss", $shopid, $dvi['roid']);
    $query->execute();
    $dcriresult = $query->get_result();
    $query->close();
}
$images = array();
while ($dcri = $dcriresult->fetch_assoc()) {
    $ext = strtolower(pathinfo($dcri['picname'], PATHINFO_EXTENSION));
    if (in_array($ext, array('jpg', 'jpeg', 'png', 'gif')))
        array_push($roimages, $dcri);
}

$dvi['ro_images'] = $roimages;

$im = getInspectionModel($conn, $shopid, $dvi['model_id'], false, $id);
$dvi['model'] = $im;

$dvi["educationAssets"] = getEducationAssets($conn, $shopid);
if (!empty($dvi["vehicle_photo_url"])) {
    if (strpos($dvi["vehicle_photo_url"], "RepairOrders") !== false) {
        $dvi["vehicle_photo_url"] = "https://customers-ss.s3.amazonaws.com/".strstr($dvi["vehicle_photo_url"], "$shopid/Uploads/RepairOrders");
    }
    if (strpos($dvi['vehicle_photo_url'], "https://") === false) {
        $dvi['vehicle_photo_url'] = 'https://shopbosspro.com' . $dvi['vehicle_photo_url'];
    }
}

$dvi['vh_vin'] = decodeString($dvi['vh_vin']);
$dvi['vh_licnumber'] = decodeString($dvi['vh_licnumber']);
$dvi['vehinfo'] = decodeString($dvi['vehinfo']);
$dvi['vh_engine'] = decodeString($dvi['vh_engine']);

echo json_encode($dvi);
?>