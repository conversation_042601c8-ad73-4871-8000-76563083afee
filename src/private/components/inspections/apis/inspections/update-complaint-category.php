<?php
header('Content-Type: application/json');

require CONN;
$shopid = $_COOKIE['shopid'];
$comid = $_POST['comid'];
$complaint_category = $_POST['category'];
$roid = $_POST['roid'];

if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

if (!empty($comid)){

    $stmt = "update complaints set issue = ? where complaintid = ? and shopid = ? AND roid = ?";
    if ($query = $conn->prepare($stmt)){
        $query->bind_param("sssi", $complaint_category, $comid, $shopid, $roid);
        $query->execute();
        $conn->commit();
        $query->close();
        echo json_encode(array("status"=>"success"));
    } else {
        echo json_encode(array("status"=>"error", "info" => $conn->error));
    }

}


