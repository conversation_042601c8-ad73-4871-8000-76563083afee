<?php
  header('Content-Type: application/json');

  require CONN;
	$shopid = $_COOKIE['shopid'];
  $modelid = $_POST['modelid'];
  $roid = $_POST['roid'];
  $mileagein = $_POST['mileagein'];
  $mileageout = $_POST['mileageout'];
  $visitreason = $_POST['visitreason'];
  $timepromised = $_POST['timepromised'];
  $waiting = $_POST['waiting'];
  $technician = $_POST['technician'];
  $advisor = $_POST['advisor'];
  $vehicle_photo_url = '';

  if ($mileagein == "") $mileagein = 0;
  if ($mileageout == "") $mileageout = 0;

  $stmt = "select picname from repairorderpics where shopid = ? and roid = ? and inspitemid = 0";
  if ($query = $conn->prepare($stmt))
  {
   $query->bind_param("si",$shopid,$roid);
   $query->execute();
   $r = $query->get_result();
   while ($rs = $r->fetch_array())
   {
    $fileext = strtolower(substr($rs['picname'],-3));
    if ($fileext == "jpg" || $fileext == "png" || $fileext == "gif" || $fileext == "peg")
    {
      //fix here, use s3
      if (file_exists("\\\\fs.shopboss.aws\\share\\upload\\" . $shopid . "\\" . $roid . "\\".$rs['picname'])) {
        $vehicle_photo_url = "/sbp/upload/" . $shopid . "/" . $roid . "/" . $rs['picname'];
      } else {
        $vehicle_photo_url = $rs['picname'];
      }
      break;
    }
   }  
  }
  
  $stmt = "update repairorders set VehicleMiles = ?, MilesOut = ?  where shopid = ? and roid = ?";
  if ($query = $conn->prepare($stmt)){
    $query->bind_param("ssss", $mileagein, $mileageout, $shopid, $roid);
    $query->execute();
    $iid = $query->insert_id;
    $conn->commit();
    $query->close();
  } else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
  }

  $stmt = "insert into dvi (shopid,roid,model_id,mileage_in,mileage_out,visit_reason,time_promised,waiting,technician,advisor,vehicle_photo_url) values (?,?,?,?,?,?,?,?,?,?,?)";
  if ($query = $conn->prepare($stmt)){
    $query->bind_param("sssssssssss", $shopid, $roid, $modelid, $mileagein, $mileageout, $visitreason, $timepromised,$waiting,$technician,$advisor,$vehicle_photo_url);
    $query->execute();
    $iid = $query->insert_id;
    $conn->commit();
    $query->close();
  } else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
  }

  $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='71'";
  $query = $conn->prepare($stmt);
  $query->execute();
  $query->store_result();
  $numrows = $query->num_rows();
  if ($numrows > 0)
  {
    $query->bind_result($textcontent,$emailcontent,$popupcontent);
    $query->fetch();
    $emailcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent));
    $popupcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent));
    $textcontent=str_replace("*|RO|*",$roid,$textcontent);
    $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'71',?,?,?)";
    if ($query = $conn->prepare($stmt))
    {
      $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
      $query->execute();
      $conn->commit();
      $query->close();
    }
   }

  $stmt = "select * from dvi where id = ? and shopid = ?";
  if ($query = $conn->prepare($stmt)) {
    $query->bind_param("ss", $iid, $shopid);
    $query->execute();
    $result = $query->get_result();
    $is = $result->fetch_assoc();
    $query->close();
  }

  echo json_encode($is);
?>