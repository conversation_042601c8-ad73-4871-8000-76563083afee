<?php
  header('Content-Type: application/json');

  require CONN;
	$shopid = $_COOKIE['shopid'];
  $techs = $_POST['techs'];
  $search = $_POST['search'];
  $history = $_POST['history'];

  $stmt = "select dvi.id id, dvi.roid roid, dvi.created_at createdAt,dvi.estimate_complete,dvi.vehicle_photo_url, im.name modelName, v.Year vehicleYear, v.Make vehicleMake, v.Model vehicleModel, v.LicNumber vehicleLicenseNumber, c.LastName customerLastName, c.FirstName customerFirstName, c.HomePhone customerHomePhone, c.WorkPhone customerWorkPhone, c.CellPhone customerCellPhone, c.EMail customerEmail, e.EmployeeEmail techEmail,concat(e.employeefirst, ' ',e.employeelast) as techName, e2.EmployeeEmail advisorEmail, concat(e2.employeefirst, ' ',e2.employeelast) as advisorName,ro.status from dvi left join inspection_models im on dvi.shopid = im.shopid and dvi.model_id = im.id left join repairorders ro on dvi.shopid = ro.shopid and dvi.roid = ro.roid left join vehicles v on ro.shopid = v.shopid and ro.VehID = v.vehid left join customer c on ro.shopid = c.shopid and ro.CustomerID = c.customerid left join employees e on dvi.technician = e.id left join employees e2 on dvi.advisor = e2.id where dvi.shopid = ? and ro.ROType != 'No Approval' and dvi.deleted='no'";

  if (!$history) {
    $stmt = $stmt . " and ro.status != 'CLOSED' ";
  }

  if ($search) {
    $stmt = $stmt . " and (concat(v.Year, ' ', v.Make, ' ', v.Model) like '%$search%' or dvi.roid like '%$search%' or im.name like '%$search%' or v.LicNumber like '%$search%' or concat(c.FirstName, ' ', c.LastName) like '%$search%' or e.EmployeeEmail like '%$search%' or e2.EmployeeEmail like '%$search%')";
  }

  if ($techs) {
    $stmt = $stmt . " and technician in ($techs)";
  }
  $stmt = $stmt . " order by dvi.created_at  desc";
  if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $results = $query->get_result();
    $query->close();
  } else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
  }


  $aresult = array();
  while ($rs = $results->fetch_assoc()) {
      if (!empty($rs['vehicle_photo_url'])) {
        if (strpos($rs['vehicle_photo_url'], "sbp/upload") !== false) {
            $rs['vehicle_photo_url'] = (isset($_SERVER['HTTPS']) ? 'https://' : 'http://') . $_SERVER['HTTP_HOST'] . '/' . ltrim($rs['vehicle_photo_url'], '/');
        } elseif (strpos($rs['vehicle_photo_url'], "customers-ss.s3.amazonaws.com") === false) {
            $rs['vehicle_photo_url'] = 'https://customers-ss.s3.amazonaws.com/' . ltrim($rs['vehicle_photo_url'], '/');
        }
      }
    array_push($aresult, $rs);
  }
  echo json_encode($aresult);
