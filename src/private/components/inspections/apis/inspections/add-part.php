<?php
  header('Content-Type: application/json');

  require CONN;

	$shopid = $oshopid = $_COOKIE['shopid'];
  $comid = $_POST['comid'];
  $partnumber = $_POST['partnumber'];
  $partdesc = $_POST['partdesc'];
  $partqty =  $_POST['partqty'];
  $partprice = $_POST['partprice'];
  $partcost = $_POST['partcost'];
  $roid = $_POST['roid'];
  $inspection_item_id = $_POST['inspection_item_id'];
  $complaint = $_POST['complaint'];
  $partcategory = $_POST['partcategory'];
  $overridematrix = $_POST['overridematrix'];
  
  if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';
  
  if(empty($comid))
  {
      $stmt = "select coalesce(complaintid,0) from complaints where shopid = ? order by complaintid desc,roid desc limit 1";
      if ($query = $conn->prepare($stmt))
      {
       $query->bind_param("s",$shopid);
       $query->execute();
       $query->store_result();
       $num_roid_rows = $query->num_rows;

       if ($num_roid_rows > 0){
       $query->bind_result($comid);
       $query->fetch();
       }
       else
       $comid = 1000;

       $query->close();

       $comid++;

       $stmt = "select displayorder from complaints where shopid = ? and roid = ? order by displayorder desc limit 1";
       if ($query = $conn->prepare($stmt))
       {
        $query->bind_param("si",$shopid,$roid);
        $query->execute();
        $query->store_result();
        $num_roid_rows = $query->num_rows;

        if ($num_roid_rows > 0){
        $query->bind_result($lastdisplayorder);
        $query->fetch();
        }else{
        $lastdisplayorder = 0;
        }
        $query->close();

        $lastdisplayorder++;

       }

       $stmt = "select technician_notes from inspection_item_finding_inputs where id = ? and shopid = ?";
        if ($query = $conn->prepare($stmt)){

          $query->bind_param("is",$inspection_item_id,$shopid);
            $query->execute();
            $query->bind_result($technotes);
            $query->fetch();
            $query->close();

        }

       $stmt = "insert into complaints (shopid,roid,complaint,acceptdecline,complaintid,displayorder,inspection_item_id,techreport) values (?,?,?,'Pending',?,?,?,?)";
       if ($query = $conn->prepare($stmt)){
       $query->bind_param('sisiiis',$shopid,$roid,$complaint,$comid,$lastdisplayorder,$inspection_item_id,$technotes);
       $query->execute();
       $conn->commit();
       $query->close();
       }

      }
    }

      $linettlprice = $partprice*$partqty;
      $linettlcost = $partcost*$partqty;
      $date = date('Y-m-d');

      $stmt = "insert into parts set shopid = ?, roid = ?,partnumber = ?, partdesc = ?, cost = ?,partprice = ?, quantity = ?, linettlprice = ?, linettlcost = ?,net = ?, complaintid = ?, tax='YES',`date`=?,partcategory = ?,overridematrix = ?";
      if ($query = $conn->prepare($stmt)){
      $query->bind_param("sissddddddisss", $shopid,$roid,$partnumber,$partdesc,$partcost,$partprice,$partqty,$linettlprice,$linettlcost,$linettlprice,$comid,$date,$partcategory,$overridematrix);
      $query->execute();
      $partid = $conn->insert_id;
      $conn->commit();
      }


      $stmt = "select upper(updateinvonadd) from company where shopid = ?";
if ($query = $conn->prepare($stmt)){

  $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($updateinvonadd);
    $query->fetch();
    $query->close();

}else{
  echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
}

if($updateinvonadd=='YES')
{
$stmt = "update partsinventory set onhand = onhand - $partqty, netonhand = netonhand - $partqty where shopid = ? and partnumber = ?";
if ($query = $conn->prepare($stmt)){
  $query->bind_param("ss",$oshopid,$partnumber);
    if ($query->execute()){
      $conn->commit();
      $query->close();
  }
}
}


// add the part to the parts registry
if ($shopid == "1238" || $shopid == "1073" || $shopid == "1191" || $shopid == "1305"){
  $preg = "`partsregistry-".$shopid."`";
}else{
  $preg = "`partsregistry`";
}

$stmt = "select count(*) c from $preg where shopid = ? and partnumber = ?";
if ($query = $conn->prepare($stmt)){
  $query->bind_param("ss",$oshopid,$partnumber);
  $query->execute();
  $query->bind_result($cnt);
  $query->fetch();
  $query->close();
}

if ($cnt == 0){
  $stmt = "insert into ".$preg." (`shopid`,`PartNumber`,`PartDesc`,`PartPrice`,`partCost`,tax) values (?,?,?,?,?,'YES')";
  if ($query = $conn->prepare($stmt)){
    $query->bind_param("sssdd",$oshopid,$partnumber,$partdesc,$partprice,$partcost);
    $query->execute();
    $conn->commit();
  }
}

$stmt = "select partid,partnumber,partdesc,partprice,quantity,linettlprice,cost,partcategory from parts where shopid = ? and partid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid,$partid);
    $query->execute();
    $result = $query->get_result();
    $is = $result->fetch_assoc();
    $query->close();
}

$stmt = "select complaintid as comid,acceptdecline as comstatus,locked from complaints where shopid = ? and inspection_item_id=?";
if ($query = $conn->prepare($stmt)){
$query->bind_param("ss", $shopid, $inspection_item_id);
$query->execute();
$comsn_result = $query->get_result();
$query->close();
}
$comrow = $comsn_result->fetch_assoc();

echo json_encode(array('complaint'=>$comrow,'parts'=>$is));

?>
