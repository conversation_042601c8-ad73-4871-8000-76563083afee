<?php
header('Content-Type: application/json');
require CONN;

$shopid = $_COOKIE['shopid'];
$inspectionId = $_POST['inspectionId'];
$findingInputId = $_POST['findingInputId'];
$file = $_FILES['file'];
$filename = $file['tmp_name'];
$curlFile = new CURLFile($filename, $file['type'], $file['name']);
$embedcode = '';

if (strpos($file['type'], 'video') === false) {
    echo json_encode(['status' => false, 'message' => 'Only video files are allowed.']);
    exit;
}

function getIframeSrc($iframeCode) {
    $doc = new DOMDocument();
    $doc->loadHTML($iframeCode);
    $iframeTags = $doc->getElementsByTagName('iframe');
    if ($iframeTags->length > 0) {
        $iframe = $iframeTags->item(0);
        return $iframe->getAttribute('src');
    }
    return '';
}

$options = array(
    CURLOPT_URL => 'https://api.sproutvideo.com/v1/videos',
    CURLOPT_HTTPHEADER => array(
        'SproutVideo-Api-Key: 0b53a5acd54897c21fad0b24849f38bf',
    ),
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => array(
        'title' => 'Inspection',
        'source_video' => $curlFile,
    ),
    CURLOPT_RETURNTRANSFER => true,
);

$curl = curl_init();
curl_setopt_array($curl, $options);
$response = curl_exec($curl);

if (curl_errno($curl)) {
    echo 'Error uploading video: ' . curl_error($curl);
} else {
    $response = json_decode($response);
    $embedcode = $response->embed_code ?? '';
}

curl_close($curl);

if ( !empty($embedcode) ){
    $code = getIframeSrc($embedcode);
    $stmt = "INSERT INTO inspection_item_finding_image_inputs (shopid, item_finding_input_id, image_url, available, type) 
             VALUES (?, ?, ?, true, 'vid')";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("sss", $shopid, $findingInputId, $code);
        $query->execute();
        $id = $query->insert_id;
        $conn->commit();
        $query->close();
    }
    $stmt = "SELECT * FROM inspection_item_finding_image_inputs WHERE id = ? AND shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ss", $id, $shopid);
        $query->execute();
        $result = $query->get_result();
        $finding_image = $result->fetch_assoc();
        $query->close();
    }
}

$videoId = $response->id ?? '';
$deployed = false;

if ($videoId) {
    for ($i = 0; $i < 12; $i++) {
        sleep(10);
        $checkOptions = [
            CURLOPT_URL => "https://api.sproutvideo.com/v1/videos/$videoId",
            CURLOPT_HTTPHEADER => [
                'SproutVideo-Api-Key: 0b53a5acd54897c21fad0b24849f38bf',
            ],
            CURLOPT_RETURNTRANSFER => true,
        ];
        $checkCurl = curl_init();
        curl_setopt_array($checkCurl, $checkOptions);
        $checkResponse = curl_exec($checkCurl);
        curl_close($checkCurl);
        $videoData = json_decode($checkResponse);
        if (!empty($videoData->state) && $videoData->state === 'deployed') {
            $deployed = true;
            break;
        }
    }
}

if ($deployed && !empty($embedcode)) {
    echo json_encode($finding_image);
}
?>
