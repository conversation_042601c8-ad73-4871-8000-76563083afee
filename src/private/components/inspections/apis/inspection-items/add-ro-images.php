<?php

ini_set("display_errors", 1);
error_reporting(E_ALL);
//  header('Content-Type: application/json');
  include INTEGRATIONS_PATH . "/sbp_bucket/sbp_bucket.php";
  require CONN;
	$shopid = $_COOKIE['shopid'];
  $findingInputId = $_POST['findingInputId'];
  $images = json_decode($_POST['images'],true);

  if(!empty($images))
  {
    $ids = array();
    foreach($images as $imgid)
    {
      $stmt = "select picname,roid from repairorderpics where shopid = ? and id = ?";
      if ($query = $conn->prepare($stmt)) 
      {
        $imagename = '';
        $query->bind_param("si", $shopid, $imgid);
        $query->execute();
        $query->bind_result($imagename,$roid);
        $query->fetch();
        $query->close();
      }

      if(!empty($imagename))
      {
          $target_dir = "\\\\fs.shopboss.aws\\share\\upload\\".$shopid."\\".$roid."\\dvi";
        if (!file_exists($target_dir))
        mkdir("\\\\fs.shopboss.aws\\share\\upload\\".$shopid."\\".$roid."\\dvi", 0777, true);

        $fname = $shopid."_".rand(10000,99999). $imagename;
        $source = "\\\\fs.shopboss.aws\\share\\upload\\".$shopid."\\".$roid."\\".$imagename;

        if(file_exists($source)) //if the file is in file system
        {
            //copy( $source, $target_dir."\\".$fname);
            $fname = $shopid . "/BossInspect/$roid/$fname"; //the path on S3
            $sbp_bucket->add_file_from_path($source, $fname);
            //$publicPath = "/src/private/integrations/sbp_bucket/file.php?key=" . $fname;
            $imageUrl = "https://customers-ss.s3.amazonaws.com/" . $fname;
        }  else { //if the file is in s3 already
            $img_parts = explode("/", $imagename);
            $fname = $img_parts[count($img_parts)-1];
            $fname = $shopid . "/BossInspect/$roid/$fname";
            $sbp_bucket->copy_file($imagename, $fname);
            $imageUrl = "https://customers-ss.s3.amazonaws.com/" . $fname;
        }
         $stmt = "insert into inspection_item_finding_image_inputs (shopid,item_finding_input_id,image_url,available) values (?,?,?,true)";
          if ($query = $conn->prepare($stmt)){
          $query->bind_param("sss", $shopid, $findingInputId, $imageUrl);
          $query->execute();
          $ids[] = $query->insert_id;
          $conn->commit();
          $query->close();
          }
      }

    } 

    $finding_images = array();

    $stmt = "select * from inspection_item_finding_image_inputs where id in (".implode(',', $ids).") and shopid = ?";
      if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s",$shopid);
        $query->execute();
        $result = $query->get_result();
        $query->close();
      }

      while ($row = $result->fetch_assoc()) {
       array_push($finding_images, $row);
      }

      echo json_encode($finding_images);

   }

?>
