<?php
include INTEGRATIONS_PATH . "/sbp_bucket/sbp_bucket.php";
require CONN;

$shopid = $_COOKIE['shopid'];
$roid = $_GET['roid'];

$target_path = "\\\\fs.shopboss.aws\\share\\upload\\" . $shopid;
$target_dir = realpath($target_path);
if (!$target_dir) {
    mkdir("\\\\fs.shopboss.aws\\share\\upload\\" . $shopid);
}

$target_path = "\\\\fs.shopboss.aws\\share\\upload\\" . $shopid . "\\" . $roid;
$target_dir = realpath($target_path);
if (!$target_dir) {
    mkdir("\\\\fs.shopboss.aws\\share\\upload\\" . $shopid . "\\" . $roid);
}

$target_path = "\\\\fs.shopboss.aws\\share\\upload\\" . $shopid . "\\" . $roid . "\\dvi";
$target_dir = realpath($target_path);
if (!$target_dir) {
    mkdir("\\\\fs.shopboss.aws\\share\\upload\\" . $shopid . "\\" . $roid . "\\dvi");
}

//Get the temp file path
$tmpFilePath = $_FILES['file']['tmp_name'];

$extension = "." . substr($_FILES['file']['type'], -3, 3);

//Make sure we have a filepath
if ($tmpFilePath != "") {
    //Setup our new file path
    $file_name = preg_replace("/[^a-zA-Z0-9 .-_]/", "", $_FILES['file']['name']) . $extension;

    //Setup our new file path
    $newfilename = rand(10000, 99999) . $file_name;

    $newFilePath = "\\\\fs.shopboss.aws\\share\\upload\\" . $shopid . "\\" . $roid . "\\" . "dvi" . "\\" . $newfilename;

    //echo $newFilePath;
    //Upload the file into the temp dir
    if (move_uploaded_file($tmpFilePath, $newFilePath)) {
        $fname = $shopid . "_" . $newfilename;
        $resizeFilePath = "\\\\fs.shopboss.aws\\share\\upload\\" . $shopid . "\\" . $roid . "\\" . "dvi" . "\\" . $fname;
        if (substr($fname, -3) != 'pdf') {
            smart_resize_image($newFilePath, null, 1200, 1200, true, $resizeFilePath, true, false, 75);
        }

        $publicPath = "/sbp/upload/$shopid/$roid/dvi/" . $fname;

        $fname = $shopid . "/BossInspect/$roid/$fname";
        $sbp_bucket->add_file_from_path($resizeFilePath, $fname);
        //$publicPath = "/src/private/integrations/sbp_bucket/file.php?key=" . $fname;
        $publicPath = sbp_bucket::public_url.$fname;

        echo $publicPath;
    }
}

/**
 * easy image resize function
 * @param  $file - file name to resize
 * @param  $string - The image data, as a string
 * @param  $width - new image width
 * @param  $height - new image height
 * @param  $proportional - keep image proportional, default is no
 * @param  $output - name of the new file (include path if needed)
 * @param  $delete_original - if true the original image will be deleted
 * @param  $use_linux_commands - if set to true will use "rm" to delete the image, if false will use PHP unlink
 * @param  $quality - enter 1-100 (100 is best quality) default is 100
 * @param  $grayscale - if true, image will be grayscale (default is false)
 * @return boolean|resource
 */
function smart_resize_image($file,
                            $string = null,
                            $width = 0,
                            $height = 0,
                            $proportional = false,
                            $output = 'file',
                            $delete_original = true,
                            $use_linux_commands = false,
                            $quality = 100,
                            $grayscale = false
)
{

    if ($height <= 0 && $width <= 0) return false;
    if ($file === null && $string === null) return false;

    # Setting defaults and meta
    $info = $file !== null ? getimagesize($file) : getimagesizefromstring($string);
    $image = '';
    $final_width = 0;
    $final_height = 0;
    list($width_old, $height_old) = $info;
    $cropHeight = $cropWidth = 0;

    if (empty($info)) return;

    # Calculating proportionality
    if ($proportional) {
        if ($width == 0) $factor = $height / $height_old;
        elseif ($height == 0) $factor = $width / $width_old;
        else                    $factor = min($width / $width_old, $height / $height_old);

        $final_width = round($width_old * $factor);
        $final_height = round($height_old * $factor);
    } else {
        $final_width = ($width <= 0) ? $width_old : $width;
        $final_height = ($height <= 0) ? $height_old : $height;
        $widthX = $width_old / $width;
        $heightX = $height_old / $height;

        $x = min($widthX, $heightX);
        $cropWidth = ($width_old - $width * $x) / 2;
        $cropHeight = ($height_old - $height * $x) / 2;
    }

    # Loading image to memory according to type
    switch ($info[2]) {
        case IMAGETYPE_JPEG:
            $file !== null ? $image = imagecreatefromjpeg($file) : $image = imagecreatefromstring($string);
            break;
        case IMAGETYPE_GIF:
            $file !== null ? $image = imagecreatefromgif($file) : $image = imagecreatefromstring($string);
            break;
        case IMAGETYPE_PNG:
            $file !== null ? $image = imagecreatefrompng($file) : $image = imagecreatefromstring($string);
            break;
        default:
            return false;
    }

    # Making the image grayscale, if needed
    if ($grayscale) {
        imagefilter($image, IMG_FILTER_GRAYSCALE);
    }

    # This is the resizing/resampling/transparency-preserving magic
    $image_resized = imagecreatetruecolor($final_width, $final_height);
    if (($info[2] == IMAGETYPE_GIF) || ($info[2] == IMAGETYPE_PNG)) {
        $transparency = imagecolortransparent($image);
        $palletsize = imagecolorstotal($image);

        if ($transparency >= 0 && $transparency < $palletsize) {
            $transparent_color = imagecolorsforindex($image, $transparency);
            $transparency = imagecolorallocate($image_resized, $transparent_color['red'], $transparent_color['green'], $transparent_color['blue']);
            imagefill($image_resized, 0, 0, $transparency);
            imagecolortransparent($image_resized, $transparency);
        } elseif ($info[2] == IMAGETYPE_PNG) {
            imagealphablending($image_resized, false);
            $color = imagecolorallocatealpha($image_resized, 0, 0, 0, 127);
            imagefill($image_resized, 0, 0, $color);
            imagesavealpha($image_resized, true);
        }
    }
    imagecopyresampled($image_resized, $image, 0, 0, $cropWidth, $cropHeight, $final_width, $final_height, $width_old - 2 * $cropWidth, $height_old - 2 * $cropHeight);


    # Taking care of original, if needed
    if ($delete_original) {
        if ($use_linux_commands) exec('rm ' . $file);
        elseif (file_exists($file)) @unlink($file);
    }

    # Preparing a method of providing result
    switch (strtolower($output)) {
        case 'browser':
            $mime = image_type_to_mime_type($info[2]);
            header("Content-type: $mime");
            $output = NULL;
            break;
        case 'file':
            $output = $file;
            break;
        case 'return':
            return $image_resized;
            break;
        default:
            break;
    }

    # Writing image according to type to the output destination and image quality
    switch ($info[2]) {
        case IMAGETYPE_GIF:
            imagegif($image_resized, $output);
            break;
        case IMAGETYPE_JPEG:
            imagejpeg($image_resized, $output, $quality);
            break;
        case IMAGETYPE_PNG:
            $quality = 9 - (int)((0.9 * $quality) / 10.0);
            imagepng($image_resized, $output, $quality);
            break;
        default:
            return false;
    }

    return true;
}

?>
<?php if (isset($conn)) {
    mysqli_close($conn);
} ?>