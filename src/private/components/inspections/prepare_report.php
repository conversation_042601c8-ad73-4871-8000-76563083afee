<?php
$stmt = "select username from apilogin where shopid = ? and companyname='prodemand'";

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $_COOKIE['shopid']);
    $query->execute();
    $query->bind_result($pdusername);
    $query->fetch();
    $query->close();
}

$stmt = "select motor from settings where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $_COOKIE['shopid']);
    $query->execute();
    $query->bind_result($motor);
    $query->fetch();
    $query->close();
}

$rlstmt = "SELECT uid FROM repairlinkshops where shopid = ?";
if($rlquery = $conn->prepare($rlstmt)){
    $rlquery->bind_param("s", $shopid);
    $rlquery->execute();
    $rlquery->bind_result($rluid);
    $rlquery->fetch();
    $rlquery->close();
}
$complaintcats = array(array('val' => 'none', 'text' => 'No Category'));
$rlstmt = "SELECT UCASE(catname) as category FROM complaintcats where shopid = ? AND catname != '' ORDER BY catname ASC";
if($rlquery = $conn->prepare($rlstmt)){
    $rlquery->bind_param("s", $shopid);
    $rlquery->execute();
    $rlquery->bind_result($category);
    while($rlquery->fetch()){
        $complaintcats[] = array(
                'val' => $category,
                'text' => $category,
        );
    }
    $rlquery->close();
}

if ($_COOKIE['empid'] == "Admin")
{
    $partsordering = "yes";
}
else
{
    $stmt = "select lower(partsordering) from employees where shopid = '$shopid' and id = ".$_COOKIE['empid'];
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $query->bind_result($partsordering);
        $query->fetch();
        $query->close();
    }
}


if (!empty($pdusername) && ($plan == 'gold' || $plan == 'platinum' || $plan == 'premier' || $plan == 'premier plus')) $showpd = "yes"; else $showpd = "no";

require(COMPONENTS_PRIVATE_PATH . DS . "inspections" . DS . "inspection_navbar.php");
?>

<div id="dvi-inspection-prepare" v-cloak class="container-fluid inspection-prepare">
    <div class="mt-2 d-flex justify-content-center" v-if="!report">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    <div class="row gx-1" v-else>
        <div class="col-xl-3 col-md-12">
            <div class="card">
                <div class="card-body">
                    <sbp-dvi-report-quote :key="quoteKey" :show-employee-photo="showEmployeePhoto"
                                          assets-path="<?= IMAGE ?>"
                                          :report="report"></sbp-dvi-report-quote>
                </div>
            </div>
        </div>
        <div class="col-xl-9 col-md-12" style="height: calc(100vh - 110px); overflow-y: auto;">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6>Reason for Today's Visit</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="dvi-estimated-completed-checkbox"
                                   :checked="report.estimateComplete" @input="handleChangeDVIEstimate"/>
                            <label class="form-check-label" for="dvi-estimated-completed-checkbox">Inspection
                                Completed</label>
                        </div>
                    </div>

                    <sbp-dvi-report-customer-request
                            :visit-reason="report.visit_reason"
                            :request="report.customer_request"
                            :request-available="report.customer_request_available"
                            :images="report.customerRequestImages"
                            @input="handleCustomerRequestChange"
                    ></sbp-dvi-report-customer-request>

                    <div class="w-100 p-4 d-flex justify-content-end">
                        <button class="btn btn-sm btn-primary" @click="window.bus.$emit('addPartsToAll', null)">
                            <i class="fa fa-cogs" aria-hidden="true"></i> Add Parts to all
                        </button>
                    </div>

                    <h6 class="mt-4" v-if="criticalFindings.length>0 || warningFindings.length>0">You have
                        {{ criticalFindings.length + warningFindings.length }} items that require attention</h6>
                    <h6 class="mt-4" v-if="criticalFindings.length>0">You have {{ criticalFindings.length }} items that
                        require attention marked as BAD</h6>
                    <sbp-dvi-report-finding-input
                            :show-tax-and-fees="showDviTaxesFees"
                            :roid="report.roid"
                            :complaint-categories="<?= htmlspecialchars(json_encode($complaintcats), ENT_QUOTES, 'UTF-8') ?>"
                            status="critical"
                            :finding-input="criticalFinding"
                            :class="`${index > 0 ? 'mt-2' : ''}`"
                            showpd="<?= $showpd ?>"
                            showpartsordering="<?= $partsordering ?>"
                            @input="handleChange(criticalFinding, ...arguments)"
                            @add-motor-pl="handleMotorOpen('<?= $motor ?>',report.VIN, ...arguments)"
                            @show-pd="handlePDOpen(report.VIN, ...arguments)"
                            @add-part="handleAddPartOpen"
                            @add-labor="handleAddLaborOpen"
                            @add-sublet="handleAddSubletOpen"
                            @show-canned="handleCannedOpen"
                            @change-category="handleCategoryChange"
                            @show-partsordering="handlePartsOrderingOpen"
                            @edit-plitem="handleEditPl(criticalFinding,...arguments)"
                            @delete-plitem="handleDeleteConfirm(criticalFinding,...arguments)"
                            v-for="(criticalFinding, index) in criticalFindings"
                            v-bind:key="criticalFinding.id"
                    ></sbp-dvi-report-finding-input>

                    <div class="mt-2" v-if="criticalFindings.length > 0 && warningFindings.length > 0"></div>

                    <h6 class="mt-4" v-if="warningFindings.length>0">You have {{ warningFindings.length }} items that
                        require attention marked as WARNING</h6>

                    <sbp-dvi-report-finding-input
                            :show-tax-and-fees="showDviTaxesFees"
                            :roid="report.roid"
                            :complaint-categories="<?= htmlspecialchars(json_encode($complaintcats), ENT_QUOTES, 'UTF-8') ?>"
                            status="warning"
                            :finding-input="warningFinding"
                            :class="`${index > 0 ? 'mt-2' : ''}`"
                            showpd="<?= $showpd ?>"
                            showpartsordering="<?= $partsordering ?>"
                            @input="handleChange(warningFinding, ...arguments)"
                            @add-motor-pl="handleMotorOpen('<?= $motor ?>',report.VIN, ...arguments)"
                            @show-pd="handlePDOpen(report.VIN, ...arguments)"
                            @add-part="handleAddPartOpen"
                            @add-labor="handleAddLaborOpen"
                            @add-sublet="handleAddSubletOpen"
                            @show-canned="handleCannedOpen"
                            @change-category="handleCategoryChange"
                            @show-partsordering="handlePartsOrderingOpen"
                            @edit-plitem="handleEditPl(warningFinding,...arguments)"
                            @delete-plitem="handleDeleteConfirm(warningFinding,...arguments)"
                            v-for="(warningFinding, index) in warningFindings"
                            v-bind:key="warningFinding.id"
                    ></sbp-dvi-report-finding-input>

                    <h6 class="mt-4" v-if="goodFindings && goodFindings.length > 0">Below are the Inspection items that
                        checked GOOD</h6>

                    <sbp-dvi-report-finding-input
                            :show-tax-and-fees="showDviTaxesFees"
                            :roid="report.roid"
                            :complaint-categories="<?= htmlspecialchars(json_encode($complaintcats), ENT_QUOTES, 'UTF-8') ?>"
                            status="success"
                            :finding-input="goodFinding"
                            :class="`${index > 0 ? 'mt-2' : ''}`"
                            @input="handleChange(goodFinding, ...arguments)"
                            v-for="(goodFinding, index) in goodFindings"
                            v-bind:key="goodFinding.id"
                    ></sbp-dvi-report-finding-input>
                </div>
            </div>
        </div>
    </div>

    <sbp-dvi-image-editor-provider ref="provider"></sbp-dvi-image-editor-provider>

    <sbp-modal-iframe ref="motor-modal" :url="iframeUrl" :modalXxl="true" :show-close="false">
        <template #footer>
            <sbp-button color="success" icon="save" @click="handleCloseMotor" data-dismiss="modal">Save</sbp-button>
        </template>
    </sbp-modal-iframe>

    <sbp-modal-iframe ref="pd-modal" id="pd-modal" :url="iframeUrlPD" :modalXxl="true" :show-close="false">
    </sbp-modal-iframe>

    <sbp-modal-base id="part-edit-modal" ref="part-edit-modal" size="lg" title="Edit Part">
        <template #body>
            <div class="mb-3" v-if="usepartsmatrix=='yes'">
                <label class="form-label" for="overridematrix">Override Matrix Price</label>
                <select id="overridematrix" aria-label="Matrix" class="form-select" v-model="plData.overridematrix">
                    <option value="yes">Yes</option>
                    <option value="no" selected>No</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label" for="partnum">Part #</label>
                <input type="text" id="partnum" class="form-control" v-model="plData.partnumber"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="partdesc">Description</label>
                <input type="text" id="partdesc" class="form-control" v-model="plData.desc"/>
            </div>
            <div class="mb-3" v-if="usepartsmatrix=='yes'">
                <label class="form-label" for="matrixcategory">Matrix Category</label>
                <select id="matrixcategory" aria-label="Matrix" class="form-select" @change="handleEditPartCalc"
                        v-model="plData.matrix">
                    <option value="">Select</option>
                    <option v-for="cat in partcatsdis" :key="cat.category">{{ cat.category }}</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label" for="partqty">Quantity</label>
                <input type="text" id="partqty" class="form-control" v-model="plData.qty"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="partcost">Cost</label>
                <input type="text" id="partcost" class="form-control" v-model="plData.cost" @blur="handleEditPartCalc"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="partprice">Price</label>
                <input type="text" id="partprice" class="form-control" v-model="plData.price"/>
            </div>
        </template>
        <template #footer>
            <sbp-button color="success" icon="save" :loading="savePL" @click="handleSavePart">Save</sbp-button>
        </template>
    </sbp-modal-base>

    <input type="hidden" id="addinput">

    <sbp-modal-base id="part-add-modal" ref="part-add-modal" size="lg" title="Add Part">
        <template #body>
            <div class="mb-3" v-if="usepartsmatrix=='yes'">
                <label class="form-label" for="addmatrixprice">Override Matrix Price</label>
                <select id="addmatrixprice" aria-label="Matrix" class="form-select">
                    <option value="yes">Yes</option>
                    <option value="no" selected>No</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addpartnum">Part #</label>
                <div>
                    <input type="text" id="addpartnum" class="form-control" v-model="partkey" @keyup="handlePartSearch"
                           autocomplete="off"/>
                    <div class="panel-footer" @mouseleave="search_data={}" v-if="search_data.length"
                         style="max-height:300px;width: 90%;overflow: auto;padding:1%;background-color:#f5f5f5;border-bottom-right-radius:3px;border-bottom-left-radius:3px;position: absolute;z-index: 1;">
                        <ul class="list-group" style="padding-left:0;font-size:12px;">
                            <a href="#" class="list-group-item" v-for="data in search_data"
                               @click="handlePartSelect(data)">{{ data.type + " | " + data.partnumber + " | " + data.partdesc + " | " + data.partcost + " | " + data.partprice
                                }}</a>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addpartdesc">Description</label>
                <input type="text" id="addpartdesc" class="form-control"/>
            </div>
            <div class="mb-3" v-if="usepartsmatrix=='yes'">
                <label class="form-label" for="addmatrixcategory">Matrix Category</label>
                <select id="addmatrixcategory" aria-label="Matrix" class="form-select" @change="handleAddPartCalc">
                    <option value="">Select</option>
                    <option v-for="cat in partcatsdis" :key="cat.category">{{ cat.category }}</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addpartqty">Quantity</label>
                <input type="text" id="addpartqty" class="form-control"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addpartcost">Cost</label>
                <input type="text" id="addpartcost" class="form-control" @blur="handleAddPartCalc"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addpartprice">Price</label>
                <input type="text" id="addpartprice" class="form-control"/>
            </div>
        </template>
        <template #footer>
            <sbp-button color="success" icon="save" :loading="savePL"
                        @click="handleSavePartCall">Add
            </sbp-button>
        </template>
    </sbp-modal-base>

    <sbp-modal-base id="labor-edit-modal" ref="labor-edit-modal" size="lg" title="Edit Labor">
        <template #body>
            <div class="mb-3">
                <label class="form-label" for="labortech">Technician</label>
                <select id="labortech" class="form-select" aria-label="Tech" v-model="plData.tech">
                    <option v-for="tech in techs" :key="tech.id">{{ tech.EmployeeLast + ', ' + tech.EmployeeFirst}}
                    </option>
                </select>
            </div>
            <div>
                <label class="form-label" for="labordesc" style="position:relative; top: 39px;">Labor Description</label>
                <sbp-ai-writing-tool
                    v-model="plData.desc"
                    label=""
                    rows="3"
                    key="labor-ai-tool"
                    ref="laborAiTool"
                    >
                </sbp-ai-writing-tool>
            </div>
            
            <div class="mb-3">
                <label class="form-label" for="labormatrix">Labor Matrix</label>
                <select id="labormatrix" aria-label="Matrix" v-model="plData.matrix" class="form-select">
                    <option v-for="cat in catsdis" :value="cat.category" :selected="cat.category==plData.matrix"
                            :key="cat.id">{{ cat.category }}
                    </option>
                    <option value='none' :selected="plData.matrix=='none' || plData.matrix==''">NONE</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label" for="laborqty">Hours</label>
                <input type="text" id="laborqty" class="form-control" v-model="plData.qty"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="laborrate">Hourly Rate</label>
                <select id="laborrate" aria-label="Rate" class="form-select" :pldrate="plData.rate.label">
                    <option v-for="rate in rates" :value="rate.label + ':;' + rate.rate"
                            :selected="rate.rate== parseFloat(plData.rate.rate) && rate.label.toLowerCase() == plData.rate.label.toLowerCase()" :key="rate.label">
                        {{ rate.label + " - " + rate.rate }}
                    </option>
                </select>
            </div>
        </template>
        <template #footer>
            <sbp-button color="success" icon="save" :loading="savePL" @click="handleSaveLabor">Save</sbp-button>
        </template>
    </sbp-modal-base>

    <sbp-modal-base id="labor-add-modal" ref="labor-add-modal" size="lg" title="Add Labor">
        <template #body>
            <div class="mb-3">
                <label class="form-label" for="addlabortech">Technician</label>
                <select id="addlabortech" class="form-select" aria-label="Tech" v-model="laborData.tech">
                    <option v-for="tech in techs" :key="tech.id">{{ tech.EmployeeLast + ', ' + tech.EmployeeFirst}}
                    </option>
                </select>
            </div>
            <div>
                <label class="form-label" for="addlabordesc" style="position:relative; top: 39px;">Labor Description</label>
                <sbp-ai-writing-tool
                    v-model="laborData.description"
                    label="Labor Description"
                    rows="3"
                    :key="laborData.tech"
                    key="labor-ai-tool"
                    ref="laborAiTool"
                    >
                </sbp-ai-writing-tool>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addlabormatrix">Labor Matrix</label>
                <select v-model="laborData.laborMatrix" id="addlabormatrix" aria-label="Matrix" class="form-select">
                    <option v-for="cat in catsdis" :value="cat.category" :key="cat.id">{{ cat.category }}</option>
                    <option value='none'>NONE</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addhours">Hours</label>
                <input v-model="laborData.hours" type="text" id="addhours" class="form-control"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addlaborrate">Hourly Rate</label>
                <select v-model="laborData.hourlyRate" id="addlaborrate" aria-label="Rate" class="form-select">
                    <option v-for="rate in rates" :value="rate.label + ':;' + rate.rate" :key="rate.label">
                        {{ rate.label + " - " + rate.rate }}
                    </option>
                </select>
            </div>
        </template>
        <template #footer>
            <sbp-button color="success" icon="save" :loading="savePL" @click="handleAddLabor">Add</sbp-button>
        </template>
    </sbp-modal-base>

    <sbp-modal-base id="sublet-add-modal" ref="sublet-add-modal" size="lg" title="Add Sublet">
        <template #body>
            <div class="mb-3">
                <label class="form-label" for="addsubletdesc">Description</label>
                <input type="text" id="addsubletdesc" class="form-control"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addsubletcost">Cost</label>
                <input type="text" id="addsubletcost" class="form-control"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addsubletprice">Price</label>
                <input type="text" id="addsubletprice" class="form-control"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addsubletinv">Invoice Number</label>
                <input type="text" id="addsubletinv" class="form-control"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addsubletsup">Supplier</label>
                <input type="text" id="addsubletsup" class="form-control"/>
            </div>
        </template>
        <template #footer>
            <sbp-button color="success" icon="save" :loading="savePL" @click="handleAddSublet">Add</sbp-button>
        </template>
    </sbp-modal-base>

    <sbp-modal-base id="sublet-edit-modal" ref="sublet-edit-modal" size="lg" title="Edit Sublet">
        <template #body>
            <div class="mb-3">
                <label class="form-label" for="addsubletdesc">Description</label>
                <input type="text" id="subletdesc" class="form-control" v-model="plData.desc"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addsubletcost">Cost</label>
                <input type="text" id="subletcost" class="form-control" v-model="plData.cost"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addsubletprice">Price</label>
                <input type="text" id="subletprice" class="form-control" v-model="plData.price"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addsubletinv">Invoice Number</label>
                <input type="text" id="subletinv" class="form-control" v-model="plData.partnumber"/>
            </div>
            <div class="mb-3">
                <label class="form-label" for="addsubletsup">Supplier</label>
                <input type="text" id="subletsup" class="form-control" v-model="plData.tech"/>
            </div>
        </template>
        <template #footer>
            <sbp-button color="success" icon="save" :loading="savePL" @click="handleSaveSublet">Save</sbp-button>
        </template>
    </sbp-modal-base>

    <sbp-modal-base id="pl-delete-modal" ref="pl-delete-modal" :show-close="false">
        <template #body>
            Do you really want to delete this {{ activeData.type }} from this inspection and hence the RO ?
        </template>
        <template #footer>
            <sbp-button color="success" icon="thumbs-up" @click="handleDeletePl" data-dismiss="modal">Yes</sbp-button>
            <sbp-button color="danger" icon="thumbs-down" data-dismiss="modal">No</sbp-button>
        </template>
    </sbp-modal-base>

    <sbp-modal-base id="bump-modal" ref="bump-modal" size="xl" :show-close="false">
        <template #body>
            <div class="float-right">
                <sbp-button color="danger" :loading="unsavePL" @click="handlecloseWOSaving">Close WITHOUT Saving
                </sbp-button>&nbsp;<sbp-button color="success" :loading="savePL" @click="handleSave">Save and Close
                </sbp-button>
            </div>
            <table class="table table-hover table-striped">
                <thead>
                <tr>
                    <th>P/L</th>
                    <th>Part# / Tech</th>
                    <th>Description&nbsp;</th>
                    <th class="text-left">Qty / Hours&nbsp;/ Rate / Matrix</th>
                    <th class="text-center">Save</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="data in motorData.parts" :key="data.id">
                    <td>PART</td>
                    <td><input type="text" style="max-width:175px;" :id="'partnumber'+data.id"
                               :value="data.partnumber.toUpperCase()"></td>
                    <td><input type="text" :id="'partdesc'+data.id" :value="data.partdesc.toUpperCase()"></td>
                    <td class="text-left"><input type="text" style="width:50px;" :id="'pqty'+data.id" value="1"> @
                        <input type="text" style="width:150px;" :id="'pprice'+data.id"
                               :value="data.partprice.toFixed(2)"></td>
                    <td class="text-center"><input style="width:20px;height:20px" checked="checked" class="motorcheck"
                                                   :id="data.id" type="checkbox" value="PART"/></td>
                </tr>

                <tr v-for="data in motorData.labor" :key="data.id">
                    <td>LABOR</td>
                    <td><select style="max-width:200px;" :id="'tech'+data.id">
                            <option v-for="tech in motorData.techs" :key="tech.id">
                                {{ tech.EmployeeLast.toUpperCase() + ", " + tech.EmployeeFirst.toUpperCase() }}
                            </option>
                        </select></td>
                    <td><input type="text" :id="'labordesc'+data.id" :value="data.labordesc.toUpperCase()"></td>
                    <td class="text-left"><input type="text" style="width:50px;" class="laborhours"
                                                 :id="'laborhours'+data.id" :value="data.laborhours"> @ <select
                                v-if="motorData.rates.length>0" :id="'laborrate'+data.id">
                            <option v-for="rt in motorData.rates" :value="rt.label + ':;' + rt.rate" :key="rt.label">
                                {{ rt.label + " - " + rt.rate }}
                            </option>
                        </select><input type="text" v-else :id="'laborrate'+data.id"> / <select :id="'lmcat'+data.id">
                            <option v-for="cat in motorData.cats" :key="cat.category">{{ cat.category.toUpperCase()}}
                            </option>
                            <option>NONE</option>
                        </select></td>
                    <td class="text-center"><input style="width:20px;height:20px" checked="checked" class="motorcheck"
                                                   :id="data.id" type="checkbox" value="LABOR"/></td>
                </tr>
                </tbody>
            </table>
            <div class="d-flex justify-content-center mt-5 mb-3" v-if="motorData.labor && motorData.labor.length>0">
                <button class="btn btn-primary" @click="handleBump(10)">Bump Hours 10%</button>&nbsp;<button
                        class="btn btn-primary" @click="handleBump(15)">Bump Hours 15%
                </button>&nbsp;<button class="btn btn-primary" @click="handleBump(20)">Bump Hours 20%</button>
            </div>

        </template>
    </sbp-modal-base>

    <sbp-modal-base id="canned-modal" ref="canned-modal" size="xl" title="Add Canned Job" :scrollable="true"
                    :show-close="true">
        <template #body>

            <div class="row px-3 mb-3">
                <input class="form-control" style="text-transform:uppercase" placeholder="Search Canned Jobs"
                       @keyup="handleJobSearch" id="srch" name="srch" autocomplete="off">
            </div>

            <div :id="'row-'+cannedjob.id" :data-cj="cannedjob" class="alert alert-info"
                 @mouseover="handleCannedMouseover" @mouseleave="handleCannedMouseleave" v-for="cannedjob in cannedJobs"
                 :key="cannedjob.id">
                <div class="row cjrow" style="cursor:pointer;" @click="handleCannedClick(cannedjob)">
                    <div class="col-md-8">
                        {{ cannedjob.jobname.toUpperCase() }}
                    </div>
                    <div class="col-md-2 text-right">
                        ${{ cannedjob.displayprice.toFixed(2) }}
                    </div>
                </div>
            </div>

        </template>
    </sbp-modal-base>

    <sbp-modal-base id="canned-hr-modal" ref="canned-hr-modal" size="lg" title="Select Tech / Rate" :show-close="true">
        <template #body>
            <h6 id="canned-hr-name"></h6>
            <form id="cannedform">
                <div class="my-3">
                    <select id="cannedtech" class="form-select" aria-label="Tech">
                        <option v-for="tech in techs" :key="tech.id">{{ tech.EmployeeLast + ', ' + tech.EmployeeFirst
                            }}
                        </option>
                    </select>
                </div>
                <div class="mb-3">
                    <select id="cannedrate" aria-label="Rate" class="form-select">
                        <option v-for="rate in rates" :value="rate.label + ':;' + rate.rate" :key="rate.label">
                            {{ rate.label + " - " + rate.rate }}
                        </option>
                    </select>
                </div>
            </form>
        </template>
        <template #footer>
            <sbp-button color="success" icon="save" :loading="savePL" @click="handleSaveCanned">Save</sbp-button>
        </template>
    </sbp-modal-base>

    <sbp-modal-base id="epicor-modal" ref="epicor-modal" size="lg" title="Epicor Parts Ordering" :show-close="true">
        <template #body>
            <div class="mb-3">
                <label class="form-label" for="epicorsupplier">Select Supplier</label>
                <select id="epicorsupplier" class="form-select sbp-form-control">
                    <option v-for="ep in porder.epicor" :key="ep.suppliername">{{ ep.suppliername.toUpperCase()}}
                    </option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label" for="epicormode">Select Mode</label>
                <select id="epicormode" class="form-select sbp-form-control">
                    <option value="Transfer">BUILD ESTIMATE</option>
                    <option value="Order">ORDER PARTS</option>
                    <option value="TransferAndOrder">BOTH</option>
                </select>
            </div>
        </template>
        <template #footer>
            <sbp-button color="success" @click="launchEpicor">Launch Epicor</sbp-button>
        </template>
    </sbp-modal-base>

    <sbp-modal-base id="nexpart-modal" ref="nexpart-modal" size="lg" title="Nexpart Parts Ordering" :show-close="true">
        <template #body>
            <div class="mb-3">
                <label class="form-label" for="nexpartsupplier">Select Supplier</label>
                <select id="nexpartsupplier" class="form-select sbp-form-control">
                    <option v-for="np in porder.nexpart" :value="np.username+'|'+np.password" :key="np.id">
                        {{ np.desc.toUpperCase() }}
                    </option>
                </select>
            </div>
        </template>
        <template #footer>
            <sbp-button color="success" @click="launchNexpart">Launch Nexpart</sbp-button>
        </template>
    </sbp-modal-base>

    <sbp-modal-base id="parts-ordering-modal" ref="parts-ordering-modal" size="lg" title="Select Parts Ordering System"
                    :show-close="false">
        <template #body>
            <center>
                <div v-if="porder.partstech">
                    <button class="btn btn-danger btn-lg" style="width:300px;" @click="launchPartstech" type="button">
                        PartsTech Parts Ordering
                    </button>
                    <br><br></div>
                <div v-if="porder.epicor && porder.epicor.length">
                    <button class="btn btn-success btn-lg" style="width:300px;" @click="showEpicor" type="button">Epicor
                        Parts Ordering
                    </button>
                    <br><br></div>
                <div v-if="porder.nexpart && porder.nexpart.length">
                    <button class="btn btn-primary btn-lg" style="width:300px;" @click="showNexpart" type="button">
                        Nexpart Parts Ordering
                    </button>
                    <br><br></div>
                <div>
                    <button class="btn btn-warning btn-lg" style="width:300px;" @click="launchWorldpac" type="button">
                        WorldPac Parts Ordering
                    </button>
                    <br><br>
                </div>
                <?php
                if (!empty($rluid)){
                ?>
                <div>
                    <button class="btn btn-info btn-lg" style="width:300px;" @click="launchRepairLink('<?= $rluid ?>')" type="button">
                        RepairLink Ordering
                    </button>
                </div>
                <?php
                }
                ?>
            </center>
        </template>
    </sbp-modal-base>

    <sbp-modal-base id="nomotor-modal" ref="nomotor-modal" size="lg" title="Trying to Access MOTOR Labor Times?">
        <template #body>
            It looks like you’re trying to access a new labor guide for your shop’s account. While you’re not currently
            connected to this service, we’d be more than happy to configure your account and ensure you’re set up for
            success.
            <center><br> To get started, please send a message to <br> <b> <a target="_blank"
                                                                              href="mailto:<EMAIL>?subject=Motor Optin Request for <?= $_COOKIE['shopid'] ?>&body=I would like to schedule my Motor setup."><EMAIL></a>
                </b>.
            </center>
            <br>Our customer success team will reach out to schedule your setup. Thank you!
        </template>
        <template #footer>
            <sbp-button color="success"
                        href="mailto:<EMAIL>?subject=Motor Optin Request for <?= $_COOKIE['shopid'] ?>&body=I would like to schedule my Motor setup.">
                Contact Customer Success
            </sbp-button>
        </template>
    </sbp-modal-base>

</div>
