<div class="breakpoint-listener">
    <div class="sm"></div>
    <div class="md"></div>
    <div class="lg"></div>
    <div class="xl"></div>
    <div class="xxl"></div>
</div>

<style>
    @media print {

        .card {
            box-shadow: none;
        }

        .btn, img, breadcrumb, .select-arrow, .dvi-inspection-sidenav, .no-print-sb, .fa-angle-up {
            display: none !important;
        }

        .card-body {
            font-size: 10pt !important;
        }

        .dvi-inspection-input-panel {
            height: 100% !important;
            margin-left: 0px !important;
        }

        .col-3 {
            width: 100% !important;
        }
    }
</style>

<?php
require(COMPONENTS_PRIVATE_PATH . DS . "inspections" . DS . "inspection_navbar.php");
?>

<div id="dvi-inspection" v-cloak class="flex-grow-1 d-flex flex-column position-relative">
    <div class="mt-2 d-flex justify-content-center" v-if="!inspection">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    <div class="flex-grow-1 d-flex" v-else>
        <div class="position-relative no-print">
            <nav class="sidenav" id="dvi-inspection-sidenav">
                <ul class="sidenav-menu">
                    <li class="sidenav-item" @click="handleClickSidebarItem('vehicle-details')">
                        <h6 class="sidenav-link">Vehicle Details</h6>
                    </li>
                    <li class="sidenav-item" @click="handleClickSidebarItem('reason-for-today-visit')">
                        <h6 class="sidenav-link">Reason for Today's Visit</h6>
                    </li>
                    <li class="sidenav-item" v-for="category in inspection.model.categories" v-bind:key="category.id">
                        <h6 class="sidenav-link">{{ category.name ? category.name : "UNKNOWN" }}</h6>
                        <ul class="sidenav-collapse">
                            <li class="sidenav-item" v-for="section in category.sections" v-bind:key="section.id"
                                @click="handleClickSidebarItem(`${category.id}-${section.id}`)">
                                <span class="sidenav-link">{{ section.name ? section.name : "Unnamed section" }}</span>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </div>
        <div class="dvi-inspection-input-panel flex-grow-1 pl-1 pr-1 pb-2 ">
            <button type="button" class="no-print-sb btn btn-success btn-rounded d-block d-lg-none"
                    style="position: absolute; left: 8px; top: 4px; z-index: 10;" @click="handleToggleSidebar">
                <i class="fas fa-bars"></i>
            </button>
            <div id="vehicle-details" class="card">
                <div class="card-body">
                    <div class="d-flex flex-column align-items-end">
                        <h4>RO# {{inspection.roid}}</h4>
                        <!-- <i class="fa fa-print cursor-pointer no-print" @click="window.print()" title="Print" aria-hidden="true"></i> -->
                    </div>
                    <div class="d-flex flex-column m-auto" style="max-width: 500px;">
                        <img class="img-fluid" src="<?= IMAGE ?>/placeholderCar.png"
                             v-if="!inspection.vehicle_photo_url"/>

                        <img class="img-fluid" :src="`${inspection.vehicle_photo_url}`" v-else/>

                        <sbp-modal-base title="Edit Photo" ref="editorDialog">
                            <template #body>
                                <sbp-imageupload-editor ref="imageEditor"/>
                            </template>
                        </sbp-modal-base>

                        <input class="d-none" type="file" name="file" ref="fileInput" accept="image/*,android/force-camera-workaround" @change="handleFileChange"/>

                        <div class="header-photo-button-container">
                            <button type="button" class="no-print-sb btn btn-primary text-nowrap mt-2"
                                    @click="handleAddPhoto">
                                <i class="fas fa-camera mr-2"></i>
                                Add Photo
                            </button>
                        </div>
                    </div>
                    <div class="row mx-auto">
                        <div class="col row gy-2">
                            <div class="px-0 col-md-6 col-12  form-outline vehicle-detail-input">
                                <input type="text" id="vin-input" class="form-control" :value="inspection.vh_vin"
                                       @input="handleChangeVehicleDetails('vh_vin', $event.target.value)"/>
                                <label class="form-label" for="vin-input">VIN</label>
                            </div>
                            <div class="px-0 col-md-6 col-12  form-outline vehicle-detail-input">
                                <input type="text" id="license-plate-input" class="form-control"
                                       :value="inspection.vh_licnumber"
                                       @input="handleChangeVehicleDetails('vh_licnumber', $event.target.value)"/>
                                <label class="form-label" for="license-plate-input">License Plate</label>
                            </div>
                            <!-- <div class="px-0 col-12 form-outline vehicle-detail-input">
                              <input type="text" id="manufacture-date-input" class="form-control" />
                              <label class="form-label" for="manufacture-date-input">Manufacture Date</label>
                            </div> -->
                            <div class="px-0 col-md-6 col-12 form-outline vehicle-detail-input">
                                <input type="text" id="mileage-in-input" class="form-control"
                                       :value="inspection.vh_miles_in"
                                       @input="handleChangeVehicleDetails('vh_miles_in', $event.target.value)"/>
                                <label class="form-label" for="mileage-in-input">Mileage In</label>
                            </div>
                            <div class="px-0 col-md-6 col-12  form-outline vehicle-detail-input">
                                <input type="text" id="mileage-out-input" class="form-control"
                                       :value="inspection.vh_miles_out"
                                       @input="handleChangeVehicleDetails('vh_miles_out', $event.target.value)"/>
                                <label class="form-label" for="mileage-out-input">Mileage Out</label>
                            </div>

                            <div class="px-0 col-md-6 col-12  form-outline vehicle-detail-input">
                                <input type="text" id="make-model-input" :value="inspection.vehinfo" readonly class="form-control"/>
                                <label class="form-label" for="make-model-input">Year / Make / Model</label>
                            </div>
                            <div class="px-0 col-md-6 col-12  form-outline vehicle-detail-input">
                                <input type="text" id="engine-size-input" class="form-control"
                                       :value="inspection.vh_engine"
                                       @input="handleChangeVehicleDetails('vh_engine', $event.target.value)"/>
                                <label class="form-label" for="engine-size-input">Engine Size</label>
                            </div>
                            <div class="col-md-6 col-12  px-0">
                                <select name="vh_transmission_type" class="select vehicle-detail-select"
                                        :value="inspection.vh_transmission_type">
                                    <option value=""></option>
                                    <option value="automatic">Automatic</option>
                                    <option value="standard">Standard</option>
                                    <option value="cvt">CVT</option>
                                </select>
                                <label class="form-label select-label">Transmission Type</label>
                            </div>
                            <div class="col-md-6 col-12 px-0">
                                <select name="vh_style" class="select vehicle-detail-select"
                                        :value="inspection.vh_style">
                                    <option value=""></option>
                                    <option value="two-door">2 Door</option>
                                    <option value="four-door">4 Door</option>
                                    <option value="hatchback">Hatchback</option>
                                    <option value="van">Van</option>
                                    <option value="truck">Truck</option>
                                    <option value="convertible">Convertible</option>
                                </select>
                                <label class="form-label select-label">Style</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="reason-for-today-visit" class="card mt-4">
                <div class="card-header">Reason for Today's Visit</div>
                <div class="card-body">
                    <sbp-dvi-customer-request 
                        mode="edit"
                        :reason-for-visit="inspection.visit_reason"
                        :note="inspection.customer_request"
                        :images="inspection.customer_request_images"
                        @set-note="handleSetCustomerRequest"
                        @set-visit-reason="handleSetVisitReason"
                        @add-image="handleAddCustomerRequestImage"
                        @delete-image="handleDeleteCustomerRequestImage">
                    </sbp-dvi-customer-request>
                </div>
            </div>

            <div class="card mt-4" v-for="category in inspection.model.categories" v-bind:key="category.id">
                <div class="card-header">{{ category.name }}</div>
                <div class="card-body">
                    <badger-accordion
                            :options="{openHeadersOnLoad: [...range(0,category.sections.length-1)],openMultiplePanels:true,}"
                            :icons="{opened: '<i class=' + quote + 'fas fa-angle-up' + quote + '></i>', closed: '<i class=' + quote + 'fas fa-angle-down' + quote + '></i>'}">
                        <badger-accordion-item :opened="true" v-for="(section, index) in category.sections"
                                               v-bind:key="section.id">

                            <template slot="header">
                                <div class="ml-0" :id="category.id + '-' + section.id">
                                    <h5 class="text-body">{{section.name}}</h5>
                                </div>
                            </template>

                            <template slot="content">
                                <div class="ml-2 mt-2">
                                    <sbp-dvi-inspection-item v-for="(item, ind) in section.items" v-bind:key="ind"
                                                             class="px-0 col-12" :section-type="section.section_type"
                                                             :item="item"
                                                             :setting-multiple-findings="settingMultipleFindings"
                                                             @change-finding-input="handleItemFindingInputChange(item, ...arguments)"
                                                             @set-multiple-findings="handleItemSetMultipleFindings(item, ...arguments)"
                                                             @change-measurement-input="handleItemMeasurementInputChange(item, ...arguments)"
                                                             @add-finding-photo="handleAddFindingPhoto(item, ...arguments)"
                                                             @add-finding-video="handleAddFindingVideo(item, ...arguments)"
                                                             @delete-finding-photo="handleDeleteFindingPhoto(item, ...arguments)"
                                                             @add-tech-notes="handleAddTechNotes(item, ...arguments)"
                                                             @add-ro-images="handleAddRoImages(item, ...arguments)"></sbp-dvi-inspection-item>
                                </div>
                                <span v-if="section.section_type=='finding'"
                                      @click="addCustomOpen(category.id,section.id)" class="cursor-pointer"><i
                                            class="fas fa-plus"></i> Add Custom</span>
                            </template>
                        </badger-accordion-item>
                    </badger-accordion>
                </div>
            </div>
        </div>
    </div>

    <sbp-dvi-image-editor-provider :upload-url="uploadUrl" ref="provider"></sbp-dvi-image-editor-provider>
    <sbp-modal-base ref="custom-item-modal" size="sm" title="Add Custom Item">
        <template #body>
            <div class="form-outline mb-3">
                <input type="text" id="customitem" class="form-control"/>
                <label class="form-label" for="customitem">Item Name</label>
            </div>
        </template>
        <template #footer>
            <sbp-button color="success" icon="save" :loading="customItem.loading" @click="handleAddCustom">Add
            </sbp-button>
        </template>
    </sbp-modal-base>
</div>