<!DOCTYPE html>
<?php
require CONN;
$shopid = $_COOKIE['shopid'];
include INTEGRATIONS_PATH . "/sbp_bucket/sbp_bucket.php";
?>
<!--[if IE 9]>
<html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus">
<!--<![endif]-->

<head>
    <meta charset="utf-8">

    <title><?= getPageTitle() ?></title>

    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon() ?>' type='image/x-icon'/>
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">

    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH . "/shared/analytics.php"); ?>
<!-- Page Container -->
<!--
        Available Classes:

        'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

        'sidebar-l'                  Left Sidebar and right Side Overlay
        'sidebar-r'                  Right Sidebar and left Side Overlay
        'sidebar-mini'               Mini hoverable Sidebar (> 991px)
        'sidebar-o'                  Visible Sidebar by default (> 991px)
        'sidebar-o-xs'               Visible Sidebar by default (< 992px)

        'side-overlay-hover'         Hoverable Side Overlay (> 991px)
        'side-overlay-o'             Visible Side Overlay by default (> 991px)

        'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

        'header-navbar-fixed'        Enables fixed header
    -->
<div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">
    <!-- Side Overlay-->
    <aside id="side-overlay">
        <!-- Side Overlay Scroll Container -->
        <div id="side-overlay-scroll">
            <!-- Side Header -->
            <div class="side-header side-content">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default pull-right" type="button" data-toggle="layout"
                        data-action="side_overlay_close">
                    <i class="fa fa-times"></i>
                </button>
                <span>
                        <img class="img-avatar img-avatar32" src="<?= IMAGE ?>/avatars/avatar10.jpg" alt="">
                        <span class="font-w600 push-10-l">Walter Fox</span>
                    </span>
            </div>
            <!-- END Side Header -->

        </div>
        <!-- END Side Overlay Scroll Container -->
    </aside>
    <!-- END Side Overlay -->

    <!-- Sidebar -->
    <nav id="sidebar">
        <!-- Sidebar Scroll Container -->
        <div id="sidebar-scroll">
            <!-- Sidebar Content -->
            <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
            <div class="sidebar-content">
                <!-- Side Header -->
                <div class="side-header side-content bg-white-op">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button"
                            data-toggle="layout" data-action="sidebar_close">
                        <i class="fa fa-times"></i>
                    </button>
                    <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                        <i class="text-primary">
                            <?php getLogo() ?></i>
                        <span class="h4 font-w600 sidebar-mini-hide">
                            </span>
                    </a>
                </div>
                <!-- END Side Header -->

                <!-- Side Content -->
                <div class="side-content-sbp-settings side-content">
                    <?php require("menu.php"); ?>
                </div>

                <!-- END Side Content -->
            </div>
            <!-- Sidebar Content -->
        </div>
        <!-- END Sidebar Scroll Container -->
    </nav>
    <!-- END Sidebar -->

    <!-- Header -->
    <header id="header-navbar" class="content-mini content-mini-full">
        <!-- Header Navigation Right -->
        <ul class="nav-header pull-right">
            <li>
                <div id="shopnotice">
                    <?php echo $_COOKIE['shopname'] . " #" . $shopid . "<br>" . $_COOKIE['username'] . '<a class="btn btn-primary btn-sm btn-logoff" href="' . COMPONENTS_PUBLIC . '/login/logoff.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Logoff</span></a>'; ?>
                </div>
            </li>
        </ul>
        <!-- END Header Navigation Right -->

        <!-- Header Navigation Left -->

        <ul class="nav-header pull-left">
            <li class="hidden-md hidden-lg">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                    <i class="fa fa-navicon"></i>
                </button>
            </li>
            <li class="hidden-xs hidden-sm">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" id="close-sidebar"
                        data-action="sidebar_mini_toggle" type="button">
                    <i class="fa fa-bars"></i>
                </button>
            </li>
            <li>
                <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                <button style="display:none" class="btn btn-default pull-right" data-toggle="modal"
                        data-target="#apps-modal" type="button">
                    <i class="si si-grid"></i>
                </button>
            </li>
            <li class="visible-xs">
                <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search"
                        data-class="header-search-xs-visible" type="button">
                    <i class="fa fa-search"></i>
                </button>
            </li>
            <li>

            </li>
        </ul>

        <!-- END Header Navigation Left -->
    </header>
    <!-- END Header -->

    <!-- Main Container -->
    <main class="container-fluid" id="main-container">
        <div class="table-responsive">
            <br>
            Click an Employee to edit. Only active employees are shown. Click the "Show All" button to the right to show
            all employees.
            <div style="float:right">
                <?php if($forcelogout){?><button onclick="forceLogout()" type="button" class="btn btn-primary btn-md">Force Logout</button><?php }?>

                <?php
                if (!isset($_GET['showall']) || (isset($_GET['showall']) && $_GET['showall'] == 'n')) {
                    echo "<button onclick=\"location.href='employees.php?showall=y'\" class=\"btn btn-info btn-md\" id=\"show\">Show All</button>";
                } elseif (isset($_GET['showall']) && $_GET['showall'] == 'y') {
                    echo "<button onclick=\"location.href='employees.php?showall=n'\" class=\"btn btn-info btn-md\" id=\"hide\">Hide Inactive</button>";
                }
                ?>
                <button onclick="location.href='employeefiles/employee-add.php'" class="btn btn-warning btn-md">Add New
                    Employee
                </button>
            </div>
            <table class="table table-bordered table-condensed table-hover table-header-bg">
                <thead>
                <tr class="header">
                    <?php if($forcelogout){?><th width="2%"><input type="checkbox" onclick="checkAll()" id="checkall" name="checkall"></th><?php }?>
                    <th>Employee</th>
                    <th>Job Description&nbsp;</th>
                    <th>Phone&nbsp;</th>
                    <th>Mode&nbsp;</th>
                    <th>Active&nbsp;</th>
                </tr>
                </thead>
                <tbody>
                <?php
                if (isset($_GET['showall'])) {
                    if ($_GET['showall'] == "y") {
                        $stmt = "select photo,employeefirst,employeelast,jobdesc,employeephone,mode,active,id from employees where shopid = '" . $shopid . "' order by employeelast asc";
                    } else {
                        $stmt = "select photo,employeefirst,employeelast,jobdesc,employeephone,mode,active,id from employees where active = 'yes' and shopid = '" . $shopid . "' order by employeelast asc";
                    }
                } else {
                    $stmt = "select photo,employeefirst,employeelast,jobdesc,employeephone,mode,active,id from employees where active = 'yes' and shopid = '" . $shopid . "' order by employeelast asc";
                }
                //echo $stmt;
                $result = $conn->query($stmt);
                //echo $result->num_rows;
                //if ($result->num_rows > 0) {
                // output data of each row
                while ($row = $result->fetch_array()) {

                    ?>
                    <tr>
                        <?php if($forcelogout){?><td><input type="checkbox" class="<?= $_COOKIE['empid']!=$row['id']?'empcheck':''?>" style="margin-left: 3px;vertical-align: middle;" value="<?= $row['id']?>" <?= $_COOKIE['empid']==$row['id']?'disabled':''?>></td><?php }?>
                        <td onclick="location.href='employeefiles/employee-edit.php?id=<?php echo $row['id']; ?>'">
                            <?php
                            $photoUrl = "https://upload.wikimedia.org/wikipedia/commons/a/ac/Default_pfp.jpg";

                            if (!empty($row['photo'])) {
                                $photoUrl = UPLOAD_URL . '/' . $shopid . '/' . $row["photo"];

                                if (strpos($row['photo'], "/") !== false){
                                    $presigned_url = $sbp_bucket->presigned_url($row['photo'], 60);
                                    $photoUrl = $presigned_url;
                                }

                            }
                            ?>
                            <img style="border-radius: 50%; margin-right: 10px;"
                                 src="<?= $photoUrl ?>"
                                 alt="<?= $row['employeefirst'] ?>'s Photo"
                                 width="40px"
                                 height="40px">

                            <?php echo strtoupper($row['employeelast'] . ", " . $row['employeefirst']); ?>&nbsp;
                        </td>
                        <td onclick="location.href='employeefiles/employee-edit.php?id=<?php echo $row['id']; ?>'"><?php echo strtoupper($row['jobdesc']); ?>&nbsp;</td>
                        <td onclick="location.href='employeefiles/employee-edit.php?id=<?php echo $row['id']; ?>'"><?php echo formatPhone(str_replace("-", "", $row['employeephone'])); ?>&nbsp;</td>
                        <td onclick="location.href='employeefiles/employee-edit.php?id=<?php echo $row['id']; ?>'"><?php echo strtoupper($row['mode']); ?>&nbsp;</td>
                        <td onclick="location.href='employeefiles/employee-edit.php?id=<?php echo $row['id']; ?>'"><?php echo strtoupper($row['active']); ?>&nbsp;</td>
                    </tr>
                    <?php
                }
                ?>
                </tbody>
            </table>

        </div>
    </main>
    <!-- END Main Container -->

    <!-- Footer -->
    <!-- END Footer -->
</div>
<!-- END Page Container -->

<!-- Apps Modal -->
<div id="carfaxmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-light">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Carfax Vehicle Lookup</h3>
                </div>
                <div id="vehinfo" class="block-content"></div>
                <div class="block-content">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control" style="padding:20px;" tabindex="1"
                                           type="text" id="cfvin" name="cfvin">
                                    <label for="material-text2">VIN</label>
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control" style="padding:20px;" tabindex="2"
                                           type="text" id="cflic" name="cflic">
                                    <label for="material-text2">License</label>
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <input class="form-control sbp-form-control" style="padding:20px;" tabindex="3"
                                           type="text" id="cfst" name="cfst">
                                    <label for="material-text2">License State</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-info btn-md" type="button" onclick="scanVIN()">SCAN VIN</button>
                <button class="btn btn-md btn-warning" type="button" onclick="addVehicle()">Lookup</button>
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>


<script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
<script src="<?= SCRIPT ?>/tipped.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

<!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
<script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
<script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
<script src="<?= SCRIPT ?>/app.js"></script>
<script src="<?= SCRIPT ?>/sbp-pageresize.js"></script>
<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
<script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
<!-- Page Plugins -->

<!-- Page JS Code
    <script src="assets/js/pages/base_pages_dashboard.js"></script>-->
<script>
    jQuery(function () {
        // Init page helpers (Slick Slider plugin)
        App.initHelpers('slick');
    });

    function hideInactive(type) {

        $('.activetd').each(function () {

            if (type == "hide") {
                if ($(this).html() == "NO") {
                    //console.log("hiding")
                    $(this).closest('tr').hide()
                    $('#showinactive').val("no")
                    $('#hidebutton').hide();
                    $('#showbutton').show();
                }
            } else {
                if ($(this).html() == "NO") {
                    $(this).closest('tr').show()
                    $('#showinactive').val("yes")
                    $('#hidebutton').show();
                    $('#showbutton').hide();
                }
            }
        })

    }

    function forceLogout()
    {
        var checks = []

        $.each($(".empcheck:checked"), function(){
                checks.push($(this).val());
        });

        if(checks.length === 0)
        {
            swal("Please select employee(s) you want to force logout")
            return;
        }

        swal({
            title: "Force Logout",
            text: "This action will force logout the selected employee(s). Are you sure?",
            type: "warning",
            showCancelButton: true,
            confirmButtonClass: "btn-danger",
            confirmButtonText: "Yes",
            closeOnConfirm: true
        }, function () {

                $('#spinner').show()

                ds = "t=forcelogout&shopid=<?php echo $shopid; ?>&empids="+checks.join(",")

                $.ajax({
                    type: "post",
                    url: "employeefiles/employee-action.php",
                    data: ds,
                    success: function () {
                        swal("Force logged out successfully")
                        $('.empcheck').prop('checked',false)
                        $('#checkall').prop('checked',false)
                        $('#spinner').hide()
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        swal("Error in force logout")
                        $('#spinner').hide()
                    }
                });

            });

      if($('.empcheck:checked').length < 1)
      swal("Please select employee(s) you want to force logout")
      else
      {

      }
    }
</script>
<img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>

</html>
<?php
mysqli_close($conn);
?>
