<!DOCTYPE html>
<style type="text/css">
    .auto-style1 {
        color: #FF0000;
    }
</style>
<?php


require CONN;
$shopid = $_COOKIE['shopid'];


?>
<!--[if IE 9]>
<html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-focus">
<!--<![endif]-->

<head>
    <meta charset="utf-8">

    <title><?= getPageTitle() ?></title>

    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon() ?>' type='image/x-icon'/>
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datepicker/bootstrap-datepicker3.css">


    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">

    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH . "/shared/analytics.php"); ?>
<!-- Page Container -->
<!--
        Available Classes:

        'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

        'sidebar-l'                  Left Sidebar and right Side Overlay
        'sidebar-r'                  Right Sidebar and left Side Overlay
        'sidebar-mini'               Mini hoverable Sidebar (> 991px)
        'sidebar-o'                  Visible Sidebar by default (> 991px)
        'sidebar-o-xs'               Visible Sidebar by default (< 992px)

        'side-overlay-hover'         Hoverable Side Overlay (> 991px)
        'side-overlay-o'             Visible Side Overlay by default (> 991px)

        'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

        'header-navbar-fixed'        Enables fixed header
    -->
<div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">
    <!-- Side Overlay-->
    <aside id="side-overlay">
        <!-- Side Overlay Scroll Container -->
        <div id="side-overlay-scroll">
            <!-- Side Header -->
            <div class="side-header side-content">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default pull-right" type="button" data-toggle="layout"
                        data-action="side_overlay_close">
                    <i class="fa fa-times"></i>
                </button>
                <span>
                        <img class="img-avatar img-avatar32" src="<?= IMAGE ?>/avatars/avatar10.jpg" alt="">
                        <span class="font-w600 push-10-l">Walter Fox</span>
                    </span>
            </div>
            <!-- END Side Header -->

        </div>
        <!-- END Side Overlay Scroll Container -->
    </aside>
    <!-- END Side Overlay -->

    <!-- Sidebar -->
    <nav id="sidebar">
        <!-- Sidebar Scroll Container -->
        <div id="sidebar-scroll">
            <!-- Sidebar Content -->
            <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
            <div class="sidebar-content">
                <!-- Side Header -->
                <div class="side-header side-content bg-white-op">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button"
                            data-toggle="layout" data-action="sidebar_close">
                        <i class="fa fa-times"></i>
                    </button>
                    <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                        <i class="text-primary">
                            <?php getLogo() ?></i>
                        <span class="h4 font-w600 sidebar-mini-hide">
                            </span>
                    </a>
                </div>
                <!-- END Side Header -->

                <!-- Side Content -->
                <div class="side-content-sbp-settings side-content">
                    <?php require(COMPONENTS_PRIVATE_PATH . "/settings/submenufiles/menu.php"); ?>
                </div>

                <!-- END Side Content -->
            </div>
            <!-- Sidebar Content -->
        </div>
        <!-- END Sidebar Scroll Container -->
    </nav>
    <!-- END Sidebar -->

    <!-- Header -->
    <header id="header-navbar" class="content-mini content-mini-full">
        <!-- Header Navigation Right -->
        <ul class="nav-header pull-right">
            <li>
                <div id="shopnotice">
                    <?php echo $_COOKIE['shopname'] . " #" . $shopid . "<br>" . $_COOKIE['username'] . '<a class="btn btn-primary btn-sm btn-logoff" href="' . COMPONENTS_PUBLIC . '/login/logoff.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Logoff</span></a>'; ?>
                </div>
            </li>
        </ul>
        <!-- END Header Navigation Right -->

        <!-- Header Navigation Left -->

        <ul class="nav-header pull-left">
            <li class="hidden-md hidden-lg">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                    <i class="fa fa-navicon"></i>
                </button>
            </li>
            <li class="hidden-xs hidden-sm">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" id="close-sidebar"
                        data-action="sidebar_mini_toggle" type="button">
                    <i class="fa fa-bars"></i>
                </button>
            </li>
            <li>
                <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                <button style="display:none" class="btn btn-default pull-right" data-toggle="modal"
                        data-target="#apps-modal" type="button">
                    <i class="si si-grid"></i>
                </button>
            </li>
            <li class="visible-xs">
                <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search"
                        data-class="header-search-xs-visible" type="button">
                    <i class="fa fa-search"></i>
                </button>
            </li>
            <li>

            </li>
        </ul>

        <!-- END Header Navigation Left -->
    </header>
    <!-- END Header -->

    <!-- Main Container -->
    <main class="container-fluid" id="main-container">
        <div class="row">
            <div class="col-xs-6 col-md-4 push-20-t">
                <form action="upload-create-photo.php" class="dropzone" id="sbpdropzone">
                    <div class="dz-message p-4" data-dz-message>
                        <span>Click the box or Drag and Drop your employee photo here</span>
                    </div>
                </form>
            </div>
        </div>

        <form id="mainform" class="form-horizontal push-10-t" name="mainform">
            <div class="row">
                <div class="row">
                    <div class="sbp-header">EMPLOYEE INFORMATION</div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" tabindex="1"
                                       type="text" value="" id="last" name="last">
                                <label for="last">Last Name*</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" tabindex="2" value=""
                                       type="text" id="first" name="first">
                                <label for="first">First Name*</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" tabindex="2" value=""
                                       type="text" id="phone" name="phone"
                                       onKeyUp="javascript:return mask(this.value,this,'3,7','-');"
                                       onBlur="javascript:return mask(this.value,this,'3,7','-');">
                                <label>Phone</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" tabindex="2"
                                       type="email" id="empemail" name="empemail">
                                <label for="empemail">Email</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <select name="Position" class="form-control sbp-form-control" name="position"
                                        id="position">
                                    <?php
                                    $stmt = "select JobDesc from jobdesc where shopid = '" . $shopid . "'";
                                    $result = $conn->query($stmt);
                                    while ($row = $result->fetch_array()) {
                                        echo "<option value='" . strtoupper($row['JobDesc']) . "'>" . strtoupper($row['JobDesc']) . "</option>";
                                    }
                                    ?>
                                </select>

                                <label>Position</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" tabindex="4"
                                       type="text" value="" id="password" name="password">
                                <label>Password</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" tabindex="5"
                                       type="text" value="" id="michmech" name="michmech">
                                <label id="cityfloatinglabel">Michigan Mech #</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <select class="form-control sbp-form-control" name="showtechlist" id="showtechlist">
                                    <option value="YES">YES</option>
                                    <option selected value="NO">NO</option>
                                </select>
                                <label id="statefloatinglabel">Is Employee a Technician</label>
                            </div>
                        </div>
                    </div>
                    <span class="auto-style1"><strong>* First and Last name are required
                            </strong></span>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <select class="form-control sbp-form-control" name="Active" id="Active">
                                    <option value="YES">YES</option>
                                    <option value="NO">NO</option>
                                </select>

                                <label>Active</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" type="text" value="0"
                                       tabindex="13" id="rate" name="rate">
                                <label>Pay Rate</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <select name="paytype" class="form-control sbp-form-control" id="paytype">
                                    <option value="FLATRATE">Flat Rate</option>
                                    <option value="HOURLY">Hourly</option>
                                    <?php if ($shopid == '1932' || $shopid == '11440') { ?>
                                        <option value="PERCENTAGE">Percentage</option>
                                        <?php
                                    }
                                    ?>
                                </select>
                                <label>Pay Type</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" type="text"
                                       tabindex="15" value="" id="pin" name="pin">
                                <label>Timeclock PIN Number</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <select name="mode" class="form-control sbp-form-control" id="mode">
                                    <option value="FULL">FULL</option>
                                    <option value="TECH">TECH</option>
                                </select>

                                <label>User Mode</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" type="text"
                                       tabindex="17" value="" id="datehired" name="datehired">
                                <label>Date Hired</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-9 text-center">
                    <br><br>
                    <div style="margin-left:100px;" class="col-sm-12">
                        <button class="btn btn-info btn-md" onclick="$('#permissionsmodal').modal('show')"
                                type="button">Permissions
                        </button>
                        <button class="btn btn-primary btn-md" onclick="saveAll()" type="button">Save Changes</button>
                        <button class="btn btn-danger btn-md" onclick="location.href='../employees.php'" type="button">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>

            <?php if (in_array($shopid, $profitboost)) { ?>
                <div class="row">
                    <div class="col-md-9 text-center" style="margin-left:100px;color: red;">
                        <br><br>*Make sure to update the PPH Calculator with the new employee settings
                    </div>
                </div>
            <?php } ?>

        </form>
    </main>
    <!-- END Main Container -->

    <!-- Footer -->
    <!-- END Footer -->
</div>
<!-- END Page Container -->

<!-- Apps Modal -->
<div id="permissionsmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-light">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Employee Permissions</h3>
                </div>
                <div class="block-content">
                    <form id="permform">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="logintosbp" name="logintosbp" checked
                                                       value="YES"><span></span> Login to Shop Boss Pro</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="CompanyAccess" name="CompanyAccess" checked
                                                       value="YES"><span></span> Settings Access</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="EmployeeAccess" name="EmployeeAccess" checked
                                                       value="YES"><span></span> Add/Edit Employees</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="ReportAccess" name="ReportAccess" checked
                                                       value="YES"><span></span> Report Access</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="CreateRO" name="CreateRO" checked
                                                       value="YES"><span></span> Create RO</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="CreateCT" name="CreateCT" checked
                                                       value="YES"><span></span> Create Part Sale</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="EditSupplier" name="EditSupplier" checked
                                                       value="YES"><span></span> Edit Supplier</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="InventoryLookup" name="InventoryLookup"
                                                       checked value="YES"><span></span> Inventory Lookup</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="candelete" name="candelete" checked
                                                       value="YES"><span></span> Delete Labor and Parts from RO</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="changeshopnotice" name="changeshopnotice"
                                                       checked value="YES"><span></span> Change Shop Notice</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="accounting" name="accounting" checked
                                                       value="YES"><span></span> Accounting Access</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="downloaddata" name="downloaddata" checked
                                                       value="YES"><span></span> Download Data</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="showgpinro" name="showgpinro" checked
                                                       value="YES"><span></span> Show GP in RO</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="editcommentsinro" name="editcommentsinro"
                                                       checked value="YES"><span></span> Edit Comments in RO</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="EditInventory" name="EditInventory" checked
                                                       value="YES"><span></span> Edit Inventory</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="ReOpenRO" name="ReOpenRO" checked
                                                       value="YES"><span></span> Re-Open RO</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="changerodate" name="changerodate"
                                                       value="YES"><span></span> Change RO Dates</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="ChangePartMatrix" name="ChangePartMatrix"
                                                       checked value="YES"><span></span> Change Parts Matrix</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="ChangePartCodes" name="ChangePartCodes"
                                                       checked value="YES"><span></span> Change Parts Codes</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="ChangeJobDescription"
                                                       name="ChangeJobDescription" checked value="YES"><span></span>
                                                Change Job Descriptions</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="ChangeSources" name="ChangeSources" checked
                                                       value="YES"><span></span> Change Advertising Sources </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="ChangeRepairOrderTypes"
                                                       name="ChangeRepairOrderTypes" checked value="YES"><span></span>
                                                Change RO Types</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="sendupdates" name="sendupdates" checked
                                                       value="YES"><span></span> Allow Send Updates to Customer?</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="deletepaymentsreceived"
                                                       name="deletepaymentsreceived" checked value="YES"><span></span>
                                                Can Delete Payments?</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="deletecustomer" name="deletecustomer" checked
                                                       value="YES"><span></span> Can Delete Customer?</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="editnotifications" name="editnotifications"
                                                       value="YES"><span></span> Edit Notifications</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" id="edittechpaidlog" name="edittechpaidlog"
                                                       value="YES"><span></span> Edit Tech Paid Log in RO</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-9">
                                        <div class="">
                                            <label class="css-input switch switch-primary switch-sm">
                                                <input type="checkbox" checked id="partsordering" name="partsordering"
                                                       value="YES"><span></span> Order Parts Online</label>
                                        </div>
                                    </div>
                                </div>

                            </div>

                        </div>
                </div>
                </form>
                <div style="margin-top:20px;" class="modal-footer">
                    <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>


    <script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
    <script src="<?= SCRIPT ?>/tipped.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

    <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
    <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
    <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
    <script src="<?= SCRIPT ?>/app.js"></script>
    <script src="../<?= SCRIPT ?>/jquery.floatThead.js"></script>
    <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="<?= SCRIPT ?>/plugins/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/dropzonejs/dropzone.min.css">
    <script src="<?= SCRIPT ?>/plugins/dropzonejs/dropzone.min.js"></script>
    <!-- Page Plugins -->

    <!-- Page JS Code
    <script src="assets/js/pages/base_pages_dashboard.js"></script>-->
    <script>
        jQuery(function () {
            // Init page helpers (Slick Slider plugin)
            App.initHelpers('slick');
        });

        $(document).ready(function () {
            Dropzone.options.sbpdropzone = {
                maxFilesize: 5, // MB
                uploadMultiple: false,
                acceptedFiles: "image/*",
                success: (file, response) => {
                    $('#photo_name').val(response);
                },
            };
        });

        function mask(str, textbox, loc, delim) {
            var x = event.which || event.keyCode;
            if (x != 8) {
                var locs = loc.split(',');
                for (var i = 0; i <= locs.length; i++) {
                    for (var k = 0; k <= str.length; k++) {
                        if (k == locs[i]) {
                            if (str.substring(k, k + 1) != delim) {
                                str = str.substring(0, k) + delim + str.substring(k, str.length)
                            }

                        }

                    }

                }
                textbox.value = str
            }
        }


        function saveAll() {

            fn = $('#first').val()
            ln = $('#last').val()

            if (fn.length == 0 || ln.length == 0) {
                swal("First and Last name are required fields")
            } else {

                ds = "t=add&shopid=<?php echo $shopid; ?>&" + $('#mainform').serialize() + "&" + $('#permform').serialize()

                console.log(ds)
                $.ajax({
                    data: ds,
                    type: "post",
                    url: "employee-action.php",
                    success: function (r) {
                        console.log(r)
                        swal({
                                title: "Employee Added",
                            },
                            function () {
                                location.href = '../employees.php'
                            }
                        )
                    }
                });
            }

        }


        $(document).ready(function () {
            $('#datehired').datepicker({
                format: 'mm/dd/yyyy',
                startDate: '-0d',
                todayHighlight: true,
                autoclose: true
            });
        });
    </script>
    <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>

</html>
<?php
mysqli_close($conn);
?>
