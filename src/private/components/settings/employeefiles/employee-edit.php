<!DOCTYPE html>
<?php
require CONN;
include INTEGRATIONS_PATH . "/sbp_bucket/sbp_bucket.php";
$shopid = $_COOKIE['shopid'];
//echo $shopid;
$id = $_GET['id'];

$stmt = "select comments,downloaddata,changerodate,shopid,EmployeeID,EmployeeLast,EmployeeFirst,EmployeeAddress,EmployeeCity,EmployeeState,EmployeeZip,EmployeePhone,EmployeeEmail,JobDesc,DateHired,DefaultWriter,Active,"
    . "hourlyrate,pin,tooltip,CompanyAccess,EmployeeAccess,ReportAccess,CreateRO,CreateCT,EditSupplier,InventoryLookup,ViewSecurityLog,EditInventory,ReOpenRO,ChangeUserSecurity,"
    . "ChangePartMatrix,ChangePartCodes,ChangeJobDescription,ChangeSources,ChangeRepairOrderTypes,logintosbp,accounting,mode,password,sendupdates,deletepaymentsreceived,ipadhelp,"
    . "editable,mechanicnumber,paytype,showtechlist,candelete,changeshopnotice,deletecustomer,editnotifications,showgpinro,edittechpaidlog,editcommentsinro,color,DashboardAccess,IntegrationAccess,changerostatus, photo, partsordering, pphAccess from employees where id = ? and shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("is", $id, $shopid);
    $query->execute();
    $query->bind_result($comments, $downloaddata, $changerodate, $shopid, $EmployeeID, $EmployeeLast, $EmployeeFirst, $EmployeeAddress, $EmployeeCity, $EmployeeState, $EmployeeZip, $EmployeePhone, $EmployeeEmail, $JobDesc, $DateHired, $DefaultWriter, $Active, $hourlyrate, $pin, $tooltip, $CompanyAccess, $EmployeeAccess, $ReportAccess, $CreateRO, $CreateCT, $EditSupplier, $InventoryLookup, $ViewSecurityLog, $EditInventory, $ReOpenRO, $ChangeUserSecurity, $ChangePartMatrix, $ChangePartCodes, $ChangeJobDescription, $ChangeSources, $ChangeRepairOrderTypes, $logintosbp, $accounting, $mode, $password, $sendupdates, $deletepaymentsreceived, $ipadhelp, $editable, $mechanicnumber, $paytype, $showtechlist, $candelete, $changeshopnotice, $deletecustomer, $editnotifications, $showgpinro, $edittechpaidlog, $editcommentsinro, $schcolor, $dashboardaccess, $integrationacc, $changerostatusfull, $photo, $partsordering, $pphAccess);
    $query->fetch();
    $query->close();
} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

// get permissions for this tech
$empid = $id;
$stmt = "select orderparts,techreport,changerostatus,showcustonwip,editparts,editlabor,viewschedule,editschedule,editcannedjobs,editsublet,changevistatus,viewhistory,editmilesin,editmilesout,vieweditcomments,showsublet,onlytechissues,showcustomerinfo,sendinspection,techsupervisor,showpartscostonro,accessworkflow, showwtkonwip from techpermissions where shopid = '$shopid' and empid = $empid";
if ($query = $conn->prepare($stmt)) {
    if ($query->execute()) {
        $query->bind_result($orderparts, $techreport, $changerostatus, $showcustonwip, $editparts, $editlabor, $viewschedule, $editschedule, $editcannedjobs, $editsublet, $changevistatus, $viewhistory, $editmilesin, $editmilesout, $vieweditcomments, $showsublet, $onlytechissues, $showcustomerinfo, $sendinspection, $techsupervisor, $showpartscostonro, $accessworkflow, $showwtkonwip);
        $query->fetch();
    } else {
        $editparts = "yes";
        $editlabor = "yes";
        $viewschedule = "yes";
        $editschedule = "no";
        $editcannedjobs = "yes";
        $editsublet = "yes";
        $changevistatus = "yes";
        $viewhistory = "yes";
        $editmilesin = "yes";
        $editmilesout = "yes";
        $vieweditcomments = "yes";
        $changerostatus = "yes";
        $showcustomerinfo = "yes";
        $sendinspection = "yes";
        $techreport = "no";
        $orderparts = "no";
        $techsupervisor = "no";
        $showpartscostonro = "yes";
        $showwtkonwip = 'no';
    }
    $query->close();
} else {
    $editparts = "yes";
    $editlabor = "yes";
    $viewschedule = "yes";
    $editschedule = "no";
    $editcannedjobs = "yes";
    $editsublet = "yes";
    $changevistatus = "yes";
    $viewhistory = "yes";
    $editmilesin = "yes";
    $editmilesout = "yes";
    $vieweditcomments = "yes";
    $changerostatus = "yes";
    $showcustomerinfo = "yes";
    $sendinspection = "yes";
    $techreport = "no";
    $orderparts = "no";
    $techsupervisor = "no";
    $showpartscostonro = "yes";
    $showwtkonwip = 'no';
}

if ($editparts == "") {
    $editparts = "yes";
}
if ($editlabor == "") {
    $editlabor = "yes";
}
if ($viewschedule == "") {
    $viewschedule = "yes";
}
if ($editschedule == "") {
    $editschedule = "no";
}
if ($editcannedjobs == "") {
    $editcannedjobs = "yes";
}
if ($editsublet == "") {
    $editsublet = "yes";
}
if ($changevistatus == "") {
    $changevistatus = "yes";
}
if ($viewhistory == "") {
    $viewhistory = "yes";
}
if ($editmilesin == "") {
    $editmilesin = "yes";
}
if ($editmilesout == "") {
    $editmilesout = "yes";
}
if ($vieweditcomments == "") {
    $vieweditcomments = "yes";
}
if ($changerostatus == "") {
    $changerostatus = "yes";
}
if ($showcustomerinfo == "") {
    $showcustomerinfo = "yes";
}
if ($sendinspection == "") {
    $sendinspection = "yes";
}
if ($techreport == "") {
    $techreport = "no";
}
if ($orderparts == "") {
    $orderparts = "no";
}
if ($techsupervisor == "") {
    $techsupervisor = "no";
}

if($_COOKIE['empid'] == 'Admin')
$loginasemployee = true;
else
{
    $stmt = "select jobdesc from employees where shopid = ? and id = ?";

    if ($query = $conn->prepare($stmt)) {

        $query->bind_param("si", $shopid,$_COOKIE['empid']);
        $query->execute();
        $query->bind_result($userjobdesc);
        $query->fetch();
        $query->close();
    }

    if(strtolower($userjobdesc) == 'owner')
    $loginasemployee = true;
    else
    $loginasemployee = false;
}
?>
<!--[if IE 9]>
<html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><![endif]-->
<!-->
<html class="no-focus"> <!--<![endif]-->

<head>
    <meta charset="utf-8">

    <title><?= getPageTitle() ?></title>

    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon() ?>' type='image/x-icon'/>
    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
    <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

    <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Web fonts -->
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/bootstrap-datepicker/bootstrap-datepicker3.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/color-picker/color-picker.css">

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">

    <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
    <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
    <!-- END Stylesheets -->
    <style type="text/css">
        .auto-style1 {
            color: #FF0000;
        }
    </style>
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH . "/shared/analytics.php"); ?>
<!-- Page Container -->
<!--
        Available Classes:

        'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

        'sidebar-l'                  Left Sidebar and right Side Overlay
        'sidebar-r'                  Right Sidebar and left Side Overlay
        'sidebar-mini'               Mini hoverable Sidebar (> 991px)
        'sidebar-o'                  Visible Sidebar by default (> 991px)
        'sidebar-o-xs'               Visible Sidebar by default (< 992px)

        'side-overlay-hover'         Hoverable Side Overlay (> 991px)
        'side-overlay-o'             Visible Side Overlay by default (> 991px)

        'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

        'header-navbar-fixed'        Enables fixed header
    -->
<div id="page-container" class="sidebar-l sidebar-o side-scroll header-navbar-fixed">
    <!-- Side Overlay-->
    <aside id="side-overlay">
        <!-- Side Overlay Scroll Container -->
        <div id="side-overlay-scroll">
            <!-- Side Header -->
            <div class="side-header side-content">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default pull-right" type="button" data-toggle="layout"
                        data-action="side_overlay_close">
                    <i class="fa fa-times"></i>
                </button>
                <span>
                        <img class="img-avatar img-avatar32" src="<?= IMAGE ?>/avatars/avatar10.jpg" alt="">
                        <span class="font-w600 push-10-l">Walter Fox</span>
                    </span>
            </div>
            <!-- END Side Header -->

        </div>
        <!-- END Side Overlay Scroll Container -->
    </aside>
    <!-- END Side Overlay -->

    <!-- Sidebar -->
    <nav id="sidebar">
        <!-- Sidebar Scroll Container -->
        <div id="sidebar-scroll">
            <!-- Sidebar Content -->
            <!-- Adding .sidebar-mini-hide to an element will hide it when the sidebar is in mini mode -->
            <div class="sidebar-content">
                <!-- Side Header -->
                <div class="side-header side-content bg-white-op">
                    <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                    <button class="btn btn-link text-gray pull-right hidden-md hidden-lg" type="button"
                            data-toggle="layout" data-action="sidebar_close">
                        <i class="fa fa-times"></i>
                    </button>
                    <a class="h5 text-white" href="<?= COMPONENTS_PRIVATE ?>/wip/wip.php">
                        <i class="text-primary">
                            <?php getLogo() ?></i>
                        <span class="h4 font-w600 sidebar-mini-hide">
                            </span>
                    </a>
                </div>
                <!-- END Side Header -->

                <!-- Side Content -->
                <div class="side-content-sbp-settings side-content">
                    <?php require(COMPONENTS_PRIVATE_PATH . "/settings/submenufiles/menu.php"); ?>
                </div>

                <!-- END Side Content -->
            </div>
            <!-- Sidebar Content -->
        </div>
        <!-- END Sidebar Scroll Container -->
    </nav>
    <!-- END Sidebar -->

    <!-- Header -->
    <header id="header-navbar" class="content-mini content-mini-full">
        <!-- Header Navigation Right -->
        <ul class="nav-header pull-right">
            <li>
                <div id="shopnotice">
                    <?php echo $_COOKIE['shopname'] . " #" . $shopid . "<br>" . $_COOKIE['username'] . '<a class="btn btn-primary btn-sm btn-logoff" href="' . COMPONENTS_PUBLIC . '/login/logoff.php"><i class="fa fa-sign-out"></i><span class="sidebar-mini-hide">Logoff</span></a>'; ?>
                </div>
            </li>
        </ul>
        <!-- END Header Navigation Right -->

        <!-- Header Navigation Left -->

        <ul class="nav-header pull-left">
            <li class="hidden-md hidden-lg">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" data-action="sidebar_toggle" type="button">
                    <i class="fa fa-navicon"></i>
                </button>
            </li>
            <li class="hidden-xs hidden-sm">
                <!-- Layout API, functionality initialized in App() -> uiLayoutApi() -->
                <button class="btn btn-default" data-toggle="layout" id="close-sidebar"
                        data-action="sidebar_mini_toggle" type="button">
                    <i class="fa fa-bars"></i>
                </button>
            </li>
            <li>
                <!-- Opens the Apps modal found at the bottom of the page, before including JS code -->
                <button style="display:none" class="btn btn-default pull-right" data-toggle="modal"
                        data-target="#apps-modal" type="button">
                    <i class="si si-grid"></i>
                </button>
            </li>
            <li class="visible-xs">
                <!-- Toggle class helper (for .js-header-search below), functionality initialized in App() -> uiToggleClass() -->
                <button class="btn btn-default" data-toggle="class-toggle" data-target=".js-header-search"
                        data-class="header-search-xs-visible" type="button">
                    <i class="fa fa-search"></i>
                </button>
            </li>
            <li>

            </li>
        </ul>

        <!-- END Header Navigation Left -->
    </header>
    <!-- END Header -->

    <!-- Main Container -->
    <main class="container-fluid" id="main-container">
        <form id="mainform" class="form-horizontal push-10-t" name="mainform">
            <div class="row">
                <div class="row">
                    <div class="sbp-header">EMPLOYEE INFORMATION</div>
                </div>

                <div class="col-md-12"></div>

                <div class="col-md-6">
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" tabindex="1"
                                       type="text" value="<?php echo strtoupper($EmployeeLast); ?>" id="last"
                                       name="last">
                                <label for="last">Last Name*</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" tabindex="2"
                                       value="<?php echo strtoupper($EmployeeFirst); ?>" type="text" id="first"
                                       name="first">
                                <label for="first">First Name*</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" tabindex="2"
                                       value="<?php echo strtoupper($EmployeePhone); ?>" type="text" id="phone"
                                       name="phone" onKeyUp="javascript:return mask(this.value,this,'3,7','-');"
                                       onBlur="javascript:return mask(this.value,this,'3,7','-');">
                                <label>Phone</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" tabindex="2"
                                       value="<?php echo strtoupper($EmployeeEmail); ?>" type="email" id="empemail"
                                       name="empemail">
                                <label for="empemail">Email</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <select class="form-control sbp-form-control" name="position" id="position">
                                    <?php
                                    echo "<option selected value='" . strtoupper($JobDesc) . "'>" . strtoupper($JobDesc) . "</option>";
                                    $stmt = "select JobDesc from jobdesc where ucase(JobDesc) != '" . strtoupper($JobDesc) . "' and shopid = '" . $shopid . "'";
                                    $result = $conn->query($stmt);
                                    while ($row = $result->fetch_array()) {
                                        echo "<option value='" . strtoupper($row['JobDesc']) . "'>" . strtoupper($row['JobDesc']) . "</option>";
                                    }
                                    ?>
                                </select>

                                <label>Position</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" tabindex="5"
                                       type="text" value="<?php echo strtoupper($mechanicnumber); ?>" id="michmech"
                                       name="michmech">
                                <label id="cityfloatinglabel">Michigan Mech #</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <select class="form-control sbp-form-control" name="showtechlist" id="showtechlist"
                                        onchange="setMode(this.value)">
                                    <?php
                                    if (strtoupper($showtechlist) == "YES") {
                                        $ys = " selected='selected' ";
                                        $ns = "";
                                    } else {
                                        $ns = " selected='selected' ";
                                        $ys = "";
                                    }
                                    ?>
                                    <option <?php echo $ys; ?> value="YES">YES</option>
                                    <option <?php echo $ns; ?> value="NO">NO</option>
                                </select>
                                <label id="statefloatinglabel">Is Employee a Technician (Must be marked yes to be assigned to a labor line)</label>
                            </div>
                        </div>
                    </div>
                    <span class="auto-style1"><strong>* First and Last name are required
                            </strong></span>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <select class="form-control sbp-form-control" name="Active" id="Active">
                                    <?php
                                    if (strtoupper($Active) == "YES") {
                                        $ys = " selected='selected' ";
                                        $ns = "";
                                    } else {
                                        $ns = " selected='selected' ";
                                        $ys = "";
                                    }
                                    ?>
                                    <option <?php echo $ys; ?> value="YES">YES</option>
                                    <option <?php echo $ns; ?> value="NO">NO</option>
                                </select>

                                <label>Active</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" type="text"
                                       value="<?php echo number_format($hourlyrate, 2); ?>" tabindex="13" id="rate"
                                       name="rate">
                                <label>Pay Rate</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <select name="paytype" class="form-control sbp-form-control" id="paytype">
                                    <?php
                                    $frs = "";
                                    $hrs = "";
                                    $prs = "";

                                    if (strtoupper($paytype) == "FLATRATE") {
                                        $frs = " selected='selected' ";
                                    }
                                    if (strtoupper($paytype) == "HOURLY") {
                                        $hrs = " selected='selected' ";
                                    }
                                    if (strtoupper($paytype) == "PERCENTAGE") {
                                        $prs = " selected='selected' ";
                                    }

                                    ?>
                                    <option <?php echo $frs; ?> value="FLATRATE">Flat Rate</option>
                                    <option <?php echo $hrs; ?> value="HOURLY">Hourly</option>
                                    <?php if ($shopid == '1932') { ?>
                                        <option <?php echo $prs; ?> value="PERCENTAGE">Percentage</option>
                                        <?php
                                    }
                                    ?>
                                </select>
                                <label>Pay Type</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" type="text"
                                       tabindex="15" value="<?php echo strtoupper($pin); ?>" id="pin" name="pin">
                                <label>Timeclock PIN Number</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <select name="mode" class="form-control sbp-form-control" id="mode">
                                    <?php
                                    if (strtoupper($mode) == "FULL") {
                                        $fs = " selected='selected' ";
                                        $ts = "";
                                    } else {
                                        $ts = " selected='selected' ";
                                        $fs = "";
                                    }
                                    ?>
                                    <option <?php echo $fs; ?> value="FULL">FULL</option>
                                    <option <?php echo $ts; ?> value="TECH">TECH</option>
                                </select>

                                <label>User Mode</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <label>Schedule Color</label>
                            <div class="form-material floating">
                                <input type="text" name="color" id="colorpicker"><br><br>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <input class="form-control sbp-form-control" style="padding:20px;" type="text"
                                       tabindex="17"
                                       value="<?= $DateHired != '0000-00-00' ? date_format(new DateTime($DateHired), 'm/d/Y') : '' ?>"
                                       id="datehired" name="datehired">
                                <label>Date Hired</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9">
                            <div class="form-material floating">
                                <textarea class="form-control sbp-form-control"
                                          style="padding:20px;width:100%;height:70px;" tabindex="18" id="comments"
                                          name="comments"><?php echo $comments; ?></textarea>
                                <label>Comments</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3">
                        <?php if (!empty($photo)) { ?>
                            <div class="text-center"
                                 style="display: flex; align-items: center; justify-content: center;">
                                <?php
                                $emp_photo = UPLOAD_URL . "/$shopid/$photo";
                                if (strpos($photo, "/") !== false) {
                                    $presigned_url = $sbp_bucket->presigned_url($photo, 60);
                                    $emp_photo = $presigned_url;
                                }

                                echo "<img id='photoImage' class='img-thumbnail ripple' onclick='editPhoto()' style='cursor:pointer; border-radius: 50%;' src='$emp_photo'>";
                                ?>

                                <div class="text-primary float-end">
                                    <i class="fa fa-2x fa-trash pe-auto" style='cursor:pointer'
                                       onclick="deletePhoto()"></i>
                                </div>
                            </div>
                        <?php } else { ?>
                            <div class="text-center"
                                 style="display: flex; align-items: center; justify-content: center;">
                                <img id='sbpdropzone' class="dropzone" style="border-radius: 50%; width: 100%;"
                                     src='https://upload.wikimedia.org/wikipedia/commons/a/ac/Default_pfp.jpg'>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-9 text-center">
                    <br><br>
                    <div style="margin-left:100px;" class="col-sm-12">
                        <button class="btn btn-primary btn-md" onclick="$('#passwordmodal').modal('show')" type="button">
                                Change Password
                        </button>
                        <?php if($loginasemployee){ ?>
                            <button class="btn btn-warning btn-md" onclick="loginAsEmployee()" type="button">
                                Login as <?= $EmployeeFirst.' '.$EmployeeLast?>
                            </button>
                        <?php }?>
                        <?php if (in_array($shopid, $fleetshops)) { ?>
                            <button class="btn btn-success btn-md" onclick="assignCustomers()" type="button">Assign
                                Customers
                            </button><?php } ?>
                        <button id="techperm" style="display: <?= strtoupper($showtechlist) != 'YES' ? 'none' : '' ?>"
                                class="btn btn-warning btn-md" onclick="openTechPermissions()" type="button">Tech Mode
                            Permissions
                        </button>
                        <button class="btn btn-info btn-md" onclick="$('#permissionsmodal').modal('show')"
                                type="button">Permissions
                        </button>
                        <button class="btn btn-primary btn-md" onclick="saveAll()" type="button">Save Changes</button>
                        <button class="btn btn-danger btn-md" onclick="location.href='../employees.php'" type="button">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>

            <?php if (in_array($shopid, $profitboost)) { ?>
                <div class="row">
                    <div class="col-md-9 text-center" style="margin-left:100px;color: red;">
                        <br><br>*Make sure to update the PPH Calculator with the updated employee settings
                    </div>
                </div>
            <?php } ?>

        </form>


    </main>
    <!-- END Main Container -->

    <!-- Footer -->
    <!-- END Footer -->
</div>
<!-- END Page Container -->


<div id="techpermissionsmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Tech Mode Permissions</h3>
                </div>
                <div class="block-content">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techeditparts" <?php if (strtoupper($editparts) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Edit Parts</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technicians can edit part lines in a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techeditlabor" <?php if (strtoupper($editlabor) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Edit Labor</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technicians can edit labor lines in a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techeditsublet" <?php if (strtoupper($editsublet) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Edit Sublet</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technicians can edit sublet lines in a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techeditcannedjobs" <?php if (strtoupper($editcannedjobs) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Edit Canned Jobs</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technicians can edit canned jobs in a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techshowsublet" <?php if (strtoupper($showsublet) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Show Sublet</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technicians can see sublets in a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techonlytechissues" <?php if (strtoupper($onlytechissues) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Show ONLY Tech
                                            Issues</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technicians can only see labor lines assigned to them."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techshowcustonwip" <?php if (strtoupper($showcustonwip) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Show Customer on WIP</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Customers name is visible on the Tech Mode Work in process."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="showwtkonwip" <?php if (strtoupper($showwtkonwip) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Show Writer & Technician on WIP</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technician can see the service writer and technician assigned to a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techviewschedule" <?php if (strtoupper($viewschedule) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> View Customer
                                            Schedule</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technician can view the customer schedule for upcoming appointments."></i>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techeditschedule" <?php if (strtoupper($editschedule) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Edit Customer
                                            Schedule</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technician can edit the customer schedule for scheduled appointments."></i>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techsupervisor" <?php if (strtoupper($techsupervisor) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Tech Supervisor/Shop Foreman</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Allows the technician to see all assigned labor lines."></i>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="showpartscostonro" <?php if (strtoupper($showpartscostonro) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Show Parts Cost in
                                            RO</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Parts cost will be visible to technicians in a repair order."></i>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techchangevistatus" <?php if (strtoupper($changevistatus) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Change Vehicle Issue Status</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technician can change vehicle issue status per customer concern – [pending, approved, scheduled, parts ordered, assembly, job complete, and declined]."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techviewhistory" <?php if (strtoupper($viewhistory) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> View History</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technician can view previous service history performed at the shop on a customer’s vehicle."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techeditmilesin" <?php if (strtoupper($editmilesin) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Edit Miles In</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technician can edit the mileage in field in a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techeditmilesout" <?php if (strtoupper($editmilesout) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Edit Miles Out</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technician can edit the mileage out field in a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techvieweditcomments" <?php if (strtoupper($vieweditcomments) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Edit Comments</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technician can edit comments field in a repair order. Can be used in addition to tech notes."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techchangerostatus" <?php if (strtoupper($changerostatus) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Change RO Status</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can change the RO Status in a repair order including marking a ticket closed."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techshowcustomerinfo" <?php if (strtoupper($showcustomerinfo) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Show Customer Info</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technician can view customer information in a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techsendinspection" <?php if (strtoupper($sendinspection) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Send Inspection to Customer</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technician can send completed inspections to a customer."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="techreport" <?php if (strtoupper($techreport) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> View Tech Production Report</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technician can view Technician Production Report by date range including Hours worked, total labor, and tech pay."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="orderparts" <?php if (strtoupper($orderparts) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Order Parts Online</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technician can access online parts integrations to order parts."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="accessworkflow" <?php if (strtoupper($accessworkflow) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="saveTech(this.id)"><span></span> Workflow Access</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Technician can view the Kanban workflow job board in addition to the work in process."></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="passwordmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Change Password</h3>
                </div>
                <div class="block-content">
                    <?php if($id != $_COOKIE['empid']){?>Changing password will force logout this employee<br><br><?php }?>
                    New Password:
                    <input class="form-control sbp-form-control" type="password" id="password" name="password">
                    <br>Confirm New Password:
                    <input class="form-control sbp-form-control" style="padding:20px;text-transform:none" type="password" id="cpassword" name="cpassword">
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-md btn-primary" id="btn-password" type="button" onclick="changePassword()">Save</button>
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>


<!-- Apps Modal -->
<div id="permissionsmodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-dark">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Employee Permissions</h3>
                </div>
                <!--$InventoryLookup,$EditInventory,$ReOpenRO,$ChangeUserSecurity,$ChangeSources,$ChangeRepairOrderTypes,$logintosbp,$accounting,$sendupdates,$showtechlist,$deletecustomer-->
                <div class="block-content">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="logintosbp" <?php if (strtoupper($logintosbp) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Login to Shop Boss Pro</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Allows employee to login to their Shop Boss Account."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="CompanyAccess" <?php if (strtoupper($CompanyAccess) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Settings Access</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Has access to system settings."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="EmployeeAccess" <?php if (strtoupper($EmployeeAccess) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Add/Edit Employees</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can add/edit employee profiles."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="ReportAccess" <?php if (strtoupper($ReportAccess) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Report Access</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Allows access to system reports."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="CreateRO" <?php if (strtoupper($CreateRO) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Create RO</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Ability to create a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="CreateCT" <?php if (strtoupper($CreateCT) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Create Part Sale</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Ability to do a part sale/over the counter sale."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="EditSupplier" <?php if (strtoupper($EditSupplier) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Edit Supplier</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Ability to add/edit part suppliers."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="InventoryLookup" <?php if (strtoupper($InventoryLookup) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Inventory Lookup</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can search current Inventory."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="candelete" <?php if (strtoupper($candelete) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Delete Labor and Parts from
                                            RO</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can delete labor/parts from a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="changeshopnotice" <?php if (strtoupper($changeshopnotice) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Change Shop Notice</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can update shop notice alerts."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="accounting" <?php if (strtoupper($accounting) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Accounting Access</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can access accounting."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="downloaddata" <?php if (strtoupper($downloaddata) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Download Data</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can download and backup system data."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="showgpinro" <?php if (strtoupper($showgpinro) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Show GP in RO</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Gross Profit is visible in a Repair Order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="editcommentsinro" <?php if (strtoupper($editcommentsinro) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Edit Comments in RO</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Ability to edit Communication log notes."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="IntegrationAccess" <?php if (strtoupper($integrationacc) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Integrations Access</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Access to system integrations."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="partsordering" <?php if (strtoupper($partsordering) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Order Parts Online</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Ability to order parts through parts integrations."></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="EditInventory" <?php if (strtoupper($EditInventory) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Edit Inventory</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can edit counts and costs along with inventory fees. "></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="ReOpenRO" <?php if (strtoupper($ReOpenRO) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Re-Open RO</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can re-open a closed repair order for adjustment."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="changerodate" <?php if (strtoupper($changerodate) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Change RO Dates</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can edit Status date in a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="ChangePartMatrix" <?php if (strtoupper($ChangePartMatrix) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Change Parts Matrix</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can adjust parts matrix."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="ChangePartCodes" <?php if (strtoupper($ChangePartCodes) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Change Parts Codes</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can change Part code descriptions."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="ChangeJobDescription" <?php if (strtoupper($ChangeJobDescription) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Change Job Descriptions</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can create and change employee job descriptions."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="ChangeSources" <?php if (strtoupper($ChangeSources) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Change Advertising Sources
                                        </label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can change advertising sources in a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="ChangeRepairOrderTypes" <?php if (strtoupper($ChangeRepairOrderTypes) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Change RO Types</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can change RO Types in a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="sendupdates" <?php if (strtoupper($sendupdates) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Allow Send Updates to Customer?</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can send out a repair order update link to the customer from a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="deletepaymentsreceived" <?php if (strtoupper($deletepaymentsreceived) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Can Delete Payments?</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Ability to delete a payment in a repair order."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="deletecustomer" <?php if (strtoupper($deletecustomer) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Can Delete Customer?</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Ability to delete a customer profile from the Customer list."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="editnotifications" <?php if (strtoupper($editnotifications) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Edit Notifications</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Ability to change what notifications the system reports on in settings."></i>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="edittechpaidlog" <?php if (strtoupper($edittechpaidlog) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Edit Tech Paid Log in RO</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Ability to add and adjust technician paid hours in a repair order."></i>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="DashboardAccess" <?php if (strtoupper($dashboardaccess) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> BOSS Board Access</label>
                                        <i class="fa fa-info-circle simple-tooltip" title=" Access to the analytics dashboard (Must be apart of your package tier)"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="changerostatus" <?php if (strtoupper($changerostatusfull) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span> Change RO Status</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Can change the RO Status in a repair order including marking a ticket closed."></i>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="col-sm-9">
                                    <div class="">
                                        <label class="css-input switch switch-primary switch-sm">
                                            <input type="checkbox"
                                                   id="pphAccess" <?php if (strtoupper($pphAccess) == "YES") {
                                                echo 'checked';
                                            } ?> onchange="save(this.id)"><span></span>PPH Access</label>
                                        <i class="fa fa-info-circle simple-tooltip" title="Allows access to PPH"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div style="margin-top:20px;" class="modal-footer">
                    <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <form id="loginform" method="post" action="<?= COMPONENTS_PUBLIC ?>/login/loginsettings.php">
        <input type="hidden" name="loginshopid" value="<?= $shopid?>">
        <input type="hidden" name="loginempid" value="<?= $id?>">
        <input type="hidden" name="ownerid" value="<?= $_COOKIE['empid']?>">
    </form>

    
    <script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
    <script src="<?= SCRIPT ?>/tipped.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

    <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
    <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
    <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
    <script src="<?= SCRIPT ?>/app.js"></script>
    <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
    <script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
    <script src="<?= SCRIPT ?>/plugins/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/dropzonejs/dropzone.min.css">
    <script src="<?= SCRIPT ?>/plugins/dropzonejs/dropzone.min.js"></script>
    <script defer src="<?= SCRIPT; ?>/emodal.js"></script>
    <!-- Page Plugins -->

    <script src="https://cdnjs.cloudflare.com/ajax/libs/spectrum/1.8.0/spectrum.min.js"></script>

    <!-- Page JS Code
        <script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->
    <script>
        jQuery(function () {
            // Init page helpers (Slick Slider plugin)
            App.initHelpers('slick');
        });

        <?php if (empty($photo)) { ?>
        Dropzone.autoDiscover = false;

        let photoDropzone = new Dropzone('#sbpdropzone', {
            url: 'upload-photo.php?employeeId=<?= $id; ?>',
            maxFilesize: 5, // MB
            uploadMultiple: false,
            acceptedFiles: "image/*",
            success: function (file, response) {
                location.reload()
            },
        });
        <?php } ?>

        function deletePhoto() {
            let photo = encodeURIComponent("<?php echo $photo; ?>");
            swal({
                title: "Are you sure?",
                text: "This will delete the employee photo so you can upload another.  Are you sure?",
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "Yes, delete it",
            }, function () {
                $.ajax({
                    data: "photo=" + photo + "&employeeId=<?= $id; ?>&shopid=<?= $shopid; ?>",
                    url: "delete-photo.php",
                    type: "post",
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    },
                    success: function (r) {
                        console.log(r)
                        if (r == "success") {
                            location.reload()
                        }
                    }
                });
            });
        }

        function editPhoto() {
            let imgurl = encodeURIComponent($('#photoImage').attr("src"))
            eModal.iframe({
                title: 'Edit Employee Photo',
                url: 'edit-photo.php?imgurl=' + imgurl + '&employeeId=<?php echo $id; ?>',
                size: eModal.size.xl,
            });
        }

        function mask(str, textbox, loc, delim) {
            var x = event.which || event.keyCode;
            if (x != 8) {
                var locs = loc.split(',');
                for (var i = 0; i <= locs.length; i++) {
                    for (var k = 0; k <= str.length; k++) {
                        if (k == locs[i]) {
                            if (str.substring(k, k + 1) != delim) {
                                str = str.substring(0, k) + delim + str.substring(k, str.length)
                            }

                        }

                    }

                }
                textbox.value = str
            }
        }


        function openTechPermissions() {
            // check to see if a record is created for this employee in techpermissions

            ds = "t=checkrec&shopid=<?php echo $shopid; ?>&empid=<?php echo $id; ?>"
            $.ajax({

                url: "employee-action.php",
                data: ds,
                type: "post",
                success: function (r) {
                    if (r == "success") {
                        $('#techpermissionsmodal').modal('show')
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }

            });


        }

        function setMode(v) {

            if (v.toLowerCase() === "yes") {
                $('#techperm').show()
            } else {
                $('#techperm').hide()
            }

        }

        function save(id) {

            if ($('#' + id).is(':checked')) {
                ds = "t=checkbox&shopid=<?php echo $shopid; ?>&id=" + id + "&val=yes&empid=<?php echo $id; ?>"
            } else {
                ds = "t=checkbox&shopid=<?php echo $shopid; ?>&id=" + id + "&val=no&empid=<?php echo $id; ?>"
            }

            console.log(ds)
            $.ajax({
                type: "post",
                data: ds,
                url: "employee-action.php",
                success: function (r) {
                    //console.log(r)
                }
            });

        }

        function saveTech(id) {

            if ($('#' + id).is(':checked')) {
                ds = "t=checkboxtech&shopid=<?php echo $shopid; ?>&id=" + id + "&val=yes&empid=<?php echo $id; ?>"
            } else {
                ds = "t=checkboxtech&shopid=<?php echo $shopid; ?>&id=" + id + "&val=no&empid=<?php echo $id; ?>"
            }

            console.log(ds)
            $.ajax({
                type: "post",
                data: ds,
                url: "employee-action.php",
                success: function (r) {
                    console.log(r)
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }

            });


        }

        function isDate(dateVal) {
            var d = new Date(dateVal);
            return d.toString() === 'Invalid Date' ? false : true;
        }

        function saveAll() {

            fn = $('#first').val()
            ln = $('#last').val()

            if (fn.length == 0 || ln.length == 0) {
                swal("First and Last name are required fields")
            } else {
                //console.log(isDate($('#datehired').val()))
                ds = "id=<?php echo $id; ?>&t=emp&shopid=<?php echo $shopid; ?>&" + $('#mainform').serialize()
                if (!isDate($('#datehired').val())) {
                    swal("Please provide a valid date hired")
                } else {
                    console.log(ds)
                    $.ajax({
                        data: ds,
                        type: "post",
                        url: "employee-action.php",
                        success: function (r) {
                            console.log(r)
                            swal({
                                    title: "Changes Saved",
                                },
                                function () {
                                    location.href = '../employees.php'
                                }
                            )
                        }
                    });
                }
            }
        }

        function assignCustomers() {

            eModal.iframe({
                title: 'Assign Customers',
                url: 'employee-customers.php?shopid=<?php echo $shopid; ?>&empid=<?php echo $id; ?>',
                size: eModal.size.lg,
                buttons: [
                    {text: 'Close', style: 'warning', close: true}
                ]

            });


        }

        function changePassword()
        {
            var password = $('#password').val()
            var cpassword = $('#cpassword').val()

            if(password != cpassword)
            {
                swal("Passwords do not match")
                return
            }

            $('#btn-password').attr('disabled','disabled')

            $.ajax({
                data: "id=<?php echo $id; ?>&t=changepassword&shopid=<?php echo $shopid; ?>&password=" + password,
                type: "post",
                url: "employee-action.php",
                success: function (r) {
                    swal(r)
                    $('#passwordmodal').modal('hide')
                    $('#btn-password').attr('disabled',false)
                }
            });
        }

        function loginAsEmployee()
        {
            swal({
                title: "Confirmation",
                text: "Do you really want to login as this employee?",
                type: "warning",
                showCancelButton: true,
                confirmButtonClass: "btn-danger",
                confirmButtonText: "Yes",
            }, function () {
                $('#spinner').show();
                $('#loginform').submit();
            })
        }



        $(document).ready(function () {
            $('#datehired').datepicker({
                format: 'mm/dd/yyyy',
                startDate: '-10y',
                todayHighlight: true,
                autoclose: true
            });

            $("#colorpicker").spectrum({
                allowEmpty: true,
                showInput: true,
                preferredFormat: "hex",
                color: "<?= $schcolor ?>"
            })

            Tipped.create('.simple-tooltip', {
                skin: 'light',
                position: 'right'
            });
        });
    </script>
    <img src="<?= IMAGE ?>/loaderbig.gif" id="spinner">
</body>

</html>
<?php
mysqli_close($conn);
?>
