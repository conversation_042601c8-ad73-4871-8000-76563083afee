<?php
require CONN;
$shopid = $oshopid = filter_var($_POST['shopid'], FILTER_SANITIZE_STRING);
$roid = filter_var($_POST['roid'], FILTER_SANITIZE_STRING);
$complaints = filter_var($_POST['complaints'], FILTER_SANITIZE_STRING);
$rawstatus = filter_var($_POST['status'], FILTER_SANITIZE_STRING);
$rawstatus = strtolower($rawstatus);
$newstatus = ucwords(strtolower($rawstatus));
$sn = $_SERVER['SERVER_NAME'];
$comparr = explode(',', $complaints);

if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

foreach ($comparr as $comid)
{
    $stmt = "select lower(acceptdecline) from complaints where shopid = ? and roid = ? and complaintid = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("ssi", $shopid, $roid, $comid);
        $query->execute();
        $query->bind_result($oldstatus);
        $query->fetch();
        $query->close();
    }

    if (!empty($oldstatus) && $rawstatus != $oldstatus)
    {
        if ($oldstatus != "declined" && $rawstatus != "declined") //scenario 1

        {
            $stmt = "update complaints set acceptdecline = ? where shopid = ? and roid = ? and complaintid = ?";
            if ($query = $conn->prepare($stmt))
            {
                $query->bind_param("sssi", $newstatus, $shopid, $roid, $comid);
                if ($query->execute()) $conn->commit();
            }
        }

        elseif ($oldstatus != "declined" && $rawstatus == "declined") //scenario 2

        {

            $stmt = "select count(*) c from labortimeclock where shopid = ? and roid = ? and complaintid = ? and isnull(enddatetime)";
            if ($query = $conn->prepare($stmt))
            {
                $query->bind_param("ssi", $shopid, $roid, $comid);
                $query->execute();
                $query->bind_result($countltc);
                $query->fetch();
                $query->close();
            }

            if ($countltc == 0){
                // now move all parts and labor to recommended
                $stmt = "select complaint,coalesce(techreport,'') techr from complaints where roid = $roid and shopid = '$shopid' and complaintid = $comid";
                if ($query = $conn->prepare($stmt)){
                    $query->execute();
                    $query->bind_result($complaint,$techreport);
                    $query->fetch();
                    $query->close();
                }else{
                    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $complaint = "Customer Declined: ".$complaint;
                $stmt = "insert into recommend (shopid,roid,comid,`desc`,technotes) values (?,?,?,?,?)";
                if ($query = $conn->prepare($stmt)){
                    $query->bind_param("siiss",$shopid,$roid,$comid,$complaint,$techreport);
                    if ($query->execute()){
                        $conn->commit();
                        //echo "success";
                    }else{
                        echo $conn->errno;
                    }
                }else{
                    echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                // now POST the id we just added
                $stmt = "select id from recommend where shopid = '$shopid' and roid = $roid and comid = $comid";
                if ($query = $conn->prepare($stmt)){
                    $query->execute();
                    $query->bind_result($id);
                    $query->fetch();
                    $query->close();
                }else{
                    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $stmt = "select SubLetID,SubletDesc,SubletPrice,SubletCost,SubletInvoiceNo,SubletSupplier from sublet where shopid = '$shopid' and roid = $roid and complaintid = $comid";
                //echo $stmt."\r\n";
                $stmt = "select SubLetID,SubletDesc,SubletPrice,SubletCost,SubletInvoiceNo,SubletSupplier from sublet where shopid = ? and roid = ? and complaintid = ?";
                $totalrec = 0;
                if ($query = $conn->prepare($stmt)){
                    $query->bind_param("sii",$shopid,$roid,$comid);
                    $query->execute();
                    $result = $query->get_result();
                    while ($row = $result->fetch_assoc()){
                        $subdesc = $row['SubletDesc'];
                        $subcost = $row['SubletCost'];
                        $subprice = $row['SubletPrice'];
                        $subsupp = $row['SubletSupplier'];
                        $totalrec += $row["SubletPrice"];

                        $sstmt = "insert into recommendsublet (recid,shopid,roid,comid,subletdesc,subletcost,subletprice,supplier) values (?,?,?,?,?,?,?,?)";
                        if ($squery = $conn->prepare($sstmt)){
                            $squery->bind_param("isiisdds",$id,$shopid,$roid,$comid,$subdesc,$subcost,$subprice,$subsupp);
                            if ($squery->execute()){
                                $conn->commit();
                            }else{
                                echo $conn->error;
                            }
                        }else{
                            echo $conn->error;
                        }
                    }
                }

                $stmt = "select `shopid`,`LaborID`,`ROID`,`HourlyRate`,`ratelabel`,`LaborHours`,`Labor`,`Tech`,`LineTotal`,`LaborOp`,`complaintid`,`techrate` from labor where shopid = ? and roid = ? and complaintid = ?";

                if ($query = $conn->prepare($stmt)){
                    $query->bind_param("sii",$shopid,$roid,$comid);
                    $query->execute();
                    $result = $query->get_result();
                    while ($row = $result->fetch_assoc()){
                        $totalrec += $row["LineTotal"];
                        $labordesc = $row['Labor'];
                        $rate = $row['HourlyRate'];
                        $hrs = $row['LaborHours'];
                        $linetotal = $row['LineTotal'];
                        $techname = $row['Tech'];
                        $ratelabel = $row['ratelabel'];
                        $istmt = "insert into recommendlabor (recid,shopid,roid,comid,`desc`,rate,ratelabel,hours,`total`,tech) values (?,?,?,?,?,?,?,?,?,?)";
                        if ($query = $conn->prepare($istmt)){
                            $query->bind_param("isiisdsdds",$id,$shopid,$roid,$comid,$labordesc,$rate,$ratelabel,$hrs,$linetotal,$techname);
                            if ($query->execute()){
                                $conn->commit();
                            }
                        }
                    }
                }

                $stmt = "select upper(updateinvonadd) from company where shopid = ?";
                if ($query = $conn->prepare($stmt)){
                    $query->bind_param("s",$shopid);
                    $query->execute();
                    $query->bind_result($updateinvonadd);
                    $query->fetch();
                    $query->close();
                }else{
                    echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
                }

                $stmt = "select allocated,`shopid`,`PartID`,`PartNumber`,`PartDesc`,`PartPrice`,`Quantity`,`ROID`,`Supplier`,`Cost`,`PartInvoiceNumber`,`PartCode`,`LineTTLPrice`,`LineTTLCost`,`Date`,`PartCategory`,`complaintid`,`discount`,`net`,`tax`,`bin`,`overridematrix`,`POSTed` from parts where deleted = 'no' and shopid = ? and roid = ? and complaintid = ?";
                if ($query = $conn->prepare($stmt)){
                    $query->bind_param("sii",$shopid,$roid,$comid);
                    $query->execute();
                    $result = $query->get_result();
                    while ($row = $result->fetch_assoc()){
                        $totalrec += $row["LineTTLPrice"];
                        $pn = $row['PartNumber'];
                        $pd = $row['PartDesc'];
                        $q = $row['Quantity'];
                        $supp = $row['Supplier'];
                        $cost = $row['Cost'];
                        $pin = $row['PartInvoiceNumber'];
                        $pc = $row['PartCode'];
                        $pp = $row['PartPrice'];
                        $extp = $row['LineTTLPrice'];
                        $extc = $row['LineTTLCost'];
                        $cost = $row['Cost'];
                        $pdate = $row['Date'];
                        $cat = $row['PartCategory'];
                        $disc = $row['discount'];
                        $net = $row['net'];
                        $tax = $row['tax'];
                        $allocated = $row['allocated'];
                        $bin = $row['bin'];
                        $overridematrix = $row['overridematrix'];
                        $tstmt = "insert into recommendparts (allocated,shopid,recid,partnumber,partdesc,partprice,quantity,roid,supplier,cost,partinvoicenumber,partcode,"
                            . "linettlprice,linettlcost,`date`,partcategory,complaintid,discount,`net`,tax,bin,overridematrix) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
                        if ($query = $conn->prepare($tstmt)){
                            $query->bind_param("ssissddisdssddssiddsss",$allocated,$shopid,$id,$pn,$pd,$pp,$q,$roid,$supp,$cost,$pin,$pc,$extp,$extc,$pdate,$cat,$comid,$disc,$net,$tax,$bin,$overridematrix);
                            if ($query->execute()){
                                $conn->commit();
                            }
                        }

                        // update the partinventory quantities
                        if (strtoupper($allocated) != 'NON' && $updateinvonadd=='YES'){
                            $ustmt = "update partsinventory set netonhand = netonhand + $q, onhand = onhand + $q where shopid = '$oshopid' and partnumber = ?";
                            if ($uquery = $conn->prepare($ustmt)){
                                $uquery->bind_param("s",$pn);
                                if ($uquery->execute()){
                                    $conn->commit();
                                }
                            }
                        }

                    }
                }

                $stmt = "update recommend set totalrec = $totalrec where id = $id";
                if ($query = $conn->prepare($stmt)){
                    if ($query->execute()){
                        $conn->commit();
                        //echo "success";
                    }else{
                        echo $conn->errno;
                    }
                }else{
                    echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $stmt = "delete from parts where shopid = '$shopid' and complaintid = $comid and roid = $roid";
                //echo $stmt."\r\n";
                if ($query = $conn->prepare($stmt)){
                    if ($query->execute()){
                        $conn->commit();
                        //echo "success";
                    }else{
                        echo $conn->errno;
                    }
                }else{
                    echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $stmt = "delete from labor where shopid = '$shopid' and complaintid = $comid and roid = $roid";
                //echo $stmt."\r\n";
                if ($query = $conn->prepare($stmt)){
                    if ($query->execute()){
                        $conn->commit();
                        //echo "success";
                    }else{
                        echo $conn->errno;
                    }
                }else{
                    echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $stmt = "delete from sublet where shopid = '$shopid' and complaintid = $comid and roid = $roid";
                //echo $stmt."\r\n";
                if ($query = $conn->prepare($stmt)){
                    if ($query->execute()){
                        $conn->commit();
                        //echo "success";
                    }else{
                        echo $conn->errno;
                    }
                }else{
                    echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $stmt = "update complaints set acceptdecline = ? where shopid = ? and roid = ? and complaintid = ?";
                if ($query = $conn->prepare($stmt)){
                    $query->bind_param("sssi",$newstatus,$shopid,$roid,$comid);
                    if ($query->execute()){
                        $conn->commit();

                        //notification
                        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='11'";
                        $query = $conn->prepare($stmt);
                        $query->execute();
                        $query->store_result();
                        $numrows = $query->num_rows();
                        if ($numrows > 0)
                        {
                            $query->bind_result($textcontent,$emailcontent,$popupcontent);
                            $query->fetch();
                            $emailcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent));
                            $popupcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent));
                            $textcontent=str_replace("*|RO|*",$roid,$textcontent);
                            $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'11',?,?,?)";
                            if ($query = $conn->prepare($stmt))
                            {
                                $query->bind_param('isss',$shopid,$popupcontent,$textcontent,$emailcontent);
                                $query->execute();
                                $conn->commit();
                                $query->close();
                            }

                        }

                        //echo "success";
                    }else{
                        echo $conn->errno;
                    }

                }else{
                    echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

            }
            else
            {

                echo "There is a technician clocked in on one of the Customer Concern. You cannot decline it until they clock out";

            }

        }

        elseif ($oldstatus == "declined" && $rawstatus != "declined") //scenario 3

        {

            $stmt = "select id from recommend where shopid = '$shopid' and roid = $roid and comid = $comid";
            if ($query = $conn->prepare($stmt))
            {
                $query->execute();
                $query->bind_result($recid);
                $query->fetch();
                $query->close();
            }
            else
            {
                echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            // restore to approved the vehicle issue
            $stmt = "update complaints set acceptdecline = ? where shopid = ? and roid = ? and complaintid = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("ssii",$newstatus,$shopid,$roid,$comid);
                $query->execute();
                $conn->commit();
                $query->close();

                //notification
                $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='8'";
                $query = $conn->prepare($stmt);
                $query->execute();
                $query->store_result();
                $numrows = $query->num_rows();
                if ($numrows > 0) {
                    $query->bind_result($textcontent, $emailcontent, $popupcontent);
                    $query->fetch();
                    $emailcontent = str_replace("*|STATUS|*", $newstatus, str_replace("*|RO|*", $roid, str_replace("*|SN|*", $sn, $emailcontent)));
                    $popupcontent = str_replace("*|STATUS|*", $newstatus, str_replace("*|RO|*", $roid, str_replace("*|SN|*", $sn, $popupcontent)));
                    $textcontent = str_replace("*|STATUS|*", $newstatus, str_replace("*|RO|*", $roid, $textcontent));
                    $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'8',?,?,?)";
                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param('isss', $shopid, $popupcontent, $textcontent, $emailcontent);
                        $query->execute();
                        $conn->commit();
                        $query->close();
                    }
                }

            } else {
                echo "Connection Error|Updating Complaints";
                exit;
            }

            $stmt = "select upper(updateinvonadd) from company where shopid = ?";
            if ($query = $conn->prepare($stmt)) {

                $query->bind_param("s", $shopid);
                $query->execute();
                $query->bind_result($updateinvonadd);
                $query->fetch();
                $query->close();

            } else {
                echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
            }

            // 2. get the parts from the recommendparts table and restore them to the parts table
            $stmt = "select shopid,partnumber,partdesc,partprice,quantity,roid,supplier,cost,partinvoicenumber,partcode,linettlprice,linettlcost,`date`,partcategory,complaintid,discount,net,tax,allocated,bin,overridematrix from recommendparts where shopid = '$shopid' and recid = $recid";

            if ($result = $conn->query($stmt)) {
                while ($row = $result->fetch_array()) {
                    $stmt = "insert into parts (shopid,partnumber,partdesc,partprice,quantity,roid,supplier"
                        . ",cost,partinvoicenumber,partcode,linettlprice,linettlcost,`date`,partcategory,"
                        . "complaintid,discount,net,tax,allocated,bin,overridematrix) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("sssddisdssddssissssss", $row["shopid"],$row["partnumber"],$row["partdesc"],$row["partprice"],$row["quantity"],$roid,$row["supplier"],$row["cost"],$row["partinvoicenumber"],$row["partcode"],$row["linettlprice"],$row["linettlcost"],$row["date"],$row["partcategory"],$row['complaintid'],$row["discount"],$row["net"],$row["tax"],$row['allocated'],$row['bin'],$row['overridematrix']);
                        if ($query->execute()) {
                            $conn->commit();
                            $query->close();
                        } else {
                            echo "Execution Error|Inserting into Part from RecommendParts";
                            exit;
                        }
                    } else {
                        echo "Connection Error|Inserting into Parts from RecommendParts";
                        exit;
                    }

                    if (strtolower($row['allocated']) != 'non' && $updateinvonadd == 'YES') {
                        // update inventory
                        $uquantity = $row["quantity"];
                        $upartnum = $row["partnumber"];
                        $ustmt = "update partsinventory set onhand = onhand - $uquantity, netonhand = netonhand - $uquantity where shopid = ? and partnumber = ?";
                        if ($uquery = $conn->prepare($ustmt)) {
                            $uquery->bind_param("ss", $oshopid, $upartnum);
                            if ($uquery->execute()) {
                                $conn->commit();
                                $uquery->close();
                            } else {
                                echo $conn->errno;
                            }

                        } else {
                            echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }
                    }

                }
            }

            // 3. Now get the labor
            $stmt = "select id,recid,shopid,roid,comid,`desc`,rate,ratelabel,hours,total,tech from recommendlabor where shopid = '$shopid' and recid = $recid";
            if ($result = $conn->query($stmt)) {
                while ($row = $result->fetch_array()) {
                    $stmt = "insert into labor (shopid,roid,hourlyrate,ratelabel,laborhours,labor,tech,linetotal,complaintid) values (?,?,?,?,?,?,?,?,?)";

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("sidsdssdi",$shopid,$roid,$row['rate'],$row['ratelabel'],$row['hours'],$row['desc'],$row['tech'],$row['total'],$row['comid']);
                        if ($query->execute()) {
                            $conn->commit();
                            $query->close();
                        } else {
                            echo "Execution Error|Inserting into Labor from RecommendLabor";
                            exit;
                        }
                    } else {
                        echo "Connection Error|Inserting into Labor from RecommendLabor";
                        exit;
                    }
                }
            }

            // 3.5  get the sublet
            $newsubletid = 1;
            $stmt = "select subletid from sublet where shopid = '$shopid' order by subletid desc limit 1";
            if ($query = $conn->prepare($stmt)) {
                $query->execute();
                $query->bind_result($subletid);
                if ($query->fetch()) {
                    if (is_numeric($subletid)) {
                        $newsubletid = $subletid + 1;
                    }
                }

                $query->close();


            } else {
                echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            $stmt = "select shopid,roid,comid,subletdesc,subletcost,subletprice,supplier from recommendsublet where shopid = '$shopid' and recid = $recid";
            if ($result = $conn->query($stmt)) {
                while ($row = $result->fetch_array()) {
                    $stmt = "insert into sublet (shopid,SubLetID,ROID,SubletDesc,SubletPrice,SubletCost,SubletSupplier,complaintid) values (?,?,?,?,?,?,?,?)";
                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("siisddsi",$shopid,$newsubletid,$roid,$row['subletdesc'],$row['subletprice'],$row['subletcost'],$row['supplier'],$row['comid']);
                        if ($query->execute()) {
                            $conn->commit();
                            $query->close();
                            $newsubletid += 1;
                        } else {
                            echo $conn->error;
                        }
                    } else {
                        echo $conn->error;
                    }
                }
            }

            // 4.  now delete from the recommendparts and recommendlabor
            $stmt = "delete from recommend where shopid = '$shopid' and id = $recid";
            if ($query = $conn->prepare($stmt)) {
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();
                } else {
                    echo "Execution Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Labor from RecommendLabor";
                exit;
            }


            $stmt = "delete from recommendlabor where shopid = '$shopid' and recid = $recid";
            if ($query = $conn->prepare($stmt)) {
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();
                } else {
                    echo "Execution Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Labor from RecommendLabor";
                exit;
            }


            $stmt = "delete from recommendparts where shopid = '$shopid' and recid = $recid";
            if ($query = $conn->prepare($stmt)) {
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();
                } else {
                    echo "Execution Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Labor from RecommendLabor";
                exit;
            }

            $stmt = "delete from recommendsublet where shopid = '$shopid' and recid = $recid";
            if ($query = $conn->prepare($stmt)) {
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();
                } else {
                    echo "Execution Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Labor from RecommendLabor";
                exit;
            }
        }

    }
}

echo("success");