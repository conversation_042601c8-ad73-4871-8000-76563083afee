<?php
require CONN;
$shopid = $oshopid = $_COOKIE['shopid'];
$roid = $_GET['roid'];
$comid = $_GET['comid'];
$partid = $_GET['partid'];
$applyDiscount = $_COOKIE['applydiscounts'] ? strtolower($_COOKIE['applydiscounts']) :'no';

if (isset($_GET['invtype'])) {
    $invtype = $_GET['invtype'];
} else {
    $invtype = "";
}

if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

if ($invtype == "NON" || $invtype == "") {
    if ($oshopid == '1238' || $oshopid == "1073" || $oshopid == "1191" || $oshopid == "1305") {
        $partreg = "`partsregistry-$oshopid`";
    } else {
        $partreg = "partsregistry";
    }
} elseif ($invtype == "INV") {
    $partreg = "partsinventory";
}

$stmt = "select PartNumber,PartDesc,coalesce(PartPrice,0),PartCode,coalesce(PartCost,0),PartSupplier,OnHand,Allocatted,NetOnHand,ReOrderLevel,MaxOnHand,MaintainStock,OrderStatus,TransitStatus,PartCategory,bin,tax,overridematrix,partid,corecharge "
    . "from $partreg where shopid = ? and partid = ? ";


if ($query = $conn->prepare($stmt)) {

    $query->bind_param("si", $oshopid, $partid);

    $query->execute();
    $query->store_result();
    $pn_num_rows = $query->num_rows;
    if ($pn_num_rows > 0) {
        $query->bind_result($PartNumber, $PartDesc, $PartPrice, $PartCode, $PartCost, $PartSupplier, $OnHand, $Allocatted, $NetOnHand, $ReOrderLevel, $MaxOnHand, $MaintainStock, $OrderStatus, $TransitStatus, $PartCategory, $bin, $tax, $overridematrix, $partid, $corecharge);
        $query->fetch();
    } else {
        $PartNumber = '';
        $PartDesc = '';
        $PartPrice = 0;
        $PartCode = '';
        $PartCost = 0;
        $PartSupplier = '';
        $OnHand = '';
        $Allocatted = '';
        $NetOnHand = '';
        $ReOrderLevel = '';
        $MaxOnHand = '';
        $MaintainStock = '';
        $OrderStatus = '';
        $TransitStatus = '';
        $PartCategory = '';
        $discount = '';
        $net = '';
        $bin = '';
        $tax = '';
        $overridematrix = '';
        $partid = '';
        $corecharge = '';
    }

    $query->close();

} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "select usepartsmatrix,updateinvonadd,chargehst,chargepst,chargegst,chargeqst,hstapplyon,pstapplyon,gstapplyon,qstapplyon,companyzip from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    if ($num_roid_rows > 0) {
        $query->bind_result($usematrix, $updateinvonadd, $chargehst, $chargepst, $chargegst, $chargeqst, $hstapplyon, $pstapplyon, $gstapplyon, $qstapplyon, $companyzip);
        $query->fetch();
    } else {
        $usematrix = "no";
    }
    $query->close();

} else {
    echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
}

if ($shopid == '1238') {
    $overridematrix = "no";
}

$stmt = "SELECT p.tax FROM parts p INNER JOIN repairorders r ON p.shopid = r.shopid AND p.roid = r.roid INNER JOIN customer c ON r.shopid = c.shopid AND r.customerid = c.customerid WHERE r.status = 'CLOSED' AND r.ROType != 'No Approval' AND r.shopid = ? AND p.partnumber = ? AND c.taxexempt = 'no' ORDER BY r.statusdate DESC LIMIT 1;";
$tax_override = false;
if ($query = $conn->prepare($stmt)) {

    $query->bind_param("ss", $shopid, $PartNumber);
    $query->execute();
    $query->store_result();
    $pn_num_rows = $query->num_rows;
    if ($pn_num_rows > 0) {
        $query->bind_result($prev_tax);
        $query->fetch();
        if (!empty($prev_tax) && (strtolower($prev_tax) != strtolower($tax))) {
            $tax_override = true;
            $tax = $prev_tax;
        }
    }

    $query->close();

} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "select lower(c.taxexempt) from repairorders r,customer c where r.origshopid=c.shopid and r.customerid=c.customerid and r.shopid = ? and r.roid = ?";
if ($query = $conn->prepare($stmt)){

    $query->bind_param("si",$shopid,$roid);
    $query->execute();
    $query->bind_result($taxexempt);
    $query->fetch();
    $query->close();
}

if ($taxexempt == "yes")
$tax = 'no';

$cantaxes = array();
$canstr = '';


if (!is_numeric($companyzip) && $chargehst == "yes"){
    if(stripos($hstapplyon,'A')!==false || stripos($hstapplyon,'P')!==false) 
    {
        if($tax=='yes' && (empty($partcantaxes) || stripos($partcantaxes,'H')!==false))$savedcantaxes[] = 'HST';
        $canstr .= "<div class='form-check'><label class='form-check-label'>HST</label><input type='checkbox' class='form-check-input cancheck' ".(empty($partcantaxes) || stripos($partcantaxes,'H')!==false?"checked='checked'":'')." value='HST'></div>";
        $cantaxes[] = 'HST';
    }
}

if (!is_numeric($companyzip) && $chargegst == "yes"){
    if(stripos($gstapplyon,'A')!==false || stripos($gstapplyon,'P')!==false)
    {
        if($tax=='yes' && (empty($partcantaxes) || stripos($partcantaxes,'G')!==false))$savedcantaxes[] = 'GST';
        $canstr .= "<div class='form-check'><label class='form-check-label'>GST</label><input type='checkbox' class='form-check-input cancheck' ".(empty($partcantaxes) || stripos($partcantaxes,'G')!==false?"checked='checked'":'')." value='GST'></div>";
        $cantaxes[] = 'GST';
    }
}

if (!is_numeric($companyzip) && $chargepst == "yes"){
    if(stripos($pstapplyon,'A')!==false || stripos($pstapplyon,'P')!==false)
    {
        if($tax=='yes' && (empty($partcantaxes) || stripos($partcantaxes,'P')!==false))$savedcantaxes[] = 'PST';
        $canstr .= "<div class='form-check'><label class='form-check-label'>PST</label><input type='checkbox' class='form-check-input cancheck' ".(empty($partcantaxes) || stripos($partcantaxes,'P')!==false?"checked='checked'":'')." value='PST'></div>";
        $cantaxes[] = 'PST';
    }
}

if (!is_numeric($companyzip) && $chargeqst == "yes"){
    if(stripos($qstapplyon,'A')!==false || stripos($qstapplyon,'P')!==false)
    {
        if($tax=='yes' && (empty($partcantaxes) || stripos($partcantaxes,'Q')!==false))$savedcantaxes[] = 'QST';
        $canstr .= "<div class='form-check'><label class='form-check-label'>QST</label><input type='checkbox' class='form-check-input cancheck' ".(empty($partcantaxes) || stripos($partcantaxes,'Q')!==false?"checked='checked'":'')." value='QST'></div>";
        $cantaxes[] = 'QST';
    }
}

include getHeadGlobal('');

?>
<body>

<form name="mainform" id="mainform">
    <input type="hidden" name="shopid" id="shopid" value="<?php echo $shopid; ?>">
    <input type="hidden" name="roid" id="roid" value="<?php echo $roid; ?>">
    <input type="hidden" name="comid" id="comid" value="<?php echo $comid; ?>">
    <input type="hidden" name="discountamt" id="discountamt" value="0">
    <input type="hidden" name="cantaxes" id="cantaxes">
    <input type="hidden" name="invid" value="<?= $partid?>">

    <div class="d-flex">

        <div class="row container-fluid mt-4 emodal-content">

            <div class="col-md-6">

                <div class="form-row mb-4">
                    <select class="select" tabindex="1" name="overridematrix" id="overridematrix">
                        <?php
                        if (strtolower($overridematrix) == "yes") {
                            $ys = ' selected="selected" ';
                            $ns = '';
                        } else {
                            $ys = '';
                            $ns = ' selected="selected" ';
                        }
                        ?>
                        <option <?php echo $ys; ?> value="yes">Yes</option>
                        <option <?php echo $ns; ?> value="no">No</option>
                    </select>
                    <label class="form-label select-label" for="overridematrix">Override Matrix Price</label>
                </div>

                <div class="form-outline mb-4">
                    <input class="form-control" tabindex="2" type="text" id="partnum" value="<?php echo strtoupper($PartNumber); ?>" name="PartNumber">
                    <label class="form-label" for="partnum">Part Number</label>
                </div>

                <div class="form-outline mb-4">
                    <input class="form-control" tabindex="3" type="text" id="partdesc" value="<?php echo str_replace('"', ' in ', strtoupper($PartDesc)); ?>" name="PartDesc">
                    <label class="form-label" for="partdesc">Part Description</label>
                </div>

                <div class="form-row mb-4">
                    <select onchange="calcPrices()" class="select" tabindex="4" name="PartCategory" id="PartCategory">
                        <?php
                        $stmt = "select distinct category c from category where shopid = ? order by displayorder";
                        $matrix = strtolower($_COOKIE['matrix']);
                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("s", $shopid);
                            $query->execute();
                            $result = $query->get_result();
                            $query->store_result();
                            $numrows = $result->num_rows;
                            if ($numrows > 0) {
                                while ($row = $result->fetch_array()) {
                                    if (strtoupper($PartCategory) == strtoupper($row['c'])) {
                                        $s = "selected";
                                    } else {
                                        $s = "";
                                    }
                                    echo "<option $s value='" . strtoupper($row['c']) . "'>" . strtoupper($row['c']) . "</option>";
                                }
                            } else {
                                echo "<option value='none'>No Suppliers Entered</option>";
                            }
                        }
                        ?>
                    </select>

                    <label class="form-label select-label" for="PartCategory">Matrix Category</label>
                </div>


                <div class="form-outline mb-4">
                    <input class="form-control" onblur="calcPrices()" tabindex="5" type="text" id="qty" value="1" name="qty">
                    <label class="form-label" for="qty">Quantity</label>
                </div>


                <div class="form-outline mb-4">
                    <input class="form-control" onblur="calcPrices()" tabindex="6" type="text" id="PartCost" value="<?php echo number_format($PartCost, 2, '.', ''); ?>" name="PartCost">
                    <label class="form-label" for="PartCost">Shop Cost</label>
                </div>


                <div class="form-outline mb-4">
                    <input class="form-control" onblur="calcPrices()" tabindex="7" type="text" id="PartPrice" value="<?php echo number_format($PartPrice, 2, '.', ''); ?>" name="PartPrice">
                    <label class="form-label" for="PartPrice">Selling Price</label>
                </div>


                <div class="form-outline mb-4">
                    <input class="form-control" onblur="calcPrices()" onkeyup="calcPrices()" tabindex="8" type="text" id="Discount" value="0" name="Discount" <?= ($applyDiscount == "yes" ? "" : "readonly") ?> >
                    <label class="form-label" for="Discount">Discount %</label>
                </div>


                <div class="form-outline mb-4">
                    <input class="form-control" onblur="calcPrices()" tabindex="9" type="text" id="net" name="net" value="0">
                    <label class="form-label" for="net">Net Selling Price</label>
                </div>

            </div>

            <div class="col-md-6">

                <div class="form-outline mb-4">
                    <input class="form-control" tabindex="10" type="text" id="core" name="core" value="<?= $corecharge ?>">
                    <label class="form-label" for="core">Core Charge</label>
                </div>


                <div class="form-row mb-4">
                    <select onkeyup="" class="select" tabindex="11" name="PartCode" id="PartCode">
                        <option selected value="<?php echo strtoupper($PartCode); ?>"><?php echo strtoupper($PartCode); ?></option>
                        <?php
                        $stmt = "select codes from codes where shopid = ? and codes != ? order by codes asc";
                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("ss", $shopid, $PartCode);
                            $query->execute();
                            $result = $query->get_result();
                            $query->store_result();
                            $numrows = $result->num_rows;
                            if ($numrows > 0) {
                                while ($row = $result->fetch_array()) {
                                    echo '<option value="' . strtoupper($row['codes']) . '">' . strtoupper($row['codes']) . '</option>';
                                }
                            } else {
                                echo '<option value="New">New</option>';
                            }
                        }
                        ?>
                    </select>
                    <label class="form-label select-label" for="PartCode">Part Code</label>
                </div>

                <div class="form-row mb-4">
                    <div class="form-control border-primary text-primary">
                        <label id="select2floatinglabel" class="form-label select-label active" for="PartCategory">Part Supplier</label>
                        <select onkeyup="" class="" tabindex="12" name="PartSupplier" id="PartSupplier">
                            <option selected value="<?php echo strtoupper($PartSupplier); ?>"><?php echo strtoupper($PartSupplier); ?></option>
                            <?php
                            $supplier = "";
                            $stmt = "select suppliername s from supplier where suppliername != ? and shopid = ? and active = 'YES' order by displayorder,suppliername";
                            $matrix = strtolower($_COOKIE['matrix']);
                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("ss", $PartSupplier, $shopid);
                                $query->execute();
                                $result = $query->get_result();
                                $query->store_result();
                                $numrows = $result->num_rows;
                                if ($numrows > 0) {
                                    while ($row = $result->fetch_array()) {
                                        if (strtoupper($PartSupplier) == strtoupper($row['s'])) {
                                            $s = "selected";
                                        } else {
                                            $s = "";
                                        }
                                        echo "<option $s value=\"" . strtoupper($row['s']) . "\">" . strtoupper($row['s']) . "</option>";
                                    }
                                } else {
                                    echo "<option value='none'>No Suppliers Entered</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>


                <div class="form-outline mb-4">
                    <input class="form-control" tabindex="13" type="text" id="ponum" name="ponum">
                    <label class="form-label" for="ponum">PO #</label>
                </div>


                <div class="form-outline mb-4">
                    <input class="form-control" tabindex="14" type="text" id="bin" name="bin" value="<?php echo $bin; ?>">
                    <label class="form-label" for="bin">Bin</label>
                </div>


                <div class="form-outline mb-4">
                    <input class="form-control" tabindex="15" type="text" id="invoicenumber" name="invoicenumber">
                    <label class="form-label" for="invoicenumber">Part Invoice Number</label>
                </div>


                <div class="form-outline mb-4">
                    <input class="form-control" tabindex="16" type="text" id="extprice" name="extprice" value="0">
                    <label class="form-label" for="extprice">Extended Price</label>
                </div>


                <div class="form-outline mb-4">
                    <input class="form-control" tabindex="17" type="text" id="extcost" name="extcost" value="0">
                    <label class="form-label" for="extcost">Extended Cost</label>
                </div>


                <div class="form-row mb-4">
                    <select onkeyup="" class="select" tabindex="18" name="tax" id="tax">
                        <?php
                        if (strtolower($tax) == "yes") {
                            $ys = ' selected="selected" ';
                            $ns = '';
                        } else {
                            $ys = '';
                            $ns = ' selected="selected" ';
                        }
                        ?>
                        <option <?php echo $ys; ?> value="yes">Yes</option>
                        <option <?php echo $ns; ?> value="no">No</option>
                    </select>
                    <label class="form-label select-label" for="tax">Taxable <?php if ($tax_override){ ?><span class="red">(Taxable is based on previous selection)</span><?php } ?></label>
                    <?php if(count($cantaxes)>1){?><small><a href="javascript:void(null)" id="cancustom" style="display:<?= $tax=='no'?'none':''?>" data-mdb-toggle="modal" data-mdb-target="#cantaxmodal">(<?= !empty($savedcantaxes)?implode('+',$savedcantaxes):'None'?>)</a></small><?php }?>
                </div>
                
                <?php if($_COOKIE['mode'] == 'full'){?>
                    <div class="form-row mb-4">
                        <select class="select" tabindex="19" id="salesperson" name="salesperson">
                            <option value="nosalesperson">None</option>
                            <?php
                            $stmt = "select employeelast,employeefirst from employees where active = 'yes' and shopid = '$shopid' order by employeelast";
                            if ($query = $conn->prepare($stmt)) {
                                $query->execute();
                                $result = $query->get_result();
                                while ($row = $result->fetch_array()) {
                                    echo "<option value='" . strtoupper($row['employeelast'] . ", " . $row['employeefirst']) . "'>" . strtoupper($row['employeelast'] . ", " . $row['employeefirst']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                        <label class="form-label select-label" for="salesperson">Who Sold This Part (For Commissions)?</label>
                    </div>
                <?php }?>
            </div>
        </div>

    </div>

    </div>

    <nav class="fixed-bottom emodal-footer">
        <button type="button" onclick="calcPrices();savePart('yes')" class="btn btn-secondary btn-md btn-savepart me-3">Save and Add Another</button>
        <button type="button" onclick="calcPrices();savePart('no')" class="btn btn-primary btn-md btn-savepart">Save</button>
    </nav>

</form>


<div id="cantaxmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="cantaxmodalLabel">Select Tax</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <?= $canstr?>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-center pt-2">
            <button class="btn btn-md btn-primary" data-mdb-dismiss="modal" type="button" onclick="saveCanTax()">Save</button>
            </div>
        </div>
    </div>
</div>


<?php include getScriptsGlobal(''); ?>
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.7/css/select2.min.css" rel="stylesheet"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.7/js/select2.min.js"></script>
<style>
    .select2-results__message {
        display: none !important;
    }

    .mdb-select2 {
        width: 100%;
    }

    .bmd-form-group .form-control, .bmd-form-group label, .bmd-form-group input::placeholder {
        /*  line-height: 1 */
    }

    span.select2.select2-container.select2-container--default, .select2-selection__rendered {
        color: var(--primary) !important;
    }

    .select2-container--default .select2-selection--single {
        border-top: 0;
        border-left: 0;
        border-right: 0;
        border-bottom: none;
        background-color: transparent;
        border-radius: 0;
    }

    .select2-dropdown, .select2-dropdown--above {
        border-radius: 0;
        padding: 0.35rem;
        border: 0;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16), 0 2px 10px 0 rgba(0, 0, 0, .12);
    }

    .select2-container--default .select2-selection--multiple {
        border-top: 0;
        border-left: 0;
        border-right: 0;
        border-bottom: none;
        background-color: transparent;
        border-radius: 0;
        height: 40px;
    }

    .select2-container *:focus {
        outline: none;
    }

    .select2-container--default.select2-container--focus .select2-selection--multiple {
        border: 0;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {

    }

    .select2-results__option:hover {
        color: var(--primary) !important;
        background-color: var(--mdb-form-outline-select-option-hover-not-disabled-bg) !important;
    }


    .select2-results__option--highlighted, .select2-results__option[aria-selected="true"] {
        color: var(--primary) !important;
        background-color: var(--mdb-form-outline-select-option-hover-not-disabled-bg) !important;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {

    }

    .select2-container--default .select2-results__option[aria-selected="true"] {
        background-color: var(--mdb-form-outline-select-option-selected-active-bg);
        color: var(--primary);
    }

    .select2-selection__arrow {
        color: var(--mdb-form-outline-select-arrow-color);
        text-align: center;
        font-size: 0.8rem !important;
        position: absolute;
        top: 5px !important;
    }

    .select2-selection__arrow::before {
        content: "▼";
    }

    .select2-selection__arrow b {
        display: none;
    }

    #select2floatinglabel {
        position: absolute;
        /* top: 0; */
        max-width: 90%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        /* left: 0.75rem; */
        /* padding-top: 0.37rem; */
        pointer-events: none;
        transform-origin: 0 0;
        transition: all .2s ease-out;
        color: rgba(0, 0, 0, .6);
        margin-bottom: 0;
        margin-top: -11px;
        font-size: 0.7rem !important;
        margin-left: 0.5rem !important;
        background: white;
        padding: 0px 5px 0px 3px;
    }

</style>
<script>

    $(document).ready(function () {

        $("#PartSupplier").select2(
            {
                tags: false,
                width: '100%',
                placeholder: "Supplier",
                selectOnClose: true,
                dropdownAutoWidth: true,

                /*
                matcher: function (params, data) {
                    return matchStart(params, data);
                }

                 */
            }
        );

    });
    $('#partnum').on('keyup', function (e) {
        if (e.key == "#") {
            var str = $('#partnum').val();
            $('#partnum').val(str.substring(0, str.length - 1));
            sbalert("You cannot use # in part numbers")
        }
    })

    function savePart(addanother) {
        setTimeout(function () {
            partnumber = $('#partnum').val()
            partdesc = $('#partdesc').val()
            partcat = $('#PartCategory').val()
            qty = $('#qty').val()
            partcost = $('#PartCost').val()
            partprice = $('#PartPrice').val()
            partnet = $('#net').val()
            extcost = $('#extcost').val()
            extprice = $('#extprice').val()

            if (partnumber == "") {
                sbalert("Part Number is a required field");
                $('#partnum').focus();
                $('.btn-savepart').attr('disabled', false);
                return
            }
            if (partdesc == "") {
                sbalert("Part Description is a required field");
                $('#partdesc').focus();
                $('.btn-savepart').attr('disabled', false);
                return
            }
            if (partcat == "") {
                sbalert("Part Category is a required field");
                $('#PartCategory').focus();
                $('.btn-savepart').attr('disabled', false);
                return
            }
            if (!$.isNumeric(qty)) {
                sbalert("Quantity is a required field");
                $('#qty').focus();
                $('.btn-savepart').attr('disabled', false);
                return
            }
            if (!$.isNumeric(partcost)) {
                sbalert("Part Cost is a required field");
                $('#PartCost').focus();
                $('.btn-savepart').attr('disabled', false);
                return
            }
            if (!$.isNumeric(partprice)) {
                sbalert("Part Price is a required field");
                $('#PartPrice').focus();
                $('.btn-savepart').attr('disabled', false);
                return
            }
            if (!$.isNumeric(partnet)) {
                sbalert("Net Selling Price is a required field");
                $('#net').focus();
                $('.btn-savepart').attr('disabled', false);
                return
            }
            if (!$.isNumeric(extcost)) {
                sbalert("Extended Cost is a required field");
                $('#extcost').focus();
                $('.btn-savepart').attr('disabled', false);
                return
            }
            if (!$.isNumeric(extprice)) {
                sbalert("Extended Price is a required field");
                $('#extprice').focus();
                $('.btn-savepart').attr('disabled', false);
                return
            }


            // post the information
            ds = $('#mainform').serialize() + "&invtype=<?php echo $invtype; ?>&updateinv=<?php echo $updateinvonadd; ?>"

            if ($('#tax').val() == "no") {

                sbconfirm("Are you sure?", "You have this part marked as NON-TAXABLE. Are you sure?", function () {

                    $('.btn-savepart').attr('disabled', 'disabled')

                    $.ajax({
                        data: ds,
                        type: "post",
                        url: "addfoundpartaction.php",
                        success: function (r) {
                            if (r !== 'success') {
                                sbalert("Error Adding Part")
                            } else {
                                if (addanother == "yes") {
                                    location.href = 'addpart.php?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&comid=<?php echo $comid; ?>'
                                } else {
                                    parent.location.reload()
                                }
                            }
                            $('.btn-savepart').attr('disabled', false)
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        }
                    });
                });
            } else {

                $('.btn-savepart').attr('disabled', 'disabled')

                $.ajax({
                    data: ds,
                    type: "post",
                    url: "addfoundpartaction.php",
                    success: function (r) {

                        if (r == "success") {
                            if (addanother == "yes") {
                                location.href = 'addpart.php?shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&comid=<?php echo $comid; ?>'
                            } else {
                                parent.location.reload()
                            }
                        } else {
                            sbalert("Error Adding Part")
                        }
                        $('.btn-savepart').attr('disabled', false)
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    }
                });
            }


            $('#spinner').hide()
        }, 500);
    }


    <?php

    if (strtolower($usematrix) == "yes"){
    ?>
    function calcPrices() {

        <?php

        $stmt = "select category,factor,start,end from category where shopid = ? order by Category, Start";

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $result = $query->get_result();
            $rs = array();
            while ($row = $result->fetch_assoc()) {
                $rs[] = $row;
            }
            echo "var clist = " . json_encode($rs) . "\r\n";
        }
        ?>
        if ($('#overridematrix').val() == "yes") {
            if (parseFloat($('#Discount').val()) > 0) {

                disc = parseFloat($('#Discount').val()) / 100
                qty = $('#qty').val()
                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())) * disc).toFixed(2)
                baseprice = $('#PartPrice').val()
                discamt = baseprice * disc
                netprice = ($('#PartPrice').val() - discamt)
                $('#net').val(netprice.toFixed(2))
                $('#extcost').val(extcost)
                $('#discountamt').val(discamt.toFixed(2))
                $('#extprice').val((netprice * qty).toFixed(2))

            } else {
                $('#discountamt').val('0')
                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                $('#net').val($('#PartPrice').val())
                $('#extcost').val(extcost)
                $('#extprice').val(extprice)

            }
        } else {

            srch = $('#PartCategory').val().toLowerCase()
            console.log("cat:" + srch)
            amt = $('#PartCost').val()
            $.each(clist, function (i, v) {
                if (v.category.toUpperCase() === srch.toUpperCase() && v.start <= amt && v.end >= amt) {
                    $('#PartPrice').val(Math.round((amt * v.factor) * 100) / 100)
                }
                if (parseFloat($('#qty').val()) > 0 && parseFloat($('#PartPrice').val()) >= 0 && parseFloat($('#PartCost').val()) >= 0) {
                    if (parseFloat($('#Discount').val()) > 0) {

                        disc = parseFloat($('#Discount').val()) / 100
                        qty = $('#qty').val()
                        extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                        extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())) * disc).toFixed(2)
                        baseprice = $('#PartPrice').val()
                        discamt = baseprice * disc
                        netprice = ($('#PartPrice').val() - discamt)
                        $('#net').val(netprice.toFixed(2))
                        $('#extcost').val(extcost)
                        $('#discountamt').val(discamt.toFixed(2))
                        $('#extprice').val((netprice * qty).toFixed(2))
                    } else {
                        $('#discountamt').val('0')
                        extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                        extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                        $('#net').val($('#PartPrice').val())
                        $('#extcost').val(extcost)
                        $('#extprice').val(extprice)
                    }
                }

            });
        }
    }
    <?php
    }else{
    ?>
    function calcPrices() {
        if (parseFloat($('#qty').val()) > 0 && parseFloat($('#PartPrice').val()) >= 0 && parseFloat($('#PartCost').val()) >= 0) {
            if (parseFloat($('#Discount').val()) > 0) {

                disc = parseFloat($('#Discount').val()) / 100
                qty = $('#qty').val()
                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())) * disc).toFixed(2)
                baseprice = $('#PartPrice').val()
                discamt = baseprice * disc
                netprice = ($('#PartPrice').val() - discamt)
                $('#net').val(netprice.toFixed(2))
                $('#extcost').val(extcost)
                $('#discountamt').val(discamt.toFixed(2))
                $('#extprice').val((netprice * qty).toFixed(2))
            } else {
                $('#discountamt').val('0')
                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                $('#net').val($('#PartPrice').val())
                $('#extcost').val(extcost)
                $('#extprice').val(extprice)
            }
        }

    }
    <?php
    }
    ?>

    function matchStart(params, data) {
        params.term = params.term || '';
        if (data.text.toUpperCase().indexOf(params.term.toUpperCase()) == 0) {
            return data;
        }
        return false;
    }

    function saveCanTax() {
        var canstr = ''

        $(".cancheck").each(function () {
            if ($(this).is(':checked'))
                canstr = canstr + $(this).val() + '+';
        })

        canstr = canstr.substring(0, (canstr.length - 1));

        $('#cantaxes').val(canstr)

        if (canstr != '') {
            $('#cancustom').html('(' + canstr + ')')
            $('#tax').val('yes')
        } else {
            $('#cancustom').html('(None)')
            $('#tax').val('no')
        }
    }

    $(document).ready(function () {
        const input = document.getElementById("qty");
        input.focus();
        input.select();
        calcPrices()
        <?php echo "// " . $usematrix . "\r\n"; ?>

    });
</script>

</body>
</html>
