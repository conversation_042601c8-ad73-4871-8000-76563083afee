<?php
require CONN;


$shopid = $oshopid = filter_var($_POST['shopid'], FILTER_SANITIZE_STRING);
$roid = filter_var($_POST['roid'], FILTER_SANITIZE_STRING);
$comid = filter_var($_POST['comid'], FILTER_SANITIZE_STRING);
$partnumber = filter_var($_POST['PartNumber'], FILTER_SANITIZE_STRING);
$description = filter_var($_POST['PartDesc'], FILTER_SANITIZE_STRING);
$category = filter_var($_POST['PartCategory'], FILTER_SANITIZE_STRING);
$override = filter_var($_POST['overridematrix'], FILTER_SANITIZE_STRING);
$quantity = filter_var($_POST['qty'], FILTER_SANITIZE_STRING);
$cost = filter_var($_POST['PartCost'], FILTER_SANITIZE_STRING);
$price = filter_var($_POST['PartPrice'], FILTER_SANITIZE_STRING);
$discount = filter_var($_POST['Discount'], FILTER_SANITIZE_STRING);
$net = filter_var($_POST['net'], FILTER_SANITIZE_STRING);
$partcode = filter_var($_POST['PartCode'], FILTER_SANITIZE_STRING);
$supplier = filter_var($_POST['PartSupplier'], FILTER_SANITIZE_STRING);
$bin = filter_var($_POST['bin'], FILTER_SANITIZE_STRING);
$extcost = filter_var($_POST['extcost'], FILTER_SANITIZE_STRING);
$extprice = filter_var($_POST['extprice'], FILTER_SANITIZE_STRING);
$core = filter_var($_POST['core'], FILTER_SANITIZE_STRING);
$taxable = filter_var($_POST['tax'], FILTER_SANITIZE_STRING);
$invnum = filter_var($_POST['invoicenumber'], FILTER_SANITIZE_STRING);
$salesperson = filter_var($_POST['salesperson'], FILTER_SANITIZE_STRING);
$ponumber = (!empty($_POST['ponum'])?filter_var($_POST['ponum'], FILTER_SANITIZE_STRING):'');
$cantaxes = $_POST['cantaxes']??'';

if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

include_once "rostatus_check.php";

if($taxable=='no')$cantaxes = '';
elseif(!empty($cantaxes))
$cantaxes = str_replace('+',',',str_replace('ST','',$cantaxes));

if (isset($cnt)) {
	$cnt = $cnt;
}else{
	$cnt = 0;
}

//$ponumber = "";
$pstatus = "";
$deleted = "no";
$pdate = date('Y-m-d');
$partid = 0;

// new for core
$partorderdate = date('Y-m-d');
$returnstatus = 'Not Returned';

// add the part to the parts registry
if ($shopid == "1238" || $shopid == "1073" || $shopid == "1191" || $shopid == "1305"){
	$preg = "`partsregistry-".$shopid."`";
}else{
	$preg = "`partsregistry`";
}

$stmt = "select partid from $preg where shopid = ? and partnumber = ?";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("ss",$oshopid,$partnumber);
	$query->execute();
	$query->bind_result($partid);
	$query->fetch();
	$query->close();
}

if (empty($partid)){
	$stmt = "insert into ".$preg." (`shopid`,`PartNumber`,`PartDesc`,`PartPrice`,PartCode,partsupplier,`partCost`,tax,overridematrix,bin,PartCategory) values (?,?,?,?,?,?,?,?,?,?,?)";
	//echo $stmt;
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("sssdssdssss",$oshopid,$partnumber,$description,$price,$partcode,$supplier,$cost,$taxable,$override,$bin,$category);
		$query->execute();
		$partid = $conn->insert_id;
		$conn->commit();
		//recordAudit("Add Part", "Added Part Number $partnumber to RO#$roid");
		//echo "success";

	}else{
		echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
}


$stmt = "insert into parts (salesperson,`shopid`,`PartNumber`,`PartDesc`,`PartPrice`,`Quantity`,`ROID`,`Supplier`,`Cost`,`PartInvoiceNumber`,`PartCode`,`LineTTLPrice`,`LineTTLCost`,`Date`,`PartCategory`,`complaintid`,`discount`,`net`,`bin`,`tax`,`cantaxes`,`overridematrix`,`ponumber`,`pstatus`,`deleted`,`allocated`,invid) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,'NON',?)";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("ssssddisdssddssiddsssssssi",$salesperson,$shopid,$partnumber,$description,$price,$quantity,$roid,$supplier,$cost,$invnum,$partcode,$extprice,$extcost,$pdate,$category,$comid,$discount,$net,$bin,$taxable,$cantaxes,$override,$ponumber,$pstatus,$deleted,$partid);
	$query->execute();
	$conn->commit();
	recordAudit("Add Part", "Added Part Number $partnumber to RO#$roid");
	echo "success";

}else{
	echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

//printf(str_replace("?","'"."%s"."'",$stmt),$salesperson,$shopid,$partnumber,$description,$price,$quantity,$roid,$supplier,$cost,$invnum,$partcode,$extprice,$extcost,$pdate,$category,$comid,$discount,$net,$bin,$taxable,$override,$ponumber,$pstatus,$deleted);

$stmt = "select upper(updateinvonadd) from company where shopid = ?";
if ($query = $conn->prepare($stmt)){

	$query->bind_param("s",$oshopid);
    $query->execute();
    $query->bind_result($updateinvonadd);
    $query->fetch();
    $query->close();

}else{
	echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
}

if($updateinvonadd=='YES')
{
// update inventory quantities
$stmt = "update partsinventory set onhand = onhand - $quantity, netonhand = netonhand - $quantity where shopid = ? and partnumber = ?";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("ss",$oshopid,$partnumber);
    if ($query->execute()){
	    $conn->commit();
	    $query->close();
	   // echo "success";
	}else{
		echo $conn->errno;
	}

}else{
	echo "Update INventory Prepare failed: (" . $conn->errno . ") " . $conn->error;
}
}

// need to get the most recently added partnumber
// commenting out the Select
if ($core > 0 ){

	$stmt = "insert into cores (`shopid`,`PartNumber`,`PartDesc`,`corecharge`,supplier,partorderdate,returnstatus,roid) values (?,?,?,?,?,?,?,?)";
	//echo $stmt;
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("sssdsssi",$shopid,$partnumber,$description,$core,$supplier,$partorderdate,$returnstatus,$roid);
		$query->execute();
		$conn->commit();
		//recordAudit("Add Core", "Added Core Part Number $partnumber to RO#$roid");
		//echo "success";

	}else{
		echo "Core Insert Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

	//printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$corepart,$description,$core,$supplier,$partorderdate,$returnstatus,$coreroid);

}

mysqli_close($conn);

?>
