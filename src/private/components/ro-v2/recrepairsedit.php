<?php
ini_set("display_errors", 1);
error_reporting(E_ALL);

require CONN;

if (isset($desc)) {
    $desc = $desc;
} else {
    $desc = '';
}


$t = $_REQUEST['t'];
$today = date('Y-m-d');

$recrepairsjson = $_REQUEST['recrepairsjson'] ?? '';

if ($t == "detail") {

    $roid = $_REQUEST['roid'];
    $shopid = $_REQUEST['shopid'];
    $id = $_REQUEST['id'];

    $stmt = "select `desc`,rate,hours,total,tech from recommendlabor where shopid = '$shopid' and recid = $id";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows;
        if ($numrows > 0) {
            $result = $query->get_result();
            echo "<table class='table table-condensed table-header-bg table-striped'>";
            echo "<thead>";
            echo "<tr>";
            echo "<td style='width:70%'>Labor</td><td style='width:10%'>Hours</td><td style='width:10%'>Tech</td><td style='width:10%;text-align:right'>Total</td>";
            echo "</tr>";
            echo "</thead>";
            echo "<tbody>";
            $result = $conn->query($stmt);
            while ($row = $result->fetch_array()) {
                echo "<tr>";
                echo "<td>" . strtoupper($row['desc']) . "</td><td>" . strtoupper($row['hours']) . "</td><td>" . strtoupper($row['tech']) . "</td><td style='text-align:right'>" . asDollars($row['total'], 2) . "</td>";
                echo "</tr>";
            }
            echo "</tbody>";
            echo "</table>";
        }
    }

    $stmt = "select partnumber,partdesc,quantity,linettlprice from recommendparts where shopid = '$shopid' and recid = $id";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows;
        if ($numrows > 0) {
            $result = $query->get_result();
            echo "<br><table class='table table-condensed table-header-bg table-striped'>";
            echo "<thead>";
            echo "<tr>";
            echo "<td style='width:70%'>Description</td><td style='width:10%'>Part #</td><td style='width:10%'>Qty</td><td style='width:10%;text-align:right'>Total</td>";
            echo "</tr>";
            echo "</thead>";
            echo "<tbody>";
            $result = $conn->query($stmt);
            while ($row = $result->fetch_array()) {
                echo "<tr>";
                echo "<td>" . strtoupper($row['partdesc']) . "</td><td>" . strtoupper($row['partnumber']) . "</td><td>" . strtoupper($row['quantity']) . "</td><td style='text-align:right'>" . asDollars($row['linettlprice'], 2) . "</td>";
                echo "</tr>";
            }
            echo "</tbody>";
            echo "</table>";
        }
    }

}


if ($t == "restore_existing") {

    $roid = $_REQUEST['roid'];
    $shopid = $oshopid = $_REQUEST['shopid'];
    $recs = (isset($_REQUEST['recs']) ? json_decode($_REQUEST['recs'], true) : '');
    $rectechs = (isset($_REQUEST['rectechs']) ? json_decode($_REQUEST['rectechs'], true) : '');
    $recrepairsjson = (isset($_REQUEST['recrepairsjson']) ? json_decode($_REQUEST['recrepairsjson'], true) : array());


    $recsarr = $labortechs = array();

    if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

    if (!empty($recs)) {
        $recsarr = $recs;
    } else {
        $recsarr[] = $_REQUEST['id'];
        if (empty($recrepairsjson)){
            $recrepairsjson[] = array('id' => $_REQUEST['id']);
        }
    }

    if (!empty($rectechs)) {
        foreach ($rectechs as $lt) {
            $labortechs[$lt['id']] = array('tech' => $lt['last'] . ', ' . $lt['first'], 'rate' => $lt['techrate']);
        }
    }

    $stmt = "select showpartfeeaspart from settings where shopid = ?";
    if ($query = $conn->prepare($stmt)){
     $query->bind_param("s",$shopid);
     $query->execute();
     $query->bind_result($showpartfeeaspart);
     $query->fetch();
     $query->close();
    }

    if (!empty($recrepairsjson)) {
        foreach ($recrepairsjson as $recrepair) {
            $id = $recrepair['id'];

            // get the details of the recommend and restore them to the current ro
            $stmt = "SELECT r.id, r.shopid,	r.roid,	r.comid, `desc`, totalrec,originalcomplaintid, originalroid,	r.technotes, c.issue, r.ts FROM	recommend r LEFT JOIN complaints c ON c.shopid = r.shopid AND c.roid = r.roid AND c.complaintid = r.comid WHERE r.shopid = ? AND r.id = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si", $shopid, $id);
                if ($query->execute()) {
                    $query->store_result();
                    $num_roid_rows = $query->num_rows;
                    if ($num_roid_rows > 0) {
                        $query->bind_result($id, $dbshopid, $dbroid, $comid, $desc, $totalrec, $originalcomplaintid, $originalroid, $technotes,$category, $ts);
                        $query->fetch();
                    } else {
                        $complaintid = 0;
                    }
                } else {
                    echo "Execution Error|Executing Query: Select from recommend";
                    exit;
                }
                $query->close();
            } else {
                echo "Connection Error|Getting Recommend Details";
                exit;
            }

            if (isset($recrepair['issue']) && !empty($recrepair['issue'])){
                $desc = $recrepair['issue'];
            }
            if (isset($recrepair['category']) && !empty($recrepair['category'])){
                $category = $recrepair['category'];
            }

            $desc = str_replace("Customer Declined: ", "RESTORED REC. REPAIR: ", $desc);

            // get the next complaintid
            $stmt = "select coalesce(complaintid,0) compid from complaints where shopid = ? order by complaintid desc,roid limit 1";
            if ($query = $conn->prepare($stmt)) {

                $query->bind_param("s", $shopid);
                $query->execute();
                $query->store_result();
                $num_roid_rows = $query->num_rows;
                if ($num_roid_rows > 0) {
                    $query->bind_result($complaintid);
                    $query->fetch();
                } else {
                    $complaintid = 999;
                }
                $query->close();

            } else {
                echo "RO Number failed: (" . $conn->errno . ") " . $conn->error;
            }

            $complaintid = $complaintid + 1;

            // add a complaint using the recommend
            $stmt = "insert into complaints (shopid,roid,complaintid,complaint,issue,techreport) values ('$shopid',$roid,$complaintid,?,?,?)";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sss", $desc,$category, $technotes);
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();

                    //notification
                    $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='14'";
                    $query = $conn->prepare($stmt);
                    $query->execute();
                    $query->store_result();
                    $numrows = $query->num_rows();
                    if ($numrows > 0) {
                        $query->bind_result($textcontent, $emailcontent, $popupcontent);
                        $query->fetch();
                        $emailcontent = str_replace("*|RO|*", $roid, str_replace("*|SN|*", $sn, $emailcontent));
                        $popupcontent = str_replace("*|RO|*", $roid, str_replace("*|SN|*", $sn, $popupcontent));
                        $textcontent = str_replace("*|RO|*", $roid, $textcontent);
                        $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'14',?,?,?)";
                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param('isss', $shopid, $popupcontent, $textcontent, $emailcontent);
                            $query->execute();
                            $conn->commit();
                            $query->close();
                        }
                    }

                } else {
                    echo "Execution Error|Inserting into Part from RecommendParts";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Parts from RecommendParts";
                exit;
            }

            $stmt = "select upper(updateinvonadd) from company where shopid = ?";
            if ($query = $conn->prepare($stmt)) {

                $query->bind_param("s", $shopid);
                $query->execute();
                $query->bind_result($updateinvonadd);
                $query->fetch();
                $query->close();

            } else {
                echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
            }

            // 2. get the parts from the recommendparts table and restore them to the parts table
            $stmt = "select allocated,shopid,partnumber,partdesc,partprice,quantity,roid,supplier,cost,partinvoicenumber,partcode,linettlprice,linettlcost,`date`,partcategory,complaintid,discount,net,tax,bin,overridematrix from recommendparts where shopid = '$shopid' and recid = $id";
            $result = $conn->query($stmt);
            while ($row = $result->fetch_array()) {
                $stmt = "insert into parts (shopid,partnumber,partdesc,partprice,quantity,roid,supplier"
                    . ",cost,partinvoicenumber,partcode,linettlprice,linettlcost,`date`,partcategory,"
                    . "complaintid,discount,net,tax,allocated,bin,overridematrix) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("sssddisdssddssissssss", $row["shopid"],$row["partnumber"],$row["partdesc"],$row["partprice"],$row["quantity"],$roid,$row["supplier"],$row["cost"],$row["partinvoicenumber"],$row["partcode"],$row["linettlprice"],$row["linettlcost"],$today,$row["partcategory"],$complaintid,$row["discount"],$row["net"],$row["tax"],$row['allocated'],$row['bin'],$row['overridematrix']);
                    if ($query->execute()) {
                        $partid = $conn->insert_id;
                        $conn->commit();
                        $query->close();
                    } else {
                        echo "Execution Error|Inserting into Part from RecommendParts";
                        exit;
                    }
                } else {
                    echo "Connection Error|Inserting into Parts from RecommendParts";
                    exit;
                }

                if (strtolower($row['allocated']) != 'non' && $updateinvonadd == 'YES') {
                    // update inventory
                    $uquantity = $row["quantity"];
                    $upartnum = $row["partnumber"];
                    $ustmt = "update partsinventory set onhand = onhand - $uquantity, netonhand = netonhand - $uquantity where shopid = ? and partnumber = ?";
                    if ($uquery = $conn->prepare($ustmt)) {
                        $uquery->bind_param("ss", $oshopid, $upartnum);
                        if ($uquery->execute()) {
                            $conn->commit();
                            $uquery->close();
                        } else {
                            echo $conn->errno;
                        }

                    } else {
                        echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }
                }

                if($showpartfeeaspart == 'no')
                {
                    $stmt = "select additionalfeename,addfeetaxable,addfeeamt,addfeepercentordollar,qtyflag from partsinventoryfees where shopid = ? and partnumber = ?";
                    if ($query = $conn->prepare($stmt)){

                        $query->bind_param("ss",$oshopid,$row["partnumber"]);
                        $query->execute();
                        $r = $query->get_result();
                        while ($rs = $r->fetch_assoc()){

                            $qtyflag = $rs['qtyflag'];
                            $feedesc = strtoupper($rs['additionalfeename']);
                            $feetype = $rs['addfeepercentordollar'];

                            if (strtolower($feetype) == "dollar"){
                                if ($qtyflag == 'yes') {
                                    $feeamt = $rs['addfeeamt'] ;
                                    $linettlprice = ($rs['addfeeamt'] * $row["quantity"]) ;
                                }else{
                                    $feeamt = $rs['addfeeamt'] ;
                                    $linettlprice = $rs['addfeeamt'] ;
                                }

                            }elseif (strtolower($feetype) == "percent"){
                                if ($qtyflag == 'yes') {
                                    $feeamt = sbpround(($row["partprice"] * ($rs['addfeeamt'] / 100)),2);
                                    $linettlprice = $feeamt * $row["quantity"];
                                }else{
                                    $feeamt = sbpround(($row["partprice"] * ($rs['addfeeamt'] / 100)),2);
                                    $linettlprice = $feeamt ;

                                }
                            }
                            $feetax = $rs['addfeetaxable'];
                                
                            $stmt = "insert into rofees (`shopid`,`roid`,`itemid`,`feename`,`feeamount`,`taxable`) values (?,?,?,?,?,?)";

                            if ($query = $conn->prepare($stmt)){
                                $query->bind_param("sddsds",$oshopid,$roid,$partid,$feedesc,$linettlprice,$feetax);
                                $query->execute();
                                $conn->commit();
                                $query->close();
                            }

                        }

                    }
                }

            }

            // 3. Now get the labor
            $stmt = "select id,recid,shopid,roid,comid,`desc`,rate,ratelabel,hours,total,tech from recommendlabor where shopid = '$shopid' and recid = $id";
            $result = $conn->query($stmt);
            while ($row = $result->fetch_array()) {
                if (isset($labortechs[$row['id']])) {
                    $row['tech'] = $labortechs[$row['id']]['tech'];
                }
                $stmt = "insert into labor (shopid,roid,hourlyrate,ratelabel,laborhours,labor,tech,linetotal,complaintid) values (?,?,?,?,?,?,?,?,?)";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("sidsdssdi",$shopid,$roid,$row['rate'],$row['ratelabel'],$row['hours'],$row['desc'],$row['tech'],$row['total'],$complaintid);
                    if ($query->execute()) {
                        $conn->commit();
                        $query->close();
                    } else {
                        echo "Execution Error|Inserting into Labor from RecommendLabor";
                        exit;
                    }
                } else {
                    echo "Connection Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            }

            $newsubletid = 1;
            $stmt = "select subletid from sublet where shopid = ? order by subletid desc limit 1";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("s",$shopid);
                $query->execute();
                $query->bind_result($subletid);
                if ($query->fetch()) {
                    if (is_numeric($subletid)) {
                        $newsubletid = $subletid + 1;
                    }
                }

                $query->close();


            } else {
                echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            $stmt = "select shopid,roid,comid,subletdesc,subletcost,subletprice,supplier from recommendsublet where shopid = '$shopid' and recid = $id";
            if ($result = $conn->query($stmt)) {
                while ($row = $result->fetch_array()) {
                    $stmt = "insert into sublet (shopid,SubLetID,ROID,SubletDesc,SubletPrice,SubletCost,SubletSupplier,complaintid) values (?,?,?,?,?,?,?,?)";
                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("siisddsi",$shopid,$newsubletid,$roid,$row['subletdesc'],$row['subletprice'],$row['subletcost'],$row['supplier'],$complaintid);
                        if ($query->execute()) {
                            $conn->commit();
                            $query->close();
                            $newsubletid += 1;
                        } else {
                            echo $conn->error;
                        }
                    } else {
                        echo $conn->error;
                    }
                }
            }


            // 4.  now delete from the recommendparts and recommendlabor
            $stmt = "delete from recommend where shopid = ? and id = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si",$shopid,$id);
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();
                } else {
                    echo "Execution Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Labor from RecommendLabor";
                exit;
            }


            $stmt = "delete from recommendlabor where shopid = ? and recid = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si",$shopid,$id);
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();
                } else {
                    echo "Execution Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Labor from RecommendLabor";
                exit;
            }


            $stmt = "delete from recommendparts where shopid = ? and recid = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si",$shopid,$id);
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();
                } else {
                    echo "Execution Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Labor from RecommendLabor";
                exit;
            }

            $stmt = "delete from recommendsublet where shopid = ? and recid = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si",$shopid,$id);
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();
                } else {
                    echo "Execution Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Labor from RecommendLabor";
                exit;
            }

        }

        echo "success";

    }

}

if ($t == "undecline") {

    $roid = $_REQUEST['roid'];
    $shopid = $oshopid = $_REQUEST['shopid'];
    $recid = $_REQUEST['recid'];
    $comid = $_REQUEST['comid'];
    if (isset($_REQUEST['newstat'])) {
        $newstat = ucwords($_REQUEST['newstat']);
    } else {
        $newstat = "Approved";
    }

    if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

    // restore to approved the vehicle issue
    $stmt = "update complaints set acceptdecline = ? where shopid = ? and roid = ? and complaintid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ssii",$newstat,$shopid,$roid,$comid);
        $query->execute();
        $conn->commit();
        $query->close();

        //notification
        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='8'";
        $query = $conn->prepare($stmt);
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows();
        if ($numrows > 0) {
            $query->bind_result($textcontent, $emailcontent, $popupcontent);
            $query->fetch();
            $emailcontent = str_replace("*|STATUS|*", $newstat, str_replace("*|RO|*", $roid, str_replace("*|SN|*", $sn, $emailcontent)));
            $popupcontent = str_replace("*|STATUS|*", $newstat, str_replace("*|RO|*", $roid, str_replace("*|SN|*", $sn, $popupcontent)));
            $textcontent = str_replace("*|STATUS|*", $newstat, str_replace("*|RO|*", $roid, $textcontent));
            $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'8',?,?,?)";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param('isss', $shopid, $popupcontent, $textcontent, $emailcontent);
                $query->execute();
                $conn->commit();
                $query->close();
            }
        }

    } else {
        echo "Connection Error|Updating Complaints";
        exit;
    }

    $stmt = "select showpartfeeaspart from settings where shopid = ?";
    if ($query = $conn->prepare($stmt)){
     $query->bind_param("s",$shopid);
     $query->execute();
     $query->bind_result($showpartfeeaspart);
     $query->fetch();
     $query->close();
    }

    $stmt = "select upper(updateinvonadd) from company where shopid = ?";
    if ($query = $conn->prepare($stmt)) {

        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($updateinvonadd);
        $query->fetch();
        $query->close();

    } else {
        echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
    }

    // 2. get the parts from the recommendparts table and restore them to the parts table
    $stmt = "select shopid,partnumber,partdesc,partprice,quantity,roid,supplier,cost,partinvoicenumber,partcode,linettlprice,linettlcost,`date`,partcategory,complaintid,discount,net,tax,allocated,bin,overridematrix from recommendparts where shopid = '$shopid' and recid = $recid";

    if ($result = $conn->query($stmt)) {
        while ($row = $result->fetch_array()) {
            $stmt = "insert into parts (shopid,partnumber,partdesc,partprice,quantity,roid,supplier"
                    . ",cost,partinvoicenumber,partcode,linettlprice,linettlcost,`date`,partcategory,"
                    . "complaintid,discount,net,tax,allocated,bin,overridematrix) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("sssddisdssddssissssss", $row["shopid"],$row["partnumber"],$row["partdesc"],$row["partprice"],$row["quantity"],$roid,$row["supplier"],$row["cost"],$row["partinvoicenumber"],$row["partcode"],$row["linettlprice"],$row["linettlcost"],$today,$row["partcategory"],$row['complaintid'],$row["discount"],$row["net"],$row["tax"],$row['allocated'],$row['bin'],$row['overridematrix']);
                if ($query->execute()) {
                    $partid = $conn->insert_id;
                    $conn->commit();
                    $query->close();
                } else {
                    echo "Execution Error|Inserting into Part from RecommendParts";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Parts from RecommendParts";
                exit;
            }

            if (strtolower($row['allocated']) != 'non' && $updateinvonadd == 'YES') {
                // update inventory
                $uquantity = $row["quantity"];
                $upartnum = $row["partnumber"];
                $ustmt = "update partsinventory set onhand = onhand - $uquantity, netonhand = netonhand - $uquantity where shopid = ? and partnumber = ?";
                if ($uquery = $conn->prepare($ustmt)) {
                    $uquery->bind_param("ss", $oshopid, $upartnum);
                    if ($uquery->execute()) {
                        $conn->commit();
                        $uquery->close();
                    } else {
                        echo $conn->errno;
                    }

                } else {
                    echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }
            }

            if($showpartfeeaspart == 'no')
            {
                $stmt = "select additionalfeename,addfeetaxable,addfeeamt,addfeepercentordollar,qtyflag from partsinventoryfees where shopid = ? and partnumber = ?";
                if ($query = $conn->prepare($stmt)){

                    $query->bind_param("ss",$oshopid,$row["partnumber"]);
                    $query->execute();
                    $r = $query->get_result();
                    while ($rs = $r->fetch_assoc()){

                        $qtyflag = $rs['qtyflag'];
                        $feedesc = strtoupper($rs['additionalfeename']);
                        $feetype = $rs['addfeepercentordollar'];

                        if (strtolower($feetype) == "dollar"){
                            if ($qtyflag == 'yes') {
                                $feeamt = $rs['addfeeamt'] ;
                                $linettlprice = ($rs['addfeeamt'] * $row["quantity"]) ;
                            }else{
                                $feeamt = $rs['addfeeamt'] ;
                                $linettlprice = $rs['addfeeamt'] ;
                            }

                        }elseif (strtolower($feetype) == "percent"){
                            if ($qtyflag == 'yes') {
                                $feeamt = sbpround(($row["partprice"] * ($rs['addfeeamt'] / 100)),2);
                                $linettlprice = $feeamt * $row["quantity"];
                            }else{
                                $feeamt = sbpround(($row["partprice"] * ($rs['addfeeamt'] / 100)),2);
                                $linettlprice = $feeamt ;

                            }
                        }
                        $feetax = $rs['addfeetaxable'];
                            
                        $stmt = "insert into rofees (`shopid`,`roid`,`itemid`,`feename`,`feeamount`,`taxable`) values (?,?,?,?,?,?)";

                        if ($query = $conn->prepare($stmt)){
                            $query->bind_param("sddsds",$oshopid,$roid,$partid,$feedesc,$linettlprice,$feetax);
                            $query->execute();
                            $conn->commit();
                            $query->close();
                        }

                    }

                }
            }

        }
    }

    // 3. Now get the labor
    $stmt = "select id,recid,shopid,roid,comid,`desc`,rate,ratelabel,hours,total,tech from recommendlabor where shopid = '$shopid' and recid = $recid";
    if ($result = $conn->query($stmt)) {
        while ($row = $result->fetch_array()) {
            $stmt = "insert into labor (shopid,roid,hourlyrate,ratelabel,laborhours,labor,tech,linetotal,complaintid) values (?,?,?,?,?,?,?,?,?)";

            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sidsdssdi",$shopid,$roid,$row['rate'],$row['ratelabel'],$row['hours'],$row['desc'],$row['tech'],$row['total'],$row['comid']);
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();
                } else {
                    echo "Execution Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Labor from RecommendLabor";
                exit;
            }
        }
    }

    // 3.5  get the sublet
    $newsubletid = 1;
    $stmt = "select subletid from sublet where shopid = '$shopid' order by subletid desc limit 1";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($subletid);
        if ($query->fetch()) {
            if (is_numeric($subletid)) {
                $newsubletid = $subletid + 1;
            }
        }

        $query->close();


    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    $stmt = "select shopid,roid,comid,subletdesc,subletcost,subletprice,supplier from recommendsublet where shopid = '$shopid' and recid = $recid";
    if ($result = $conn->query($stmt)) {
        while ($row = $result->fetch_array()) {
            $stmt = "insert into sublet (shopid,SubLetID,ROID,SubletDesc,SubletPrice,SubletCost,SubletSupplier,complaintid) values (?,?,?,?,?,?,?,?)";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("siisddsi",$shopid,$newsubletid,$roid,$row['subletdesc'],$row['subletprice'],$row['subletcost'],$row['supplier'],$row['comid']);
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();
                    $newsubletid += 1;
                } else {
                    echo $conn->error;
                }
            } else {
                echo $conn->error;
            }
        }
    }

    // 4.  now delete from the recommendparts and recommendlabor
    $stmt = "delete from recommend where shopid = '$shopid' and id = $recid";
    if ($query = $conn->prepare($stmt)) {
        if ($query->execute()) {
            $conn->commit();
            $query->close();
        } else {
            echo "Execution Error|Inserting into Labor from RecommendLabor";
            exit;
        }
    } else {
        echo "Connection Error|Inserting into Labor from RecommendLabor";
        exit;
    }


    $stmt = "delete from recommendlabor where shopid = '$shopid' and recid = $recid";
    if ($query = $conn->prepare($stmt)) {
        if ($query->execute()) {
            $conn->commit();
            $query->close();
        } else {
            echo "Execution Error|Inserting into Labor from RecommendLabor";
            exit;
        }
    } else {
        echo "Connection Error|Inserting into Labor from RecommendLabor";
        exit;
    }


    $stmt = "delete from recommendparts where shopid = '$shopid' and recid = $recid";
    if ($query = $conn->prepare($stmt)) {
        if ($query->execute()) {
            $conn->commit();
            $query->close();
        } else {
            echo "Execution Error|Inserting into Labor from RecommendLabor";
            exit;
        }
    } else {
        echo "Connection Error|Inserting into Labor from RecommendLabor";
        exit;
    }

    $stmt = "delete from recommendsublet where shopid = '$shopid' and recid = $recid";
    if ($query = $conn->prepare($stmt)) {
        if ($query->execute()) {
            $conn->commit();
            $query->close();
        } else {
            echo "Execution Error|Inserting into Labor from RecommendLabor";
            exit;
        }
    } else {
        echo "Connection Error|Inserting into Labor from RecommendLabor";
        exit;
    }

    echo "success";


}

if ($t == "addnew") {

    $roid = $_REQUEST['roid'];
    $desc = $_REQUEST['descr'];
    $cost = $_REQUEST['cost'];
    $shopid = $_COOKIE['shopid'];
    $stmt = "insert into recommend (roid,shopid,`desc`,totalrec) values (?,?,?,?)";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("issd", $roid, $shopid, $desc, $cost);
        if ($query->execute()) {
            $conn->commit();
            $query->close();
        } else {
            echo "Execution Error|Inserting into Recommended 1" . $query->error;
            exit;
        }
    } else {
        echo "Connection Error|Inserting into Recommended 2";
        exit;
    }

    echo "success";

}

if ($t == "delete") {

    $roid = $_REQUEST['roid'];
    $shopid = $_REQUEST['shopid'];
    $recid = $_REQUEST['id'];

    // 4.  now delete from the recommendparts and recommendlabor
    $stmt = "delete from recommend where shopid = '$shopid' and id = $recid";
    if ($query = $conn->prepare($stmt)) {
        //$query->bind_param("si",$shopid,$roid,$comid);
        if ($query->execute()) {
            $conn->commit();
            $query->close();
        } else {
            echo "Execution Error|Inserting into Labor from RecommendLabor";
            exit;
        }
    } else {
        echo "Connection Error|Inserting into Labor from RecommendLabor";
        exit;
    }


    $stmt = "delete from recommendlabor where shopid = '$shopid' and recid = $recid";
    if ($query = $conn->prepare($stmt)) {
        //$query->bind_param("si",$shopid,$roid,$comid);
        if ($query->execute()) {
            $conn->commit();
            $query->close();
        } else {
            echo "Execution Error|Inserting into Labor from RecommendLabor";
            exit;
        }
    } else {
        echo "Connection Error|Inserting into Labor from RecommendLabor";
        exit;
    }


    $stmt = "delete from recommendparts where shopid = '$shopid' and recid = $recid";
    if ($query = $conn->prepare($stmt)) {
        //$query->bind_param("si",$shopid,$roid,$comid);
        if ($query->execute()) {
            $conn->commit();
            $query->close();
        } else {
            echo "Execution Error|Inserting into Labor from RecommendLabor";
            exit;
        }
    } else {
        echo "Connection Error|Inserting into Labor from RecommendLabor";
        exit;
    }

    $stmt = "delete from recommendsublet where shopid = '$shopid' and recid = $recid";
    if ($query = $conn->prepare($stmt)) {
        //$query->bind_param("si",$shopid,$roid,$comid);
        if ($query->execute()) {
            $conn->commit();
            $query->close();
        } else {
            echo "Execution Error|Inserting into Labor from RecommendLabor";
            exit;
        }
    } else {
        echo "Connection Error|Inserting into Labor from RecommendLabor";
        exit;
    }
    echo "success";


}

if ($t == "get_comparison") {

    $roid = $_REQUEST['roid'];
    $shopid = $oshopid =$_REQUEST['shopid'];
    $id = $_REQUEST['id'];

    $new_total = 0;
    $old_total = 0;
    $laborArr = array();
    $partsArr = array();
    $subletArr = array();
    $prices_changed = false;

    if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

    $stmt = "select id,partnumber,partdesc,quantity,PartPrice,linettlprice, Supplier,allocated from recommendparts where shopid = '$shopid' and recid = $id";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($recpid, $rec_part, $rec_desc, $rec_quantity, $rec_price, $rec_total, $rec_supplier, $allocated);
        $query->store_result();
        while ($query->fetch()) {
            $temparr = array(
                'rec_id' => $recpid,
                'part' => $rec_part,
                'desc' => $rec_desc,
                'qty' => $rec_quantity,
                'price' => $rec_price,
                'total' => $rec_total,
                'same' => true
            );
            $partsArr['recommend'][] = $temparr;
            if(strtoupper($allocated) == "NON"){
                $preg = "partsregistry";
                $stmt = sprintf("SELECT table_name FROM information_schema.tables WHERE table_schema = 'shopboss' AND table_name = 'partsregistry-%s' LIMIT 1", $shopid);
                if ($tquery = $conn->prepare($stmt)) {
                    $tquery->execute();
                    $results = $tquery->get_result();
                    $tquery->store_result();
                    if ($results->num_rows > 0) {
                        $preg = "`partsregistry-" . $shopid . "`";
                    }
                    $tquery->close();
                }
                $pstmt = "SELECT partid, PartNumber, PartDesc, PartPrice FROM $preg WHERE shopid = ? AND PartNumber = ? AND PartDesc = ? AND PartSupplier = ? ORDER BY ts DESC LIMIT 1";
            } else {
                $pstmt = "SELECT partid, PartNumber, PartDesc, PartPrice FROM partsinventory WHERE shopid = ? AND PartNumber = ? AND PartDesc = ? AND PartSupplier = ? ORDER BY ts DESC LIMIT 1";
            }
            if ($pquery = $conn->prepare($pstmt)) {
                $pquery->bind_param("ssss", $oshopid, $rec_part, $rec_desc, $rec_supplier);
                $pquery->execute();
                $pquery->bind_result($partid, $p_part, $p_desc, $p_price);
                $pquery->store_result();
                if ($pquery->num_rows > 0) {
                    $pquery->fetch();
                    $partsArr['new'][] = array(
                        'rec_id' => $recpid,
                        'id' => $partid,
                        'part' => $p_part,
                        'desc' => $p_desc,
                        'price' => $p_price,
                        'qty' => $rec_quantity,
                        'total' => $rec_quantity * $p_price,
                        'same' => $p_price == $rec_price
                    );
                    if ($p_price != $rec_price) {
                        $prices_changed = true;
                    }
                } else {
                    $partsArr['new'][] = $temparr;
                }
                $pquery->close();
            } else {
                echo "parts prepare failed " . $conn->error;
            }

        }
        $query->close();
    } else {
        echo "Rec Parts Prepare failed " . $conn->error;
    }


    $stmt = "select id,UCASE(`desc`),rate,hours,total,tech from recommendlabor where shopid = '$shopid' and recid = $id";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $query->bind_result($reclid, $rec_desc, $rec_rate, $rec_hours, $rec_total, $rec_tech);
        while ($query->fetch()) {
            $temparr = array(
                'rec_id' => $reclid,
                'desc' => $rec_desc,
                'rate' => $rec_rate,
                'hours' => $rec_hours,
                'total' => $rec_total,
                'tech' => $rec_tech,
                'same' => true
            );
            $laborArr['recommend'][] = $temparr;

            $lstmt = "SELECT LaborID, labor, HourlyRate, LaborHours, LineTotal FROM labor where shopid = ? AND labor = ? AND tech = ? order by ts DESC LIMIT 1";

            $labor_rate = "NA";
            $labor_hours = "NA";
            $labor_total = 0;
            if ($lquery = $conn->prepare($lstmt)) {
                $lquery->bind_param("sss", $shopid, $rec_desc, $tec_tech);
                $lquery->execute();
                $lquery->bind_result($labor_id, $labor_desc, $labor_rate, $labor_hours, $labor_total);
                if ($lquery->num_rows > 0) {
                    $lquery->fetch();

                    $laborArr['new'][] = array(
                        'rec_id' => $reclid,
                        'id' => $labor_id,
                        'desc' => $labor_desc,
                        'rate' => $labor_rate,
                        'hours' => $labor_hours,
                        'total' => $labor_total,
                        'tech' => $rec_tech,
                        'same' => $labor_total == $rec_total
                    );
                    if ($labor_total != $rec_total) {
                        $prices_changed = true;
                    }
                } else {
                    $laborArr['new'][] = $temparr;
                }
                $lquery->close();
            } else {
                echo "Prepare Failed " . $conn->error;
            }
        }
    } else {
        echo "Prepare Failed " . $conn->error;
    }

    $stmt = "select id,subletdesc, subletcost, subletprice from recommendsublet where shopid = '$shopid' and recid = $id";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($recsid, $rec_sublet, $rec_cost, $rec_price);
        $query->store_result();
        while ($query->fetch()) {
            $temparr = array(
                'rec_id' => $recsid,
                'desc' => $rec_sublet,
                'cost' => $rec_cost,
                'price' => $rec_price,
                'same' => true
            );
            $subletArr['recommend'][] = $temparr;

            $pstmt = "SELECT SubLetID, SubletDesc, SubletCost, SubletPrice FROM sublet WHERE shopid = ? AND SubletDesc = ? ORDER BY ts DESC LIMIT 1";
            if ($pquery = $conn->prepare($pstmt)) {
                $pquery->bind_param("ss", $shopid, $rec_sublet);
                $pquery->execute();
                $pquery->bind_result($sublet_id, $s_sublet, $s_cost, $s_price);
                if ($pquery->num_rows > 0) {
                    $pquery->fetch();
                    $subletArr['new'][] = array(
                        'rec_id' => $recsid,
                        'id' => $sublet_id,
                        'desc' => $s_sublet,
                        'cost' => $s_cost,
                        'price' => $s_price,
                        'same' => $s_price == $rec_price
                    );
                } else {
                    $subletArr['new'][] = $temparr;
                }
                $pquery->close();
            } else {
                echo "sublt prepare failed " . $conn->error;
            }

        }
        $query->close();
    } else {
        echo "Rec sublt Prepare failed " . $conn->error;
    }

    ?>
    <table class="sbdatatable w-100">
        <?php
        if (!empty($laborArr)) {
            ?>
            <thead>
            <tr class="table_header">
                <th colspan="5">Labor</th>
            </tr>
            <tr>
                <th>Description</th>
                <th colspan="2">Previous</th>
                <th colspan="2">Current</th>
            </tr>
            </thead>
            <?php
            for ($i = 0; $i < sizeof($laborArr['recommend']); $i++) {

                $reclbr = $laborArr['recommend'][$i];
                $curlbr = $laborArr['new'][$i];
                ?>
                <tr <?= ($reclbr['total'] != $curlbr['total'])? "style='background-color:#ffc4c4'":"" ?>>
                    <td><?= $reclbr['desc'] ?></td>
                    <td><?= $reclbr['hours'] ?></td>
                    <td><?= asDollars($reclbr['total']) ?></td>
                    <td><?= $curlbr['hours'] ?></td>
                    <td><?= asDollars($curlbr['total']) ?></td>
                </tr>
                <?php
                $old_total += $reclbr['total'];
                $new_total += $curlbr['total'];
            }
        }
        if (!empty($partsArr)) {
            ?>
            <thead>
            <tr>
                <th colspan="5">PARTS</th>
            </tr>
            <tr>
                <th>Part Number</th>
                <th colspan="2">Previous</th>
                <th colspan="2">Current</th>
            </tr>
            </thead>
            <?php
            for ($i = 0; $i < sizeof($partsArr['recommend']); $i++) {

                $recprts = $partsArr['recommend'][$i];
                $curprts = $partsArr['new'][$i];
                ?>
                <tr <?= ($recprts['total'] != $curprts['total'])? "style='background-color:#ffc4c4'":"" ?>>
                    <td><?= $recprts['part'] ?></td>
                    <td><?= $recprts['qty'] . "@" . asDollars($recprts['price']) ?></td>
                    <td><?= asDollars($recprts['total']) ?></td>
                    <td><?= $curprts['qty'] . "@" . asDollars($curprts['price']) ?></td>
                    <td><?= asDollars($curprts['total']) ?></td>
                </tr>
                <?php
                $old_total += $recprts['total'];
                $new_total += $curprts['total'];
            }
        }
        if (!empty($subletArr)) {
            ?>
            <thead>
            <tr>
                <th colspan="5">SUBLET</th>
            </tr>
            <tr>
                <th>Sublet Desc</th>
                <th colspan="2">Previous</th>
                <th colspan="2">Current</th>
            </tr>

            </thead>
            <?php
            for ($i = 0; $i < sizeof($subletArr['recommend']); $i++) {

                $recsub = $subletArr['recommend'][$i];
                $cursub = $subletArr['new'][$i];
                ?>
                <tr <?= ($recsub['price'] != $cursub['price'])? "style='background-color:#ffc4c4'":"" ?>>
                    <td><?= $recsub['desc'] ?></td>
                    <td colspan="2"><?= asDollars($recsub['price']) ?></td>
                    <td colspan="2"><?= asDollars($cursub['price']) ?></td>
                </tr>
                <?php
                $old_total += $recsub['price'];
                $new_total += $cursub['price'];
            }
        }

        $allItems = array("labor" => $laborArr, "parts" => $partsArr, "sublet" => $subletArr);
        ?>
        <tr>
            <th>TOTAL</th>
            <th colspan="2" class="text-right"><?= asDollars($old_total) ?></th>
            <th colspan="2" class="text-right"><?= asDollars($new_total) ?></th>
        </tr>
        <tr>
            <td colspan="5">&nbsp;&nbsp;&nbsp;&nbsp;</td>
        </tr>
    </table>
    <input type="hidden" value='<?= json_encode($allItems) ?>' name="rec_data" id="rec_data"/>
    <div class="col d-flex justify-content-center align-items-center pt-2">
        <button class="btn btn-primary me-2"
                onclick="restore_recommended_repair('<?= $shopid ?>','<?= $roid ?>','<?= $id ?>','old')">
            Restore Repair (Old Prices)
        </button>
        <button class="btn btn-secondary"
                onclick="restore_recommended_repair('<?= $shopid ?>','<?= $roid ?>','<?= $id ?>','new')">
            Restore Repair (Current Prices)
        </button>
    </div>
    <script>
        $(document).ready(function () {
            <?php if ($prices_changed) { ?>
            $("#recommed_compare").modal("show");
            <?php } else { ?>
            restore_recommended_repair('<?= $shopid ?>', <?= $roid ?>, <?= $id ?>, 'old');
            <?php } ?>
        });

        function restore_recommended_repair(shopid, roid, id, ver = "old") {
            var vertext = "";
            if (ver == "new") {
                vertext = " (Latest Prices)";
            }
            sbconfirm("Restore","Click OK to add this recommendation" + vertext + " to the current RO",
                function () {
                    if (ver == "old") {
                        $('.restoreok').attr('disabled', 'disabled').html("Please wait...")
                        // add the maint list as complaint to complaints for the ro
                        ds = "t=restore_existing&roid=" + roid + "&shopid=" + shopid + "&id=" + id
                        //console.log(ds)
                        $.ajax({
                            data: ds,
                            type: "get",
                            url: "recrepairsedit.php",
                            success: function (r) {
                                //     console.log(r)
                                if (r == "success") {
                                    parent.$("#restorelist").val(parent.$("#restorelist").val() + "|" + id)
                                    parent.eModal.close()
                                    parent.location.href = 'ro.php?restore=y&roid=' + roid
                                } else {
                                    sbalert(r)
                                    $('.restoreok').attr('disabled', false).html("OK")
                                }
                            }
                        });
                    } else {

                        $('.restoreok').attr('disabled', 'disabled').html("Please wait...")
                        // add the maint list as complaint to complaints for the ro
                        ds = "t=restore_existing_new&roid=" + roid + "&shopid=" + shopid + "&id=" + id
                        //console.log(ds)
                        $.ajax({
                            data: ds,
                            type: "get",
                            url: "recrepairsedit.php",
                            success: function (r) {
                                //     console.log(r)
                                if (r == "success") {
                                    parent.$("#restorelist").val(parent.$("#restorelist").val() + "|" + id)
                                    parent.eModal.close()
                                    parent.location.href = 'ro.php?restore=y&roid=' + roid
                                } else {
                                    sbalert(r)
                                    $('.restoreok').attr('disabled', false).html("OK")
                                }
                            }
                        });
                    }
                }
            );
        }
    </script>
    <?php
    return;
}

if ($t == "restore_existing_new") {

    $roid = $_REQUEST['roid'];
    $shopid = $oshopid = $_REQUEST['shopid'];
    $recs = (isset($_REQUEST['recs']) ? json_decode($_REQUEST['recs'], true) : '');
    //$rectechs = (isset($_REQUEST['rectechs']) ? json_decode($_REQUEST['rectechs'], true) : '');
    $recsarr = $labortechs = array();
    $recrepairsjson = (isset($_REQUEST['recrepairsjson']) ? json_decode($_REQUEST['recrepairsjson'], true) : array());
    $rectechs = isset($recrepairsjson['rectechs']) ? $recrepairsjson['rectechs'] : '';

    if (!empty($recs)) {
        $recsarr = $recs;
    } else {
        $recsarr[] = $_REQUEST['id'];
        if (empty($recrepairsjson)){
            $recrepairsjson[] = array('id' => $_REQUEST['id']);
        }
    }

    if (!empty($rectechs)) {
        foreach ($rectechs as $lt) {
            $labortechs[$lt['id']] = array('tech' => $lt['last'] . ', ' . $lt['first'], 'rate' => $lt['techrate']);
        }
    }

    $stmt = "select showpartfeeaspart from settings where shopid = ?";
    if ($query = $conn->prepare($stmt)){
     $query->bind_param("s",$shopid);
     $query->execute();
     $query->bind_result($showpartfeeaspart);
     $query->fetch();
     $query->close();
    }

    if (!empty($recrepairsjson)) {
        foreach ($recrepairsjson as $recrepair) {
            $id = $recrepair['id'];

            // get the details of the recommend and restore them to the current ro
            $stmt = "select id,shopid,roid,comid,`desc`,totalrec,originalcomplaintid,originalroid,technotes,ts from recommend where shopid = ? and id = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si", $shopid, $id);
                if ($query->execute()) {
                    $query->store_result();
                    $num_roid_rows = $query->num_rows;
                    if ($num_roid_rows > 0) {
                        $query->bind_result($id, $dbshopid, $dbroid, $comid, $desc, $totalrec, $originalcomplaintid, $originalroid, $technotes, $ts);
                        $query->fetch();
                    } else {
                        $complaintid = 0;
                    }
                } else {
                    echo "Execution Error|Executing Query: Select from recommend";
                    exit;
                }
                $query->close();
            } else {
                echo "Connection Error|Getting Recommend Details";
                exit;
            }

            if (isset($recrepair['issue']) && !empty($recrepair['issue'])){
                $desc = $recrepair['issue'];
            }
            if (isset($recrepair['category']) && !empty($recrepair['category'])){
                $category = $recrepair['category'];
            }

            $desc = str_replace("Customer Declined: ", "RESTORED REC. REPAIR: ", $desc);
            //echo $desc;

            // get the next complaintid
            $stmt = "select coalesce(complaintid,0) compid from complaints where shopid = ? order by complaintid desc,roid limit 1";
            //echo $stmt."\r\n";
            if ($query = $conn->prepare($stmt)) {

                $query->bind_param("s", $shopid);
                $query->execute();
                $query->store_result();
                $num_roid_rows = $query->num_rows;
                if ($num_roid_rows > 0) {
                    $query->bind_result($complaintid);
                    $query->fetch();
                } else {
                    $complaintid = 999;
                }
                $query->close();

            } else {
                echo "RO Number failed: (" . $conn->errno . ") " . $conn->error;
            }
            //echo $complaintid."\r\n";
            $complaintid = $complaintid + 1;

            // add a complaint using the recommend
            $stmt = "insert into complaints (shopid,roid,complaintid,complaint,issue,techreport) values ('$shopid',$roid,$complaintid,?,?,?)";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sss", $desc, $category, $technotes);
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();

                    //notification
                    $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='14'";
                    $query = $conn->prepare($stmt);
                    $query->execute();
                    $query->store_result();
                    $numrows = $query->num_rows();
                    if ($numrows > 0) {
                        $query->bind_result($textcontent, $emailcontent, $popupcontent);
                        $query->fetch();
                        $emailcontent = str_replace("*|RO|*", $roid, str_replace("*|SN|*", $sn, $emailcontent));
                        $popupcontent = str_replace("*|RO|*", $roid, str_replace("*|SN|*", $sn, $popupcontent));
                        $textcontent = str_replace("*|RO|*", $roid, $textcontent);
                        $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'14',?,?,?)";
                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param('isss', $shopid, $popupcontent, $textcontent, $emailcontent);
                            $query->execute();
                            $conn->commit();
                            $query->close();
                        }
                    }

                } else {
                    echo "Execution Error|Inserting into Part from RecommendParts";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Parts from RecommendParts";
                exit;
            }

            $stmt = "select upper(updateinvonadd) from company where shopid = ?";
            if ($query = $conn->prepare($stmt)) {

                $query->bind_param("s", $shopid);
                $query->execute();
                $query->bind_result($updateinvonadd);
                $query->fetch();
                $query->close();

            } else {
                echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
            }

            // 2. get the parts from the recommendparts table and restore them to the parts table
            $stmt = "select shopid,partnumber,partdesc,partprice,quantity,roid,supplier,cost,partinvoicenumber,partcode,linettlprice,linettlcost,`date`,partcategory,complaintid,discount,net,tax,bin,overridematrix, allocated from recommendparts where shopid = '$shopid' and recid = $id";
            $result = $conn->query($stmt);
            while ($row = $result->fetch_assoc()) {

                if(strtoupper($row['allocated']) == "NON"){
                    $preg = "partsregistry";
                    $stmt = sprintf("SELECT table_name FROM information_schema.tables WHERE table_schema = 'shopboss' AND table_name = 'partsregistry-%s' LIMIT 1", $shopid);
                    if ($tquery = $conn->prepare($stmt)) {
                        $tquery->execute();
                        $results = $tquery->get_result();
                        $tquery->store_result();
                        if ($results->num_rows > 0) {
                            $preg = "`partsregistry-" . $shopid . "`";
                        }
                        $tquery->close();
                    }
                    $pstmt = "SELECT allocatted, shopid, partnumber, partdesc, partprice, PartSupplier as supplier, PartCost as cost, ' non-inv' as partinvoicenumber, partcode, PartPrice as price, ts as `date`, partcategory, tax, bin, overridematrix FROM $preg WHERE shopid = ? AND PartNumber = ? AND PartDesc = ? ORDER BY ts DESC LIMIT 1";
                } else {
                    $pstmt = "SELECT allocatted, shopid, partnumber, partdesc, partprice, PartSupplier as supplier, PartCost as cost, invoicenumber as partinvoicenumber, partcode, PartPrice as price, ts as `date`, partcategory, tax, bin, overridematrix FROM partsinventory WHERE shopid = ? AND PartNumber = ? AND PartDesc = ? ORDER BY ts DESC LIMIT 1";
                }
                if ($pquery = $conn->prepare($pstmt)) {
                    $pquery->bind_param("sss", $oshopid, $row['partnumber'], $row['partdesc']);
                    $pquery->execute();
                    $rs = $pquery->get_result();
                    if ($rs->num_rows > 0) {
                        $presult = $rs->fetch_assoc();
                        $presult['linettlprice'] = $row['quantity'] * $presult['price'];
                        $presult['linettlcost'] = $row['quantity'] * $presult['cost'];
                        $presult['discount'] = $row['discount'];
                        $presult['net'] = $row['net'];
                        $row = array_merge($row, $presult);
                    }
                } else {
                    echo "pi prepare failed " . $conn->error;
                }


                $stmt = "insert into parts (shopid,partnumber,partdesc,partprice,quantity,roid,supplier"
                    . ",cost,partinvoicenumber,partcode,linettlprice,linettlcost,`date`,partcategory,"
                    . "complaintid,discount,net,tax,allocated,bin,overridematrix) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("sssddisdssddssissssss", $row["shopid"],$row["partnumber"],$row["partdesc"],$row["partprice"],$row["quantity"],$roid,$row["supplier"],$row["cost"],$row["partinvoicenumber"],$row["partcode"],$row["linettlprice"],$row["linettlcost"],$today,$row["partcategory"],$complaintid,$row["discount"],$row["net"],$row["tax"],$row['allocated'],$row['bin'],$row['overridematrix']);
                    if ($query->execute()) {
                        $partid = $conn->insert_id;
                        $conn->commit();
                        $query->close();
                    } else {
                        echo "Execution Error|Inserting into Part from RecommendParts - ".$stmt;
                        exit;
                    }
                } else {
                    echo "Connection Error|Inserting into Parts from RecommendParts - ".$stmt;
                    exit;
                }

                if (strtolower($row['allocated']) != 'non' && $updateinvonadd == 'YES') {
                    // update inventory
                    $uquantity = $row["quantity"];
                    $upartnum = $row["partnumber"];
                    $ustmt = "update partsinventory set onhand = onhand - $uquantity, netonhand = netonhand - $uquantity where shopid = ? and partnumber = ?";
                    if ($uquery = $conn->prepare($ustmt)) {
                        $uquery->bind_param("ss", $oshopid, $upartnum);
                        if ($uquery->execute()) {
                            $conn->commit();
                            $uquery->close();
                            //echo "success";
                        } else {
                            echo $conn->errno;
                        }

                    } else {
                        echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }
                }

                if($showpartfeeaspart == 'no')
                {
                    $stmt = "select additionalfeename,addfeetaxable,addfeeamt,addfeepercentordollar,qtyflag from partsinventoryfees where shopid = ? and partnumber = ?";
                    if ($query = $conn->prepare($stmt)){

                        $query->bind_param("ss",$oshopid,$row["partnumber"]);
                        $query->execute();
                        $r = $query->get_result();
                        while ($rs = $r->fetch_assoc()){

                            $qtyflag = $rs['qtyflag'];
                            $feedesc = strtoupper($rs['additionalfeename']);
                            $feetype = $rs['addfeepercentordollar'];

                            if (strtolower($feetype) == "dollar"){
                                if ($qtyflag == 'yes') {
                                    $feeamt = $rs['addfeeamt'] ;
                                    $linettlprice = ($rs['addfeeamt'] * $row["quantity"]) ;
                                }else{
                                    $feeamt = $rs['addfeeamt'] ;
                                    $linettlprice = $rs['addfeeamt'] ;
                                }

                            }elseif (strtolower($feetype) == "percent"){
                                if ($qtyflag == 'yes') {
                                    $feeamt = sbpround(($row["partprice"] * ($rs['addfeeamt'] / 100)),2);
                                    $linettlprice = $feeamt * $row["quantity"];
                                }else{
                                    $feeamt = sbpround(($row["partprice"] * ($rs['addfeeamt'] / 100)),2);
                                    $linettlprice = $feeamt ;

                                }
                            }
                            $feetax = $rs['addfeetaxable'];
                                
                            $stmt = "insert into rofees (`shopid`,`roid`,`itemid`,`feename`,`feeamount`,`taxable`) values (?,?,?,?,?,?)";

                            if ($query = $conn->prepare($stmt)){
                                $query->bind_param("sddsds",$oshopid,$roid,$partid,$feedesc,$linettlprice,$feetax);
                                $query->execute();
                                $conn->commit();
                                $query->close();
                            }

                        }

                    }
                }

            }

            // 3. Now get the labor
            $stmt = "select id,recid,shopid,roid,comid,`desc`,rate,ratelabel,hours,total,tech from recommendlabor where shopid = '$shopid' and recid = $id";
            $result = $conn->query($stmt);
            while ($row = $result->fetch_array()) {
                if (isset($labortechs[$row['id']])) {
                    $row['tech'] = $labortechs[$row['id']]['tech'];
                }

                /*$lstmt = "SELECT hourlyrate as rate, LaborHours as hours, labor as `desc`, tech, LineTotal as total  FROM labor where shopid = ? AND labor = ? AND tech = ? order by ts DESC LIMIT 1";
                if ($lquery = $conn->prepare($lstmt)) {
                    $lquery->bind_param("sss", $shopid, $row['desc'], $row['tech']);
                    $lquery->execute();
                    $rs = $lquery->get_result();
                    if ($rs->num_rows > 0) {
                        $row = $rs->fetch_assoc();
                    }
                    $lquery->close();
                }*/

                $stmt = "insert into labor (shopid,roid,hourlyrate,ratelabel,laborhours,labor,tech,linetotal,complaintid) values (?,?,?,?,?,?,?,?,?)";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("sidsdssdi",$shopid,$roid,$row['rate'],$row['ratelabel'],$row['hours'],$row['desc'],$row['tech'],$row['total'],$complaintid);
                    if ($query->execute()) {
                        $conn->commit();
                        $query->close();
                    } else {
                        echo "Execution Error|Inserting into Labor from RecommendLabor";
                        exit;
                    }
                } else {
                    echo "Connection Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            }

            $newsubletid = 1;
            $stmt = "select subletid from sublet where shopid = '$shopid' order by subletid desc limit 1";
            if ($query = $conn->prepare($stmt)) {
                $query->execute();
                $query->bind_result($subletid);
                if ($query->fetch()) {
                    if (is_numeric($subletid)) {
                        $newsubletid = $subletid + 1;
                    }
                }

                $query->close();


            } else {
                echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            $stmt = "select shopid,roid,comid,subletdesc,subletcost,subletprice,supplier from recommendsublet where shopid = '$shopid' and recid = $id";
            if ($result = $conn->query($stmt)) {
                while ($row = $result->fetch_array()) {

/*                    $pstmt = "SELECT shopid, roid, subletdesc, subletcost, subletprice, SubletSupplier as supplier FROM sublet WHERE shopid = ? AND SubletDesc = ? ORDER BY ts DESC LIMIT 1";
                    if ($pquery = $conn->prepare($pstmt)) {
                        $pquery->bind_param("ss", $shopid, $row['subletdesc']);
                        $pquery->execute();
                        $r = $pquery->get_result();
                        if ($r->num_rows > 0) {
                            $row = $r->fetch_assoc();
                        }
                        $pquery->close();
                    } else {
                        echo "sublt prepare failed " . $conn->error;
                    }*/

                    $stmt = "insert into sublet (shopid,SubLetID,ROID,SubletDesc,SubletPrice,SubletCost,SubletSupplier,complaintid) values (?,?,?,?,?,?,?,?)";
                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("siisddsi",$shopid,$newsubletid,$roid,$row['subletdesc'],$row['subletprice'],$row['subletcost'],$row['supplier'],$complaintid);
                        if ($query->execute()) {
                            $conn->commit();
                            $query->close();
                            $newsubletid += 1;
                        } else {
                            echo $conn->error;
                            //exit;
                        }
                    } else {
                        echo $conn->error;
                        //exit;
                    }
                }
            }


            // 4.  now delete from the recommendparts and recommendlabor
            $stmt = "delete from recommend where shopid = '$shopid' and id = $id";
            if ($query = $conn->prepare($stmt)) {
                //$query->bind_param("si",$shopid,$roid,$comid);
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();
                } else {
                    echo "Execution Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Labor from RecommendLabor";
                exit;
            }


            $stmt = "delete from recommendlabor where shopid = '$shopid' and recid = $id";
            if ($query = $conn->prepare($stmt)) {
                //$query->bind_param("si",$shopid,$roid,$comid);
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();
                } else {
                    echo "Execution Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Labor from RecommendLabor";
                exit;
            }


            $stmt = "delete from recommendparts where shopid = '$shopid' and recid = $id";
            if ($query = $conn->prepare($stmt)) {
                //$query->bind_param("si",$shopid,$roid,$comid);
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();
                } else {
                    echo "Execution Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Labor from RecommendLabor";
                exit;
            }

            $stmt = "delete from recommendsublet where shopid = '$shopid' and recid = $id";
            if ($query = $conn->prepare($stmt)) {
                //$query->bind_param("si",$shopid,$roid,$comid);
                if ($query->execute()) {
                    $conn->commit();
                    $query->close();
                } else {
                    echo "Execution Error|Inserting into Labor from RecommendLabor";
                    exit;
                }
            } else {
                echo "Connection Error|Inserting into Labor from RecommendLabor";
                exit;
            }
        }
        echo "success";

    }

}

?>
<?php if (isset($conn)) {
    mysqli_close($conn);
} ?>