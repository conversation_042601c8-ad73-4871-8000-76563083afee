<?php
require CONN;
$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$roid = filter_var($_GET['roid'], FILTER_SANITIZE_STRING);
$comid = filter_var($_GET['comid'], FILTER_SANITIZE_STRING);
$laborid = filter_var($_GET['laborid'], FILTER_SANITIZE_STRING);
$labortax = 0;
$applyDiscount = $_COOKIE['applydiscounts'] ? strtolower($_COOKIE['applydiscounts']) :'no';

$stmt = "select labortaxrate from repairorders where shopid = ? and roid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("ss", $shopid, $roid);
    $query->execute();
    $query->bind_result($labortax);
    $query->fetch();
    $query->close();
}

$stmt = "select schedulelength,scheduletext,hourlyrate,ratelabel,laborhours,labor,tech,linetotal,discount,discountpercent,salesperson from labor where shopid = ? and laborid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("ss", $shopid, $laborid);
    $query->execute();
    $query->bind_result($taxable, $labormatrix, $hourlyrate, $ratelabel, $laborhours, $labor, $tech, $linetotal, $discount, $discountpercent, $salesperson);
    $query->fetch();
    $query->close();

} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$calclabor = sbpround(($laborhours * $hourlyrate) - $discount);
if (abs(round($calclabor - $linetotal, 3)) >= 0.015) {
    $flatprice = " checked ";
} else {
    $flatprice = "";
}

include getHeadGlobal('');
?>

<body>
<form name="mainform" id="mainform">
    <input type="hidden" name="shopid" id="shopid" value="<?php echo $shopid; ?>">
    <input type="hidden" name="roid" id="roid" value="<?php echo $roid; ?>">
    <input type="hidden" name="comid" id="comid" value="<?php echo $comid; ?>">
    <input type="hidden" name="laborid" id="laborid" value="<?php echo $laborid; ?>">
    <input type="hidden" name="discountamt" id="discountamt" value="">

    <div>
        <div class="row p-3 mt-4">
            <div class="col-md-12">

                <?php
                if ($labortax > 0) {
                    if (strtolower($taxable) == "yes") {
                        $ty = " selected ";
                        $tn = "";
                    } elseif (strtolower($taxable) == "no") {
                        $ty = "";
                        $tn = " selected ";
                    } elseif ($taxable == "" || $taxable == 'null') {
                        $ty = " selected ";
                        $tn = "";
                    }
                    ?>

                    <div class="form-row mb-4">
                        <select id="labortax" name="labortax" class="select">
                            <option <?php echo $ty; ?> value="yes">Yes</option>
                            <option <?php echo $tn; ?> value="no">No</option>
                        </select>
                        <label class="form-label select-label" for="labortax">Labor Taxable?</label>
                    </div>

                    <?php
                }
                ?>

                <div class="form-outline mb-4">
                    <textarea class="form-control" tabindex="1" id="labor" name="labor" rows="3" ai-writing-tool><?php echo $labor; ?></textarea>
                    <label class="form-label" for="labor" title="2000 character max">Labor Description</label>
                </div>

                <div class="form-outline mb-4">
                    <input class="form-control" onblur="calcLabor()" tabindex="2" type="text"
                           value="<?php echo $laborhours; ?>" id="hours" name="hours">
                    <label class="form-label" for="hours">Labor Hours</label>
                </div>

                <?php if($_COOKIE['mode'] == 'full'){?>
                <div class="form-row mb-4">
                    <select onchange="checkVal(this.value)" name="labormatrix" id="labormatrix" tabindex="3"
                            class="select">
                        <?php
                        $stmt = "select distinct category from labormatrix where shopid = '$shopid'";
                        if ($labormatrix == "none") {
                            $ns = "selected";
                        } else {
                            $ns = "";
                        }
                        if ($query = $conn->prepare($stmt)) {
                            $query->execute();
                            $query->store_result();
                            $numrows = $query->num_rows();
                            $query->free_result();
                            if ($numrows > 0) {
                                $query->execute();
                                $r = $query->get_result();
                                while ($rs = $r->fetch_array()) {
                                    if (strtolower($labormatrix) == strtolower(trim($rs['category']))) {
                                        $s = "selected";
                                    } else {
                                        $s = "";
                                    }
                                    echo "<option $s value='" . $rs['category'] . "'>" . $rs['category'] . "</option>";
                                }
                                echo "<option $ns value='none'>NONE</option>";
                            } else {
                                echo "<option $ns value='none'>NONE</option>";
                            }
                        }
                        ?>

                    </select>
                    <label class="form-label select-label" for="labormatrix">Labor Matrix</label>
                </div>
                <?php }else{?><input type="hidden" name="labormatrix" id="labormatrix"><?php }?>

                <div class="form-row mb-4">
                    <select class="select" tabindex="4" name="tech" id="tech">
                        <?php

                        $query = "Select EmployeeLast, EmployeeFirst from employees where shopid = ? and Active = 'yes' and showtechlist = 'yes' order by employeelast asc";
                        if ($stmt = $conn->prepare($query)) {
                            $stmt->bind_param("s", $shopid);
                            $stmt->execute();
                            $result = $stmt->get_result();
                            $stmt->store_result();
                            while ($row = $result->fetch_assoc()) {
                                if (htmlspecialchars_decode(strtoupper($tech), ENT_QUOTES) == strtoupper($row['EmployeeLast']) . ', ' . strtoupper($row['EmployeeFirst'])) {
                                    echo '<option selected value="' . strtoupper($row['EmployeeLast']) . ', ' . strtoupper($row['EmployeeFirst']) . '">' . strtoupper($row['EmployeeLast']) . ', ' . strtoupper($row['EmployeeFirst']) . '</option>';
                                } else {
                                    echo '<option value="' . strtoupper($row['EmployeeLast']) . ', ' . strtoupper($row['EmployeeFirst']) . '">' . strtoupper($row['EmployeeLast']) . ', ' . strtoupper($row['EmployeeFirst']) . '</option>';
                                }
                            }
                        }
                        ?>
                    </select>
                    <label class="form-label select-label" for="tech">Technician</label>
                </div>

                
                    <?php

                    $rquery = "select HourlyRate,hourlyrate2,hourlyrate3,hourlyrate4,hourlyrate5,hourlyrate6,hourlyrate7,hourlyrate8,hourlyrate9,hourlyrate10,hourlyrate1label,hourlyrate2label,hourlyrate3label,hourlyrate4label,hourlyrate5label,hourlyrate6label,hourlyrate7label,hourlyrate8label,hourlyrate9label,hourlyrate10label from company where shopid = ?";
                    if ($rstmt = $conn->prepare($rquery)) {
                        $rstmt->bind_param("s", $shopid);
                        $rstmt->execute();
                        $rresult = $rstmt->bind_result($hrate, $hrate2, $hrate3, $hrate4, $hrate5, $hrate6, $hrate7, $hrate8, $hrate9, $hrate10, $hlabel1, $hlabel2, $hlabel3, $hlabel4, $hlabel5, $hlabel6, $hlabel7, $hlabel8, $hlabel9, $hlabel10);
                        $rstmt->fetch();
                        $rstmt->close();

                    }
                    if (strlen($hlabel2) > 0) {
                        ?>
                        <div class="form-row mb-4">
                        <select onchange="reCalcLabor(this.value,this.options[this.selectedIndex].getAttribute('lab'))"
                                tabindex="5" class="select" name="hourlyrate" style="text-transform:uppercase"
                                id="hourlyrate">
                            <option lab="<?= $hlabel1 ?>" <?php if ((empty($ratelabel) || strtoupper($ratelabel) == strtoupper($hlabel1)) && $hourlyrate == $hrate) {
                                echo 'selected';
                            } ?>
                                    value="<?php echo $hrate; ?>"><?php echo strtoupper($hlabel1) . "-" . number_format($hrate, 2); ?></option>
                            <option lab="<?= $hlabel2 ?>" <?php if ((empty($ratelabel) || strtoupper($ratelabel) == strtoupper($hlabel2)) && $hourlyrate == $hrate2) {
                                echo 'selected';
                            } ?>
                                    value="<?php echo $hrate2; ?>"><?php echo strtoupper($hlabel2) . "-" . number_format($hrate2, 2); ?></option>
                            <?php if ($hlabel3 != '') { ?>
                                <option
                                lab="<?= $hlabel3 ?>" <?php if ((empty($ratelabel) || strtoupper($ratelabel) == strtoupper($hlabel3)) && $hourlyrate == $hrate3) {
                                    echo 'selected';
                                } ?>
                                value="<?php echo $hrate3; ?>"><?php echo strtoupper($hlabel3) . "-" . number_format($hrate3, 2); ?></option><?php } ?>
                            <?php if ($hlabel4 != '') { ?>
                                <option
                                lab="<?= $hlabel4 ?>" <?php if ((empty($ratelabel) || strtoupper($ratelabel) == strtoupper($hlabel4)) && $hourlyrate == $hrate4) {
                                    echo 'selected';
                                } ?>
                                value="<?php echo $hrate4; ?>"><?php echo strtoupper($hlabel4) . "-" . number_format($hrate4, 2); ?></option><?php } ?>
                            <?php if ($hlabel5 != '') { ?>
                                <option
                                lab="<?= $hlabel5 ?>" <?php if ((empty($ratelabel) || strtoupper($ratelabel) == strtoupper($hlabel5)) && $hourlyrate == $hrate5) {
                                    echo 'selected';
                                } ?>
                                value="<?php echo $hrate5; ?>"><?php echo strtoupper($hlabel5) . "-" . number_format($hrate5, 2); ?></option><?php } ?>
                            <?php if ($hlabel6 != '') { ?>
                                <option
                                lab="<?= $hlabel6 ?>" <?php if ((empty($ratelabel) || strtoupper($ratelabel) == strtoupper($hlabel6)) && $hourlyrate == $hrate6) {
                                    echo 'selected';
                                } ?>
                                value="<?php echo $hrate6; ?>"><?php echo strtoupper($hlabel6) . "-" . number_format($hrate6, 2); ?></option><?php } ?>
                            <?php if ($hlabel7 != '') { ?>
                                <option
                                lab="<?= $hlabel7 ?>" <?php if ((empty($ratelabel) || strtoupper($ratelabel) == strtoupper($hlabel7)) && $hourlyrate == $hrate7) {
                                    echo 'selected';
                                } ?>
                                value="<?php echo $hrate7; ?>"><?php echo strtoupper($hlabel7) . "-" . number_format($hrate7, 2); ?></option><?php } ?>
                            <?php if ($hlabel8 != '') { ?>
                                <option
                                lab="<?= $hlabel8 ?>" <?php if ((empty($ratelabel) || strtoupper($ratelabel) == strtoupper($hlabel8)) && $hourlyrate == $hrate8) {
                                    echo 'selected';
                                } ?>
                                value="<?php echo $hrate8; ?>"><?php echo strtoupper($hlabel8) . "-" . number_format($hrate8, 2); ?></option><?php } ?>
                            <?php if ($hlabel9 != '') { ?>
                                <option
                                lab="<?= $hlabel9 ?>" <?php if ((empty($ratelabel) || strtoupper($ratelabel) == strtoupper($hlabel9)) && $hourlyrate == $hrate9) {
                                    echo 'selected';
                                } ?>
                                value="<?php echo $hrate9; ?>"><?php echo strtoupper($hlabel9) . "-" . number_format($hrate9, 2); ?></option><?php } ?>
                            <?php if ($hlabel10 != '') { ?>
                                <option
                                lab="<?= $hlabel10 ?>" <?php if ((empty($ratelabel) || strtoupper($ratelabel) == strtoupper($hlabel10)) && $hourlyrate == $hrate10) {
                                    echo 'selected';
                                } ?>
                                value="<?php echo $hrate10; ?>"><?php echo strtoupper($hlabel10) . "-" . number_format($hrate10, 2); ?></option><?php } ?>
                            <?php if ($ratelabel == 'TEMP') { ?>
                                <option lab="TEMP" selected
                                        value="<?php echo $hourlyrate ?>"><?php echo "TEMP-" . number_format($hourlyrate, 2); ?></option><?php } ?>
                        </select>
                        <label class="form-label select-label" for="hourlyrate">Hourly Rate</label>
                        <input type="hidden" name="ratelabel" id="ratelabel" value="<?= $ratelabel ?>">
                        </div>
                        <?php
                    } else {
                        ?>
                        <div class="form-outline mb-4">
                        <input required type="text" name="hourlyrate" id="hourlyrate"
                               value="<?php echo number_format($hourlyrate, 2); ?>" class="form-control">
                        <label class="form-label" for="hourlyrate">Hourly Rate</label>
                        <input type="hidden" name="ratelabel" id="ratelabel" value="<?= $ratelabel ?>">
                        </div>
                        <?php
                    }
                    ?>
                    

                <div class="form-outline mb-4">
                    <input onkeyup="calcLabor()" class="form-control" onblur="discpercent()" tabindex="6" type="text"
                           value="<?php echo $discountpercent; ?>" id="discount" name="discount" <?= ($applyDiscount == "yes" ? "" : "readonly") ?> >
                    <label class="form-label" for="discount">Discount Percent</label>
                </div>


                <div class="form-outline mb-4">
                    <input class="form-control" tabindex="7" type="text" id="total" value="<?php echo $linetotal; ?>"
                           name="total">
                    <label class="form-label" for="total">Total Labor</label>
                </div>


                <div class="form-row mb-4">
                    <select class="select" tabindex="8" name="complaintid" id="complaintid">
                        <?php
                        $stmt = "select complaint,complaintid from complaints where shopid = '$shopid' and roid = $roid and cstatus = 'no' order by displayorder asc";
                        if ($query = $conn->prepare($stmt)) {
                            $query->execute();
                            $result = $query->get_result();
                            $query->store_result();
                            $numrows = $result->num_rows;
                            if ($numrows > 0) {
                                $i = 1;
                                while ($row = $result->fetch_array()) {
                                    if ($row['complaintid'] == $comid) {
                                        echo "<option selected='selected' value='" . $row['complaintid'] . "'>#" . $i . ' ' . strtoupper(substr($row['complaint'], 0, 30)) . "</option>";
                                    } else {
                                        echo "<option value='" . $row['complaintid'] . "'>#" . $i . ' ' . strtoupper(substr($row['complaint'], 0, 30)) . "</option>";
                                    }
                                    $i++;
                                }
                            }
                        }

                        ?>
                    </select>
                    <label class="form-label select-label" for="complaintid">Select Vehicle Issue</label>
                </div>

                <?php if($_COOKIE['mode'] == 'full'){?>

                <div class="form-row mb-4">
                    <select class="select" tabindex="9" id="salesperson" name="salesperson">
                        <option value="nosalesperson">None</option>
                        <?php
                        $stmt = "select employeelast,employeefirst from employees where active = 'yes' and shopid = '$shopid' order by employeelast";
                        if ($query = $conn->prepare($stmt)) {
                            $query->execute();
                            $result = $query->get_result();
                            while ($row = $result->fetch_array()) {

                                if (strtoupper($row['employeelast'] . ", " . $row['employeefirst']) == strtoupper($salesperson)) {
                                    $s = " selected ";
                                } else {
                                    $s = "";
                                }
                                echo "<option " . $s . " value='" . strtoupper($row['employeelast'] . ", " . $row['employeefirst']) . "'>" . strtoupper($row['employeelast'] . ", " . $row['employeefirst']) . "</option>";


                            }
                        }
                        ?>
                    </select>
                    <label class="form-label select-label" for="salesperson">Who Sold This Labor (For
                        Commissions)?</label>
                </div>

               <?php }?>

                <div class="form-check mb-8">
                    <input <?php echo $flatprice; ?> class="form-check-input" tabindex="10" type="checkbox"
                                                     id="override" name="override">
                    <label for="override" class="form-check-label">Override Calculations <i class="fas fa-circle-info"
                                                                                            data-mdb-toggle="tooltip"
                                                                                            title="This will prevent normal calculations as long as Labor Matrix is NONE"></i>
                    </label>
                </div>


            </div>
        </div>
    </div>


</form>

<nav class="fixed-bottom emodal-footer">
    <button type="button" onclick="saveLabor('no')" class="btn btn-primary">Save</button>
</nav>


<?php include getScriptsGlobal(''); ?>

<script>

    $(document).ready(function () {

        setTimeout(function () {
            $('#labor').focus()
        }, 500)

    });

    $('#discount').on("keyup", function () {

        if ($.isNumeric($('#price').val())) {
            price = $('#price').val()
            discpercent = $('#discount').val() / 100
            $('#discountamt').val((price * discpercent).toFixed(2))
            $('#labelfordiscount').show().html("Discount Percentage  (Discount Amount: $" + (price * discpercent).toFixed(2) + ")").css("font-weight", "bold").css("color", "red")
            $('#net').val(($('#price').val() - (price * discpercent).toFixed(2)).toFixed(2)).css("font-weight", "bold")
        }

    });

    function discpercent() {

        d = $('#discount').val()
        if (!$.isNumeric(d)) {
            $('#discount').val(0)
            $('#discountfloatinglabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")
        }

    }


    function saveLabor(addanother) {


        setTimeout(function () {
            lsat = false;
            hsat = false;
            proceed = true
            labor = $('#labor').val()

            if (labor == '' || labor.length <= 1) {
                proceed = false
                sbalert("Labor Description is Required");
            }
            if (proceed == true) {
                if (!$.isNumeric($('#hours').val())) {
                    proceed = false
                    sbalert("Labor Hours are Required");
                }
            }
            if (proceed == true) {
                if (!$.isNumeric($('#hourlyrate').val())) {
                    proceed = false
                    sbalert("Hourly Rate is Required");
                }
            }
            discpercent = $('#discount').val()
            if (!$.isNumeric(discpercent)) {
                $('#discount').val(0)
            }

            calcLabor()

            if (proceed == true) {

                if ($('#override').is(':checked')) {

                    sbconfirm("Override Check", "The Override is checked. Do you want to Override the calculated labor?", function () {

                        showLoader()

                        ds = $("#mainform").serialize()
                        $.ajax({
                            data: ds,
                            url: "editlaboraction.php",
                            type: "get",
                            success: function (r) {
                                parent.location.reload()
                            }
                        });

                    });
                } else {
                    showLoader()
                    ds = $("#mainform").serialize()
                    $.ajax({
                        data: ds,
                        url: "editlaboraction.php",
                        type: "get",
                        success: function (r) {
                            parent.location.reload()
                        }
                    });
                }
            }
        }, 500);
    }


    function checkVal(v) {
        if (v == "none") {
            $('#override').prop('checked', false)
        }

        calcLabor()
    }

    function reCalcLabor(v, l) {
        $('#ratelabel').val(l)
        calcLabor()
    }


    function calcLabor() {


        <?php

        $stmt = "select category,factor,start,end from labormatrix where shopid = ? order by Category, Start";

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $result = $query->get_result();
            $rs = array();
            while ($row = $result->fetch_assoc()) {
                $rs[] = $row;
            }
            echo "var lm = " . json_encode($rs) . "\r\n";
        }
        ?>

        lmid = $('#labormatrix').val()
        hourlyrate = $('#hourlyrate').val()
        hours = $('#hours').val()
        discamt = parseFloat($('#discount').val())
        if (!$.isNumeric(discamt)) {
            $('#discount').val(0)
            discamt = parseFloat(0)
        }
        console.log("disc:" + discamt)
        if (!$('#override').is(':checked')) {

            if (lmid != "none" && lmid != "") {
                // get the markup from the name

                if ($.isNumeric(hourlyrate) && $.isNumeric(hours)) {
                    tlabor = (hourlyrate * hours).toFixed(2)
                    discamt = (discamt / 100) * tlabor
                    $('#discountamt').val(discamt)
                    calclabor = tlabor - discamt

                    $.each(lm, function (i, v) {

                        if (v.category.toUpperCase() === lmid.toUpperCase() && v.start <= calclabor && v.end >= calclabor) {
                            calclabor = Math.round((calclabor * v.factor) * 100) / 100
                            $('#override').prop('checked', true)
                            return false
                        }
                    })

                    $('#total').val(calclabor.toFixed(2)).addClass("active")
                    $('#discountfloatinglabel').html("Discount Percent " + "<span id='discpercentlabel' style='color:red;margin-left:20px;'>(In Dollars $" + discamt.toFixed(2) + ")</span>")

                }


            } else {

                if ($.isNumeric(hourlyrate) && $.isNumeric(hours)) {

                    if ($.isNumeric(discamt)) {
                        tlabor = (hourlyrate * hours).toFixed(2)
                        discamt = (discamt / 100) * tlabor
                        $('#discountamt').val(discamt)
                        calclabor = tlabor - discamt
                        $('#total').val(calclabor.toFixed(2)).addClass("active")
                        $('#discountfloatinglabel').html("Discount Percent " + "<span id='discpercentlabel' style='color:red;margin-left:20px;'>(In Dollars $" + discamt.toFixed(2) + ")</span>")
                    } else {
                        tlabor = (hourlyrate * hours).toFixed(2)
                        discamt = 0
                        calclabor = tlabor - discamt
                        $('#total').val(calclabor.toFixed(2)).addClass("active")
                    }
                }

            }
        } else if ($('#override').is(':checked') && lmid != "none" && lmid != "") {

            if ($.isNumeric(hourlyrate) && $.isNumeric(hours)) {
                tlabor = (hourlyrate * hours).toFixed(2)
                discamt = (discamt / 100) * tlabor
                $('#discountamt').val(discamt)
                calclabor = tlabor - discamt
                $.each(lm, function (i, v) {
                    if (v.category.toUpperCase() === lmid.toUpperCase() && v.start <= calclabor && v.end >= calclabor) {
                        calclabor = Math.round((calclabor * v.factor) * 100) / 100
                        $('#override').prop('checked', true)
                        return false
                    }
                })

                $('#total').val(calclabor.toFixed(2)).addClass("active")
                $('#discountfloatinglabel').html("Discount Percent " + "<span id='discpercentlabel' style='color:red;margin-left:20px;'>(In Dollars $" + discamt.toFixed(2) + ")</span>")

            }

        }

    }

</script>
<?php require_once(AI_WRITING_TOOL);?>
</body>
</html>
