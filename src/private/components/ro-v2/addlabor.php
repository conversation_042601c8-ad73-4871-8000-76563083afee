<?php
require CONN;
include getHeadGlobal('');

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$roid = filter_var($_GET['roid'], FILTER_SANITIZE_STRING);
$comid = filter_var($_GET['comid'], FILTER_SANITIZE_STRING);
$applyDiscount = $_COOKIE['applydiscounts'] ? strtolower($_COOKIE['applydiscounts']) :'no';
$showcarfax = "both";

$stmt = "select labortypeahead,carfaxlocation,vehiclefield1label,vehiclefield2label,vehiclefield3label,vehiclefield4label,vehiclefield5label,vehiclefield6label,vehiclefield7label,vehiclefield8label,"
    . "showcarfaxonly from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($labortypeahead, $cf, $vf1, $vf2, $vf3, $vf4, $vf5, $vf6, $vf7, $vf8, $showcarfax);
    $query->fetch();
    $query->close();

} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$labortypeahead = strtolower($labortypeahead);

$stmt = "select vinlabel vl, yearlabel yl,makelabel ml,modellabel modl,colorlabel cl,enginelabel el,cylinderlabel cyll,translabel tl,drivelabel dl,licenselabel ll,statelabel sl,"
    . "fleetlabel fl,currmileagelabel cml from vehiclelabels where shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($vl, $yl, $ml, $modl, $cl, $el, $cyll, $tl, $dl, $ll, $sl, $fl, $cml);
    $query->fetch();
    $query->close();

} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "select DefaultTech from repairorders where shopid = ? and roid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("ss", $shopid, $roid);
    $query->execute();
    $query->bind_result($DefaultTech);
    $query->fetch();
    $query->close();
}

?>
<body>

  <div class="d-flex">

   <div class="container-fluid mt-4 emodal-content">
    
        <form name="mainform" id="mainform">
        <input type="hidden" name="shopid" id="shopid" value="<?php echo $shopid; ?>">
        <input type="hidden" name="roid" id="roid" value="<?php echo $roid; ?>">
        <input type="hidden" name="comid" id="comid" value="<?php echo $comid; ?>">
        <input type="hidden" name="discountamt" id="discountamt" value="">
        <div class="form-outline mb-4">
            <textarea class="form-control" id="labor" tabindex="1" name="labor" required rows="3" ai-writing-tool></textarea>
            <label class="form-label" for="labor" title="2000 character max">Labor Description</label>
        </div>
        <div class="form-outline mb-4">
            <input class="form-control" onblur="calcLabor()" tabindex="2"
                   type="text" id="hours" name="hours" required>
            <label class="form-label" for="hours">Labor Hours</label>
        </div>

        <?php if($_COOKIE['mode'] == 'full'){?>
        <div class="form-row mb-4">
            <select onchange="checkVal(this.value)" name="labormatrix" id="labormatrix" tabindex="3" class="select">

                <?php
                $stmt = "select distinct category from labormatrix where shopid = '$shopid' order by displayorder";
                if ($query = $conn->prepare($stmt)) {
                    $query->execute();
                    $query->store_result();
                    $numrows = $query->num_rows();
                    $query->free_result();
                    if ($numrows > 0) {
                        $query->execute();
                        $r = $query->get_result();
                        while ($rs = $r->fetch_array()) {
                            echo "<option value='" . $rs['category'] . "'>" . $rs['category'] . "</option>";
                        }
                        echo "<option value='none'>NONE</option>";
                    } else {
                        echo "<option value='none'>NONE</option>";
                    }
                }
                ?>

            </select>
            <label class="form-label select-label" for="labormatrix">Labor Matrix</label>
        </div>
        <?php }else{?><input type="hidden" name="labormatrix" id="labormatrix"><?php }?>

        <div class="form-row mb-4">
            <select class="select" tabindex="4" name="tech" id="tech">
                <?php
                $query = "Select EmployeeLast, EmployeeFirst from employees where shopid = ? and Active = 'yes' and showtechlist = 'yes' order by employeelast asc";
                $lcntr = 0;
                if ($stmt = $conn->prepare($query)) {
                    $stmt->bind_param("s", $shopid);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $stmt->store_result();
                    while ($row = $result->fetch_assoc()) {
                        $lcntr += 1;
                    }
                }


                if ($lcntr >= 2) {
                    echo '<option selected value="">SELECT TECHNICIAN</option>';
                }

                $query = "Select EmployeeLast, EmployeeFirst from employees where shopid = ? and Active = 'yes' and showtechlist = 'yes' order by employeelast asc";
                if ($stmt = $conn->prepare($query)) {
                    $stmt->bind_param("s", $shopid);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $stmt->store_result();
                    while ($row = $result->fetch_assoc()) {
                        echo '<option value="' . ($row['EmployeeLast']) . ', ' . ($row['EmployeeFirst']) . '" ' . ($DefaultTech == ($row['EmployeeLast']) . ', ' . ($row['EmployeeFirst']) ? 'selected' : '') . '>' . ($row['EmployeeLast']) . ', ' . ($row['EmployeeFirst']) . '</option>';
                    }
                }
                ?>
            </select>
            <label class="form-label select-label" for="tech">Technician</label>
        </div>
        <div class="form-row mb-4">
            <?php

            $rquery = "select HourlyRate,hourlyrate2,hourlyrate3,hourlyrate4,hourlyrate5,hourlyrate6,hourlyrate7,hourlyrate8,hourlyrate9,hourlyrate10,hourlyrate1label,hourlyrate2label,hourlyrate3label,hourlyrate4label,hourlyrate5label,hourlyrate6label,hourlyrate7label,hourlyrate8label,hourlyrate9label,hourlyrate10label from company where shopid = ?";
            if ($rstmt = $conn->prepare($rquery)) {
                $rstmt->bind_param("s", $shopid);
                $rstmt->execute();
                $rresult = $rstmt->bind_result($hrate, $hrate2, $hrate3, $hrate4, $hrate5, $hrate6, $hrate7, $hrate8, $hrate9, $hrate10, $hlabel1, $hlabel2, $hlabel3, $hlabel4, $hlabel5, $hlabel6, $hlabel7, $hlabel8, $hlabel9, $hlabel10);
                $rstmt->fetch();
                $rstmt->close();

            }
            if (strlen($hlabel2) != '') {
                ?>
                <select
                    onchange="reCalcLabor(this.value,this.options[this.selectedIndex].getAttribute('data-label'))"
                    class="select" name="hourlyrate" tabindex="5"  id="hourlyrate">
                    <option data-label="<?= $hlabel1 ?>"
                            value="<?php echo $hrate; ?>"><?php echo ($hlabel1) . "-" . number_format($hrate, 2); ?></option>
                    <option data-label="<?= $hlabel2 ?>"
                            value="<?php echo $hrate2; ?>"><?php echo ($hlabel2) . "-" . number_format($hrate2, 2); ?></option>
                    <?php if ($hlabel3 != '') { ?>
                        <option  data-label="<?= $hlabel3 ?>"
                                 value="<?php echo $hrate3; ?>"><?php echo ($hlabel3) . "-" . number_format($hrate3, 2); ?></option><?php } ?>
                    <?php if ($hlabel4 != '') { ?>
                        <option  data-label="<?= $hlabel4 ?>"
                                 value="<?php echo $hrate4; ?>"><?php echo ($hlabel4) . "-" . number_format($hrate4, 2); ?></option><?php } ?>
                    <?php if ($hlabel5 != '') { ?>
                        <option  data-label="<?= $hlabel5 ?>"
                                 value="<?php echo $hrate5; ?>"><?php echo ($hlabel5) . "-" . number_format($hrate5, 2); ?></option><?php } ?>
                    <?php if ($hlabel6 != '') { ?>
                        <option  data-label="<?= $hlabel6 ?>"
                                 value="<?php echo $hrate6; ?>"><?php echo ($hlabel6) . "-" . number_format($hrate6, 2); ?></option><?php } ?>
                    <?php if ($hlabel7 != '') { ?>
                        <option data-label="<?= $hlabel7 ?>"
                                value="<?php echo $hrate7; ?>"><?php echo ($hlabel7) . "-" . number_format($hrate7, 2); ?></option><?php } ?>
                    <?php if ($hlabel8 != '') { ?>
                        <option data-label="<?= $hlabel8 ?>"
                                value="<?php echo $hrate8; ?>"><?php echo ($hlabel8) . "-" . number_format($hrate8, 2); ?></option><?php } ?>
                    <?php if ($hlabel9 != '') { ?>
                        <option data-label="<?= $hlabel9 ?>"
                                value="<?php echo $hrate9; ?>"><?php echo ($hlabel9) . "-" . number_format($hrate9, 2); ?></option><?php } ?>
                    <?php if ($hlabel10 != '') { ?>
                        <option data-label="<?= $hlabel10 ?>"
                                value="<?php echo $hrate10; ?>"><?php echo ($hlabel10) . "-" . number_format($hrate10, 2); ?></option><?php } ?>
                </select>
                <?php
            } else {
                ?>
                <input required type="tel" name="hourlyrate" id="hourlyrate" tabindex="6"
                       value="<?php echo number_format($hrate, 2); ?>"
                       class="form-control">
                <?php
            }
            ?>

            <input type="hidden" name="ratelabel" id="ratelabel" value="<?= $hlabel1 ?>">
            <label class="form-label select-label" for="hourlyrate">Hourly Rate</label>
        </div>
        <div class="form-outline mb-4">
            <input onkeyup="calcLabor()" value="0" class="form-control" tabindex="7" type="text" id="discount" name="discount" <?= ($applyDiscount == "yes" ? "" : "readonly") ?> >
            <label class="form-label" for="discount">Discount Percent</label>
        </div>
        <div class="form-outline mb-4">
            <input class="form-control" tabindex="8" type="text" id="total" name="total">
            <label class="form-label" for="total">Total Labor</label>
        </div>
        <?php if($_COOKIE['mode'] == 'full'){?>
        <div class="form-row mb-4">
            <select class="select" tabindex="9" id="salesperson" name="salesperson">
                <option value="nosalesperson">None</option>
                <?php
                $stmt = "select employeelast,employeefirst from employees where active = 'yes' and shopid = '$shopid' order by employeelast";
                if ($query = $conn->prepare($stmt)) {
                    $query->execute();
                    $result = $query->get_result();
                    while ($row = $result->fetch_array()) {
                        echo "<option value='" . ($row['employeelast'] . ", " . $row['employeefirst']) . "'>" . ($row['employeelast'] . ", " . $row['employeefirst']) . "</option>";
                    }
                }
                ?>
            </select>
            <label class="form-label select-label" for="salesperson">Who Sold This Labor
                (For Commissions)</label>
        </div>
        <?php }?>

        <div class="form-check mb-4">
            <input class="form-check-input" tabindex="10" type="checkbox" id="override" name="override">
            <label for="override" class="form-check-label">Override Calculations <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="This will prevent normal calculations as long as Labor Matrix is NONE"></i></label>
        </div>
    </form>

    <nav class="fixed-bottom emodal-footer">

        <button onclick="saveLabor('yes')" class="btn btn-secondary me-3">Save and Add Another</button>
        <button type="button" onclick="saveLabor('no')" class="btn btn-primary">Save</button>

            </nav>
</div>
</div>

<?php include getScriptsGlobal(''); ?>

<script src="<?= SCRIPT ?>/plugins/typeahead/typeahead.js"></script>
<script>
    jQuery(function () {
        
        <?php
        $tstring = "";
        if ($showcarfax == "both") {
            $stmt = "select repair from repairs where shopid = '$shopid' or shopid = '0' order by repair";
            if ($query = $conn->prepare($stmt)) {
                $query->execute();
                $result = $query->get_result();
                $query->store_result();
                while ($row = $result->fetch_assoc()) {
                    if (strlen($row['repair']) > 0) {
                        $tstring .= '"' . str_replace('"', '', $row['repair']) . '",';
                    }
                }
            }
            $stmt = "select labordesc from carfaxdesc order by labordesc";
            if ($query = $conn->prepare($stmt)) {
                $query->execute();
                $result = $query->get_result();
                $query->store_result();
                while ($row = $result->fetch_assoc()) {
                    if (strlen($row['labordesc']) > 0) {
                        $tstring .= '"' . str_replace('"', '', $row['labordesc']) . '",';
                    }
                }
            }
        } elseif ($showcarfax == "yes") {
            $stmt = "select labordesc from carfaxdesc order by labordesc";
            if ($query = $conn->prepare($stmt)) {
                $query->execute();
                $result = $query->get_result();
                $query->store_result();
                while ($row = $result->fetch_assoc()) {
                    if (strlen($row['labordesc']) > 0) {
                        $tstring .= '"' . str_replace('"', '', $row['labordesc']) . '",';
                    }
                }
            }
        } elseif ($showcarfax == "no") {
            $stmt = "select repair from repairs where shopid = '$shopid' or shopid = '0' order by repair";
            if ($query = $conn->prepare($stmt)) {
                $query->execute();
                $result = $query->get_result();
                $query->store_result();
                while ($row = $result->fetch_assoc()) {
                    if (strlen($row['repair']) > 0) {
                        $tstring .= '"' . str_replace('"', '', $row['repair']) . '",';
                    }
                }
            }
        }

        $tstring = substr($tstring, 0, strlen($tstring) - 1);
        //echo $tstring;
        ?>
        <?php
        if ($labortypeahead == "yes"){
        ?>
        data = [<?php echo $tstring; ?>]
        $('#labor').typeahead({
            source: data,
            items: 'all'
        });
        <?php
        }
        ?>
        setTimeout(function () {
            $('#labor').focus()
        }, 500)
    });

    $('#discount').on("keyup", function () {

        if ($.isNumeric($('#price').val())) {
            price = $('#price').val()
            discpercent = $('#discount').val() / 100
            discamt = (price * discpercent).toFixed(2)
            $('#discountamt').val(discamt)
            $('#labelfordiscount').show().html("Discount Percentage  (Discount Amount: $" + (price * discpercent).toFixed(2) + ")").css("font-weight", "bold").css("color", "red")
            $('#net').val(($('#price').val() - (price * discpercent).toFixed(2)).toFixed(2)).css("font-weight", "bold")
        }

    });


    function saveLabor(addanother) {
        showLoader();

        setTimeout(function () {
            lsat = false;
            hsat = false;
            proceed = true
            labor = $('#labor').val()
            labor = labor.replace(" - CARFAX APPROVED", "")
            discpercent = $('#discount').val()

            if ($('#tech').val() == ""){
                proceed = false
                sbalert("You must select a technician")
                hideLoader();
                return
            }
            if ( labor == '' || labor.length <= 1 ){
                proceed = false
                hideLoader();
                sbalert("Labor Description is Required");
            }
            if (proceed == true){
                if ( !$.isNumeric($('#hours').val()) ) {
                    proceed = false
                    hideLoader();
                    sbalert("Labor Hours are Required");
                }
            }
            if (proceed == true){
                if (!$.isNumeric($('#hourlyrate').val())){
                    proceed = false
                    hideLoader();
                    sbalert("Hourly Rate is Required");
                }
            }

            $('#labor').val(labor).addClass('active');
            if ($('#tech').val() == "") {
                proceed = false
                $("#tech_invalid").fadeIn();
                hideLoader();
                return
            }
            if (!$.isNumeric(discpercent)) {
                $('#discount').val(0)
            }

            calcLabor()

            if (proceed == true) {
                ds = $("#mainform").serialize()
                $.ajax({
                    data: ds,
                    url: "addnewlaboraction.php",
                    type: "post",
                    success: function (r) {
                        if (r == "success") {
                            if (addanother == "no") {
                                parent.location.reload()
                            } else {
                                location.reload()
                            }
                        } else {
                            sbalert("Error Adding Labor")
                        }
                    }
                });
            }
        }, 500);
    }


    function checkVal(v) {

        if (v == "none") {
            // uncheck the override box
            $('#override').prop('checked', false)
        }

        calcLabor()
    }

    function reCalcLabor(v, l) {
        $('#ratelabel').val(l)
        calcLabor()
    }

    function calcLabor() {


        <?php

        $stmt = "select category,factor,start,end from labormatrix where shopid = ? order by Category, Start";

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $result = $query->get_result();
            $rs = array();
            while ($row = $result->fetch_assoc()) {
                $rs[] = $row;
            }
            echo "var lm = " . json_encode($rs) . "\r\n";
        }
        ?>

        lmid = $('#labormatrix').val()
        hourlyrate = $('#hourlyrate').val()
        hours = $('#hours').val()
        discamt = parseFloat($('#discount').val())
        if (!$.isNumeric(discamt)) {
            $('#discount').val(0)
            discamt = parseFloat(0)
        }

        if (!$('#override').is(':checked')) {
            //console.log("not checked")

            if (lmid != "none" && lmid != "") {
                // get the markup from the name

                if ($.isNumeric(hourlyrate) && $.isNumeric(hours)) {
                    tlabor = (hourlyrate * hours).toFixed(2)
                    discamt = (discamt / 100) * tlabor
                    $('#discountamt').val(discamt)
                    calclabor = tlabor - discamt

                    $.each(lm, function (i, v) {

                        if (v.category.toUpperCase() === lmid.toUpperCase() && v.start <= calclabor && v.end >= calclabor) {
                            calclabor = Math.round((calclabor * v.factor) * 100) / 100
                            $('#override').prop('checked', true)
                            return false
                        }
                    })

                    $('#total').val(calclabor.toFixed(2)).addClass("active")
                //    $('#discountfloatinglabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold").html("Discount Percent " + "<span id='discpercentlabel' style='color:red;margin-left:20px;'>(In Dollars $" + discamt.toFixed(2) + ")</span>")
                //    $('#totalfloatinglabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold")

                }


            } else {

                if ($.isNumeric(hourlyrate) && $.isNumeric(hours)) {

                    if ($.isNumeric(discamt)) {
                        tlabor = (hourlyrate * hours).toFixed(2)
                        discamt = (discamt / 100) * tlabor
                        $('#discountamt').val(discamt)
                        calclabor = tlabor - discamt
                        $('#total').val(calclabor.toFixed(2)).addClass("active")
                     //   $('#discountfloatinglabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold").html("Discount Percent " + "<span id='discpercentlabel' style='color:red;margin-left:20px;'>(In Dollars $" + discamt.toFixed(2) + ")</span>")
                    } else {
                        tlabor = (hourlyrate * hours).toFixed(2)
                        discamt = 0
                        calclabor = tlabor - discamt
                        $('#total').val(calclabor.toFixed(2)).addClass("active")
                    }
                }

            }
        } else if ($('#override').is(':checked') && lmid != "none" && lmid != "") {

            if ($.isNumeric(hourlyrate) && $.isNumeric(hours)) {
                tlabor = (hourlyrate * hours).toFixed(2)
                discamt = (discamt / 100) * tlabor
                $('#discountamt').val(discamt)
                calclabor = tlabor - discamt
                $.each(lm, function (i, v) {
                    if (v.category.toUpperCase() === lmid.toUpperCase() && v.start <= calclabor && v.end >= calclabor) {
                        calclabor = Math.round((calclabor * v.factor) * 100) / 100
                        $('#override').prop('checked', true)
                        return false
                    }
                })
                $('#total').val(calclabor.toFixed(2)).addClass("active")
            //    $('#discountfloatinglabel').css("-webkit-transform", "translateY(-24px)").css("-ms-transform", "translateY(-24px)").css("transform", "translateY(-24px)").css("font-size", "small").css("font-color", "gray").css("font-weight", "bold").html("Discount Percent " + "<span id='discpercentlabel' style='color:red;margin-left:20px;'>(In Dollars $" + discamt.toFixed(2) + ")</span>")

            }

        }

    }
</script>
<?php require_once(AI_WRITING_TOOL);?>
</body>
</html>
