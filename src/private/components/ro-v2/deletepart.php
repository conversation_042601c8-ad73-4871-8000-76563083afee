<?php
require CONN;

$shopid = $oshopid = filter_var($_POST['shopid'], FILTER_SANITIZE_STRING);
$roid = filter_var($_POST['roid'], FILTER_SANITIZE_STRING);
$comid = filter_var($_POST['comid'], FILTER_SANITIZE_STRING);
$partid = filter_var($_POST['partid'], FILTER_SANITIZE_STRING);

include_once "rostatus_check.php";

if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

$stmt = "select partnumber,quantity,allocated,invid from parts where shopid = '$shopid' and partid = $partid";
if ($query = $conn->prepare($stmt)){
    $query->execute();
	$query->bind_result($partnumber,$quantity,$allocated,$invid);
	$query->fetch();
    $query->close();
}else{
	echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}



$stmt = "delete from parts where partid = ? and complaintid = ? and roid = ? and shopid = ?";

if ($query = $conn->prepare($stmt)){
	$query->bind_param("iiis",$partid,$comid,$roid,$shopid);
	if ($query->execute()){
	}else{
		echo "error";
	}
	$conn->commit();
	recordAudit("Delete Part", "Deleted Part Number $partnumber from RO#$roid");
}else{
	echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "delete from rofees where itemid = ? and roid = ? and shopid = ?";

if ($query = $conn->prepare($stmt)){
	$query->bind_param("iis",$partid,$roid,$shopid);
	if ($query->execute()){
	}else{
		echo "error";
	}
	$conn->commit();
	recordAudit("Delete Part", "Deleted Part Number $partnumber from RO#$roid");
}else{
	echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "delete from cores where shopid = ? and roid = ? and partnumber = ? and returnstatus = 'not returned'";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("sis",$shopid,$roid,$partnumber);
    if ($query->execute()){
	    $conn->commit();
	    $query->close();
	}else{
		echo $conn->errno;
	}
	
}else{
	echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

if (strtoupper($allocated) != 'NON')
{

$stmt = "select upper(updateinvonadd) from company where shopid = ?";
if ($query = $conn->prepare($stmt)){

	$query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($updateinvonadd);	
    $query->fetch();
    $query->close();

}else{
	echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
}

if($updateinvonadd=='YES')
{
	if(strtoupper($allocated)=='NON')
	$utable = "partsregistry";
    else
    $utable = "partsinventory";

    $updateByPartnumber = true;

    if (!empty($invid)) 
    {
      $stmt = "select partid from {$utable} where shopid = ? and partid = ? and partnumber = ?";
      if ($query = $conn->prepare($stmt))
      {
        $query->bind_param("sis",$shopid,$invid,$partnumber);
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows();
        if ($numrows > 0)
        {
          $whereField = 'partid';
          $whereValue = $invid;
          $whereType = 'i';
          $updateByPartnumber = false;
        }
      }
	}
	
	if($updateByPartnumber)
	{
	 $whereField = 'partnumber';
	 $whereValue = $partnumber;
	 $whereType = 's';
	}

	$stmt = "update {$utable} set onhand = onhand + $quantity, netonhand = netonhand + $quantity where shopid = ? and {$whereField} = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s{$whereType}",$oshopid,$whereValue);
	    if ($query->execute()){
		    $conn->commit();
		    $query->close();
		}else{
			echo $conn->errno;
		}
		
	}else{
		echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
}
}


echo "success";
die();
?>



