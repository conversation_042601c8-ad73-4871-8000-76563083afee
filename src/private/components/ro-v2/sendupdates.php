<?php
require CONN;
require(INTEGRATIONS_PATH."/twilio/autoload.php");
use Twilio\Rest\Client;
require(PRIVATE_PATH."/integrations/mandrill/src/Mandrill.php");
require(COMPONENTS_PRIVATE_PATH."/shared/securitycheck.php");

$t = $_GET['t'];
$cell = $_GET['cell'];
$email = $toemail = $_GET['email'];
$shopid = $_GET['shopid'];
$roid = $_GET['roid'];
$send_attachment = isset($_GET['send_attachment']) ? filter_var($_GET['send_attachment'], FILTER_VALIDATE_BOOLEAN) : false;

$cell=str_replace(array('-','(',')',' '), '', $cell);
// get the sending shops sms number
$sendsmsnum = "";
$stmt = "select smsnum from smsnumbers where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)){
	$query->execute();
	$query->bind_result($sendsmsnum);
	$query->fetch();
	$query->close();
}

//echo $sendsmsnum;
$stmt = "select status,CellPhone,CustomerPhone,spousecell,spousework,customeraddress,customerstate,customercity,customerzip,customerfirst,customerlast,email,balance from repairorders where shopid = '$shopid' and roid = $roid";
if ($query = $conn->prepare($stmt)){
	$query->execute();
	$query->bind_result($rostat,$CellPhone,$CustomerPhone,$spousecell,$spousework,$customeraddress,$customerstate,$customercity,$customerzip,$customerfirst,$customerlast,$cusemail,$balancero);
	$query->fetch();
	$query->close();
}

$rostat = strtolower($rostat);

$stmt = "select companyname,companyphone,companyemail,repairstatusurl,cfpid from company where shopid = '$shopid'";

if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    if ($num_roid_rows > 0){
    	$query->bind_result($shopname,$shopphone,$shopemail,$rsu,$cfpid);
    	$query->fetch();
    }else{
    	echo "error";
    }
    $query->close();
}else{
	echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$shopphone = formatPhone($shopphone);

$stmt = "select sendprequal,showcfp,ccshop from settings where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)){
	$query->execute();
	$query->bind_result($sendprequal,$showcfp,$ccshop);
	$query->fetch();
	$query->close();
}


$cfpphone = $cfpaltphone = $cfplink = '';

if($showcfp=='yes' && !empty($cfpid) && $sendprequal=='yes')
{
	$phones = array($CellPhone,$CustomerPhone,$spousecell,$spousework);
	foreach($phones as $phone)
	{
		if(!empty($phone))
		{
			if(empty($cfpphone))$cfpphone = $phone;
			elseif($phone!=$cfpphone && empty($cfpaltphone))$cfpaltphone = $phone;
		}
	}

	$data = array("merchantId" => $cfpid, "phone" => $cfpphone,"altphone"=>$cfpaltphone, "zip" => $customerzip, "firstName" => $customerfirst, "lastName" => $customerlast, "street" => $customeraddress, "state" => $customerstate, "city" => $customercity, "email" => $cusemail, "middle" => '', 'amount' => $balancero);

    if (in_array($shopid, array('13846', '6062'))) {
        $data["partnerKey"] = "SBPS9teaU";
        $url = "https://p2-6182--partial.sandbox.my.site.com/360payments/services/apexrest/remotelend?" . http_build_query($data); //sandbox
    } else {
        $data["partnerKey"] = "Sb24F110";
        $url = "https://360-partners.force.com/360payments/services/apexrest/remotelend?" . http_build_query($data);
    }

    $response = makeCurlRequest($url,[],"GET",$encdata);

    $cfpresult = json_decode($response['response']);

    if (isset($cfpresult->applyUrl) && !empty($cfpresult->applyUrl)) {
        $href = $cfpresult->applyUrl;
        $cfplink = '<a href="' . $href . '">' . $cfpresult->result . ' Click to Apply</a>';
    }
}

if($ccshop=='yes')$email .= ';'.$shopemail;

// now check for DVI in companyadds
$stmt = "select name from companyadds where shopid = '$shopid' and name = 'DVI Boss'";
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    if ($num_roid_rows > 0){
    	$query->bind_result($dvi);
    	$query->fetch();
    }else{
    	$dvi = "none";
    }
    $query->close();
}else{
	echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}


if(stripos($_SERVER['SERVER_NAME'], 'staging') !== false)
{
	$qs = encryptData("shopid=$shopid&roid=$roid");
	$url = "https://staging.shopbosspro.com/status.php?$qs";
}
else
{
	$check=true;$ct=1;$rand='';
	while($check && $ct<=20)
	{
	$rand=generateRandomStr(5);
	$stmt = "select id from urlshort where url = ? limit 1";
	if ($query = $conn->prepare($stmt))
	{
	$query->bind_param("s",$rand);
	$query->execute();
	$query->store_result();
	$num = $query->num_rows();
	$query->close();
	if($num<1)$check=false;
	}
	$ct++;
	}

	if(empty($rand))die();


	$stmt = "Insert into `urlshort` set `shopid`=?,`roid`=?,`url`=?";
	if ($query = $conn->prepare($stmt))
	{
	$query->bind_param("sss",$shopid,$roid,$rand);
	$query->execute();
	$conn->commit();
	$query->close();
	}else echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;

	$url="https://sbpt.io/".$rand;
}



if ($t == "email"){

	$subject = "Vehicle repair update from $shopname";
	$message = "An update on your vehicle repair from $shopname.<br><br>";
	if ($rostat == "final"){
		$message .= "CURRENT STATUS IS FINAL.  This means work is complete and your vehicle should be ready.  Please contact the shop to make arrangements<br><br>";
	}
	$message .= "Please visit the following link to view pictures and updates on your vehicle repair"
	. " -  <a href='$url'>$url</a>"
	. "<br>If you have any questions, you can reply to this email or call us at $shopphone.<br><br>"
	. "Sincerely,<br><br> $shopname";

	if(!empty($cfplink))$message .= "<center>$cfplink</center>";

	$attachment = array();
	if ($send_attachment) {
		$invoice_path = file_get_contents(COMPONENTS_PUBLIC."/invoices/printpdfro.php?roid=$roid&shopid=$shopid");
		if (!empty($invoice_path)) {
			$invoice_contents = file_get_contents(COMPONENTS_PRIVATE . "/invoices" . $invoice_path);
			$attachment_name = ($rostat == "final" || $rostat == 'closed') ? "invoice.pdf" : "estimate.pdf";
			$attachment = array(
				'string' => $invoice_contents,
				'name' => $attachment_name
			);
		}
	}
	
	$res = sendEmailMandrill($email,$subject,$message,$shopname,$shopemail,$attachment);
	
	if(empty($res)) {
	    echo 'Message could not be sent.';
	}else{
	    echo 'success';
	    recordAudit("EMail Sent", "Send Update: An Email Update was sent to $toemail for RO#$roid");
	}

}else if ($t == "text"){

	if ($sendsmsnum == ""){

		if ($rostat == "final"){
			$txtmsg = "CURRENT STATUS IS FINAL.  This means work is complete and your vehicle should be ready.  Please contact the shop to make arrangements";
		}elseif ($rostat == "closed") {
			$txtmsg = "";
		}else{
			$txtmsg = "and may require approval for work to begin";
		}

		$mb =  "Information on your vehicle repair from $shopname.  $url";
		// $mb = "An update on your vehicle repair from $shopname is available  $txtmsg. Please click the link listed below for more information.\r\n "
		// . $url . ". For questions please contact us at $shopphone. \r\n** Please Do Not Reply to this text as the phone number is not monitored **";

		// Your Account SID and Auth Token from twilio.com/console
		$sid = '**********************************';
		$token = 'f519be94a23d1968e0a1c89ea7030589';
		$client = new Client($sid, $token);

		// Use the client to do fun stuff like send text messages!
		$client->messages->create(
		    // the number you'd like to send the message to
		    '+1'.$cell,
		    array(
		        // A Twilio phone number you purchased at twilio.com/console
		        'from' => '+***********',
		        // the body of the text message you'd like to send
		        'body' => $mb
		    )
		);
		recordAudit("Text Message Sent", "Send Update: An Text Message Update was sent to $cell for RO#$roid");

	}else{

		$token = "t-ewgnokfkia545y4zn4xzxdi";
		$secret = "zb2lcrqeouxma52dhgjfpqak6w5dg4tfnwvytxy";

		if ($rostat == "final"){
			$txtmsg = "CURRENT STATUS IS FINAL.  This means work is complete and your vehicle should be ready.  Please contact the shop to make arrangements";
		}elseif ($rostat == "closed") {
			$txtmsg = "";
		}else{
			$txtmsg = "and may require approval for work to begin ";
		}

		$mb =  "From $shopname at $sendsmsnum: Information update on your vehicle repair.  $url";
		// $mb = $rsurl . " An update on your vehicle repair from $shopname is available  $txtmsg. Please click the link above for more information. For questions please contact us at $shopphone or reply to this text";

		require(PRIVATE_PATH."/integrations/bandwidth/sendsmsv2.php");
	    SendSmsV2($sendsmsnum,$cell,$mb);


		$from = $sendsmsnum;
		$to = $cell;
		//$roid = $_POST['roid'];
		$msg = $mb;
		$usr = $_COOKIE['usr'];

		$shopname = $_COOKIE['shopname'];
		$ts = localTimeStamp($shopid);

		// add to the sms table
		$stmt = "insert into sms (`from`,name,msg,ts,shopid,markread,roid) values (?,?,?,?,?,'yes',?)";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sssssi",$to,$usr,$msg,$ts,$shopid,$roid);
		    $query->execute();
		    $conn->commit();
		    $query->close();
		}else{
			echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$commmb = "Sent via Text Message: ".$mb;
		$stmt = "insert into repairordercommhistory (shopid,roid,`datetime`,comm,`by`) values (?,?,?,?,?)";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sisss",$shopid,$roid,$ts,$commmb,$usr);
		    $query->execute();
		    $conn->commit();
		    $query->close();
		}else{
			echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

	}

	echo "success";

}else if ($t == "both"){

	if ($sendsmsnum == ""){

		if ($rostat == "final"){
			$txtmsg = ".CURRENT STATUS IS FINAL.  This means work is complete and your vehicle should be ready.  Please contact the shop to make arrangements";
		}elseif ($rostat == "closed") {
			$txtmsg = "";
		}else{
			$txtmsg = "and may require approval for work to begin";
		}

		// $mb = "An update on your vehicle repair from $shopname is available $txtmsg. Please click the link listed below for more information.\r\n"
		// . $url . ". For questions please contact us at $shopphone. \r\n** Please Do Not Reply to this text as the phone number is not monitored **";
		$mb =  "Information on your vehicle repair from $shopname.  $url";

		// Your Account SID and Auth Token from twilio.com/console
		$sid = '**********************************';
		$token = 'f519be94a23d1968e0a1c89ea7030589';
		$client = new Client($sid, $token);

		// Use the client to do fun stuff like send text messages!
		$client->messages->create(
		    // the number you'd like to send the message to
		    '+1'.$cell,
		    array(
		        // A Twilio phone number you purchased at twilio.com/console
		        'from' => '+***********',
		        // the body of the text message you'd like to send
		        'body' => $mb
		    )
		);
		recordAudit("Text Message Sent", "Send Update: An Text Message Update was sent to $cell for RO#$roid");

	}else{

		$token = "t-ewgnokfkia545y4zn4xzxdi";
		$secret = "zb2lcrqeouxma52dhgjfpqak6w5dg4tfnwvytxy";

		if ($rostat == "final"){
			$txtmsg = ".CURRENT STATUS IS FINAL. This means work is complete and your vehicle should be ready.  Please contact the shop to make arrangements";
		}elseif ($rostat == "closed") {
			$txtmsg = "";
		}else{
			$txtmsg = "and may require approval for work to begin";
		}

		$mb =  "From $shopname at $sendsmsnum: Information update on your vehicle repair.  $url";
		// $mb = $url . ". An update on your vehicle repair from $shopname is available $txtmsg. Please click the link above for more information. For questions please contact us at $shopphone or reply to this text";

		require(PRIVATE_PATH."/integrations/bandwidth/sendsmsv2.php");
	    SendSmsV2($sendsmsnum,$cell,$mb);

		$from = $sendsmsnum;
		$to = $cell;
		//$roid = $_POST['roid'];
		$msg = $mb;
		$usr = $_COOKIE['usr'];

		$shopname = $_COOKIE['shopname'];
		$ts = localTimeStamp($shopid);

		// add to the sms table
		$stmt = "insert into sms (`from`,name,msg,ts,shopid,markread,roid) values (?,?,?,?,?,'yes',?)";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sssssi",$to,$usr,$msg,$ts,$shopid,$roid);
		    $query->execute();
		    $conn->commit();
		    $query->close();
		}else{
			echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$commmb = "Sent via Text Message: ".$mb;
		$stmt = "insert into repairordercommhistory (shopid,roid,`datetime`,comm,`by`) values (?,?,?,?,?)";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sisss",$shopid,$roid,$ts,$commmb,$usr);
		    $query->execute();
		    $conn->commit();
		    $query->close();
		}else{
			echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

	}

	//echo "success";
	$message = "";

	recordAudit("Text Message Sent", "Send Update: An Text Message Update was sent to $cell for RO#$roid");

	//echo $shopemail.":".$shopname;
	$subject = "Vehicle repair update from $shopname";
	if ($rostat == "final"){
		$txtmsg = "CURRENT STATUS IS FINAL.  This means work is complete and your vehicle should be ready.  Please contact the shop to make arrangements";
	}
	$message .= "Please visit the following link to view pictures and updates on your vehicle repair"
	. " -  <a href='$url'>$url</a>"
	. "<br>If you have any questions, you can reply to this email or call us at $shopphone.<br><br>"
	. "Sincerely,<br><br> $shopname";

	if(!empty($cfplink))$message .= "<center>$cfplink</center>";

	$attachment = array();
	if ($send_attachment) {
		$invoice_path = file_get_contents(COMPONENTS_PUBLIC."/invoices/printpdfro.php?roid=$roid&shopid=$shopid");
		if (!empty($invoice_path)) {
			$invoice_contents = file_get_contents(COMPONENTS_PRIVATE . "/invoices" . $invoice_path);
			$attachment_name = ($rostat == "final" || $rostat == 'closed') ? "invoice.pdf" : "estimate.pdf";
			$attachment = array(
				'string' => $invoice_contents,
				'name' => $attachment_name
			);
		}
	}

$res = sendEmailMandrill($email,$subject,$message,$shopname,$shopemail, $attachment);
	
	if(empty($res)) {
	    echo 'Message could not be sent.';
	}else{
	    echo 'success';
	    recordAudit("EMail Sent", "Send Update: An Email Update was sent to $toemail for RO#$roid");
	}


}



?>
<?php if(isset($conn)){mysqli_close($conn);} ?>
