			<li class="sidenav-item sidenav-selected">
					<a class="sidenav-link ripple-surface-primary" href="/v2/wip/wip.php">
						<i class="fas fa-long-arrow-alt-left"></i>
						<span class="ms-2"><?php if($Status!='CLOSED'){?>Save and <?php }?>Exit</span>
					</a>
				</li>
				<div class=""></div>

				<?php if($_COOKIE['mode'] == 'full'){?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" href="javascript:void(null)" onclick="showPrintedROs()">
						<i class="fas fa-print"></i>
						<span class="ms-2">Print</span>
					</a>

					<ul class="sidenav-collapse printedros">
						<?php if($Status!='CLOSED'){?>
						<li class="sidenav-item" id="sigfirst" style="display:none">
                    		<a class="sidenav-link" title='Get E-Signature' onclick="getEsigFirst()" href="javascript:void(null)"><i class="fa fa-xxs fa-circle"></i> Get E-Signature </a>
                    	</li>
                        <?php }?>

                        <li class="sidenav-item">
                            <a class="sidenav-link" title='Print the Repair Order' onclick="printRO()" href="javascript:void(null)"><i class="fa fa-xxs fa-circle"></i> Print </a>
                        </li>

                        <li class="sidenav-item">
                            <a class="sidenav-link" title='Print a Work Order for your customer' onclick="printWO()" href="javascript:void(null)"><i class="fa fa-xxs fa-circle"></i> Print Tech Workorder</a>
                        </li>
                        <?php if($oilchangestickers == 'yes'){?>
						<li class="sidenav-item">
                            <a class="sidenav-link" title='Print a Oil Sticker your customer' onclick="showOilChangeSticker()" href="javascript:void(null)"><i class="fa fa-xxs fa-circle"></i> Print Oil Sticker</a>
                        </li>
                        <?php }?>
                        <?php if($Status!='CLOSED'){?>
                        <li class="sidenav-item">
                            <a class="sidenav-link" title='Print a Parts Pull Sheet' onclick="pullSheet()" href="javascript:void(null)"><i class="fa fa-xxs fa-circle"></i> Parts Pull Sheet </a>
                        </li>
                        <?php
                        if ($shopid == "11867"){
                        ?>
                        <li class="sidenav-item">
                            <a class="sidenav-link" title='Print a Vehicle Release' onclick="printVehRelease()" href="javascript:void(null)"><i class="fa fa-xxs fa-circle"></i> Vehicle Release Form </a>
                        </li>
                        <?php
                        }
                        ?>
                        <li class="sidenav-item">
                            <a class="sidenav-link" title='Print the Repair Order' onclick="$('#esigrequestmodal').modal('show')" href="javascript:void(null)"><i class="fa fa-xxs fa-circle"></i> Remote E-Signature </a>
                        </li>
                        <?php }?>

                        <?php $path = "\\\\fs.shopboss.aws\\share\\savedinvoices/".$shopid."/".$roid;

						if (is_dir($path)){

							//echo "<li class='sidenav-item'><a class='sidenav-link ms-4' style='color:var(--primary)' href='javascript:void(null)'>Printed RO's</a></li>";
							$files = scandir($path,1);
							$files = array_diff(scandir($path), array('.', '..'));
							
							foreach ($files as $a => $b){
								$filepath = SBP."savedinvoices/$shopid/$roid/".$b;
								$datecreated = date("m/d/Y H:i", filectime($path."/".$b));
								if (substr($b,-3) == "pdf"){
									echo "<li class='pro sidenav-item'><a class='pro sidenav-link' onclick='showPrintedRO(\"$filepath\")' href='javascript:void(null)'><i class='fas fa-arrow-right'></i>$datecreated</a></li>";
								}
							}
							
						}?>

				  	</ul>

				</li>
				<?php
			    }
                if($dvilite=='yes'){

                 if($Status!='CLOSED')
                 {
                ?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" href="javascript:void(null)" title='Create / Edit an Inspection' onclick="showInspection('')">
						<i class="fas fa-clipboard-check"></i>
						<span class="ms-2">DVI Lite</span>
					</a>
				</li>
			    <?php }
			    else
			    {
			    $cistmt = "select distinct inspectionname from roinspection where shopid = ? and roid = ? order by id asc";
					if ($ci_query = $conn->prepare($cistmt)) {
						$ci_query->bind_param("ss", $shopid, $roid);
						$ci_query->execute();
						$tmp_result = $ci_query->get_result();
						$ci_query->close();
					}
					while($curr_inspection = $tmp_result->fetch_assoc())
					{
						$curr_inspection_link="https://". $_SERVER['SERVER_NAME'] ."/inspections/inspection.php?id=" . $curr_inspection["id"];
						?>
						<li class="sidenav-item">
						  <a class="sidenav-link ripple-surface-primary" title='Current Inspections' href="javascript:void(null)" onclick="showInspection('<?= addslashes($curr_inspection['inspectionname'])?>')">
							<i class="fas fa-stethoscope "></i>
							<span class="ms-2"><?= $curr_inspection['inspectionname']?></span>
						  </a>
				        </li>
				<?php }
			    }
			    }

                if($Status!='CLOSED'){
                ?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='New Inspection' target="_blank" href="https://<?= $_SERVER['SERVER_NAME'] ?>/inspections/new_inspection.php?roid=<?= $roid ?>">
						<i class="fas fa-user-md"></i>
						<span class="ms-2">BOSS Inspect</span>
					</a>
				</li>
				<?php
				}
					$cistmt = "SELECT dvi.id,m.name FROM dvi,inspection_models m where dvi.shopid=m.shopid and dvi.model_id=m.id and dvi.shopid = ? and dvi.roid = ? and dvi.deleted='no' order by dvi.id desc";
					if ($ci_query = $conn->prepare($cistmt)) {
						$ci_query->bind_param("ss", $shopid, $roid);
						$ci_query->execute();
						$tmp_result = $ci_query->get_result();
						$ci_query->close();
					}
					if ($tmp_result->num_rows > 0) {
						if ($tmp_result->num_rows == 1) {
							$curr_inspection = $tmp_result->fetch_assoc();
							$curr_inspection_link="https://". $_SERVER['SERVER_NAME'] ."/inspections/inspection.php?id=" . $curr_inspection["id"];
							?>
							<li class="sidenav-item">
							  <a class="sidenav-link ripple-surface-primary" title='Current Inspections' href="<?= $curr_inspection_link ?>" target="_blank">
								<i class="fas fa-stethoscope "></i>
								<span class="ms-2">Current Inspection</span>
							  </a>
					        </li>
							<?php
						} else {
							echo('<li class="sidenav-item"><a class="sidenav-link ripple-surface-primary" href="javascript:void(null)"><i class="fas fa-file-text"></i><span class="ms-2">Current Inspections</span></a><ul class="sidenav-collapse">');
							while($curr_inspection = $tmp_result->fetch_assoc())
							{
							 $curr_inspection_link="https://". $_SERVER['SERVER_NAME'] ."/inspections/inspection.php?id=" . $curr_inspection["id"];
							 ?>
							 <li class="sidenav-item">
                              <a class="sidenav-link" href="<?= $curr_inspection_link ?>" target="_blank"><i class="fa fa-xxs fa-circle"></i><?= $curr_inspection['name'] ?></a>
						     </li>
							 <?php
							}
							echo("</ul></li>");
						}
				?>

				<?php } ?>

				<?php if(!empty($matco_records))
				{
				    if (count($matco_records) == 1) {
					?>
						<li class="sidenav-item">
						<a class="sidenav-link ripple-surface-primary" onclick="showMatcoReport('<?= $matco_records[0]['id']?>')" href="javascript:void(null)"><i class="fas fa-file-pdf"></i><span class="ms-2">Scan Tool Report</span></a>
				        </li>
						<?php
					} else {
						echo('<li class="sidenav-item"><a class="sidenav-link ripple-surface-primary" href="javascript:void(null)"><i class="fas fa-file-text"></i><span class="ms-2">Scan Tool Reports</span></a><ul class="sidenav-collapse">');
						foreach($matco_records as $matco_record)
						{
						 ?>
						 <li class="sidenav-item">
                          <a class="sidenav-link" onclick="showMatcoReport('<?= $matco_records[0]['id']?>')" href="javascript:void(null)"><i class="fa fa-xxs fa-circle"></i><?= $matco_record['type']?></a>
					     </li>
						 <?php
						}
						echo("</ul></li>");
					}
				}?>

				<?php if($viewhistory == 'YES'){?>

				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='View Repair History on this Vehicle' onclick="showHistory()" href="javascript:void(null)">
						<i class="fas fa-car-side"></i>
						<span class="ms-2">History</span>
					</a>
				</li>
				
				<?php
			    }
			    
                if($_COOKIE['mode'] == 'full' && $Status!='CLOSED'){
                ?>

				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='View Repair History Similar Vehicles' onclick="vehicleSearch()" href="javascript:void(null)">
						<i class="fab fa-searchengin"></i>
						<span class="ms-2">Repair History Search</span>
					</a>
				</li>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='Assign all labor lines to a specific technician' data-mdb-toggle="modal" data-mdb-target="#assigntechmodal" href="javascript:void(null)">
						<i class="fas fa-address-card"></i>
						<span class="ms-2">Assign Tech</span>
					</a>
				</li>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='Assign an hourly rate to all labor lines' data-mdb-toggle="modal" data-mdb-target="#hourlyratemodal" href="javascript:void(null)">
						<i class="fas fa-money-bill-wave-alt"></i>
						<span class="ms-2">Change Labor Rate</span>
					</a>
				</li>
				<?php
                } 
                ?>

				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" onclick="showUploads()" title='Upload images of the work performed' href="javascript:void(null)">
						<i class="fas fa-images"></i>
						<span class="ms-2 <?= !empty($picscount)?'text-primary':''?>">Upload<?= $Status=='CLOSED'?'ed':''?> Pics </span>
                        <span class="position-static translate-middle badge rounded-pill" id="ropics_badge"><?= !empty($picscount)? $picscount: "" ?></span>
					</a>
				</li>
				<?php if ($Status!='CLOSED'){

				if($_COOKIE['mode'] == 'full'){ 

				if(empty(trim($schdate))){?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='Send this RO to Calendar' onclick="calendarSet()" href="javascript:void(null)">
						<i class="fas fa-calendar-plus"></i>
						<span class="ms-2">Send to Calendar</span>
					</a>
				</li>
				<?php }else{?>
					<li class="sidenav-item">
					   <a class="sidenav-link ripple-surface-primary" title='' data-mdb-toggle="modal" data-mdb-target="#calendarmodal" href="javascript:void(null)">
						<i class="fas fa-calendar-plus"></i>
						<span class="ms-2 text-primary"><?php echo date("m/d/Y h:i A",strtotime($schdate));?></span>
					   </a>
					</li>
				<?php }}?>

				<?php if ($_COOKIE['mode'] == 'full'){?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='Issue a PO to a vendor for this RO' onclick="managePO()" href="javascript:void(null)">
						<i class="fas fa-money-check-alt"></i>
						<span class="ms-2">Issue PO</span>
					</a>
				</li>
				<?php }
				if(!empty($pdusername) && ($newpackagetype=='gold' || $newpackagetype=='platinum' || $newpackagetype=='premier')){
                 if($pdtype!='3'){?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='ProDemand' onclick="loadProDemand('<?= $pdtype?>')" href="javascript:void(null)">
						<i class="fas fa-suitcase"></i>
						<span class="ms-2">ProDemand</span>
					</a>
				</li>
				<?php }else{?>
                <li class="sidenav-item"><a class="sidenav-link ripple-surface-primary" href="javascript:void(null)"><i class="fas fa-suitcase"></i><span class="ms-2">ProDemand</span></a>
                	<ul class="sidenav-collapse">
                    <li class="sidenav-item">
                        <a class="sidenav-link" title='ProDemand for Light Duty Vehicles' onclick="loadProDemand('1')" href="javascript:void(null)"><i class="fa fa-xxs fa-circle"></i>Light Duty</a>
                    </li>
                    <li class="sidenav-item">
                        <a class="sidenav-link" title='ProDemand for Heavy Duty Vehicles' onclick="loadProDemand('2')" href="javascript:void(null)"><i class="fa fa-xxs fa-circle"></i>Heavy Duty</a>
                    </li>
                    </ul>
                </li>

                <?php }}?>

                <?php if ($_COOKIE['mode'] == 'full'){?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='Post all parts to Shop Boss Accounting. Not applicable for QB integration' onclick="postAllParts()" href="javascript:void(null)">
						<i class="fas fa-share-square"></i>
						<span class="ms-2">Post All Parts</span>
					</a>
				</li>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='Setup Service Reminders for this Customer / Vehicle' onclick="showReminders()" href="javascript:void(null)">
						<i class="far fa-bell"></i>
						<span class="ms-2">Service Reminders</span>
					</a>
				</li>
				<?php
				}
			    }

				if($_COOKIE['mode'] == 'full' && $sendupdates=='YES'){?>
				<li class="sidenav-item">
				  <a class="sidenav-link ripple-surface-primary" title='Send an Update Link to your customer via Email or Text Message' onclick="showSendUpdate()" href="javascript:void(null)">
					  <i class="fas fa-paper-plane"></i>
					  <span class="ms-2">Send Update</span>
					</a>
				</li>
			    <?php }?>

			    <?php if($_COOKIE['mode'] == 'full' && $Status!='CLOSED'){?>

				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='Add / Edit / ReOrder the Vehicle Issues' onclick="showIssues()" href="javascript:void(null)">
					  <i class="fas fa-car-crash"></i>
					  <span class="ms-2">Vehicle Issues</span>
					</a>
				</li>

				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='Add / Edit / ReOrder the Vehicle Issues' onclick="showMassChange()" href="javascript:void(null)">
					  <i class="fas fa-regular fa-list-dropdown"></i>
					  <span class="ms-2">Mass Status Change</span>
					</a>
				</li>
                    <?php } ?>

                <?php if ($_COOKIE['mode'] == 'full'){?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='Add / Restore Recommended Repairs to this RO' onclick="showRecRepairs()" href="javascript:void(null)">
					  <i class="fas fa-tools"></i>
					  <span class="ms-2">Recommended Repairs</span>
					</a>
				</li>
				<?php
				if($Status=='CLOSED' && strtolower($reopenro) == 'yes'){
					?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='Re-Open this RO' onclick="$('#reopenmodal').modal('show')" href="javascript:void(null)">
					  <i class="fas fa-door-open"></i>
					  <span class="ms-2">Re-Open RO</span>
					</a>
				</li>
				<?php
				}}

				 if($Status!='CLOSED'){
                if ($readonly == "no" && $partsordering == 'YES' && ($newpackagetype != 'silver' || ($newpackagetype == 'silver' && strtotime($datestarted) < strtotime('2022-06-09')))){
                ?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='Place an electronic order for parts with Epicor / Nexpart / Worldpac' href="javascript:void(null)" onclick="$('#partsordering').modal('show')">
					  <i class="fas fa-cart-plus"></i>
					  <span class="ms-2">Parts Ordering</span>
					</a>
				</li>
			    <?php }
			    if ($_COOKIE['mode'] == 'full'){?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" onclick="niSuppliers()" href="javascript:void(null)">
					  <i class="fas fa-luggage-cart"></i>
					  <span class="ms-2">External Parts Ordering</span>
					</a>
				</li>
				<?php
			    }
                if ($_COOKIE['mode'] == 'full' && $readonly == "no"){
                ?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='Retrieve Scheduled Maintenance for this vehicle (requires a valid VIN number)' onclick="getMaint()" href="javascript:void(null)">
					  <i class="fas fa-calendar-alt"></i>
					  <span class="ms-2">Scheduled Maintenance</span>
					</a>
				</li>
			    <?php }?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" title='Retrieve Quick Lube Info' onclick="getQuickLube()" href="javascript:void(null)">
					  <i class="fas fa-oil-can"></i>
					  <span class="ms-2">Lube Info</span>
					</a>
				</li>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" onclick="cfServiceHistory()" href="javascript:void(null)">
					  <i class="fas fa-certificate"></i>
					  <span class="ms-2">Carfax Svc History</span>
					</a>
				</li>
				<?php if ($_COOKIE['mode'] == 'full'){?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" onclick="showSuppliers()" href="javascript:void(null)">
					  <i class="fas fa-truck"></i>
					  <span class="ms-2">Supplier List</span>
					</a>
				</li>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" onclick="$('#mvrmodal').modal('show')" href="javascript:void(null)" style="color:var(--primary);">
					  <i class="fas fa-hammer"></i>
					  <span class="ms-2">MyVehicleRepairs.net</span>
					</a>
				</li>
				<?php
			    }
                if ($readonly == "no"){
                ?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" onclick="openTPMS()" href="javascript:void(null)">
					  <i class="fas fa-wind"></i>
					  <span class="ms-2">TPMS Speed</span>
					</a>
				</li>
			    <?php }?>

			    <?php if ($_COOKIE['mode'] == 'full'){?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" onclick="importEMS()" href="javascript:void(null)">
					  <i class="fas fa-file-import"></i>
					  <span class="ms-2">Import EMS</span>
					</a>
				</li>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/customer/customer-search.php?droid=<?= $roid?>">
					  <i class="fas fa-clone"></i>
					  <span class="ms-2">Duplicate RO</span>
					</a>
				</li>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" href="javascript:void(null)" onclick="transferVehicle()">
					  <i class="fas fa-exchange"></i>
					  <span class="ms-2">Transfer Vehicle</span>
					</a>
				</li>
				<?php if($edittechpaidlog=='YES'){?>
				<li class="sidenav-item">
					<a class="sidenav-link ripple-surface-primary" href="javascript:void(null)" onclick="showtechpaidlogs()">
					  <i class="fas fa-dollar-sign"></i>
					  <span class="ms-2">Tech Paid Log</span>
					</a>
				</li>
				<?php }?>

				<?php }} ?>