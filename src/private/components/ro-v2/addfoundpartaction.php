<?php
require CONN;
// add the part

$shopid = $oshopid = filter_var($_POST['shopid'], FILTER_SANITIZE_STRING);
$roid = filter_var($_POST['roid'], FILTER_SANITIZE_STRING);
$comid = filter_var($_POST['comid'], FILTER_SANITIZE_STRING);
$partnumber = filter_var($_POST['PartNumber'], FILTER_SANITIZE_STRING);
$description = filter_var($_POST['PartDesc'], FILTER_SANITIZE_STRING);
$category = filter_var($_POST['PartCategory'], FILTER_SANITIZE_STRING);
$override = filter_var($_POST['overridematrix'], FILTER_SANITIZE_STRING);
$quantity = filter_var($_POST['qty'], FILTER_SANITIZE_STRING);
$cost = filter_var($_POST['PartCost'], FILTER_SANITIZE_STRING);
$price = filter_var($_POST['PartPrice'], FILTER_SANITIZE_STRING);
$discount = filter_var($_POST['Discount'], FILTER_SANITIZE_STRING);
$discountamt = filter_var($_POST['discountamt'], FILTER_SANITIZE_STRING);
$net = filter_var($_POST['net'], FILTER_SANITIZE_STRING);
$partcode = filter_var($_POST['PartCode'], FILTER_SANITIZE_STRING);
$supplier = filter_var($_POST['PartSupplier'], FILTER_SANITIZE_STRING);
$bin = filter_var($_POST['bin'], FILTER_SANITIZE_STRING);
$extcost = filter_var($_POST['extcost'], FILTER_SANITIZE_STRING);
$extprice = filter_var($_POST['extprice'], FILTER_SANITIZE_STRING);
$core = filter_var($_POST['core'], FILTER_SANITIZE_STRING);
$taxable = filter_var($_POST['tax'], FILTER_SANITIZE_STRING);
$updateinvonadd = filter_var($_POST['updateinv'], FILTER_SANITIZE_STRING);
$salesperson = filter_var($_POST['salesperson'], FILTER_SANITIZE_STRING);
$invnum = filter_var($_POST['invoicenumber'], FILTER_SANITIZE_STRING);
$invtype = filter_var($_POST['invtype'], FILTER_SANITIZE_STRING);
$ponumber = filter_var($_POST['ponum'], FILTER_SANITIZE_STRING);
$cantaxes = $_POST['cantaxes']??'';
$invid = $_POST['invid'] ?? '0';

if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

include_once "rostatus_check.php";

if($taxable=='no')$cantaxes = '';
elseif(!empty($cantaxes))
$cantaxes = str_replace('+',',',str_replace('ST','',$cantaxes));

$pstatus = " ";
$deleted = "no";
$pdate = date('Y-m-d');
$returnstatus = 'Not Returned';
$updateinvprices = '';

$stmt = "select updateinvprices,showpartfeeaspart from settings where shopid = ?";
if ($query = $conn->prepare($stmt)){
 $query->bind_param("s",$oshopid);
 $query->execute();
 $query->bind_result($updateinvprices,$showpartfeeaspart);
 $query->fetch();
 $query->close();
}

$stmt = "insert into parts (`salesperson`,`shopid`,`PartNumber`,`PartDesc`,`PartPrice`,`Quantity`,`ROID`,`Supplier`,`Cost`,`PartInvoiceNumber`,`PartCode`,`LineTTLPrice`,`LineTTLCost`,`Date`,`PartCategory`,`complaintid`,`discount`,`net`,`bin`,`tax`,`cantaxes`,`overridematrix`,`ponumber`,`pstatus`,`deleted`,allocated,invid) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("ssssddsssssdsssddsssssssssi",$salesperson,$shopid,$partnumber,$description,$price,$quantity,$roid,$supplier,$cost,$invnum,$partcode,$extprice,$extcost,$pdate,$category,$comid,$discount,$net,$bin,$taxable,$cantaxes,$override,$ponumber,$pstatus,$deleted,$invtype,$invid);
    if ($query->execute()){
    	$partid = $conn->insert_id;
	    $conn->commit();
	    $query->close();
	    //echo "success";
	}else{
		echo $conn->errno;
	}

}else{
	echo "Insert Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

if ($core > 0 )
{

	$stmt = "insert into cores (`shopid`,`PartNumber`,`PartDesc`,`corecharge`,supplier,partorderdate,returnstatus,roid) values (?,?,?,?,?,?,?,?)";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("sssdsssi",$shopid,$partnumber,$description,$core,$supplier,$pdate,$returnstatus,$roid);
		$query->execute();
		$conn->commit();
	}else{
		echo "Core Insert Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

}

// check for any fees attached to this item
// check for qtyflag
$stmt = "select additionalfeename,addfeetaxable,addfeeamt,addfeepercentordollar,qtyflag from partsinventoryfees where shopid = ? and partnumber = ?";
if ($query = $conn->prepare($stmt)){

	$query->bind_param("ss",$oshopid,$partnumber);
	$query->execute();
	$r = $query->get_result();
	while ($rs = $r->fetch_assoc()){

		$feepartnumber = "PARTFEE";
		$qtyflag = $rs['qtyflag'];


		$feedesc = strtoupper($rs['additionalfeename']);
		$feetype = $rs['addfeepercentordollar'];

		// adding quantity to the calculation
		// if yes than multiply by quantity
		// use quantity for calculation

		if ($qtyflag == 'yes') {
			$feeqty = $quantity;
		}else{
			$feeqty = 1;
		}

		if (strtolower($feetype) == "dollar"){
			if ($qtyflag == 'yes') {
				$feeamt = $rs['addfeeamt'] ;
				$linettlprice = ($rs['addfeeamt'] * $quantity) ;
				$linettlcost = $linettlprice;
			}else{
				$feeamt = $rs['addfeeamt'] ;
				$linettlprice = $rs['addfeeamt'] ;
				$linettlcost = $linettlprice;
			}

		}elseif (strtolower($feetype) == "percent"){
			if ($qtyflag == 'yes') {
				$feeamt = sbpround(($price * ($rs['addfeeamt'] / 100)),2);
				$linettlprice = $feeamt * $quantity;
				$linettlcost = $feeamt * $quantity;
			}else{
				$feeamt = sbpround(($price * ($rs['addfeeamt'] / 100)),2);
				$linettlprice = $feeamt ;
				$linettlcost = $feeamt;

			}
		}
		$feesupp = "NONE";


		$feecat = "PARTFEE";
		$feedisc = 0;
		$feetax = $rs['addfeetaxable'];
		$feeor = "yes";

		if($showpartfeeaspart == 'yes')
        {
		// add the fees to the parts table
		$stmt = "insert into parts (`shopid`,`PartNumber`,`PartDesc`,`PartPrice`,`Quantity`,`ROID`,`Supplier`,`Cost`,`LineTTLPrice`,`LineTTLCost`,`Date`,`PartCategory`,`complaintid`,`discount`,`net`,`tax`,`cantaxes`,`overridematrix`,`pstatus`,`deleted`) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sssddisdddssiddsssss",$shopid,$feepartnumber,$feedesc,$feeamt,$feeqty,$roid,$feesupp,$feeamt,$linettlprice,$linettlcost,$pdate,$feecat,$comid,$feedisc,$feeamt,$feetax,$cantaxes,$feeor,$pstatus,$deleted);
		    if ($query->execute()){
			    $conn->commit();
			    $query->close();
			}else{
				echo $conn->errno;
			}

		}

		}
		else
		{
			
			$stmt = "insert into rofees (`shopid`,`roid`,`itemid`,`feename`,`feeamount`,`taxable`) values (?,?,?,?,?,?)";

			if ($query = $conn->prepare($stmt)){
				$query->bind_param("sddsds",$shopid,$roid,$partid,$feedesc,$linettlprice,$feetax);
			    $query->execute();
			    $conn->commit();
			    $query->close();
			}

		}

	}

}else{
	echo $conn->error;
}


if (strtolower($updateinvonadd) == "yes" || strtolower($updateinvprices) == "yes"){

	if($invtype=='INV')
	$utable = "partsinventory";
    elseif ($shopid == '1238' || $shopid == "2703")
    $utable = "`partsregistry-$shopid`";
    else
    $utable = "partsregistry";

    $updateByPartnumber = true;

    if (!empty($invid)) 
    {
      $stmt = "select partid from {$utable} where shopid = ? and partid = ? and partnumber = ?";
      if ($query = $conn->prepare($stmt))
      {
        $query->bind_param("sis",$shopid,$invid,$partnumber);
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows();
        if ($numrows > 0)
        {
          $whereField = 'partid';
          $whereValue = $invid;
          $whereType = 'i';
          $updateByPartnumber = false;
        }
      }
	}
	
	if($updateByPartnumber)
	{
	 $whereField = 'partnumber';
	 $whereValue = $partnumber;
	 $whereType = 's';
	}

	
	if (strtolower($updateinvonadd) == "yes")
	$stmt = "update {$utable} set onhand = onhand - $quantity, netonhand = netonhand - $quantity, overridematrix = ? where shopid = ? and {$whereField} = ?";
	elseif(strtolower($updateinvprices)=='yes')
	$stmt = "update {$utable} set overridematrix = ?, partprice = ?, partcost = ? where shopid = ? and {$whereField} = ?";

	if ($query = $conn->prepare($stmt))
	{
		if (strtolower($updateinvonadd) == "yes")
		$query->bind_param("ss{$whereType}",$override,$shopid,$whereValue);
	    elseif(strtolower($updateinvprices)=='yes')
	    $query->bind_param("sdds{$whereType}",$override,$price,$cost,$shopid,$whereValue);

	    $query->execute();
		$conn->commit();
		$query->close();
	}

	if(strtolower($updateinvprices)=='yes')
	{
		if ($shopid == '1238' || $shopid == "2703")
		$utable = "`partsregistry-$shopid`";
        else
        $utable = "partsregistry";

		$stmt = "update {$utable} set overridematrix = ?, partprice = ?, partcost = ? where shopid = ? and {$whereField} = ?";
		if ($query = $conn->prepare($stmt))
	    { 
	     $query->bind_param("sdds{$whereType}",$override,$price,$cost,$shopid,$whereValue);
	     $query->execute();
		 $conn->commit();
		 $query->close();
	    }
	}

	if($invtype=='INV')
	{
		$stmt = "select NetOnHand,ReOrderLevel from partsinventory where shopid = ? and partnumber = ?";
        if ($query = $conn->prepare($stmt))
        {
	     $query->bind_param("ss",$oshopid,$partnumber);
	     $query->execute();
	     $query->bind_result($netonhand,$reorderlevel);
         $query->fetch();
	     $query->close();
        }

        if ($netonhand<=$reorderlevel && $netonhand!='' && $reorderlevel!='') 
        {
         $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid = ? AND s.notification_type='166'";
         if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $oshopid);
        $query->execute();
        $query->store_result();
        $num_rows = $query->num_rows;
        if($num_rows>0)
        {   
        $query->bind_result($textcontent, $emailcontent, $popupcontent);
        $query->fetch();

        $popupcontent = str_replace("*|PARTNUMBER|*", $partnumber, $popupcontent);
        $emailcontent = str_replace("*|PARTNUMBER|*", $partnumber, $emailcontent);
        $textcontent = str_replace("*|PARTNUMBER|*", $partnumber, $textcontent);
        $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'166',?,?,?)";
        if ($squery = $conn->prepare($stmt)) {
            $squery->bind_param("ssss", $oshopid, $popupcontent, $textcontent, $emailcontent);
            if ($squery->execute()) {
                $conn->commit();
            } else {
                echo $conn->errno;
            }
        } else {
            echo "Notification q Prepare Failed: (" . $conn->errno . ") " . $conn->error;
        }
        $squery->close();
        }
        
    } else {
        echo "notification Prepare Failed: (" . $conn->errno . ") " . $conn->error;
    }
       }

	}
}
echo "success";

recordAudit("Add Part", "Added Part Number $partnumber to RO#$roid");


mysqli_close($conn);







?>
