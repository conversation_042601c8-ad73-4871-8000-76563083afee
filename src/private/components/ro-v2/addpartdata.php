<?php
require CONN;
$shopid = $_GET['shopid'];
$roid = $_GET['roid'];
$comid = $_GET['comid'];
$sf = isset($_GET['sf'])?$_GET['sf']:'';
$sfs = str_replace(" ","",str_replace("-","",$sf));
$ig = isset($_GET["ig"])?$_GET["ig"]:'';

if(in_array($shopid, array('13445','22957'))) $shopid = '22865';

// check for a custom parts registry table
if ($shopid == "1238" || $shopid == "1073" || $shopid == "1191" || $shopid == "1305"){
	$preg = "`partsregistry-".$shopid."`";
}else{
	$preg = "`partsregistry`";
}

// check for xref
$stmt = "select count(*) c from xref where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)){
	$query->execute();
	$query->bind_result($xrefcount);
	$query->fetch();
	$query->close();
}


if ($ig == "yes"){
	$sf = str_replace(array('-', ' ', '/'),'',$sf);
}
?>
	<table class="sbdatatable w-100">
		<thead>
		<tr>
			<th>Part Number&nbsp;</th>
			<th>Description&nbsp;</th>
			<th>Price&nbsp;</th>
			<th>On Hand</th>
			<th>Supplier&nbsp;</th>
			<th>XRef</th>
		</tr>
		</thead>
		<tbody>

		<?php

$stmt = "Select p.shopid, p.PartNumber, p.PartDesc, p.PartPrice, p.PartSupplier,p.partid,p.netonhand,x.xref from partsinventory p left join xref x on p.shopid = x.shopid and "
. "p.partnumber = x.partnumber where p.shopid = '" . $shopid . "' and (replace(replace(x.xref,'-',''),' ','') like '%" . addslashes($sf) . "%' or x.xref like '".addslashes($sf)."' or p.partnumber Like '%" . addslashes($sf) . "%' or p.partdesc like '%" . addslashes($sf) . "%' or replace(replace(x.xref,'-',''),' ','') like '%".str_replace(" ","",str_replace("-","",addslashes($sf)))."%' or replace(replace(p.partnumber,'-',''),' ','') like '%" . str_replace(" ","",str_replace("-","",addslashes($sf))) . "%') limit 50";
$result = $conn->query($stmt);
$nrows = $result->num_rows;
if ($nrows > 0){
	while($row = $result->fetch_assoc()) {
		$partnumber=strtoupper(htmlspecialchars($row['PartNumber']));

?>
			<tr>
				<td onclick="top.parent.partDetail('<?php echo $row['partid']?>','INV','<?php echo $_GET['comid']; ?>')">INV: <?php echo $partnumber;?> </td>
				<td onclick="top.parent.partDetail('<?php echo $row['partid']?>','INV','<?php echo $_GET['comid']; ?>')"><?php echo strtoupper($row['PartDesc']);?></td>
				<td onclick="top.parent.partDetail('<?php echo $row['partid']?>','INV','<?php echo $_GET['comid']; ?>')"><?php echo $row['PartPrice'];?></td>
				<td onclick="top.parent.partDetail('<?php echo $row['partid']?>','INV','<?php echo $_GET['comid']; ?>')"><?php echo $row['netonhand'];?></td>
				<td onclick="top.parent.partDetail('<?php echo $row['partid']?>','INV','<?php echo $_GET['comid']; ?>')"><?php echo strtoupper($row['PartSupplier']);?></td>
				<td onclick="top.parent.partDetail('<?php echo $row['partid']?>','INV','<?php echo $_GET['comid']; ?>')"><?php echo strtoupper($row['xref']);?></td>
			</tr>

<?php
	}
}
	// now get inventory


	if ($xrefcount > 5){
		$stmt = "Select distinct pr.shopid, pr.PartNumber, pr.PartDesc, pr.PartPrice, pr.partid, pr.PartSupplier, x.xref, x.partnumber xpn from " . $preg . " pr left join xref x on pr.shopid = x.shopid and pr.partnumber = x.partnumber where pr.shopid = '" . $shopid . "' and ( replace(replace(pr.partnumber,'-',''),' ','') like '%" . str_replace(" ","",str_replace("-","",addslashes($sf))) . "%' or replace(replace(x.xref,' ',''),'-','') Like '%" . addslashes($sf) . "%' or pr.partdesc like '%" . addslashes($sf) . "%') limit 50";
	}else{
		$stmt = "Select distinct pr.shopid, pr.PartNumber, pr.PartDesc, pr.PartPrice, pr.partid, pr.PartSupplier, '' as xref from " . $preg . " pr where pr.shopid = '" . $shopid . "' and (replace(replace(pr.partnumber,'-',''),' ','') like '%" . str_replace(" ","",str_replace("-","",addslashes($sf))) . "%' or pr.partdesc like '%" . addslashes($sf) . "%') limit 50";

	}
	
	$result = $conn->query($stmt);
	if ($result->num_rows > 0){
	    while($row = $result->fetch_assoc()) {
?>
			<tr>
				<td onclick="top.parent.partDetail('<?php echo $row['partid'];?>','NON','<?php echo $_GET['comid']; ?>')"><?php if ($shopid != '13179'){echo 'NON:'; } ?> <?php echo strtoupper($row['PartNumber']);?> </td>
				<td onclick="top.parent.partDetail('<?php echo $row['partid'];?>','NON','<?php echo $_GET['comid']; ?>')"><?php echo strtoupper($row['PartDesc']);?></td>
				<td onclick="top.parent.partDetail('<?php echo $row['partid'];?>','NON','<?php echo $_GET['comid']; ?>')"><?php echo $row['PartPrice'];?></td>
				<td onclick="top.parent.partDetail('<?php echo $row['partid'];?>','NON','<?php echo $_GET['comid']; ?>')">
				</td>
				<td onclick="top.parent.partDetail('<?php echo $row['partid'];?>','NON','<?php echo $_GET['comid']; ?>')"><?php echo strtoupper($row['PartSupplier']);?></td>
				<td onclick="top.parent.partDetail('<?php echo $row['partid'];?>','NON','<?php echo $_GET['comid']; ?>')"><?php echo strtoupper($row['xref']);?></td>
			</tr>

<?php
		}
	}

?>
		</tbody>
	</table>
</div>
<script>
	$(document).ready(function () {

        var options = {
            info: false,
            paging: false,
			searching: false,
			order:[]
        };

        var table = $(".sbdatatable").dataTable(options);

	});
</script>
	