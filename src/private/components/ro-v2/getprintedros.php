<?php

require CONN;
require_once INTEGRATIONS_PATH . "/sbp_bucket/sbp_bucket.php";

$roid = $_GET['roid'];
$shopid = $_GET['shopid'];

$path = "\\\\fs.shopboss.aws\\share\\savedinvoices/" . $shopid . "/" . $roid;

$files_arr = array();

$files_arr = $sbp_bucket->get_files_by_timestamp($shopid . "/invoices/RepairOrders/" . $roid, $shopid);

if (is_dir($path)) {
    $files = scandir($path, 1);
    $files = array_diff(scandir($path), array('.', '..'));

    foreach ($files as $a => $b) {
        $filepath = SBP . "savedinvoices/$shopid/$roid/" . $b;
        $datecreated = filectime($path . "/" . $b);
        if (substr($b, -3) == "pdf") {
            $files_arr[$datecreated] = $filepath;
        }
    }
}

if (!empty($files_arr)) {
    echo "success|<li class='pro sidenav-item'><a class='sidenav-link' style='' href='#'><i class='fa fa-md fa-file-pdf'></i>Printed RO's</a></li>";
    krsort($files_arr);
    foreach ($files_arr as $ts => $url) {
        $datecreated = date("m/d/Y H:i", $ts);
        echo "<li class='pro sidenav-item'><a class='sidenav-link' onclick='showPrintedRO(\"$url\")' href='#'><i class='fa fa-xxs fa-circle'></i>$datecreated</a></li>";
    }
}


?>
<?php if (isset($conn)) {
    mysqli_close($conn);
} ?>