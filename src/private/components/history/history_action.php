<?php

date_default_timezone_set('America/Phoenix');

require CONN;
$shopid = $_GET['shopid'];

if (isset($_GET['sf'])){
	$sf = $_GET['sf'];
}else{
	$sf = "";
}

$joined = false;
$shopstr = " and (shopid = '".$shopid."' ";
$stmt = "select shopid,joinedshopid from joinedshops where shopid = '$shopid'";
if($query = $conn->prepare($stmt)){
    $query->execute();
    $result = $query->get_result();
	while($row = $result->fetch_array())
	{
	$shopstr .= "|| shopid = '".$row['joinedshopid']."' ";
	$joined = true;
    }
}

$shopstr .= ")";

?>

<div id="table-container" class="table-responsive">
Click a row to open the RO
<table id="wiplist-table" style="width:99%" class="table table-condensed table-header-bg-nohover">
	<thead>
		<tr class="header">
			<th style="padding:1px 1px 1px 5px;" class="rocell">RO</th>
			<th style="padding:1px;" class="stcell">Status</th>
			<th style="padding:1px;" class="dacell">Date</th>
			<th style="padding:1px;" class="cucell">Customer</th>
			<th style="padding:1px;" class="phcell">Phone</th>
			<th style="padding:1px;" class="vecell">Vehicle</th>
			<th style="padding:1px 15px 1px 1px;" class="tocell text-right" style="padding-right:20px;">Total</th>
			<th style="padding:1px;" class="licell">License</th>
			<?php if($joined){?><th style="padding:1px;" class="licell">Shop ID</th><?php }?>
		</tr>
	</thead>
	<tbody>
<?php

if ($sf != ''){
	$c = 1;
	$stmt = "select totalprts,totallbr,totalsublet,salestax,discountamt,totalfees,gp,totalro,statusdate,vin,fleetno,customerid,roid,status as stat,datein,tagnumber,customer,"
		. " customerphone as home,customerwork as work,cellphone as cell,vehinfo,totalro,vehlicense,rotype,shopid"
		. " from repairorders where (roid like '$sf%' or customer like '%$sf%' or vin like '%$sf%' or vehlicense like '%$sf%' or fleetno like '%$sf%' or customerphone like '$sf%'"
		. " or customerwork like '$sf%' or cellphone like '$sf%' or vehinfo like '%$sf%' or convert(totalro,char) like '$sf%') {$shopstr} order by roid desc, datein desc limit 100";
	$result = $conn->query($stmt);
	//echo $result->num_rows;
	//if ($result->num_rows > 0) {
	    // output data of each row
    if ($result->num_rows > 0) {
	    while($row = $result->fetch_array()) {
	    	$mymod = fmod($c,2);
	    	$home = $row["home"];
	    	$work = $row["work"];
	    	$cell = $row["cell"];
	    	$status = $row["stat"];
	    	$statvar = substr($status,0,1);
	    	$statdate = new DateTime($row["statusdate"]);
	    	if( is_numeric($statvar) ){
	    		$status = substr($status,1);
	    	}
	    	if(strlen($row["home"]) > 0){
	    		$home = "H: (".substr($row["home"], 0, 3).") ".substr($row["home"], 3, 3)."-".substr($row["home"],6);
	    	}
	    	if(strlen($row["work"]) > 0){
	    		$work = "W: (".substr($row["work"], 0, 3).") ".substr($row["work"], 3, 3)."-".substr($row["work"],6);
	    	}
	    	if(strlen($row["cell"]) > 0){
	    		$cell = "C: &nbsp;(".substr($row["cell"], 0, 3).") ".substr($row["cell"], 3, 3)."-".substr($row["cell"],6);
	    	}
	    	if ($mymod == 1){
	    		$bgcolor = '#f5f5f5';
	    	}else{
	    		$bgcolor = 'white';
	    	}
	    	if (strtolower($row['stat']) != 'closed'){
	    		$golink = "location.href='".SBP."ro.asp?roid=".$row["roid"]."'";
	    	}else if (strtolower($row['stat']) == 'closed'){
	    		$golink = "location.href='".SBP."viewro.asp?roid=".$row["roid"]."'";
	    	}
	    	if($row['shopid']!=$shopid)
	    	$golink = "location.href='".COMPONENTS_PRIVATE."/ro/ro.php?jshopid=".$row['shopid']."&roid=".$row['roid']."'";
	    	else
	    	$golink = "location.href='".COMPONENTS_PRIVATE."/ro/ro.php?roid=".$row['roid']."'";
?>
		<tr onclick="<?php echo $golink;?>" class="history-row" style="background-color:<?php echo $bgcolor; ?>;border:2px <?php echo $bgcolor; ?> solid;">
			<td  class="rocell" ><strong><?php echo $row["roid"];?></strong></td>
			<td class="stcell"><strong><?php echo $status." ";?>
			</strong>
			</td>
			<td class="dacell" ><strong><?php echo $statdate->format('m/d/Y');?>
			</strong></td>
			<td class="cucell" ><strong><?php echo $row["customer"];?></strong></td>
				<?php
					$h = "";
					if(strlen($home) > 0){
						$h = $h.$home;
						if(strlen($work) > 0 || strlen($cell) > 0){$h = $h."<br>";}
					}
					if(strlen($work) > 0){
						$h = $h.$work;
						if(strlen($cell) > 0){$h = $h."<br>";}
					}
					if(strlen($cell) > 0){$h = $h.$cell;}

				?>
			<td class="phcell" ><div data-html="true" data-toggle="tooltip" title="<?php echo $h;?>" style="max-width:117px;overflow:hidden;white-space: nowrap;" >
				<strong>
				<?php
					$h = "";
					if(strlen($home) > 0){
						echo $home;
						if(strlen($work) > 0 || strlen($cell) > 0){echo " ";}
					}
					if(strlen($work) > 0){
						echo $work;
						if(strlen($cell) > 0){echo " ";}
					}
					if(strlen($cell) > 0){echo $cell;}

				?></strong></div></td>
			<td class="vecell" >
			<strong>
			<?php
				if (strlen($row['fleetno']) > 0){
					echo "<b>#".$row['fleetno']."</b> ";
				}
				echo substr($row["vehinfo"],0,30);
			?>
			</strong>
			</td>
			<td class="tocell text-right" style="padding-right:20px;"><strong><?php echo number_format($row["totalro"],2);?>
			</strong></td>
			<td class="licell"  ><strong><?php echo strtoupper($row["vehlicense"]);?>
			</strong></td>
			<?php if($joined){?><td class="licell"><strong><?= $row['shopid']?></strong></td><?php }?>
		</tr>
<?php
		$roid = $row["roid"];
		$sstmt = "select complaint from complaints where shopid = '".$row['shopid']."' and roid = $roid and cstatus = 'no'";
		$sresult = $conn->query($sstmt);
		$srow = $sresult->fetch_array();
		if ($srow){
?>
		<tr onclick="" class="history-row" style="background-color:<?php echo $bgcolor; ?>;border:2px <?php echo $bgcolor; ?> solid;">
			<td style="border-bottom:2px black solid" colspan="9">
				<div style="margin-left:20px;">VEHICLE ISSUES:
<?php
			echo $srow['complaint']." | ";
		}
		while($srow = $sresult->fetch_array()) {
			echo $srow['complaint']." | ";
		}
		echo "</div>";

		// now get the recommendations
		$tstmt = "select id,`desc`,totalrec from recommend where shopid = '".$row['shopid']."' and roid = $roid";
		//echo $tstmt;
		$tresult = $conn->query($tstmt);
		while ($trow = $tresult->fetch_array()){
			$recid = $trow['id'];
			$rec = $trow['desc'];
			$totalrec = "$".number_format($trow['totalrec'],2);
			echo "<div class='collapse in' style='cursor:pointer;margin-left:30px;' onclick='$(\"#ulrec$recid\").fadeToggle()'><i class='fa fa-plus'></i> <b>RECOMMENDED</b>: $totalrec - $rec</div>";
			echo "<ul style='margin-left:40px;' id='ulrec$recid' class='nav nav-list collapse'>";

			// now get the labor for the recommend
			$lstmt = "select `desc`, hours,id from recommendlabor where shopid = '".$row['shopid']."' and roid = $roid and recid = $recid";
			//echo "<li>$lstmt</li>";
			$lresult = $conn->query($lstmt);
			while ($lrow = $lresult->fetch_array()){
				$ldesc = strtoupper($lrow['desc']);
				$lhrs = $lrow['hours'];
				$id = $lrow['id'];
				echo "<li>LABOR: $ldesc - $lhrs</li>";

			}
			$pstmt = "select `partdesc`, partnumber,id,partprice,quantity from recommendparts where shopid = '".$row['shopid']."' and roid = $roid and recid = $recid";
			//echo "<li>$lstmt</li>";
			$presult = $conn->query($pstmt);
			while ($prow = $presult->fetch_array()){
				$pdesc = strtoupper($prow['partnumber']).' '.strtoupper($prow['partdesc']);
				$phrs = $prow['quantity'].' @ '.number_format($prow['partprice'],2);
				echo "<li>PART: $pdesc - $phrs</li>";

			}


			echo "</ul>";
		}
		echo "</div></td></tr>";
?>

<?php
		$tcroid = "no";
		$c++;
	}
    }
}
mysqli_close($conn);
?>
	</tbody>
</table>
