
<?php
	$title = 'Quote Comm Report';
	// Use this for Custom Reports
	include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_config.php');

	// Global Variables
	$shopid = $_COOKIE['shopid'];
	$date = new DateTime('now');
	$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
	$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
	$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
	$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
	$sd = $sdate;
	$ed = $edate;
	$sd1=date('Y-m-d',strtotime($sd));
	$ed1=date('Y-m-d',strtotime($ed));

    $roStatus = isset($_REQUEST['roStatus']) ? filter_var($_REQUEST['roStatus'], FILTER_SANITIZE_STRING) : "all";

    $status = [
        'INSPECTION' => '1INSPECTION',
        'APPROVAL' => '2APPROVAL',
        'PARTS' => '3PARTS',
        'ASSEMBLY' => '4ASSEMBLY',
        'QCHECK' => '5QCHECK'
    ];
    
    $roStatus = $status[strtoupper($roStatus)] ?? $roStatus;

    $roStatusSQL = "";
    if ($roStatus != "all") {
        $roStatusSQL = "AND r.roid IN ( SELECT roid FROM shopboss.repairorders WHERE shopid = '24197' AND status = '" . $roStatus . "' )";
    }

	// Use this for Custom Reports
	$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder

    // Query Starts Here
    $tablefields=array('RO #', 'Customer', 'Date');
    $alldata=array();

    $statement="SELECT r.roid, datetime, ro.Customer 
                FROM repairordercommhistory r 
                JOIN repairorders ro ON ro.shopid = r.shopid AND ro.roid = r.roid
                WHERE r.shopid = ? 
                    AND datetime >= ?
                    AND datetime <= ?
                    AND ( comm LIKE '%An Email Update was sent to%' OR comm LIKE '%https://staging.shopbosspro.com/status.php%' OR comm LIKE '%An Text Message Update was sent to%' ) 
                    $roStatusSQL";   

    if ($query = $conn->prepare($statement)) {
        $query->bind_param("sss", $shopid, $sd1, $ed1);
        $query->execute();
        $result = $query->get_result();

        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $alldata[]=array(
                    $row['roid'],
                    $row['Customer'],
                    date_format(new DateTime($row['datetime']),'m/d/Y h:i A'),
                );
            }
        }
        $query->close();
    } else {
        echo "Data Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
?>
<!DOCTYPE html>
<html>
	<body>

		<?php
		include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_buttons.php");
		?>

		<table class="table table-condensed table-header-bg" >
			<thead>
				<tr class="table_header table_head">
                    <?php foreach ($tablefields as $field): ?>
                        <td><?= $field ?></td>
                    <?php endforeach; ?>
				</tr>
			</thead>
			<tbody>
                <?php foreach ($alldata as $row): ?>
                    <tr>
                        <?php foreach ($row as $cell): ?>
                            <td><?= $cell; ?></td>
                        <?php endforeach; ?>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

		<?php
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/footer_reports.php");
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_form.php");
		?>
	</body>
</html>
<?php
	if(isset($conn)){
		mysqli_close($conn);
	}
?>
