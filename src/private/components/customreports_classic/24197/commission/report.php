
<?php
	$title = ' Commission Report';
	// Use this for Custom Reports
	include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_config.php');

	// Global Variables
	$shopid = $_COOKIE['shopid'];
	$date = new DateTime('now');
	$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
	$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
	$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
	$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
	$sd = $sdate;
	$ed = $edate;
	$sd1=date('Y-m-d',strtotime($sd));
	$ed1=date('Y-m-d',strtotime($ed));


	// Use this for Custom Reports
	$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder

    // Query Starts Here
    $tablefields=array('RO #', 'GP Parts', 'GP Labor', 'Commission');
    $alldata=array();

    $statement="SELECT roid
                FROM repairorders r 
                WHERE shopid = ? 
                    AND r.rotype != 'no approval' 
                    AND r.status = 'closed'
                    AND r.StatusDate >= ?
                    AND r.StatusDate <= ?";

    if ($query = $conn->prepare($statement)) {
        $query->bind_param("sss", $shopid, $sd1, $ed1);
        $query->execute();
        $result = $query->get_result();

        $total_part_gp = 0;
        $total_labor_gp = 0;

        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                // Calculate Part GP
                $part_stmt =   "SELECT SUM(PartPrice * Quantity) - SUM(Cost  * Quantity) as partGP 
                                FROM parts p 
                                WHERE shopid = ? AND roid = ?";
                if ($part_query = $conn->prepare($part_stmt)) {
                    $part_query->bind_param("ss", $shopid, $row['roid']);
                    $part_query->execute();
                    $part_query->bind_result($part_gp);
                    $part_query->fetch();
                    $part_query->close();
                }
                
                // Calculate Labor GP
                $labor_gp = 0; $labor_gp_sum = 0;
                $labor_stmt =  "SELECT  l.LineTotal,
                                        e.hourlyrate,
                                        l.LaborHours,
                                        e.EmployeeFirst,
                                        e.EmployeeLast,
                                        l.LaborID,
                                        l.complaintid,
                                        l.labor
                                FROM labor l 
                                JOIN employees e ON e.shopid = l.shopid AND l.Tech = CONCAT(e.EmployeeLast, ', ', e.EmployeeFirst)
                                WHERE l.shopid = ? AND l.roid = ?";
                if ($labor_query = $conn->prepare($labor_stmt)) {
                    $labor_query->bind_param("ss", $shopid, $row['roid']);
                    $labor_query->execute();
                    $labor_result = $labor_query->get_result();

                    if ($labor_result->num_rows > 0) {
                        while ($l_row = $labor_result->fetch_assoc()) {
                            $labor_gp = $l_row['LineTotal'] - ($l_row['hourlyrate'] * $l_row['LaborHours']);
                            $roid = $row['roid'];
                            $labor_id = $l_row['LaborID'];
                            $complaint_id = $l_row['complaintid'];
                            $labor = $l_row['labor'];

                            // CHECK IF THERE IS TIME CLOCK HOURS
                            $tc_stmt = "select sum(TIMESTAMPDIFF(MINUTE, startdatetime, enddatetime)) as tiff FROM"
                                        . " labortimeclock where shopid = ? and not isnull(enddatetime)"
                                        . " and roid = ? and laborid = ?";
                            
                            if ($tc_query = $conn->prepare($tc_stmt)) {
                                $tc_query->bind_param("sis", $shopid, $roid, $labor_id);
                                $tc_query->execute();
                                $tc_query->bind_result($timeclock);
                                $tc_query->fetch();
                                $tc_query->close();
                            }
                            if ( $timeclock ){
                                $labor_gp = $l_row['LineTotal'] - ($l_row['hourlyrate'] * $timeclock / 60);
                            }

                            $laborCost = ($l_row['hourlyrate'] * $l_row['LaborHours']);
                            $laborCostTC = ($l_row['hourlyrate'] * $timeclock / 60);
                            
                            $labor_gp_sum += $labor_gp;
                        }
                    }
                    
                    $labor_query->close();
                }

                $total_part_gp += $part_gp;
                $total_labor_gp += $labor_gp_sum;

                $alldata[]=array(
                    $row['roid'],
                    asDollars($part_gp),
                    asDollars($labor_gp_sum),
                    asDollars(($part_gp + $labor_gp_sum) * 5.41 / 100)
                );
            }

            $alldata[]=array(
                'TOTALS',
                asDollars($total_part_gp),
                asDollars($total_labor_gp),
                asDollars(($total_part_gp + $total_labor_gp) * 5.41 / 100)
            );
        }
        $query->close();
    } else {
        echo "Data Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
?>
<!DOCTYPE html>
<html>
    <head>
        <style>
            .bold{
                font-weight: bold;
            }
        </style>
    </head>
	<body>

		<?php
		include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_buttons.php");
		?>

		<table class="table table-condensed table-header-bg" >
			<thead>
				<tr class="table_header table_head">
                    <?php foreach ($tablefields as $field): ?>
                        <td><?= $field ?></td>
                    <?php endforeach; ?>
				</tr>
			</thead>
			<tbody>
                <?php foreach ($alldata as $row): ?>
                    <tr class="<?= ($row[0] == 'TOTALS') ? 'bold' : ''; ?>">
                        <?php foreach ($row as $cell): ?>
                            <td><?= $cell; ?></td>
                        <?php endforeach; ?>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

		<?php
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/footer_reports.php");
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_form.php");
		?>
	</body>
</html>
<?php
	if(isset($conn)){
		mysqli_close($conn);
	}
?>
