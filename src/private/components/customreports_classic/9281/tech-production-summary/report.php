<?php
$title = 'Technician Production Summary';  // Report Title Goes Here
// Use this for Gen Pop Reports
require(COMPONENTS_PRIVATE_PATH."/reports/includes/header_reports.php");
require CONN;

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));
$job_completed = isset($_REQUEST['job_completed']) ? filter_var($_REQUEST['job_completed'], FILTER_SANITIZE_STRING) : "";
$ro_status = isset($_REQUEST['ro_status']) ? filter_var($_REQUEST['ro_status'], FILTER_SANITIZE_STRING) : "Closed";
// Page Variables
$subtitle = 'This Report Includes ' . $ro_status . ' Status Repair Orders';  // Report SubTitle Goes Here - Hide if not needed
if ($job_completed == "yes") {
    $subtitle .= " and job status marked complete";
}
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($companyname, $conamereports);
    $query->fetch();
    $query->close();
}

if ($conamereports == 'yes') {
    $title .= " - $companyname";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_tech_sum.php'; //Only change if a custom PHPExcel is created in the template folder
?>

<title><?= $title; ?></title>  <!-- Meta title for page. Change in variable above-->

<!DOCTYPE html>
<html>
<body>

<?php
// Use this for Gen Pop Reports
include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_buttons.php");

$roStatusSQL = "";
if ($ro_status != "all") {
    if ($ro_status != "open") {
        $roStatusSQL = "AND status = '" . $ro_status . "'";
    } else {
        $roStatusSQL = "AND status != 'Closed'";
    }
}


$jobSQL = "";
if (!empty($job_completed) && $job_completed == "yes") {
    $jobSQL = "AND c.acceptdecline = 'Job Complete'";
}


?>
<!-- Column Headers Insert Report Variables here -->
<table class=report_table>
    <tr class="table_header table_head">
        <td>Date</td>
        <td>Technician Name</td>
        <td>Hours Sold</td>
    </tr>
    <?php
    $tablefields = array('Date', 'Technician Name', 'Hours Sold');//set table headings array for excel export
    $alldata = array();//this will hold all the data arrays to be exported to excel

    $runttlhrs = 0;

    $startDate = DateTime::createFromFormat('m/d/Y', '01/01/2025');
    $endDate = DateTime::createFromFormat('m/d/Y', '01/31/2025');

    if ($startDate && $endDate && $startDate <= $endDate) {
        $currentDate = clone $startDate;
        while ($currentDate <= $endDate) {
            $dayDate = date('Y-m-d', strtotime($currentDate->format('m/d/Y')));

            $tar = "";
            // Insert DB Query Here
            $tstmt = "select roid from repairorders where rotype != 'no approval' and shopid = ? and statusdate = ? $roStatusSQL";
            if ($query = $conn->prepare($tstmt)) {
                $query->bind_param("ss", $shopid, $dayDate);
                $query->execute();
                $r = $query->get_result();
                while ($rs = $r->fetch_assoc()) {
                    $tar .= $rs['roid'] . ",";
                }
        
            } else {
                echo "103-RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }
        
        
            if (substr($tar, -1) == ",") {
                $tar = substr($tar, 0, strlen($tar) - 1);
            }
        
            // Template Query Begins - Replace entire section
            if (!empty($tar)) {
                $stmt = "select labor.tech, sum(labor.laborhours) as hours from labor JOIN complaints c ON c.shopid = labor.shopid AND c.roid = labor.ROID AND labor.complaintid = c.complaintid where labor.deleted = 'no' and labor.Tech != 'discount, discount' and labor.roid in ($tar) and labor.shopid = ? $jobSQL group by labor.tech";
                // echo $stmt;
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $roresult = $query->get_result();
        
                    while ($ro = $roresult->fetch_array()) {
                        $hours = $ro["hours"];
                        $runttlhrs += $hours;
                        ?>
                        <tr>
                            <td><?= $dayDate; ?></td>
                            <td><?= strtoupper($ro["tech"]); ?></td>
                            <td><?= $hours; ?></td>
                        </tr>
                        <?php
                        $alldata[] = array($dayDate, strtoupper($ro["tech"]), strtoupper($ro["hours"]));//fill up the alldata array with the arrays of data to be shown in excel export
                    } // end of while for loop
                    $query->close();
                } else {
                    echo "133-RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }
                // end if for end of file
            }

            $currentDate->modify('+1 day');
        }
    } else {
        echo "Invalid date range.";
    }


    $alldata[] = array('', '');
    $alldata[] = array('TOTALS', '',$runttlhrs)
    ?>
    <tr>
        <td colspan="2"><b>Total Hours</b></td>
        <td><b><?= $runttlhrs; ?></b></td>
    </tr>
</table>
<?php
include(COMPONENTS_PRIVATE_PATH. '/reports/includes/footer_reports.php');
include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_form.php');
?>
</body>
</html>
<?php
if (isset($conn)) {
    mysqli_close($conn);
}
?>
