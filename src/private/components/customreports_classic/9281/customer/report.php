<!DOCTYPE html>
<html>
<?php

// Use this for Gen Pop Reports
require(COMPONENTS_PRIVATE_PATH."/reports/includes/header_reports.php");
require CONN;
//require("../php/functions.php");



// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
//$sd1=date('Y-m-d',strtotime($sd));
//$ed1=date('Y-m-d',strtotime($ed));
// Page Variables
$title = 'Customer DOB Report';  // Report Title Goes Here
//$subtitle = 'my subtitle';  // Report SubTitle Goes Here - Hide if not needed
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder
$customerSQL = "";
if($cid != "all"){
    $customerSQL = " AND customerid = ".$cid;
}
// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>

<title><?= $title;?></title>  <!-- Meta title for page. Change in variable above-->
<body>

<?php

// Use this for Gen Pop Reports
require COMPONENTS_PRIVATE_PATH."/reports/includes/report_buttons.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/report_buttons.php");
?>

<!-- Column Headers Insert Report Variables here -->

	<table class=report_table>
	<tr class="table_header table_head">
		<td>Customer</td>
		<td>Phones</td>
        <td>Email</td>
		<td>Address</td>
        <td>City</td>
        <td>State</td>
        <td>Zip</td>
		<td>DOB</td>
	</tr>

    <?php
    $tablefields=array('Customer','Home Phone','Work Phone','Cell Phone','Email','Address','City','State','Zip','DOB');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel

// Insert DB Query Here

// Template Query Begins - Replace entire section
		$tc = 0;
		$gtro = 0;
		$stmt = "select UCASE(CONCAT(LastName,', ',FirstName)) as customer, cellphone, workphone as customerwork, homephone as customerphone,  userdefined2 as DOB, UCASE(EMail) as email, UCASE(Address) as address, UCASE(City) as city, UCASE(state) as state, UCASE(zip) as zip FROM customer WHERE shopid = ? AND SUBSTRING_INDEX(userdefined2, '/', 1) = ?";
		if($query = $conn->prepare($stmt)){
			$query->bind_param("si",$shopid,$sd);
			$query->execute();
			$roresult = $query->get_result();
		}else{
			echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

			while($ro = $roresult->fetch_array()) {
				$cid = $ro["customerid"];
				$stdate= date('m/d/Y',strtotime($ro['statusdate']));

		?>
			<tr>
				<td><?= $ro["customer"]; ?></td>
				<td><?php
					if (strlen($ro["customerphone"]) > 5) {
						echo " <b>H:</b>".formatPhone($ro["customerphone"]);
					}if (strlen($ro["customerwork"]) > 5) {
						echo " <b>W:</b>".formatPhone($ro["customerwork"]);
					}if (strlen($ro["cellphone"]) >5) {
						echo " <b>C:</b>".formatPhone($ro["cellphone"]);
					}?>
				</td>
                <td><?= $ro["email"]; ?></td>
                <td><?= $ro["address"]; ?></td>
                <td><?= $ro["city"]; ?></td>
                <td><?= $ro["state"]; ?></td>
                <td><?= $ro["zip"]; ?></td>
				<td style=""><?= $ro['DOB'] ?></td>
			</tr>
        <?php

                $alldata[] = array($ro['customer'], formatPhone($ro['customerphone']), formatPhone($ro['customerwork']), formatPhone($ro['cellphone']), $ro['email'], $ro['address'],$ro['city'],$ro['state'],$ro['zip'], $ro['DOB']);
            }
        ?>
		</table>


<?php

// Use this for Gen Pop Reports
require COMPONENTS_PRIVATE_PATH."/reports/includes/footer_reports.php";
require COMPONENTS_PRIVATE_PATH."/reports/includes/report_form.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/footer_reports.php");
//include("../../../php/includes/reports/report_form.php");
?>

</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
