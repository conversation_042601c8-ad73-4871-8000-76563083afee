<?php
// Use this for Gen Pop Reports
require(COMPONENTS_PRIVATE_PATH. "/reports/includes/header_reports.php");
require CONN;
////require("../php/functions.php");
// Use this for Custom Reports
//require("../../../php/includes/reports/header_reports.php");
//require("../../../php/conn.php");
//require("../../functions.php");
// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));
$paymentMetod = isset($_GET['pm']) ? $_GET['pm'] : '';
// Page Variables
$title = 'Cash In Report';  // Report Title Goes Here
// $subtitle = 'Summary and Detail';  // Report SubTitle Goes Here - Hide if not needed
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($companyname, $conamereports);
    $query->fetch();
    $query->close();
}
if ($conamereports == 'yes') {
    $title .= " - $companyname";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . "/reports/templates/excelexport.php"; //Only change if a custom PHPExcel is created in the template folder
// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';


?>
<title><?= $title; ?></title>  <!-- Meta title for page. Change in variable above-->
<!DOCTYPE html>
<html>
<body>
<style>
    .summary {
        padding-left: 250px;
        padding-right: 250px;
    }
    td {
        max-width: 25px;
    }
    .table_margin {
        margin: 15px;
    }
    @media print {
    }
</style>
<?php
// Use this for Gen Pop Reports
include(COMPONENTS_PRIVATE_PATH. "/reports/includes/report_buttons.php");
// Use this for Custom Reports
//include("../../../php/includes/reports/report_buttons.php");
?>
        <?php
        $tablefields = array('Payment Method', 'Count', 'Total Amount', '');//set table headings array for excel export
        $alldata = array();//this will hold all the data arrays to be exported to excel
        $tapcount = 0;
        $tapamount = 0;
        // Insert DB Query Here
        // Template Query Begins - Replace entire section
        $stmt = "select distinct ptype as method from accountpayments where shopid = ? and pdate >= ? and pdate <= ? UNION select distinct ptype as method from `accountpayments-ps` where shopid = ? and pdate >= ? and pdate <= ? UNION SELECT distinct method FROM paymentmethods WHERE shopid = ? ";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("sssssss",$shopid, $sd1, $ed1, $shopid, $sd1, $ed1, $shopid);
            $query->execute();
            $pmresult = $query->get_result();
        } else {
            echo "Payment Methods Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }
        $pmArr = array();
        if ($pmresult->num_rows > 0) {
            while ($pm = $pmresult->fetch_array()) {
                $ptype = $pm["method"];
                $stmt = "SELECT sum(amt) as a, count(*) as c ";
                $stmt .= " FROM accountpayments ";
                $stmt .= "WHERE shopid = ? ";
                $stmt .= "  AND ptype = ?";
                $stmt .= "  AND pdate >= ? And pdate <= ? ";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("ssss", $shopid, $ptype, $sd1, $ed1);
                    $query->execute();
                    $apresult = $query->get_result();
                } else {
                    echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }
                //printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$ptype,$sdate,$edate);
                $ap = $apresult->fetch_array();
                if ($apresult->num_rows > 0) {
                    if (!is_null($ap["a"])) {
                        $apamount = $ap["a"];
                    } else {
                        $apamount = 0;
                    }
                    if (!is_null($ap["c"])) {
                        $apcount = (int)$ap["c"];
                    } else {
                        $apcount = 0;
                    }
                }
                $stmt = "SELECT sum(amt) as a, count(*) as c ";
                $stmt .= " FROM `accountpayments-ps` ";
                $stmt .= "WHERE shopid = ? ";
                $stmt .= "  AND ptype = ?";
                $stmt .= "  AND pdate >= ? And pdate <= ? ";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("ssss", $shopid, $ptype, $sd1, $ed1);
                    $query->execute();
                    $apsresult = $query->get_result();
                } else {
                    echo "Account Payments PS Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }
                $aps = $apsresult->fetch_array();
                if ($apsresult->num_rows > 0) {
                    if (!is_null($aps["a"])) {
                        $apamount = $apamount + $aps["a"];
                    } else {
                        $apamount = $apamount + (int)0;
                    }
                    if (!is_null($aps["c"])) {
                        $apcount = $apcount + (int)$aps["c"];
                    } else {
                        $apcount = $apcount + (int)0;
                    }
                }
                $tapcount = $tapcount + $apcount;
                $tapamount = $tapamount + $apamount;
                $nptype = strtoupper(trim($ptype));
                if ($nptype == "MC") {
                    $nptype = "MASTERCARD";
                } else if($nptype == "AMEX"){
                    $nptype = "AMERICAN EXPRESS";
                } else if($nptype == "DISC"){
                    $nptype = "DISCOVER";
                }
                if (isset($pmArr[$nptype])) {
                    $pmcount = $pmArr[$nptype]['count'] + $apcount;
                    $pmamt = $pmArr[$nptype]['amount'] + $apamount;
                } else {
                    $pmcount = $apcount;
                    $pmamt = $apamount;
                }
                $pmArr[$nptype] = array(
                    'ptype' => $nptype,
                    'count' => $pmcount,
                    'amount' => $pmamt
                );
            } // while loop
       //     print_r($pmArr);
            foreach ($pmArr as $pm){
            ?>
            <!-- <tr>
                <td style="padding-top:1px; padding-left:10px;"><?= $pm['ptype'] ?></td>
                <td style="padding-top:1px; text-align:center"><?= $pm['count']; ?></td>
                <td style="padding-top:1px; text-align:right; padding-right:10px;"><?= asDollars($pm['amount']); ?></td>
            </tr> -->
            <?php
                // $alldata[] = array($pm['ptype'], $pm['count'], asDollars($pm['amount']), '');
                }
            } // end if pm
            ?>

        <?php

        $alldata[] = array('TABLEHEAD', 'RO #', 'Date Received', 'Payment Type', 'Amount');
        $ttlamt = 0;
        $ttlcount = 0;
        ?>
    </table>
</div>
<div class="table_margin table_detail">
    <!-- <h4 style="text-align: center">Repair Order Details</h4> -->
    <table class="report_table summary_t2">
        <tr class="table_header table_head">
            <td> RO #</td>
            <td>Date Received</td>
            <td>Payment Type</td>
            <td style="text-align:right">Amount</td>
        </tr>
        <?php
        $stmt = "SELECT roid,pdate,ptype,amt";
        $stmt .= " FROM accountpayments ";
        $stmt .= "WHERE shopid = ? ";
        $stmt .= "  AND pdate >= ? And pdate <= ? AND ptype = ? order by pdate, roid asc";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("ssss", $shopid, $sd1, $ed1, $paymentMetod);
            $query->execute();
            $apresult = $query->get_result();
        } else {
            echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }

        ?>
        <?php
        if ($apresult->num_rows > 0) {
            while ($ap = $apresult->fetch_array()) {
                $pdate = new Datetime($ap["pdate"]);
                $nptype = strtoupper($ap['ptype']);
                if ($nptype == "MC") {
                    $nptype = "MASTERCARD";
                } else if($nptype == "AMEX"){
                    $nptype = "AMERICAN EXPRESS";
                } else if($nptype == "DISC"){
                    $nptype = "DISCOVER";
                }
                ?>
                <tr>
                    <td><?= $ap["roid"]; ?>&nbsp;</td>
                    <td><?= date_format($pdate, 'm/d/Y'); ?></td>
                    <td><?= $nptype; ?></td>
                    <td style="text-align:right"><?= asDollars($ap["amt"]); ?></td>
                </tr>
                <?php
                $ttlamt += $ap['amt'];
                $ttlcount++;
                $alldata[] = array($ap["roid"], date_format($pdate, 'm/d/Y'), strtoupper($ap["ptype"]), asDollars($ap["amt"]));
            } // end while
        } // end if
        ?>
    <!-- </table>
</div> -->
<?php
// $alldata[] = array('', '', '', '');
// $alldata[] = array('TABLETITLE', 'Part Sales Details', '', '');
// $alldata[] = array('TABLEHEAD', 'PS #', 'Date Received', 'Payment Type', 'Amount');
?>
<div class="table_margin table_detail">
    <!-- <h4 style="text-align: center">Part Sales Details</h4> -->
    <table class="report_table summary_t2">
        <tr class="table_header table_head">
            <td>PS #</td>
            <td>Date Received</td>
            <td>Payment Type</td>
            <td style="text-align:right">Amount</td>
        </tr>
        <?php
        $stmt = "SELECT *";
        $stmt .= " FROM `accountpayments-ps` ";
        $stmt .= "WHERE shopid = ? ";
        $stmt .= "  AND pdate >= ? And pdate <= ? order by pdate, psid asc";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("sss", $shopid, $sd1, $ed1);
            $query->execute();
            $apsresult = $query->get_result();
        } else {
            echo "Account Payments for Part Sales Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }
        if ($apsresult->num_rows > 0) {
            while ($aps = $apsresult->fetch_array()) {
                $pdate = new Datetime($aps["pdate"]);
                $nptype = strtoupper($aps['ptype']);
                if ($nptype == "MC") {
                    $nptype = "MASTERCARD";
                } else if($nptype == "AMEX"){
                    $nptype = "AMERICAN EXPRESS";
                } else if($nptype == "DISC"){
                    $nptype = "DISCOVER";
                }
                ?>
                <tr>
                    <td><?php echo "PS " . $aps["psid"]; ?>&nbsp;</td>
                    <td><?php echo date_format($pdate, 'm/d/Y'); ?></td>
                    <td><?php echo $nptype; ?></td>
                    <td style="text-align:right"><?php echo asDollars($aps["amt"]); ?></td>
                </tr>
                <?php
                $ttlamt += $aps['amt'];
                $ttlcount++;
                $alldata[] = array("PS " . $aps["psid"], date_format($pdate, 'm/d/Y'), strtoupper($aps["ptype"]), asDollars($aps["amt"]));
            } // end while
        } // end if
        ?>
        <tr class="">
            <td colspan="4">&nbsp;</td>
        </tr>
        <tr class="table_total">
            <td>TOTALS</td>
            <td></td>
            <td><strong><?= $ttlcount ?></strong></td>
            <td style="text-align:right; font-weight: bold"><?php echo asDollars($ttlamt); ?></td>
        </tr>
    </table>
</div>
<?php
// Use this for Gen Pop Reports
include(COMPONENTS_PRIVATE_PATH . "/reports/includes/footer_reports.php");
include(COMPONENTS_PRIVATE_PATH . "/reports/includes/report_form.php");
// Use this for Custom Reports
//include("../../../php/includes/reports/footer_reports.php");
//include("../../../php/includes/reports/report_form.php");

?>
</body>
</html>
<?php
if (isset($conn)) {
    mysqli_close($conn);
}
?>