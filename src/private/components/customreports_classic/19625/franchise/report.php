<?php

include(COMPONENTS_PRIVATE_PATH . '/reports/includes/report_config.php');

// Global Variables
$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? filter_var($_GET['sd'], FILTER_SANITIZE_STRING) : '';
$ed = isset($_GET['ed']) ? filter_var($_GET['ed'], FILTER_SANITIZE_STRING) : '';
$sdate = isset($_GET['sdate']) ? filter_var($_GET['sdate'], FILTER_SANITIZE_STRING) : $sd;
$edate = isset($_GET['edate']) ? filter_var($_GET['edate'], FILTER_SANITIZE_STRING) : $ed;
$ex_bd = isset($_GET['ex_bd']) ? filter_var($_GET['ex_bd'], FILTER_SANITIZE_STRING) : "true";
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));
$path = $_SERVER['HTTP_HOST'];

// Page Variables
$title = 'Franchise Fee Report';  // Report Title Goes Here
//$subtitle = 'my subtitle';  // Report SubTitle Goes Here - Comment out if not needed
$template = COMPONENTS_PRIVATE . "/reports/templates/excelexport.php"; //Only change if a custom PHPExcel is created in the template folder
?>

<title><?= $title; ?></title>  <!-- Meta title for page. Change in variable above-->

<!DOCTYPE html>
<html>
<body>

<?php
include(COMPONENTS_PRIVATE_PATH . '/reports/includes/report_buttons.php');
?>

<!-- Column Headers Insert Report Variables here -->

<table class=report_table>
    <tr class="table_header table_head">
        <td>Invoice Date</td>
        <td>RO #</td>
        <td>Customer</td>
        <td style="text-align:right">Invoice Total</td>
        <td style="text-align:right">Amount Paid</td>
        <td style="text-align:right">Percent Paid</td>
        <td style="text-align:right">Sales Tax Total</td>
        <td style="text-align:right">Sales Tax Paid</td>
        <td style="text-align:right">Net Sale</td>
    </tr>

    <?php


    $ex_bdSQL = "";
    if(!empty($ex_bd) && $ex_bd == "true"){
        $ex_bdSQL = "AND a.ptype != 'Bad Debt'";
    }

    $tablefields = array('Invoice Date', 'RO #', 'Customer', 'Invoice Total', 'Amount Paid', 'Percent Paid', 'Sales Tax Total', 'Sales Tax Paid', 'Net Sales');//set table headings array for excel export
    $alldata = array();//this will hold all the data arrays to be exported to excel
    // Template Query Begins - Replace entire section
    $ttlro = $ttlamt = $ttltax = $ttltaxamt = $ttlnetsale = '0';
    $stmt = "select r.statusdate, r.roid, r.`customer`, r.totalro, r.salestax, SUM(a.amt) as amt FROM repairorders r join accountpayments a ON r.shopid = a.shopid AND r.roid = a.roid WHERE r.shopid = ? AND r.`status` = 'closed' AND r.`rotype` != 'no approval' AND r.statusdate >= ? AND r.statusdate <= ? $ex_bdSQL group by r.roid order by statusdate";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("sss", $shopid, $sd1, $ed1);
        $query->execute();
        $roresult = $query->get_result();
    } else {
        echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
    while ($ro = $roresult->fetch_array()) {
        $statdate = date('m/d/Y', strtotime($ro["statusdate"]));
        $amtpct = (($ro["amt"] / $ro["totalro"]) * 100);
        $netsale = $ro["totalro"] - $ro["salestax"];
        if ($ro["totalro"] == $ro["amt"]) {
            $taxpaid = $ro["salestax"];
        } else {
            $taxpaid = "0";
        }
        $ttlro += $ro["totalro"];
        $ttlamt += $ro["amt"];
        $ttltax += $ro["salestax"];
        $ttltaxamt += $taxpaid;
        $ttlnetsale += $netsale;
        ?>
        <tr>
            <td><?= $statdate; ?></td>
            <td><?= $ro["roid"]; ?></td>
            <td><?= strtoupper($ro["customer"]); ?></td>
            <td style="text-align:right"><?= number_format($ro["totalro"]); ?></td>
            <td style="text-align:right"><?= number_format($ro["amt"]); ?></td>
            <td style="text-align:right"><?= number_format($amtpct); ?></td>
            <td style="text-align:right"><?= number_format($ro["salestax"]); ?></td>
            <td style="text-align:right"><?= number_format($taxpaid); ?></td>
            <td style="text-align:right"><?= number_format($netsale); ?></td>
        </tr>
        <?php
        $alldata[] = array($statdate, $ro["roid"], strtoupper($ro["customer"]), number_format($ro["totalro"]), number_format($ro["amt"]), number_format($amtpct), number_format($ro["salestax"]), number_format($taxpaid), number_format($netsale));//fill up the alldata array with the arrays of data to be shown in excel export
    } // end of while for loop
    // end if for end of file
    $franfee = ($ttlnetsale * 0.05);

    $alldata[] = array('', '', '', '', '', '', '', '', '');
    $alldata[] = array('TOTALS', '', 'FEE: ' . number_format($franfee), number_format($ttlro), number_format($ttlamt), '', number_format($ttltax), number_format($ttltaxamt), number_format($ttlnetsale));
    ?>
    <tr>
        <td><b>Totals</b></td>
        <td><b></b></td>
        <td><b>Franchise Fee: <?= number_format($franfee); ?></b></td>
        <td style="text-align:right"><b><?= number_format($ttlro); ?></b></td>
        <td style="text-align:right"><b><?= number_format($ttlamt); ?></b></td>
        <td style="text-align:right"><b></b></td>
        <td style="text-align:right"><b><?= number_format($ttltax); ?></b></td>
        <td style="text-align:right"><b><?= number_format($ttltaxamt); ?></b></td>
        <td style="text-align:right"><b><?= number_format($ttlnetsale); ?></b></td>
    </tr>
</table>

<?php
include(COMPONENTS_PRIVATE_PATH . '/reports/includes/footer_reports.php');
include(COMPONENTS_PRIVATE_PATH . '/reports/includes/report_form.php');
?>


</body>
</html>
<?php
if (isset($conn)) {
    mysqli_close($conn);
}
?>
