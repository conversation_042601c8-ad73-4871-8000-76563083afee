<?php
/*
Cloned off of 3584
php version derived from productionreportsdrdetailwithpayments.php version in production 8/2/17
putting in dateprompt like servicewriter/servicewriter.php
*/
require CONN;
//require("../../functions.php");

$shopid = $_COOKIE['shopid'];
if (isset($_GET['excel']) == "y") {

    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; '.'filename="shopproddetbytech.xls"');
}
if (isset($_GET['excel'])) {
    $excel = $_GET['excel'];
    if ($excel == "y") {
        $startdate = $_GET['sd'];
        $sd = date_format(new DateTime($startdate),'Y-m-d');
        $enddate = $_GET['ed'];
        $ed = date_format(new DateTime($enddate),'Y-m-d');
    }
}

if (isset($_GET['sdate'])) {
    $startdate = $_GET['sdate'];
    $sd = date_format(new DateTime($startdate),'Y-m-d');
}

if (isset($_GET['edate'])) {
    $enddate = $_GET['edate'];
    $ed = date_format(new DateTime($enddate),'Y-m-d');
}

if (isset($_POST['sdate'])) {
    $startdate = $_POST['sdate'];
    $sd = date_format(new DateTime($startdate),'Y-m-d');
}

if (isset($_POST['edate'])) {
    $enddate = $_POST['edate'];
    $ed = date_format(new DateTime($enddate),'Y-m-d');
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <title>Shop Production Detail Report</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700"/>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css"/>

    <!-- Page JS Plugins CSS -->
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css"/>
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css"/>

    <!-- Bootstrap and OneUI CSS framework -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css"/>
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datetimepicker/bootstrap-datetime.css"/>
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css"/>
    <script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
    <script src="<?= SCRIPT ?>/tipped.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

    <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
    <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
    <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
    <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
    <script src="<?= SCRIPT ?>/app.js"></script>
    <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
    <script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
    <script src="<?= SCRIPT ?>/plugins/datetimepicker/bootstrap-datetime.js"></script>

    <title>Shop Production Detail Report: Date Range</title>

    <script language="javascript">
        s = screen.width
        h = screen.height
        cw = (s / 2)-150
        ch = (h / 2)-50
        //mywin = window.open("pmess.htm", "what", "left="+cw+", top="+ch+", width=300, height=100, toolbar=no, menubar=no, scrollbar=no")
        mywin.focus()
        //window.print()
        timerID = setTimeout("closeBack()", 500)
        function closeBack(){
            mywin.close()
            //history.go(-1)
        }
        function printSPR(){
            document.getElementById('buttons').style.display = 'none';
            window.print();
            setTimeout("showButtons()",1000)
        }
        function showButtons(){
            document.getElementById('buttons').style.display = 'block';
        }
    </script>
    <style type="text/css">
        .style6 {
            font-size: x-small;
        }
        .style7 {
            border: 1px solid #000000;
            font-size: x-small;
            font-weight: bold;
        }
        input{
            width:100px;
            border-radius:3px;
            border:1px silver solid;
            background-color:#2C5783;
            color:white;
            padding:10px;
            cursor:pointer;
            font-size:14px;
        }

        .style5 {
            font-size: x-small;
            font-weight: bold;
        }
        .style6 {
            font-size: x-small;
        }
        .style7 {
            border: 1px solid #000000;
            font-size: x-small;
            font-weight: bold;
        }
        .auto-style1 {
            color: #FF0000;
        }
        .inputul{
            list-style: none;
        }
        .mt-3{
            margin-top: 10px;
        }

        table{
            margin-top: 25px;
        }
    </style>
</head>

<body style="font-family:Verdana, Geneva, Tahoma, sans-serif" >

<?php
if (!isset($_GET['sd'])){



//$ed = new DateTime('2017-10-13');

//$sdate = '2017-10-09';
//$edate = '2017-10-13';


    ?>
    <main id="main-container" style="padding:50px; background-color:white; color:black;">
        <div class="row" style="width:50%;margin:auto">
            <h3>Select Start and End Dates</h3><br>
            <div class="row">
                <div class="col-md-5">Select Start Date</div>
                <div class="col-md-5"><input type="text" class="form-control input-border" id="sd" name="sd"></div>
            </div>
            <div class="row mt-3">
                <div class="col-md-5">Select End Date</div>
                <div class="col-md-5"><input type="text" class="form-control input-border" id="ed" name="ed"></div>
            </div>
            <div class="row mt-3">
                <div class="col-md-5">
                <nav id="datenav" class="inputnav">
                    <ul class="inputul">
                        <li class="dropdown input-top" >
                            <div id="datestat" style="color:white" class="btn btn-warning" role="button" class="dropdownselected" data-toggle="dropdown">Select Date Range</div>
                            <ul style="width:100%" id="statusselect" class="dropdown-menu">
                                <li id="assembly-li" class="input-li"><a href="#" class="nav-link" onclick="setdates('This Week')">This Week</a></li>
                                <li id="q-check-li" class="input-li"><a href="#" class="nav-link" onclick="setdates('This Month')">This Month</a></li>
                                <li id="final-li" class="input-li"><a href="#" class="nav-link" onclick="setdates('This Year')">This Year</a></li>
                                <li id="inspection-li" class="input-li"><a href="#" class="nav-link" onclick="setdates('Last Week')">Last Week</a></li>
                                <li id="approval-li" class="input-li"><a href="#" class="nav-link active" onclick="setdates('Last Month')">Last Month</a></li>
                                <li id="parts-li" class="input-li"><a href="#" class="nav-link" onclick="setdates('Last Year')">Last Year</a></li>
                            </ul>
                        </li>
                    </ul>
                </nav>
                </div>
                <div class="col-md-5">
                    <button type="button" class="btn btn-primary" onclick="location.href='report.php?sd='+$('#sd').val()+'&ed='+$('#ed').val()">Run Report</button>
                    <button  onclick="location.href='<?= COMPONENTS_PRIVATE ?>/reports/reports.php'" type="button" class="btn btn-default">Done</button>
                </div>
            </div>
        </div>

    </main>

    <?php
} // end if of date prompt
?>
<?php
if (isset($_GET['sd'])){

    $sd = $_GET['sd'];
    $sdate = date_format(new DateTime($sd),'Y-m-d');
    $startdate = new DateTime($_GET['sd']);

    $ed = $_GET['ed'];
    //echo "EndDate is " . $enddate;
    $edate = date_format(new DateTime($ed),'Y-m-d');
    $enddate = new DateTime($_GET['ed']);

    ?>

    <p align="center">
        <strong>Shop Production Detail Report </strong><br/>
        <strong>from&nbsp;<?php echo date_format($startdate,'m/d/Y') ;?> to <?php echo date_format($enddate,'m/d/Y') ;?>
        </strong>&nbsp;<br/>
        This report has been modified to include only Closed Repair Orders.
    <p id="buttons" class="auto-style5">
        <span class="btn btn-primary" id="pbtn" style="float:left;margin-right:5px" onclick="printSPR()">Print</span>
        <span class="btn btn-info" id="donebtn" style="float:left;margin-right:5px" onclick="location.href='<?= COMPONENTS_PRIVATE ?>/reports/reports.php'">Done</span>
        <span class="btn btn-warning" id="excelbtn" style="float:left" onclick="location.href='report.php?excel=y&sd=<?php echo date_format($startdate,'m/d/Y');?>&ed=<?php echo date_format($enddate,'m/d/Y');?>'">Export to Excel</span>

    </p>

    <div><span class="auto-style1"><strong style="padding-left: 10px">Red Date</strong></span> indicates payment not received on RO closing date.</div>

    <?php
    $stmt = "SELECT distinct rotype as type ";
    $stmt .= " FROM rotype ";
    $stmt .= "WHERE shopid = ? ";
    $stmt .= " ORDER BY rotype ";

    if($query = $conn->prepare($stmt)){
        $query->bind_param("s",$shopid);
        $query->execute();
        $rotresult = $query->get_result();
    }else{
        echo "RO Type Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }


    //printf(str_replace("?","'"."%s"."'",$stmt),$shopid);


    while($rot = $rotresult->fetch_array()) {



        $stmt = "SELECT replacerowithtag ";
        $stmt .= "FROM company ";
        $stmt .= "WHERE shopid = ? ";
        //echo $stmt;

        if($query = $conn->prepare($stmt)){
            $query->bind_param("s",$shopid);
            $query->execute();
            $coresult = $query->get_result();
        }else{
            echo "Company Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }

        $co = $coresult->fetch_array();

        $rrwt = strtolower($co["replacerowithtag"]);


        $rotype = $rot["type"];

        $stmt = "SELECT ro.roid,ro.writer,ro.rotype,ro.tagnumber,ro.statusdate,ro.totalprts,ro.totalro,ro.totalsublet,ro.totalfees,ro.discountamt,ro.salestax,ro.customer,ro.fleetno,sum(l.LaborHours) as LaborHrsSold,sum(l.LineTotal) as Laboramt ";
        $stmt .= " FROM repairorders ro ";
        $stmt .= " LEFT JOIN labor l ";
        $stmt .= "   ON ro.shopid = l.shopid and ro.roid = l.ROID ";
        $stmt .= " WHERE ro.shopid = ?";
        $stmt .= "  AND ro.rotype = ? ";
        $stmt .= "  AND ro.`status` = 'Closed' ";
        $stmt .= "  AND ro.statusdate >= ? ";
        $stmt .= "  AND ro.statusdate <= ? ";
        $stmt .= " GROUP BY ro.roid,ro.rotype,ro.tagnumber,ro.statusdate,ro.totalprts,ro.totalro,ro.totalsublet,ro.totalfees,ro.discountamt,ro.salestax,ro.customer,ro.fleetno";


        //echo $query;

        if($query = $conn->prepare($stmt)){
            $query->bind_param("ssss",$shopid,$rotype,$sdate,$edate);
            $query->execute();
            $roresult = $query->get_result();
        }else{
            echo "Repair ORders Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }

        //printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$tech,$sdate,$edate);


        //$ro = $roresult->fetch_array();


        if ($roresult->num_rows > 0) {
            // exit out of detail

            ?>
            <table width=100% cellpadding="3" cellspacing="0">
                <tr>
                    <td width="10%" bgcolor="#C0C0C0" class="style5"><strong>RO Type</strong></td>
                    <td width="5%" bgcolor="#C0C0C0" class="style5"><strong>RO#</strong></td>
                    <td width="20%" bgcolor="#C0C0C0" class="style5"><strong>Service Writer</strong></td>
                    <td width="5%" bgcolor="#C0C0C0" class="style5"><strong>Final_Date</strong></td>
                    <td width="20%" bgcolor="#C0C0C0" class="style5"><strong>Customer</strong></td>
                    <td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Labor</strong></td>
                    <td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Labor Hrs Sold</strong></td>
                    <td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Parts</strong></td>
                    <td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Sublet</strong></td>
                    <td align="right" width="5%" bgcolor="#C0C0C0">
                        <span class="style6"><strong>Fees</strong></span></td>
                    <td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Tax</strong></td>
                    <td align="right" width="5%" bgcolor="#C0C0C0" class="style6" style="width: 0%"><strong>
                            Discount</strong></td>
                    <td align="right" width="5%" bgcolor="#C0C0C0" class="style6" style="width: 2%">
                        <strong>Pmt_Date</strong></td>
                    <td align="right" width="5%" bgcolor="#C0C0C0" class="style6" style="width: 2%">
                        <strong>Pmts</strong></td>
                    <td align="right" width="5%" bgcolor="#C0C0C0" class="style6" style="width: 2%">
                        <strong>Source</strong></td>
                    <td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Total_RO</strong></td>
                    <td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Parts_Cost</strong></td>
                </tr>
                <?php
                $runningpmts = 0;
                $laboramt = 0;
                $labor = 0;
                $parts = 0;
                $sublet = 0;
                $slstax = 0;
                $pcost = 0;
                $ttldisc = 0;
                $totalfees = 0;
                $rrwt = "";
                $tro = 0;
                $dfirstdate = "";

                while($ro = $roresult->fetch_array()) {
                    $roid =  $ro["roid"];

                    $statusdate = new DateTime($ro["statusdate"]);
                    $statusdate = date_format($statusdate,'m/d/Y');

                    $today = new DateTime('now');
                    $today = date_format($today,'m/d/Y');

                    if (strlen($ro["fleetno"]) > 0 ) {
                        $fleetno = " (#" & $ro["fleetno"] .  ")";
                    }else{
                        $fleetno = "";
                    }

                    $stmt = "SELECT coalesce(sum(cost*quantity),0) as pcost ";
                    $stmt .= "FROM parts ";
                    $stmt .= "WHERE shopid = ? ";
                    $stmt .= "  AND roid = ? ";
                    $stmt .= "  AND deleted = 'no'";
                    //echo $stmt;

                    if($query = $conn->prepare($stmt)){
                        $query->bind_param("si",$shopid,$roid);
                        $query->execute();
                        $partresult = $query->get_result();
                    }else{
                        echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    if ($partresult->num_rows > 0) {

                        $part = $partresult->fetch_array();

                        if (is_null($part["pcost"]) ) {
                            $mypcost = 0;
                        }else{
                            $mypcost = $part["pcost"];
                        } // end of is null

                    } // end of parts end if

                    $laboramt = $laboramt + $ro["Laboramt"];
                    $labor = $labor + $ro["LaborHrsSold"];
                    $parts = $parts + $ro["totalprts"];
                    $sublet = $sublet + $ro["totalsublet"];
                    $slstax = $slstax + $ro["salestax"];

                    $pcost = $pcost + $mypcost;
                    $ttldisc = $ttldisc + $ro["discountamt"];
                    $totalfees = $totalfees + $ro["totalfees"];

                    if ($rrwt == "yes") {
                        $displayroid = $ro["tagnumber"];
                    }else{
                        $displayroid = $ro["roid"];
                    }

                    $stmt = "SELECT amt,ptype,id,pdate ";
                    $stmt .= "FROM accountpayments ";
                    $stmt .= "WHERE shopid = ? ";
                    $stmt .= "  AND roid = ? ";
                    //echo $stmt;

                    if($query = $conn->prepare($stmt)){
                        $query->bind_param("si",$shopid,$roid);
                        $query->execute();
                        $apresult = $query->get_result();
                    }else{
                        echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    if ($apresult->num_rows > 0) {
                        $ap = $apresult->fetch_array();

                        $firstamt = $ap["amt"];
                        $firsttype = $ap["ptype"];

                        $firstdate = $ap["pdate"];

                        // display first date
                        if (!empty($ap["pdate"]) ) {
                            $dfirstdate = new DateTime($ap["pdate"]);
                            $dfirstdate = date_format($dfirstdate,'m/d/Y');
                        }else{
                            $dfirstdate = "" ;
                        }

                        $runningpmts = $runningpmts + $ap["amt"];
                    }else{
                        $firstamt = 0;
                        $firsttype = 0;
                        $firstdate = 0;
                        $dfirstdate = "" ;
                    }

                    $totalro = $ro["Laboramt"]+$ro["totalprts"]+$ro["totalsublet"]+$ro["totalfees"]+$ro["salestax"]-$ro["discountamt"];
                    $tro = $tro + $totalro;
                    ?>

                    <tr>
                        <td width="10%" class="style6"><?php echo $ro["rotype"]; ?></td>
                        <td width="5%" class="style6"><?php echo $displayroid; ?></td>
                        <td width="20%" class="style6"><?= $ro["writer"]; ?></td>
                        <td width="5%" class="style6"><?php echo $statusdate; ?></td>
                        <td width="20%" class="style6"><?php echo strtoupper($ro["customer"]) . $fleetno; ?></td>
                        <td align="right" width="5%" class="style6"><?php echo number_format($ro["Laboramt"],2,".",$thousands_sep = ","); ?></td>
                        <td align="right" width="5%" class="style6"><?php echo number_format($ro["LaborHrsSold"],2,".",$thousands_sep = ","); ?></td>

                        <td align="right" width="5%" class="style6"><?php echo number_format($ro["totalprts"],2,".",$thousands_sep = ","); ?></td>
                        <td align="right" width="5%" class="style6"><?php echo number_format($ro["totalsublet"],2,".",$thousands_sep = ","); ?></td>
                        <td align="right" width="5%" class="style6"><?php echo number_format($ro["totalfees"],2,".",$thousands_sep = ","); ?>&nbsp;</td>
                        <td align="right" width="5%" class="style6"><?php echo number_format($ro["salestax"],2,".",$thousands_sep = ","); ?></td>
                        <td align="right" width="5%" class="style6" style="width: 0%"><?php echo number_format($ro["discountamt"],2,".",$thousands_sep = ","); ?>&nbsp;</td>
                        <td align="right" width="5%" class="style6" style="width: 2%;
				<?php if ($firstdate <> $ro["statusdate"]) {echo "font-weight:bold;color:red" ;}?>">
                            <?php
                            echo $dfirstdate;
                            ?>
                        </td>

                        <td align="right" width="5%" class="style6" style="width: 2%"><?php echo number_format($firstamt,2,".",$thousands_sep = ","); ?>&nbsp;</td>
                        <td align="right" width="5%" class="style6" style="width: 2%"><?php echo strtoupper(($firsttype ? $firsttype : "")); ?>&nbsp;</td>
                        <td align="right" width="5%" class="style6"><?php echo number_format($totalro,2,".",$thousands_sep = ","); ?></td>
                        <td align="right" width="5%" class="style6"><?php echo number_format($mypcost,2,".",$thousands_sep = ","); ?></td>
                    </tr>

                    <?php

                    if ($apresult->num_rows > 0) {
                        while($ap = $apresult->fetch_array()) {
                            $runningpmts = $runningpmts + $ap["amt"];
                            ?>
                            <tr>
                                <td width="5%" class="style6"></td>
                                <td width="5%" class="style6"></td>
                                <td width="5%" class="style6"></td>
                                <td width="20%" class="style6"></td>
                                <td width="20%" class="style6"></td>
                                <td align="right" width="5%" class="style6"></td>
                                <td align="right" width="5%" class="style6"></td>
                                <td align="right" width="5%" class="style6"></td>
                                <td align="right" width="5%" class="style6">&nbsp;</td>
                                <td align="right" width="5%" class="style6"></td>
                                <td align="right" width="5%" class="style6" style="width: 0%"></td>
                                <td align="right" width="5%" class="style6" style="width: 2%">&nbsp;</td>
                                <td align="right" width="5%" class="style6" style="width: 2%"><?php echo number_format($ap["amt"],2); ?>&nbsp;</td>
                                <td align="right" width="5%" class="style6" style="width: 2%"><?php echo $ap["ptype"]; ?>&nbsp;</td>
                                <td align="right" width="5%" class="style6"></td>
                                <td align="right" width="5%" class="style6"></td>
                            </tr>
                            <?php

                        } // end of ap while

                    } // end of ap result end if

                } // end of while




                ?>

                <tr>
                    <td width="5%" class="style5">Totals</td>
                    <td width="5%"></td>
                    <td width="5%"></td>
                    <td width="20%"></td>
                    <td width="20%"></td>
                    <td align="right" width="5%" class="style7"><?php echo number_format($laboramt,2,".",$thousands_sep = ","); ?></td>
                    <td align="right" width="5%" class="style7"><?php echo number_format($labor,2,".",$thousands_sep = ","); ?></td>
                    <td align="right" width="5%" class="style7"><?php echo number_format($parts,2,".",$thousands_sep = ","); ?></td>
                    <td align="right" width="5%" class="style7"><?php echo number_format($sublet,2,".",$thousands_sep = ","); ?></td>
                    <td align="right" width="5%" class="style7"><?php echo number_format($totalfees,2,".",$thousands_sep = ","); ?></td>
                    <td align="right" width="5%" class="style7"><?php echo number_format($slstax,2,".",$thousands_sep = ","); ?></td>
                    <td align="right" width="5%" class="style7" style="width: 0%"><?php echo number_format($ttldisc,2,".",$thousands_sep = ","); ?></td>
                    <td align="right" width="5%" class="style7" style="width: 2%">&nbsp;</td>
                    <td align="right" width="5%" class="style7" style="width: 2%"><?php echo number_format($runningpmts,2,".",$thousands_sep = ","); ?>
                        &nbsp;</td>
                    <td align="right" width="5%" class="style7"></td>
                    <td align="right" width="5%" class="style7"><?php echo number_format($tro,2,".",$thousands_sep = ","); ?></td>
                    <td align="right" width="5%" class="style7"><?php echo number_format($pcost,2,".",$thousands_sep = ","); ?></td>
                </tr>

            </table>
            <?php
        } // end if for empty result set from rotype lookup


    } // end of while for rotype

} // end of isset
?>

<script>

    function setdates(t){
        <?php
        echo "\r\nvar lw = '".date("m/d/Y",strtotime("last week monday"))."|".date("m/d/Y",strtotime("last week sunday"))."';\r\n";
        echo "var lm = '".date("m/d/Y",strtotime("first day of previous month"))."|".date("m/d/Y",strtotime("last day of previous month"))."';\r\n";
        $ly = date("Y") - 1;
        echo "var ly = '".date("m/d/Y",strtotime("01/01/".$ly))."|".date("m/d/Y",strtotime("12/31/".$ly))."';\r\n";
        echo "var tw = '".date("m/d/Y",strtotime("this week monday"))."|".date("m/d/Y",strtotime("this week sunday"))."';\r\n";
        echo "var tm = '".date("m/d/Y",strtotime("first day of this month"))."|".date("m/d/Y",strtotime("last day of this month"))."';\r\n";
        $ty = date("Y");
        echo "var ty = '".date("m/d/Y",strtotime("01/01/".$ty))."|".date("m/d/Y",strtotime("12/31/".$ty))."';\r\n";
        ?>

        if (t == "This Week"){
            tar = tw.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "This Month"){
            tar = tm.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "This Year"){
            tar = ty.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "Last Week"){
            tar = lw.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "Last Month"){
            tar = lm.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "Last Year"){
            tar = ly.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
    }
    $('#sd').datetimepicker({
        ignoreReadonly: true,
        useCurrent: true,
        format: "MM/DD/Y"

    })

    $('#ed').datetimepicker({
        ignoreReadonly: true,
        useCurrent: true,
        format: "MM/DD/Y"

    })

</script>
</body>

</html>
