<?php
// Page Variables
$title = 'Cash In Report';  // Report Title Goes Here
$subtitle = 'Summary and Detail';  // Report SubTitle Goes Here - Hide if not needed

// Use this for Custom Reports
require(COMPONENTS_PRIVATE_PATH . "/reports/includes/header_reports.php");
require(CONN);


// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$pmethod = isset($_GET['method']) ? $_GET['method'] : 'all';
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));

// Use this for Custom Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_multiple.php';
?>

<title><?= $title; ?></title>  <!-- Meta title for page. Change in variable above-->

<!DOCTYPE html>
<html>
<body>
<style>
    .summary {
        padding-left: 250px;
        padding-right: 250px;
    }

    td {
        max-width: 25px;
    }

    .table_margin {
        margin: 15px;
    }

    @media print {
        table {
            display: inline-table;
        }

        tbody {
            height: 100% !important;
        }

        td {
            max-width: 25px;
        }

    }

</style>


<?php

// Use this for Gen Pop Reports
//include("../php/includes/reports/report_buttons.php");

// Use this for Custom Reports
include(COMPONENTS_PRIVATE_PATH . "/reports/includes/report_buttons.php");
?>

<!-- Column Headers Insert Report Variables here -->
<div class="table_margin summary">
    <h4 style="text-align:center">Summary</h4>
    <table class="report_table summary_t2 cash_in_print">
        <tr class="table_header table_head cash_in_print">
            <td>Payment Method</td>
            <td style="text-align:center">Count</td>
            <td style="text-align:right">Total Amount</td>
        </tr>
        <?php
        //    $tablefields = array('Payment Method', 'Count', 'Total Amount', '');//set table headings array for excel export
        $alldata = array();//this will hold all the data arrays to be exported to excel
        $alldata[] = array('TABLETITLE', 'Summary', '', '', '', '');
        $alldata[] = array('TABLEHEAD', 'Payment Method', 'Count', 'Total Amount', '', '');
        $tapcount = 0;
        $tapamount = 0;

        // Insert DB Query Here

        // Template Query Begins - Replace entire section
        if ($pmethod == "all") {
            $stmt = "SELECT * FROM paymentmethods WHERE shopid = ? ";
        } else {
            $stmt = "SELECT * FROM paymentmethods WHERE shopid = ? AND method = '$pmethod'";
        }

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $pmresult = $query->get_result();
        } else {
            echo "Payment Methods Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }


        if ($pmresult->num_rows > 0) {
            while ($pm = $pmresult->fetch_array()) {
                $ptype = $pm["method"];

                $stmt = "SELECT sum(amt) as a, count(*) as c ";
                $stmt .= " FROM accountpayments ";
                $stmt .= "WHERE shopid = ? ";
                $stmt .= "  AND ptype = ? ";
                $stmt .= "  AND pdate >= ? And pdate <= ? ";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("ssss", $shopid, $ptype, $sd1, $ed1);
                    $query->execute();
                    $apresult = $query->get_result();
                } else {
                    echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                //printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$ptype,$sdate,$edate);

                $ap = $apresult->fetch_array();

                if ($apresult->num_rows > 0) {

                    if (!is_null($ap["a"])) {
                        $apamount = $ap["a"];
                    } else {
                        $apamount = 0;
                    }
                    if (!is_null($ap["c"])) {
                        $apcount = (int)$ap["c"];
                    } else {
                        $apcount = 0;
                    }
                }
                $stmt = "SELECT sum(amt) as a, count(*) as c ";
                $stmt .= " FROM `accountpayments-ps` ";
                $stmt .= "WHERE shopid = ? ";
                $stmt .= "  AND ptype = ? ";
                $stmt .= "  AND pdate >= ? And pdate <= ? ";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("ssss", $shopid, $ptype, $sd1, $ed1);
                    $query->execute();
                    $apsresult = $query->get_result();
                } else {
                    echo "Account Payments PS Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $aps = $apsresult->fetch_array();

                if ($apsresult->num_rows > 0) {

                    if (!is_null($aps["a"])) {
                        $apamount = $apamount + $aps["a"];
                    } else {
                        $apamount = $apamount + (int)0;
                    }
                    if (!is_null($aps["c"])) {
                        $apcount = $apcount + (int)$aps["c"];
                    } else {
                        $apcount = $apcount + (int)0;
                    }
                }
                $tapcount = $tapcount + $apcount;
                $tapamount = $tapamount + $apamount;

                ?>
                <tr>
                    <td style="padding-top:1px; padding-left:10px;"><?= strtoupper($pm["method"]); ?></td>
                    <td style="padding-top:1px; text-align:center"><?= $apcount; ?></td>
                    <td style="padding-top:1px; text-align:right; padding-right:10px;"><?= asDollars($apamount, 2); ?></td>
                </tr>
                <?php
                $alldata[] = array(strtoupper($pm["method"]), $apcount, asDollars($apamount), '', '', '');

            } // while loop
        } // end if pm
        ?>
        <tr>
            <td><strong>Total: </strong></td>
            <td style="text-align:center"><strong><?= $tapcount; ?></strong></td>
            <td style="text-align:right; padding-right:10px;"><strong><?= asDollars($tapamount); ?></strong></td>
        </tr>

        <?php
        $alldata[] = array('TOTALS', $tapcount, asDollars($tapamount), '', '', '');//fill up the alldata array with the arrays of data to be shown in excel export
        //  } // end of while for loop
        // end if for end of file


        ?>
    </table>
</div>

<?php
$stmt = "SELECT roid,tagnumber,statusdate ,totalprts,totallbr,totalro,totalsublet,totalfees,discountamt,salestax,customer,fleetno FROM repairorders WHERE shopid = ? AND `status` = 'Closed' AND statusdate >= ? AND repairorders.rotype != 'No Approval' AND statusdate <= ? order by statusdate desc";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("sss", $shopid, $sd1, $ed1);
    $query->execute();
    $results = $query->get_result();
    $query->close();
} else {
    die ("prepare failed " . $conn->error);
}
$dailyROSales = array();
$ARPayments = array();

if ($results->num_rows > 0) {
    while ($ro = $results->fetch_assoc()) {
        $roid = $ro['roid'];
        //echo $roid." <br />";
        $statusdate = date('m/d/Y', strtotime($ro['statusdate']));
        if ($pmethod == "all") {
            $stmt = "SELECT amt,ptype,id,pdate FROM accountpayments WHERE shopid = ? AND roid = ?";
        } else {
            $stmt = "SELECT amt,ptype,id,pdate FROM accountpayments WHERE shopid = ? AND roid = ? AND ptype = '$pmethod'";
        }
        //echo $stmt;
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("si", $shopid, $roid);
            $query->execute();
            $apresult = $query->get_result();
            $query->close();
        } else {
            die ("Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error);
        }

        if ($apresult->num_rows > 0) {
            $ap = $apresult->fetch_array();
            $firstamt = $ap["amt"];
            $firsttype = $ap["ptype"];
            $firstdate = $ap["pdate"];
            // display first date
            //build an array based on payment date matches RO date or not
            if (!empty($ap["pdate"])) {
                $dfirstdate = new DateTime($ap["pdate"]);
                $dfirstdate = date_format($dfirstdate, 'm/d/Y');
            } else {
                $dfirstdate = "";
            }
            $runningpmts = $runningpmts + $ap["amt"];
        } else {
            $firstamt = 0;
            $firsttype = "";
            $firstdate = 0;
            $dfirstdate = "";
        }

        if ( $firstamt != 0 && $firstdate != 0 ){
            $dailyROSales[] = array(
                'statusdate' => $ro['statusdate'],
                'roid' => $ro['roid'],
                'totalro' => $ro['totalro'],
                'pmtdate' => $firstdate,
                'pmt' => $firstamt,
                'ptype' => $firsttype
            );
        }else{
            $ARPayments[] = array(
                'statusdate' => $ro['statusdate'],
                'roid' => $ro['roid'],
                'totalro' => $ro['totalro'],
                'pmtdate' => $firstdate,
                'pmt' => $firstamt,
                'ptype' => $firsttype
            );
        }
        
    } // end while

} // end if
$alldata[] = array('', '', '', '', '', '');
$alldata[] = array('TABLETITLE', 'Daily RO Sales', '', '', '', '', '');
$alldata[] = array('TABLEHEAD', 'Status Date', 'RO #', 'Payment Date', 'Payment', 'Source', 'Total Sales');
?>
<div class="row">
    <div class="col-sm-6">
        <h4 style="text-align: center">Daily RO Sales</h4>
        <table class="report_table">
            <tr class="table_header">
                <td>Status Date</td>
                <td>RO #</td>
                <td>Payment Date</td>
                <td>Payment</td>
                <td>Source</td>
                <td>Total Sales</td>
            </tr>
            <?php
            foreach ($dailyROSales as $sales) {
                ?>
                <tr>
                    <td><?= date('m/d/Y', strtotime($sales['statusdate'])) ?></td>
                    <td><?= $sales['roid'] ?></td>
                    <td><?= $sales['pmtdate'] != 0 ? date('m/d/Y', strtotime($sales['pmtdate'])) : "" ?></td>
                    <td><?= asDollars($sales['pmt']) ?></td>
                    <td><?= strtoupper($sales['ptype']) ?></td>
                    <td><?= asDollars($sales['totalro']) ?></td>
                </tr>
                <?php
                $alldata[] = array(date('m/d/Y', strtotime($sales['statusdate'])), $sales['roid'], $sales['pmtdate'] != 0 ? date('m/d/Y', strtotime($sales['pmtdate'])) : "", asDollars($sales['pmt']), strtoupper($sales['ptype']), asDollars($sales['totalro']));
            }
            ?>
        </table>
    </div>
    <?php
    $alldata[] = array('', '', '', '', '', '');
    $alldata[] = array('TABLETITLE', 'A/R Payments', '', '', '', '', '');
    $alldata[] = array('TABLEHEAD', 'Status Date', 'RO #', 'Payment Date', 'Payment', 'Source', 'Total Sales');
    ?>
    <div class="col-sm-6">
        <h4 style="text-align: center">A/R Payments</h4>
        <table class="report_table">
            <tr class="table_header">
                <td>Status Date</td>
                <td>RO #</td>
                <td>Payment Date</td>
                <td>Payment</td>
                <td>Source</td>
                <td>Total Sales</td>
            </tr>
            <?php
            foreach ($ARPayments as $sales) {
                ?>
                <tr>
                    <td><?= date('m/d/Y', strtotime($sales['statusdate'])) ?></td>
                    <td><?= $sales['roid'] ?></td>
                    <td><?= $sales['pmtdate'] != 0 ? date('m/d/Y', strtotime($sales['pmtdate'])) : "" ?></td>
                    <td><?= asDollars($sales['pmt']) ?></td>
                    <td><?= strtoupper($sales['ptype']) ?></td>
                    <td><?= asDollars($sales['totalro']) ?></td>
                </tr>
                <?php
                $alldata[] = array(date('m/d/Y', strtotime($sales['statusdate'])), $sales['roid'], $sales['pmtdate'] != 0 ? date('m/d/Y', strtotime($sales['pmtdate'])) : "", asDollars($sales['pmt']), strtoupper($sales['ptype']), asDollars($sales['totalro']));
            }
            ?>
        </table>
    </div>
</div>
<?php
$alldata[] = array('', '', '', '', '', '');

$alldata[] = array('TABLETITLE', 'Part Sales Details', '', '', '', '');

$alldata[] = array('TABLEHEAD', 'PS #', 'Date Received', 'Payment Type', 'Ref. Number', 'Amount');
?>
<div class="table_margin table_detail">
    <h4 style="text-align: center">Part Sales Details</h4>
    <table class="report_table summary_t2">
        <tr class="table_header table_head">
            <td>PS #</td>
            <td>Date Received</td>
            <td>Payment Type</td>
            <td>Ref. Number</td>
            <td style="text-align:right">Amount</td>
        </tr>

        <?php
        if ($pmethod == "all") {
            $stmt = "SELECT * FROM `accountpayments-ps` WHERE shopid = ? ";
            $stmt .= "  AND pdate >= ? And pdate <= ? order by pdate, psid asc";
        } else {
            $stmt = "SELECT *";
            $stmt .= " FROM `accountpayments-ps` ";
            $stmt .= "WHERE shopid = ? ";
            $stmt .= "  AND pdate >= ? And pdate <= ? AND ptype = '$pmethod' order by pdate, psid asc";
        }

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("sss", $shopid, $sd1, $ed1);
            $query->execute();
            $apsresult = $query->get_result();
        } else {
            echo "Account Payments for Part Sales Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }

        if ($apsresult->num_rows > 0) {
            while ($aps = $apsresult->fetch_array()) {

                $pdate = new Datetime($aps["pdate"]);

                ?>
                <tr>
                    <td><?php echo "PS " . $aps["psid"]; ?>&nbsp;</td>
                    <td><?php echo date_format($pdate, 'm/d/Y'); ?></td>
                    <td><?php echo strtoupper($aps["ptype"]); ?></td>
                    <td><?= $aps["pnumber"]; ?></td>
                    <td style="text-align:right"><?php echo asDollars($aps["amt"]); ?></td>
                </tr>


                <?php

                $alldata[] = array("PS " . $aps["psid"], date_format($pdate, 'm/d/Y'), strtoupper($aps["ptype"]), $aps["pnumber"], asDollars($aps["amt"]));

            } // end while

        } // end if

        ?>

    </table>
</div>
<?php

// Use this for Gen Pop Reports
//include("../php/includes/reports/footer_reports.php");
//include("../php/includes/reports/report_form.php");

// Use this for Custom Reports
include(COMPONENTS_PRIVATE_PATH . "/reports/includes/footer_reports.php");
include(COMPONENTS_PRIVATE_PATH . "/reports/includes/report_form.php");
?>


</body>
</html>
<?php
if (isset($conn)) {
    mysqli_close($conn);
}
?>
