<?php

// Use this for Gen Pop Reports
//require("../php/includes/reports/header_reports.php");
//require("../php/conn.php");
//require("../php/functions.php");

// Use this for Custom Reports
require(COMPONENTS_PRIVATE_PATH."/reports/includes/header_reports.php");
require(CONN);
//require("../../functions.php");


// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));
$ref = trim($_GET['ref']) ?? '';


// Page Variables
$title = 'Cash In Report';  // Report Title Goes Here
$subtitle = 'Summary and Detail';  // Report SubTitle Goes Here - Hide if not needed

// Use this for Gen Pop Reports
//$template = 'templates/excelexport_multiple.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport_multiple.php';
?>

<title><?= $title; ?></title>  <!-- Meta title for page. Change in variable above-->

<!DOCTYPE html>
<html>
<body>
<style>
    .summary {
        padding-left: 250px;
        padding-right: 250px;
    }

    td {
        max-width: 25px;
    }

    .table_margin {
        margin: 15px;
    }

    @media print {
        table {
            display: inline-table;
        }

        tbody {
            height: 100% !important;
        }

        td {
            max-width: 25px;
        }

    }

</style>


<?php

// Use this for Gen Pop Reports
//include("../php/includes/reports/report_buttons.php");

// Use this for Custom Reports
include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_buttons.php");
?>

<!-- Column Headers Insert Report Variables here -->
<div class="table_margin summary">
    <h4 style="text-align:center">Summary</h4>
    <table class="report_table summary_t2 cash_in_print">
        <tr class="table_header table_head cash_in_print">
            <td>Payment Method</td>
            <td style="text-align:center">Count</td>
            <td style="text-align:right">Total Amount</td>
        </tr>
        <?php
        $tablefields = array('Payment Method', 'Count', 'Total Amount', '');//set table headings array for excel export
        $alldata = array();//this will hold all the data arrays to be exported to excel

        $tapcount = 0;
        $tapamount = 0;

        // Insert DB Query Here

        // Template Query Begins - Replace entire section

        $stmt = "SELECT * ";
        $stmt .= " FROM paymentmethods ";
        $stmt .= "WHERE shopid = ? ";

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $pmresult = $query->get_result();
        } else {
            echo "Payment Methods Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }


        if ($pmresult->num_rows > 0) {
            while ($pm = $pmresult->fetch_array()) {
                $ptype = $pm["method"];

                $stmt = "SELECT sum(amt) as a, count(*) as c ";
                $stmt .= " FROM accountpayments ";
                $stmt .= "WHERE shopid = ? ";
                $stmt .= "  AND ptype = ? ";
                $stmt .= "  AND pdate >= ? And pdate <= ? ";
                if (!empty($ref)) $stmt .= " AND pnumber = ?";

                if ($query = $conn->prepare($stmt)) {
                    if (!empty($ref)) {
                        $query->bind_param("sssss", $shopid, $ptype, $sd1, $ed1, $ref);
                    } else {
                        $query->bind_param("ssss", $shopid, $ptype, $sd1, $ed1);
                    }
                    $query->execute();
                    $apresult = $query->get_result();
                } else {
                    echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                //printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$ptype,$sdate,$edate);

                $ap = $apresult->fetch_array();

                if ($apresult->num_rows > 0) {

                    if (!is_null($ap["a"])) {
                        $apamount = $ap["a"];
                    } else {
                        $apamount = 0;
                    }
                    if (!is_null($ap["c"])) {
                        $apcount = (int)$ap["c"];
                    } else {
                        $apcount = 0;
                    }
                }
                $stmt = "SELECT sum(amt) as a, count(*) as c ";
                $stmt .= " FROM `accountpayments-ps` ";
                $stmt .= "WHERE shopid = ? ";
                $stmt .= "  AND ptype = ? ";
                $stmt .= "  AND pdate >= ? And pdate <= ? ";
                if (!empty($ref)) $stmt .= " AND pnumber = ?";

                if ($query = $conn->prepare($stmt)) {
                    if (!empty($ref)) {
                        $query->bind_param("sssss", $shopid, $ptype, $sd1, $ed1, $ref);
                    } else {
                        $query->bind_param("ssss", $shopid, $ptype, $sd1, $ed1);
                    }
                    $query->execute();
                    $apsresult = $query->get_result();
                } else {
                    echo "Account Payments PS Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $aps = $apsresult->fetch_array();

                if ($apsresult->num_rows > 0) {

                    if (!is_null($aps["a"])) {
                        $apamount = $apamount + $aps["a"];
                    } else {
                        $apamount = $apamount + (int)0;
                    }
                    if (!is_null($aps["c"])) {
                        $apcount = $apcount + (int)$aps["c"];
                    } else {
                        $apcount = $apcount + (int)0;
                    }
                }
                $tapcount = $tapcount + $apcount;
                $tapamount = $tapamount + $apamount;

                ?>
                <tr>
                    <td style="padding-top:1px; padding-left:10px;"><?= strtoupper($pm["method"]); ?></td>
                    <td style="padding-top:1px; text-align:center"><?= $apcount; ?></td>
                    <td style="padding-top:1px; text-align:right; padding-right:10px;"><?= asDollars($apamount, 2); ?></td>
                </tr>
                <?php
                $alldata[] = array(strtoupper($pm["method"]), $apcount, asDollars($apamount), '');

            } // while loop
        } // end if pm
        ?>
        <tr>
            <td><strong>Total: </strong></td>
            <td style="text-align:center"><strong><?= $tapcount; ?></strong></td>
            <td style="text-align:right; padding-right:10px;"><strong><?= asDollars($tapamount); ?></strong></td>
        </tr>

        <?php
        $alldata[] = array('TOTALS', $tapcount, asDollars($tapamount), '');//fill up the alldata array with the arrays of data to be shown in excel export
        //  } // end of while for loop
        // end if for end of file


        $alldata[] = array('', '', '', '');

        $alldata[] = array('TABLETITLE', 'Repair Order Details', '', '');

        $alldata[] = array('TABLEHEAD', 'RO #', 'Date Received', 'Payment Type', 'Ref. Number', 'Amount');

        ?>
    </table>
</div>

<div class="table_margin table_detail">
    <h4 style="text-align: center">Repair Order Details</h4>
    <div class="row">
        <?php
            $stmt = "SELECT roid, statusdate, totalro from repairorders where shopid = ? AND StatusDate >= ? AND StatusDate <= ?";
            if($query = $conn->prepare($stmt)){
                $query->bind_param("sss", $shopid, $sd1, $ed1);
                $query->execute();
                $results = $query->get_result();
                $query->close();
            } else {
                die ("prepare failed ".$conn->error);
            }
        ?>
        <div class="col-md-6">
            <table class="report_table summary_t2">
                <tr class="table_header table_head">
                    <td> RO #</td>
                    <td>Date Received</td>
                    <td>Payment Type</td>
                    <td>Ref. Number</td>
                    <td style="text-align:right">Amount</td>
                </tr>
                <?php
                $stmt = "SELECT roid,pdate,ptype,amt,pnumber";
                $stmt .= " FROM accountpayments ";
                $stmt .= "WHERE shopid = ? ";
                if (!empty($ref)) $stmt .= " AND pnumber = ?";
                $stmt .= "  AND pdate >= ? And pdate <= ? order by pdate, roid asc";

                if ($query = $conn->prepare($stmt)) {
                    if (!empty($ref)) {
                        $query->bind_param("ssss", $shopid, $ref, $sd1, $ed1);
                    } else {
                        $query->bind_param("sss", $shopid, $sd1, $ed1);
                    }
                    $query->execute();
                    $apresult = $query->get_result();
                } else {
                    echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                ?>

                <?php
                if ($apresult->num_rows > 0) {
                    while ($ap = $apresult->fetch_array()) {

                        $pdate = new Datetime($ap["pdate"]);

                        ?>
                        <tr>
                            <td><?= $ap["roid"]; ?>&nbsp;</td>
                            <td><?= date_format($pdate, 'm/d/Y'); ?></td>
                            <td><?= strtoupper($ap["ptype"]); ?></td>
                            <td><?= $ap["pnumber"]; ?></td>
                            <td style="text-align:right"><?= asDollars($ap["amt"]); ?></td>
                        </tr>


                        <?php

                        $alldata[] = array($ap["roid"], date_format($pdate, 'm/d/Y'), strtoupper($ap["ptype"]), $ap["pnumber"], asDollars($ap["amt"]));

                    } // end while

                } // end if

                ?>
            </table>
        </div>
        <div class="col-md-6">
            <table class="report_table summary_t2">
                <tr class="table_header table_head">
                    <td> RO #</td>
                    <td>Date Received</td>
                    <td>Payment Type</td>
                    <td>Ref. Number</td>
                    <td style="text-align:right">Amount</td>
                </tr>
                <?php
                $stmt = "SELECT roid,pdate,ptype,amt,pnumber";
                $stmt .= " FROM accountpayments ";
                $stmt .= "WHERE shopid = ? ";
                if (!empty($ref)) $stmt .= " AND pnumber = ?";
                $stmt .= "  AND pdate >= ? And pdate <= ? order by pdate, roid asc";

                if ($query = $conn->prepare($stmt)) {
                    if (!empty($ref)) {
                        $query->bind_param("ssss", $shopid, $ref, $sd1, $ed1);
                    } else {
                        $query->bind_param("sss", $shopid, $sd1, $ed1);
                    }
                    $query->execute();
                    $apresult = $query->get_result();
                } else {
                    echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                ?>

                <?php
                if ($apresult->num_rows > 0) {
                    while ($ap = $apresult->fetch_array()) {

                        $pdate = new Datetime($ap["pdate"]);

                        ?>
                        <tr>
                            <td><?= $ap["roid"]; ?>&nbsp;</td>
                            <td><?= date_format($pdate, 'm/d/Y'); ?></td>
                            <td><?= strtoupper($ap["ptype"]); ?></td>
                            <td><?= $ap["pnumber"]; ?></td>
                            <td style="text-align:right"><?= asDollars($ap["amt"]); ?></td>
                        </tr>


                        <?php

                        $alldata[] = array($ap["roid"], date_format($pdate, 'm/d/Y'), strtoupper($ap["ptype"]), $ap["pnumber"], asDollars($ap["amt"]));

                    } // end while

                } // end if

                ?>
            </table>
        </div>
    </div>
</div>

<?php
$alldata[] = array('', '', '', '', '');

$alldata[] = array('TABLETITLE', 'Part Sales Details', '', '');

$alldata[] = array('TABLEHEAD', 'PS #', 'Date Received', 'Payment Type', 'Ref. Number', 'Amount');
?>
<div class="table_margin table_detail">
    <h4 style="text-align: center">Part Sales Details</h4>
    <table class="report_table summary_t2">
        <tr class="table_header table_head">
            <td>PS #</td>
            <td>Date Received</td>
            <td>Payment Type</td>
            <td>Ref. Number</td>
            <td style="text-align:right">Amount</td>
        </tr>

        <?php
        $stmt = "SELECT *";
        $stmt .= " FROM `accountpayments-ps` ";
        $stmt .= "WHERE shopid = ? ";
        if (!empty($ref)) $stmt .= " AND pnumber = ?";
        $stmt .= "  AND pdate >= ? And pdate <= ? order by pdate, psid asc";

        if ($query = $conn->prepare($stmt)) {
            if (!empty($ref)) {
                $query->bind_param("ssss", $shopid, $ref, $sd1, $ed1);
            } else {
                $query->bind_param("sss", $shopid, $sd1, $ed1);
            }
            $query->execute();
            $apsresult = $query->get_result();
        } else {
            echo "Account Payments for Part Sales Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }

        if ($apsresult->num_rows > 0) {
            while ($aps = $apsresult->fetch_array()) {

                $pdate = new Datetime($aps["pdate"]);

                ?>
                <tr>
                    <td><?php echo "PS " . $aps["psid"]; ?>&nbsp;</td>
                    <td><?php echo date_format($pdate, 'm/d/Y'); ?></td>
                    <td><?php echo strtoupper($aps["ptype"]); ?></td>
                    <td><?= $aps["pnumber"]; ?></td>
                    <td style="text-align:right"><?php echo asDollars($aps["amt"]); ?></td>
                </tr>


                <?php

                $alldata[] = array("PS " . $aps["psid"], date_format($pdate, 'm/d/Y'), strtoupper($aps["ptype"]), $aps["pnumber"], asDollars($aps["amt"]));

            } // end while

        } // end if

        ?>

    </table>
</div>
<?php

// Use this for Gen Pop Reports
//include("../php/includes/reports/footer_reports.php");
//include("../php/includes/reports/report_form.php");

// Use this for Custom Reports
include(COMPONENTS_PRIVATE_PATH."/reports/includes/footer_reports.php");
include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_form.php");
?>


</body>
</html>
<?php
if (isset($conn)) {
    mysqli_close($conn);
}
?>
