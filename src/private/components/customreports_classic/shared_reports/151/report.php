<?php
$title = 'Part Sale Report';  // Report Title Goes Here

include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_config.php');

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1=date('Y-m-d',strtotime($sd));
$ed1=date('Y-m-d',strtotime($ed));


// Page Variables

//$subtitle = 'my subtitle';  // Report SubTitle Goes Here - Hide if not needed

// Use this for Gen Pop Reports
//$template = 'templates/excelexport_partsale.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport.php';
?>

<title><?= $title;?></title>  <!-- Meta title for page. Change in variable above-->

<!DOCTYPE html>
<html>
<body>



	<?php
include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_buttons.php');
	?>

<!-- Column Headers Insert Report Variables here -->

	<table class=report_table>
		<thead>
	<tr class="table_header table_head">
		<td>PS #</td>
		<td>Status</td>
		<td>Writer</td>
		<td>Customer</td>
		<td>Date</td>
		<td>Subtotal</td>
		<td>Tax</td>
		<td>Total</td>
		<td>Amt Paid</td>
	</tr>
</thead>

    <?php
    $tablefields=array('PS #','Status', 'Writer', 'Customer','Date','Subtotal','Tax','Total','Amt Paid');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel


// Insert DB Query Here
// Template Query Begins - Replace entire section
	$gtpay = 0;
	$gttps = 0;
	$stmt = "select ps.psid, ps.statusdate,ps.writer, ps.subtotal, ps.tax, ps.total, ps.status,customer.firstname,customer.lastname from ps,customer where ps.cid=customer.customerid and ps.shopid = '$shopid' and customer.shopid='$shopid' and ps.statusdate >= ? and ps.statusdate <= ? ";
	if($query = $conn->prepare($stmt)){
		$query->bind_param("ss",$sd1,$ed1);
		$query->execute();
		$roresult = $query->get_result();
	}else{
		echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
		while($ro = $roresult->fetch_array()) {
			$psid = $ro["psid"];
			$stmt = "SELECT coalesce(sum(amt)) as amt FROM `accountpayments-ps` WHERE shopid = ? AND psid = ?";
			if($query = $conn->prepare($stmt)){
				$query->bind_param("ss",$shopid,$psid);
				$query->execute();
                $query->bind_result($amt);
                $query->fetch();
                $query->close();
			}else{
				echo "AP Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
			$gtpay += $amt;
			$gttps += $ro["total"];
?>
	<tr>
		<td><?= $psid; ?></td>
		<td><?= strtoupper($ro["status"]); ?></td>
		<td><?= strtoupper($ro["writer"]); ?></td>
		<td><?= strtoupper($ro["firstname"]. " " . $ro["lastname"]); ?></td>
		<td><?= date('m/d/Y', strtotime($ro["statusdate"])); ?></td>
		<td><?= asDollars($ro["subtotal"]); ?></td>
		<td><?= asDollars($ro["tax"]); ?></td>
		<td><?= asDollars($ro["total"]); ?></td>
		<td><?= asDollars($amt); ?></td>
	</tr>
	    <?php
			$alldata[]=array($ro["psid"], strtoupper($ro["status"]), strtoupper($ro["writer"]), strtoupper($ro["firstname"]. " " . $ro["lastname"]), date('m/d/Y', strtotime($ro["statusdate"])), asDollars($ro["subtotal"]), asDollars($ro["tax"]), asDollars($ro["total"]), asDollars($amt));//fill up the alldata array with the arrays of data to be shown in excel export
	    } // end of while for loop
	     // end if for end of file
			$alldata[]=array('','','','','','','','');
			$alldata[]=array('TOTALS','','','','','','',asDollars($gttps),asDollars($gtpay));
	    ?>
	<tr class="table_total">
		<td colspan="7"><b>TOTALS</b></td>
		<td><b><?= asDollars($gttps); ?></b></td>
		<td><b><?= asDollars($gtpay); ?></b></td>
	</tr>

	</table>

		<?php
			include(COMPONENTS_PRIVATE_PATH. '/reports/includes/footer_reports.php');
			include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_form.php');

		?>


</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
