<!DOCTYPE html>
<?php
require CONN;
$shopid = $_COOKIE['shopid'];

if ($_COOKIE["reportaccess"] == "yes") {

$stmt = "select shopnotice from company where shopid = ?";

if ($query = $conn->prepare($stmt)){

	$query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($shopnotice);
    $query->fetch();
    $query->close();

}else{
	echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$date = date('m/d/Y');
?>
<!--[if IE 9]>         <html class="ie9 no-focus"> <![endif]-->
<!--[if gt IE 9]><!--> <html class="no-focus"> <!--<![endif]-->
    <head>
        <meta charset="utf-8">

         <title><?= getPageTitle() ?></title>

        <meta name="robots" content="noindex, nofollow">
        <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
		<link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon'/ >
        <!-- Icons -->
        <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->

        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-16x16.png" sizes="16x16">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-32x32.png" sizes="32x32">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-96x96.png" sizes="96x96">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-160x160.png" sizes="160x160">
        <link rel="icon" type="image/png" href="<?= IMAGE ?>/favicons/favicon-192x192.png" sizes="192x192">

        <link rel="apple-touch-icon" sizes="57x57" href="<?= IMAGE ?>/favicons/apple-touch-icon-57x57.png">
        <link rel="apple-touch-icon" sizes="60x60" href="<?= IMAGE ?>/favicons/apple-touch-icon-60x60.png">
        <link rel="apple-touch-icon" sizes="72x72" href="<?= IMAGE ?>/favicons/apple-touch-icon-72x72.png">
        <link rel="apple-touch-icon" sizes="76x76" href="<?= IMAGE ?>/favicons/apple-touch-icon-76x76.png">
        <link rel="apple-touch-icon" sizes="114x114" href="<?= IMAGE ?>/favicons/apple-touch-icon-114x114.png">
        <link rel="apple-touch-icon" sizes="120x120" href="<?= IMAGE ?>/favicons/apple-touch-icon-120x120.png">
        <link rel="apple-touch-icon" sizes="144x144" href="<?= IMAGE ?>/favicons/apple-touch-icon-144x144.png">
        <link rel="apple-touch-icon" sizes="152x152" href="<?= IMAGE ?>/favicons/apple-touch-icon-152x152.png">
        <link rel="apple-touch-icon" sizes="180x180" href="<?= IMAGE ?>/favicons/apple-touch-icon-180x180.png">
        <!-- END Icons -->

        <!-- Stylesheets -->
        <!-- Web fonts -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css">

        <!-- Page JS Plugins CSS -->
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">

        <!-- Bootstrap and OneUI CSS framework -->
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
        <link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
        <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datetimepicker/bootstrap-datetime.css">
    <!--    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css"> -->

        <!-- You can include a specific file from css/themes/ folder to alter the default color theme of the template. eg: -->
        <!-- <link rel="stylesheet" id="css-theme" href="assets/css/themes/flat.min.css"> -->
        <!-- END Stylesheets -->
        <style>
    	.rocell, .stcell, .dacell, .cucell, .phcell, .vecell, .tocell, .licell, .tycell{
			padding:1px;
		}
		.table-medium{
			font-size:14px
		}
		.table-small{
			font-size:10px
		}
		.table-large{
			font-size:18px
		}
		.btn-md{
			margin:3px;
			width:300px;
		}
		.btn-warning{
			color:black;
		}
		.inputnav{
			padding:0px;margin:0px;
		}
		.inputul{
			list-style:none;text-align:left;padding:0px;border:0px;margin:1px;

		}

		.input-top{
			text-align:left;
			font-weight:normal;
			font-size:small
		}

		.input-li{
			text-align:left;font-size:12pt;
		}

		.col-md-5{
			margin:10px;
		}
		.input-border{
			border-color:black;
			border-width:thin;
		}
        </style>
    </head>
    <body>
        <!-- Page Container -->
        <!--
            Available Classes:

            'enable-cookies'             Remembers active color theme between pages (when set through color theme list)

            'sidebar-l'                  Left Sidebar and right Side Overlay
            'sidebar-r'                  Right Sidebar and left Side Overlay
            'sidebar-mini'               Mini hoverable Sidebar (> 991px)
            'sidebar-o'                  Visible Sidebar by default (> 991px)
            'sidebar-o-xs'               Visible Sidebar by default (< 992px)

            'side-overlay-hover'         Hoverable Side Overlay (> 991px)
            'side-overlay-o'             Visible Side Overlay by default (> 991px)

            'side-scroll'                Enables custom scrolling on Sidebar and Side Overlay instead of native scrolling (> 991px)

            'header-navbar-fixed'        Enables fixed header
        -->

            <!-- END Header -->

            <!-- Main Container -->
            <main id="main-container" style="padding:50px; background-color:white; color:black;">
				<div class="row" style="width:50%;margin:auto">
					<h3>Select Start and End Dates</h3><br>
					<div class="row">
						<div class="col-md-5">Select Start Date</div>
						<div class="col-md-5"><input type="text" class="form-control input-border" id="sd" name="sd"></div>
					</div>
					<div class="row">
						<div class="col-md-5">Select End Date</div>
						<div class="col-md-5"><input type="text" class="form-control input-border" id="ed" name="ed"></div>
					</div>
					<div class="row">
						<div class="col-md-5">
						<nav id="datenav" class="inputnav">
							<ul class="inputul">
								<li class="dropdown input-top" >
									<div id="datestat" style="color:white" class="btn btn-warning" role="button" class="dropdownselected" data-toggle="dropdown">Select Date Range</div>
									<ul style="width:100%" id="statusselect" class="dropdown-menu">
										<li id="assembly-li" class="input-li"><a href="#" class="nav-link" onclick="setdates('This Week')">This Week</a></li>
										<li id="q-check-li" class="input-li"><a href="#" class="nav-link" onclick="setdates('This Month')">This Month</a></li>
										<li id="final-li" class="input-li"><a href="#" class="nav-link" onclick="setdates('This Year')">This Year</a></li>
										<li id="inspection-li" class="input-li"><a href="#" class="nav-link" onclick="setdates('Last Week')">Last Week</a></li>
										<li id="approval-li" class="input-li"><a href="#" class="nav-link active" onclick="setdates('Last Month')">Last Month</a></li>
										<li id="parts-li" class="input-li"><a href="#" class="nav-link" onclick="setdates('Last Year')">Last Year</a></li>
									</ul>
								</li>
							</ul>
						</nav>
						</div>
						<div class="col-md-5">
							<button type="button" class="btn btn-primary" onclick="checkForm()">Run Report</button>
							<button  onclick="location.href='<?= COMPONENTS_PRIVATE ?>/reports/reports.php'" type="button" class="btn btn-default">Done</button>
						</div>
					</div>
				</div>

            </main>
            <!-- END Main Container -->

            <!-- Footer -->
            <!-- END Footer -->

        <!-- END Page Container -->

        <!-- Apps Modal -->
        <!-- Opens from the button in the header -->
        <div class="modal fade" id="datemodal" tabindex="-1" role="dialog" aria-hidden="true">


        <script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
        <script src="<?= SCRIPT ?>/tipped.js"></script>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

        <!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
        <script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
        <script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
        <script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
        <script src="<?= SCRIPT ?>/app.js"></script>
        <script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
        <script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
        <script src="<?= SCRIPT ?>/plugins/datetimepicker/bootstrap-datetime.js"></script>


        <!-- Page Plugins -->

        <!-- Page JS Code
        <script src="<?= SCRIPT ?>/pages/base_pages_dashboard.js"></script>-->
    <script>
		$(document).ready(function(){

			$('#sd').datetimepicker({
                format: 'MM/DD/YYYY'
            });
			$('#ed').datetimepicker({
                format: 'MM/DD/YYYY'
                //useCurrent: false
            });
	        $("#sd").on("dp.change", function (e) {
	            $('#ed').focus()
	        });
		});

		function setdates(t){
	    	<?php
	    	echo "\r\nvar lw = '".date("m/d/Y",strtotime("last week monday"))."|".date("m/d/Y",strtotime("last week sunday"))."';\r\n";
	    	echo "var lm = '".date("m/d/Y",strtotime("first day of previous month"))."|".date("m/d/Y",strtotime("last day of previous month"))."';\r\n";
	    	$ly = date("Y") - 1;
	    	echo "var ly = '".date("m/d/Y",strtotime("01/01/".$ly))."|".date("m/d/Y",strtotime("12/31/".$ly))."';\r\n";
	    	echo "var tw = '".date("m/d/Y",strtotime("this week monday"))."|".date("m/d/Y",strtotime("this week sunday"))."';\r\n";
	    	echo "var tm = '".date("m/d/Y",strtotime("first day of this month"))."|".date("m/d/Y",strtotime("last day of this month"))."';\r\n";
	    	$ty = date("Y");
	    	echo "var ty = '".date("m/d/Y",strtotime("01/01/".$ty))."|".date("m/d/Y",strtotime("12/31/".$ty))."';\r\n";
	    	?>

	    	if (t == "This Week"){
	    		tar = tw.split("|")
	    		$('#sd').val(tar[0])
	    		$('#ed').val(tar[1]);
	    	}
	    	if (t == "This Month"){
	    		tar = tm.split("|")
	    		$('#sd').val(tar[0])
	    		$('#ed').val(tar[1]);
	    	}
	    	if (t == "This Year"){
	    		tar = ty.split("|")
	    		$('#sd').val(tar[0])
	    		$('#ed').val(tar[1]);
	    	}
	    	if (t == "Last Week"){
	    		tar = lw.split("|")
	    		$('#sd').val(tar[0])
	    		$('#ed').val(tar[1]);
	    	}
	    	if (t == "Last Month"){
	    		tar = lm.split("|")
	    		$('#sd').val(tar[0])
	    		$('#ed').val(tar[1]);
	    	}
	    	if (t == "Last Year"){
	    		tar = ly.split("|")
	    		$('#sd').val(tar[0])
	    		$('#ed').val(tar[1]);
	    	}
    	}
		$('#sd').datetimepicker({
			ignoreReadonly: true,
			useCurrent: true,
			format: "MM/DD/Y"

		})

		$('#ed').datetimepicker({
			ignoreReadonly: true,
			useCurrent: true,
			format: "MM/DD/Y"

		})


		function checkForm(){

			sd = $('#sd').val()
			ed = $('#ed').val()

			if (sd.length > 0 && ed.length > 0){
				location.href='report.php?sd='+sd+'&ed='+ed
			}

		}

	</script>
    </body>
</html>
<?php
}else {
	echo "You do not have access to this function.  ";
?>
	<a href='<?=  COMPONENTS_PRIVATE. "/wip/wip.php"; ?>'>Click here</a> to return to the WIP list"
<?php
}
?>
<?php if(isset($conn)){mysqli_close($conn);} ?>
