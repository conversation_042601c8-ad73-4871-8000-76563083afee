
<?php
	$title = 'Sales By Technician Report';
	// Use this for Custom Reports
	include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_config.php');

	// Global Variables
	$shopid = $_COOKIE['shopid'];
	$date = new DateTime('now');
	$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
	$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
	$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
	$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
	$sd = $sdate;
	$ed = $edate;
	$sd1=date('Y-m-d',strtotime($sd));
	$ed1=date('Y-m-d',strtotime($ed));
    $tech = isset($_REQUEST['tech']) ? filter_var($_REQUEST['tech'], FILTER_SANITIZE_STRING) : "all";
    $techSQL = "";
    if ($tech != "all") {
        $techSQL = "AND l.Tech = '" . $tech . "'";
    }

	// Use this for Custom Reports
	$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder

    function formatName($name) {
        return mb_convert_case($name, MB_CASE_TITLE, "UTF-8");
    }

    // Query Starts Here
    $tablefields=array('Date', 'RO', 'Customer', 'Technician', 'Labor Sales', 'Labor Hours', 'Parts Sales', 'Total Sales');
    $alldata=array();

    $statement="SELECT l.datedone,
                    ro.roid,
                    ro.customer,
                    l.Tech,
                    SUM(l.laborhours) as laborHours,
                    SUM(l.linetotal) as laborTotal
                FROM repairorders ro 
                JOIN labor l 
                    ON ro.shopid = l.shopid 
                    AND ro.roid = l.roid 
                JOIN complaints c 
                    ON l.shopid = c.shopid 
                    AND l.roid = c.roid 
                    AND l.complaintid=c.complaintid
                WHERE ro.shopid = ?
                    AND l.Tech != 'discount, discount'
                    AND ro.rotype != 'No Approval' 
                    AND l.deleted = 'no'
                 	AND l.datedone >= ?
                 	AND l.datedone <= ? 
                    AND ro.status in ('CLOSED', 'FINAL')
                    $techSQL
                GROUP BY ro.roid, l.Tech
                ORDER BY l.Tech";

    $current_tech = "";
    $total_labor_sales = $total_labor_hours = 0;
    $total_part_sales = $total_sales = 0;
    $grand_total_labor_sales = $grand_total_labor_hours = 0;
    $grand_total_part_sales = $grand_total_sales = 0;

    $counter = 0;
    if ($query = $conn->prepare($statement)) {
        $query->bind_param("sss", $shopid, $sd1, $ed1);
        $query->execute();
        $result = $query->get_result();

        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $counter++;
                $parts_price = 0;
                $parts_stmt = "SELECT SUM(p.LineTTLPrice) FROM parts p WHERE p.roid = ? AND p.shopid = ?";
                if ($parts_query = $conn->prepare($parts_stmt)) {
                    $parts_query->bind_param("ss", $row['roid'], $shopid);
                    $parts_query->execute();
                    $parts_query->bind_result($parts_price);
                    $parts_query->fetch();
                    $parts_query->close();
                }

                if ($current_tech !== "" && $current_tech !== $row['Tech']) {
                    $alldata[] = array(
                        '',
                        '',
                        '',
                        formatName($current_tech) . ' Total',
                        asDollars($total_labor_sales),
                        number_format($total_labor_hours, 2),
                        asDollars($total_part_sales),
                        asDollars($total_sales)
                    );

                    $total_labor_sales = $total_labor_hours = 0;
                    $total_part_sales = $total_sales = 0;
                }

                $current_tech = $row['Tech'];

                $total_labor_sales += $row['laborTotal'];
                $total_labor_hours += $row['laborHours'];
                $total_part_sales += $parts_price;
                $total_sales += $row['laborTotal'] + $parts_price;

                $grand_total_labor_sales += $row['laborTotal'];
                $grand_total_labor_hours += $row['laborHours'];
                $grand_total_part_sales += $parts_price;
                $grand_total_sales += $row['laborTotal'] + $parts_price;

                $alldata[] = array(
                    date_format(new DateTime($row["datedone"]),'m/d/Y'),
                    $row['roid'],
                    formatName($row['customer']),
                    formatName($row['Tech']),
                    asDollars($row['laborTotal']),
                    number_format($row['laborHours'], 2),
                    asDollars($parts_price),
                    asDollars($row['laborTotal'] + $parts_price)
                );
            }

            if ($current_tech !== "") {
                $alldata[] = array(
                    '',
                    '',
                    '',
                    formatName($current_tech) . ' Total',
                    asDollars($total_labor_sales),
                    number_format($total_labor_hours, 2),
                    asDollars($total_part_sales),
                    asDollars($total_sales)
                );
            }

            if ($tech == "all") {
                $alldata[] = array(
                    '',
                    '',
                    '',
                    'Grand Total',
                    asDollars($grand_total_labor_sales),
                    number_format($grand_total_labor_hours, 2),
                    asDollars($grand_total_part_sales),
                    asDollars($grand_total_sales)
                );
            }
        }

        $query->close();
    } else {
        echo "Data Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
?>
<!DOCTYPE html>
<html>
	<body>

		<?php
		include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_buttons.php");
		?>

		<table class="table table-condensed table-header-bg" >
			<thead>
				<tr class="table_header table_head">
                    <?php foreach ($tablefields as $field): ?>
                        <td><?= $field ?></td>
                    <?php endforeach; ?>
				</tr>
			</thead>
            <tbody>
                <?php foreach ($alldata as $row): ?>
                    <tr>
                        <?php foreach ($row as $index => $cell): ?>
                            <?php if ($index === 3 && stripos($cell, 'Total') !== false): ?>
                                <td><strong><?= $cell; ?></strong></td>
                            <?php else: ?>
                                <td><?= $cell; ?></td>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

		<?php
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/footer_reports.php");
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_form.php");
		?>
	</body>
</html>
<?php
	if(isset($conn)){
		mysqli_close($conn);
	}
?>
