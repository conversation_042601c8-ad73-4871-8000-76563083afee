<!DOCTYPE html>
<html>
<?php

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1=date('Y-m-d',strtotime($sd));
$ed1=date('Y-m-d',strtotime($ed));
$job_completed = isset($_REQUEST['job_completed'])?filter_var($_REQUEST['job_completed'], FILTER_SANITIZE_STRING):"";
$ro_status = isset($_REQUEST['ro_status'])?filter_var($_REQUEST['ro_status'], FILTER_SANITIZE_STRING):"Closed";
$path = $_SERVER['HTTP_HOST'];

// Page Variables
$title = 'Technician Detail Report';  // Report Title Goes Here

include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_config.php');

$subtitle = 'This report includes '.$ro_status.' status Repair Orders';  // Report SubTitle Goes Here - Hide if not needed

if($job_completed == "yes"){
    $subtitle .= " and job status marked complete";
}

$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}
// Use this for Gen Pop Reports
// $template = 'excelexport.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
$template = COMPONENTS_PRIVATE."/reports/templates/gg_excellexport.php"; //Only change if a custom PHPExcel is created in the template folder
?>
<body>

<?php

include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_buttons.php');

$sdate = date_format(new DateTime($sd),'Y-m-d');
$edate = date_format(new DateTime($ed),'Y-m-d')

?>

<!-- Column Headers Insert Report Variables here -->
	<table class="report_table tech_prod_td">
	<thead>
	<tr class="table_header table_head">
		<td>Tech</td>
		<td>RO #</td>
		<td>Status</td>
		<td>Status Date</td>
		<?php if($job_completed == "yes"){?><td>Job Complete Date</td><?php }?>
		<td>Customer</td>
		<td>Vehicle</td>
      	<td>Labor Description</td>
		<td>VIN</td>
      	<td style="text-align:right">Labor Hours</td>
      	<td style="text-align:right">Sold Rate</td>
      	<td style="text-align:right">Total Labor</td>
      	<td style="text-align:right">Tech Rate</td>
      	<td style="text-align:right">Tech Pay</td>
	</tr>
</thead>

<?php

$roStatusSQL = "";
if($ro_status != "all"){
    if($ro_status != "open") {
        $roStatusSQL = "AND ro.status = '" . $ro_status . "'";
    } else {
        $roStatusSQL = "AND ro.status != 'Closed'";
    }
}

$jobSQL = "";
if(!empty($job_completed) && $job_completed == "yes"){
    $jobSQL = "AND c.acceptdecline = 'Job Complete'";
}


    $tablefields=array('Tech','RO #','Status','Status Date','Customer','Vehicle','Labor Description','VIN','Labor Hours','Sold Rate','Total Labor','Tech Rate','Tech Pay');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel

	$stmt = "SELECT replacerowithtag FROM company WHERE shopid = '$shopid' ";
	if ($query = $conn->prepare($stmt)) {
		$query->execute();
		$query->bind_result($replacerowithtag);
		$query->fetch();
		$query->close();
	}

if ($_GET['empid'] == "all" ){

	$stmt = "select distinct tech from labor l,repairorders ro where ro.shopid = l.shopid and ro.roid = l.roid AND l.Tech != 'discount, discount' AND ro.rotype != 'No Approval' AND l.deleted = 'no' $roStatusSQL and l.shopid = ? AND ro.statusdate >= ? AND ro.statusdate <= ?";

	if ($query = $conn->prepare($stmt)) {
			$query->bind_param("sss", $shopid,$sdate,$edate);
			$query->execute();
			$lbrresult = $query->get_result();
	}else{
			"Labor Prepare failed (" . $conn->errorno . ")" . $conn->error;
	}
	//echo $stmt;
	//printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$tech,$sdate,$edate);

	if ($lbrresult->num_rows > 0) {
		while($lbr = $lbrresult->fetch_array()) {

			$totallbrhrs = 0;
			$totallabor = 0;
			$totalcurrentpay = 0;
			$tech = $lbr["tech"];

			$tar = explode(",",$tech);
			$techlast = trim($tar[0]," ");
			if (!empty($tar[1]) ) {
				$techfirst = trim($tar[1]," ");
			}else{
				$techfirst = "";
			}

			$tech2 = $techlast.','.$techfirst;

			if(in_array($tech, $techarr))continue;

			$techarr[] = $tech;

			$stmt = "SELECT ro.roid,ro.tagnumber,ro.vin,ro.rotype,ro.customer,ro.vehinfo,ro.status,ro.statusdate,ro.datetimepromised,l.laborhours,l.Tech, l.techrate,l.hourlyrate,l.linetotal,l.labor,c.datedone ";
			$stmt .= " FROM repairorders ro ";
			$stmt .= " JOIN labor l ";
			$stmt .= "   ON ro.shopid = l.shopid and ro.roid = l.roid ";
            $stmt .= " JOIN complaints c ON c.complaintid = l.complaintid AND l.roid = c.roid AND l.shopid = c.shopid ";
			$stmt .= " WHERE ro.shopid = ?";
			$stmt .= "  AND l.tech = ? ";
			$stmt .= "  AND l.Tech != 'discount, discount' AND ro.rotype != 'No Approval' AND l.deleted = 'no' ";
			$stmt .= "  AND ro.statusdate >= ? AND ro.statusdate <= ? ";
            $stmt .= $roStatusSQL." ".$jobSQL;
			$stmt .= "  ORDER BY ro.roid ";

            //echo sprintf(str_replace("?","'%s'", $stmt), $shopid,$tech,$sdate,$edate);

			if ($query = $conn->prepare($stmt)) {
				$query->bind_param("ssss", $shopid,$tech,$sdate,$edate);
				$query->execute();
				$roresult = $query->get_result();
			}else{
				"RO Prepare failed (" . $conn->errorno . ")" . $conn->error;
			}

			if ($roresult->num_rows > 0) {
				while($ro = $roresult->fetch_array()) {

					$statusdate = new Datetime($ro["statusdate"]);

					$totallbrhrs = $totallbrhrs +  $ro["laborhours"];
					$totallabor = $totallabor +  $ro["linetotal"];

					$paytype = "";
					$payrate = 0;
					$rtechpay = 0;

					$stmt = "SELECT concat(employeelast,', ',employeefirst) emp, hourlyrate,paytype ";
					$stmt .= " FROM employees ";
					$stmt .= " WHERE shopid = ?";
					$stmt .= "   AND (concat(employeelast,', ',employeefirst) = ? || concat(employeelast,employeefirst) = ?) AND active = 'yes'";
					if ($query = $conn->prepare($stmt)) {
						$query->bind_param("sss", $shopid,$tech,$tech2);
						$query->execute();
						$empresult = $query->get_result();

					}else{
						echo "Employee prepare failed  (" . $conn->errno . ")" . $conn->error;
					}
					//printf(str_replace("?","'" . "%s" . "'",$stmt),$shopid,$tech)  ;

					$emp = mysqli_fetch_assoc($empresult);

					if ($empresult->num_rows > 0) {

						$paytype = $emp["paytype"];
						$payrate = $emp["hourlyrate"];

						$laborchg = $ro["linetotal"];
						if (strtolower($paytype) == "percentage") {
							if (is_numeric($payrate) and $payrate > 0) {
							//echo $ro["tech"] . ":" . $paytype . ":" . $payrate . ":" . $linetotal .  "<BR>";

								if ($linetotal == 0) {
									//echo $ro["tech"] . ":" . $paytype . ":" . $payrate . "<BR>";
									// calculate the charged labor and
									$linetotal = $ro["laborhours"] * $ro["memorate"];
									$techpay = asDollars(round($linetotal * ($payrate/100),2),2);
									//echo $ro["laborhours"] . ":" . $ro["hourlyrate"] . ":" . $linetotal . ":" . $techpay . "<BR>";
								}else{
									//$techpaydisplay = asDollars(round($laborchg * ($payrate/100),2),2);
									$techpay = round($laborchg * ($payrate/100),2);
								}
							}else{
								$techpay = 0.00;
							}

						}elseif ($paytype == "flatrate") {
							$techpay = $ro["laborhours"] * $payrate;
							$rtechpay = $rtechpay + $techpay;

						}elseif (strtoupper($paytype) == "HOURLY") {
							$techpay = $ro["laborhours"] * $payrate;
							$rtechpay = $rtechpay + $techpay;
						}
						if (floatval($ro["hourlyrate"]) > 0) {
                        	$currentpay = ($emp["hourlyrate"]*$ro["laborhours"]);
                        }else{
                        	$currentpay = 0;
                        } // end of current pay

                        $totalcurrentpay = $totalcurrentpay + $currentpay;

						$rostatus = $ro["status"];
						if(is_numeric(substr($rostatus,0,1))){
                            $rostatus = substr($rostatus,1);
                        }

						// rlk pulling l.laborhours vs lbrhrs * hrhly rate 1/13/20

		    ?>

<!-- Table Results Begin -->
    <tr class="table_data">
  		<td><?= $ro["Tech"];?></td>
  		<td><?= $ro["roid"];?></td>
  		<td><?= strtoupper($rostatus);?></td>
  		<td><?= date_format($statusdate,'m/d/Y');?></td>
  		<?php if($job_completed == "yes"){?><td><?= !empty($ro['datedone']) && $ro['datedone']!='0000-00-00'?date('m/d/Y',strtotime($ro['datedone'])):''?></td><?php }?>
  		<td><?= strtoupper($ro["customer"]);?></td>
  		<td><?= substr(strtoupper($ro["vehinfo"]),0,30);?></td>
  		<td><?= substr(strtoupper($ro["labor"]),0,50);?></td>
		<td><?= strtoupper($ro["vin"]);?></td>
  		<td style="text-align:right"><?php echo $ro["laborhours"]; ?></td>
  		<td style="text-align:right"><?php echo asDollars($ro["hourlyrate"]); ?></td>
  		<td style="text-align:right"><?php echo asDollars($ro["linetotal"]); ?></td>
		<td style="text-align:right"><?php echo asDollars($payrate);?></td>
		<td style="text-align:right"><?php echo asDollars($currentpay);?></td>
    </tr>

    <?php
         				$alldata[]=array($ro["Tech"],$ro["roid"],strtoupper($rostatus),date_format($statusdate,'m/d/Y'),strtoupper($ro["customer"]),strtoupper($ro["vehinfo"]),strtoupper($ro["labor"]),strtoupper($ro["vin"]),floatval($ro["laborhours"]),asDollars($ro["hourlyrate"]),asDollars($ro["linetotal"]),asDollars($payrate),asDollars($currentpay)); //fill up the alldata array with the arrays of data to be shown in excel export
					} // end of emp if
				} // end of ro while loop
?>
		<tr class="table_total">
		 	<td><b>TOTAL</b></td>
		 	<td></td>
		 	<td></td>
		 	<td></td>
		 	<?php if($job_completed == "yes"){?><td></td><?php }?>
		 	<td></td>
		 	<td></td>
			<td></td>
		 	<td><b><?= $techfirst . " " . $techlast;?></b></td>
		 	<td style="text-align:right"><b><?php echo $totallbrhrs;?></b></td>
		 	<td></td>
		 	<td style="text-align:right"><b><?php echo asDollars($totallabor,2);?></b></td>
		 	<td></td>
		 	<td style="text-align:right"><b><?php echo asDollars($totalcurrentpay);?></b></td>
		 </tr>
<?php
				$alldata[]=array('','','','','','','','','','','','');
				$alldata[]=array('TOTAL','','','','','','Hours for',$techfirst . " " . $techlast, floatval($totallbrhrs),'',asDollars($totallabor,2),'',asDollars($totalcurrentpay));
    		} // end if for ro

  		} // end of labor while for loop
 	} // end if for labor end of file

// individual technician
}else{
	$tech = $_GET['empid'];

	$totallbrhrs = 0;
	$totallabor = 0;
	$totalcurrentpay = 0;
	$tar = explode(",",$tech);
	$techlast = trim($tar[0]," ");
	if (!empty($tar[1]) ) {
		$techfirst = trim($tar[1]," ");
	}else{
		$techfirst = "";
	}

	$stmt = "SELECT ro.roid,ro.tagnumber,ro.vin,ro.rotype,ro.customer,ro.vehinfo,ro.status,ro.statusdate,ro.datetimepromised,l.laborhours,l.Tech, l.techrate,l.hourlyrate,l.linetotal,l.labor ";
	$stmt .= " FROM repairorders ro ";
	$stmt .= " JOIN labor l ";
	$stmt .= "   ON ro.shopid = l.shopid and ro.roid = l.roid ";
    $stmt .= " JOIN complaints c ON c.complaintid = l.complaintid AND l.roid = c.roid AND l.shopid = c.shopid ";
	$stmt .= " WHERE ro.shopid = ?";
	$stmt .= "  AND l.Tech = ? ";
	$stmt .= "  AND l.Tech != 'discount, discount' AND ro.rotype != 'No Approval' AND l.deleted = 'no' ";
    $stmt .= $jobSQL." ".$roStatusSQL;
	$stmt .= "  AND ro.statusdate >= ? AND ro.statusdate <= ? ";
	$stmt .= "  ORDER BY ro.roid ";

	if ($query = $conn->prepare($stmt)) {
		$query->bind_param("ssss", $shopid,$tech,$sdate,$edate);
		$query->execute();
		$roresult = $query->get_result();
	}else{
		"RO Prepare failed (" . $conn->errorno . ")" . $conn->error;
	}
	if ($roresult->num_rows > 0) {
		while($ro = $roresult->fetch_array()) {

			$statusdate = new Datetime($ro["statusdate"]);

			$totallbrhrs = $totallbrhrs +  $ro["laborhours"];
			$totallabor = $totallabor +  $ro["linetotal"];


			$paytype = "";
			$payrate = 0;
			$rtechpay = 0;

			$stmt = "SELECT concat(employeelast,', ',employeefirst) emp, hourlyrate,paytype ";
			$stmt .= " FROM employees ";
			$stmt .= " WHERE shopid = ?";
			$stmt .= "   AND concat(employeelast,', ',employeefirst) = ? ";
			if ($query = $conn->prepare($stmt)) {
				$query->bind_param("ss", $shopid,$tech);
				$query->execute();
				$empresult = $query->get_result();

			}else{
				echo "Employee prepare failed  (" . $conn->errno . ")" . $conn->error;
			}
			//printf(str_replace("?","'" . "%s" . "'",$stmt),$shopid,$tech)  ;

			$emp = mysqli_fetch_assoc($empresult);

			if ($empresult->num_rows > 0) {

				$paytype = $emp["paytype"];
				$payrate = $emp["hourlyrate"];

				$laborchg = $ro["linetotal"];
				if (strtolower($paytype) == "percentage") {
					if (is_numeric($payrate) and $payrate > 0) {

						if ($linetotal == 0) {
							$linetotal = $ro["laborhours"] * $ro["memorate"];
							$techpay = asDollars(round($linetotal * ($payrate/100),2),2);
						}else{
							$techpay = round($laborchg * ($payrate/100),2);
						}
					}else{
						$techpay = 0.00;
					}

				}elseif ($paytype == "flatrate") {
					$techpay = $ro["laborhours"] * $payrate;
					$rtechpay = $rtechpay + $techpay;

				}elseif (strtoupper($paytype) == "HOURLY") {
					$techpay = $ro["laborhours"] * $payrate;
					$rtechpay = $rtechpay + $techpay;
				}
				if (floatval($ro["hourlyrate"]) > 0) {
                	$currentpay = ($emp["hourlyrate"]*$ro["laborhours"]);
                }else{
                	$currentpay = 0;
                } // end of current pay

                $totalcurrentpay = $totalcurrentpay + $currentpay;
				// rlk pulling l.laborhours vs lbrhrs * hrhly rate 1/13/20

				$rostatus = $ro["status"];
				if(is_numeric(substr($rostatus,0,1))){
					$rostatus = substr($rostatus,1);
				}

?>

<!-- Table Results Begin -->
    <tr class="table_data">
  		<td><?= $ro["Tech"];?></td>
  		<td><?= $ro["roid"];?></td>
  		<td><?= strtoupper($rostatus);?></td>
  		<td><?= date_format($statusdate,'m/d/Y');?></td>
  		<td><?= strtoupper($ro["customer"]);?></td>
  		<td><?= substr(strtoupper($ro["vehinfo"]),0,30);?></td>
  		<td><?= substr(strtoupper($ro["labor"]),0,50);?></td>
		<td><?= strtoupper($ro["vin"]);?></td>
  		<td style="text-align:right"><?php echo $ro["laborhours"]; ?></td>
  		<td style="text-align:right"><?php echo asDollars($ro["hourlyrate"]); ?></td>
  		<td style="text-align:right"><?php echo asDollars($ro["linetotal"]); ?></td>
		<td style="text-align:right"><?php echo asDollars($payrate);?></td>
		<td style="text-align:right"><?php echo asDollars($currentpay);?></td>
    </tr>

<?php
        		$alldata[]=array($ro["Tech"],$ro["roid"],strtoupper($rostatus),date_format($statusdate,'m/d/Y'),strtoupper($ro["customer"]),strtoupper($ro["vehinfo"]),strtoupper($ro["labor"]),strtoupper($ro["vin"]),floatval($ro["laborhours"]),asDollars($ro["hourlyrate"]),asDollars($ro["linetotal"]),asDollars($payrate),asDollars($currentpay)); //fill up the alldata array with the arrays of data to be shown in excel export
			} // end of emp if
		} // end of ro while loop
?>
		<tr class="table_total">
		 	<td><b>TOTAL</b></td>
		 	<td></td>
		 	<td></td>
		 	<td></td>
		 	<td></td>
		 	<td></td>
			<td></td>
		 	<td><b><?= $techfirst . " " . $techlast;?></b></td>
		 	<td style="text-align:right"><b><?php echo $totallbrhrs;?></b></td>
		 	<td></td>
		 	<td style="text-align:right"><b><?php echo asDollars($totallabor,2);?></b></td>
		 	<td></td>
		 	<td style="text-align:right"><b><?php echo asDollars($totalcurrentpay);?></b></td>
		 </tr>
<?php
				$alldata[]=array('','','','','','','','','','','','');
				$alldata[]=array('TOTAL','','','','','','Hours for',$techfirst . " " . $techlast,floatval($totallbrhrs),'',asDollars($totallabor,2),'',asDollars($totalcurrentpay));
    } // end if for ro

} // end if of employee all

?>

    </table>

<?php
include(COMPONENTS_PRIVATE_PATH. '/reports/includes/footer_reports.php');
include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_form.php');
?>


</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
