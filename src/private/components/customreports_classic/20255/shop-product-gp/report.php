
<?php
	$title = 'Shop Product GP';
	// Use this for Custom Reports
	include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_config.php');

	// Global Variables
	$shopid = $_COOKIE['shopid'];
	$date = new DateTime('now');
	$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
	$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
	$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
	$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
	$sd = $sdate;
	$ed = $edate;
	$sd1=date('Y-m-d',strtotime($sd));
	$ed1=date('Y-m-d',strtotime($ed));

    $ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "all";
    $roTypeSQL = "";
    if ($ro_type != "all") {
        $roTypeSQL = "AND r.rotype = '" . $ro_type . "'";
    }

	// Use this for Custom Reports
	$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder

    // Query Starts Here
    $tablefields=array('R.O #', 'R.O Type', 'Customer', 'Discount Type', 'Parts Discount', 'Labor Discount');
    $alldata=array();

    $statement =   "SELECT 
                        ROID,
                        UPPER(ROType) as roType,
                        UPPER(CB) as discountReason,
                        Customer
                    FROM 
                        repairorders r 
                    WHERE 
                        shopid = ?
                        AND StatusDate >= ?
                        AND StatusDate <= ?
                        $roTypeSQL
                    ORDER BY ROType";

    $totalLaborDiscount = $totalPartsDiscount = 0;
    $previousROType = "";

    if ($query = $conn->prepare($statement)) {
        $query->bind_param("sss", $shopid, $sd1, $ed1);
        $query->execute();
        $result = $query->get_result();

        if ($result->num_rows > 0) {
            $previousROType = "";
            $totalPartsDiscount = 0;
            $totalLaborDiscount = 0;
            
            $row = $result->fetch_assoc();
        
            while ($row) {
                $currentRow = $row;
        
                $row = $result->fetch_assoc();
        
                // Parts Discount
                $partsDiscount = '';
                $stmt = "SELECT coalesce(sum(linettlprice),0) as partDiscounts 
                        FROM parts 
                        WHERE 
                            shopid = ? 
                            AND roid = ? 
                            AND partnumber = 'DISCOUNT' 
                            AND partdesc = 'DISCOUNT' 
                            AND supplier = 'DISCOUNT'";
        
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("si", $shopid, $currentRow['ROID']);
                    $query->execute();
                    $query->bind_result($partsDiscount);
                    $query->fetch();
                    $query->close();
                }
        
                // Labor Discount
                $laborDiscount = '';
                $stmt = "SELECT
                            coalesce(sum(linetotal),0) linetotal 
                        FROM labor 
                        WHERE 
                            shopid = ? 
                            AND roid = ?
                            AND labor = 'discount' 
                            AND tech = 'discount, discount'";
        
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("si", $shopid, $currentRow['ROID']);
                    $query->execute();
                    $query->bind_result($laborDiscount);
                    $query->fetch();
                    $query->close();
                }
        
                if ($previousROType == $currentRow['roType'] || $previousROType == "") {
                    $totalLaborDiscount += (-1 * $laborDiscount);
                    $totalPartsDiscount += (-1 * $partsDiscount);
                } else {
                    $alldata[] = array(
                        "Totals",
                        $previousROType,
                        "",
                        "",
                        asDollars($totalPartsDiscount),
                        asDollars($totalLaborDiscount)
                    );
        
                    $totalLaborDiscount = 0;
                    $totalPartsDiscount = 0;
        
                    $totalLaborDiscount += (-1 * $laborDiscount);
                    $totalPartsDiscount += (-1 * $partsDiscount);
                }
        
                $previousROType = $currentRow['roType'];
        
                $alldata[] = array(
                    $currentRow['ROID'],
                    $currentRow['roType'],
                    strtoupper($currentRow['Customer']),
                    strtoupper($currentRow['discountReason']),
                    asDollars(-$partsDiscount),
                    asDollars(-$laborDiscount)
                );

                if (!$row) {
                    $alldata[] = array(
                        "Totals",
                        $currentRow['roType'],
                        "",
                        "",
                        asDollars($totalPartsDiscount),
                        asDollars($totalLaborDiscount)
                    );
                }
            }
        }
        $query->close();
    } else {
        echo "Data Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    $lastRoType = "";
?>
<!DOCTYPE html>
<html>
	<body>

		<?php
		include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_buttons.php");

        $tablefields=array('R.O #', 'R.O Type', 'Customer', 'Discount Type', 'Parts Discount', 'Labor Discount');

		?>

<?php 
        $lastRoType = "";
        $counter = 0;
        ?>

		<?php foreach ($alldata as $row): ?>
			<?php if ($row[1] != $lastRoType): ?>
				<?php if ($lastRoType != ""): ?>
					</tbody></table><br>
				<?php endif; ?>

				<h5 style="background-color: #70b9eb; color: white"><?= $row[1]; ?></h5>
				<table class="table table-condensed table-header-bg">
					<thead>
						<tr class="table_header table_head">
							<?php foreach ($tablefields as $field): ?>
								<td><?= $field ?></td>
							<?php endforeach; ?>
						</tr>
					</thead>
					<tbody>
			<?php endif; ?>

			<tr>
				<?php foreach ($row as $cell): ?>
					<td><?= ($row[0] == 'Totals') ? '<b>' . $cell . '</b>' : $cell; ?></td>
				<?php endforeach; ?>
			</tr>

			<?php 
				$lastRoType = $row[1]; 
			?>
		<?php endforeach; ?>

		</tbody></table>

		<?php
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/footer_reports.php");
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_form.php");
		?>
	</body>
</html>
<?php
	if(isset($conn)){
		mysqli_close($conn);
	}
?>
