
<?php
    ini_set("display_errors", "1");
    error_reporting(E_ALL);
	
    $title = 'Audit Log Report';
	// Use this for Custom Reports
	include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_config.php');

	// Global Variables
	$shopid = $_COOKIE['shopid'];
	$date = new DateTime('now');
	$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
	$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
	$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
	$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
	$sd = $sdate;
	$ed = $edate;
	$sd1=date('Y-m-d',strtotime($sd));
	$ed1=date('Y-m-d',strtotime($ed));
    $roStatus = isset($_GET['roStatus']) ? $_GET['roStatus'] : '';
    $category = isset($_GET['category']) ? $_GET['category'] : '';

	// Use this for Custom Reports
	$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder

    // Query Starts Here
    $tablefields=array('Date/Time', 'Category', 'Event', 'User');
    $alldata=array();

    $roStatusSQL = "";
    if ($roStatus != 'all') {
        $rosQuery = "SELECT GROUP_CONCAT(roid SEPARATOR ',') AS roids
                     FROM repairorders r 
                     WHERE shopid = ?
                       AND StatusDate >= ?
                       AND StatusDate <= ?
                       AND status = ?
                     ORDER BY roid";
        if ($query = $conn->prepare($rosQuery)) {
            $query->bind_param("ssss", $shopid, $sd1, $ed1, $roStatus);
            $query->execute();
            $query->bind_result($ros);
            $query->fetch();
            $query->close();
        }
    
        if (!empty($ros)) {
            $roArray = explode(',', $ros);
            $roConditions = array_map(fn($ro) => "event LIKE '%$ro%'", $roArray);
            $roStatusSQL = "AND (" . implode(" OR ", $roConditions) . ")";
        }else{
            $roStatusSQL = "AND (event = 'NOT FOUND 404')";
        }

        if (is_numeric(substr($roStatus,0,1)) ) {
            $roStatus = substr($roStatus,1);
        }else{
            $roStatus = $roStatus;
        }
        $title .= " - RO Type: " . ucwords(strtolower($roStatus));
    }

    $categorySQL = "";
    if ( $category != 'all' ){
        $categorySQL = "AND category = '" . $category . "'";
        $title .= " - Category: " . ucwords(strtolower($category));
    }
    
    $statement = "SELECT category, event, eventdatetime, useraccount
                  FROM audit 
                  WHERE shopid = ?
                    AND eventdatetime >= ?
                    AND eventdatetime <= ?
                    $roStatusSQL
                    $categorySQL
                  ORDER BY eventdatetime";
    
    if ($query = $conn->prepare($statement)) {
        $query->bind_param("sss", $shopid, $sd1, $ed1);
        $query->execute();
        $result = $query->get_result();
    
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $alldata[] = array(
                    date_format(new DateTime($row['eventdatetime']),'m/d/Y h:i A'),
                    $row['category'],
                    $row['event'],
                    $row['useraccount'],
                );
            }
        }
        $query->close();
    } else {
        echo "Data Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
    
?>
<!DOCTYPE html>
<html>
	<body>

		<?php
		include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_buttons.php");
		?>

		<table class="table table-condensed table-header-bg" >
			<thead>
				<tr class="table_header table_head">
                    <?php foreach ($tablefields as $field): ?>
                        <td><?= $field ?></td>
                    <?php endforeach; ?>
				</tr>
			</thead>
			<tbody>
                <?php foreach ($alldata as $row): ?>
                    <tr>
                        <?php foreach ($row as $cell): ?>
                            <td><?= $cell; ?></td>
                        <?php endforeach; ?>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

		<?php
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/footer_reports.php");
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_form.php");
		?>
	</body>
</html>
<?php
	if(isset($conn)){
		mysqli_close($conn);
	}
?>
