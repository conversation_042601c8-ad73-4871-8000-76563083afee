
<?php
	$title = 'Custom Parts Added Report';
	// Use this for Custom Reports
	include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_config.php');

	// Global Variables
	$shopid = $_COOKIE['shopid'];
	$date = new DateTime('now');
	$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
	$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
	$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
	$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
	$sd = $sdate;
	$ed = $edate;
	$sd1=date('Y-m-d',strtotime($sd)) . ' 00:00:00';
	$ed1=date('Y-m-d',strtotime($ed)) . ' 23:59:59';


	// Use this for Custom Reports
	$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder

    // Query Starts Here
    $tablefields=array('Part Number','Part Description','Supplier','On Hand');
    $alldata=array();

    $statement="SELECT 
                    partNumber,
                    partDesc,
                    partSupplier,
                    onHand
                FROM
                    partsinventory p 
                WHERE 
                    p.shopid = ?
                    AND ts BETWEEN ? AND ?";

    if ($query = $conn->prepare($statement)) {
        $query->bind_param("sss", $shopid, $sd1, $ed1);
        $query->execute();
        $result = $query->get_result();

        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $alldata[]=array(
                    strtoupper($row['partNumber']),
                    strtoupper($row['partDesc']),
                    strtoupper($row['partSupplier']),
                    $row['onHand']
                );
            }
        }
        $query->close();
    } else {
        echo "Data Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
?>
<!DOCTYPE html>
<html>
	<body>

		<?php
		include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_buttons.php");
		?>

        <table class="table table-condensed table-header-bg">
            <thead>
                <tr class="table_header table_head">
                    <?php foreach ($tablefields as $field): ?>
                        <td><?= $field ?></td>
                    <?php endforeach; ?>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($alldata as $row): ?>
                    <?php $isBold = strpos($row[0], 'TOTAL') !== false; ?>
                    <tr<?= $isBold ? ' style="font-weight:bold;"' : '' ?>>
                        <?php foreach ($row as $cell): ?>
                            <td><?= $cell; ?></td>
                        <?php endforeach; ?>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

		<?php
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/footer_reports.php");
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_form.php");
		?>
	</body>
</html>
<?php
	if(isset($conn)){
		mysqli_close($conn);
	}
?>
