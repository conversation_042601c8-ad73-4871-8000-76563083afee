<?php
$title = 'Technician Efficiency';  // Report Title Goes Here
// Use this for Gen Pop Reports
require COMPONENTS_PRIVATE_PATH . "/reports/includes/header_reports.php";
require CONN;

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1=date('Y-m-d',strtotime($sd));
$ed1=date('Y-m-d',strtotime($ed));
$job_completed = isset($_REQUEST['job_completed'])?filter_var($_REQUEST['job_completed'], FILTER_SANITIZE_STRING):"";
$ro_status = isset($_REQUEST['ro_status'])?filter_var($_REQUEST['ro_status'], FILTER_SANITIZE_STRING): "Closed";

$roStatusSQL = "";
if($ro_status != "all"){
    if($ro_status != "open") {
        $roStatusSQL = "AND ro.status = '" . $ro_status . "'";
    } else {
        $roStatusSQL = "AND ro.status != 'Closed'";
    }
}

$jobSQL = "";
if($job_completed == "yes"){
    $jobSQL = "AND c.acceptdecline = 'Job Complete'";
}else{
	$jobSQL = "AND c.acceptdecline != 'Job Complete'";
}

// Page Variables
//$subtitle = 'my subtitle';  // Report SubTitle Goes Here - Hide if not needed
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}

$subtitle = 'This report includes '.$ro_status.' status Repair Orders';  // Report SubTitle Goes Here - Hide if not needed
$subtitle .= " and job status marked " . ($job_completed == "yes" ? "complete" : "not complete");

// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>

<title><?= $title;?></title>  <!-- Meta title for page. Change in variable above-->

<!DOCTYPE html>
<html>
<body>

<?php
require COMPONENTS_PRIVATE_PATH . "/reports/includes/report_buttons.php";
?>

<!-- Column Headers Insert Report Variables here -->

	<table class=report_table>
		<thead>
	<tr class="table_header table_head">
		<td>Tech</td>
		<td colspan="2">RO #</td>
		<td>Labor</td>
		<td>Labor Start</td>
		<td>Labor End</td>
		<td>Elapsed Time (Hr:Min)</td>
		<td>Sold Time</td>
	</tr>
</thead>

    <?php
    $tablefields=array('Tech','RO #','Labor','Labor Start','Labor End','Elapsed Time (Hr:Min)','Sold Time');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel

// Insert DB Query Here

// Template Query Begins - Replace entire section
	$totlabhrs = 0;
	$runningtime=0;
	$lastlaborid='';
	$stmt= "SELECT 
				t.tech, 
				t.roid, 
				l.labor, 
				l.laborhours, 
				t.startdatetime, 
				t.enddatetime, 
				t.laborid, 
				TIMEDIFF(t.enddatetime,t.startdatetime) as min,
				c.acceptdecline
			FROM labortimeclock t
			LEFT JOIN labor l 
				ON l.laborid = t.laborid 
				AND l.shopid = t.shopid
			LEFT JOIN repairorders ro
				ON ro.shopid = t.shopid
				AND ro.roid = t.roid
			LEFT JOIN complaints c 
				ON c.shopid = t.shopid
				AND c.roid = t.roid
				AND c.complaintid = t.complaintid
			WHERE t.shopid = ? 
				$roStatusSQL $jobSQL
				AND DATE(t.startdatetime) BETWEEN ? AND ?
				AND NOT ISNULL(t.enddatetime)
			ORDER BY t.tech, t.startdatetime, t.complaintid ASC;";

	if($query = $conn->prepare($stmt)){
		$query->bind_param("sss",$shopid,$sd1,$ed1);
		$query->execute();
		$roresult = $query->get_result();
	}else{
		echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
	$runmins = 0;
	if ($roresult->num_rows > 0) {

        $count=$roresult->num_rows;
        $ro = $roresult->fetch_array();
        $i=1;
		
        while($i<=$count) {
			$lasttech = $ro["tech"];
			$starttime = date('m/d/Y g:i:s A',strtotime($ro["startdatetime"]));
			if(!empty($ro["enddatetime"]))
			{
				$endtime = date('m/d/Y g:i:s A',strtotime($ro["enddatetime"]));
				$elapsedtime=date('H:i',strtotime($ro["min"]));
				
				$sdt = strtotime($ro['startdatetime']);
				$edt = strtotime($ro['enddatetime']);
				$elapsedmins = (($edt - $sdt) / 60);
				$runmins += $elapsedmins;
		    }
		    else
		    {
		    $endtime='';
		    $elapsedtime='';
		    }
			$soldhours = $ro["laborhours"];
	?>
		<tr>
			<td><?= strtoupper($ro["tech"]) ?></td>
			<td colspan="2"><?= $ro["roid"] ?></td>
			<td><?= strtoupper($ro["labor"]) ?></td>
			<td><?= $starttime ?></td>
			<td><?= $endtime ?></td>
			<td><?= $elapsedtime ?></td>
			<td>
			<?php
			if($lastlaborid != $ro["laborid"]) {
				$soldlabel=$soldhours;
				$totlabhrs+=$soldhours;
			}
			else
			$soldlabel="SAME";

		    echo($soldlabel);
			?>
		    </td>
		</tr>
	    <?php
	    $alldata[]=array(strtoupper($ro["tech"]),$ro["roid"],strtoupper($ro["labor"]),$starttime,$endtime,$elapsedtime,$soldlabel);//fill up the alldata array with the arrays of data to be shown in excel export

	    $lastlaborid=$ro["laborid"];
	    $mindiff=strtotime($ro["enddatetime"])-strtotime($ro["startdatetime"]);
	    if($mindiff>0)
	    $runningtime+=$mindiff;
	    $ro = $roresult->fetch_array();
	    if(isset($ro["tech"]))$newtech=$ro["tech"];else $newtech='';
	    $i++;
        if($lasttech != $newtech) {

			$anTotalSeconds = $runningtime;
			$anHours = (int)($anTotalSeconds/3600);
			$anTotalSeconds = $anTotalSeconds - (3600 * $anHours);
			$anMinutes = (int)($anTotalSeconds/60);
			$anMinutes = round($anMinutes * 1.67);

			$ear = explode(',',$lasttech);
			$stmt = "select id from employees where active = 'yes' and shopid = ? and employeelast = ? and employeefirst = ?";
			if($query = $conn->prepare($stmt)){
			$l=trim($ear[0]);$f=trim($ear[1]);
		    $query->bind_param("sss",$shopid,$l,$f);
		    $query->execute();
		    $query->bind_result($empid);
		    $query->fetch();
		    $query->close();
	        }else{
		    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	        }

			//get timeclock hours for that day for this tech
			$stmt = "select sum(timestampdiff(second,startdatetime,enddatetime)) as secdiff from timeclock where emp = ? and shopid = ? and date_format(startdatetime,'%Y-%m-%d') >= ? and date_format(startdatetime,'%Y-%m-%d') <= ?";
			if($query = $conn->prepare($stmt)){
		    $query->bind_param("ssss",$empid,$shopid,$sd1,$ed1);
		    $query->execute();
		    $query->bind_result($secdiff);
		    $query->fetch();
		    $query->close();
	        }else{
		    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	        }

			$nTotalSeconds = $secdiff;
			$nHours = (int)($nTotalSeconds / 3600);
			$nTotalSeconds = $nTotalSeconds - (3600 * $nHours);
			$nMinutes = round($nTotalSeconds/60,0,PHP_ROUND_HALF_EVEN);
			$nMinutes = round($nMinutes * 1.67);

            $employeeTimeClock = number_format($secdiff / 3600, 2);

			$decmins = round(($runmins / 60), 2);

			if ($decmins > 0) {
				$efficiencypercent = round(($totlabhrs / $decmins) * 100, 2) . '%';
			} else {
				$efficiencypercent = '0%';
			}	

			if($decmins > 0 && $employeeTimeClock > 0){
				$productivityseconds = number_format(($decmins / $employeeTimeClock * 100), 2) .'%';
			}else{
				$productivityseconds= '0%';	
			}
        	?>
        	<tr>
			<td colspan="2"><b>TOTALS FOR <?= $lasttech?></b></td>
			<td colspan="2" align="right"><b>Labor Time Clock (LTC):&nbsp;<?= $decmins ?> hours</b></td>
			<td><b>Employee Timeclock (ETC):&nbsp;<?= $employeeTimeClock ?> hours</b></td>
			<td><b>Sold Time: <?= $totlabhrs ?> hours</b></td>
			<td><b>Productivity<br>(LTC/ ETC)<br><?= $productivityseconds?></b></td>
			<td><b>Efficiency<br>(Sold / LTC)<br><?= $efficiencypercent ?></b></td>
		    </tr>
        	<?php
        	$alldata[]=array('TOTALS FOR '.$lasttech,'',"Labor Time Clock (LTC): ". $decmins ." hours","Employee Timeclock (ETC): ".$employeeTimeClock." hours","Sold Time: ".$totlabhrs." hours","Productivity\n(LTC/ ETC)\n".$productivityseconds,"Efficiency\n(Sold / LTC)\n".$efficiencypercent);//fill up the alldata array with the arrays of data to be shown in excel export

        	$totlabhrs=0;
        	$runningtime=0;

			$runmins = 0;
        }

	       		} // end of while for loop
	     	}// end if for end of file
			//$alldata[]=array('','','','','','','','','','','','');
	    ?>

	<?php

	?>
	</table>

<?php
require COMPONENTS_PRIVATE_PATH . "/reports/includes/footer_reports.php";
require COMPONENTS_PRIVATE_PATH . "/reports/includes/report_form.php";
?>

</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
