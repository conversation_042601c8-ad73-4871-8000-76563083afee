<?php
    $title = 'Custom Shop Fees Report';
    $subtitle = 'This report includes all Closed status Repair Orders';
    // Use this for Custom Reports
    include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_config.php');

    // Global Variables
    $shopid = $_COOKIE['shopid'];
    $date = new DateTime('now');
    $sd = isset($_GET['sd']) ? $_GET['sd'] : '';
    $ed = isset($_GET['ed']) ? $_GET['ed'] : '';
    $sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
    $edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
    $sd = $sdate;
    $ed = $edate;
    $sd1=date('Y-m-d',strtotime($sd));
    $ed1=date('Y-m-d',strtotime($ed));

    // Use this for Custom Reports
    $template = COMPONENTS_PRIVATE.'/reports/templates/excelexport.php';

    // Query Starts Here
    $statement="SELECT 
                    r.roid,
                    r.userfee1label, r.UserFee1, r.UserFee1amount, r.userfee1type,
                    r.userfee2label, r.UserFee2, r.UserFee2amount, r.userfee2type,
                    r.userfee3label, r.UserFee3, r.UserFee3amount, r.userfee3type,
                    r.statusDate
                FROM repairorders r 
                WHERE shopid = ?
                    AND r.StatusDate >= ?
                    AND r.StatusDate  <= ?
                    AND status = 'closed' 
                    AND rotype != 'no approval'
                ORDER BY r.statusDate DESC";

    $fee1Data = array();
    $fee2Data = array();
    $shippingFeeData = array();
    $creditCardSurchargeData = array();
    $otherFee3Data = array();
    
    $totalFee1 = 0;
    $totalFee2 = 0;
    $totalShippingFee = 0;
    $totalCreditCardSurcharge = 0;
    $totalOtherFee3 = 0;

    if ($query = $conn->prepare($statement)) {
        $query->bind_param("sss", $shopid, $sd1, $ed1);
        $query->execute();
        $result = $query->get_result();

        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $dateFormatted = date_format(new DateTime($row['statusDate']), 'm/d/Y');
                
                // Process Fee 1 if it exists
                if (!empty($row['UserFee1']) && $row['UserFee1amount'] > 0) {
                    $fee1Data[] = array(
                        $row['roid'],
                        strtoupper($row['userfee1label']),
                        $row['UserFee1amount'],
                        $dateFormatted
                    );
                    $totalFee1 += $row['UserFee1amount'];
                }
                
                // Process Fee 2 if it exists
                if (!empty($row['UserFee2']) && $row['UserFee2amount'] > 0) {
                    $fee2Data[] = array(
                        $row['roid'],
                        strtoupper($row['userfee2label']),
                        $row['UserFee2amount'],
                        $dateFormatted
                    );
                    $totalFee2 += $row['UserFee2amount'];
                }
                
                // Process Fee 3 - separate Shipping Fee and Credit Card Surcharge
                if (!empty($row['UserFee3']) && $row['UserFee3amount'] > 0) {
                    $feeLabel = strtoupper($row['userfee3label']);
                    
                    if (strpos($feeLabel, 'SHIPPING FEE') !== false) {
                        $shippingFeeData[] = array(
                            $row['roid'],
                            $feeLabel,
                            $row['UserFee3amount'],
                            $dateFormatted
                        );
                        $totalShippingFee += $row['UserFee3amount'];
                    } elseif (strpos($feeLabel, 'CREDIT CARD SURCHARGE') !== false) {
                        $creditCardSurchargeData[] = array(
                            $row['roid'],
                            $feeLabel,
                            $row['UserFee3amount'],
                            $dateFormatted
                        );
                        $totalCreditCardSurcharge += $row['UserFee3amount'];
                    } else {
                        $otherFee3Data[] = array(
                            $row['roid'],
                            $feeLabel,
                            $row['UserFee3amount'],
                            $dateFormatted
                        );
                        $totalOtherFee3 += $row['UserFee3amount'];
                    }
                }
            }

            $tablefields = array(
                'RO #',
                'Fee Name',
                'Amount',
                'Date'
            );

            $alldata = array();

            if (!empty($fee1Data)) {
                $alldata = array_merge($alldata,
                    array_map(fn($r) => [$r[0], $r[1], '$' . number_format($r[2], 2), date('m/d/Y', strtotime($r[3]))], $fee1Data),
                    [['TOTAL ', '', '$' . number_format($totalFee1, 2), ''], ['','','','']]
                );
            }

            if (!empty($fee2Data)) {
                $alldata = array_merge($alldata,
                    array_map(fn($r) => [$r[0], $r[1], '$' . number_format($r[2], 2), date('m/d/Y', strtotime($r[3]))], $fee2Data),
                    [['TOTAL ', '', '$' . number_format($totalFee2, 2), ''], ['','','','']]
                );
            }

            if (!empty($shippingFeeData)) {
                $alldata = array_merge($alldata,
                    array_map(fn($r) => [$r[0], $r[1], '$' . number_format($r[2], 2), date('m/d/Y', strtotime($r[3]))], $shippingFeeData),
                    [['TOTAL', '', '$' . number_format($totalShippingFee, 2), ''], ['','','','']]
                );
            }

            if (!empty($creditCardSurchargeData)) {
                $alldata = array_merge($alldata,
                    array_map(fn($r) => [$r[0], $r[1], '$' . number_format($r[2], 2), date('m/d/Y', strtotime($r[3]))], $creditCardSurchargeData),
                    [['TOTAL', '', '$' . number_format($totalCreditCardSurcharge, 2), ''], ['','','','']]
                );
            }

            if (!empty($otherFee3Data)) {
                $alldata = array_merge($alldata,
                    array_map(fn($r) => [$r[0], $r[1], '$' . number_format($r[2], 2), date('m/d/Y', strtotime($r[3]))], $otherFee3Data),
                    [['TOTAL', '', '$' . number_format($totalOtherFee3, 2), '']]
                );
            }

        }
        $query->close();
    } else {
        echo "Data Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
?>
<!DOCTYPE html>
<html>
    <head>
        <style>
            .fee-section {
                margin-bottom: 30px;
                page-break-after: always;
            }
            .fee-title {
                font-weight: bold;
                font-size: 15px;
                margin-bottom: 10px;
                border-bottom: 2px solid #333;
                padding-bottom: 5px;
            }
            .fee-total {
                font-weight: bold;
                background-color: #f5f5f5;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            th, td {
                padding: 8px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }
            th {
                background-color: #f2f2f2;
            }
        </style>
    </head>
    <body>

        <?php
        include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_buttons.php");
        ?>

        <!-- Fee 1 Section -->
        <div class="fee-section">
            <div class="fee-title">
                <?= !empty($fee1Data) ? strtoupper($fee1Data[0][1]) : 'FEE 1' ?> REPORT
            </div>
            <table class="table table-condensed table-header-bg">
                <thead>
                    <tr>
                        <th>RO #</th>
                        <th>Fee Name</th>
                        <th>Amount</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($fee1Data as $row): ?>
                        <tr>
                            <td><?= $row[0] ?></td>
                            <td style="width:30%"><?= $row[1] ?></td>
                            <td><?= asDollars($row[2]) ?></td>
                            <td><?= $row[3] ?></td>
                        </tr>
                    <?php endforeach; ?>
                    <?php if (!empty($fee1Data)): ?>
                        <tr class="fee-total">
                            <td colspan="2">TOTAL</td>
                            <td><?= asDollars($totalFee1) ?></td>
                            <td></td>
                        </tr>
                    <?php else: ?>
                        <tr>
                            <td colspan="4">No data found for this fee</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Fee 2 Section -->
        <div class="fee-section">
            <div class="fee-title">
                <?= !empty($fee2Data) ? strtoupper($fee2Data[0][1]) : 'FEE 2' ?> REPORT
            </div>
            <table class="table table-condensed table-header-bg">
                <thead>
                    <tr>
                        <th>RO #</th>
                        <th>Fee Name</th>
                        <th>Amount</th>
                        <th>Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($fee2Data as $row): ?>
                        <tr>
                            <td><?= $row[0] ?></td>
                            <td style="width:30%"><?= $row[1] ?></td>
                            <td><?= asDollars($row[2]) ?></td>
                            <td><?= $row[3] ?></td>
                        </tr>
                    <?php endforeach; ?>
                    <?php if (!empty($fee2Data)): ?>
                        <tr class="fee-total">
                            <td colspan="2">TOTAL</td>
                            <td><?= asDollars($totalFee2) ?></td>
                            <td></td>
                        </tr>
                    <?php else: ?>
                        <tr>
                            <td colspan="4">No data found for this fee</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <?php if (!empty($shippingFeeData) || !empty($creditCardSurchargeData)): ?>
            <!-- Show separate sections only if they have data -->
            <?php if (!empty($shippingFeeData)): ?>
            <div class="fee-section">
                <div class="fee-title">
                    SHIPPING FEE REPORT
                </div>
                <table class="table table-condensed table-header-bg">
                    <thead>
                        <tr>
                            <th>RO #</th>
                            <th>Fee Name</th>
                            <th>Amount</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($shippingFeeData as $row): ?>
                            <tr>
                                <td><?= $row[0] ?></td>
                                <td style="width:30%"><?= $row[1] ?></td>
                                <td><?= asDollars($row[2]) ?></td>
                                <td><?= $row[3] ?></td>
                            </tr>
                        <?php endforeach; ?>
                        <tr class="fee-total">
                            <td colspan="2">TOTAL</td>
                            <td><?= asDollars($totalShippingFee) ?></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>

            <?php if (!empty($creditCardSurchargeData)): ?>
            <div class="fee-section">
                <div class="fee-title">
                    CREDIT CARD SURCHARGE REPORT
                </div>
                <table class="table table-condensed table-header-bg">
                    <thead>
                        <tr>
                            <th>RO #</th>
                            <th>Fee Name</th>
                            <th>Amount</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($creditCardSurchargeData as $row): ?>
                            <tr>
                                <td><?= $row[0] ?></td>
                                <td style="width:30%"><?= $row[1] ?></td>
                                <td><?= asDollars($row[2]) ?></td>
                                <td><?= $row[3] ?></td>
                            </tr>
                        <?php endforeach; ?>
                        <tr class="fee-total">
                            <td colspan="2">TOTAL</td>
                            <td><?= asDollars($totalCreditCardSurcharge) ?></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        <?php else: ?>
            <!-- Show combined FEE 3 REPORT if no shipping or credit card fees -->
            <div class="fee-section">
                <div class="fee-title">
                    <?= !empty($otherFee3Data) ? strtoupper($otherFee3Data[0][1]) : 'FEE 3' ?> REPORT
                </div>
                <table class="table table-condensed table-header-bg">
                    <thead>
                        <tr>
                            <th>RO #</th>
                            <th>Fee Name</th>
                            <th>Amount</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($otherFee3Data as $row): ?>
                            <tr>
                                <td><?= $row[0] ?></td>
                                <td><?= $row[1] ?></td>
                                <td style="width:30%"><?= asDollars($row[2]) ?></td>
                                <td><?= $row[3] ?></td>
                            </tr>
                        <?php endforeach; ?>
                        <?php if (!empty($otherFee3Data)): ?>
                            <tr class="fee-total">
                                <td colspan="2">TOTAL</td>
                                <td><?= number_format($totalOtherFee3) ?></td>
                                <td></td>
                            </tr>
                        <?php else: ?>
                            <tr>
                                <td colspan="4">No data found for this fee</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <?php
            include(COMPONENTS_PRIVATE_PATH."/reports/includes/footer_reports.php");
            include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_form.php");
        ?>
    </body>
</html>
<?php
    if(isset($conn)){
        mysqli_close($conn);
    }
?>