
<?php
	$title = 'Custom Expenses Report';
	// Use this for Custom Reports
	include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_config.php');

	// Global Variables
	$shopid = $_COOKIE['shopid'];
	$date = new DateTime('now');
	$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
	$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
	$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
	$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
	$sd = $sdate;
	$ed = $edate;
	$sd1=date('Y-m-d',strtotime($sd));
	$ed1=date('Y-m-d',strtotime($ed));

    $expenseSearchParameter = isset($_GET['expenseSearchParameter']) ? $_GET['expenseSearchParameter'] : '';
    $expenseSearchText = isset($_GET['expenseSearchText']) ? $_GET['expenseSearchText'] : '';

    $validParameters = [
        'dateDue' => "AND duedate = ?",
        'paidDate' => "AND paiddate = ?",
        'paidTo' => "AND paidto = ?",
        'name' => "AND expensename LIKE ?",
        'category' => "AND expensecategory LIKE ?",
        'amount' => "AND amount = ?",
        'ref' => "AND ref LIKE ?"
    ];

    $searchSQL = "";
    if ($expenseSearchParameter != ""){
        $searchSQL = $validParameters[$expenseSearchParameter];
    }

	// Use this for Custom Reports
	$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder

    // Query Starts Here
    $tablefields=array('Date Due', 'Date Paid', 'Paid To', 'Description', 'Ref', 'Account', 'Amount', 'Paid', 'Balance');
    $alldata=array();

    $statement="SELECT 
                    duedate,
                    paiddate,
                    paidto,
                    expensename,
                    ref,
                    expensecategory,
                    amount,
                    expensepaid                   
                FROM expenses
                WHERE 
                    shopid = ?
                    AND ( (duedate IS NOT NULL AND duedate >= '$sd1' AND duedate <= '$ed1')
                            OR (duedate IS NULL AND '$ed1' >= CURRENT_DATE())
                        )
                    $searchSQL
                ORDER BY 
                    duedate desc";

    if ($query = $conn->prepare($statement)) {
        ($expenseSearchParameter != "") 
            ? $query->bind_param("ss", $shopid, $expenseSearchText)
            : $query->bind_param("s", $shopid);
        $query->execute();
        $result = $query->get_result();

        $data = [];
        if ($result->num_rows > 0) {
            $balance = 0;
            while ($row = $result->fetch_assoc()) {
                $data[] = $row;

                $balance += $row['amount'];

                $duedate = new DateTime($row["duedate"]);
				$paiddate = new DateTime($row["paiddate"]);

                if ($row["paiddate"] == "0000-00-00") {
					$paiddate = "";
				}else{	
					$paiddate = new DateTime($row["paiddate"]);
					$paiddate = $paiddate->format('m/d/Y'); 
				}	

                if ($row["duedate"] == "0000-00-00") {
                    $duedate = "";
                }else{	
                    $duedate = new DateTime($row["duedate"]);
                    $duedate= $duedate->format('m/d/Y'); 
                }

                $alldata[]=array(
                    $duedate,
                    $paiddate,
                    strtoupper($row['paidto']),
                    strtoupper($row['expensename']),
                    strtoupper($row['ref']),
                    strtoupper($row['expensecategory']),
                    asDollars($row['amount']),
                    strtoupper($row['expensepaid']),
                    asDollars($balance)
                );
            }
        }
        $query->close();
    } else {
        echo "Data Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
?>
<!DOCTYPE html>
<html>
	<body>

		<?php
		include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_buttons.php");
		?>

		<table class="table table-condensed table-header-bg" >
			<thead>
				<tr class="table_header table_head">
                    <?php foreach ($tablefields as $field): ?>
                        <td><?= $field ?></td>
                    <?php endforeach; ?>
				</tr>
			</thead>
			<tbody>
                <?php foreach ($alldata as $row): ?>
                    <tr>
                        <?php foreach ($row as $cell): ?>
                            <td><?= $cell; ?></td>
                        <?php endforeach; ?>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

		<?php
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/footer_reports.php");
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_form.php");
		?>
	</body>
</html>
<?php
	if(isset($conn)){
		mysqli_close($conn);
	}
?>
