<!DOCTYPE html>
<html>
<?php
//require("../php/functions.php");

$sdate = date("Y-m-d", strtotime($_GET['sd']));
$edate = date("Y-m-d", strtotime($_GET['ed']));
$shopid = $_COOKIE['shopid'];

if (isset($_GET['referrer'])) {
    $referrer = $_GET['referrer'];
} else {
    $referrer = '';
}


$title = 'Custom Shop Production Summary With GP & Tire Sales';

require COMPONENTS_PRIVATE_PATH. "/reports/includes/report_config.php";

$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($companyname, $conamereports);
    $query->fetch();
    $query->close();
}

if ($conamereports == 'yes') {
    $title .= " - $companyname";
}

?>
<body>
<script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
<script src="<?= SCRIPT ?>/tipped.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

<!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
<script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
<script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
<script src="<?= SCRIPT ?>/app.js"></script>
<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
<script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
<script src="<?= SCRIPT ?>/plugins/datetimepicker/bootstrap-datetime.js"></script>
<style>
    .table {
        color: black
    }

    .auto-style1 {
        background-color: #5c90d2;
        color: white;
        font-weight: bold
    }

    .autostyle2 {
        border-bottom: 2px black solid
    }

    td {
        font-size: medium;
        padding: 10px;
        background-color: white;
    }

    body {
        font-family: Arial, Helvetica, sans-serif
    }

    .auto-style2 {
        border-bottom: 2px black solid;
        text-align: right;
    }

    .text-right {
        border-right: 1px silver solid;
    }

    .auto-style3 {
        text-align: right;
    }

    .auto-style4 {
        text-align: right;
        border-right: 1px solid silver;
        padding-right: 20px !important;
    }

    @media print {
        .no-print {
            display: none;
        }

        tr {
            font-size: 13px;
        }

        td {
            font-size: 12px;
        }

        h3 {
            font-size: 16px;
        }

        h4 {
            font-size: 15px;
        }

        tbody {
            display: inline;
        }

        #main-container {
            min-height: 750px !important;
        }

        body {
         /*   min-height: 750px !important; */
        }

    }


</style>


<?php
$profitboost = "";
$stmt = "SELECT profitboost FROM company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($profitboost);
    $query->fetch();
    $query->close();
}
// get a list of the ro numbers
$rolist = "";
$totalro = 0;
$labor = 0;
$parts = 0;
$sublet = 0;
$tax = 0;
$partscost = 0;
$numros = 0;
$laborcost = 0;
$subletcost = 0;
$totalfees = 0;
$totallaborhours = 0;
$PPH = 0;
$stmt = "select 1 as c,roid,totalro,totallbr,totalprts,totalsublet,salestax,partscost,totalfees,PPH from repairorders where shopid = '$shopid' and status = 'closed' and rotype != 'No Approval' and statusdate >= '$sdate' and statusdate <= '$edate'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        $rolist .= $rs['roid'] . ",";
        $labor += $rs['totallbr'];
        $parts += $rs['totalprts'];
        $sublet += $rs['totalsublet'];
        $tax += $rs['salestax'];
        $partscost += $rs['partscost'];
        $totalro += $rs['totalro'];
        $numros += $rs['c'];
        $totalfees += $rs['totalfees'];
        $PPH += $rs['PPH'];
    }
}

$rolist = substr($rolist, 0, strlen($rolist) - 1);

// calculate the labor cost using the rolist

//Get tire sales and cost
$tire_cost = $tire_price = $tire_quantity = 0;
$stmt = "SELECT SUM(LineTTLCost) as tire_cost, SUM(LineTTLPrice) as tire_price, SUM(Quantity) as tire_quantity FROM parts p JOIN complaints c ON c.shopid = p.shopid AND c.roid = p.roid AND c.complaintid = p.complaintid WHERE p.shopid = ? AND p.roid IN ($rolist) AND deleted = 'no' AND c.cstatus = 'no' AND c.acceptdecline != 'declined' AND p.partcode = 'tires'";

if ( $rolist ){
    if($query = $conn->prepare($stmt)){
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($tire_cost, $tire_price, $tire_quantity);
        $query->fetch();
        $query->close();
    } else {
        echo "Prepare failed ".$conn->error;
    }
}

//Subtract tire cost and price from total parts cost and price
$partscost -= $tire_cost;
$parts -= $tire_price;

$stmt = "select distinct laborid,l.tech,l.roid,l.laborhours from labor l inner join complaints c on l.shopid = c.shopid and l.complaintid = c.complaintid where l.shopid = '$shopid' and l.roid in ($rolist) and c.cstatus = 'no' and l.deleted = 'no'";
//echo $stmt."<BR>";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        // get the hourly rate for the tech
        $totallaborhours += $rs['laborhours'];
        $tech = $rs['tech'];
        //echo $tech."<BR>";
        if (strpos($tech, ",") > 0) {
            $tar = explode(",", $tech);
            $techlast = trim($tar[0], " ");
            $techfirst = trim($tar[1], " ");
            $techrate = 0;

            // get the hourly rate for this tech
            $estmt = "select hourlyrate from employees where shopid = '$shopid' and employeelast = '$techlast' and employeefirst = '$techfirst' and active = 'yes'";
            //echo $estmt."<BR>";
            if ($equery = $conn->prepare($estmt)) {
                //$equery->bind_param("ss",$techlast,$techfirst);
                $equery->execute();
                $equery->bind_result($techrate);
                $equery->fetch();
                $equery->close();
            }

            if ($techrate > 0) {
                $currlaborcost = $techrate * $rs['laborhours'];
                $laborcost += $currlaborcost;
            }
        }
    }
}

// get sublet cost
$stmt = "select distinct s.complaintid,subletcost scost from sublet s inner join complaints c on s.shopid = c.shopid and s.complaintid = c.complaintid where s.shopid = '$shopid' and s.roid in ($rolist) and c.cstatus = 'no'";
//echo $stmt;
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        $subletcost += $rs['scost'];
    }
    $query->close();
}

$subtotal = $totalro - $tax;
if ($numros > 0) {
    $avghoursperro = number_format($totallaborhours / $numros, 2);
    $aro = number_format($subtotal / $numros, 2);
} else {
    $avghoursperro = 0;
    $aro = 0;
}
if ($totallaborhours > 0) {
    $effectivelaborrate = number_format($labor / $totallaborhours, 2);
} else {
    $effectivelaborrate = 0;
}

$gp = $subtotal - $partscost - $tire_cost - $laborcost - $subletcost;

$subletprofit = $sublet - $subletcost;
$laborprofit = $labor - $laborcost;

if ($labor != 0) {
    $laborgppercent = $laborprofit / $labor;
} else {
    $laborgppercent = 0;
}

$partsprofit = $parts - $partscost;
if ($parts != 0) {
    $partsgppercent = $partsprofit / $parts;
} else {
    $partsgppercent = 0;
}


$tiresprofit = $tire_price - $tire_cost;
if ($tire_price != 0) {
    $tiregppercent = $tiresprofit / $tire_price;
} else {
    $tiregppercent = 0;
}

if ($gp > 0) {
    $partsgpp = number_format(100 * ($partsprofit / $gp), 0) . "%";
    $laborgpp = number_format(100 * ($laborprofit / $gp), 0) . "%";
    $subletgpp = number_format(100 * ($subletprofit / $gp), 0) . "%";
    $feesgpp = number_format(100 * ($totalfees / $gp), 0) . "%";
    $tiregpp = number_format(100 * ($tiresprofit / $gp), 0) . "%";
} else {
    $partsgpp = 0;
    $laborgpp = 0;
    $subletgpp = 0;
    $feesgpp = 0;
    $tiregpp = 0;
}

if ($subtotal > 0) {
    $gpp = number_format(100 * ($gp / $subtotal), 0);
    $partsgppoftotal = number_format(100 * ($partscost / $subtotal), 2) . "%";
    $tiresgppoftotal = number_format(100 * ($tire_cost / $subtotal), 2) . "%";
} else {
    $gpp = 0;
    $partsgppoftotal = 0;
    $tiresgppoftotal = 0;
}
// labor
if ($subtotal > 0) {
    $gpl = number_format(100 * ($gp / $subtotal), 0);
    $laborgppoftotal = number_format(100 * ($laborcost / $subtotal), 2) . "%";
} else {
    $gpl = 0;
    $laborgppoftotal = 0;
}

// sublet
if ($subtotal > 0) {
    $gps = number_format(100 * ($gp / $subtotal), 0);
    $subletgppoftotal = number_format(100 * ($subletcost / $subtotal), 2) . "%";
} else {
    $gps = 0;
    $subletgppoftotal = 0;
}

$totalprofit = $partsprofit + $tiresprofit + $laborprofit + $subletprofit + $totalfees;
$gpperlh = 0;
if ($totallaborhours > 0) {
    $gpperlh = $totalprofit / $totallaborhours;
} else {
    $gpperlh = 0;
}

$stmt = "SELECT roid FROM repairorders WHERE shopid = ? AND rotype != 'No Approval' AND status = 'closed' AND statusdate >= ? AND statusdate <= ? ";
//echo $stmt;

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("sss", $shopid, $sdate, $edate);
    $query->execute();
    $roresult = $query->get_result();
} else {
    echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$numofrows = $roresult->num_rows;

$shopidlist = "";

if ($roresult->num_rows > 0) {
    while ($ro = $roresult->fetch_array()) {
        $shopidlist = $shopidlist . $ro["roid"] . ",";
    } // end of while loop
}

if (substr($shopidlist, -1) == ",") {
    $shopidlist = substr($shopidlist, 0, strlen($shopidlist) - 1);

}

$tlabordiscount = 0;
$tpartsdiscount = 0;
$tiresdiscount = 0;
$stmt = "SELECT coalesce(SUM(if(tech = 'DISCOUNT, DISCOUNT',abs(linetotal),discount)),0) as tlabordiscount FROM labor WHERE shopid = ? AND roid in (" . $shopidlist . ")";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($tlabordiscount);
    $query->fetch();
    $query->close();
}
$stmt = "SELECT coalesce(SUM(if(partnumber = 'DISCOUNT',abs(linettlprice),((partprice*quantity)-linettlprice))),0) as tpartsdiscount FROM parts WHERE shopid = ? AND roid in (" . $shopidlist . ") and (partnumber='Discount' || discount!=0)";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($tpartsdiscount);
    $query->fetch();
    $query->close();
}
$stmt = "SELECT coalesce(SUM(if(partnumber = 'DISCOUNT',abs(linettlprice),((partprice*quantity)-linettlprice))),0) as tpartsdiscount FROM parts WHERE shopid = ? AND roid in (" . $shopidlist . ") and (partnumber='Discount' || discount!=0) AND partcode = 'tires'";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($tiresdiscount);
    $query->fetch();
    $query->close();
}

$tpartsdiscount -= $tiresdiscount; //Subtracting tire discount from the total parts discount.

?>
<main id="main-container" style="padding:10px;background-color:white;">
    <div style="text-align:center"><h3 style="text-align:center"><?= $title; ?></h3>
        <h4><?= date("m/d/Y", strtotime($sdate)) . " to " . date("m/d/Y", strtotime($edate)); ?></h4></div>
    <div id="print" style="padding-bottom:10px">
        <span class="btn btn-primary no-print" onclick="printSPR()">Print</span>
        <?php
        if ($referrer != 'report_dashboard') {
            ?>
            <span onclick="location.href='<?= COMPONENTS_PRIVATE ?>/reports/reports.php'" class="btn btn-info no-print">Done</span>
            <?php
        }
        ?>
    </div>
    <h4>Repair Orders</h4>
    <table style="page-break-after: avoid" class="table table-condensed table-striped table-hover">
        <tbody style="height:380px !important;">
        <tr>
            <td class="auto-style1" style="width: 25%"><strong>Data</strong></td>
            <td class="auto-style1" style="width: 25%">&nbsp;</td>
            <td class="auto-style1" style="width: 25%"><strong>Analysis</strong></td>
            <td class="auto-style1 text-right" style="width: 25%" colspan="2">&nbsp;</td>
        </tr>
        <tr>
            <td style="width: 25%;"># Repair Orders</td>
            <td class="text-right" style="width: 25%;"><?= $numros; ?></td>
            <td style="width: 25%;">Current ARO (Subtotal / # RO's):</td>
            <td class="text-right" style="width: 25%;" colspan="2">$<?= $aro; ?></td>
        </tr>
        <tr>
            <td style="width: 25%">Parts Cost | % Of Subtotal</td>
            <td class="text-right">$<?= number_format($partscost, 2) . " <b>|</b> " . $partsgppoftotal; ?></td>
            <td style="width: 25%">Gross Profit&nbsp; | %</td>
            <td class="text-right" style="width: 25%">$<?= number_format($gp, 2); ?></td>
            <td class="text-right" style="width: 10%"><?= number_format($gpp, 2) . "%"; ?></td>
        </tr>
        <tr>
            <td style="width: 25%">Tire Cost | % Of Subtotal</td>
            <td class="text-right">$<?= number_format($tire_cost, 2) . " <b>|</b> " . $tiresgppoftotal; ?></td>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td style="width: 25%">Labor Cost | % Of Subtotal</td>
            <td class="text-right" style="width: 12.5%">
                $<?= number_format($laborcost, 2) . " <b>|</b> " . $laborgppoftotal; ?></td>
            <?php
            if ($profitboost == "yes") {
                ?>
                <td style="width: 25%">PPH</td>
                <td style="width: 12.5%" class="text-right"><?= asDollars(($numros) ? ($PPH / $numros) : 0) ?></td>
                <?php
            } else {
                ?>
                <td style="width: 25%"></td>
                <td style="width: 12.5%" class="text-right"></td>
                <?php
            }
            ?>
            <td class="text-right" style="width: 12.5%"></td>
        </tr>
        <tr>
            <td style="width: 25%">Sublet Cost | % Of Subtotal</td>
            <td class="text-right" style="width: 12.5%">
                $<?= number_format($subletcost, 2) . " <b>|</b> " . $subletgppoftotal; ?></td>
            <td style="width: 25%"></td>
            <td style="width: 12.5%"></td>
            <td class="text-right" style="width: 12.5%"></td>
        </tr>
        <tr>
            <td style="width: 25%">Labor Sales</td>
            <td class="text-right" style="width: 25%">$<?= number_format($labor, 2); ?></td>
            <td style="width: 25%">Labor Profit&nbsp; | % of GP</td>
            <td class="text-right"
                style="width: 12.5%"><?= number_format($laborgppercent * 100, 0) . "% / $" . number_format($laborprofit, 2); ?></td>
            <td class="text-right" style="width: 12.5%"><?= $laborgpp; ?></td>
        </tr>
        <tr>
            <td style="width: 25%">Parts Sales</td>
            <td class="text-right" style="width: 25%">$<?= number_format($parts, 2); ?></td>
            <td style="width: 25%">Parts Profit&nbsp;&nbsp; | % of GP</td>
            <td class="text-right"
                style="width: 15%"><?= number_format($partsgppercent * 100, 0) . "% / $" . number_format($partsprofit, 2); ?></td>
            <td class="text-right" style="width: 10%"><?= $partsgpp; ?></td>
        </tr>
        <tr>
            <td style="width: 25%">Tires Sales</td>
            <td class="text-right" style="width: 25%">$<?= number_format($tire_price, 2); ?></td>
            <td style="width: 25%">Tires Profit&nbsp;&nbsp; | % of GP</td>
            <td class="text-right"
                style="width: 15%"><?= number_format($tiregppercent * 100, 0) . "% / $" . number_format($tiresprofit, 2); ?></td>
            <td class="text-right" style="width: 10%"><?= $tiregpp; ?></td>
        </tr>
        <tr>
            <td style="width: 25%">Sublet Sales</td>
            <td class="text-right" style="width: 25%">$<?= number_format($sublet, 2); ?></td>
            <td style="width: 25%">Sublet Profit | % of GP</td>
            <td class="text-right" style="width: 15%">$<?= number_format($subletprofit, 2); ?></td>
            <td class="text-right" style="width: 10%"><?= $subletgpp; ?></td>
        </tr>
        <tr>
            <td style="width: 25%">Fees</td>
            <td class="text-right" style="width: 25%">$<?= number_format($totalfees, 2); ?></td>
            <td style="width: 25%">Fees Profit&nbsp;&nbsp; | % of GP</td>
            <td class="text-right" style="width: 15%">$<?= number_format($totalfees, 2); ?></td>
            <td class="text-right" style="width: 10%"><?= $feesgpp; ?></td>
        </tr>
        <tr>
            <td style="width: 25%">Discount (Tires/Parts/Labor/Total)</td>
            <td class="text-right" style="width: 25%">
                (<?= asDollars($tiresdiscount)." / ". asDollars($tpartsdiscount) . " / " . asDollars($tlabordiscount) . " / " . asDollars($tlabordiscount + $tpartsdiscount + $tiresdiscount); ?>
                )
            </td>
            <td style="width: 25%">Total Labor Hours</td>
            <td class="text-right" colspan="2" style="width: 25%"><?= round($totallaborhours, 2); ?></td>
        </tr>
        <tr>
            <td style="width: 25%">Subtotal</td>
            <td class="text-right" style="width: 25%">$<?= number_format($subtotal, 2); ?></td>
            <td style="width: 25%">Gross Profit Per Labor Hour</td>
            <td class="text-right" colspan="2" style="width: 25%"><?= "$" . round($gpperlh, 2); ?></td>
        </tr>
        <tr>
            <td style="width: 25%">Tax</td>
            <td class="text-right" style="width: 25%">$<?= number_format($tax, 2); ?></td>
            <td style="width: 25%">Avg Hours per RO</td>
            <td class="text-right" colspan="2" style="width: 25%"><?= number_format($avghoursperro, 2); ?></td>
        </tr>
        <tr>
            <td class="autostyle2" style="width: 25%"><strong>Total RO Sales</strong></td>
            <td class="text-right auto-style2" style="width: 25%"><strong>$<?= number_format($totalro, 2); ?></strong>
            </td>
            <td class="autostyle2" style="width: 25%">Effective Labor Rate</td>
            <td class="text-right auto-style2" colspan="2" style="width: 25%">
                $<?= number_format($effectivelaborrate, 2); ?></td>
        </tr>
        </tbody>
    </table>
    <?php

    $pstotal = 0;
    $pscost = 0;
    $psdisc = 0;
    $pssubtotal = 0;
    $pstax = 0;
    $pscount = 0;

    $stmt = "select count(*) cnt, sum(pcost) as mcost, sum(discount) as pd, sum(subtotal) as subt, sum(tax) as st, sum(total) as ttl from ps where shopid = '$shopid' and status = 'Closed' and statusdate >= '$sdate' and statusdate <= '$edate'";

    if ($query = $conn->prepare($stmt)) {

        $query->execute();
        $query->bind_result($pscount, $pscost, $psdisc, $pssubtotal, $pstax, $pstotal);
        $query->fetch();
        $query->close();

    }

    // we need to get the psdetail parts cost
    // now get total parts cost
    $pspartcost = 0;

    $pcstmt = "select psid from ps where shopid = ? and status = 'Closed' and statusdate >= ? and statusdate <= ?";
    //set pcrs = con.execute(pcstmt)

    if ($query = $conn->prepare($pcstmt)) {
        $query->bind_param("sss", $shopid, $sdate, $edate);
        $query->execute();
        $pcrsresult = $query->get_result();
    } else {
        echo "pcrs Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    if ($pcrsresult->num_rows > 0) {
        while ($pcrs = $pcrsresult->fetch_array()) {
            $psid = $pcrs["psid"];
            $psdstmt = "select (cost*qty) as extcost from psdetail where shopid = ? and psid = ? ";

            //set psdrs = con.execute(psdstmt)
            if ($query = $conn->prepare($psdstmt)) {
                $query->bind_param("si", $shopid, $psid);
                $query->execute();
                $psdsresult = $query->get_result();
            } else {
                echo "psds Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            if ($psdsresult->num_rows > 0) {
                while ($psdrs = $psdsresult->fetch_array()) {
                    $pspartcost = $pspartcost + $psdrs["extcost"];
                } // end psdrs while loop
            } // end if psdrs
        } // end pcrs while loop
    } //end if pcrs


    if ($pscount > 0) {
        $avgps = $pstotal / $pscount;
    } else {
        $avgps = 0;
    }

    // cost is now coming off of psdetail
    //$psgp = number_format($pssubtotal - $pscost,2);
    $psgp = number_format($pssubtotal - $pspartcost, 2);


    //$psgpcalc = $pssubtotal - $pscost;
    $psgpcalc = $pssubtotal - $pspartcost;


    if ($pssubtotal > 0) {
        $psgpp = number_format(100 * ($psgpcalc / $pssubtotal), 0);
    } else {
        $psgpp = 0;
    }
    //$psgpp = number_format(100 * ($psgpcalc / $pssubtotal),0);
    if($pssubtotal != 0){
    ?>
    <div>
        <h4>Parts Sales</h4>
        <table style="margin-bottom: 0px !important; page-break-before: avoid; page-break-after: avoid" class="table table-condensed table-striped table-hover">
            <tbody style="height:235px !important;">
            <tr>
                <td class="auto-style1" style="width: 25%"><strong>Data</strong></td>
                <td class="auto-style1" style="width: 25%">&nbsp;</td>
                <td class="auto-style1" style="width: 25%"><strong>Analysis</strong></td>
                <td class="auto-style1" style="width: 25%" colspan="2">&nbsp;</td>
            </tr>
            <tr>
                <td style="width: 25%"># Parts Sales</td>
                <td class="auto-style3 text-right" style="width: 25%"><?= $pscount; ?></td>
                <td style="width: 25%">Gross Profit | %</td>
                <td class="auto-style3 text-right" style="width: 25%">$<?= $psgp; ?></td>
                <td class="auto-style3 text-right" style="width: 10%"><?= $psgpp . "%"; ?></td>
            </tr>
            <tr>
                <td style="width: 25%">Parts Cost</td>
                <td class="auto-style3 text-right" style="width: 25%">$<?= number_format($pspartcost, 2); ?></td>
                <td style="width: 25%">Avg per Part Sale</td>
                <td class="auto-style3 text-right" colspan="2" style="width: 25%">$<?= number_format($avgps, 2); ?></td>
            </tr>
            <tr>
                <td style="width: 25%">Parts Sales</td>
                <td class="auto-style3 text-right" style="width: 25%">$<?= number_format($pstotal - $pstax, 2); ?></td>
                <td style="width: 25%">&nbsp;</td>
                <td class="auto-style3 text-right" colspan="2" style="width: 25%">&nbsp;</td>
            </tr>
            <tr>
                <td style="width: 25%">Discount</td>
                <td class="auto-style3 text-right" style="width: 25%">$<?= number_format($psdisc, 2); ?></td>
                <td style="width: 25%">&nbsp;</td>
                <td class="auto-style3 text-right" colspan="2" style="width: 25%">&nbsp;</td>
            </tr>
            <tr>
                <td style="width: 25%">Subtotal</td>
                <td class="auto-style3 text-right" style="width: 25%">$<?= number_format($pssubtotal, 2); ?></td>
                <td style="width: 25%">&nbsp;</td>
                <td class="auto-style3 text-right" colspan="2" style="width: 25%">&nbsp;</td>
            </tr>
            <tr>
                <td style="width: 25%">Tax</td>
                <td class="auto-style3 text-right" style="width: 25%">$<?= number_format($pstax, 2); ?></td>
                <td style="width: 25%">&nbsp;</td>
                <td class="auto-style3 text-right" colspan="2" style="width: 25%">&nbsp;</td>
            </tr>
            <tr>
                <td style="text-align:left; width:25%;" class="auto-style2"><b>Total Parts Sales</b></td>
                <td class="auto-style3 auto-style2 text-right" style="width: 25%">
                    <b>$<?= number_format($pstotal, 2); ?></b></td>
                <td class="auto-style2" style="width: 25%">&nbsp;</td>
                <td class="auto-style3 auto-style2 text-right" colspan="2" style="width: 25%">&nbsp;</td>
            </tr>
            </tbody>
        </table>
    </div>
    <?php
    }
    ?>
</main>
<script>

    function printSPR() {
        // document.getElementById('buttons').style.display = 'none';
        window.print();
        //setTimeout("showButtons()",3000)
    }


    function oldVersion() {

        location.href = 'productionsummarywithgp.php?sdate=<?= $_GET['sdate']; ?>&edate=<?= $_GET['edate']; ?>'

    }

</script>
</body>

</html>
