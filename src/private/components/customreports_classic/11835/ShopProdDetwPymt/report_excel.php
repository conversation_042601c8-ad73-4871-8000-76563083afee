<?php

/***********************************************************
4/14/18
Buit from 6923
***********************************************************/
require CONN;
//require("../../functions.php");

$shopid = $_COOKIE['shopid'];

$today = new DateTime('now');
$today = date_format($today, 'm/d/Y H:i:s');		


if (isset($_GET['excel']) == "y") {
		header('Content-Type: application/vnd.ms-excel');
		header('Content-Disposition: attachment; '.'filename="prodshopdetwpymt.xls"'); 
}
if (isset($_GET['excel'])) {
	$excel = $_GET['excel'];
	if ($excel == "y") {
		echo "you are here";
		$startdate = $_GET['sd'];
		$sd = date_format(new DateTime($startdate),'Y-m-d');
		$enddate = $_GET['ed'];
		$ed = date_format(new DateTime($enddate),'Y-m-d');

	}	
}


if (isset($_GET['ed'])) {
	$enddate = $_GET['ed'];
	$ed = date_format(new DateTime($enddate),'Y-m-d');
}

if (isset($_POST['sd'])) {
	$startdate = $_POST['sd'];
	$sd = date_format(new DateTime($startdate),'Y-m-d');
}

if (isset($_POST['ed'])) {
	$enddate = $_POST['ed'];
	$ed = date_format(new DateTime($enddate),'Y-m-d');
}

$rrwt = "";

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head><meta name="robots" content="noindex,nofollow"/>
<meta http-equiv="Content-Language" content="en-us"/>
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252"/>
<meta name="GENERATOR" content="Microsoft FrontPage 12.0"/>
<meta name="ProgId" content="FrontPage.Editor.Document"/>
<meta name="swp" content="The report gives a detailed look at shop production."/>
<meta name="datereq" content="yes"/>

<title>Shop Production Detail Report: Date Range</title>

<script language="javascript">
	s = screen.width
	h = screen.height
	cw = (s / 2)-150
	ch = (h / 2)-50
	//mywin = window.open("pmess.htm", "what", "left="+cw+", top="+ch+", width=300, height=100, toolbar=no, menubar=no, scrollbar=no")
	//mywin.focus()
	//window.print()
	timerID = setTimeout("closeBack()", 500)
	function closeBack(){
		//mywin.close()
		//history.go(-1)
	}
	function printSPR(){
		document.getElementById('buttons').style.display = 'none';
		window.print();
		setTimeout("showButtons()",1000)
	}
	function showButtons(){
		document.getElementById('buttons').style.display = 'block';
	}
</script>
<style type="text/css">
.style6 {
	font-size: x-small;
}
.style7 {
	border: 1px solid #000000;
	font-size: x-small;
	font-weight: bold;
}
input{
	width:100px;
	border-radius:3px;
	border:1px silver solid;
	background-color:#2C5783;
	color:white;
	padding:10px;
	cursor:pointer;
	font-size:14px;
}

.style5 {
	font-size: x-small;
	font-weight: bold;
}
.style6 {
	font-size: x-small;
}
.style7 {
	border: 1px solid #000000;
	font-size: x-small;
	font-weight: bold;
}
.auto-style1 {
	color: #FF0000;
}
</style>
</head>

<body style="font-family:Verdana, Geneva, Tahoma, sans-serif" link="#800000" vlink="#800000" alink="#800000">
<p align="center">
<strong>Custom Shop Production Detail Report - GRIFFIS AUTOMOTIVE REPAIR</strong><br>
<strong>from&nbsp;<?php echo $startdate ;?> to <?php echo $enddate;?>
</strong>&nbsp;</b><br>
This report has been modified to include only Closed Repair Orders. 
<div><span class="auto-style1"><strong>Red Date</strong></span> indicates payment not received on RO closing date.</div>

<?php

	//$reportcode = $_POST['reportcode'];
	$reportcode = "All";
	
	$stmt = "SELECT replacerowithtag ";
	$stmt .= "FROM company ";
	$stmt .= "WHERE shopid = ? ";
	//echo $stmt; 

	if($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
		$query->execute();
		$coresult = $query->get_result();
	}else{
		echo "Company Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

	$co = $coresult->fetch_array();
	
	$rrwt = strtolower($co["replacerowithtag"]);
	
	//if ($reportcode == 'All') {
	
	$stmt = "SELECT roid,tagnumber,statusdate ,totalprts,totallbr,totalro,totalsublet,totalfees,discountamt,salestax,customer,fleetno,userfee1,userfee1label,userfee2,userfee2label,userfee3,userfee3label,HazardousWaste ";
	$stmt .= " FROM repairorders ";
	$stmt .= " WHERE shopid = ? ";
	$stmt .= "  AND `status` = 'Closed'";
	$stmt .= "  AND statusdate >= ? ";
	$stmt .= "  AND repairorders.rotype != 'No Approval'";		
	$stmt .= "  AND statusdate <= ? ";
	//echo $stmt;	

	if($query = $conn->prepare($stmt)){
		$query->bind_param("sss",$shopid,$sd,$ed);
		$query->execute();
		$roresult = $query->get_result();
	}else{
		echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
	
	//printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$sdate,$edate);
		
	$ro = mysqli_fetch_assoc($roresult);
	
	// resetting the result set pointer to beginning	
	mysqli_data_seek($roresult, 0);
?>
		
		<table style="width: 100%" cellpadding="3" cellspacing="0">
			<tr>
				<td width="5%" bgcolor="#C0C0C0" class="style5"><strong>RO#</strong></td>
				<td width="5%" bgcolor="#C0C0C0" class="style5"><strong>
				Final_Date</strong></td>
				<td width="20%" bgcolor="#C0C0C0" class="style5"><strong>
				Customer</strong></td>
				<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>
				Labor</strong></td>
				<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>
				Parts</strong></td>
				<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>
				Sublet</strong></td>
				<td align="right" width="5%" bgcolor="#C0C0C0">
				<span class="style6"><strong><?php echo $ro["userfee1label"]; ?></strong></span></td>
				<td align="right" width="5%" bgcolor="#C0C0C0">
				<span class="style6"><strong><?php echo $ro["userfee2label"]; ?></strong></span></td>
				<td align="right" width="5%" bgcolor="#C0C0C0">
				<span class="style6"><strong><?php echo $ro["userfee3label"]; ?></strong></span></td>
				<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>
				Haz Waste</strong></td>
				<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>
				Tax</strong></td>
				<td align="right" width="5%" bgcolor="#C0C0C0" class="style6" style="width: 0%"><strong>
				Discount</strong></td>
				<td align="right" width="5%" bgcolor="#C0C0C0" class="style6" style="width: 2%">
				<strong>Pmt_Date</strong></td>
				<td align="right" width="5%" bgcolor="#C0C0C0" class="style6" style="width: 2%">
				<strong>Pmts</strong></td>
				<td align="right" width="5%" bgcolor="#C0C0C0" class="style6" style="width: 2%">
				<strong>Source</strong></td>
				<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>
				Total_RO</strong></td>
				<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>
				Parts_Cost</strong></td>
	</tr>		
<?php
		$runningpmts = 0;
		$labor = 0;
		$parts = 0;
		$sublet = 0;
		$hazmat = 0;
		$slstax = 0;
		$pcost = 0;
		$ttldisc = 0;
		$userfee1 = 0;
		$userfee2 = 0;
		$tro = 0;
		$dfirstdate = "";
	
		if ($roresult->num_rows > 0) {
			while($ro = $roresult->fetch_array()) {
				$roid =  $ro["roid"];
				
				$statusdate = new DateTime($ro["statusdate"]);
				$statusdate = date_format($statusdate,'m/d/Y');
				
				$today = new DateTime('now');
				$today = date_format($today,'m/d/Y');
				
				if (strlen($ro["fleetno"]) > 0 ) {
					$fleetno = " (#" & $ro["fleetno"] .  ")";
				}else{
					$fleetno = "";
				}
				
				$stmt = "SELECT coalesce(sum(cost*quantity),0) as pcost ";
				$stmt .= "FROM parts ";
				$stmt .= "WHERE shopid = ? ";
				$stmt .= "  AND roid = ? ";	
				$stmt .= "  AND deleted = 'no'";	
				//echo $stmt; 
	
				if($query = $conn->prepare($stmt)){
					$query->bind_param("si",$shopid,$roid);
	   				$query->execute();
	    			$partresult = $query->get_result();
				}else{
					echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}
	
				if ($partresult->num_rows > 0) {
				
					$part = $partresult->fetch_array();

					if (is_null($part["pcost"]) ) {
						$mypcost = 0;																
					}else{
						$mypcost = $part["pcost"];
					} // end of is null
									
				} // end of parts end if
				
				$labor = $labor + $ro["totallbr"];
				$parts = $parts + $ro["totalprts"];
				$sublet = $sublet + $ro["totalsublet"];
				
				$hazmat = $hazmat + $ro["HazardousWaste"];
				
				$slstax = $slstax + $ro["salestax"];

				$pcost = $pcost + $mypcost;
				$ttldisc = $ttldisc + $ro["discountamt"];
				
				$userfee1 = $userfee1 + $ro["userfee1"];
				$userfee2 = $userfee2 + $ro["userfee2"];
				
				//$totalfees = $totalfees + $ro["totalfees"];
				
				if ($rrwt == "yes") {
					$displayroid = $ro["tagnumber"];
				}else{
					$displayroid = $ro["roid"];
				}
				
				$stmt = "SELECT amt,ptype,id,pdate ";
				$stmt .= "FROM accountpayments ";
				$stmt .= "WHERE shopid = ? ";
				$stmt .= "  AND roid = ? ";	
				//echo $stmt; 
	
				if($query = $conn->prepare($stmt)){
					$query->bind_param("si",$shopid,$roid);
	   				$query->execute();
	    			$apresult = $query->get_result();
				}else{
					echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}
	
				if ($apresult->num_rows > 0) {
					$ap = $apresult->fetch_array();
				
					$firstamt = $ap["amt"];
					$firsttype = $ap["ptype"];
					
					$firstdate = $ap["pdate"];
				
					// display first date
					if (!empty($ap["pdate"]) ) { 
						$dfirstdate = new DateTime($ap["pdate"]);
						$dfirstdate = date_format($dfirstdate,'m/d/Y');
					}else{			
						$dfirstdate = "" ;
					}
				
					$runningpmts = $runningpmts + $ap["amt"];
				}else{
					$firstamt = 0;
					$firsttype = 0;
					$firstdate = 0;
					$dfirstdate = "" ;
				}
				
				$totalro = $ro["totallbr"]+$ro["totalprts"]+$ro["totalsublet"]+$ro["totalfees"]+$ro["salestax"]-$ro["discountamt"];
				$tro = $tro + $totalro;
?>
			<tr>
				<td width="5%" class="style6"><?php echo $displayroid; ?></td>
				<td width="5%" class="style6">
				<?php 
					echo $statusdate; ?>
				</td>
				<td width="20%" class="style6"><?php echo $ro["customer"] . $fleetno; ?></td>
				<td align="right" width="5%" class="style6"><?php echo number_format($ro["totallbr"],2,".",$thousands_sep = ","); ?></td>
				<td align="right" width="5%" class="style6"><?php echo number_format($ro["totalprts"],2,".",$thousands_sep = ","); ?></td>
				<td align="right" width="5%" class="style6"><?php echo number_format($ro["totalsublet"],2,".",$thousands_sep = ","); ?></td>
				<td align="right" width="5%" class="style6"><?php echo number_format($ro["userfee1"],2,".",$thousands_sep = ","); ?></td>
				<td align="right" width="5%" class="style6"><?php echo number_format($ro["userfee2"],2,".",$thousands_sep = ","); ?></td>
				<td align="right" width="5%" class="style6"><?php echo number_format($ro["userfee3"],2,".",$thousands_sep = ","); ?></td>
				<td align="right" width="5%" class="style6"><?php echo number_format($ro["HazardousWaste"],2,".",$thousands_sep = ","); ?></td>
				<td align="right" width="5%" class="style6"><?php echo number_format($ro["salestax"],2,".",$thousands_sep = ","); ?></td>
				<td align="right" width="5%" class="style6" style="width: 0%"><?php echo number_format($ro["discountamt"],2,".",$thousands_sep = ","); ?></td>
				<td align="right" width="5%" class="style6" style="width: 2%;
				<?php 
					if ($firstdate <> $ro["statusdate"]) {
						echo "font-weight:bold;color:red" ;
					}
				?>">
				<?php 
					echo $dfirstdate; 
				?>
				</td>
				
				<td align="right" width="5%" class="style6" style="width: 2%"><?php echo number_format($firstamt,2,".",$thousands_sep = ","); ?></td>
				<td align="right" width="5%" class="style6" style="width: 2%"><?php echo $firsttype; ?></td>
				<td align="right" width="5%" class="style6"><?php echo number_format($totalro,2,".",$thousands_sep = ","); ?></td>
				<td align="right" width="5%" class="style6"><?php echo number_format($mypcost,2,".",$thousands_sep = ","); ?></td>
			</tr>
<?php

				if ($apresult->num_rows > 0) {
	
					while($ap = $apresult->fetch_array()) {
	
						$runningpmts = $runningpmts + $ap["amt"];
?>						
						<tr>
							<td width="5%" class="style6"></td>
							<td width="5%" class="style6"></td>
							<td width="20%" class="style6"></td>
							<td align="right" width="5%" class="style6"></td>
							<td align="right" width="5%" class="style6"></td>
							<td align="right" width="5%" class="style6"></td>
							<td align="right" width="5%" class="style6">&nbsp;</td>
							<td align="right" width="5%" class="style6"></td>
							<td align="right" width="5%" class="style6" style="width: 0%"></td>
							<td align="right" width="5%" class="style6" style="width: 2%">&nbsp;</td>
							<td align="right" width="5%" class="style6" style="width: 2%">&nbsp;</td>
							<td align="right" width="5%" class="style6" style="width: 2%">&nbsp;</td>
							<td align="right" width="5%" class="style6" style="width: 2%">&nbsp;</td>							
							<td align="right" width="5%" class="style6" style="width: 2%"><?php echo number_format($ap["amt"],2); ?>&nbsp;</td>
							<td align="right" width="5%" class="style6" style="width: 2%"><?php echo $ap["ptype"]; ?>&nbsp;</td>
							<td align="right" width="5%" class="style6"></td>
							<td align="right" width="5%" class="style6"></td>
						</tr>
<?php						
						
					} // end of ap while
	
				} // end of ap result end if

			} // end of while
	
		}else{
			echo "No Repair Orders found ";
		} //end if
	
?>
		
<?php		

	//} // end of overall if

?>

<tr>
		<td width="5%" class="style5">Totals</td>
		<td width="5%"></td>
		<td width="20%"></td>
		<td align="right" width="5%" class="style7"><?php echo number_format($labor,2,".",$thousands_sep = ","); ?></td>
		<td align="right" width="5%" class="style7"><?php echo number_format($parts,2,".",$thousands_sep = ","); ?></td>
		<td align="right" width="5%" class="style7"><?php echo number_format($sublet,2,".",$thousands_sep = ","); ?></td>
		<td align="right" width="5%" class="style7"><?php echo number_format($userfee1,2,".",$thousands_sep = ","); ?></td>
		<td align="right" width="5%" class="style7"><?php echo number_format($userfee2,2,".",$thousands_sep = ","); ?></td>
		<td align="right" width="5%" class="style7"></td>
		<td align="right" width="5%" class="style7"><?php echo number_format($hazmat,2,".",$thousands_sep = ","); ?></td>
		<td align="right" width="5%" class="style7"><?php echo number_format($slstax,2,".",$thousands_sep = ","); ?></td>
		<td align="right" width="5%" class="style7" style="width: 0%"><?php echo number_format($ttldisc,2,".",$thousands_sep = ","); ?></td>
		<td align="right" width="5%" class="style7" style="width: 2%"></td>
		<td align="right" width="5%" class="style7" style="width: 2%"><?php echo number_format($runningpmts,2,".",$thousands_sep = ","); ?></td>
		<td align="right" width="5%" class="style7"></td>
		<td align="right" width="5%" class="style7"><?php echo number_format($tro,2,".",$thousands_sep = ","); ?></td>
		<td align="right" width="5%" class="style7"><?php echo number_format($pcost,2,".",$thousands_sep = ","); ?></td>
</tr>

</table>

</body>

</html>
