<?php
require CONN;
//require("../../functions.php");

$shopid = $_COOKIE['shopid'];

if (isset($_GET['excel']) == "y") {

		header('Content-Type: application/vnd.ms-excel');
		header('Content-Disposition: attachment; '.'filename="TechPay.xls"');
}
if (isset($_GET['excel'])) {
	$excel = $_GET['excel'];
	if ($excel == "y") {
		echo "you are here";
		$startdate = $_GET['sd'];
		$sd = date_format(new DateTime($startdate),'Y-m-d');
		$enddate = $_GET['ed'];
		$ed = date_format(new DateTime($enddate),'Y-m-d');

	}
}

if (isset($_GET['sd'])) {
	$startdate = $_GET['sd'];
	$sd = date_format(new DateTime($startdate),'Y-m-d');
}

if (isset($_GET['ed'])) {
	$enddate = $_GET['ed'];
	$ed = date_format(new DateTime($enddate),'Y-m-d');
}

if (isset($_POST['sd'])) {
	$startdate = $_POST['sd'];
	$sd = date_format(new DateTime($startdate),'Y-m-d');
}

if (isset($_POST['ed'])) {
	$enddate = $_POST['ed'];
	$ed = date_format(new DateTime($enddate),'Y-m-d');
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
<title>Tech Pay</title>

<style type="text/css">
body{
	font-family:Arial, Helvetica, sans-serif
}
.auto-style1 {
	background-color: #C0C0C0;
}
</style>
<script language="javascript">
	function printSPR(){
		document.getElementById('buttons').style.display = 'none';
		window.print();
		setTimeout("showButtons()",1000)
	}
	function showButtons(){
		document.getElementById('buttons').style.display = 'block';
	}
</script>

</head>

<body>

<div class="text-center">
<h3>Tech Pay Report - GRIFFIS AUTOMOTIVE REPAIR</h3>
<h3><?php echo $startdate . " to " . $enddate;?></h3></div>
<br/>
<?php

	$stmt = "SELECT replacerowithtag ";
	$stmt .= "FROM company ";
	$stmt .= "WHERE shopid = ? ";
	//echo $stmt;

	if($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
		$query->execute();
		$coresult = $query->get_result();
	}else{
		echo "Company Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

	$co = $coresult->fetch_array();

	$rrwt = strtolower($co["replacerowithtag"]);


	$stmt = "SELECT  ro.shopid,ro.roid,ro.tagnumber,ro.statusdate,ro.customer,laborhours,laborid,vehinfo,linetotal,tech,l.deleted,l.hourlyrate,l.memorate ";
	$stmt .= " from repairorders ro ";
	$stmt .= " LEFT JOIN labor l ";
	$stmt .= "   on ro.shopid = l.shopid and ro.roid = l.roid ";
	$stmt .= " WHERE ro.shopid = ?";
	$stmt .= "  AND ro.status in ('closed','final') ";
	$stmt .= "  AND ro.statusdate >= ? ";
	$stmt .= "  AND ro.statusdate <= ? ";
	$stmt .= "  AND l.deleted = 'no' ";
	$stmt .= "  AND ro.rotype != 'No Approval' ";
	$stmt .= "  AND tech != 'DISCOUNT, DISCOUNT' ";
	$stmt .= " ORDER BY tech,roid";

	if($query = $conn->prepare($stmt)){
				$query->bind_param("sss",$shopid,$sd,$ed);
				$query->execute();
				$roresult = $query->get_result();
	}else{
				echo "ros Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

	$ro = mysqli_fetch_assoc($roresult);

	$tech = $ro["tech"];

	// resetting the result set pointer to beginning
	mysqli_data_seek($roresult, 0);

?>

<table style="width: 100%" cellpadding="3" cellspacing="0">
	<tr>
		<td class="auto-style1"><strong>RO#</strong></td>
		<td class="auto-style1"><strong>LaborID</strong></td>
		<td class="auto-style1"><strong>Date</strong></td>
		<td class="auto-style1"><strong>Customer</strong></td>
		<td class="auto-style1"><strong>Vehicle</strong></td>
		<td class="auto-style1"><strong>Labor</strong></td>
		<td class="auto-style1"><strong>Hours</strong></td>
		<td class="auto-style1"><strong>Tech</strong></td>
		<td class="auto-style1"><strong>Pay Rate</strong></td>
		<td class="auto-style1"><strong>Tech Pay</strong></td>
	</tr>
<?php

if ($roresult->num_rows > 0) {
	$techpay = 0;
	$rtechpay = 0;
	$tech = $ro["tech"];
	$totalpay = 0;

	while($ro = $roresult->fetch_array()) {

		if ($tech <> $ro["tech"]) {
			if (is_numeric($rtechpay) ) {
					$rtechpaydisplay = asDollars($rtechpay);
			}else{
					$rtechpaydisplay = $rtechpay;
			}
?>
	<tr>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td><strong><?php echo "TOTAL " ;?></strong></td>
		<td></td>
		<td><strong><?php echo asDollars($totalpay); ?></strong></td>
	</tr>
<?php
			$totalpay = 0;

			$tech = $ro["tech"];
		}

		//$tech = $ro["tech"];

		$statusdate = new Datetime($ro["statusdate"]);

		if ($rrwt == "yes") {
			$displayroid = $ro["tagnumber"];
		}else{
			$displayroid = $ro["roid"];
		}
		if (is_numeric($ro["linetotal"])) {
			$linetotal = $ro["linetotal"];
		}else{
			$linetotal = 0.00;
		}

		$paytype = "";
		$payrate = 0;

		$stmt = "SELECT concat(employeelast,', ',employeefirst) emp, hourlyrate,paytype ";
		$stmt .= " FROM employees ";
		$stmt .= " WHERE shopid = ?";
		$stmt .= "   AND concat(employeelast,', ',employeefirst) = ? ";
		if ($query = $conn->prepare($stmt)) {
			$query->bind_param("ss", $shopid,$tech);
			$query->execute();
			$empresult = $query->get_result();

		}else{
			echo "Employee prepare failed  (" . $conn->errno . ")" . $conn->error;
		}

		$emp = mysqli_fetch_assoc($empresult);

		if ($empresult->num_rows > 0) {

			$paytype = $emp["paytype"];
			$payrate = $emp["hourlyrate"];

			$laborchg = $ro["linetotal"];
			if (strtolower($paytype) == "percentage") {
				//echo $ro["tech"] . ":" . $paytype . "<BR>";
				if (is_numeric($payrate) and $payrate > 0) {
					if ($linetotal == 0) {
						// calculate the charged labor and
						$linetotal = $ro["laborhours"] * $ro["memorate"];
						$techpay = asDollars(round($linetotal * ($payrate/100),2),2);
						//echo $ro["laborhours"] . ":" . $ro["hourlyrate"] . ":" . $linetotal . ":" . $techpay . "<BR>";
					}else{
						//$techpaydisplay = asDollars(round($laborchg * ($payrate/100),2),2);
						$techpay = round($laborchg * ($payrate/100),2);
					}
				}else{
					$techpay = 0.00;
				}

			}elseif ($paytype == "flatrate") {
				$techpay = $ro["laborhours"] * $payrate;
				$rtechpay = $rtechpay + $techpay;

			}elseif ($paytype == "hourly") {
				$techpay = $ro["laborhours"] * $payrate;
				$rtechpay = $rtechpay + $techpay;
			}

?>
	<tr>
		<td><?php echo $ro["roid"]; ?></td>
		<td><?php echo $ro["laborid"]; ?></td>
		<td><?php echo date_format($statusdate,'m/d/Y'); ?></td>
		<td><?php echo $ro["customer"]; ?></td>
		<td><?php echo $ro["vehinfo"]; ?></td>
		<td><?php echo number_format($linetotal,2); ?></td>
		<td><?php echo $ro["laborhours"]; ?></td>
		<td><?php echo $ro["tech"]; ?></td>
		<td>
<?php
		if ($paytype == "PERCENTAGE") {
			echo $payrate . "%" ;
		}elseif ($paytype == "FLATRATE") {
			echo "flat" ;
		}elseif ($paytype == "HOURLY") {
			echo "$" . $payrate ;
		}else{
			echo "Paytype undefined";
		}
?>
		</td>
		<td><?php if (is_numeric($techpay) ) { echo asDollars($techpay);}else{ $techpay = 0; } ?></td>
	</tr>
<?php

		$totalpay = $totalpay + $techpay;

		} // end of emp if

	} // end of while
} // end if

?>
	<tr>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td><strong><?php echo "TOTAL " ;?></strong></td>
		<td></td>
		<td><strong><?php echo asDollars($totalpay); ?></strong></td>
	</tr>


</table>
</body>


</html>
