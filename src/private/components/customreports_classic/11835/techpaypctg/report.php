<?php
require CONN;
//require("../../functions.php");

$shopid = $_COOKIE['shopid'];

if (isset($_GET['excel']) == "y") {

		header('Content-Type: application/vnd.ms-excel');
		header('Content-Disposition: attachment; '.'filename="TechPay.xls"');
}
if (isset($_GET['excel'])) {
	$excel = $_GET['excel'];
	if ($excel == "y") {
		echo "you are here";
		$startdate = filter_var($_GET['sd'], FILTER_SANITIZE_STRING);
		$sd = date_format(new DateTime($startdate),'Y-m-d');
		$enddate = filter_var($_GET['ed'], FILTER_SANITIZE_STRING);
		$ed = date_format(new DateTime($enddate),'Y-m-d');

	}
}

if (isset($_GET['sd'])) {
	$startdate = filter_var($_GET['sd'], FILTER_SANITIZE_STRING);
	$sd = date_format(new DateTime($startdate),'Y-m-d');
}

if (isset($_GET['ed'])) {
	$enddate = filter_var($_GET['ed'], FILTER_SANITIZE_STRING);
	$ed = date_format(new DateTime($enddate),'Y-m-d');
}

if (isset($_POST['sd'])) {
	$startdate = filter_var($_POST['sd'], FILTER_SANITIZE_STRING);
	$sd = date_format(new DateTime($startdate),'Y-m-d');
}

if (isset($_POST['ed'])) {
	$enddate = filter_var($_POST['ed'], FILTER_SANITIZE_STRING);
	$ed = date_format(new DateTime($enddate),'Y-m-d');
}

$title = "Tech Pay Report";
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($companyname, $conamereports);
    $query->fetch();
    $query->close();
}

if ($conamereports == 'yes') {
    $title .= " - $companyname";
}

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
<title>Tech Pay</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700"/>
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.6.3/css/font-awesome.min.css"/>

<!-- Page JS Plugins CSS -->
<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css"/>
<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css"/>

<!-- Bootstrap and OneUI CSS framework -->
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css"/>
<link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css"/>
<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/datetimepicker/bootstrap-datetime.css"/>
<link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css"/>


<script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
<script src="<?= SCRIPT ?>/tipped.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

<!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
<script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
<script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
<script src="<?= SCRIPT ?>/app.js"></script>
<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
<script src="<?= SCRIPT ?>/plugins/moment/moment.js"></script>
<script src="<?= SCRIPT ?>/plugins/datetimepicker/bootstrap-datetime.js"></script>

<style type="text/css">
body{
	font-family:Arial, Helvetica, sans-serif
}
.auto-style1 {
	background-color: #C0C0C0;
}
</style>
<script language="javascript">
	function printSPR(){
		document.getElementById('buttons').style.display = 'none';
		window.print();
		setTimeout("showButtons()",1000)
	}
	function showButtons(){
		document.getElementById('buttons').style.display = 'block';
	}
</script>

</head>

<body>
<p id="buttons">
<span class="btn btn-primary" id="pbtn" style="float:left;margin-right:5px" onclick="printSPR()">Print</span>
<span class="btn btn-info" id="donebtn" style="float:left;margin-right:5px" onclick="location.href='<?= COMPONENTS_PRIVATE ?>/reports/reports.php'">Done</span>
<span class="btn btn-warning" id="excelbtn" style="float:left" onclick="location.href='report_excel.php?excel=y&sd=<?php echo $startdate;?>&ed=<?php echo $enddate;?>'">Export to Excel</span>
</p>

<div class="text-center">
<h3><?= $title; ?></h3>
<h3><?php echo $startdate . " to " . $enddate;?></h3></div>
<br/>
<?php

	$stmt = "SELECT replacerowithtag ";
	$stmt .= "FROM company ";
	$stmt .= "WHERE shopid = ? ";
	//echo $stmt;

	if($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
		$query->execute();
		$coresult = $query->get_result();
	}else{
		echo "Company Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

	$co = $coresult->fetch_array();

	$rrwt = strtolower($co["replacerowithtag"]);


	$stmt = "SELECT  ro.shopid,ro.roid,ro.tagnumber,ro.statusdate,ro.customer,laborhours,laborid,vehinfo,linetotal,tech,l.deleted,l.hourlyrate,l.memorate ";
	$stmt .= " from repairorders ro ";
	$stmt .= " LEFT JOIN labor l ";
	$stmt .= "   on ro.shopid = l.shopid and ro.roid = l.roid ";
	$stmt .= " WHERE ro.shopid = ?";
	$stmt .= "  AND ro.status in ('closed','final') ";
	$stmt .= "  AND ro.statusdate >= ? ";
	$stmt .= "  AND ro.statusdate <= ? ";
	$stmt .= "  AND l.deleted = 'no' ";
	$stmt .= "  AND ro.rotype != 'No Approval' ";
	$stmt .= "  AND tech != 'DISCOUNT, DISCOUNT' ";
	$stmt .= " ORDER BY tech,roid";

	//echo $stmt;
	//printf(str_replace("?","'" . "%s" . "'",$stmt),$shopid,$sd,$ed) ;

	if($query = $conn->prepare($stmt)){
				$query->bind_param("sss",$shopid,$sd,$ed);
				$query->execute();
				$roresult = $query->get_result();
	}else{
				echo "ros Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

	$ro = mysqli_fetch_assoc($roresult);

	$tech = $ro["tech"];

	// resetting the result set pointer to beginning
	mysqli_data_seek($roresult, 0);

?>

<table style="width: 100%" cellpadding="3" cellspacing="0">
	<tr>
		<td class="auto-style1"><strong>RO#&nbsp;</strong></td>
		<td class="auto-style1"><strong>LaborID&nbsp;</strong></td>
		<td class="auto-style1"><strong>Date</strong></td>
		<td class="auto-style1"><strong>Customer&nbsp;</strong></td>
		<td class="auto-style1"><strong>Vehicle&nbsp;</strong></td>
		<td class="auto-style1"><strong>Labor&nbsp;</strong></td>
		<td class="auto-style1"><strong>Hours&nbsp;</strong></td>
		<td class="auto-style1"><strong>Tech&nbsp;</strong></td>
		<td class="auto-style1"><strong>Pay Rate&nbsp;</strong></td>
		<td class="auto-style1"><strong>Tech Pay&nbsp;</strong></td>
	</tr>
<?php

if ($roresult->num_rows > 0) {
	$techpay = 0;
	$rtechpay = 0;
	$tech = $ro["tech"];
	$totalpay = 0;

	while($ro = $roresult->fetch_array()) {

		if ($tech <> $ro["tech"]) {
			//echo "Change in Tech" . "</br>";
			if (is_numeric($rtechpay) ) {
				$rtechpaydisplay = asDollars($rtechpay);
			}else{
				$rtechpaydisplay = $rtechpay;
			}
?>
	<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td><strong><?php echo "TOTAL " ;?></strong></td>
		<td><strong><?php echo asDollars($totalpay); ?></strong></td>
	</tr>
<?php
			$totalpay = 0;

			$tech = $ro["tech"];
		}

		//$tech = $ro["tech"];

		$statusdate = new Datetime($ro["statusdate"]);

		if ($rrwt == "yes") {
			$displayroid = $ro["tagnumber"];
		}else{
			$displayroid = $ro["roid"];
		}
		if (is_numeric($ro["linetotal"])) {
			$linetotal = $ro["linetotal"];
		}else{
			$linetotal = 0.00;
		}

		$paytype = "";
		$payrate = 0;

		$stmt = "SELECT concat(employeelast,', ',employeefirst) emp, hourlyrate,paytype ";
		$stmt .= " FROM employees ";
		$stmt .= " WHERE shopid = ?";
		$stmt .= "   AND concat(employeelast,', ',employeefirst) = ? ";
		if ($query = $conn->prepare($stmt)) {
			$query->bind_param("ss", $shopid,$tech);
			$query->execute();
			$empresult = $query->get_result();

		}else{
			echo "Employee prepare failed  (" . $conn->errno . ")" . $conn->error;
		}
		//printf(str_replace("?","'" . "%s" . "'",$stmt),$shopid,$tech)  ;

		$emp = mysqli_fetch_assoc($empresult);

		if ($empresult->num_rows > 0) {

			$paytype = $emp["paytype"];
			$payrate = $emp["hourlyrate"];

			$laborchg = $ro["linetotal"];
			if (strtolower($paytype) == "percentage") {

				if (is_numeric($payrate) and $payrate > 0) {
				//echo $ro["tech"] . ":" . $paytype . ":" . $payrate . ":" . $linetotal .  "<BR>";


					if ($linetotal == 0) {
						//echo $ro["tech"] . ":" . $paytype . ":" . $payrate . "<BR>";
						// calculate the charged labor and
						$linetotal = $ro["laborhours"] * $ro["memorate"];
						$techpay = asDollars(round($linetotal * ($payrate/100),2),2);
						//echo $ro["laborhours"] . ":" . $ro["hourlyrate"] . ":" . $linetotal . ":" . $techpay . "<BR>";
					}else{
						//$techpaydisplay = asDollars(round($laborchg * ($payrate/100),2),2);
						$techpay = round($laborchg * ($payrate/100),2);
					}
				}else{
					$techpay = 0.00;
				}


			}elseif ($paytype == "flatrate") {
				$techpay = $ro["laborhours"] * $payrate;
				$rtechpay = $rtechpay + $techpay;

			}elseif ($paytype == "hourly") {
				$techpay = $ro["laborhours"] * $payrate;
				$rtechpay = $rtechpay + $techpay;
			}

?>
	<tr>
		<td><?php echo $displayroid; ?>&nbsp;</td>
		<td><?php echo $ro["laborid"]; ?>&nbsp;</td>
		<td><?php echo date_format($statusdate,'m/d/Y'); ?>&nbsp;</td>
		<td><?php echo $ro["customer"]; ?>&nbsp;</td>
		<td><?php echo $ro["vehinfo"]; ?>&nbsp;</td>
		<td><?php echo asDollars($linetotal,2); ?>&nbsp;</td>
		<td><?php echo $ro["laborhours"]; ?>&nbsp;</td>
		<td><?php echo $ro["tech"]; ?>&nbsp;</td>
		<td>
<?php
		if ($paytype == "PERCENTAGE") {
			echo $payrate . "%" ;
		}elseif ($paytype == "FLATRATE") {
			echo "flat" ;
		}elseif ($paytype == "HOURLY") {
			echo "$" . $payrate ;
		}else{
			echo "Paytype undefined";
		}
?>

		</td>
		<td><?php if (is_numeric($techpay) ) { echo asDollars($techpay);}else{ $techpay = 0; } ?>&nbsp;</td>

	</tr>
<?php

		//$totalpay = settype($totalpay,"double") + settype($techpay,"double") ;
		$totalpay = $totalpay + $techpay;
		//echo "Tech pay for " . $tech . "is:  " . $totalpay . "</br>";

		} // end of emp if

		// check for change in emp


	} // end of while
} // end if

?>
	<tr>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td>&nbsp;</td>
		<td><strong><?php echo "TOTAL " ;?></strong></td>
		<td><strong><?php echo asDollars($totalpay); ?></strong></td>
	</tr>

</table>
</body>

<?php

//}else{
//	echo "No records found";
//} //end if
?>

</html>
