
<?php
	$title = 'Custom Part Code Report';
	// Use this for Custom Reports
	include(COMPONENTS_PRIVATE_PATH. '/reports/includes/report_config.php');

	// Global Variables
	$shopid = $_COOKIE['shopid'];
	$date = new DateTime('now');
	$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
	$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
	$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
	$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
	$sd = $sdate;
	$ed = $edate;
	$sd1=date('Y-m-d',strtotime($sd));
	$ed1=date('Y-m-d',strtotime($ed));

    $partNumber = isset($_REQUEST['partNumber']) ? filter_var($_REQUEST['partNumber'], FILTER_SANITIZE_STRING) : "all";
    $partNumberSQL = "";
    if ($partNumber) {
        $partNumberSQL = "AND p.PartCode = '" . $partNumber . "'";
    }

	// Use this for Custom Reports
	$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder

    // Query Starts Here
    $tablefields=array(
        'RO #',
        'Part #',
        'Part Code',
        'Description',
        'Date',
        'Quantity',
        'Cost',
        'Selling Price',
        'Total Cost',
        'Total Price',
        'Discount',
        'GP'
    );
    $alldata=array();

    $total = [
        "quantity" => 0,
        "cost" => 0,
        "price" => 0,
        "totalCost" => 0,
        "totalPrice" => 0,
        "discount" => 0,
        "gp" => 0,
    ];

    $statement="SELECT 
                    p.PartNumber,
                    p.PartCode,
                    p.PartDesc,
                    p.Quantity,
                    p.Cost,
                    p.PartPrice,
                    p.LineTTLCost,
                    p.LineTTLPrice,
                    p.ts,
                    p.roid
                FROM parts p
                WHERE 
                    p.shopid = ?
                    AND p.deleted = 'no'
                    AND PartDesc != 'DISCOUNT'
                    $partNumberSQL
                    AND ts BETWEEN ? AND ?";

    if ($query = $conn->prepare($statement)) {
        $query->bind_param("sss", $shopid, $sd1, $ed1);
        $query->execute();
        $result = $query->get_result();

        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $alldata[]=array(
                    $row['roid'],
                    strtoupper($row['PartNumber']),
                    strtoupper($row['PartCode']),
                    strtoupper($row['PartDesc']),
                    date("m/d/Y h:i A", strtotime($row['ts'])),
                    $row['Quantity'],
                    asDollars($row['Cost']),
                    asDollars($row['PartPrice']),
                    asDollars($row['LineTTLCost']),
                    asDollars($row['LineTTLPrice']),
                    asDollars(($row['PartPrice'] * $row['Quantity']) - $row['LineTTLPrice']),
                    asDollars($row['LineTTLPrice'] - $row['LineTTLCost'])
                );

                $total['quantity'] += $row['Quantity'];
                $total['cost'] += $row['Cost'];
                $total['price'] += $row['PartPrice'];
                $total['totalCost'] += $row['LineTTLCost'];
                $total['totalPrice'] += $row['LineTTLPrice'];
                $total['discount'] += ($row['PartPrice'] * $row['Quantity']) - $row['LineTTLPrice'];
                $total['gp'] += $row['LineTTLPrice'] - $row['LineTTLCost'];
            }

            $alldata[]=array(
                "TOTALS",
                "",
                "",
                "",
                "",
                $total['quantity'],
                asDollars($total['cost']),
                asDollars($total['price']),
                asDollars($total['totalCost']),
                asDollars($total['totalPrice']),
                asDollars($total['discount']),
                asDollars($total['gp'])
            );
        }
        $query->close();
    } else {
        echo "Data Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
?>
<!DOCTYPE html>
<html>
	<body>

		<?php
		include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_buttons.php");
		?>

<table class="table table-condensed table-header-bg">
            <thead>
                <tr class="table_header table_head">
                    <?php foreach ($tablefields as $field): ?>
                        <td><?= $field ?></td>
                    <?php endforeach; ?>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($alldata as $row): ?>
                    <?php $isBold = strpos($row[0], 'TOTAL') !== false; ?>
                    <tr<?= $isBold ? ' style="font-weight:bold;"' : '' ?>>
                        <?php foreach ($row as $cell): ?>
                            <td><?= $cell; ?></td>
                        <?php endforeach; ?>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

		<?php
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/footer_reports.php");
			include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_form.php");
		?>
	</body>
</html>
<?php
	if(isset($conn)){
		mysqli_close($conn);
	}
?>
