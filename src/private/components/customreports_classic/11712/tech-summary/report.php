<?php
$title = 'Technician Production Summary';
// Use this for Gen Pop Reports
require(COMPONENTS_PRIVATE_PATH."/reports/includes/header_reports.php");
require CONN;

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));
$job_completed = isset($_REQUEST['job_completed']) ? filter_var($_REQUEST['job_completed'], FILTER_SANITIZE_STRING) : "";
$ro_status = isset($_REQUEST['ro_status']) ? filter_var($_REQUEST['ro_status'], FILTER_SANITIZE_STRING) : "Closed";
// Page Variables
$subtitle = 'This Report Includes ' . $ro_status . ' Status Repair Orders';  // Report SubTitle Goes Here - Hide if not needed
if ($job_completed == "yes") {
    $subtitle .= " and job status marked complete";
}
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($companyname, $conamereports);
    $query->fetch();
    $query->close();
}

if ($conamereports == 'yes') {
    $title .= " - $companyname";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_tech_sum.php'; //Only change if a custom PHPExcel is created in the template folder
?>

<title><?= $title; ?></title>

<!DOCTYPE html>
<html>
<body>

<?php
// Use this for Gen Pop Reports
include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_buttons.php");

$roStatusSQL = "";
if ($ro_status != "all") {
    if ($ro_status != "open") {
        $roStatusSQL = "AND status = '" . $ro_status . "'";
    } else {
        $roStatusSQL = "AND status != 'Closed'";
    }
}


$jobSQL = "";
if (!empty($job_completed) && $job_completed == "yes") {
    $jobSQL = "AND c.acceptdecline = 'Job Complete'";
}


?>
<!-- Column Headers Insert Report Variables here -->
<table class=report_table>
    <tr class="table_header table_head">
        <td>Technician Name</td>
        <td>Hours Sold</td>
        <td>Gross Sale</td>
    </tr>
    <?php
    $tablefields = array('Technician Name', 'Hours Sold', 'Gross Sale');//set table headings array for excel export
    $alldata = array();//this will hold all the data arrays to be exported to excel

    $tar = "";
    // Insert DB Query Here
    $tstmt = "select roid from repairorders where rotype != 'no approval' and shopid = ? and statusdate >= ? and statusdate <= ? $roStatusSQL";
    if ($query = $conn->prepare($tstmt)) {
        $query->bind_param("sss", $shopid, $sd1, $ed1);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_assoc()) {
            $tar .= $rs['roid'] . ",";
        }

    } else {
        echo "103-RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }


    if (substr($tar, -1) == ",") {
        $tar = substr($tar, 0, strlen($tar) - 1);
    }

    // Template Query Begins - Replace entire section
    $runttlhrs = 0;
    $totalGrossSale = 0;
    if (!empty($tar)) {
        $stmt = "SELECT 
                    UPPER(labor.tech) AS tech, 
                    SUM(labor.laborhours) AS techHours, 
                    labor.HourlyRate AS laborHourlyRate, 
                    e.hourlyrate AS employeeHourlyRate
                FROM labor 
                JOIN complaints c 
                    ON c.shopid = labor.shopid 
                    AND c.roid = labor.ROID 
                    AND labor.complaintid = c.complaintid
                JOIN employees e 
                    ON e.shopid = labor.shopid 
                    AND labor.Tech = CONCAT(e.employeelast, ', ', e.employeefirst)
                WHERE labor.deleted = 'no' 
                    AND labor.Tech != 'discount, discount' 
                    AND labor.shopid = ?
                    AND labor.roid in ($tar)
                    $jobSQL
                GROUP BY labor.tech";
        // echo $stmt;
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $roresult = $query->get_result();

            while ($ro = $roresult->fetch_array()) {
                $hours = $ro["techHours"];
                $runttlhrs += $hours;
                $grossSale = ($ro["techHours"] * $ro['laborHourlyRate']) - ($ro["techHours"] * $ro['employeeHourlyRate']);
                $totalGrossSale += $grossSale;
                ?>
                <tr>
                    <td><?= strtoupper($ro["tech"]); ?></td>
                    <td><?= $hours; ?></td>
                    <td><?= asDollars($grossSale); ?></td>
                </tr>
                <?php
                $alldata[] = array(strtoupper($ro["tech"]), strtoupper($ro["techHours"]), asDollars($grossSale));//fill up the alldata array with the arrays of data to be shown in excel export
            } // end of while for loop
            $query->close();
        } else {
            echo "133-RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }
        // end if for end of file
    }
    $alldata[] = array('', '');
    $alldata[] = array('TOTALS', $runttlhrs, asDollars($totalGrossSale))
    ?>
    <tr>
        <td><b>Total Hours</b></td>
        <td><b><?= $runttlhrs; ?></b></td>
        <td><b><?= asDollars($totalGrossSale); ?></b></td>
    </tr>
</table>
<?php
include(COMPONENTS_PRIVATE_PATH."/reports/includes/footer_reports.php");
include(COMPONENTS_PRIVATE_PATH."/reports/includes/report_form.php");
?>
</body>
</html>
<?php
if (isset($conn)) {
    mysqli_close($conn);
}
?>
