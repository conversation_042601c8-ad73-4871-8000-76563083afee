<%
if len(request("shopid")) > 0 then
	response.cookies("shopid") = request("shopid")
end if
response.cookies("shopid") = request("shopid")
%>
<!-- #include file=../../../../sbp/aspscripts/connwoshopid.asp -->
<%
function formatDollar(d)

	if len(d) > 0 then
		formatDollar = formatcurrency(d)
	else
		formatDollar = "$0.00"
	end if

end function


'*** get company tax rate
stmt = "select * from company where shopid = '" & request("shopid") & "'"
set rs = con.execute(stmt)
rs.movefirst
logo = rs("logo")
trate = rs("defaulttaxrate")
licnum = rs("barno")
ss = ucase(rs("companyname"))
sa = ucase(rs("companyaddress"))
scsz = ucase(rs("companycity")) & ", " & ucase(rs("companystate")) & ". &nbsp;" & ucase(rs("companyzip"))
sph = ucase(rs("companyphone"))
ps = rs("psdisclosure")
hst = cdbl(rs("hst"))
gst = cdbl(rs("gst"))
pst = cdbl(rs("pst"))
qst = cdbl(rs("qst"))
chargehst = rs("chargehst")
chargepst = rs("chargepst")
chargegst = rs("chargegst")
chargeqst = rs("chargeqst")
timezone = rs("timezone")
cantax = 0
if chargehst = "yes" and hst > 0 then
	cantax = cdbl(cantax) + hst
end if

if chargegst = "yes" and gst > 0 then
	cantax = cdbl(cantax) + gst
end if

if chargepst = "yes" and pst > 0 then
	cantax = cdbl(cantax) + pst
end if

if chargeqst = "yes" and qst > 0 then
	cantax = cdbl(cantax) + qst
end if

showpartnumberonprintedps = rs("showpartnumberonprintedps")

'*** get the ps info
stmt = "select * from ps where shopid = '" & request("shopid") & "' and psid = " & request("psid")
set rs = con.execute(stmt)
psid = rs("psid")
cid = rs("cid")
pdate = rs("psdate")
c = rs("comments")
po = rs("ponumber")
status = rs("status")
statusdate = rs("statusdate")
closed = rs("closed")
writer = rs("writer")
tax = rs("tax")
canadiantax = rs("canadiantax")
'*** now get the customer info
stmt = "select * from customer where shopid = '" & request("shopid") & "' and customerid = " & cid
set rs = con.execute(stmt)
rs.movefirst
name = rs("lastname") & ", " & rs("firstname")
addr = rs("address")
csz = rs("city") & ", " & rs("state") & ". &nbsp;" & rs("zip")
ph = rs("homephone")
wp = rs("workphone")
cp = rs("cellphone")
if request("shopid") = "19394" then
    treatyno = rs("userdefined2")
    pstexempt = rs("userdefined3")
end if

strPhone = ""
if len(ph) >= 7 then
	strPhone = "Home: " & formatphone(ph)
	if len(wp) >= 7 then
		strPhone = strPhone & "<br>Work: " & formatphone(wp)
		if len(cp) >= 7 then
			strPhone = strPhone & "<br>Cell: " & formatphone(cp)
		end if
	end if
end if
if len(strPhone) = 0 and len(wp) >= 7 then
	strPhone = "Work: " & formatphone(wp)
	if len(cp) >= 7 then
		strPhone = strPhone & "<br>Cell: " & formatphone(cp)
	end if
end if
if len(strPhone) = 0 and len(cp) >= 7 then
	strPhone = "Cell: " & formatphone(cp)
end if

if request("shopid") = "4256" then
    pdate = statusdate
end if

surchargemessage = ""

surchargestmt = "select surchargemsg from `accountpayments-ps` where shopid = '" & request.cookies("shopid") & "' and psid = " & psid & " and surchargemsg is not null and surchargemsg != ''"
set srs = con.execute(surchargestmt)
if not srs.eof then
 surchargemessage = srs("surchargemsg")
end if
%>

<html>
<!-- Copyright 2011 - Boss Software Inc. --><head><meta name="robots" content="noindex,nofollow">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">

<title><%=request.cookies("shopname")%></title>
<link rel="stylesheet" type="text/css" href="pscss.css">
<style type="text/css">
<!--
#footer{
	/*position:absolute;
	top:695px;
	left:0px;*/
	
}
.obutton{
	height:30px;
	font-family:Arial, Helvetica, sans-serif;
	font-size:small;
	font-weight:bold;
	color:maroon;
	cursor:hand;
}
obutton:hover{
	color:blue;
}

A {text-decoration: none;}
A:link { text-decoration: none;}
A:visited { text-decoration: none;}
A:hover { text-decoration: none;}
p, td, th, li, body { font-size: 10pt; font-family: Verdana, Arial, Helvetica }
.style2 {
	color: #000000;
	background-color: #C0C0C0;
}
.style3 {
	text-align: right;
}
.style4 {
	color: #000000;
	text-align: right;
	background-color: #C0C0C0;
}
.style5 {
	text-align: right;
}
.style6 {
	color: #000000;
	text-align: right;
	background-color: #C0C0C0;
}
.style9 {

	font-size: small;
}
.style10 {

	font-size: small;
}

-->
</style>

</head>
<body  link="#800000" vlink="#800000" alink="#800000" topmargin="0" leftmargin="0" marginwidth="0" marginheight="0" sc"))">
<script type="text/javascript">


//x = setTimeout("closeSupplier()",2000)


function closeSupplier(){

	document.getElementById("popup").style.display = "none"
	document.getElementById("popuphider").style.display = "none"
	document.getElementById("popupdropshadow").style.display = "none"
	window.print();
	setTimeout("redir()",1000)
}

function redir(){

location.href='partsale.php?psid=<%=request("psid")%>'

}
</script>
<style type="text/css">
#popup{
	position:absolute;
	top:100px;
	left:100px;
	width:600px;
	height:500px;
	border:medium navy outset;
	text-align:center;
	color:black;
	display:none;
	z-index:999;
	background-color:white;
}
#popupdropshadow{
	position:absolute;
	top:115px;
	left:115px;
	width:600px;
	height:500px;
	text-align:center;
	color:black;
	display:none;
	z-index:998;
	background-color:black;
}
#popuphider{
	position:absolute;
	top:0px;
	left:0px;
	width:100%;
	height:300%;
	background-color:gray;
	-ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
	filter: alpha(opacity=70); 
	-moz-opacity:.70; 
	opacity: .7;
	z-index:997;
	display:none;

}

.innerBox1 {
	font-weight: bold;
	background-image: url('newimages/wipheader.jpg');
	color: #FFFFFF;
	text-align:center;
}

.innerBox2 {
	background-color: #FFFFFF;
	text-align: center;
}

</style>
<div id="popuphider"></div>
<div id="popup">
<br><br><br>Preparing page to print...
<br><br><br><img alt="" height="100" src="newimages/ajax-loader.gif" width="100"></div>
<div id="popupdropshadow"></div>


 <table border="0" cellpadding="0" cellspacing="0" style="width: 98%">
  <tr>
   <td valign="top" class="style10" style="width: 60%; height: 35px;">
<%
if request("shopid") <> "3029" then
	response.write ss & "<br>"
	response.write sa & "<br>"
	response.write scsz & "<br>"
	response.write formatphone(sph) & "<br>"
end if
%>
   </td>
   <td class="style10" style="width: 25%; height: 35px;">
   <%
   if len(logo) > 2 then
    if request.cookies("shopid") = "1526" then
    	response.write "<img width='200' height='109'  src='upload/" & request("shopid") & "/" & logo & "'>"
    elseif request.cookies("shopid") = "1734" then
    	response.write "<img width='250' height='100'  src='upload/" & request("shopid") & "/" & logo & "'>"
    elseif request.cookies("shopid") = "2839" then
    	response.write "<img width='150' height='110'  src='upload/" & request("shopid") & "/" & logo & "'>"
    elseif request.cookies("shopid") = "4075" then
    	response.write "<img max-height='80'  src='upload/" & request("shopid") & "/" & logo & "'>"
    elseif request.cookies("shopid") = "9177" then
    	response.write "<img style='width='250px' height='110px' src='http://" & request.servervariables("SERVER_NAME") & "/sbp/upload/" & request("shopid") & "/" & logo & "'>"
    elseif request.cookies("shopid") = "9833" then
    	response.write "<img style='width:250px; height:30px;' src='http://" & request.servervariables("SERVER_NAME") & "/sbp/upload/" & request("shopid") & "/" & logo & "'>"
   elseif request.cookies("shopid") = "8829" then
    	response.write "<img style='width:200px; height:80px;' src='http://" & request.servervariables("SERVER_NAME") & "/sbp/upload/" & request("shopid") & "/" & logo & "'>"
   elseif request.cookies("shopid") = "10118" then
    	response.write "<img style='width:150px; height:80px;' src='http://" & request.servervariables("SERVER_NAME") & "/sbp/upload/" & request("shopid") & "/" & logo & "'>"
  elseif request.cookies("shopid") = "15194" then
    	response.write "<img style='width:150px; height:80px;' src='http://" & request.servervariables("SERVER_NAME") & "/sbp/upload/" & request("shopid") & "/" & logo & "'>"

    elseif request.cookies("shopid") = "12871" then
    	response.write "<img style='width:250px; height:90px;' src='http://" & request.servervariables("SERVER_NAME") & "/sbp/upload/" & request("shopid") & "/" & logo & "'>"

    	elseif request.cookies("shopid") = "19220" then
            	response.write "<img style='width:250px; height:90px;' src='http://" & request.servervariables("SERVER_NAME") & "/sbp/upload/" & request("shopid") & "/" & logo & "'>"

        elseif request.cookies("shopid") = "10077" then
                            	response.write "<img style='max-height:90px; max-width:150px;' src='http://" & request.servervariables("SERVER_NAME") & "/sbp/upload/" & request("shopid") & "/" & logo & "'>"

    elseif request.cookies("shopid") = "19175" then
                	response.write "<img style='width:250px; height:90px;' src='http://" & request.servervariables("SERVER_NAME") & "/sbp/upload/" & request("shopid") & "/" & logo & "'>"

        else
	   	'response.write "<img style='max-height:90px;' src='upload/" & request("shopid") & "/" & logo & "'>"
	   	response.write "<img style='max-height:90px;max-width:150px;' src='http://" & request.servervariables("SERVER_NAME") & "/sbp/upload/" & request("shopid") & "/" & logo & "'>"
	end if
   else
   	response.write "<img style='max-height:90px;' src='newimages/invoicelogo.png'>"
   end if
   %>
   &nbsp;</td>
   <td align="center" class="style9" style="width: 15%; height: 35px;" valign="top">

   Invoice #PS-<%=psid%>
<%
if  len(licnum) > 0 then
%>
<br>Number: <%=licnum%>
<%
end if
%>
<%
if request("shopid") = "10077" then
rowspan = "5"
else
rowspan = "4"
end if
%>

</td>
  </tr>
  <tr>
   <td width="100%" valign="top" colspan="3">
   <table style="width: 100%" cellspacing="0" cellpadding="4">
	<tr>
		<td style="width: 0%;border:1px black ridge" valign="top" rowspan="<%=rowspan%>">Customer</td>
		<td style="width: 50%;border:1px black ridge" valign="top" rowspan="<%=rowspan%>"><%=name&"<br>"&addr&"<br>"&csz&"<br>"&strPhone & "<br>" %>
		<br><span style='font-size: 11px;line-height: 1em; '>
		<% if request("shopid") = "19394" then
		    if treatyno <> "" then
		        response.write "Treaty No. " & treatyno & "<br>"
		        end if
		    if pstexempt <> "" then
		        response.write "PST Exempt: " & pstexempt & "</span>"
		    end if
		end if
		 %>
		</td>
		<td style="width: 0%;border:1px black ridge" valign="top">Date</td>
		<td style="width: 50%;border:1px black ridge" valign="top"><%=pdate%>&nbsp;</td>
	</tr>
	<%
	if request("shopid") = "10077" then
	%>
	<tr>
    		<td style="width: 0%;border:1px black ridge" valign="top">
    		Date Closed</td>
    		<td style="width: 50%;border:1px black ridge" valign="top"><%
    		if status = "Closed" then
    		response.write statusdate
    		end if
    		%>&nbsp;</td>
    	</tr>
    	<%
    	end if
    	%>
	<tr>
		<td style="width: 0%;border:1px black ridge" valign="top">
		Comments</td>
		<td style="width: 50%;border:1px black ridge" valign="top"><%=ucase(c)%>&nbsp;</td>
	</tr>
	<tr>
		<td style="width: 10%;border:1px black ridge" valign="top">
		Service Writer</td>
		<td style="width: 50%;border:1px black ridge" valign="top"><%=ucase(writer)%>&nbsp;</td>
	</tr>
	<tr>
		<td style="width: 10%;border:1px black ridge" valign="top">
		PO #</td>
		<td style="width: 50%;border:1px black ridge" valign="top"><%=po%>&nbsp;</td>
	</tr>
	</table>
	</td>
  </tr>
 </table>


<p>
</p>
<%
'*** get the parts
ttlprice = 0
taxable = 0
nontax = 0
stmt = "select * from psdetail where shopid = '" & request("shopid") & "' and psid = " & psid
set rs = con.execute(stmt)
if not rs.eof then
	rs.movefirst
%>
	<table style="width: 98%" cellspacing="0" cellpadding="0">
		<tr>
			<% if lcase(showpartnumberonprintedps) = "yes" then %><td style="width: 15%" class="style2"><strong>Part Number</strong></td><% end if %>
			<td style="width: 40%" class="style2"><strong>Description</strong></td>
			<td style="width: 25%" class="style4"><strong>Qty<% if request("price") <> "no" then %>/Price<% end if %></strong></td>
			<% if request("price") <> "no" then %><td style="width: 10%" class="style6"><strong>Net</strong></td><% end if %>
		</tr>
<%
	do until rs.eof
		ttlprice = ttlprice + cdbl(rs("ext"))
		'response.write ttlprice & "<BR>"
		'*** calculate the discount amount
		d = (rs("qty") * cdbl(rs("price"))) - cdbl(rs("ext"))
		rd = rd + d
		pcost = pcost + cdbl(rs("cost"))
%>
		<tr>
			<% if lcase(showpartnumberonprintedps) = "yes" then %><td style="width: 15%"><%=ucase(rs("pnumber"))%>&nbsp;</td><% end if %>
			<td style="width: 40%"><%=ucase(rs("desc"))%>&nbsp;</td>
			<td style="width: 25%" class="style3"><%=rs("qty")%><% if request("price") <> "no" then %>&nbsp;@&nbsp;<%=formatdollar(rs("price"))%><% end if %>
			<%
			if lcase(rs("tax")) = "yes" then
				taxable = taxable + cdbl(rs("ext"))
				'response.write taxable
			else
				
				nontax = nontax + cdbl(rs("ext"))
				'response.write nontax
			end if
			%>
			</td>
			<% if request("price") <> "no" then %><td style="width: 10%" class="style5"><%=formatdollar(rs("ext"))%>&nbsp;</td><% end if %>
		</tr>
<%
		rs.movenext
	loop
end if

ttl = ttlprice + tax
dtaxable = formatcurrency(taxable)
dnontax = formatcurrency(nontax)
dtax = formatcurrency(tax)
dttl = formatcurrency(ttl)
dttlprice = formatcurrency(cdbl(taxable) + cdbl(nontax))
'if ttlprice > 0 then dttlprice = formatcurrency(ttlprice)
'if tax > 0 then dtax = formatcurrency(tax)
'if ttl > 0 then dttl = formatcurrency(ttl)

if not isnumeric(pcost) or isnull(pcost) or len(pcost) = 0 then
	pcost = "0"
end if
if not isnumeric(ttlprice) or isnull(ttlprice) or len(ttlprice) = 0 then
	ttlprice = "0"
end if
if not isnumeric(rd) or isnull(rd) or len(rd) = 0 then
	rd = "0"
end if
if not isnumeric(ttl) or isnull(ttl) or len(ttl) = 0 then
	ttl = "0"
end if

'*** now update the ps record
'stmt = "update ps set pcost = " & pcost & ", discount = " & rd & ", total = " & ttl & ", tax = " & tax & ", subtotal = " & ttlprice & " where shopid = '" & request("shopid") & "' and psid = " & psid
'response.write stmt
'con.execute(stmt)
%>
	</table><br/>
<strong>Payments Received:
</strong>
<table style="width: 100%">
	<%
	' get payments made on this ps
	totalsurcharge = 0
	stmt = "select * from `accountpayments-ps` where shopid = '" & request.cookies("shopid") & "' and psid = " & psid
	set rs = con.execute(stmt)
	if not rs.eof then
		do until rs.eof
		pnum = ""

		if len(rs("pnumber")) = 16 then
			pnum = ""
		else
			pnum = rs("pnumber")
		end if
		tpmts = tpmts + rs("amt")

		payamt = formatnumber(rs("amt")+rs("surcharge"),2)
		if rs("surcharge") > 0 then
		payamt = payamt & "*"
		totalsurcharge = cdbl(totalsurcharge) + cdbl(rs("surcharge"))
	    end if

	    if left(rs("pnumber"),3) = "360" and instr(rs("pnumber"),"~") > 0 then
			payar = split(rs("pnumber"),"~")	
			apprcode = payar(ubound(payar))  
			if apprcode <> "" then
				pnum = "AuthCode " & apprcode
			end if          
	    end if

	    if rs("last4") <> "" then
			pnum = pnum & " - CC# " & rs("last4")
		end if
	%>
	<tr>
		<td><%=rs("pdate")%>&nbsp;</td>
		<td><%=rs("ptype")%>&nbsp;</td>
		<td><%= pnum %>&nbsp;</td>
		<td style="text-align:right"><%=payamt%>&nbsp;</td>
	</tr>
	<%
			rs.movenext
		loop
	else
		tpmts = 0
	%>
	<tr>
		<td colspan="4">No Payments received&nbsp;</td>
	</tr>
	<%
	end if
	%>
</table>
<%

' PS canadian tax str col canadiantax hst, pst, gst, qst

hst_val = 0
pst_val = 0
gst_val = 0
qst_val = 0
cantaxstr = ""
if len(canadiantax) > 0 then
        cantxarr = split(canadiantax,",")
        hst_val = cantxarr(0)
        pst_val = cantxarr(1)
        gst_val = cantxarr(2)
        qst_val = cantxarr(3)

        if hst_val > 0 then
            hst_amount = dtaxable * (hst_val / 100)
            cantaxstr = "<div style='height:25px;width:80%; text-align:right'>HST "& formatnumber(hst_val)&"%</div><div style='padding-right:5px;border-right:1px black ridge;border-left:1px black ridge;height:25px; margin-top:-25px; width:98%;text-align:right'>"& formatcurrency(hst_val) &"</div>"
        end if

    if pst_val > 0 then
            pst_amount = dtaxable * (pst_val / 100)
            cantaxstr = cantaxstr & "<div style='height:25px;width:80%; text-align:right'>PST "& formatnumber(pst_val)&"%</div><div style='padding-right:5px;border-right:1px black ridge;border-left:1px black ridge;height:25px; margin-top:-25px; width:98%;text-align:right'>"& formatcurrency(pst_amount) &"</div>"
        end if

        if gst_val > 0 then
            gst_amount = dtaxable * (gst_val / 100)
            cantaxstr = cantaxstr & "<div style='height:25px;width:80%; text-align:right'>GST "& formatnumber(gst_val)&"%</div><div style='padding-right:5px;border-right:1px black ridge;border-left:1px black ridge;height:25px; margin-top:-25px; width:98%;text-align:right'>"& formatcurrency(gst_amount) &"</div>"
        end if

        if qst_val > 0 then
          qst_amount = dtaxable * (qst_val / 100)
          cantaxstr = cantaxstr & "<div style='height:25px;width:80%; text-align:right'>QST "& formatnumber(qst_val)&"%</div><div style='padding-right:5px;border-right:1px black ridge;border-left:1px black ridge;height:25px; margin-top:-25px; width:98%;text-align:right'>"& formatcurrency(qst_amount) &"</div>"
        end if
end if
%>
<div id="footer">
<% if request("price") <> "no" then %>
<div style="height:25px;width:80%; text-align:right">Total Taxable</div>
<div style="padding-right:5px;border-left:1px black ridge;border-top:1px black ridge;border-right:1px black ridge;height:25px; margin-top:-25px;width:98%;text-align:right;"><%=dtaxable%></div>
<div style="height:25px;width:80%; text-align:right">Total Non-Taxable</div><div style="padding-right:5px;border-right:1px black ridge;border-left:1px black ridge;height:25px; margin-top:-25px; width:98%;text-align:right"><%=dnontax%></div>
<div style="height:25px;width:80%; text-align:right">Subtotal</div><div style="padding-right:5px;border-right:1px black ridge;border-left:1px black ridge;height:25px; margin-top:-25px; width:98%;text-align:right"><%=dttlprice%></div>
<%
if request("shopid") = "20487" then
    response.write cantaxstr
end if
 %>
<div style="height:25px;width:80%; text-align:right">Sales Tax</div><div style="padding-right:5px;border-right:1px black ridge;border-left:1px black ridge;height:25px; margin-top:-25px; width:98%;text-align:right"><%=dtax %></div>
<div style="height:25px;width:80%; text-align:right">Total</div><div style="padding-right:5px;height:25px; margin-top:-25px; width:98%;text-align:right;border-left:1px black ridge;border-right:1px black solid"><%=dttl%></div>

<div style="height:25px;width:80%; text-align:right">Total Payments</div><div style="padding-right:5px;border-right:1px black ridge;border-left:1px black ridge;height:25px; margin-top:-25px; width:98%;text-align:right"><%=formatcurrency(tpmts)%></div>
<% if totalsurcharge <> 0 then %>
<div style="height:25px;width:80%; text-align:right">Credit Card Fee</div><div style="padding-right:5px;border-right:1px black ridge;border-left:1px black ridge;height:25px; margin-top:-25px; width:98%;text-align:right"><%=formatcurrency(totalsurcharge)%>*</div>
<% end if %>
<div style="height:25px;width:80%; text-align:right; border-bottom:1px black ridge;border-left:1px black ridge;">Balance</div><div style="padding-right:5px;border-left:1px black ridge;height:25px; margin-top:-25px; width:98%;text-align:right;border-right:1px black ridge;border-bottom:1px black solid"><%=formatcurrency(dttl-tpmts)%></div>
<div style="font-size:large;font-weight:bold;margin-top:-25px;height:25px;width:20px;text-align:left">X</div>
<% end if %>
<span style="font-size:10px;"><%=ps%></span>
</div>
<div></div>

<% if surchargemessage <> "" then %>
 <div style="font-size: 12px; text-align: left;position: fixed; bottom: 0px; width: 100%; padding-top: 20%">
 	*<%= surchargemessage %>
 </div>
<% end if %>

<div style="font-size: 12px; text-align: right;position: fixed; bottom: 0px; width: 100%; padding-top: 20%">
Date :
<%
response.write(formatdatetime(formatTimeClockTime(getCurrentLocalTime(timezone))))
%>
</div>


</body>

</html>
<%
'Copyright 2011 - Boss Software Inc.
%>