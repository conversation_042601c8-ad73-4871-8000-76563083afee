﻿<?php

$shopid = $oshopid = $_COOKIE["shopid"];

// Global Variables
$date = new DateTime('now');
$component = "inventory-v2";
// Page Variables
$title = 'Inventory';
$subtitle = "";
include getRulesGlobal($component);

if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

$partSupplier = isset($_REQUEST['supplier']) ? filter_var($_REQUEST['supplier'], FILTER_SANITIZE_STRING) : "";
$sub = isset($_POST['sub']) ? filter_var($_POST['sub'], FILTER_SANITIZE_STRING) : "";
$poNumber = isset($_REQUEST['ponumber']) ? filter_var($_REQUEST['ponumber'], FILTER_SANITIZE_STRING) : "";
$poid = isset($_REQUEST['id']) ? filter_var($_REQUEST['id'], FILTER_SANITIZE_STRING) : 0;

$showtaxonro = 'no';
$sstmt = "SELECT LCASE(showtaxonpo) FROM settings where shopid = ?";
if ($query = $conn->prepare($sstmt)){
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($showtaxonro);
    $query->fetch();
    $query->close();
}

if ($sub == "yes") {
    $stmt = "update partsinventory set partcategory = ?, partsupplier = ?, onhand = onhand + ?, netonhand = netonhand + ?,partcost = ? where partid = ? and shopid = ?";

    $canned_query = "update cannedparts set partcost = ? where shopid = ? and partnumber = ?";

    $updatedesc = false;
    if ($pstmt = $conn->prepare($stmt)) {
        $pstmt->bind_param("ssdddis", $partcategory, $partSupplier, $onHand, $onHand, $partCost, $partId, $oshopid);

        $canned_stmt = $conn->prepare($canned_query);
        $canned_stmt->bind_param("dss", $partCost, $shopid, $part_number);

        foreach ($_POST as $i => $x) {
            if ($x == "on" && $i != "master") {
                $partId = $i;
                $partId = str_replace("partid", "", $partId);
                $qtypost = (!empty($_REQUEST["qtypost" . $partId]) ? $_REQUEST["qtypost" . $partId] : 0);
                $onHand = $qtypost;
                $partcategory = $_REQUEST["partcategory" . $partId] ?? '';
                $qty = $_REQUEST["qty" . $partId];
                $partCost = $_REQUEST["cost" . $partId];
                $part_number = $_REQUEST["partnumber" . $partId];
                if ($pstmt->execute()) {
                    $conn->commit();
                    if ($canned_stmt->execute()){
                        $conn->commit();
                    } else {
                        echo "Error updating canned job ".$conn->error;
                    }
                } else {
                    echo $conn->errno;
                }

                if ($qtypost == $qty) {
                    $piostmt = "update partsinventoryorder set done = 'yes' where shopid = ? and partid = ?";
                    if ($pioquery = $conn->prepare($piostmt)) {
                        $pioquery->bind_param("si", $shopid, $partId);
                        if ($pioquery->execute()) {
                            $conn->commit();
                        } else {
                            echo $conn->errno;
                        }
                    } else {
                        echo "pio Prepare Failed: (" . $conn->errno . ") " . $conn->error;
                    }
                } else {
                    $updatedesc = true;
                    $piostmt = "update partsinventoryorder set quantity = ? where shopid = ? and partid = ?";
                    if ($pioquery = $conn->prepare($piostmt)) {
                        $newqty = $qty - $qtypost;
                        $pioquery->bind_param("isi", $newqty, $shopid, $partId);
                        if ($pioquery->execute()) {
                            $conn->commit();
                        }
                    }
                }
            }
        }
        $pstmt->close();
    } else {
        echo "partsinventory Prepare Failed: (" . $conn->errno . ") " . $conn->error . " (on iteration $x)";
    }

    $stmt = "select count(*) c from partsinventoryorder where done != 'yes' and shopid = ? and ponumber = ?";
    $c = 0;
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $poNumber);
        $query->execute();
        $query->store_result();
        $query->bind_result($c);
        $query->fetch();
        if ($c == 0) {
            $sstmt = "update po set status = 'Closed', receiveddate = ?, invnum = ? where id = ? and shopid = ?";
            $date = date('Y-m-d');
            if ($squery = $conn->prepare($sstmt)) {
                $squery->bind_param("ssis", $date, $_POST['invnum'], $poid, $shopid);
                if ($squery->execute()) {
                    $conn->commit();
                } else {
                    echo $conn->errno;
                }
                $squery->close();
            } else {
                echo "po Prepare Failed: (" . $conn->errno . ") " . $conn->error;
            }

            // backoffice
            $apikey = "";
            $stmt = "select `locid` from backofficekeys where shopid = '$shopid' and active='yes'";
            if ($query = $conn->prepare($stmt)) {
                $query->execute();
                $query->bind_result($apikey);
                $query->fetch();
                $query->close();
            }

            if ($apikey != "") {
                require(INTEGRATIONS_PATH . "/backoffice/closepowebhook.php");
                BackofficePO($poid, $shopid);
            }
        } elseif ($updatedesc) {
            $sstmt = "update po set `desc`=concat(`desc`,' ','Partially Received'), invnum = ? where id = ? and shopid = ?";
            $date = date('Y-m-d');
            if ($squery = $conn->prepare($sstmt)) {
                $squery->bind_param("sis", $_POST['invnum'], $poid, $shopid);
                if ($squery->execute()) {
                    $conn->commit();
                } else {
                    echo $conn->errno;
                }
            }
        }
        $query->close();
    } else {
        echo "partsinventory Prepare Failed: (" . $conn->errno . ") " . $conn->error;
    }

    $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid= ? AND s.notification_type='116'";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->store_result();
        $num_rows = $query->num_rows;
        if ($num_rows > 0) {
            $query->bind_result($textcontent, $emailcontent, $popupcontent);
            $query->fetch();

            $popupContent = str_replace("*|PO|*", $poNumber, $popupcontent);
            $emailContent = str_replace("*|PO|*", $poNumber, $emailcontent);
            $textContent = str_replace("*|PO|*", $poNumber, $textcontent);

            $sstmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'116',?,?,?)";
            if ($squery = $conn->prepare($sstmt)) {
                $squery->bind_param("ssss", $shopid, $popupContent, $textContent, $emailContent);
                if ($squery->execute()) {
                    $conn->commit();
                } else {
                    echo $conn->errno;
                }
                $squery->close();
            } else {
                echo "notification Prepare Failed: (" . $conn->errno . ") " . $conn->error;
            }
        }
        $query->close();
    } else {
        echo "notification set retrieval Prepare Failed: (" . $conn->errno . ") " . $conn->error;
    }


    if ($_REQUEST["sp"] == "m") {
        header("Location:" . COMPONENTS_PRIVATE . "/v2/po/receivepo.php");
    } else {
        header("Location:inventoryorders.php");
    }
}

include getHeadGlobal('');
include getHeadComponent($component);
echo "<body>";
include getHeaderGlobal($component);
include getMenuGlobal($component);

?>

<!-- Main Container -->
<main id="main-container">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/inventory/inventoryorders.php" class="text-secondary">Manage PO's</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">Receive PO to Inventory</h2>
                    </div>
                    <hr />
                </div>
            </div>
        </div>
    </div>

    <form name="theform" id="poform" action="inventoryorderreceive.php<?php echo (!empty($poid)) ? "?id=" . $poid : ""; ?>" method="post">
        <input name="sub" value="yes" type="hidden" />
        <input name="id" value="<?php echo $_REQUEST['id']; ?>" type="hidden" />
        <input name="sp" value="<?php echo isset($_REQUEST['sp']) ? $_REQUEST['sp'] : ""; ?>" type="hidden" />

        <input type="hidden" id="dfield" />
        <?php
        $stmt = "select * from po where shopid = ? and id = ?";
        $poNumber = 0;
        $partSupplier = "";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("si", $shopid, $poid);
            $query->execute();
            $results = $query->get_result();
            $rs = $results->fetch_assoc();
            $poNumber = $rs["ponumber"];
            $partSupplier = $rs["issuedto"];
            $query->close();
        } else {
            echo "po Prepare Failed: (" . $conn->errno . ") " . $conn->error;
        }
        ?>
        <input name="supplier" value="<?php echo $partSupplier; ?>" type="hidden" />

        <table class="table table-sm">
            <tr>
                <td>PO Number</td>
                <td>
                    <input name="ponumber" value="<?php echo $rs['ponumber']; ?>" type="hidden" /><?php echo $rs['ponumber']; ?>
                </td>
                <td>Issue Date</td>
                <td><?php echo $rs['issuedate']; ?>&nbsp;</td>
            </tr>
            <tr>
                <td>Status</td>
                <td><?php echo $rs['status']; ?>&nbsp;</td>

                <td>Issued To</td>
                <td>
                    <?php echo $rs['issuedto']; ?>
                </td>
            </tr>
            <tr>
                <td>Comments</td>
                <td>
                    <?php echo $rs['desc']; ?>
                </td>
                <td>Invoice #</td>
                <td>
                    <div class="form-outline">
                        <input type="text" name="invnum" id="invnum" value="<?= $rs['invnum'] ?>" class="form-control">
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    <input class="btn btn-primary" name="Button1" onclick="checkPost()" type="button" value="Post to Inventory" />
                </td>
            </tr>
        </table>

        <?php
        $stmt = "select * from partsinventoryorder where shopid = ? and ponumber = ? order by PartDesc,PartNumber";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("si", $shopid, $poNumber);
            $query->execute();
            $results = $query->get_result();
        ?>
            <table id="partslist" class="sbdatatable w-100">
                <thead>
                    <tr>
                        <th>
                            <div class="form-check">
                                <input class="form-check-input" name="master" id="master" checked="checked" type="checkbox" onclick="selectAll()" />
                            </div>
                        </th>
                        <th>Part #</th>
                        <th>Description</th>
                        <th>Cost</th>
                        <?php if ($showtaxonro == "yes"){ ?>
                        <th>Tax</th>
                        <?php } ?>
                        <th>Supplier</th>
                        <th>Net On Hand</th>
                        <th>Qty to Order</th>
                        <th>Qty to Post</th>
                    </tr>
                </thead>
                <?php
                $partcategory = "";
                $stmt1 = "select partcategory, PartPrice, PartNumber from partsinventory where shopid = ? and partid = ?";
                if ($piquery = $conn->prepare($stmt1)) {
                    $piquery->bind_param("si", $oshopid, $rsPartId);
                    while ($rs = $results->fetch_assoc()) {
                        $rsPartId = $rs["partid"];
                        $piquery->execute();
                        $piquery->store_result();
                        $piquery->bind_result($partcategory,$partprice,$pipnum);
                        $piquery->fetch();
                ?>
                        <input type="hidden" name="cost<?php echo $rs['partid']; ?>" value="<?php echo $rs['partcost']; ?>">
                        <input type="hidden" name="price<?php echo $rs['partid']; ?>" value="<?php echo $partprice; ?>">
                        <input type="hidden" name="partnumber<?php echo $rs['partid']; ?>" value="<?php echo $pipnum; ?>">
                        <tr>
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input" <?= $rs['done'] != 'yes' ? "checked='checked'" : 'disabled' ?> id="partid<?php echo $rs['ponumber']; ?>" name="partid<?php echo $rs['partid']; ?>" value="on" type="checkbox" />
                                </div>
                            </td>
                            <td><?php echo $rs['partnumber']; ?></td>
                            <td><?php echo $rs['partdesc']; ?>&nbsp;</td>
                            <td>
                                <?php echo asDollars($rs['partcost']); ?>
                            </td>
                        <?php if ($showtaxonro == "yes"){ ?>
                            <td><?= asDollars($rs['salestax']) ?></td>
                                <?php } ?>
                            <td><?php echo $rs['partsupplier']; ?>&nbsp;</td>
                            <td><?php echo $rs['netonhandatorder']; ?>&nbsp;</td>
                            <td>
                                <?php
                                if ($partcategory == "UNK") {
                                    $cstmt = "select distinct category from category where shopid = ? order by displayorder";
                                    if ($cquery = $conn->prepare($cstmt)) {
                                        $cquery->bind_param("s", $shopid);
                                        $cquery->execute();
                                        $cresults = $cquery->get_result();
                                        $cquery->store_result();
                                        $num_rows = $cresults->num_rows;
                                        if ($num_rows > 0) {
                                ?>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <input name="qty<?php echo $rs['partid']; ?>" type="hidden" value="<?php echo $rs['quantity']; ?>" /><?php echo $rs['quantity']; ?>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-row">
                                                        <select class="select" name="partcategory<?php echo $rs['partid']; ?>">
                                                            <?php
                                                            while ($trs = $cresults->fetch_assoc()) {
                                                            ?>
                                                                <option value="<?php echo $trs["category"]; ?>"><?php echo $trs["category"]; ?></option>
                                                            <?php
                                                            }
                                                            ?>
                                                        </select>
                                                        <label class="form-label select-label" for="partcategory<?php echo $rs['partid']; ?>">Select a Category</label>
                                                    </div>
                                                </div>
                                            </div>
                                <?php } else {
                                            echo "<input class='form-control' name='qty" . $rs['partid'] . "' type='hidden' value='" . $rs['quantity'] . "'/>" . $rs['quantity'];
                                            echo "<input name='partcategory" . $rs['partid'] . "' type='hidden' value='" . $partcategory . "'/>";
                                        }
                                        $cquery->close();
                                    } else {
                                        echo "category Prepare Failed: (" . $conn->errno . ") " . $conn->error;
                                    }
                                } else {
                                    echo "<input class='form-control' name='qty" . $rs['partid'] . "' type='hidden' value='" . $rs['quantity'] . "'/>" . $rs['quantity'];
                                    echo "<input name='partcategory" . $rs['partid'] . "' type='hidden' value='" . $partcategory . "'/>";
                                }
                                ?>
                            </td>
                            <td>
                                <div class="form-outline">
                                    <input class='form-control' name="qtypost<?php echo $rs['partid']; ?>" type="text" <?= $rs['done'] == 'yes' ? 'disabled' : '' ?> value="<?php echo $rs['quantity']; ?>" />
                                    <label class="form-label" for="qtypost<?php echo $rs['partid']; ?>"><?= $rs['done'] == 'yes' ? "Received" : '' ?></label>
                                </div>
                            </td>
                        </tr>
            <?php
                    }
                    $piquery->close();
                } else {
                    echo "partcat Prepare Failed: (" . $conn->errno . ") " . $conn->error;
                }
                $query->close();
            } else {
                echo "partsinventory Prepare Failed: (" . $conn->errno . ") " . $conn->error;
            }
            ?>

            </table>
    </form>
</main>
<!-- END Main Container -->

<?php
include getScriptsGlobal('');
include getScriptsComponent($component);
include getFooterComponent($component);
?>
<script>
    function showAll() {
        sbconfirm(
            'Are you sure?',
            'Depending on how many parts you have in stock, this list could take some time to load. Do you wish to continue?',
            function() {
                location.href = "inventory.php?showall=yes"
            }
        );
    }

    function addBlankLine() {

        var table = document.getElementById("partslist");
        var row = table.insertRow(1);
        var cell0 = row.insertCell(0)
        var cell1 = row.insertCell(1)
        var cell2 = row.insertCell(2)
        var cell3 = row.insertCell(3)
        var cell4 = row.insertCell(4)
        var cell5 = row.insertCell(5)
        var cell6 = row.insertCell(6)
        r = Math.random()
        cell0.innerHTML = "<div class='form-check'><input class='form-check-input' id='partid" + r + "' name='partid" + r + "' checked='checked' type='checkbox'></div>"
        cell1.innerHTML = "<input class='form-control' id='partnumber" + r + "' name='partnumber" + r + "' type='text'>"
        cell2.innerHTML = "<input class='form-control' id='partdesc" + r + "' name='partdesc" + r + "' type='text'>"
        cell3.innerHTML = "<input class='form-control' id='partcost" + r + "' name='partcost" + r + "' type='text'>"
        cell4.innerHTML = "<input id='partsupplier" + r + "' name='" + r + "' type='hidden'>" + document.getElementById("issuedto").value.toUpperCase()
        cell5.innerHTML = "0"
        cell6.innerHTML = "<input class='form-control' id='qtytoorder" + r + "' name='qtytoorder" + r + "' type='text'>"

    }

    function deletePO(po, id) {
        sbconfirm(
            'Are you sure?',
            'This will delete this Purchase Order',
            function() {
                location.href = 'inventoryorderdelete.php?ponumber=' + po + '&poid=' + id
            }
        );
    }

    function selectAll() {

        var checkboxes = new Array();
        checkboxes = document.getElementsByTagName('input');

        for (var i = 0; i < checkboxes.length; i++) {
            if (checkboxes[i].type == 'checkbox' && !checkboxes[i].disabled) {
                if (checkboxes[i].checked == true) {
                    checkboxes[i].checked = false
                    c = false
                } else {
                    checkboxes[i].checked = true
                    c = true
                }
            }
        }
        document.getElementById("master").checked = c
    }

    function checkPost() {
        if ($('#invnum').val() == '') {
            sbalert("Please enter the Invoice #")
            return;
        } else
            $('#poform').submit()
    }
</script>
</body>

</html>

<?php
mysqli_close($conn);
?>
