<?php
require_once CONN;

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$partSupplier = $partsupp = $_POST['PartSupplier'];
$partNumber = filter_var(str_replace("&", " and ", $_POST['PartNumber']), FILTER_SANITIZE_STRING);
$partDesc = filter_var(str_replace("&", " and ", $_POST['PartDesc']), FILTER_SANITIZE_STRING);
$tax = filter_var($_POST['tax'], FILTER_SANITIZE_STRING);
$partPrice = filter_var($_POST['PartPrice'], FILTER_SANITIZE_STRING);
$partCode = filter_var($_POST["PartCode"], FILTER_SANITIZE_STRING);
$partCost = filter_var($_POST["PartCost"], FILTER_SANITIZE_STRING);
$bin = filter_var($_POST['bin'], FILTER_SANITIZE_STRING);
$onHand = filter_var($_POST['netOnHand'], FILTER_SANITIZE_STRING ); //not sure if it is a mistake or intentional. will check back when I can see the database.
$netOnHand = filter_var($_POST['netOnHand'], FILTER_SANITIZE_STRING);
$reOrderLevel = filter_var($_POST['ReOrderLevel'], FILTER_SANITIZE_STRING);
$maxOnHand = filter_var($_POST['MaxOnHand'], FILTER_SANITIZE_STRING);
$maintainStock = filter_var($_POST['MaintainStock'], FILTER_SANITIZE_STRING);
$partCategory = filter_var($_POST['PartCategory'], FILTER_SANITIZE_STRING);
$overrideMatrix = filter_var($_POST['overridematrix'], FILTER_SANITIZE_STRING);
$partId = filter_var($_POST['partid'], FILTER_SANITIZE_STRING);
$notes = isset($_POST['notes'])?filter_var($_POST['notes'], FILTER_SANITIZE_STRING):"";
$oldPartNumber = isset($_POST['oldpartnumber']) ? filter_var($_POST['oldpartnumber'], FILTER_SANITIZE_STRING) : "";
$oldPartPrice = isset($_POST['oldpartprice']) ? filter_var($_POST['oldpartprice'], FILTER_SANITIZE_STRING) : 0;
$oldPartSp = isset($_POST['oldpartsp']) ? filter_var($_POST['oldpartsp'], FILTER_SANITIZE_STRING) : 0;
$coreCharge = $_POST['corecharge']??'0';

if(in_array($shopid, array('13445','22957'))) $shopid = '22865';

$stmt = sprintf("SELECT table_name FROM information_schema.tables WHERE table_schema = 'shopboss' AND table_name = 'partsregistry-%s' LIMIT 1", $shopid);
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $results = $query->get_result();
    $query->store_result();
    $num_rows = $results->num_rows;
    if ($num_rows > 0) {
        $preg = "`partsregistry-" . $shopid . "`";
    } else {
        $preg = "partsregistry";
    }
    $query->close();
} else {
    echo "Prepared statement failed";
}

if (strtolower($_POST["MaintainStock"]) == "yes") {
    if (!empty($_POST["onhand"])) {
        if (strpos($_POST['onhand'], ",") > 0) {
            $onhand = doubleval(str_replace($_POST['onhand'], ",", ""));
        } else {
            $onhand = doubleval($_POST['onhand']);
        }
    } else {
        $onhand = 0;
    }
    
    $netonhand = $onhand;

    if ($_POST['itype'] == "non")
    {
        $whereField = 'partid';
        $whereValue = $partId;
        $whereType = 'i';
    }
    else
    {
       $whereField = 'partnumber';
       $whereValue = $partNumber;
       $whereType = 's'; 
    }

    $stmt = "DELETE from $preg where shopid = ? and {$whereField} = ?";
    if ($pstmt = $conn->prepare($stmt)) {
        $pstmt->bind_param("s{$whereType}", $shopid, $whereValue);
        if ($pstmt->execute()) {
            $conn->commit();
        } else {
            echo $conn->errno;
        }
        $pstmt->close();
        recordAudit("Non Inventory Deleted", "Deleted from parts noninventory.  Part Number: ".$partNumber.", PartID number: ".$partId);
    } else {
        echo "pi prepare failed : ( " . $conn->error . ") " . $conn->error;
    }

    $stmt = "select partid from partsinventory where shopid = ? and partnumber = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ss", $shopid, $partNumber);
        $query->execute();
        $results = $query->get_result();
        $query->store_result();
        $num_rows = $results->num_rows;
        if ($num_rows > 0 || $_POST['itype'] == "inv") 
        {
            $stmt = "UPDATE partsinventory SET notes = ?, partnumber = ?, PartDesc = ?, tax = ?, PartPrice = ?,PartCode = ?, PartCost = ?, PartSupplier = ?, `bin` = ?, onHand = ?, netOnHand = ?, ReOrderLevel = ?, MaxOnHand = ?, MaintainStock = ?, PartCategory = ?, overridematrix = ?, corecharge = ? WHERE shopid = ? AND partid = ?";
            if ($pstmt = $conn->prepare($stmt)) {

                $pstmt->bind_param("ssssdsdssddddsssdsi", $notes, $partNumber, $partDesc, $tax, $partPrice, $partCode, $partCost, $partSupplier, $bin, $onHand, $netOnHand, $reOrderLevel, $maxOnHand, $maintainStock, $partCategory, $overrideMatrix, $coreCharge, $shopid, $partId);
                if ($pstmt->execute()) {
                    $conn->commit();
                } else {
                    echo $conn->errno;
                }
                $auditmessage = "Updated inventory. Part Number: ".$partNumber.",".($oldPartNumber!=$partNumber?' Old Part Number: '.$oldPartNumber.', ':'')." PartID number: ".$partId;
                recordAudit("Inventory Updated", $auditmessage);
                $conn->commit();
                $pstmt->close();
            } else {
                echo "partsinv Prepare Failed: (" . $conn->errno . ") " . $conn->error;
            }
        }
        else
        {
            $stmt = "insert into partsinventory (shopid,PartNumber,PartDesc,PartPrice,PartCode,PartCost,PartSupplier,OnHand,Allocatted,NetOnHand,ReOrderLevel,MaxOnHand,MaintainStock,PartCategory,Bin,tax,overridematrix,corecharge) values (?,?,?,?,?,?,?,?,0,?,?,?,?,?,?,?,?,?)";

            if ($pstmt = $conn->prepare($stmt)) {

                $pstmt->bind_param("sssdsdsddddsssssd", $shopid, $partNumber, $partDesc, $partPrice, $partCode, $partCost, $partSupplier, $onHand, $netOnHand, $reOrderLevel, $maxOnHand, $maintainStock, $partCategory, $bin, $tax, $overrideMatrix,$coreCharge);
                if ($pstmt->execute()) {
                    $conn->commit();
                } else {
                    echo $conn->errno;
                }
                $auditmessage = "Updated Inventory. Part Number: ".$partNumber.",".($oldPartNumber!=$partNumber?' Old Part Number: '.$oldPartNumber.', ':'')." PartID number: ".$partId;
                recordAudit("Inventory Updated", $auditmessage);
                $pstmt->close();
            } else {
                echo "partsinv Prepare Failed: (" . $conn->errno . ") " . $conn->error;
            }
        }
    }

} else {
    $stmt = sprintf("select * from %s where shopid = ? and partnumber = ?", $preg);
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ss", $shopid, $partNumber);
        $query->execute();
        $results = $query->get_result();
        $query->store_result();
        $num_rows = $results->num_rows;
        if ($num_rows > 0) {
            $stmt = sprintf("update %s set PartDesc = ? , tax = ?, partnumber = ?, PartPrice = ? , PartCode = ?, PartCost = ?, PartSupplier = ?, bin = ?, PartCategory = ?, overridematrix = ?, corecharge = ?, onHand = ?, NetOnHand = ? where shopid = ? and PartNumber = ?", $preg);
            if ($pstmt = $conn->prepare($stmt)) {
                $pstmt->bind_param("sssdsdssssdddss", $partDesc, $tax, $partNumber, $partPrice, $partCode, $partCost, $partSupplier, $bin, $partCategory, $overrideMatrix, $coreCharge, $onHand, $netOnHand, $shopid, $partNumber);
                if ($pstmt->execute()) {
                    $conn->commit();
                } else {
                    echo $conn->errno;
                }
                $pstmt->close();
            } else {
                echo "update $preg prepared failed : ( " . $conn->error . ") " . $conn->error;
            }
        } else {
            $stmt = sprintf("insert into %s (partnumber,partdesc,tax,partprice,partcode,partcost,partsupplier,bin,partcategory,overridematrix,shopid,corecharge,onHand,NetOnHand) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?)", $preg);
            if ($pstmt = $conn->prepare($stmt)) {
                $pstmt->bind_param("sssdsdsssssddd", $partNumber, $partDesc, $tax, $partPrice, $partCode, $partCost, $partSupplier, $bin, $partCategory, $overrideMatrix, $shopid,$coreCharge, $onHand,$netOnHand);
                if ($pstmt->execute()) {
                    $conn->commit();
                } else {
                    echo $conn->errno;
                }
                $pstmt->close();
            } else {
                echo "INSERT $preg prepared failed : ( " . $conn->error . ") " . $conn->error;
            }
        }
        $query->close();
    } else {
        echo "$preg prepared failed : ( " . $conn->error . ") " . $conn->error;
    }

    if ($_POST['itype'] == "inv")
    {
        $whereField = 'partid';
        $whereValue = $partId;
        $whereType = 'i';
    }
    else
    {
       $whereField = 'partnumber';
       $whereValue = $partNumber;
       $whereType = 's'; 
    }

    $stmt = "DELETE from partsinventory where shopid = ? and {$whereField} = ?";
    if ($pstmt = $conn->prepare($stmt)) {
        $pstmt->bind_param("s{$whereType}", $shopid, $whereValue);
        if ($pstmt->execute()) {
            $conn->commit();
        } else {
            echo $conn->errno;
        }
        $pstmt->close();
        recordAudit("Inventory Deleted", "Deleted from parts inventory.  Part Number: ".$partNumber.", PartID number: ".$partId);
    } else {
        echo "pi prepare failed : ( " . $conn->error . ") " . $conn->error;
    }
}
//update the cannedparts using the shopid and partnumber
$stmt = "update cannedparts set partnumber = ?, partdescription = ?, supplier = ?, partcost = ? , partprice = ?, partcategory = ?, partcode = ?, overridematrix = ?, taxable=?, `bin`= ? where shopid = ? and partnumber = ?";

if ($pstmt = $conn->prepare($stmt)) {
    $pstmt->bind_param("sssddsssssss", $partNumber, $partDesc, $partSupplier, $partCost, $partPrice, $partCategory, $partCode, $overrideMatrix, $tax, $bin, $shopid, $oldPartNumber);
    if ($pstmt->execute()) {
        $conn->commit();
    } else {
        echo $conn->errno;
    }
    $pstmt->close();
} else {
    echo "update canned prepared failed : ( " . $conn->error . ") " . $conn->error;
}

/*
//update the recommended parts using the shopid and partnumber
$stmt = "update recommendparts set cost = ?, partprice = ?,net=ROUND(partprice-((discount/100)*partprice),2),lineTTLCost=Round(quantity * ? ,2),lineTTLPrice=Round(quantity * net ,2) where shopid = ? and partnumber = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("dddss", $partCost, $partPrice, $partCost, $shopid, $partNumber);
    if ($query->execute()) {
        $conn->commit();
    } else {
        echo $conn->errno;
    }
    $query->close();
} else {
    echo "Recommended parts prepared failed : ( " . $conn->error . ") " . $conn->error;
}
//return;
*/

if ($oldPartPrice != $partCost || $oldPartSp != $partPrice) {
    $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid= ? AND s.notification_type='119'";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->store_result();
        $num_rows = $query->num_rows;
        if($num_rows>0)
        {
        $query->bind_result($textcontent, $emailcontent, $popupcontent);
        $query->fetch();
        if ($oldPartPrice != $partCost) {
            $popupcontent = str_replace("*|PARTNUMBER|*", $partNumber, $popupcontent);
            $popupcontent = str_replace("*|TYPE|*", "Cost", $popupcontent);
            $emailcontent = str_replace("*|PARTNUMBER|*", $partNumber, $emailcontent);
            $emailcontent = str_replace("*|TYPE|*", "Cost", $emailcontent);
            $textcontent = str_replace("*|PARTNUMBER|*", $partNumber, $textcontent);
            $textcontent = str_replace("*|TYPE|*", "Cost", $textcontent);
        }
        if ($oldPartSp != $partPrice) {
            $popupcontent = str_replace("*|PARTNUMBER|*", $partNumber, $popupcontent);
            $popupcontent = str_replace("*|TYPE|*", "Price", $popupcontent);
            $emailcontent = str_replace("*|PARTNUMBER|*", $partNumber, $emailcontent);
            $emailcontent = str_replace("*|TYPE|*", "Price", $emailcontent);
            $textcontent = str_replace("*|PARTNUMBER|*", $partNumber, $textcontent);
            $textcontent = str_replace("*|TYPE|*", "Price", $textcontent);
        }
        $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'119',?,?,?)";
        if ($squery = $conn->prepare($stmt)) {
            $squery->bind_param("ssss", $shopid, $popupcontent, $textcontent, $emailcontent);
            if ($squery->execute()) {
                $conn->commit();
            } else {
                echo $conn->errno;
            }
            $squery->close();
        } else {
            echo "notification queue Prepare Failed: (" . $conn->errno . ") " . $conn->error;
        }
      }
        $query->close();
    } else {
        echo "notification_set prepared failed : ( " . $conn->error . ") " . $conn->error;
    }
}
if ($_POST['oldqty'] != $netOnHand) {
    $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid = ? AND s.notification_type='157'";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->store_result();
        $num_rows = $query->num_rows;
        if($num_rows>0)
        {
        $query->bind_result($textcontent, $emailcontent, $popupcontent);
        $query->fetch();

        $popupcontent = str_replace("*|PARTNUMBER|*", $partNumber, $popupcontent);
        $emailcontent = str_replace("*|PARTNUMBER|*", $partNumber, $emailcontent);
        $textcontent = str_replace("*|PARTNUMBER|*", $partNumber, $textcontent);
        $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'157',?,?,?)";
        if ($squery = $conn->prepare($stmt)) {
            $squery->bind_param("ssss", $shopid, $popupcontent, $textcontent, $emailcontent);
            if ($squery->execute()) {
                $conn->commit();
            } else {
                echo $conn->errno;
            }
        } else {
            echo "Notification q Prepare Failed: (" . $conn->errno . ") " . $conn->error;
        }
        }
        $query->close();
    } else {
        echo "notification Prepare Failed: (" . $conn->errno . ") " . $conn->error;
    }
}


mysqli_close($conn);

if ($_POST['sendto'] == "addxref.php") {
    header("Location:addxref.php?PartNumber=" . $partNumber);
} else {
    header("Location:inventory.php");
}
