﻿<?php
require_once CONN;

$sf = isset($_REQUEST['sf']) ? $_REQUEST["sf"] : "";
$st = isset($_REQUEST['st']) ? filter_var($_REQUEST['st'], FILTER_SANITIZE_STRING) : "";
$sb = isset($_REQUEST['sb']) ? filter_var($_REQUEST['sb'], FILTER_SANITIZE_STRING) : "";
$shopid = isset($_REQUEST['shopid']) ? filter_var($_REQUEST["shopid"], FILTER_SANITIZE_STRING) : "";
$sf_rep = isset($_REQUEST['sf']) ? filter_var(str_replace("-", "", str_replace(" ", "", $sf)), FILTER_SANITIZE_STRING) : "";

if(in_array($shopid, array('13445','22957'))) $shopid = '22865';

$stmt = sprintf("SELECT table_name FROM information_schema.tables WHERE table_schema = 'shopboss' AND table_name = 'partsregistry-%s' LIMIT 1", $shopid);
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $results = $query->get_result();
    $query->store_result();
    $num_rows = $results->num_rows;
    if ($num_rows > 0) {
        $preg = "`partsregistry-" . $shopid . "`";
    } else {
        $preg = "partsregistry";
    }
    $query->close();
} else {
    echo "info schema Prepare Failed: (" . $conn->errno . ") " . $conn->error;
}

if ($st == "non") {
    $tbleSearch = $preg;
} else {
    if ($st == "inv") {
        $tbleSearch = "partsinventory";
    }
}


$sf = trim($_REQUEST['sf'] ?? '');
$sf_rep = str_replace(['-', ' '], '', $sf);
$showall = ($_REQUEST['showall'] ?? '') === 'yes';

$limit = $showall ? 500 : 100;

$page = max(1, intval($_REQUEST['page'] ?? 1));
$offset = ($page - 1) * $limit;

$baseSql = "SELECT p.partid, p.partnumber, partdesc, x.xref, partprice, netonhand, partsupplier, overridematrix, bin
            FROM $tbleSearch p
            LEFT JOIN xref x ON p.shopid = x.shopid AND p.partnumber = x.partnumber
            WHERE p.shopid = ?";

$params = [$shopid];
$types = "s";

if (!empty($sf)) {
    $search = "%$sf%";
    $searchRep = "%$sf_rep%";

    $baseSql .= " AND (
        p.partnumber LIKE ? OR
        p.bin LIKE ? OR
        REPLACE(REPLACE(p.partnumber, '-', ''), ' ', '') LIKE ? OR
        p.partdesc LIKE ? OR
        REPLACE(REPLACE(x.xref, ' ', ''), '-', '') LIKE ? OR
        p.partsupplier LIKE ?
    )
    GROUP BY p.partid
    ORDER BY CASE
        WHEN p.partnumber = ? THEN 1
        WHEN p.partnumber LIKE ? THEN 2
        WHEN p.partnumber LIKE ? THEN 3
        WHEN p.partdesc LIKE ? THEN 4
        WHEN REPLACE(REPLACE(x.xref, ' ', ''), '-', '') LIKE ? THEN 5
        WHEN p.partsupplier LIKE ? THEN 6
        ELSE 7
    END";

    $params = array_merge($params, [
        $search, $search, $searchRep, $search, $searchRep, $search,
        $sf, "$sf%", "%$sf%", $search, $searchRep, $search
    ]);

    $types .= "ssssssssssss";
} else {
    $baseSql .= " ORDER BY p.partnumber";
}

if ($showall) {
    $baseSql .= " LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    $types .= "ii";
} else {
    $baseSql .= " LIMIT ?";
    $params[] = $limit;
    $types .= "i";
}
?>

<table class="sbdatatable w-100">
    <thead>
        <tr>
            <th>Part Number</th>
            <th>Description</th>
            <th>Bin</th>
            <th>X Reference</th>
            <th>Price</th>
            <th>On Hand</th>
            <th>Supplier</th>
            <th>Override Matrix</th>
            <th>Delete</th>
        </tr>
    </thead>
    <tbody>
        <?php
        if ($query = $conn->prepare($baseSql)) {
            $query->bind_param($types, ...$params);
            $query->execute();
            $results = $query->get_result();
            while ($rs = $results->fetch_assoc()) {
                $bcolor = (strtoupper($rs['overridematrix']) == "YES") ? "background-color:#FFD7D7" : "";
        ?>
                <tr id="<?php echo $rs['partid']; ?>">
                    <td onclick="location.href='editinventory.php?pn=<?php echo $rs['partid']; ?>'"><?php echo strtoupper($rs['partnumber']); ?></td>
                    <td onclick="location.href='editinventory.php?pn=<?php echo $rs['partid']; ?>'"><?php echo strtoupper($rs['partdesc']); ?></td>
                    <td onclick="location.href='editinventory.php?pn=<?php echo $rs['partid']; ?>'"><?php echo strtoupper($rs['bin']); ?></td>
                    <td onclick="location.href='editinventory.php?pn=<?php echo $rs['partid']; ?>'"><?php echo strtoupper($rs['xref']); ?></td>
                    <td onclick="location.href='editinventory.php?pn=<?php echo $rs['partid']; ?>'"><?php echo $rs['partprice']; ?></td>
                    <td onclick="location.href='editinventory.php?pn=<?php echo $rs['partid']; ?>'"><?php echo $rs['netonhand']; ?></td>
                    <td onclick="location.href='editinventory.php?pn=<?php echo $rs['partid']; ?>'"><?php echo strtoupper($rs['partsupplier']); ?></td>
                    <td style="<?php echo $bcolor; ?>" onclick="location.href='editinventory.php?pn=<?php echo $rs['partid']; ?>'">
                        <strong><?php echo strtoupper($rs['overridematrix']); ?></strong>
                    </td>

                    <?php if (!empty($_COOKIE['editinventory']) && $_COOKIE['editinventory'] == "yes") { ?>
                        <td class="text-center">
                            <a href="javascript:void(null)" class="text-primary" onclick="deletePart('<?php echo $rs['partid']; ?>','<?php echo urlencode($rs['partnumber']); ?>','<?php echo $sb; ?>','<?php echo $sf; ?>','<?php echo str_replace('`', '', $tbleSearch); ?>')">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    <?php } else { ?>
                        <td class="text-end">
                            <span class="text-primary" disabled="disabled">
                                <i class="fas fa-trash"></i>
                            </span>
                        </td>
                    <?php } ?>
                </tr>
        <?php
            }
            $query->close();
        } else {
            echo "tbl Prepare Failed: (" . $conn->errno . ") " . $conn->error;
        }
        ?>
    </tbody>
</table>

<script>
    $(document).ready(function() {
        $(".sbdatatable").dataTable({
            paging: false,
            searching: false,
            responsive: true,
            fixedHeader: true,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            retrieve: true,
            order: [],
            columnDefs: [
              { targets: [-1], orderable: false } // Exclude the last column from sorting
            ]
        });
    });
</script>
