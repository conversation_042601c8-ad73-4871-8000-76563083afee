<?php

require_once CONN;


$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);

if(in_array($shopid, array('13445','22957'))) $shopid = '22865';

if (isset($_REQUEST['PartNumber'])) {
    $partNumber = filter_var($_REQUEST['PartNumber'], FILTER_SANITIZE_STRING);
    $partNumber = str_replace("%", "", $partNumber);
    $partNumber = str_replace("&", " and ", $partNumber);
    $partNumber = strtoupper($partNumber);
} else {
    $partNumber = "";
}
if (isset($_REQUEST['PartDesc'])) {
    $partDescription = filter_var(str_replace("&", " and ", $_POST['PartDesc']), FILTER_SANITIZE_STRING);
} else {
    $partDescription = "";
}
if (isset($_REQUEST['PartSupplier'])) {
    $partSupplier = $_REQUEST['PartSupplier'];
    $partSupplier = strtoupper($partSupplier);
} else {
    $partSupplier = "";
}
if (isset($_REQUEST['bin'])) {
    $bin = filter_var($_REQUEST["bin"], FILTER_SANITIZE_STRING);
} else {
    $bin = "";
}
$tax = (isset($_REQUEST['tax'])) ? strtoupper($_REQUEST['tax']) : "";
$overrideMatrix = isset($_REQUEST['overridematrix']) ? filter_var($_REQUEST['overridematrix'], FILTER_SANITIZE_STRING) : "";
$partPrice = isset($_REQUEST['PartPrice']) ? filter_var($_REQUEST['PartPrice'], FILTER_SANITIZE_STRING) : "";
$onHand = isset($_REQUEST['OnHand']) ? filter_var($_REQUEST['OnHand'], FILTER_SANITIZE_STRING) : "";
$netOnHand = isset($_REQUEST['OnHand'])?filter_var($_REQUEST['OnHand'], FILTER_SANITIZE_STRING):"";
$partCode = isset($_REQUEST['PartCode']) ? filter_var(strtoupper($_REQUEST['PartCode']), FILTER_SANITIZE_STRING) : "";
$invoiceNumber = isset($_REQUEST['invoicenumber']) ? filter_var(strtoupper($_REQUEST['invoicenumber']), FILTER_SANITIZE_STRING) : "";
$partCost = isset($_REQUEST['PartCost']) ? filter_var($_REQUEST["PartCost"], FILTER_SANITIZE_STRING) : "";
$reOrderLevel = isset($_REQUEST['ReOrderLevel']) ? filter_var($_REQUEST['ReOrderLevel'], FILTER_SANITIZE_STRING) : "";
$maxOnHand = isset($_REQUEST['MaxOnHand']) ? filter_var($_REQUEST['MaxOnHand'], FILTER_SANITIZE_STRING) : "";
$maintainStock = isset($_REQUEST['MaintainStock']) ? filter_var(strtoupper($_REQUEST['MaintainStock']), FILTER_SANITIZE_STRING) : "";
$partCategory = isset($_REQUEST["PartCategory"]) ? filter_var(strtoupper($_REQUEST["PartCategory"]), FILTER_SANITIZE_STRING) : "";
$coreCharge = $_REQUEST['corecharge']??'0';
$otherfees = $_REQUEST['otherfees']??'';

if (strtolower($maintainStock) == "yes")
{
    $p_stmt = "insert into partsinventory (PartNumber, tax, overridematrix, PartDesc, PartPrice, netonhand, PartCode, invoicenumber, bin, PartCost, PartSupplier, OnHand, ReOrderLevel, MaxOnHand, MaintainStock, PartCategory, shopid, corecharge) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    if ($query = $conn->prepare($p_stmt)) {
        $query->bind_param("ssssddsssdsdddsssd", $partNumber, $tax, $overrideMatrix, $partDescription, $partPrice, $netOnHand, $partCode, $invoiceNumber, $bin, $partCost, $partSupplier, $onHand, $reOrderLevel, $maxOnHand, $maintainStock, $partCategory, $shopid,$coreCharge);
        if ($query->execute()) {
            $conn->commit();
            $query->close();
        } else {
            echo $conn->errno;
        }
    }
}

else
{
$stmt = sprintf("SELECT table_name FROM information_schema.tables WHERE table_schema = 'shopboss' AND table_name = 'partsregistry-%s' LIMIT 1", $shopid);
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $results = $query->get_result();
    $num_rows = $results->num_rows;
    if ($num_rows > 0) {
        $preg = "`partsregistry-" . $shopid . "`";
    } else {
        $preg = "partsregistry";
    }
    $query->close();
} else {
    echo "information s Prepare Failed: (" . $conn->errno . ") " . $conn->error;
}

$cstmt = sprintf("select PartNumber from %s where shopid = '%s' and PartNumber = '%s'", $preg, $shopid, $partNumber);
if ($query = $conn->prepare($cstmt)) {
    $query->execute();
    $results = $query->get_result();
    $query->store_result();
    $num_rows = $results->num_rows;
    if ($num_rows == 0) {
        $stmt = "insert into `" . $preg . "` (PartNumber, overridematrix, PartDesc, PartPrice, PartCode,bin, PartCost, PartSupplier, PartCategory, shopid, tax, corecharge) values (?,?,?,?,?,?,?,?,?,?,?,?)";
        if ($query1 = $conn->prepare($stmt)) {
            $query1->bind_param("sssdssdssssd", $partNumber, $overrideMatrix, $partDescription, $partPrice, $partCode, $bin, $partCost, $partSupplier, $partCategory, $shopid, $tax, $coreCharge);
            $query1->execute();
            if ($conn->commit()) {
            $conn->commit();
            } else {
                echo $conn->errno;
            }
            $query1->close();
        } else {
            echo "Failed to prepare query1 ";
        }
    } else {
        $stmt = "UPDATE `" . $preg . "` SET PartDesc=?, tax = ?, PartPrice = ? , PartCode = ?, PartCost = ?, overridematrix = ?, bin = ? , PartSupplier = ?, PartCategory = ?, corecharge = ? WHERE shopid = ? AND PartNumber = ?";
        if ($query2 = $conn->prepare($stmt)) {
            $query2->bind_param("ssdsdssssdss", $partDescription, $tax, $partPrice, $partCode, $partCost, $overrideMatrix, $bin, $partSupplier, $partCategory, $coreCharge, $shopid, $partNumber);
            if ($query2->execute()) {
            $conn->commit();
            } else {
                echo $conn->errno;
            }
            $query2->close();
        } else {
            echo "Failed to prepare Query2";
        }
    }
} else {
    echo "partnumber Prepare Failed: (" . $conn->errno . ") " . $conn->error;
}

}


if(!empty($otherfees))
{
    $arr = explode(',', $otherfees);
    if(!empty($arr))
    {
     foreach($arr as $fid)
     {
        if(!empty($fid))
        {
            $stmt = "select feename,feeamount,dollarpercent,taxable,qtyflag from otherfees where shopid = ? and id = ?";
            if ($query = $conn->prepare($stmt)){
                $query->bind_param("si",$shopid,$fid);
                $query->execute();
                $query->bind_result($feename,$feeamount,$dollarpercent,$taxable,$qtyflag);
                $query->fetch();
                $query->close();
            }

            $stmt = "insert into partsinventoryfees(additionalfeename,addfeetaxable,addfeeamt,addfeepercentordollar,qtyflag,shopid,partnumber) values (?,?,?,?,?,?,?)";

            if ($query = $conn->prepare($stmt)){
                $query->bind_param("ssdssss",$feename,$taxable,$feeamount,$dollarpercent,$qtyflag,$shopid,$partNumber);
                $query->execute();
                $conn->commit();
                $query->close();
            }

        }
     }
    }
}

mysqli_close($conn);

header("Location:inventory.php");
