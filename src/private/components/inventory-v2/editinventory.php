﻿<?php

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
// Global Variables
$date = new DateTime('now');
$component = "inventory-v2";
// Page Variables
$title = 'Inventory';
$subtitle = "";

include getHeadGlobal('');
include getHeadComponent($component);
include getRulesGlobal($component);
echo "<body>";
include getHeaderGlobal($component);
include getMenuGlobal($component);

if(in_array($shopid, array('13445','22957'))) $shopid = '22865';

$editInventory = isset($_COOKIE['editinventory']) ? $_COOKIE['editinventory'] : "";
$editInventory = filter_var($editInventory, FILTER_SANITIZE_STRING);
$pn = isset($_GET['pn']) ? $_GET["pn"] : "";
$partNumber = filter_var($pn, FILTER_SANITIZE_STRING);

if (!empty($shopid) && !empty($partNumber)) {

    if ($shopid == "2703" || $shopid == "1238") {
        $preg = "`partsregistry-" . $shopid . "`";
    } else {
        $preg = "`partsregistry`";
    }
    $stmt = "SELECT usepartsmatrix, inventoryappreciation from company where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $res = $query->get_result();
        $row = $res->fetch_assoc();
        if (strtolower($row['usepartsmatrix']) == "no") {
            $moverridematrix = "yes";
        } else {
            if (strtolower($row['usepartsmatrix']) == "yes") {
                $moverridematrix = "no";
            }
        }
        $inventoryappreciation = strtolower($row["inventoryappreciation"]);
        $query->close();
    } else {
        echo "useoarts mtx Prepare Failed: (" . $conn->errno . ") " . $conn->error;
    }

    if (!empty($editInventory) && $editInventory == "yes") {
        $piQuery = "SELECT * FROM partsinventory WHERE shopid = ? and partid = ?";
        if ($ismt = $conn->prepare($piQuery)) {
            $ismt->bind_param("ss", $shopid, $partNumber);
            $ismt->execute();
            $pi_res = $ismt->get_result();
            $ismt->store_result();
            if ($pi_res->num_rows > 0) {
                $pi_row = $pi_res->fetch_assoc();
                $overridematrix = strtolower($pi_row['overridematrix']);
                $itype = "inv";
            } else {
                //No records found
                $pregtmt = "select * from " . $preg . " where shopid = ? and partid = ?";
                if ($prquery = $conn->prepare($pregtmt)) {
                    $prquery->bind_param("ss", $shopid, $partNumber);
                    $prquery->execute();
                    $presults = $prquery->get_result();
                    $pi_row = $presults->fetch_assoc();
                    $itype = "non";
                }
            }
            $ismt->close();
        } else {
            echo "partsinv Prepare Failed: (" . $conn->errno . ") " . $conn->error;
        }
        $bin = "";
        if (!empty($pi_row['Bin'])) {
            $bin = $pi_row['Bin'];
        } else {
            if (!empty($pi_row['bin'])) {
                $bin = $pi_row['bin'];
            }
        }
        $script = "";
        $stmt = "SELECT * FROM category WHERE shopid = ? ORDER BY Category, Start";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $result = $query->get_result();
            $script .= "\n <script type='text/javascript'>\n";
            $script .= "var clist = new Array();\n";
            $count = 0;
            while ($res = $result->fetch_assoc()) {
                $script .= "clist[" . $count . "] = {Category:'" . strtolower($res['Category']) . "', Start:" . $res['Start'] . ", End:" . $res['End'] . ", Factor:" . $res["Factor"] . "}\n";
                $count++;
            }
            $script .= "</script>";
            $query->close();
        } else {
            echo "category Prepare Failed: (" . $conn->errno . ") " . $conn->error;
        }
?>

        <style>
            main {
                margin-top: 143px;
                min-height: 100vh;
            }
        </style>

        <?php echo $script; ?>

        <!-- Main Container -->
        <main id="main-container">
            <div class="report">
                <div class="col-12">
                    <div class="row">
                        <div class="col-md-12 col-sm-12">
                            <div class="title col breadcrumb d-flex align-items-center mb-0">
                                <a href="<?= COMPONENTS_PRIVATE ?>/v2/inventory/inventory.php" class="text-secondary">Inventory</a>
                                <span class="text-secondary ps-3 pe-3">/</span>
                                <h2 class="">Edit Part</h2>
                            </div>
                            <hr />
                        </div>
                    </div>
                </div>
            </div>

            <form method="post" name="theform" action="editinventorynow.php">
                <input type="hidden" name="itype" value="<?php echo $itype; ?>">
                <div class="row">
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="row">
                                    <div class="col-md-12 mb-4">
                                        <div class="form-outline">
                                            <input class="form-control" type="text" name="PartNumber" id="PartNumber" size="22" style="text-transform:uppercase" value="<?php echo $pi_row['PartNumber'] ?>" tabindex="1">
                                            <label class="form-label" for="PartNumber">Part Number</label>
                                        </div>
                                    </div>

                                    <div class="col-md-12 mb-4">
                                        <div class="form-outline">
                                            <input class="form-control" type="text" name="PartDesc" size="56" style="text-transform:uppercase" value="<?php echo str_replace("\"", "&quot;", $pi_row["PartDesc"]); ?>" tabindex="2">
                                            <label class="form-label" for="PartDesc">Part Description</label>
                                        </div>
                                    </div>

                                    <div class="col-md-12 mb-4">
                                        <div class="form-row">
                                            <select class="select" size="1" name="PartCategory" tabindex="3">
                                                <?php
                                                $cstmt = "select distinct category from category where shopid = ? order by displayorder, category, Start";
                                                // echo $cstmt;
                                                if ($query = $conn->prepare($cstmt)) {
                                                    $query->bind_param('s', $shopid);
                                                    $query->execute();
                                                    $results = $query->get_result();
                                                    if ($results->num_rows > 0) {
                                                        while ($crs = $results->fetch_assoc()) {
                                                            $s = "";
                                                            if (strtolower($pi_row['PartCategory']) == strtolower($crs["category"])) {
                                                                $s = "selected";
                                                            }
                                                            echo "<option $s value='" . $crs["category"] . "'>" . $crs["category"] . "</option>";
                                                        }
                                                    } else {
                                                        echo "<option value='General Part'>General Part</option>";
                                                    }

                                                    $query->close();
                                                } else {
                                                    echo "category Prepare Failed: (" . $conn->errno . ") " . $conn->error;
                                                }
                                                ?>
                                            </select>
                                            <label class="form-label select-label" for="PartCategory">Category</label>
                                        </div>
                                    </div>

                                    <div class="col-md-12 mb-4">
                                        <div class="form-outline">
                                            <input class="form-control" onblur="getFactor();<?php echo ($inventoryappreciation == 'yes') ? 'checkCost(this.value);' : ''; ?>" name="PartCost" size="11" style="text-transform:uppercase" tabindex="4" value="<?php echo $pi_row['PartCost']; ?>">
                                            <label class="form-label" for="PartCost">Cost</label>
                                        </div>
                                        <input id="oldpartprice" name="oldpartprice" type="hidden" value="<?php echo $pi_row['PartCost']; ?>">
                                    </div>

                                    <div class="col-md-12 mb-4">
                                        <div class="form-outline">
                                            <input class="form-control" onblur="getFactor()" type="text" name="PartPrice" size="11" style="text-transform:uppercase" value="<?php echo $pi_row["PartPrice"]; ?>" tabindex="5">
                                            <label class="form-label" for="PartPrice">Price</label>
                                        </div>
                                        <input name="oldpartsp" type="hidden" value="<?php echo $pi_row["PartPrice"]; ?>">
                                    </div>

                                    <div class="col-md-12 mb-4">
                                        <div class="form-row">
                                            <select class="select" size="1" name="PartCode" tabindex="6">
                                                <?php
                                                $cstmt = "select * from codes where shopid = ? order by displayorder,Codes";
                                                if ($query = $conn->prepare($cstmt)) {
                                                    $query->bind_param("s", $shopid);
                                                    $query->execute();
                                                    $results = $query->get_result();
                                                    if ($results->num_rows > 0) {
                                                        while ($crs = $results->fetch_assoc()) {
                                                            $s = "";
                                                            if (strtolower($pi_row['PartCode']) == strtolower($crs["Codes"])) {
                                                                $s = "selected";
                                                            }
                                                            echo "<option $s value='" . $crs["Codes"] . "'>" . $crs["Codes"] . "</option>";
                                                        }
                                                    } else {
                                                        echo "<option value='No Codes'>No Codes</option>";
                                                    }
                                                    $query->close();
                                                } else {
                                                    echo "codes Prepare Failed: (" . $conn->errno . ") " . $conn->error;
                                                }
                                                ?>
                                            </select>
                                            <label class="form-label select-label" for="PartCode">Code</label>
                                        </div>
                                    </div>

                                    <div class="col-md-12 mb-4">
                                        <div class="form-row">
                                            <select class="select" size="1" name="PartSupplier" tabindex="7">
                                                <option value="">Select</option>
                                                <?php
                                                $crs = null;
                                                $cstmt = "select SupplierName from supplier where shopid = ? and length(SupplierName) > 1 and Active = 'YES' order by displayorder, SupplierName";
                                                if ($query = $conn->prepare($cstmt)) {
                                                    $query->bind_param("s", $shopid);
                                                    $query->execute();
                                                    $results = $query->get_result();
                                                    if ($results->num_rows > 0) {
                                                        while ($crs = $results->fetch_assoc()) {
                                                            $s = "";
                                                            if (strtolower($crs['SupplierName']) == strtolower($pi_row['PartSupplier'])) {
                                                                $s = "selected";
                                                            }
                                                            echo "<option $s value=\"" . $crs["SupplierName"] . "\">" . $crs["SupplierName"] . "</option>";
                                                        }
                                                    } else {
                                                        echo "<option value='None'>No Suppliers Entered</option>";
                                                    }
                                                } else {
                                                    echo "supplier Prepare Failed: (" . $conn->errno . ") " . $conn->error;
                                                }
                                                ?>
                                            </select>
                                            <label class="form-label select-label" for="PartSupplier">Supplier</label>
                                        </div>
                                    </div>

                                    <div class="col-md-12 mb-4">
                                        <div class="form-outline">
                                            <input class="form-control" type="text" name="bin" size="11" style="text-transform:uppercase" value="<?php echo $bin; ?>" tabindex="10">
                                            <label class="form-label" for="bin">Bin/Location</label>
                                        </div>
                                    </div>

                                    <div class="col-md-12 mb-4">
                                        <div class="form-outline">
                                            <?php
                                            $stmt = "select roid from repairorders where shopid = ? and status != 'CLOSED' and ROType != 'No Approval'";

                                            if ($query = $conn->prepare($stmt)) {
                                                $query->bind_param("s", $shopid);
                                                $query->execute();
                                                $query->store_result();
                                                $query->bind_result($roid);
                                                $rolist = array();
                                                while ($query->fetch()) {
                                                    $rolist[] = $roid;
                                                }
                                                $query->close();
                                            }

                                            $aq = floatval(0);

                                            if (!empty($rolist)) {
                                                $rolist = implode(",", $rolist);
                                                $extrastr = $itype == "non" ? " and allocated='NON'" : " and allocated!='NON'";

                                                $stmt = sprintf("SELECT COALESCE(SUM(quantity),0) q FROM parts WHERE shopid = '%s' AND partnumber = '%s' AND roid in (%s) {$extrastr}", $shopid, $pi_row['PartNumber'], $rolist);

                                                if ($query = $conn->prepare($stmt)) {
                                                    $query->execute();
                                                    $query->bind_result($aq);
                                                    $query->fetch();
                                                    $aq = floatval($aq);
                                                    $query->close();
                                                } else {
                                                    echo "parts Prepare Failed: (" . $conn->errno . ") " . $conn->error;
                                                }
                                            }
                                            ?>

                                            <input readonly disabled class="form-control" type="text" name="allocatedOpenRO" value="<?php echo $aq; ?>">
                                            <label class="form-label" for="allocatedOpenRO">Allocated to Open RO's</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="col-md-12 mb-4">
                                    <div class="form-outline">
                                        <input class="form-control" type="text" name="netOnHand" id="netOnHand" size="8" style="text-transform:uppercase" value="<?php echo $pi_row['NetOnHand']; ?>" tabindex="11">
                                        <label class="form-label" for="netOnHand">On Hand</label>
                                    </div>
                                    <input name="oldqty" type="hidden" value="<?php echo $pi_row['NetOnHand']; ?>">
                                </div>

                                <div class="col-md-12 mb-4">
                                    <div class="form-outline">
                                        <input class="form-control" type="text" name="ReOrderLevel" size="8" style="text-transform:uppercase" value="<?php echo $pi_row['ReOrderLevel']; ?>" tabindex="12">
                                        <label class="form-label" for="ReOrderLevel">Re-Order Level</label>
                                    </div>
                                </div>

                                <div class="col-md-12 mb-4">
                                    <div class="form-outline">
                                        <input class="form-control" type="text" name="MaxOnHand" size="8" style="text-transform:uppercase" value="<?php echo $pi_row['MaxOnHand']; ?>" tabindex="13">
                                        <label class="form-label" for="MaxOnHand">Max On Hand</label>
                                    </div>
                                </div>

                                <div class="col-md-12 mb-4">
                                    <div class="form-outline">
                                        <input class="form-control" type="text" name="corecharge" size="8" style="text-transform:uppercase" value="<?php echo $pi_row['corecharge']; ?>" tabindex="14">
                                        <label class="form-label" for="corecharge">Core Charge</label>
                                    </div>
                                </div>

                                <div class="col-md-12 mb-4">
                                    <div class="form-row">
                                        <select class="select" size="1" name="MaintainStock" tabindex="15">
                                            <?php
                                            $ysel = $nsel = "";

                                            if (strtolower($pi_row["MaintainStock"]) == "yes") {
                                                $ysel = " selected";
                                            } elseif (strtolower($pi_row["MaintainStock"]) == "no") {
                                                $nsel = " selected";
                                            }
                                            ?>
                                            <option <?= $ysel; ?> value="Yes">Yes</option>
                                            <option <?= $nsel; ?> value="No">No</option>
                                        </select>
                                        <label class="form-label select-label" for="MaintainStock">Mantain Stock</label>
                                    </div>
                                </div>

                                <div class="col-md-12 mb-4">
                                    <div class="form-row">
                                        <select class="select" size="1" name="tax" tabindex="15">
                                            <?php
                                            $ysel = $nsel = "";

                                            if (strtolower($pi_row["tax"]) == "yes") {
                                                $ysel = " selected";
                                            } elseif (strtolower($pi_row["tax"]) == "no") {
                                                $nsel = " selected";
                                            }
                                            ?>
                                            <option <?= $ysel; ?> value="Yes">Yes</option>
                                            <option <?= $nsel; ?> value="No">No</option>
                                        </select>
                                        <label class="form-label select-label" for="tax">Taxable</label>
                                    </div>
                                </div>

                                <div class="col-md-12 mb-4">
                                    <div class="form-row">
                                        <select class="select" size="1" name="overridematrix" tabindex="16">
                                            <?php
                                            $ysel = $nsel = "";

                                            if (strtolower($pi_row["overridematrix"]) == "yes") {
                                                $ysel = " selected";
                                            } elseif (strtolower($pi_row["overridematrix"]) == "no") {
                                                $nsel = " selected";
                                            }
                                            ?>
                                            <option <?= $ysel; ?> value="Yes">Yes</option>
                                            <option <?= $nsel; ?> value="No">No</option>
                                        </select>
                                        <label class="form-label select-label" for="overridematrix">Override Matrix</label>
                                    </div>
                                </div>

                                <?php
                                if ($itype == "inv") {
                                ?>
                                    <div class="col-md-12 mb-4">
                                        <div class="form-outline">
                                            <textarea class="form-control" type="text" name="notes" tabindex="16"><?php echo $pi_row['notes']; ?></textarea>
                                            <label class="form-label" for="notes">Notes</label>
                                        </div>
                                    </div>
                                <?php } ?>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-4 text-center">
                                <button class="btn btn-primary" name="inv" onclick="checkForm()">Save</button>
                            </div>

                            <div class="col-md-12">
                                <?php
                                $count = 0;
                                /*
                                 * Assuming the function is working on a date 1095 days ago
                                 */
                                $daysAgoTs = strtotime("-1095 days");
                                $startDate = date('Y-m-d', $daysAgoTs);
                                $endDate = date("Y-m-d");
                                $stmt = "select coalesce(sum(qty),0) as c from ps left join psdetail on ps.psid = psdetail.psid where ps.shopid = ? and psdetail.shopid = ? and psdetail.pnumber = ? and ps.statusdate between ? and ?";

                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("sssss", $shopid, $shopid, $pi_row['PartNumber'], $startDate, $endDate);
                                    $query->execute();
                                    $query->store_result();
                                    $query->bind_result($pcntr);
                                    $query->fetch();
                                    $query->close();
                                } else {
                                    echo "ps Prepare Failed: (" . $conn->errno . ") " . $conn->error;
                                }

                                if ($itype == "non") {
                                    $extrastr = " and allocated!='INV'";
                                } else {
                                    $extrastr = " and allocated!='NON'";
                                }

                                $stmt = "select distinct r.roid,p.quantity,p.`date` from parts p,repairorders r where p.shopid=r.shopid and p.roid=r.roid and p.quantity > 0 and p.deleted = 'no' and p.`date` between ? and ? and p.shopid = ? and p.partnumber = ? and r.rotype != 'no approval' {$extrastr} order by p.`date` desc";
                                $partsDetailsArr = array();

                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("ssss", $startDate, $endDate, $shopid, $pi_row['PartNumber']);
                                    $query->execute();
                                    $result = $query->get_result();
                                    $num_rows = $result->num_rows;
                                    if ($num_rows > 0) {
                                        while ($trs = $result->fetch_assoc()) {
                                            $count += doubleval($trs['quantity']);
                                            if (isset($partsDetailsArr[$trs['roid']])) {
                                                $old_qty = $partsDetailsArr[$trs['roid']]['quantity'];
                                                $partsDetailsArr[$trs['roid']]['quantity'] = $old_qty + doubleval($trs['quantity']);
                                                $partsDetailsArr[$trs['roid']]['date'] =  date('m/d/Y', strtotime($trs['date']));
                                            } else {
                                                $partsDetailsArr[$trs['roid']] = array(
                                                    'roid' => $trs['roid'],
                                                    'quantity' => doubleval($trs['quantity']),
                                                    'date' => date('m/d/Y', strtotime($trs['date']))
                                                );
                                            }
                                        }
                                        $result->data_seek(0);
                                ?>
                                        <h4>Repair Orders with this Part Number in the last 3 years:</h4>

                                        <p>Total Sold: <?= round($count + $pcntr, 2) ?></p>
                                        <?php
                                        while ($trs = $result->fetch_assoc()) {
                                            echo "<a href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?roid=" . $trs["roid"] . "'>RO#" . $trs["roid"] . "</a>&nbsp; ";
                                        }

                                        $query->close();
                                    }
                                } else {
                                    die("parts Prepare Failed: (" . $conn->errno . ") " . $conn->error);
                                }

                                $count = 0;
                                $stmt = "select distinct psid,qty from psdetail where qty > 0 and `ts` between ? and ? and shopid = ? and pnumber = ? order by psid asc";
                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("ssss", $startDate, $endDate, $shopid, $pi_row['PartNumber']);
                                    $query->execute();
                                    $result = $query->get_result();
                                    $num_rows = $result->num_rows;
                                    if ($num_rows > 0) {
                                        while ($trs = $result->fetch_assoc()) {
                                            $count += doubleval($trs['qty']);
                                        }
                                        $result->data_seek(0);
                                        ?>
                                        <h4>Part Sales with this Part Number in the last year:</h4>
                                        <p>Total Sold: <?= round($pcntr, 2) ?></p>

                                <?php
                                        while ($trs = $result->fetch_assoc()) {
                                            echo "<a href='../partsale/partsale.php?psid=" . $trs["psid"] . "'>PS#" . $trs["psid"] . "</a>&nbsp; ";
                                        }

                                        $query->close();
                                    }
                                } else {
                                    echo "psdetail Prepare Failed: (" . $conn->errno . ") " . $conn->error;
                                }
                                ?>
                            </div>

                            <?php
                            if ($shopid == '8395' && strtolower($itype) == 'non') {
                                $pdstmt = "select distinct roid,`date`, COUNT(quantity) as quantity from parts where quantity > 0 and deleted = 'no' and `date` between ? and ? and shopid = ? and partnumber = ? and allocated = 'NON' GROUP BY roid order by roid asc";
                                if (!empty($partsDetailsArr)) {
                            ?>

                                    <div class="row mb-2">
                                        <h4>Inventory Sold Details</h4>
                                    </div>

                                    <div class="col-md-12">
                                        <table class="sbdatatable w-100">
                                            <thead>
                                                <tr>
                                                    <th>RO #</th>
                                                    <th>Date</th>
                                                    <th class="text-end">Quantity</th>
                                                </tr>
                                            </thead>
                                            <?php
                                            $ttlqty = 0;
                                            foreach ($partsDetailsArr as $pdrs) {
                                            ?>
                                                <tr>
                                                    <td><?= "<a target='_blank' href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?roid=" . $pdrs["roid"] . "'>" . $pdrs['roid'] . "</a>" ?></td>
                                                    <td><?= $pdrs['date'] ?></td>
                                                    <td class="text-end"><?= round($pdrs['quantity'], 2) ?></td>
                                                </tr>
                                            <?php
                                                $ttlqty += $pdrs['quantity'];
                                            }
                                            if ($ttlqty > 0) {
                                            ?>
                                                <tr>
                                                    <td><strong>Totals</strong></td>
                                                    <td></td>
                                                    <td class="text-end"><strong><?= number_format($ttlqty, 2) ?></strong></td>
                                                </tr>
                                            <?php
                                            }
                                            ?>
                                        </table>
                                    </div>
                            <?php
                                }
                            }
                            ?>
                        </div>

                        <input type=hidden name=sendto value='none'>
                        <input name="partid" type="hidden" value='<?php echo $pi_row['partid']; ?>'>
                        <input name="oldpartnumber" type="hidden" value='<?php echo $pi_row['PartNumber']; ?>'>
                    </div>

                    <div class="col-md-4">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="row mb-2">
                                    <h4>X Reference</h4>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <span style="float:right" class="btn btn-secondary mb-2" onclick="addXRef()">Add X Reference</span>

                                        <table class="sbdatatable w-100">
                                            <thead>
                                                <tr>
                                                    <th width="50%">Part Number</th>
                                                    <th>XReference</th>
                                                    <th>Delete</th>
                                                </tr>
                                            </thead>
                                            <?php
                                            $xstmt = "SELECT * FROM xref WHERE shopid = ? AND PartNumber = ?";
                                            if ($query = $conn->prepare($xstmt)) {
                                                $query->bind_param("ss", $shopid, $pi_row['PartNumber']);
                                                $query->execute();
                                                $result = $query->get_result();
                                                $query->store_result();
                                                $num_rows = $result->num_rows;
                                                if ($num_rows > 0) {
                                                    while ($xrs = $result->fetch_assoc()) {
                                            ?>
                                                        <tr>
                                                            <td><?php echo $xrs['PartNumber']; ?></td>
                                                            <td><?php echo $xrs['XREF']; ?></td>
                                                            <td class="text-end text-primary"><i onclick="deleteXREF('<?php echo $xrs['id']; ?>')" class="fas fa-trash"></i></td>
                                                        </tr>
                                            <?php
                                                    }
                                                }
                                                $query->close();
                                            } else {
                                                echo "xref Prepare Failed: (" . $conn->errno . ") " . $conn->error;
                                            }
                                            ?>
                                        </table>
                                    </div>
                                </div>

                                <div class="row mb-2">
                                    <h4>Inventory Auto Fees</h4>
                                </div>

                                <div class="row">
                                    <p>
                                        Below you can add fees that are automatically
                                        added
                                        when
                                        this part
                                        is added to an RO. For example, you can automatically add a Tire Fee or a
                                        Disposal
                                        fee.
                                    </p>
                                </div>

                                <?php
                                $stmt = "SELECT * FROM otherfees WHERE shopid = ?";
                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("s", $shopid);
                                    $query->execute();
                                    $query->store_result();
                                    $num_rows = $query->num_rows;
                                    if ($num_rows > 0) {
                                ?>
                                        <span style="float:right" class="btn btn-secondary mb-2" onclick="$('#feemodal').modal('show')">Add Fee</span>

                                        <table class="sbdatatable w-100">
                                            <thead>
                                                <tr>
                                                    <th>Fee Name</th>
                                                    <th>Fee Amount</th>
                                                    <th>$ or %</th>
                                                    <th>Taxable</th>
                                                    <th>Qty Flag</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody id="feebody">
                                                <?php
                                                $PN = $pi_row['PartNumber'];
                                                $pif_stmt = sprintf("SELECT * FROM partsinventoryfees WHERE shopid = ? AND partnumber = ?");

                                                if ($pif_query = $conn->prepare($pif_stmt)) {
                                                    $pif_query->bind_param("ss", $shopid, $PN);
                                                    $pif_query->execute();
                                                    $pif_result = $pif_query->get_result();
                                                    $pif_query->store_result();

                                                    while ($trs = $pif_result->fetch_assoc()) {
                                                ?>
                                                        <tr>
                                                            <td><?php echo strtoupper($trs['additionalfeename']); ?></td>
                                                            <td><?php echo $trs["addfeeamt"]; ?></td>
                                                            <td><?php echo strtoupper($trs["addfeepercentordollar"]); ?></td>
                                                            <td><?php echo strtoupper($trs["addfeetaxable"]); ?></td>
                                                            <td><?php echo strtoupper($trs["qtyflag"]); ?></td>
                                                            <td class="text-end text-primary"><i class="fas fa-trash" onclick="deleteInvFee('<?php echo $trs["id"]; ?>')"></i></td>
                                                        </tr>
                                                <?php
                                                    }
                                                    $pif_query->close();
                                                } else {
                                                    echo "Prepaired statement failed";
                                                }
                                                ?>
                                            </tbody>
                                        </table>
                                    <?php
                                    } else {
                                    ?>
                                        You have no Inventory Fees. Please <a href="inventoryfees.php">click here</a> to create them
                                <?php
                                    }
                                    $query->close();
                                } else {
                                    echo "otherfee Prepare Failed: (" . $conn->errno . ") " . $conn->error;
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </main>
        <!-- END Main Container -->

        <div id="feemodal" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
            <input id="customerid" type="hidden">

            <div class="modal-dialog modal-md">
                <div class="modal-content p-4">
                    <div class="modal-header ps-1 pe-1">
                        <h5 class="modal-title">Inventory Fees</h5>
                        <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                    </div>

                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <?php
                                $stmt = "SELECT * FROM otherfees WHERE shopid = ?";
                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("s", $shopid);
                                    $query->execute();
                                    $result = $query->get_result();
                                    while ($xrs = $result->fetch_assoc()) {
                                        echo "<span style='width:70%;margin-left:15%;margin-bottom: 2%;' class='btn btn-secondary'
                                      onclick='addFee(" . $xrs["id"] . ")'>Add " . strtoupper($xrs["feename"]) . "</span><br>";
                                    }
                                    $query->close();
                                } else {
                                    echo "otherfee Prepare Failed: (" . $conn->errno . ") " . $conn->error;
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php
        include getScriptsGlobal('');
        include getScriptsComponent($component);
        include getFooterComponent($component);
        ?>

        <script>
            function deleteInvFee(id) {
                sbconfirm(
                    'Are you sure?',
                    'Are you sure you want to delete this Fee?',
                    function() {

                        showLoader()
                        ds = "id=" + id + "&t=deleteinvfee&shopid=<?php echo $shopid; ?>&pn="
                        $.ajax({
                            data: ds,
                            url: "crudinventoryfees.php",
                            type: "post",
                            success: function(r) {
                                location.reload()
                            },
                            error: function(xhr, ajaxOptions, thrownError) {
                            }
                        })
                    }
                );
            }

            function newFee() {
                $('#feemodal').modal('show')
            }

            function addFee(id) {
                pn = $('#PartNumber').val();

                ds = "t=addfee&pn=" + pn + "&shopid=<?php echo $shopid; ?>&partnumber=" + pn + "&id=" + id
                showLoader()
                $.ajax({
                    data: ds,
                    url: "crudinventoryfees.php",
                    type: "post",
                    success: function(r) {
                        location.reload()
                    },
                    error: function(xhr, ajaxOptions, thrownError) {
                    }
                })
            }


            function addXRef() {
                eModal.iframe({
                    title: 'Add X-Reference',
                    url: "addxref.php?p=<?php echo urlencode($pi_row["PartNumber"]); ?>",
                    size: eModal.size.lg,
                });
            }

            function getFactor() {
                srch = document.theform.PartCategory.value.toLowerCase();
                amt = document.theform.PartCost.value;
                overridematrix = document.theform.overridematrix.value;
                overridematrix = overridematrix.toLowerCase();
                var i
                for (i = 0; i < clist.length; i++) {
                    if (clist[i].Category == srch && clist[i].Start <= amt && clist[i].End >= amt) {
                        if (overridematrix == "no") {
                            document.theform.PartPrice.value = Math.round((amt * clist[i].Factor) * 100) / 100;
                            break
                        }
                    }
                }
            }

            function checkForm() {
                d = document.theform;
                if (d.PartNumber.value.length == 0) {
                    sbalert("Part Number is required");
                    return false;
                }
                if (d.PartDesc.value.length == 0) {
                    sbalert("Part Description is required");
                    return false;
                }
                if (d.PartCost.value.length == 0) {
                    sbalert("Cost is required");
                    return false;
                }
                if (d.netOnHand.value.length == 0) {
                    sbalert("On Hand is required")
                    return false;
                }
                if (d.PartPrice.value.length == 0) {
                    sbalert("Price is required")
                    return false;
                }
                if (d.ReOrderLevel.value.length == 0) {
                    sbalert("Re-Order Level Price is required")
                    return false;
                }
                if (d.MaxOnHand.value.length == 0) {
                    sbalert("Max On Hand Price is required")
                    return false;
                }

                document.theform.submit()
            }

            function deleteXREF(id) {
                sbconfirm(
                    'Are you sure?',
                    'Are you sure you want to delete this X-Reference?',
                    function() {
                        location.href = 'deletexref.php?partnumber=<?php echo $partNumber; ?>&id=' + id
                    }
                );
            }

            <?php
            if ($inventoryappreciation == "yes") {
            ?>

                function checkCost(currentcost) {
                    oldcost = document.theform.oldpartprice.value;
                    if (currentcost != oldcost && !isNaN(currentcost)) {
                        document.getElementById("inventoryappreciation").style.display = "block";
                        document.getElementById("hider").style.display = "block";
                        document.getElementById("inventoryappreciation").focus();
                        document.getElementById("pricedifference").value = parseFloat(Math.round((currentcost - oldcost) * 100) / 100).toFixed(2);
                        calcAppreciation()
                    }
                }

                function cancelAppreciation() {
                    document.getElementById("inventoryappreciation").style.display = "none";
                    document.getElementById("hider").style.display = "none";
                }

                function calcAppreciation() {
                    pricediff = document.getElementById("pricedifference").value;
                    qty = document.getElementById("appreciationquantity").value;
                    app = pricediff * qty;
                    document.getElementById("appreciation").value = app;
                }

                function loadXMLDoc() {
                    amt = document.getElementById("appreciation").value;
                    if (amt != 0) {
                        var xmlhttp;
                        if (window.XMLHttpRequest) {
                            // code for IE7+, Firefox, Chrome, Opera, Safari
                            xmlhttp = new XMLHttpRequest();
                        } else {
                            // code for IE6, IE5
                            xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
                        }
                        xmlhttp.onreadystatechange = function() {
                            if (xmlhttp.readyState == 4 && xmlhttp.status == 200) {
                                rt = xmlhttp.responseText
                                if (rt == "success") {
                                    sbalert("Inventory appreciation saved")
                                    document.getElementById("inventoryappreciation").style.display = "none"
                                    document.getElementById("hider").style.display = "none"
                                } else {
                                    sbalert(rt)
                                }
                            }
                        };
                        url = "addinventoryappreciation.php?partnumber=<?php echo urlencode($pi_row["PartNumber"]); ?>&partid=<?php echo $pi_row['partid']; ?>&amt=" + amt;
                        xmlhttp.open("GET", url, true);
                        xmlhttp.send();
                    }
                }

            <?php
            }
            ?>
            document.theform.PartNumber.focus()
        </script>
        </body>

        </html>
<?php
    } else {
        echo "You do not have access to this function. <a href='" . COMPONENTS_PRIVATE . "/wip/wip.php'>Click here</a> to return to the WIP list";
    }
} else {
    echo "No shop provided,";
    //perhaps login again
}

mysqli_close($conn);

?>
