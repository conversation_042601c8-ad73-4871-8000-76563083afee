﻿<?php

require_once CONN;

$shopid = isset($_GET['shopid']) ? $_GET['shopid'] : $_COOKIE['shopid'];
$shopid = filter_var($shopid, FILTER_SANITIZE_STRING);
if (in_array($shopid, array('13445', '22957'))) $shopid = '22865';
$v = $_GET['v'];

?>
<h4>Click a column to sort by that column</h4>
<table id="partslist" class="sbdatatable w-100">
    <thead>
    <tr>
        <th>
            <div class="form-check">
                <input class="form-check-input" name="mastercheck" id="mastercheck" type="checkbox" value="on"
                       onchange="selectAll()">
            </div>
        </th>
        <th>Part #</th>
        <th>Description</th>
        <th>X-Ref #</th>
        <th>Cost</th>
        <th>Supplier</th>
        <th>Net On Hand</th>
        <th>Max On Hand</th>
        <th>Qty to Order</th>
    </tr>
    </thead>
    <?php

    $stmt = "select partid,partnumber,partdesc,partsupplier,netonhand,maxonhand,partcost,maxonhand-netonhand as qtytoorder,ReOrderLevel from partsinventory where shopid = ? and (partsupplier = ? || replace(partsupplier,'&#39;',\"'\") = ?) order by PartDesc, PartNumber";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("sss", $shopid, $v, $v);
        $query->execute();
        $results = $query->get_result();
        while ($rs = $results->fetch_assoc()) {
            if ($shopid == "8567") {
                if ($rs["maxonhand"] == 1 && $rs["ReOrderLevel"] == 0) {
                    $qtyOrder = $rs["qtytoorder"];
                } else {
                    $qtyOrder = 0;
                }
            } else {
                if ($rs['ReOrderLevel'] == 0 || $rs['maxonhand'] == 0) {
                    $qtyOrder = 0;
                } else {
                    $qtyOrder = $rs["qtytoorder"];
                }
            }

            $xref = "";
            $xstmt = "SELECT xref from xref WHERE shopid = ? and partnumber = ? LIMIT 1";
            if ($squery = $conn->prepare($xstmt)) {
                $squery->bind_param("ss", $shopid, $rs['partnumber']);
                $squery->execute();
                $squery->store_result();
                $squery->bind_result($xref);
                $squery->fetch();
                $squery->close();
            } else {
                echo "xref Prepare Failed: (" . $conn->errno . ") " . $conn->error;
            }
            ?>
            <tr>
                <td>
                    <span class="hidden"></span>
                    <input id="partid<?php echo $rs['partid']; ?>" class="partscheck"
                           name="partid<?php echo $rs['partid']; ?>"
                           value="on" <?php echo ($qtyOrder > 0) ? "checked" : ""; ?> type="checkbox"/>&nbsp;
                </td>
                <td>
                    <span style="display: none;"><?php echo str_replace("-", " ", $rs['partid']); ?></span>
                    <input name="partnumber<?php echo $rs['partid']; ?>" value="<?php echo $rs['partnumber']; ?>"
                           type="hidden"/><?php echo $rs['partnumber']; ?>
                </td>
                <td><input name="partdesc<?php echo $rs['partid']; ?>" value="<?php echo $rs['partdesc']; ?>"
                           type="hidden"/><?php echo $rs['partdesc']; ?>
                </td>
                <td><?php echo strtoupper($xref); ?></td>
                <td>
                    <span class="hidden"><?php echo $rs['partcost']; ?></span>
                    <div class="form-outline">
                        <input class="form-control" id="cost<?php echo $rs['partid']; ?>"
                               name="cost<?php echo $rs['partid']; ?>" type="text"
                               value="<?php echo $rs['partcost']; ?>"/>
                    </div>
                </td>
                <td><input name="partsupplier<?php echo $rs['partid']; ?>" value="<?php echo $rs['partsupplier']; ?>"
                           type="hidden"/><?php echo $rs['partsupplier']; ?>&nbsp;
                </td>
                <td><input name="partnetonhand<?php echo $rs['partid']; ?>" value="<?php echo $rs['netonhand']; ?>"
                           type="hidden"/><?php echo $rs['netonhand']; ?>
                </td>
                <td><?php echo $rs['maxonhand']; ?>&nbsp;</td>
                <td>
                    <span class="hidden"><?php echo $qtyOrder; ?></span>
                    <div class="form-outline">
                        <input class="form-control" id="qty<?php echo $rs['partid']; ?>"
                               name="qty<?php echo $rs['partid']; ?>" type="text" value="<?php echo $qtyOrder; ?>"/>
                    </div>
                </td>
            </tr>
            <?php
        }
        $query->close();
    } else {
        echo "partsinventory Prepare Failed: (" . $conn->errno . ") " . $conn->error;
    }
    ?>
</table>
<script>
    $(document).ready(() => {
        const table = $("#partslist").dataTable({
            paging: false,
            searching: false,
            columnDefs: [
                { orderable: false, targets: [0, 4, 8] } // Disable ordering for specified columns
            ],
            order: []
        });

        document.querySelectorAll('.form-outline').forEach((formOutline) => {
            new mdb.Input(formOutline).init();
        });
    });
</script>

<?php
mysqli_close($conn);
?>
