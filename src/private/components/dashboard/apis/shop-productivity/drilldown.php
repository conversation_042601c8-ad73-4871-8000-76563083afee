<?php
header('Content-Type: application/json');

require CONN;
$shopid = $_COOKIE['shopid'];
$sdate = date("Y-m-d", strtotime($_POST['startdate']));
$sdateTime = date("Y-m-d", strtotime($_POST['startdate']))." 00:00:00";
$edate = date("Y-m-d", strtotime($_POST['enddate']));
$edateTime = date("Y-m-d", strtotime($_POST['enddate'])) . " 23:59:59";
$rostatus = $_POST['rostatus'];

$roStatusWhere = "";
if ($rostatus == "closedRoOnly") {
    $roStatusWhere = " r.status = 'Closed' AND";
} else if ($rostatus == "finalRoOnly") {
    $roStatusWhere = " r.status = 'Final' AND";
}

$stmt = "select employeefirst ef,employeelast el, sum(TIMESTAMPDIFF(SECOND, startdatetime, enddatetime)) td, startdatetime, enddatetime FROM timeclock tc LEFT JOIN employees e ON tc.shopid = e.shopid and tc.emp = e.id WHERE tc.shopid = ? AND tc.startdatetime >= ? AND tc.enddatetime <= ? and (e.jobdesc like '%technician%' or e.jobdesc like '%general labor%') GROUP BY tc.emp ORDER BY tc.emp";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("sss", $shopid, $sdateTime, $edateTime);
    $query->execute();
    $rsresult = $query->get_result();
} else {
    echo "Timeclock Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$aResult = array();

if ($rsresult->num_rows > 0) {
    while ($rs = $rsresult->fetch_array()) {
        $item = array();
        $tech = trim($rs["el"]) . ", " . trim($rs["ef"]);
        $item['tech'] = strtoupper($tech);

        /*
        $stmt = "SELECT SUM(TIME_TO_SEC(enddatetime) - TIME_TO_SEC(startdatetime)) td from labortimeclock WHERE shopid = ? AND tech = ? AND startdatetime >= ? AND enddatetime <= ? ";

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("ssss", $shopid, $tech, $sdate, $edate);
            $query->execute();
            $xrsresult = $query->get_result();
        } else {
            echo "Labor Time Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }
        */
        $labor_hours = $labor_secs = 0;
        $lstmt = "select sum(labor.laborhours) as hours from labor JOIN repairorders r ON r.shopid = labor.shopid AND r.roid = labor.roid where labor.deleted = 'no' and $roStatusWhere r.rotype != 'No Approval' AND r.statusdate >= ? AND r.statusdate <= ? and r.shopid = ? AND labor.Tech = ?";
        //  echo sprintf(str_replace("?","'%s'", $lstmt), $shopid, $tech);
        //  die();
        if ($lquery = $conn->prepare($lstmt)) {
            $lquery->bind_param("ssss", $sdate, $edate, $shopid, $tech);
            $lquery->execute();
            //    $lquery->store_result();
            $lquery->bind_result($labor_hours);
            $lquery->fetch();
            $lquery->close();
        } else {
            echo "l73 prepare failed " . $conn->error;
        }

        $labor_secs = $labor_hours * 3600;

        //$xrs = $xrsresult->fetch_array();
        //$hoursSold = intval($xrs["td"] ?? 0);

        $hoursSold = round($labor_secs ?? 0);
        $hoursAvailable = round($rs["td"] ?? 0);

        $productivity = 0;
        if ($hoursAvailable > 0) {
            $productivity = ($hoursSold / $hoursAvailable);
        }
        $item['timeclock'] = $hoursAvailable;
        $item['laborclock'] = $hoursSold;
        $item['productivity'] = $productivity;
        //$item['laborHours'] = $labor_hours;
        //$item['query'] = sprintf(str_replace("?","'%s'", $lstmt), $sdate, $edate, $shopid, $tech);

        array_push($aResult, $item);
    }
}
echo json_encode($aResult);
?>
