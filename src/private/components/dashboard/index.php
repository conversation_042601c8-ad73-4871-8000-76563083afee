<div class="breakpoint-listener">
  <div class="sm"></div>
  <div class="md"></div>
  <div class="lg"></div>
  <div class="xl"></div>
  <div class="xxl"></div>
</div>

<?php
if (!empty($trialdaysleft)) {
?>
  <div class="alert alert-danger text-center font-weight-bolder" style="border-radius: 0px;">
    <small class="text-danger">This is a free trial of the BOSS Board. You have <?= $trialdaysleft ?> day<?= $trialdaysleft > 1 ? 's' : '' ?> left.</small>
    <a href="<?= COMPONENTS_PRIVATE ?>/settings/account.php" target="__blank"><small>Click here to upgrade!</small></a>
  </div>
<?php
}
?>

<div class="d-block d-sm-none text-center mb-4">
  <img src="<?= IMAGE ?>/dashboard_logo.png" style="height: 40px;">
</div>

<div class="d-flex justify-content-between align-items-center position-relative no-print">
  <sbp-date-range range="<?= $rangeoption ?>" @change="handleChangeDateRange($event, ...arguments)"></sbp-date-range>
  <img class="position-absolute d-none d-sm-block" src="<?= IMAGE ?>/dashboard_logo.png" style="height: 40px; left: calc(50% - 132px);">
  <div class="text-right ml-2">
    <a class="mr-1" href='#' @click="handlePrintDashboard"><i class="fas fa-lg fa-print"></i></a>
    <a href='#' @click="handleClickLayout"><i class="fas fa-lg fa-grip-horizontal"></i></a>
  </div>
</div>
<div class="text-center mt-4">
  <h1 class="mb-0"><?= $current_start_date ?> - <?= $current_end_date ?></h1>
</div>

<sbp-report-settings-dialog class="no-print" ref="report-settings-dialog" :ls-keys="lsKeys" :default-cards="defaultCards" @update="handleUpdateSettings"></sbp-report-settings-dialog>

<div class="p-1" v-cloak>
  <h2 class="mt-4">Sales Summary</h2>
  <draggable v-model="cards.sales" :animation="200" @start="handleDragCardStart" @end="handleDragCardEnd" handle=".handle">
    <transition-group class="row g-2 print-block" type="transition" :name="!drag ? 'flip-list' : null">
      <sbp-resizable :id="`sales-${salesCard.id}-card`" class="col-sm-12 col-md-4 col-lg-3 col-xl-2 print-no-break-inside print-keep-color" :class="getCardClass(index)" :active="['r']" @resize:end="handleResizeCard('sales', salesCard.id, $event)" v-for="(salesCard, index) in cards.sales" :key="salesCard.id">
        <template>
          <sbp-metric-summary-card v-if="salesCard.id === 'total-sales'" name="Total Sales" description="RO and Parts Sales" date-range="<?= $daterange ?>" :get-summary="getTotalSalesSummary" :get-drilldown="getTotalSalesDrilldown" @close="handleCloseMetric('sales', 'total-sales')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/productiondetail.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&rotype=all&referrer=report_dashboard">
            <template #drilldown="props">
              <sbp-total-sales-drilldown v-bind="props"></sbp-total-sales-drilldown>
            </template>
          </sbp-metric-summary-card>

          <sbp-metric-summary-card v-if="salesCard.id === 'ro-sales'" name="RO Sales" description="RO Sales" date-range="<?= $daterange ?>" :get-summary="getRoSalesSummary" :get-drilldown="getRoSalesDrilldown" @close="handleCloseMetric('sales', 'ro-sales')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/productiondetail.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&rotype=all&referrer=report_dashboard">
            <template #drilldown="props">
              <sbp-ro-sales-drilldown id="ro_sales_drilldown" v-bind="props"></sbp-ro-sales-drilldown>
            </template>
          </sbp-metric-summary-card>

          <!-- <sbp-metric-summary-card
            v-if="salesCard.id === 'invoiced'"
            name="Invoiced"
            date-range="<?= $daterange ?>"
            :get-summary="getInvoicedSummary"
            :get-drilldown="getInvoicedDrilldown"
            @close="handleCloseMetric('sales', 'invoiced')"
            report-url="<?= $baseurl ?>/sbpi2/reports/productiondetail.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&referrer=report_dashboard"
          >
            <template #drilldown="props">
              <sbp-ro-sales-drilldown id="invoiced_drilldown" v-bind="props"></sbp-ro-sales-drilldown>
            </template>
          </sbp-metric-summary-card> -->

          <sbp-metric-summary-card v-if="salesCard.id === 'payments'" name="Payments" description="Payments Received" date-range="<?= $daterange ?>" :get-summary="getPaymentsSummary" :get-drilldown="getPaymentsDrilldown" @close="handleCloseMetric('sales', 'payments')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/cashinreport.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&referrer=report_dashboard">
            <template #drilldown="props">
              <sbp-payments-drilldown v-bind="props"></sbp-payments-drilldown>
            </template>
          </sbp-metric-summary-card>

          <sbp-metric-summary-card v-if="salesCard.id === 'partsales'" name="PartSales (PS)" description="Parts Sales" date-range="<?= $daterange ?>" :get-summary="getPartsSalesSummary" :get-drilldown="getPartsSalesDrilldown" @close="handleCloseMetric('sales', 'partsales')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/partsinvoicelist.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&referrer=report_dashboard">
            <template #drilldown="props">
              <sbp-parts-sales-drilldown v-bind="props"></sbp-parts-sales-drilldown>
            </template>
          </sbp-metric-summary-card>

           <sbp-metric-summary-card v-if="salesCard.id === 'parts-sold-by-category'" name="Parts Sold by Category" description="Parts Sales by Category" date-range="<?= $daterange ?>" :get-summary="getPartsSalesCategorySummary" :get-drilldown="getPartsSalesCategoryDrilldown" @close="handleCloseMetric('sales', 'parts-sold-by-category')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/partssalesbycategory.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&rotype=all&cat=all&referrer=report_dashboard">

            <template #drilldown="props">
              <sbp-parts-sales-category-drilldown v-bind="props"></sbp-parts-sales-category-drilldown>
            </template>
          </sbp-metric-summary-card>

          <sbp-metric-summary-card v-if="salesCard.id === 'parts-profit-margin'" name="Parts Profit Margin" description="Profit Parts Sold" date-range="<?= $daterange ?>" :get-summary="getPartsProfitMarginSummary" :get-drilldown="getPartsProfitMarginDrilldown" @close="handleCloseMetric('sales', 'parts-profit-margin')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/shopprodsum.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&rotype=all&referrer=report_dashboard">
            <template #drilldown="props">
              <sbp-parts-profit-margin-drilldown v-bind="props"></sbp-parts-profit-margin-drilldown>
            </template>
          </sbp-metric-summary-card>
        </template>
      </sbp-resizable>
    </transition-group>
  </draggable>

  <h2 class="mt-4 print-page-break" v-cloak>Chart</h2>
  <draggable v-model="cards.charts" :animation="200" @start="handleDragCardStart" @end="handleDragCardEnd" handle=".handle">
    <transition-group class="row g-2 print-block" type="transition" :name="!drag ? 'flip-list' : null">
      <sbp-resizable class="print-keep-color print-no-break-inside print-block-center" :id="`charts-${chartCard.id}-card`" :class="getChartCardClass(chartCard.id)" :active="['r', 'rb']" @resize:end="handleResizeCard('charts', chartCard.id, $event)" v-for="chartCard in cards.charts" :key="chartCard.id">
        <template>
          <sbp-metric-summary-card v-if="chartCard.id === 'total-sales'" name="Total Sales" description="RO and Parts Sales" date-range="<?= $daterange ?>" :get-chart="getTotalSalesChart" :overwrite-summary="true" granular-unit="<?= $granular_unit ?>" @close="handleCloseMetric('charts', 'total-sales')">
            <template #summary="props">
              <sbp-total-sales-chart v-bind="props"></sbp-total-sales-chart>
            </template>
          </sbp-metric-summary-card>

          <sbp-metric-summary-card v-if="chartCard.id === 'aro'" name="ARO" description="Average Repair Order Amount" date-range="<?= $daterange ?>" :get-chart="getAROChart" :overwrite-summary="true" granular-unit="<?= $granular_unit ?>" @close="handleCloseMetric('charts', 'aro')">
            <template #summary="props">
              <sbp-aro-chart v-bind="props"></sbp-aro-chart>
            </template>
          </sbp-metric-summary-card>

          <sbp-metric-summary-card v-if="chartCard.id === 'effective-labor-rate'" name="Effective Labor Rate" description="Calculated by Total Labor $ Sold / Total Labor Hours Sold" date-range="<?= $daterange ?>" :get-chart="getEffectiveLaborRateChart" :overwrite-summary="true" granular-unit="<?= $granular_unit ?>" @close="handleCloseMetric('charts', 'effective-labor-rate')">
            <template #summary="props">
              <sbp-effective-labor-rate-chart v-bind="props"></sbp-effective-labor-rate-chart>
            </template>
          </sbp-metric-summary-card>

          <sbp-metric-summary-card v-if="chartCard.id === 'gross-profit'" name="Gross Profit" description="" date-range="<?= $daterange ?>" :get-chart="getGrossProfitChart" :overwrite-summary="true" granular-unit="<?= $granular_unit ?>" @close="handleCloseMetric('charts', 'gross-profit')">
            <template #summary="props">
              <sbp-gross-profit-chart v-bind="props"></sbp-gross-profit-chart>
            </template>
          </sbp-metric-summary-card>

          <sbp-metric-summary-card v-if="chartCard.id === 'revenue-summary'" name="Revenue Summary" description="Summary of all Revenue" date-range="<?= $daterange ?>" :get-drilldown="getRevenueDrilldown" :get-chart="getRevenueSummaryChart" :overwrite-summary="true" granular-unit="<?= $granular_unit ?>" @close="handleCloseMetric('charts', 'revenue-summary')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/shopprodsum.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&rotype=all&referrer=report_dashboard">
            <template #summary="props">
              <sbp-revenue-summary v-bind="props" @on-summary-load="handleResizeCard('charts', 'revenue-summary')"></sbp-revenue-summary>
            </template>
            <template #drilldown="props">
              <sbp-revenue-summary-drilldown v-bind="props"></sbp-revenue-summary-drilldown>
            </template>
          </sbp-metric-summary-card>

          <sbp-metric-summary-card v-if="chartCard.id === 'customer-concern-by-category'" name="Customer Concerns by Category" description="Breakdown of Customer Concern Categories" date-range="<?= $daterange ?>" :get-chart="getCustomerConcernByCategoryChart" :get-drilldown="getCustomerConcernByCategoryDrilldown" :overwrite-summary="true" granular-unit="<?= $granular_unit ?>" @close="handleCloseMetric('charts', 'customer-concern-by-category')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/concernsbycategory.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&referrer=report_dashboard">
            <template #summary="props">
              <sbp-customer-concerns-by-category v-bind="props" @on-summary-load="handleResizeCard('charts', 'customer-concern-by-category')"></sbp-customer-concerns-by-category>
            </template>
            <template #drilldown="props">
              <sbp-customer-concerns-by-category-drilldown v-bind="props"></sbp-customer-concerns-by-category-drilldown>
            </template>
          </sbp-metric-summary-card>

          <sbp-metric-summary-card v-if="chartCard.id === 'labor-hours-per-tech'" name="Labor Hours per Tech" date-range="<?= $daterange ?>" :get-chart="getLaborHoursPerTechChart" :get-drilldown="getLaborHoursPerTechDrilldown" :overwrite-summary="true" granular-unit="<?= $granular_unit ?>" @close="handleCloseMetric('charts', 'labor-hours-per-tech')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/tech_report_summary.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&referrer=report_dashboard">
            <template #summary="props">
              <sbp-labor-hours-per-tech v-bind="props" @on-summary-load="handleResizeCard('charts', 'labor-hours-per-tech')"></sbp-labor-hours-per-tech>
            </template>
            <template #drilldown="props">
              <sbp-labor-hours-per-tech-drilldown v-bind="props"></sbp-labor-hours-per-tech-drilldown>
            </template>
          </sbp-metric-summary-card>
        </template>
      </sbp-resizable>
    </transition-group>
  </draggable>

  <h2 class="mt-4 print-page-break" v-cloak>Shop KPIs and Metrics</h2>
  <draggable v-model="cards.kpis" :animation="200" @start="handleDragCardStart" @end="handleDragCardEnd" handle=".handle">
    <transition-group class="row g-2 print-block" type="transition" :name="!drag ? 'flip-list' : null">
      <sbp-resizable :id="`kpis-${kpiCard.id}-card`" class="col-sm-12 col-md-4 col-lg-3 col-xl-2 print-no-break-inside print-keep-color" :class="getCardClass(index)" :active="['r']" @resize:end="handleResizeCard('kpis', kpiCard.id, $event)" v-for="(kpiCard, index) in cards.kpis" :key="kpiCard.id">
        <sbp-metric-summary-card v-if="kpiCard.id === 'ro-count'" name="RO Count" description="Count of all Repair Orders" date-range="<?= $daterange ?>" :get-summary="getRoCountSummary" :get-drilldown="getRoCountDrilldown" @close="handleCloseMetric('kpis', 'ro-count')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/shopprodsum.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&rotype=all&referrer=report_dashboard">
          <template #drilldown="props">
            <sbp-ro-count-drilldown v-bind="props"></sbp-ro-count-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'aro'" name="ARO" description="Average Repair Order Amount" date-range="<?= $daterange ?>" :get-summary="getAROSummary" :get-drilldown="getARODrilldown" @close="handleCloseMetric('kpis', 'aro')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/shopprodsum.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&rotype=all&referrer=report_dashboard">
          <template #drilldown="props">
            <sbp-aro-drilldown v-bind="props"></sbp-aro-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'shop-productivity'" name="Shop Productivity" description="Hours Sold / Hours Available = X%. Goal Close to 100%" date-range="<?= $daterange ?>" :get-summary="getShopProductivitySummary" :get-drilldown="getShopProductivityDrilldown" @close="handleCloseMetric('kpis', 'shop-productivity')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/timeclock_labor_sold.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&referrer=report_dashboard&job_completed=no&ro_status=Closed">
          <template #drilldown="props">
            <sbp-shop-productivity-drilldown v-bind="props"></sbp-shop-productivity-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'closing-rate'" name="Closing Rate" description="RO Sales vs RO Presented Sales" date-range="<?= $daterange ?>" :get-summary="getClosingRateSummary" :get-drilldown="getClosingRateDrilldown" @close="handleCloseMetric('kpis', 'closing-rate')">
          <template #drilldown="props">
            <sbp-closing-rate-drilldown v-bind="props"></sbp-closing-rate-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'labor-hours-per-ro'" name="Labor Hours Per RO" description="Average Count of Labor Hours of Closed RO’s" date-range="<?= $daterange ?>" :get-summary="getLaborHoursPerRoSummary" :get-drilldown="getLaborHoursPerRoDrilldown" @close="handleCloseMetric('kpis', 'labor-hours-per-ro')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/labor_timesheet.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&referrer=report_dashboard">
          <template #drilldown="props">
            <sbp-labor-hours-per-ro-drilldown v-bind="props"></sbp-labor-hours-per-ro-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'total-labor-hours'" name="Total Labor Hours" description="Total Sold Labor Hours" date-range="<?= $daterange ?>" :get-summary="getTotalLaborHoursSummary" :get-drilldown="getTotalLaborHoursDrilldown" @close="handleCloseMetric('kpis', 'total-labor-hours')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/tech_report_summary.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&referrer=report_dashboard">
          <template #drilldown="props">
            <sbp-total-labor-hours-drilldown v-bind="props"></sbp-total-labor-hours-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'average-hours-per-tech'" name="Average Hours Per Tech" description="Average Count of Labor Hours of Closed RO’s per Technician" date-range="<?= $daterange ?>" :get-summary="getAvgHoursPerTechSummary" :get-drilldown="getAvgHoursPerTechDrilldown" @close="handleCloseMetric('kpis', 'average-hours-per-tech')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/tech_report_summary.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&referrer=report_dashboard">
          <template #drilldown="props">
            <sbp-avg-hours-per-tech-drilldown v-bind="props"></sbp-avg-hours-per-tech-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'tech-cost-per-labor-hour'" name="Tech Cost Per Labor Hour" description="Average Pay Rate for Technicians Per Hour" date-range="<?= $daterange ?>" :get-summary="getTechCostPerLaborHourSummary" :get-drilldown="getTechCostPerLaborHourDrilldown" @close="handleCloseMetric('kpis', 'tech-cost-per-labor-hour')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/techreportsingle.php?techreq=yes&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&empid=all&referrer=report_dashboard">
          <template #drilldown="props">
            <sbp-tech-cost-per-labor-hour-drilldown v-bind="props"></sbp-tech-cost-per-labor-hour-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'average-ros-per-tech'" name="Average ROs Per Tech" description="Average Count of Closed RO’s per Technician" date-range="<?= $daterange ?>" :get-summary="getAvgRosPerTechSummary" :get-drilldown="getAvgRosPerTechDrilldown" @close="handleCloseMetric('kpis', 'average-ros-per-tech')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/labor_timesheet.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&referrer=report_dashboard">
          <template #drilldown="props">
            <sbp-avg-ros-per-tech-drilldown v-bind="props"></sbp-avg-ros-per-tech-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'labor-cost-as-percent-of-sales'" name="Labor Cost As Percent of Sales" description="Cost of Labor as a Percentage of Total Sales" date-range="<?= $daterange ?>" :down-favorable="true" :get-summary="getLaborCostAsPercentOfSalesSummary" :get-drilldown="getLaborCostAsPercentOfSalesDrilldown" @close="handleCloseMetric('kpis', 'labor-cost-as-percent-of-sales')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/shopprodsum.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&rotype=all&referrer=report_dashboard">
          :down-favorable="true"
          <template #drilldown="props">
            <sbp-labor-cost-as-percent-of-sales-drilldown v-bind="props"></sbp-labor-cost-as-percent-of-sales-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'parts-cost-as-percent-of-sales'" name="Parts Cost As Percent of Sales" description="Cost of Parts as a Percentage of Total Sales" date-range="<?= $daterange ?>" :down-favorable="true" :get-summary="getPartsCostAsPercentOfSalesSummary" :get-drilldown="getPartsCostAsPercentOfSalesDrilldown" @close="handleCloseMetric('kpis', 'parts-cost-as-percent-of-sales')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/shopprodsum.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&rotype=all&referrer=report_dashboard">
          :down-favorable="true"
          <template #drilldown="props">
            <sbp-parts-cost-as-percent-of-sales-drilldown v-bind="props"></sbp-parts-cost-as-percent-of-sales-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'touch-time'" name="Touch Time" description="Uses Labor Time Clock to calculate Time Touched by Technician" date-range="<?= $daterange ?>" :get-summary="getTouchTimeSummary" :get-drilldown="getTouchTimeDrilldown" @close="handleCloseMetric('kpis', 'touch-time')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/labor_timesheet.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&referrer=report_dashboard">
          <template #drilldown="props">
            <sbp-touchtime-drilldown v-bind="props"></sbp-touchtime-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'effective-labor-rate'" name="Effective Labor Rate" description="Calculated by Total Labor $ Sold / Total Labor Hours Sold" date-range="<?= $daterange ?>" :get-summary="getEffectiveLaborRateSummary" :get-drilldown="getEffectiveLaborRateDrilldown" @close="handleCloseMetric('kpis', 'effective-labor-rate')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/shopprodsum.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&rotype=all&referrer=report_dashboard">
          <template #drilldown="props">
            <sbp-effective-labor-rate-drilldown v-bind="props"></sbp-effective-labor-rate-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'gross-profit'" name="Gross Profit" date-range="<?= $daterange ?>" :get-summary="getGrossProfitSummary" :get-drilldown="getGrossProfitDrilldown" @close="handleCloseMetric('kpis', 'gross-profit')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/shopprodsum.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&rotype=all&referrer=report_dashboard">
          <template #drilldown="props">
            <sbp-gross-profit-drilldown v-bind="props"></sbp-gross-profit-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'gross-profit-per-hour'" name="Gross Profit Per Hour" date-range="<?= $daterange ?>" :get-summary="getGrossProfitPerHourSummary" :get-drilldown="getGrossProfitPerHourDrilldown" @close="handleCloseMetric('kpis', 'gross-profit-per-hour')" report-url="<?= COMPONENTS_PRIVATE ?>/reports/shopprodsum.php?techreq=no&sdate=<?= $current_start_date ?>&edate=<?= $current_end_date ?>&rotype=all&referrer=report_dashboard">
          <template #drilldown="props">
            <sbp-gross-profit-per-hour-drilldown v-bind="props"></sbp-gross-profit-per-hour-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'customer-acquisition-rate'" name="Customer Acquisition Rate" date-range="<?= $daterange ?>" :get-summary="getCustomerAcquisitionRateSummary" :get-drilldown="getCustomerAcquisitionRateDrilldown" @close="handleCloseMetric('kpis', 'customer-acquisition-rate')">
          <template #drilldown="props">
            <sbp-customer-acquisition-rate-drilldown v-bind="props"></sbp-customer-acquisition-rate-drilldown>
          </template>
        </sbp-metric-summary-card>

        <sbp-metric-summary-card v-if="kpiCard.id === 'new-existing-customers-sales'" name="New vs. Existing Customers" date-range="<?= $daterange ?>" :get-summary="getNewExistingCustomersSalesSummary" :get-drilldown="getNewExistingCustomersSalesDrilldown" @close="handleCloseMetric('kpis', 'new-existing-customers-sales')">
          <template #drilldown="props">
            <sbp-new-existing-customers-sales-drilldown v-bind="props"></sbp-new-existing-customers-sales-drilldown>
          </template>
        </sbp-metric-summary-card>
      </sbp-resizable>
    </transition-group>
  </draggable>
  <div style="margin-top: 50px;"></div>
</div>