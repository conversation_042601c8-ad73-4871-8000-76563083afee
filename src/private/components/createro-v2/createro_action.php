<?php

require CONN;

// ************  prepare the post variables ****************
$cid = $_POST['cid'];
$vid = $_POST['vid'];
$c1 = $_POST['c1'];
$c2 = $_POST['c2'];
$c3 = $_POST['c3'];
$c4 = $_POST['c4'];
$c5 = $_POST['c5'];
$c6 = $_POST['c6'];
$c7 = $_POST['c7'];
$c8 = $_POST['c8'];
$c9 = $_POST['c9'];
$c10 = $_POST['c10'];
$c11 = $_POST['c11'];
$c12 = $_POST['c12'];
$c13 = $_POST['c13'];
$c14 = $_POST['c14'];
$c15 = $_POST['c15'];
$currmileage = $_POST['currmileage'];
$tagnum = $_POST['tagnum'];
$shopid = $_COOKIE['shopid'];
$oshopid = $_POST['oshopid'];
$schid = $_POST['schid'];
$waiter = $_POST['waiter'] ?? '';
$cannedjson = isset($_REQUEST['cannedjson']) ? json_decode($_REQUEST['cannedjson'], true) : array();
$recrepairsjson = isset($_REQUEST['recrepairsjson']) ? json_decode($_REQUEST['recrepairsjson'], true) : array();
$empname = "";
if (!is_numeric($cid)) {
    echo "nope";
    exit;
}

if (!is_numeric($vid)) {
    echo "nope";
    exit;
}

if (!empty($schid)) {
    $stmt = "select concat(employeefirst,' ',employeelast) from employees e, schedule s where s.empid=e.id and s.shopid=e.shopid and s.shopid = ? and s.id=?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $schid);
        $query->execute();
        $query->bind_result($empname);
        $query->fetch();
    }
    $query->close();
}

// get the customer info

$stmt = "select taxexempt,email,firstname,lastname,city,state,zip,contact,address,homephone,workphone,CellPhone,spousename,spousework,spousecell from customer where shopid = ? and customerid = ?";
if ($query = $conn->prepare($stmt)) {

    if ($shopid != $oshopid) {
        $query->bind_param("si", $oshopid, $cid);
    } else {
        $query->bind_param("si", $shopid, $cid);
    }
    $query->execute();
    $query->store_result();
    $num_stmt_rows = $query->num_rows;
    //echo "customer rows:".$num_stmt_rows."<BR>";
    if ($num_stmt_rows > 0) {
        $query->bind_result($taxexempt, $email, $customerfirst, $customerlast, $customercity, $customerstate, $customerzip, $contact, $CustomerAddress, $CustomerPhone, $CustomerWork, $CellPhone, $spousename, $spousework, $spousecell);
        $query->fetch();
    } else {
        $taxexempt = '';
        $email = '';
        $customerfirst = '';
        $customerlast = '';
        $customercity = '';
        $customerstate = '';
        $customerzip = '';
        $contact = '';
        $CustomerAddress = '';
        $CustomerPhone = '';
        $CustomerWork = '';
        $CellPhone = '';
        $spousename = '';
        $spousework = '';
        $spousecell = '';
    }
    $query->close();
} else {
    echo "Customer failed: (" . $conn->errno . ") " . $conn->error;
}

$Customer = $customerfirst . ' ' . $customerlast;
$CustomerCSZ = $customercity . ', ' . $customerstate . '. ' . $customerzip;
$lastFirst = $customerlast . ',' . $customerfirst;


// ****************** get vehicle information *********************

$stmt = "select fleetno,year,make,model,licnumber,licstate,currentmileage,vin,engine,cyl,transmission,drivetype,custom1,custom2,custom3,custom4,custom5,custom6,custom7,custom8,color from vehicles where shopid = ? and vehid = ?";
if ($query = $conn->prepare($stmt)) {

    if ($shopid != $oshopid) {
        $query->bind_param("si", $oshopid, $vid);
    } else {
        $query->bind_param("si", $shopid, $vid);
    }
    $query->execute();
    $query->store_result();
    $num_vstmt_rows = $query->num_rows;
    //echo "vehicle rows:".$num_vstmt_rows."<BR>";
    if ($num_vstmt_rows > 0) {
        $query->bind_result($fleetno, $vehyear, $vehmake, $vehmodel, $vehlicense, $vehstate, $vehiclemiles, $Vin, $VehEngine, $Cyl, $VehTrans, $DriveType, $customvehicle1, $customvehicle2, $customvehicle3, $customvehicle4, $customvehicle5, $customvehicle6, $customvehicle7, $customvehicle8, $color);
        $query->fetch();
    } else {
        $fleetno = '';
        $vehyear = '';
        $vehmake = '';
        $vehmodel = '';
        $vehlicense = '';
        $vehstate = '';
        $vehiclemiles = '';
        $Vin = '';
        $VehEngine = '';
        $Cyl = '';
        $VehTrans = '';
        $DriveType = '';
        $customvehicle1 = '';
        $customvehicle2 = '';
        $customvehicle3 = '';
        $customvehicle4 = '';
        $customvehicle5 = '';
        $customvehicle6 = '';
        $customvehicle7 = '';
        $customvehicle8 = '';
        $color = '';
    }
    $query->close();
} else {
    echo "Vehicle failed: (" . $conn->errno . ") " . $conn->error;
}
$VehInfo = $vehyear . ' ' . $vehmake . ' ' . $vehmodel . ' ' . $color;
$VehLicNum = $vehlicense . ' ' . $vehstate;

// ***************** get the next ro number ***********************

$stmt = "select coalesce(roid,0) from repairorders where shopid = ? order by roid desc limit 1";
if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    //echo "roid rows:".$num_roid_rows."<BR>";
    if ($num_roid_rows > 0) {
        $query->bind_result($roid);
        $query->fetch();
    } else {
        $roid = 1000;
    }
    $query->close();
} else {
    echo "RO Number failed: (" . $conn->errno . ") " . $conn->error;
}

$newroid = $roid + 1;

// ******************* get company values for the ro ******************
$stmt = "select coupon,defaulttaxrate,defaultlabortaxrate,defaultsublettaxrate,defaultwarrmos,defaultwarrmiles,timezone,vehiclefield1label,vehiclefield2label,vehiclefield3label"
    . ",vehiclefield4label,vehiclefield5label,vehiclefield6label,vehiclefield7label,vehiclefield8label,rowarrdisclosure,rodisclosure,userfee1amount,userfee1type,userfee1"
    . ",userfee2amount,userfee2type,userfee2,userfee3amount,userfee3type,userfee3,hazardouswaste,storagefee,hst,gst,pst,qst,chargehst,chargepst,chargegst,chargeqst,companyzip from company where shopid = ?";
//echo $stmt."<BR>";

$coupon = "";
$taxrate = 0;
$labortaxrate = 0;
$sublettaxrate = 0;
$warrmos = "";
$warrmiles = "";
$timezone = "";
$cflabel1 = "";
$cflabel2 = "";
$cflabel3 = "";
$cflabel4 = "";
$cflabel5 = "";
$cflabel6 = "";
$cflabel7 = "";
$cflabel8 = "";
$warrdisc = "";
$rodisc = "";
$canadiantax = "";
$userfee1amount = 0;
$userfee2amount = 0;
$userfee3amount = 0;
$hazwaste = 0;
$storagefee = 0;
$hst = 0;
$gst = 0;
$pst = 0;
$qst = 0;
$cantaxrate = 0;

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    //echo "company rows:".$num_roid_rows."<BR>";
    if ($num_roid_rows > 0) {
        $query->bind_result($coupon, $taxrate, $labortaxrate, $sublettaxrate, $warrmos, $warrmiles, $timezone, $cflabel1, $cflabel2, $cflabel3, $cflabel4, $cflabel5, $cflabel6, $cflabel7, $cflabel8, $warrdisc, $rodisc, $userfee1amount, $userfee1type, $userfee1desc, $userfee2amount, $userfee2type, $userfee2desc, $userfee3amount, $userfee3type, $userfee3desc, $hazwaste, $storagefee, $hst, $gst, $pst, $qst, $chargehst, $chargepst, $chargegst, $chargeqst, $companyzip);
        $query->fetch();
    } else {
        $coupon = '';
        $taxrate = '';
        $labortaxrate = '';
        $sublettaxrate = '';
        $warrmos = '';
        $warrmiles = '';
        $timezone = '';
        $cflabel1 = '';
        $cflabel2 = '';
        $cflabel3 = '';
        $cflabel4 = '';
        $cflabel5 = '';
        $cflabel6 = '';
        $cflabel7 = '';
        $cflabel8 = '';
        $warrdisc = 'Warranty Disclosure';
        $rodisc = 'Signature Disclosure';
        $userfee1amount = '';
        $userfee1type = '';
        $userfee1desc = '';
        $userfee2amount = '';
        $userfee2type = '';
        $chargehst = '';
        $chargepst = '';
        $chargegst = '';
        $chargeqst = '';
        $userfee2desc = '';
        $userfee3amount = '';
        $userfee3type = '';
        $userfee3desc = '';
        $hazwaste = '';
        $storagefee = '';
        $hst = 0;
        $gst = 0;
        $pst = 0;
        $qst = 0;
    }
    $query->close();
}

$stmt = "select sourceoptimized from settings where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($sourceoptimized);
    $query->fetch();
    $query->close();
}

if (!is_numeric($companyzip) && ($hst > 0 || $gst > 0 || $pst > 0 || $qst > 0)) {

    if ($chargehst == "yes")
        $cantaxrate += $hst;

    if ($chargepst == "yes")
        $cantaxrate += $pst;

    if ($chargegst == "yes")
        $cantaxrate += $gst;

    if ($chargeqst == "yes")
        $cantaxrate += $qst;

    $taxrate = $cantaxrate;
    $labortaxrate = $taxrate;
    $sublettaxrate = $taxrate;
    $canadiantax = $hst . ',' . $pst . ',' . $gst . ',' . $qst;
}

if (strtolower($taxexempt) == "yes") {
    $taxrate = 0;
    $labortaxrate = 0;
    $sublettaxrate = 0;
    if (!is_numeric($companyzip))
        $canadiantax = '0,0,0,0';
}


$datein = getCurrentLocalTime($timezone, 'date');
$timein = getCurrentLocalTime($timezone);
//echo $timein."<BR>";

if (!empty($empname))
    $defaultwriter = strtoupper($empname);
else
    $defaultwriter = strtoupper($_COOKIE['username']);

$status = "1INSPECTION";
$statusdate = $datein;
$complainttable = 'yes';

if ($userfee1type == '%') {
    $userfee1percent = $userfee1amount;
    $userfee1amount = 0;
} else {
    $userfee1percent = 0;
}
if ($userfee2type == '%') {
    $userfee2percent = $userfee2amount;
    $userfee2amount = 0;
} else {
    $userfee2percent = 0;
}
if ($userfee3type == '%') {
    $userfee3percent = $userfee3amount;
    $userfee3amount = 0;
} else {
    $userfee3percent = 0;
}

// ****************** get the last source for this customer or default source ******************

$stmt = "select source from source where shopid = ? and `default` = 'yes' order by `default` desc, source asc limit 1";
if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    //echo "source from source rows:".$num_roid_rows."<BR>";
    if ($num_roid_rows > 0) {
        $query->bind_result($defaultsource);
        $query->fetch();
    } else {
        $defaultsource = 'NONE';
    }
    $query->close();
} else {
    echo "Source from failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "select source,origtech from repairorders where shopid = ? and customerid = ? order by roid desc limit 1";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $cid);
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    //echo "source from ro rows:".$num_roid_rows."<BR>";
    if ($num_roid_rows > 0) {
        $query->bind_result($lastsource, $lastref);
        $query->fetch();
        if ($sourceoptimized == 'yes') {
            $lastsource = 'Repeat';
            $lastref = '';
        }
    } else {
        $lastsource = $defaultsource;
        $lastref = '';
    }

    $query->close();
} else {
    echo "Source from RO failed: (" . $conn->errno . ") " . $conn->error;
}

if ($shopid == '3584' || $shopid == 'demo') {
    $lastsource = "NONE";
    $lastref = '';
}

$stmt = "select rotype from rotype where shopid = ? order by isdefault desc, rotype asc limit 1";
if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    //echo "rotype rows:".$num_roid_rows."<BR>";
    if ($num_roid_rows > 0) {
        $query->bind_result($rotype);
        $query->fetch();
    } else {
        $rotype = "Unknown";
    }
    $query->close();
} else {
    echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
}
//echo $rotype."<BR>";
$origshopid = '';

// now check to see which mileage is greater or is not ''
$currmileage = str_replace(",", "", $currmileage);
$milestouse = 0;
if (is_numeric($vehiclemiles) && is_numeric($currmileage)) {
    // which is greater
    if ($vehiclemiles > $currmileage) {
        $milestouse = $vehiclemiles;
    } elseif ($currmileage > $vehiclemiles) {
        $milestouse = $currmileage;
    }
} elseif (is_numeric($vehiclemiles)) {
    $milestouse = $vehiclemiles;
} elseif (is_numeric($currmileage)) {
    $milestouse = $currmileage;
} else {
    $milestouse = 0;
}


$discounttaxable = 'no';
// ******************* create the ro insert statement *****************

$rostmt = "insert into repairorders (coupon,shopid,origshopid,ROID,fleetno,source,rodisc,warrdisc,email,userfee1,userfee2,userfee3,hazardouswaste,userfee1label,"
    . "userfee2label,userfee3label,userfee1type,userfee2type,userfee3type,userfee1percent,userfee2percent,userfee3percent,CustomerID,LaborTaxRate,SubletTaxRate,TaxRate,DateIn,"
    . "TimeIn,customerfirst,customerlast,customercity,customerstate,customerzip,VehID,Customer,VehInfo"
    . ", vehyear,vehmake,vehmodel,vehlicense,vehstate,vehiclemiles,VehLicNum,WarrMos,contact,Writer,WarrMiles,Status,StatusDate,CustomerAddress,CustomerCSZ,CustomerPhone"
    . ", ROType,Vin,CustomerWork,complainttable,VehEngine,Cyl,VehTrans,CellPhone,LastFirst,DriveType"
    . ",customvehicle1label,customvehicle2label,customvehicle3label,customvehicle4label,customvehicle5label,customvehicle6label,customvehicle7label,customvehicle8label,customvehicle1,"
    . "customvehicle2,customvehicle3,customvehicle4,customvehicle5,customvehicle6,customvehicle7,customvehicle8,spousename,spousework,spousecell,tagnumber,origtech,discounttaxable,canadiantax,waiter) values ("
    . "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

//printf (str_replace('?',"'%s'",$rostmt),$coupon,$shopid,$origshopid,$newroid,$fleetno,$lastsource,$rodisc,$warrdisc,$email,$userfee1amount,$userfee2amount,$userfee3amount,$hazwaste,$userfee1desc,$userfee2desc,$userfee3desc,$userfee1type,$userfee2type,$userfee3type,$userfee1percent,$userfee2percent,$userfee3percent,$cid,$labortaxrate,$sublettaxrate,$taxrate,$datein,$timein,$customerfirst,$customerlast,$customercity,$customerstate,$customerzip,$vid,$Customer,$VehInfo,$vehyear,$vehmake,$vehmodel,$vehlicense,$vehstate,$vehiclemiles,$VehLicNum,$warrmos,$contact,$defaultwriter,$warrmiles,$status,$statusdate,$CustomerAddress,$CustomerCSZ,$CustomerPhone,$rotype,$Vin,$CustomerWork,$complainttable,$VehEngine,$Cyl,$VehTrans,$CellPhone,$lastFirst,$DriveType,$cflabel1,$cflabel2,$cflabel3,$cflabel4,$cflabel5,$cflabel6,$cflabel7,$cflabel8,$customvehicle1,$customvehicle2,$customvehicle3,$customvehicle4,$customvehicle5,$customvehicle6,$customvehicle7,$customvehicle8,$spousename,$spousework,$spousecell);
if ($roquery = $conn->prepare($rostmt)) {
    $roquery->bind_param("sssisssssddddsssssssssidddsssssssissssssssssssssssssssssssssssssssssssssssssssssssssss", $coupon, $shopid, $oshopid, $newroid, $fleetno, $lastsource, $rodisc, $warrdisc, $email, $userfee1amount, $userfee2amount, $userfee3amount, $hazwaste, $userfee1desc, $userfee2desc, $userfee3desc, $userfee1type, $userfee2type, $userfee3type, $userfee1percent, $userfee2percent, $userfee3percent, $cid, $labortaxrate, $sublettaxrate, $taxrate, $datein, $timein, $customerfirst, $customerlast, $customercity, $customerstate, $customerzip, $vid, $Customer, $VehInfo, $vehyear, $vehmake, $vehmodel, $vehlicense, $vehstate, $currmileage, $VehLicNum, $warrmos, $contact, $defaultwriter, $warrmiles, $status, $statusdate, $CustomerAddress, $CustomerCSZ, $CustomerPhone, $rotype, $Vin, $CustomerWork, $complainttable, $VehEngine, $Cyl, $VehTrans, $CellPhone, $lastFirst, $DriveType, $cflabel1, $cflabel2, $cflabel3, $cflabel4, $cflabel5, $cflabel6, $cflabel7, $cflabel8, $customvehicle1, $customvehicle2, $customvehicle3, $customvehicle4, $customvehicle5, $customvehicle6, $customvehicle7, $customvehicle8, $spousename, $spousework, $spousecell, $tagnum, $lastref, $discounttaxable, $canadiantax, $waiter);
    if ($roquery->execute()) {
        $conn->commit();
        $roquery->close();
        echo "success|" . $newroid;
    } else {
        echo $conn->errno;
    }
} else {
    echo "RO Create failed: (" . $conn->errno . ") " . $conn->error;
}

// now post to myshopmanager if apikey exists
$msmapikey = "";
$stmt = "select apikey from apilogin where shopid = ? and companyname = 'myshopmanager'";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($msmapikey);
    $query->fetch();
    $query->close();
} else {
    echo "RO Type failed: (" . $conn->errno . ") " . $conn->error;
}

$result = "";
if ($msmapikey != "") {

    // post to the endpoint
    $ar = array();
    $ar['shopid'] = $shopid;
    $ar['key'] = $msmapikey;
    $ar['ronumber'] = $newroid;
    $ar['customerid'] = $cid;
    $ar['lastname'] = $customerlast;
    $ar['firstname'] = $customerfirst;
    $ar['address'] = $CustomerAddress;
    $ar['city'] = $customercity;
    $ar['state'] = $customerstate;
    $ar['zip'] = $customerzip;
    $ar['homephone'] = $CustomerPhone;
    $ar['workphone'] = $CustomerWork;
    $ar['cellphone'] = $CellPhone;
    $ar['vehid'] = $vid;
    $ar['year'] = $vehyear;
    $ar['make'] = $vehmake;
    $ar['model'] = $vehmodel;
    $ar['vin'] = $Vin;
    $ar['license'] = $vehlicense;
    $ar['status'] = "Inspection";
    $ar['totalro'] = "0";
    $ar['partstaxrate'] = $taxrate;
    $ar['labortaxrate'] = $labortaxrate;
    $ar['sublettaxrate'] = $sublettaxrate;
    $ar['writer'] = $defaultwriter;
    $ar['milesin'] = $currmileage;
    $ar['vehengine'] = $VehEngine;

    $json = json_encode($ar);
    $json = str_replace("\n", "", str_replace("\r", "", $json));
    // echo "|".$json;
    // now post to msm endpoinit

    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, "https://myshopmanager.com/webhooks/shopboss/repairorder/created");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10); //timeout in seconds

    $headers = array();
    $headers[] = "Content-Type: application/json";
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $result = curl_exec($ch);
    //echo "|".$result;

}


$stmt = "select coalesce(complaintid,0) from complaints where shopid = ? order by complaintid desc,roid desc limit 1";
if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    //echo "roid rows:".$num_roid_rows."<BR>";
    if ($num_roid_rows > 0) {
        $query->bind_result($complaintid);
        $query->fetch();
    } else {
        $complaintid = 1000;
    }
    $query->close();
} else {
    echo "RO Number failed: (" . $conn->errno . ") " . $conn->error;
}


// now create the vehicle issues
$newcomid = $complaintid + 1;
$runconcerns = "";
for ($j = 1; $j <= 15; $j++) {

    $v = 'c' . $j;

    $foundJobs = array_filter($cannedjson, function ($job) use ($j) {
        return $job['order'] === $j;
    });

    $foundRecRepairs = array_filter($recrepairsjson, function ($job) use ($j) {
        return $job['order'] === $j;
    });

    if (!empty($foundJobs) || !empty($foundRecRepairs)) {
        continue;
    }

    if (strlen($_REQUEST[$v]) > 0) {
        $cv = 's' . $j;
        if (isset($_POST[$cv])) {
            $catval = $_POST[$cv];
        } else {
            $catval = "";
        }
        $stmt = "insert into complaints (shopid,roid,complaint,acceptdecline,complaintid,displayorder,issue) values (?,?,?,'Pending',?,?,?)";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param('sisiis', $shopid, $newroid, $_POST[$v], $newcomid, $j, $catval);
            if ($query->execute()) {
                $conn->commit();
                $runconcerns .= $_POST[$v] . ", ";
                $query->close();
            } else {
                echo $conn->errno;
            }
        }
    }
    $newcomid++;
}

// test for autotext.me
$apikey = "";
$stmt = "select apikey from apilogin where shopid = ? and companyname = 'autotext'";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($apikey);
    $query->fetch();
    $query->close();
} else {
    $apikey = "";
}

if ($apikey != "") {

    // post to the endpoint
    $ar = array();
    $ar['shopid'] = $shopid;
    $ar['key'] = $apikey;
    $ar['ronumber'] = $newroid;
    $ar['customerid'] = $cid;
    $ar['lastname'] = $customerlast;
    $ar['firstname'] = $customerfirst;
    $ar['address'] = $CustomerAddress;
    $ar['city'] = $customercity;
    $ar['state'] = $customerstate;
    $ar['zip'] = $customerzip;
    $ar['homephone'] = $CustomerPhone;
    $ar['workphone'] = $CustomerWork;
    $ar['cellphone'] = $CellPhone;
    $ar['vehid'] = $vid;
    $ar['year'] = $vehyear;
    $ar['make'] = $vehmake;
    $ar['model'] = $vehmodel;
    $ar['vin'] = $Vin;
    $ar['license'] = $vehlicense;
    $ar['status'] = "Inspection";
    $ar['totalro'] = "0";
    $ar['partstaxrate'] = $taxrate;
    $ar['labortaxrate'] = $labortaxrate;
    $ar['sublettaxrate'] = $sublettaxrate;
    $ar['writer'] = $defaultwriter;
    $ar['milesin'] = $currmileage;
    $ar['vehengine'] = $VehEngine;
    $ar['email'] = $email;
    $ar['customerConcerns'] = $runconcerns;

    $json = json_encode($ar);
    $json = str_replace("\n", "", str_replace("\r", "", $json));

    $json = json_encode($ar);
    $json = str_replace("\n", "", str_replace("\r", "", $json));
    //echo $json;

    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, "https://shopboss.autotext.me/Admin/Shopboss/webhook.php");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
    curl_setopt($ch, CURLOPT_POST, 1);

    $headers = array("Content-Type: application/json");
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $json_result = curl_exec($ch);
    //echo $json_result;
    curl_close($ch);
}

// repairshopsolutions
$apikey = "";
$stmt = "select apikey from apilogin where shopid = ? and companyname = 'repairshopsolutions'";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($apikey);
    $query->fetch();
    $query->close();
} else {
    $apikey = "";
}

if ($apikey != "") {
    // post to the endpoint
    $ar = $ro = $phones = array();

    if (!empty($CustomerPhone))
        $phones[] = array('kind' => 'Home', 'number' => $CustomerPhone);
    if (!empty($CustomerWork))
        $phones[] = array('kind' => 'Work', 'number' => $CustomerWork);
    if (!empty($CellPhone))
        $phones[] = array('kind' => 'Mobile', 'number' => $CellPhone);

    $ro['primaryKey'] = $shopid . $newroid;
    $ro['repairOrderNumber'] = strval($newroid);
    $ro['invoiceNumber'] = strval($newroid);
    $ro['estimateNumber'] = strval($newroid);
    $ro['status'] = 'Inspection';
    $ro['customerId'] = $cid;
    $ro['customerFirstName'] = $customerlast;
    $ro['customerLastName'] = $customerfirst;
    $ro['customerEmail'] = $email;
    $ro['customerPhones'] = $phones;
    $ro['vehicleId'] = $vid;
    $ro['vehicleMake'] = $vehmake;
    $ro['vehicleModel'] = $vehmodel;
    $ro['vehicleYear'] = (int) ($vehyear);
    $ro['vehiclePlate'] = $vehlicense;
    $ro['vehicleVin'] = $Vin;
    $ro['vehicleMileage'] = (int) ($currmileage);
    $ro['vehicleMileageUnit'] = 'miles';
    $ro['dateCreated'] = strtotime(date('Y-m-d'));

    $ar['shopBossShopId'] = (int) ($shopid);
    $ar['rssDviShopApiKey'] = $apikey;
    $ar['repairOrder'] = $ro;


    $json = json_encode($ar);
    $json = str_replace("\n", "", str_replace("\r", "", $json));

    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, "https://www.repairshopsolutions.com/RssConnector/ShopBoss");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
    curl_setopt($ch, CURLOPT_POST, 1);

    $headers = array("Content-Type: application/json", "Authorization: Bearer NvutpxYUv6z1aq0jwLQzuwROrq53R4XafMcA3");
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $json_result = curl_exec($ch);
    curl_close($ch);
}

$stmt = "delete from vinscan where vin = '$Vin'";
//echo $stmt;
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $conn->commit();
    $query->close();
} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

mysqli_close($conn);
