<?php
require_once CONN;
require(INTEGRATIONS_PATH."/twilio/autoload.php");
use Twilio\Rest\Client;

$shopid = $oshopid = isset($_COOKIE['shopid']) ? filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING) : "";
$quoteid = isset($_REQUEST['quoteid']) ? filter_var($_REQUEST['quoteid'], FILTER_SANITIZE_STRING) : "";
$type = isset($_REQUEST['t']) ? filter_var($_REQUEST['t'], FILTER_SANITIZE_STRING) : "";
if(in_array($shopid, array('13445','22957'))) $oshopid = '22865';

function reCalculate()
{
    global $conn,$shopid,$quoteid;

    $stmt = "select readonly,feesonquote,alldatausername,newpackagetype,carfaxlocation,datestarted, defaulttaxrate, defaultlabortaxrate, defaultsublettaxrate,userfee1max,userfee2max,userfee3max,hst,pst,gst,qst,chargehst,chargepst,chargegst,chargeqst,userfee1taxable,userfee2taxable,userfee3taxable,userfee1applyon,userfee2applyon,userfee3applyon,updateinvonadd from company where shopid = ?";
    if ($query = $conn->prepare($stmt)) 
    {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->store_result();
        $query->bind_result($readonly, $feesonquote, $alldatausername, $newpackagetype,$cfl,$datestarted, $defaulttaxrate, $defaultlabortaxrate, $defaultsublettaxrate, $userfee1max, $userfee2max, $userfee3max,$hst,$pst,$gst,$qst,$chargehst,$chargepst,$chargegst,$chargeqst,$userfee1taxable,$userfee2taxable,$userfee3taxable,$userfee1applyon,$userfee2applyon,$userfee3applyon,$updateinvonadd);
        $query->fetch();
        $feesonquote = strtolower($feesonquote);
        $userfee1max = doubleval($userfee1max);
        $userfee2max = doubleval($userfee2max);
        $userfee3max = doubleval($userfee3max);
        $query->close();
    }
    $feesonquote = strtolower($feesonquote);

    $stmt = "select quotedate,customer,address,city,state,zip,phone,email,year,make,model,color,vin,notes,writer,fee1label,fee2label,fee3label,fee1amount,fee2amount,fee3amount,fee1percent,fee2percent,fee3percent,notes,cid,vid,roid,totalfees,salestax,totalparts,totallabor,totalsublet from quotes where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $quoteid);
        $query->execute();
        $query->bind_result($quotedate,$customer,$address,$city,$state,$zip,$phone,$email,$year,$make,$model,$color,$vin,$notes,$writer,$fee1label,$fee2label,$fee3label,$fee1amount,$fee2amount,$fee3amount,$fee1percent,$fee2percent,$fee3percent,$notes,$cid,$vid,$quoteroid,$totalfees,$salestax,$totalparts,$totallabor,$totalsublet);
        $query->fetch();
        $query->close();
    }

    if(!empty($cid))
    {
    $cstmt = "select taxexempt from customer where shopid = ? and customerid = ?";
    if ($cquery = $conn->prepare($cstmt)) 
     {
        $cquery->bind_param("si", $shopid,$cid);
        $cquery->execute();
        $cquery->store_result();
        $cquery->bind_result($taxexempt);
        $cquery->fetch();
        if ($cquery->num_rows < 1) {
            $taxable = "yes";
        } else {
            if (strtolower($taxexempt) == "yes") {
                $taxable = "no";
            } else {
                $taxable = "yes";
            }
        }
        $cquery->close();
     }
    }
    else
    $taxable = "yes";

    $taxablefees = 0;

    $salestax = 0;

    $laborstmt = "select coalesce(round(sum(extlabor),2),0) tlabor, COALESCE(SUM(if(upper(taxable)='YES',extlabor,0)),0) from quotelabor where shopid = '$shopid' and quoteid = $quoteid";
    if ($lquery = $conn->prepare($laborstmt)){
        $lquery->execute();
        $lquery->store_result();
        $lquery->bind_result($totallabor,$taxablelabor);
        $lquery->fetch();
    }

    $partstmt = "select coalesce(round(sum(extprice),2),0) tprts ,COALESCE(SUM(if(taxable='yes',extprice,0)),0) from quoteparts where shopid = '$shopid' and quoteid = $quoteid";
    if ($pquery = $conn->prepare($partstmt)){
        $pquery->execute();
        $pquery->store_result();
        $pquery->bind_result($totalparts,$taxableparts);
        $pquery->fetch();
    }


    $substmt = "select coalesce(round(sum(price),2),0) from quotesublet where shopid = '$shopid' and quoteid = $quoteid";
    if ($squery = $conn->prepare($substmt)){
        $squery->execute();
        $squery->store_result();
        $squery->bind_result($totalsublet);
        $squery->fetch();
    }

    if ($feesonquote == "yes") 
    {  
        if ($fee1percent > 0) {
            if ($userfee1applyon == "all") {
                $fee1amount = ($totalparts + $totallabor + $totalsublet) * ($fee1percent / 100);
            } else {
                $fee1amount = 0;
                if (stripos($userfee1applyon,"labor") !== false)
                    $fee1amount += $totallabor * ($fee1percent / 100);
                if (stripos($userfee1applyon,"parts") !== false)
                    $fee1amount += $totalparts * ($fee1percent / 100);
                if (stripos($userfee1applyon,"sublet") !== false)
                    $fee1amount += $totalsublet * ($fee1percent / 100);
            }

            $fee1max = $userfee1max;
            if ($fee1max > 0 && $fee1max < $fee1amount) {
                $fee1amount = $fee1max;
            }
            if (($totalparts + $totallabor + $totalsublet) == 0) {
                $fee1amount = 0;
            }
        }

        if ($fee2percent > 0) {
            if ($userfee2applyon == "all") {
                $fee2amount = ($totalparts + $totallabor + $totalsublet) * ($fee2percent / 100);
            } else {
                $fee2amount = 0;
                if (stripos($userfee2applyon, "labor") !== false)
                    $fee2amount += $totallabor * ($fee2percent / 100);
                if (stripos($userfee2applyon,"parts") !== false)
                    $fee2amount += $totalparts * ($fee2percent / 100);
                if (stripos($userfee2applyon,"sublet") !== false)
                    $fee2amount += $totalsublet * ($fee2percent / 100);
            }

            $fee2max = $userfee2max;
            if ($fee2max > 0 && $fee2max < $fee2amount) {
                $fee2amount = $fee2max;
            }
            if (($totalparts + $totallabor) == 0) {
                $fee2amount = 0;
            }
        }

        if ($fee3percent > 0) {
            if ($userfee3applyon == "all") {
                $fee3amount = ($totalparts + $totallabor + $totalsublet) * ($fee3percent / 100);
            } else {
                $fee3amount = 0;
                if (stripos($userfee3applyon, "labor") !== false)
            $fee3amount = $totallabor * ($fee3percent / 100);
                if (stripos($userfee3applyon, "parts") !== false)
            $fee3amount = $totalparts * ($fee3percent / 100);
                if (stripos($userfee3applyon,"sublet") !== false)
                    $fee3amount = $totalsublet * ($fee3percent / 100);
            }
        }

        $totalfees = $fee1amount + $fee2amount + $fee3amount;

    } else {
        $fee1amount = $fee2amount = $fee3amount = 0;
        $fee1percent = $fee2percent = $fee3percent = 0;
        $totalfees = 0;
    }

    if($taxable=='yes')
    {
        if($userfee1taxable=='Taxable') $taxablefees += $fee1amount;
        if($userfee2taxable=='Taxable') $taxablefees += $fee2amount;
        if($userfee3taxable=='Taxable') $taxablefees += $fee3amount;

        $ptax = $ltax = $stax = $cantax = 0;

        if($chargehst == "yes" && $hst > 0 ) $cantax += $hst;
        if($chargegst == "yes" && $gst > 0 ) $cantax += $gst;
        if($chargepst == "yes" && $pst > 0 ) $cantax += $pst;
        if($chargeqst == "yes" && $qst > 0 ) $cantax += $qst;

        if($cantax>0 && $defaulttaxrate==0 && $defaultlabortaxrate==0 && $defaultsublettaxrate==0)
        {
            $defaulttaxrate = $defaultlabortaxrate = $defaultsublettaxrate = $cantax;
        }

        if ($defaulttaxrate > 0) {
            $ptax = round(($taxableparts + $taxablefees) * ($defaulttaxrate / 100), 2);
        }
        if ($defaultlabortaxrate > 0) {
            $ltax = round($taxablelabor * ($defaultlabortaxrate / 100), 2);
        }
        if ($defaultsublettaxrate > 0) {
            $stax = round($totalsublet * ($defaultsublettaxrate / 100), 2);
        }

        $salestax = round($ptax + $ltax + $stax,2);
    }

    $fee1amount = round($fee1amount,2);
    $fee2amount = round($fee2amount,2);
    $fee3amount = round($fee3amount,2);
        
    $subtotal = sbpround($totallabor+$totalparts+$totalsublet+$totalfees,2);
    $totalquote = round($subtotal + $salestax,2);

    $stmt = "update quotes set totalparts = ?,totallabor = ?,totalsublet = ?,totalfees = ?,salestax = ?,totalquote = ?,fee1amount = ?,fee2amount = ?,fee3amount = ? where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)){
        $query->bind_param("dddddddddsi", $totalparts, $totallabor, $totalsublet, $totalfees, $salestax, $totalquote, $fee1amount, $fee2amount, $fee3amount, $shopid, $quoteid);
        $query->execute();
        $conn->commit();
        $query->close();
    }

    echo("success");

}

if($type=='createquote')
{
    $cid = isset($_REQUEST['cid']) ? filter_var($_REQUEST['cid'], FILTER_SANITIZE_STRING) : "";
    $vid = isset($_REQUEST['vid']) ? filter_var($_REQUEST['vid'], FILTER_SANITIZE_STRING) : "";
    $customer = isset($_REQUEST['customer']) ? filter_var(strtoupper($_REQUEST['customer']), FILTER_SANITIZE_STRING) : "";
    $address = isset($_REQUEST['address']) ? filter_var(strtoupper($_REQUEST['address']), FILTER_SANITIZE_STRING) : "";
    $city = isset($_REQUEST['city']) ? filter_var(strtoupper($_REQUEST['city']), FILTER_SANITIZE_STRING) : "";
    $state = isset($_REQUEST['state']) ? filter_var(strtoupper($_REQUEST['state']), FILTER_SANITIZE_STRING) : "";
    $zip = isset($_REQUEST['zip']) ? filter_var(strtoupper($_REQUEST['zip']), FILTER_SANITIZE_STRING) : "";
    $phone = isset($_REQUEST['phone']) ? filter_var(strtoupper($_REQUEST['phone']), FILTER_SANITIZE_STRING) : "";
    $email = isset($_REQUEST['email']) ? filter_var($_REQUEST['email'], FILTER_SANITIZE_EMAIL) : "";
    $year = isset($_REQUEST['year']) ? filter_var(strtoupper($_REQUEST['year']), FILTER_SANITIZE_STRING) : "";
    $make = isset($_REQUEST['make']) ? filter_var(strtoupper($_REQUEST['make']), FILTER_SANITIZE_STRING) : "";
    $model = isset($_REQUEST['model']) ? filter_var(strtoupper($_REQUEST['model']), FILTER_SANITIZE_STRING) : "";
    $color = isset($_REQUEST['color']) ? filter_var(strtoupper($_REQUEST['color']), FILTER_SANITIZE_STRING) : "";
    $vin = isset($_REQUEST['vin']) ? filter_var(strtoupper($_REQUEST['vin']), FILTER_SANITIZE_STRING) : "";
    $notes = isset($_REQUEST['comments']) ? filter_var(strtoupper($_REQUEST['comments']), FILTER_SANITIZE_STRING) : "";
    $writer = isset($_REQUEST['writer']) ? filter_var(strtoupper($_REQUEST['writer']), FILTER_SANITIZE_STRING) : "";
    $date = date('Y-m-d');

    $fee1percent = $fee2percent = $fee3percent = 0;
    $fee1amount = $fee2amount = $fee3amount = 0;
    $defaulttaxrate = $defaultlabortaxrate = 0;
    $newquoteid = 101;

    $stmt = "select * from company where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $results = $query->get_result();
        $rs = $results->fetch_assoc();
        $feesonquote = strtolower($rs['feesonquote']);
        $partstaxrate = $rs['DefaultTaxRate'];
        $labortaxrate = $rs['DefaultLaborTaxRate'];
        $sublettaxrate = $rs['DefaultSubletTaxRate'];
        $hazwaste = $rs['HazardousWaste'];

        if ($feesonquote == "yes") {
            $fee1name = strtoupper($rs['UserFee1']);
            $fee2name = strtoupper($rs['UserFee2']);
            $fee3name = strtoupper($rs['UserFee3']);

            $fee1type = $rs['userfee1type'];
            $fee2type = $rs['userfee2type'];
            $fee3type = $rs['userfee3type'];

            $fee1amount = doubleval($rs["UserFee1amount"]);
            $fee2amount = doubleval($rs["UserFee2amount"]);
            $fee3amount = doubleval($rs["UserFee3amount"]);

            if ($fee1type == "%") {
                $fee1percent = $fee1amount;
                $fee1amount = 0;
            } else {
                $fee1percent = 0;
            }
            if ($fee2type == "%") {
                $fee2percent = $fee2amount;
                $fee2amount = 0;
            } else {
                $fee2percent = 0;
            }
            if ($fee3type == "%") {
                $fee3percent = $fee3amount;
                $fee3amount = 0;
            } else {
                $fee3percent = 0;
            }
        }

      $query->close();
    } 

    $canadiantax = '';

    if(!is_numeric($rs['CompanyZip']))
    {
        $canadiantax = $rs['hst'].','.$rs['pst'].','.$rs['gst'].','.$rs['qst'];

        if($rs['chargehst'] == "yes" && $rs['hst'] > 0 ) $cantax += $rs['hst'];
        if($rs['chargegst'] == "yes" && $rs['gst'] > 0 ) $cantax += $rs['gst'];
        if($rs['chargepst'] == "yes" && $rs['pst'] > 0 ) $cantax += $rs['pst'];
        if($rs['chargeqst'] == "yes" && $rs['qst'] > 0 ) $cantax += $rs['qst'];

        $partstaxrate = $labortaxrate = $sublettaxrate = $cantax;

    }


    $stmt = "select * from quotes where shopid = ? order by id desc limit 1";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $results = $query->get_result();
        if($results->num_rows>0)
        {
        $rs = $results->fetch_assoc();
        $newquoteid = $rs['id'] + 1;
        }
        $query->close();
    } else {
        echo "quots prepare failed: (" . $conn->errno . ") " . $conn->error;

    }

    $stmt = "insert into quotes (shopid,id,quotedate,fee1label, fee2label, fee3label, fee1percent, fee2percent, fee3percent, fee1amount,fee2amount,fee3amount,hazardouswaste,vin, customer, address, cid, vid, city, state, zip, phone, `year`, make, model, color, email, writer,notes,partstaxrate,labortaxrate,sublettaxrate,canadiantax) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("sissssdddddddsssiisssssssssssddds", $shopid, $newquoteid, $date, $fee1name, $fee2name, $fee3name, $fee1percent, $fee2percent, $fee3percent, $fee1amount,$fee2amount,$fee3amount,$hazwaste,$vin,$customer,$address,$cid,$vid,$city,$state,$zip,$phone,$year,$make,$model,$color,$email,$writer,$notes,$partstaxrate,$labortaxrate,$sublettaxrate,$canadiantax);
        $query->execute();
        $conn->commit();
        $query->close();
        echo($newquoteid);
    } 
    else
    echo "Quote creation failed: (" . $conn->error . ")";    
}


elseif($type=='addpart')
{
    $comid = filter_var($_REQUEST['comid'], FILTER_SANITIZE_STRING);
    $partnumber = filter_var($_REQUEST['PartNumber'], FILTER_SANITIZE_STRING);
    $description = filter_var($_REQUEST['PartDesc'], FILTER_SANITIZE_STRING);
    $category = filter_var($_REQUEST['PartCategory'], FILTER_SANITIZE_STRING);
    $override = filter_var($_REQUEST['overridematrix'], FILTER_SANITIZE_STRING);
    $quantity = filter_var($_REQUEST['qty'], FILTER_SANITIZE_STRING);
    $cost = filter_var($_REQUEST['PartCost'], FILTER_SANITIZE_STRING);
    $price = filter_var($_REQUEST['PartPrice'], FILTER_SANITIZE_STRING);
    $discount = filter_var($_REQUEST['Discount'], FILTER_SANITIZE_STRING);
    $net = filter_var($_REQUEST['net'], FILTER_SANITIZE_STRING);
    $partcode = filter_var($_REQUEST['PartCode'], FILTER_SANITIZE_STRING);
    $supplier = filter_var($_REQUEST['PartSupplier'], FILTER_SANITIZE_STRING);
    $bin = filter_var($_REQUEST['bin'], FILTER_SANITIZE_STRING);
    $extprice = filter_var($_REQUEST['extprice'], FILTER_SANITIZE_STRING);
    $taxable = filter_var($_REQUEST['tax'], FILTER_SANITIZE_STRING);
    $addmore = $_REQUEST['addmore']??'no';


    $stmt = "insert into quoteparts (shopid, quoteid, complaintid, partnumber, part, price, qty, net, discount, extprice, partcost, partcode, supplier, bin, matrixcat, taxable,overridematrix) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("siissddddddssssss",$shopid,$quoteid,$comid,$partnumber,$description,$price,$quantity,$net,$discount,$extprice,$cost,$partcode,$supplier,$bin,$category,$taxable,$override);
        $query->execute();
        $conn->commit();
        $query->close();
    }
    else
    echo "Quote creation failed: (" . $conn->error . ")"; 

    if($addmore=='no')
    echo("success");
    else
    reCalculate();
}

elseif($type=='savepart')
{
    $id = filter_var($_REQUEST['quotepartid'], FILTER_SANITIZE_STRING);
    $partnumber = filter_var($_REQUEST['PartNumber'], FILTER_SANITIZE_STRING);
    $description = filter_var($_REQUEST['PartDesc'], FILTER_SANITIZE_STRING);
    $category = filter_var($_REQUEST['PartCategory'], FILTER_SANITIZE_STRING);
    $override = filter_var($_REQUEST['overridematrix'], FILTER_SANITIZE_STRING);
    $quantity = filter_var($_REQUEST['qty'], FILTER_SANITIZE_STRING);
    $cost = filter_var($_REQUEST['PartCost'], FILTER_SANITIZE_STRING);
    $price = filter_var($_REQUEST['PartPrice'], FILTER_SANITIZE_STRING);
    $discount = filter_var($_REQUEST['Discount'], FILTER_SANITIZE_STRING);
    $net = filter_var($_REQUEST['net'], FILTER_SANITIZE_STRING);
    $partcode = filter_var($_REQUEST['PartCode'], FILTER_SANITIZE_STRING);
    $supplier = filter_var($_REQUEST['PartSupplier'], FILTER_SANITIZE_STRING);
    $bin = filter_var($_REQUEST['bin'], FILTER_SANITIZE_STRING);
    $extprice = filter_var($_REQUEST['extprice'], FILTER_SANITIZE_STRING);
    $taxable = filter_var($_REQUEST['tax'], FILTER_SANITIZE_STRING);


    $stmt = "update quoteparts set partnumber = ?, part = ?, price = ?, qty = ?, net = ?, discount = ?, extprice = ?, partcost = ?, partcode = ?, supplier = ?, bin = ?, matrixcat = ?, taxable = ?,overridematrix = ? where shopid = ? and quoteid = ? and id = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("ssddddddsssssssii",$partnumber,$description,$price,$quantity,$net,$discount,$extprice,$cost,$partcode,$supplier,$bin,$category,$taxable,$override,$shopid, $quoteid, $id);
        $query->execute();
        $conn->commit();
        $query->close();
    }
    else
    echo "Quote update failed: (" . $conn->error . ")"; 
    
    echo("success");
}

elseif($type=='addlabor')
{
    $comid = filter_var($_REQUEST['comid'], FILTER_SANITIZE_STRING);
    $labor = isset($_REQUEST['labor']) ? filter_var($_REQUEST['labor'], FILTER_SANITIZE_STRING) : "";
    $hours = isset($_REQUEST['hours']) ? filter_var($_REQUEST['hours'], FILTER_SANITIZE_STRING) : 0;
    $rate = isset($_REQUEST['rate']) ? filter_var($_REQUEST['rate'], FILTER_SANITIZE_STRING) : 0;
    $calcrate = isset($_REQUEST['calcrate']) ? filter_var($_REQUEST['calcrate'], FILTER_SANITIZE_STRING) : 0;
    $labormatrix = isset($_REQUEST['labormatrix']) ? filter_var($_REQUEST['labormatrix'], FILTER_SANITIZE_STRING) : "";
    $tech = isset($_REQUEST['tech']) ? filter_var($_REQUEST['tech'], FILTER_SANITIZE_STRING) : "";



    $stmt = "insert into quotelabor (shopid,complaintid,quoteid,labor,tech,hours,rate,extlabor,matrix) values (?,?,?,?,?,?,?,?,?)";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("siissddds",$shopid, $comid, $quoteid, $labor, $tech, $hours, $rate, $calcrate, $labormatrix);
        $query->execute();
        $conn->commit();
        $query->close();
    }
    else
    echo "Quote creation failed: (" . $conn->error . ")"; 

    echo("success");
}

elseif($type=='editlabor')
{
    $laborid = filter_var($_REQUEST['laborid'], FILTER_SANITIZE_STRING);
    $labor = isset($_REQUEST['labor']) ? filter_var($_REQUEST['labor'], FILTER_SANITIZE_STRING) : "";
    $hours = isset($_REQUEST['hours']) ? filter_var($_REQUEST['hours'], FILTER_SANITIZE_STRING) : 0;
    $rate = isset($_REQUEST['rate']) ? filter_var($_REQUEST['rate'], FILTER_SANITIZE_STRING) : 0;
    $labormatrix = isset($_REQUEST['labormatrix']) ? filter_var($_REQUEST['labormatrix'], FILTER_SANITIZE_STRING) : "";
    $tech = isset($_REQUEST['tech']) ? filter_var($_REQUEST['tech'], FILTER_SANITIZE_STRING) : "";
    $calcrate = isset($_REQUEST['calcrate']) ? filter_var($_REQUEST['calcrate'], FILTER_SANITIZE_STRING) : 0;




    $stmt = "update quotelabor set labor = ?,tech = ?,hours = ?,rate = ?,extlabor = ?,matrix = ? where shopid = ? and quoteid = ? and id = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("ssdddssii",$labor, $tech, $hours, $rate, $calcrate, $labormatrix,$shopid, $quoteid, $laborid);
        $query->execute();
        $conn->commit();
        $query->close();
    }
    else
    echo "Quote update failed: (" . $conn->error . ")"; 

    echo("success");
}

elseif($type=='addsublet')
{
    $comid = filter_var($_REQUEST['comid'], FILTER_SANITIZE_STRING);
    $sublet = isset($_REQUEST['sublet']) ? filter_var($_REQUEST['sublet'], FILTER_SANITIZE_STRING) : "";
    $invnum = isset($_REQUEST['invnum']) ? filter_var($_REQUEST['invnum'], FILTER_SANITIZE_STRING) : "";
    $supplier = isset($_REQUEST['supplier']) ? filter_var($_REQUEST['supplier'], FILTER_SANITIZE_STRING) : "";
    $cost = isset($_REQUEST['cost']) ? filter_var($_REQUEST['cost'], FILTER_SANITIZE_STRING) : 0;
    $price = isset($_REQUEST['price']) ? filter_var($_REQUEST['price'], FILTER_SANITIZE_STRING) : 0;



    $stmt = "insert into quotesublet (shopid,quoteid,complaintid,description,cost,price,invnum,supplier) values (?,?,?,?,?,?,?,?)";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("siisddss", $shopid, $quoteid, $comid, $sublet, $cost, $price, $invnum, $supplier);
        $query->execute();
        $conn->commit();
        $query->close();
    }
    else
    echo "Quote creation failed: (" . $conn->error . ")"; 

    echo("success");
}

elseif($type=='editsublet')
{
    $subletid = filter_var($_REQUEST['subletid'], FILTER_SANITIZE_STRING);
    $sublet = isset($_REQUEST['sublet']) ? filter_var($_REQUEST['sublet'], FILTER_SANITIZE_STRING) : "";
    $invnum = isset($_REQUEST['invnum']) ? filter_var($_REQUEST['invnum'], FILTER_SANITIZE_STRING) : "";
    $supplier = isset($_REQUEST['supplier']) ? filter_var($_REQUEST['supplier'], FILTER_SANITIZE_STRING) : "";
    $cost = isset($_REQUEST['cost']) ? filter_var($_REQUEST['cost'], FILTER_SANITIZE_STRING) : 0;
    $price = isset($_REQUEST['price']) ? filter_var($_REQUEST['price'], FILTER_SANITIZE_STRING) : 0;


    $stmt = "update quotesublet set description = ?,cost = ?,price = ?,invnum = ?,supplier = ? where shopid = ? and quoteid = ? and id = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("sddsssii",$sublet, $cost, $price, $invnum, $supplier, $shopid,$quoteid,$subletid );
        $query->execute();
        $conn->commit();
        $query->close();
    }
    else
    echo "Quote update failed: (" . $conn->error . ")"; 

    echo("success");
}

elseif($type=='deleteitem')
{
    $id = filter_var($_REQUEST['id'], FILTER_SANITIZE_STRING);
    $type = isset($_REQUEST['type']) ? filter_var($_REQUEST['type'], FILTER_SANITIZE_STRING) : "";
    
    if($type=='PART')$deltable = 'quoteparts';
    elseif($type=='LABOR')$deltable = 'quotelabor';
    elseif($type=='SUBLET')$deltable = 'quotesublet';
    else die();

    $stmt = "delete from $deltable where shopid = ? and quoteid = ? and id = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("sii",$shopid,$quoteid,$id );
        $query->execute();
        $conn->commit();
        $query->close();
    }
    else
    die("Quote delete failed: (" . $conn->error . ")"); 

    echo("success");
}

elseif($type=='addcannedjob')
{
    $comid = filter_var($_REQUEST['comid'], FILTER_SANITIZE_STRING);
    $cannedjob = isset($_REQUEST['cannedjob']) ? filter_var($_REQUEST['cannedjob'], FILTER_SANITIZE_STRING) : "";
    $hourlyrate = isset($_REQUEST['hourlyrate']) ? filter_var($_REQUEST['hourlyrate'], FILTER_SANITIZE_STRING) : "";
    $tech = isset($_REQUEST['tech']) ? filter_var($_REQUEST['tech'], FILTER_SANITIZE_STRING) : "";
    
    
    $isFlatPrice = "no";
    $stmt = "select * from cannedjobs where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $cannedjob);
        $query->execute();
        $results = $query->get_result();
        $rs = $results->fetch_assoc();
        $jobName = $rs['jobname'];
        $cannedtaxable = $rs['taxable'];
        if (doubleval($rs['flatprice']) > 0) {
            $isFlatPrice = "yes";
            $flatPrice = doubleval($rs['flatprice']);
            $stmt = "insert into quoteparts (shopid,quoteid,complaintid,partnumber,part,price,qty,extprice,taxable,cannedjobsid) values (?,?,?,'JOB',?,?,1,?,?,?)";
            if ($qpquery = $conn->prepare($stmt)) {
                $qpquery->bind_param("siisddsi", $shopid, $quoteid, $comid, $jobName, $flatPrice, $flatPrice, $cannedtaxable, $cannedjob);
                if ($qpquery->execute()) {
                    $conn->commit();
                } else {
                    echo $conn->errno;
                }
                $qpquery->close();
            } else {
                echo "quotepart prepare failed: (" . $conn->errno . ") " . $conn->error;
            }
        }
        $query->close();
    } else {
        echo "cannedjob prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    $hourlyrate = number_format($hourlyrate, 2);

    $stmt = "select * from cannedlabor where shopid = ? and cannedjobsid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $cannedjob);
        $query->execute();
        $results = $query->get_result();
        while ($rs = $results->fetch_assoc()) {
            $qlstmt = sprintf("INSERT into quotelabor (shopid,quoteid,complaintid,rate,hours,tech,labor,extlabor,taxable,cannedjobsid) values ('%s',%d,%d,%f,%f", $shopid, $quoteid, $comid, $hourlyrate, $rs['laborhours'],$tech);
            $qlstmt .= sprintf(",'%s'", $tech);
            if ($isFlatPrice == "no") {
                $qlstmt .= sprintf(",'%s' ,", $rs['labor']);
            } else {
                $included = "";
                if (!empty($jobName)) {
                    $included = " (INCLUDED IN " . strtoupper($jobName) . ")";
                }
                $qlstmt .= sprintf(",'%s',", $rs['labor'] . $included);
            }
            if ($isFlatPrice == "yes") {
                $qlstmt .= "0,";
            } else {
                if ($rs['flatprice'] > 0 || $rs['nocalc'] == '1')
                    $amount_billed = $rs['flatprice'];
                else
                    $amount_billed = round($hourlyrate * $rs['laborhours'],2);
                $qlstmt .= sprintf("%f,", $amount_billed);
            }

            $qlstmt .= sprintf("'%s',", $cannedtaxable);
            $qlstmt .= sprintf("'%s')", $cannedjob);


            if ($qlquery = $conn->prepare($qlstmt)) {
                if ($qlquery->execute()) {
                    $conn->commit();
                } else {
                    echo $conn->errno;
                }
            } else {
                echo $qlstmt . "\n";
                echo "inst quote Labor prepare failed: (" . $conn->errno . ") " . $conn->error;
            }
        }
        $query->close();
    } else {
        echo "canned Labor prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    $stmt = sprintf("select * from cannedparts where shopid = '%s' and cannedjobsid = %d", $shopid, $cannedjob);
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $results = $query->get_result();
        while ($rs = $results->fetch_assoc()) {
            $q = $rs['qty'];
            $nstmt = sprintf("insert into quoteparts (overridematrix,shopid,quoteid,complaintid,partnumber,part,price,qty,partcost, partcode, supplier, bin,
matrixcat,extprice,taxable,cannedjobsid) values ('%s', '%s', %d, %d, '%s','%s',%f,%f,%f,'%s','%s','%s','%s'", $rs['overridematrix'],$shopid, $quoteid, $comid, $rs['partnumber'], $rs['partdescription'], $rs['partprice'], $rs['qty'], $rs['partcost'], $rs['partcode'], $rs['supplier'], $rs['bin'], $rs['partcategory']);

            if ($isFlatPrice == "no") {
                $prod = doubleval($rs["partprice"]) * doubleval($rs["qty"]);
                $nstmt .= sprintf(",%f", $prod);
            } else {
                $nstmt .= ",0";
            }

            $nstmt .= sprintf(", '%s'", $rs['tax']);
            $nstmt .= sprintf(", '%s')", $cannedjob);
            if ($qpquery = $conn->prepare($nstmt)) {
                if ($qpquery->execute()) {
                    $conn->commit();
                } else {
                    echo $conn->errno;
                }
                $qpquery->close();
            } else {
                echo "quote prts prepare failed: (" . $conn->errno . ") " . $conn->error;
            }
        }
        $query->close();
    } else {
        echo "canned prts prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    $stmt = sprintf("select * from cannedsublet where shopid = '%s' and cannedjobsid = %d", $shopid, $cannedjob);
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $result = $query->get_result();
        $query->store_result();
        while ($row = $result->fetch_assoc()) {

            $stmt = "insert into quotesublet (shopid,quoteid,complaintid,description,cost,price,supplier) values (?,?,?,?,?,?,?)";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("siisdds", $shopid, $quoteid, $comid, $row['subletdesc'], $row['subletcost'], $row['subletprice'], $row['subletsupplier']);
                if ($query->execute()) {
                    $conn->commit();
                } else {
                    echo $conn->errno;
                }
                $query->close();
            }
        }
    } else {
        echo "labor stmt error" . $conn->errno;
    }

    echo("success");
}

elseif($type=='writer')
{
    $writer = filter_var($_REQUEST['w'], FILTER_SANITIZE_STRING);

    $stmt = "update quotes set writer = ? where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("ssi", $writer,$shopid,$quoteid);
        $query->execute();
        $conn->commit();
        $query->close();
    }
    else
    die("Quote writer failed: (" . $conn->error . ")"); 

    echo("success");
}

elseif($type=='comments')
{
    $notes = filter_var($_REQUEST['c'], FILTER_SANITIZE_STRING);

    $stmt = "update quotes set notes = ? where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("ssi", $notes,$shopid,$quoteid);
        $query->execute();
        $conn->commit();
        $query->close();
    }
    else
    die("Quote comments failed: (" . $conn->error . ")"); 
}

elseif($type=='savequote')
{
    $cid = isset($_REQUEST['cid']) ? filter_var($_REQUEST['cid'], FILTER_SANITIZE_STRING) : "";
    $vid = isset($_REQUEST['vid']) ? filter_var($_REQUEST['vid'], FILTER_SANITIZE_STRING) : "";
    $customer = isset($_REQUEST['customer']) ? filter_var(strtoupper($_REQUEST['customer']), FILTER_SANITIZE_STRING) : "";
    $address = isset($_REQUEST['address']) ? filter_var(strtoupper($_REQUEST['address']), FILTER_SANITIZE_STRING) : "";
    $city = isset($_REQUEST['city']) ? filter_var(strtoupper($_REQUEST['city']), FILTER_SANITIZE_STRING) : "";
    $state = isset($_REQUEST['state']) ? filter_var(strtoupper($_REQUEST['state']), FILTER_SANITIZE_STRING) : "";
    $zip = isset($_REQUEST['zip']) ? filter_var(strtoupper($_REQUEST['zip']), FILTER_SANITIZE_STRING) : "";
    $phone = isset($_REQUEST['phone']) ? filter_var(strtoupper($_REQUEST['phone']), FILTER_SANITIZE_STRING) : "";
    $email = isset($_REQUEST['email']) ? filter_var($_REQUEST['email'], FILTER_SANITIZE_EMAIL) : "";
    $year = isset($_REQUEST['year']) ? filter_var(strtoupper($_REQUEST['year']), FILTER_SANITIZE_STRING) : "";
    $make = isset($_REQUEST['make']) ? filter_var(strtoupper($_REQUEST['make']), FILTER_SANITIZE_STRING) : "";
    $model = isset($_REQUEST['model']) ? filter_var(strtoupper($_REQUEST['model']), FILTER_SANITIZE_STRING) : "";
    $color = isset($_REQUEST['color']) ? filter_var(strtoupper($_REQUEST['color']), FILTER_SANITIZE_STRING) : "";
    $vin = isset($_REQUEST['vin']) ? filter_var(strtoupper($_REQUEST['vin']), FILTER_SANITIZE_STRING) : "";

    $stmt = "update quotes set vin = ?, customer = ?, address = ?, cid = ?, vid = ?, city = ?, state = ?, zip = ?, phone = ?, `year` = ?, make = ?, model = ?, color = ?, email = ? where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("sssiissssssssssi", $vin,$customer,$address,$cid,$vid,$city,$state,$zip,$phone,$year,$make,$model,$color,$email,$shopid, $quoteid);
        $query->execute();
        $conn->commit();
        $query->close();
        echo("success"); 
    } 
    else
    die("Quote update failed: (" . $conn->error . ")");
}

elseif($type=='addissuestoro')
{
  $roid = $_REQUEST['roid'];
  $tempdate = date("Y-m-d");

  $stmt = "select coalesce(complaintid,0) from complaints where shopid = ? order by complaintid desc,roid desc limit 1";
  if ($query = $conn->prepare($stmt))
  {
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->store_result();
    $num_rows = $query->num_rows;
    if ($num_rows > 0){
        $query->bind_result($complaintid);
        $query->fetch();
    }else{
        $complaintid = 1000;
    }
    $query->close();
  }

  $newcomid = $complaintid + 1;
  $lastdisplayorder = 1;


  $stmt = "select * from quotecomplaints where shopid = ? and quoteid = ? and cstatus = 'no' order by displayorder asc";
  if ($query = $conn->prepare($stmt))
  {
    $query->bind_param("si",$shopid,$quoteid);
    $query->execute();
    $comresult = $query->get_result();
    while($comrow = $comresult->fetch_assoc()) 
    {   
        $quotecomid = $comrow['complaintid'];

        $stmt = "insert into complaints (shopid,roid,complaint,complaintid,issue,displayorder) values (?,?,?,?,?,?)";
        if ($query = $conn->prepare($stmt))
        {
            $query->bind_param("sisisi",$shopid,$roid,$comrow['complaint'],$newcomid,$comrow['issue'],$lastdisplayorder);
            $query->execute();
            $conn->commit();
            $query->close();
        }

        $stmt = "insert into parts (shopid,PartNumber,PartDesc,PartPrice,Quantity,ROID,Supplier,Cost,PartInvoiceNumber,PartCode,LineTTLPrice,LineTTLCost,Date,PartCategory,complaintid,discount"
            . ",net,bin,tax,overridematrix,ponumber,pstatus,deleted,cannedjobsid) select shopid,partnumber,part,price,qty,$roid,supplier,partcost,'',partcode,extprice,partcost*qty,'$tempdate',matrixcat,$newcomid,discount,net,bin,taxable,overridematrix,'','','no',cannedjobsid from quoteparts where shopid = '$shopid' and quoteid = '$quoteid' and complaintid='$quotecomid'";
        if ($query = $conn->prepare($stmt))
        {
            $query->execute();
            $conn->commit();
            $query->close();
        }

        if(strtoupper($_REQUEST['updateinv'])=='YES')
        {

          $stmt = "select qty,partnumber from quoteparts where shopid = ? and quoteid = ? and complaintid = ?";
          if ($query = $conn->prepare($stmt))
          {
           $query->bind_param("sii",$shopid,$quoteid, $quotecomid);
           $query->execute();
           $r = $query->get_result();
           while ($rs = $r->fetch_array())
           {
             $pq = $rs['qty'];
             $stmt = "update partsinventory set onhand = onhand - $pq, netonhand = netonhand - $pq where shopid = ? and partnumber = ?";
             if ($query = $conn->prepare($stmt)){
             $query->bind_param("ss",$oshopid,$rs['partnumber']);
             $query->execute();
             $conn->commit();
             $query->close();
             }

             $stmt = "select NetOnHand,ReOrderLevel from partsinventory where shopid = ? and partnumber = ?";
             if ($query = $conn->prepare($stmt))
             {
              $query->bind_param("ss",$oshopid,$rs['partnumber']);
              $query->execute();
              $query->bind_result($netonhand,$reorderlevel);
              $query->fetch();
              $query->close();
             }

             if ($netonhand<=$reorderlevel && $netonhand!='' && $reorderlevel!='') 
             {
              $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid = ? AND s.notification_type='166'";
              if ($query = $conn->prepare($stmt)) 
              {
               $query->bind_param("s", $shopid);
               $query->execute();
               $query->store_result();
               $num_rows = $query->num_rows;
               if($num_rows>0)
               {
                $query->bind_result($textcontent, $emailcontent, $popupcontent);
                $query->fetch();

                $popupcontent = str_replace("*|PARTNUMBER|*", $rs['partnumber'], $popupcontent);
                $emailcontent = str_replace("*|PARTNUMBER|*", $rs['partnumber'], $emailcontent);
                $textcontent = str_replace("*|PARTNUMBER|*", $rs['partnumber'], $textcontent);
                $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'166',?,?,?)";
                if ($squery = $conn->prepare($stmt)) 
                {
                 $squery->bind_param("ssss", $shopid, $popupcontent, $textcontent, $emailcontent);
                 $squery->execute();
                 $conn->commit();      
                 $squery->close();
                } 
               }
              }
             }
            }
          }
        }

        $stmt = "insert into labor (shopid,roid,tech,hourlyrate,laborhours,labor,linetotal,complaintid,schedulelength,cannedjobsid) select shopid,$roid,tech,rate,hours,labor,extlabor,$newcomid,lower(taxable),cannedjobsid from quotelabor where shopid = '$shopid' and quoteid = '$quoteid' and complaintid='$quotecomid'";
        if ($query = $conn->prepare($stmt))
        {
            $query->execute();
            $conn->commit();
            $query->close();
        }

        $stmt = "select subletid from sublet where shopid = '$shopid' order by subletid desc limit 1";
        if ($query = $conn->prepare($stmt)){
            $query->execute();
            $query->store_result();
            $query->bind_result($newsubletid);
            $query->fetch();
            $query->close();
        }else{
            echo "3Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }

        $stmt = " SELECT shopid,$roid,`description`,price,cost,invnum,`supplier` from quotesublet where shopid = '$shopid' and quoteid = '$quoteid' and complaintid='$quotecomid'";

        if ($query = $conn->prepare($stmt)){
            $query->execute();
            $result = $query->get_result();
            while ($row = $result->fetch_array()){
                $newsubletid = $newsubletid + 1;
                $desc = $row["description"];
                $subprice = $row["price"];
                $subcost = $row["cost"];
                $subinvnum = $row["invnum"];
                $subsupplier = $row["supplier"];

                $stmt = "insert into sublet (shopid,SubletID,ROID,SubletDesc,SubletPrice,SubletCost,SubletInvoiceNo,SubletSupplier,complaintid) values (?,?,?,?,?,?,?,?,?)";

                if ($query = $conn->prepare($stmt))
                {
                    $query->bind_param("siisddssi",$shopid,$newsubletid,$roid,$desc,$subprice,$subcost,$subinvnum,$subsupplier,$newcomid);
                    $query->execute();
                    $conn->commit();
                    $query->close();
                }
            }
        }

        $newcomid++;
        $lastdisplayorder++;
    }
  }

    $stmt = "update quotes set roid = '$roid' where shopid = '$shopid' and id = $quoteid";
    if ($query = $conn->prepare($stmt))
    {
     $query->execute();
     $conn->commit();
     $query->close();
    }

    $stmt = "update partstechreturn ptr inner join partstechsessions pts on ptr.shopid = pts.shopid and ptr.ptsessionid = pts.sessionid set ptr.roid='$roid',pts.roid='$roid' where pts.status = 'open' and ptr.shopid = '$shopid' and ptr.roid = '$quoteid'";
    if ($query = $conn->prepare($stmt))
    {
     $query->execute();
     $conn->commit();
     $query->close();
    }

    $stmt = "update partstechreturn ptr inner join partstechsessions pts on ptr.shopid = pts.shopid and ptr.ptsessionid = pts.sessionid join parts p on ptr.shopid=p.shopid and ptr.roid=p.roid and ptr.partnumber=p.partnumber set p.orderstatus='quote' where pts.status = 'open' and ptr.shopid = '$shopid' and ptr.roid = '$roid'";
    if ($query = $conn->prepare($stmt))
    {
     $query->execute();
     $conn->commit();
     $query->close();
    }
    
    $stmt = "select writer from quotes where shopid = '$shopid' and id = $quoteid";
    if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->bind_result($writer);
    $query->fetch();
    $query->close();
    }

    $stmt = "update repairorders set writer = ? where shopid = ? and roid = ?";
    if ($query = $conn->prepare($stmt))
    {
     $query->bind_param("ssi",$writer,$shopid,$roid);
     $query->execute();
     $conn->commit();
     $query->close();
    }
}

elseif($type=='textquote')
{
    $shopname = $_COOKIE['shopname'];
    $sendsmsnum = "";
    $stmt = "select smsnum from smsnumbers where shopid = '$shopid'";
    if ($query = $conn->prepare($stmt)){
        $query->execute();
        $query->bind_result($sendsmsnum);
        $query->fetch();
        $query->close();
    }

    //$url = "https://".$_SERVER['SERVER_NAME']."/quotestatus.php?".base64_encode("quoteid=$quoteid&shopid=$shopid");

    $check=true;$ct=1;$rand='';
    while($check && $ct<=20)
    {
    $rand=generateRandomStr(5);
    $stmt = "select id from urlshort where url = ? limit 1";
    if ($query = $conn->prepare($stmt))
    {
    $query->bind_param("s",$rand);
    $query->execute();
    $query->store_result();
    $num = $query->num_rows();
    $query->close();
    if($num<1)$check=false;
    }
    $ct++;
    }

    if(empty($rand))die();

    $stmt = "Insert into `urlshort` set `shopid`=?,`roid`=?,`url`=?,`type`='quote'";
    if ($query = $conn->prepare($stmt))
    {
    $query->bind_param("sss",$shopid,$quoteid,$rand);
    $query->execute();
    $conn->commit();
    $query->close();
    }

    $url="https://sbpt.io/".$rand;

    $msg =  "Repair Quote From ".$shopname." ".$url;

    if ($sendsmsnum != ""){

        $token = "t-ewgnokfkia545y4zn4xzxdi";
        $secret = "zb2lcrqeouxma52dhgjfpqak6w5dg4tfnwvytxy";

        $from = $sendsmsnum;
        $to = $_POST['textto'];

        require(INTEGRATIONS_PATH."/bandwidth/sendsmsv2.php");
        SendSmsV2($from,$to,$msg);
    }else{

        $stmt = "select companyphone from company where shopid = '$shopid'";
        if ($query = $conn->prepare($stmt)){
            $query->execute();
            $query->bind_result($shopphone);
            $query->fetch();
            $query->close();
        }

        $cell = $_POST['textto'];
        // Your Account SID and Auth Token from twilio.com/console
        $sid = '**********************************';
        $token = 'f519be94a23d1968e0a1c89ea7030589';
        $client = new Client($sid, $token);

        // Use the client to do fun stuff like send text messages!
        $client->messages->create(
            // the number you'd like to send the message to
            '+1'.$cell,
            array(
                // A Twilio phone number you purchased at twilio.com/console
                'from' => '+***********',
                // the body of the text message you'd like to send
                'body' => $msg
            )
        );

    }

    echo "success";

}

elseif($type=='savefees')
{
    $qstr = '';

    $partstaxrate = $_POST['partstaxrate']??0;
    $labortaxrate = $_POST['labortaxrate']??0;
    $sublettaxrate = $_POST['sublettaxrate']??0;
    $hazardouswaste = $_POST['hazardouswaste']??0;


    if(isset($_POST['fee1percent']))$qstr .= ",fee1percent = '".$_POST['fee1percent']."'";
    if(isset($_POST['fee2percent']))$qstr .= ",fee2percent = '".$_POST['fee2percent']."'";
    if(isset($_POST['fee3percent']))$qstr .= ",fee3percent = '".$_POST['fee3percent']."'";
    if(isset($_POST['fee1amount']))
    {
        $qstr .= ",fee1amount = '".$_POST['fee1amount']."'";
        $totalfees += $_POST['fee1amount'];
    }
    if(isset($_POST['fee2amount']))
    {
        $qstr .= ",fee2amount = '".$_POST['fee2amount']."'";
        $totalfees += $_POST['fee2amount'];
    }
    if(isset($_POST['fee3amount']))
    {
        $qstr .= ",fee3amount = '".$_POST['fee3amount']."'";
        $totalfees += $_POST['fee3amount'];
    }

    $totalfees += $hazardouswaste;

    $laborstmt = "select coalesce(round(sum(extlabor),2),0) tlabor, COALESCE(SUM(if(upper(taxable)='YES',extlabor,0)),0) from quotelabor where shopid = '$shopid' and quoteid = $quoteid";
    if ($lquery = $conn->prepare($laborstmt)){
        $lquery->execute();
        $lquery->store_result();
        $lquery->bind_result($totallabor,$taxablelabor);
        $lquery->fetch();
    }

    $partstmt = "select coalesce(round(sum(extprice),2),0) tprts ,COALESCE(SUM(if(taxable='yes',extprice,0)),0) from quoteparts where shopid = '$shopid' and quoteid = $quoteid";
    if ($pquery = $conn->prepare($partstmt)){
        $pquery->execute();
        $pquery->store_result();
        $pquery->bind_result($totalparts,$taxableparts);
        $pquery->fetch();
    }


    $substmt = "select coalesce(round(sum(price),2),0) from quotesublet where shopid = '$shopid' and quoteid = $quoteid";
    if ($squery = $conn->prepare($substmt)){
        $squery->execute();
        $squery->store_result();
        $squery->bind_result($totalsublet);
        $squery->fetch();
    }        

    if($_POST['taxable']=='yes')
    {
        $stmt = "select companyzip,hst,pst,gst,qst,chargehst,chargepst,chargegst,chargeqst,userfee1taxable,userfee2taxable,userfee3taxable,hazwastetaxable from company where shopid = ?";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $query->store_result();
            $query->bind_result($companyzip,$hst,$pst,$gst,$qst,$chargehst,$chargepst,$chargegst,$chargeqst,$userfee1taxable,$userfee2taxable,$userfee3taxable,$hazwastetaxable);
            $query->fetch();
            $query->close();
        }

        if($userfee1taxable=='Taxable') $taxablefees += (isset($_POST['fee1amount'])?$fee1amount:0);
        if($userfee2taxable=='Taxable') $taxablefees += (isset($_POST['fee2amount'])?$fee2amount:0);
        if($userfee3taxable=='Taxable') $taxablefees += (isset($_POST['fee3amount'])?$fee3amount:0);

        $ptax = $ltax = $stax = $cantax = 0;

        $canadiantax = '';

        if(!is_numeric($companyzip))
        {
            $hst = $_POST['hst']??0;
            $pst = $_POST['pst']??0;
            $gst = $_POST['gst']??0;
            $qst = $_POST['qst']??0;
            $canadiantax = $hst.','.$pst.','.$gst.','.$qst;

        if($chargehst == "yes" && $hst > 0 ) $cantax += $hst;
        if($chargegst == "yes" && $gst > 0 ) $cantax += $gst;
        if($chargepst == "yes" && $pst > 0 ) $cantax += $pst;
        if($chargeqst == "yes" && $qst > 0 ) $cantax += $qst;

        }

        if($cantax>0 && $partstaxrate==0 && $labortaxrate==0 && $sublettaxrate==0)
        {
            $partstaxrate = $labortaxrate = $sublettaxrate = $cantax;
        }

        if ($partstaxrate > 0) {
            $ptax = round(($taxableparts + $taxablefees) * ($partstaxrate / 100), 2);
        }
        if ($labortaxrate > 0) {
            $ltax = round($taxablelabor * ($labortaxrate / 100), 2);
        }
        if ($sublettaxrate > 0) {
            $stax = round($totalsublet * ($sublettaxrate / 100), 2);
        }

        if (strtolower($hazwastetaxable) == "yes"){
            $hazwastetax = ($partstaxrate/100) * $hazardouswaste;
        }else{
            $hazwastetax = 0.00;
        }

        $salestax = round($ptax + $ltax + $stax + $hazwastetax,2);
    }
    else
    $salestax = 0;

    $subtotal = sbpround($totallabor+$totalparts+$totalsublet+$totalfees,2);
    $totalquote = round($subtotal + $salestax,2);


    $stmt = "update quotes set hazardouswaste = ?, partstaxrate = ?, labortaxrate = ?, sublettaxrate = ? ,totalfees = ?,salestax = ?,totalquote = ?,canadiantax = ? $qstr where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)){
        $query->bind_param("dddddddssi",$hazardouswaste,$partstaxrate,$labortaxrate,$sublettaxrate, $totalfees, $salestax, $totalquote, $canadiantax, $shopid, $quoteid);
        $query->execute();
        $conn->commit();
        $query->close();
    }

    echo "success";

}

elseif($type=='addnewpart')
{
    $comid = filter_var($_POST['comid'], FILTER_SANITIZE_STRING);
    $partnumber = filter_var($_POST['PartNumber'], FILTER_SANITIZE_STRING);
    $description = filter_var($_POST['PartDesc'], FILTER_SANITIZE_STRING);
    $category = filter_var($_POST['PartCategory'], FILTER_SANITIZE_STRING);
    $override = filter_var($_POST['overridematrix'], FILTER_SANITIZE_STRING);
    $quantity = filter_var($_POST['qty'], FILTER_SANITIZE_STRING);
    $cost = filter_var($_POST['PartCost'], FILTER_SANITIZE_STRING);
    $price = filter_var($_POST['PartPrice'], FILTER_SANITIZE_STRING);
    $discount = filter_var($_POST['Discount'], FILTER_SANITIZE_STRING);
    $net = filter_var($_POST['net'], FILTER_SANITIZE_STRING);
    $partcode = filter_var($_POST['PartCode'], FILTER_SANITIZE_STRING);
    $supplier = filter_var($_POST['PartSupplier'], FILTER_SANITIZE_STRING);
    $bin = filter_var($_POST['bin'], FILTER_SANITIZE_STRING);
    $extprice = filter_var($_POST['extprice'], FILTER_SANITIZE_STRING);
    $taxable = filter_var($_POST['tax'], FILTER_SANITIZE_STRING);
    $addmore = $_REQUEST['addmore']??'no';


    $stmt = "insert into quoteparts set partnumber = ?, part = ?, price = ?, qty = ?, net = ?, discount = ?, extprice = ?, partcost = ?, partcode = ?, supplier = ?, bin = ?, matrixcat = ?, taxable = ?,overridematrix = ?, shopid = ?, quoteid = ?, complaintid = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("ssddddddsssssssii",$partnumber,$description,$price,$quantity,$net,$discount,$extprice,$cost,$partcode,$supplier,$bin,$category,$taxable,$override,$shopid, $quoteid, $comid);
        $query->execute();
        $conn->commit();
        $query->close();
    }
    else
    echo "Quote update failed: (" . $conn->error . ")"; 

    // add the part to the parts registry
    if ($shopid == "1238" || $shopid == "1073" || $shopid == "1191" || $shopid == "1305"){
        $preg = "`partsregistry-".$shopid."`";
    }else{
        $preg = "`partsregistry`";
    }

    $stmt = "select count(*) c from $preg where shopid = ? and partnumber = ?";
    if ($query = $conn->prepare($stmt)){
        $query->bind_param("ss",$shopid,$partnumber);
        $query->execute();
        $query->bind_result($cnt);
        $query->fetch();
        $query->close();
    }

    if ($cnt == 0){
        $stmt = "insert into ".$preg." (`shopid`,`PartNumber`,`PartDesc`,`PartPrice`,PartCode,partsupplier,`partCost`,tax,overridematrix,bin,PartCategory) values (?,?,?,?,?,?,?,?,?,?,?)";
        if ($query = $conn->prepare($stmt)){
            $query->bind_param("sssdssdssss",$shopid,$partnumber,$description,$price,$partcode,$supplier,$cost,$taxable,$override,$bin,$category);
            $query->execute();
            $conn->commit();
        }else{
            echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }
    }

    if($addmore=='no')
    echo("success");
    else
    reCalculate();
}

elseif($type=='checkdupcustomer')
{
    $customer = filter_var($_POST['customer'], FILTER_SANITIZE_STRING);
    $phone = filter_var($_POST['phone'], FILTER_SANITIZE_STRING);
    $arr = array();
    if(!empty($phone))$phstr = " or cellphone like '".addslashes($phone)."%'";else $phstr = '';

    $stmt = "select customerid,lastname,firstname,address,city,state,zip,email,cellphone from customer where shopid = '$shopid' and (replace(lastname,'&#39;','\'') like '".addslashes($customer)."%' or replace(firstname,'&#39;','\'') like '".addslashes($customer)."%' $phstr ) and active != 'no' order by lastname,firstname";
    if ($query = $conn->prepare($stmt))
    {
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_array())
        $arr[] = $rs;
    }

    echo(json_encode($arr));
}

elseif($type=='getvehicles')
{
    $cid = filter_var($_POST['cid'], FILTER_SANITIZE_STRING);
    $arr = array();

    $stmt = "select vehid,year,make,model,vin,color from vehicles where shopid = ? and customerid = ?";
    if ($query = $conn->prepare($stmt))
    {
        $query->bind_param("si",$shopid,$cid);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_array())
        $arr[] = $rs;
    }

    echo(json_encode($arr));
}

elseif($type=='cusvehcreate')
{
    $cid = isset($_REQUEST['cid']) ? filter_var($_REQUEST['cid'], FILTER_SANITIZE_STRING) : "";
    $vid = isset($_REQUEST['vid']) ? filter_var($_REQUEST['vid'], FILTER_SANITIZE_STRING) : "";
    $customer = isset($_REQUEST['customer']) ? filter_var(strtoupper($_REQUEST['customer']), FILTER_SANITIZE_STRING) : "";
    $address = isset($_REQUEST['address']) ? filter_var(strtoupper($_REQUEST['address']), FILTER_SANITIZE_STRING) : "";
    $city = isset($_REQUEST['city']) ? filter_var(strtoupper($_REQUEST['city']), FILTER_SANITIZE_STRING) : "";
    $state = isset($_REQUEST['state']) ? filter_var(strtoupper($_REQUEST['state']), FILTER_SANITIZE_STRING) : "";
    $zip = isset($_REQUEST['zip']) ? filter_var(strtoupper($_REQUEST['zip']), FILTER_SANITIZE_STRING) : "";
    $phone = isset($_REQUEST['phone']) ? filter_var(strtoupper($_REQUEST['phone']), FILTER_SANITIZE_STRING) : "";
    $email = isset($_REQUEST['email']) ? filter_var($_REQUEST['email'], FILTER_SANITIZE_EMAIL) : "";
    $year = isset($_REQUEST['year']) ? filter_var(strtoupper($_REQUEST['year']), FILTER_SANITIZE_STRING) : "";
    $make = isset($_REQUEST['make']) ? filter_var(strtoupper($_REQUEST['make']), FILTER_SANITIZE_STRING) : "";
    $model = isset($_REQUEST['model']) ? filter_var(strtoupper($_REQUEST['model']), FILTER_SANITIZE_STRING) : "";
    $color = isset($_REQUEST['color']) ? filter_var(strtoupper($_REQUEST['color']), FILTER_SANITIZE_STRING) : "";
    $vin = isset($_REQUEST['vin']) ? filter_var(strtoupper($_REQUEST['vin']), FILTER_SANITIZE_STRING) : "";
    $cuscreate = $_REQUEST['createcus'];
    $vehcreate = $_REQUEST['createveh'];
    $carr = explode(',',$customer);
    $lastname = $carr[0];
    $firstname = (isset($carr[1])?trim($carr[1]):'');

    if(empty($cid) && $cuscreate=='yes')
    {
        $cid = 1;
        $stmt = "select customerid from customer where shopid = ? order by customerid desc limit 1";
        if ($query = $conn->prepare($stmt)){
        
            $query->bind_param("s",$shopid);
            $query->execute();
            $query->bind_result($cid);
            $query->fetch();
            $query->close();
        
        }

        $cid = $cid + 1;
        
        $stmt = "insert into customer (shopid,customerid,lastname,firstname,address,city,state,zip,cellphone,email)"
        . " values (?,?,?,?,?,?,?,?,?,?)";
        
        if ($query = $conn->prepare($stmt)){
            $query->bind_param("sissssssss",$shopid,$cid,$lastname,$firstname,$address,$city,$state,$zip,$phone,$email);
            $query->execute();
            $conn->commit();
            $query->close();        
        }
    }

    if(empty($vid) && $vehcreate=='yes')
    {
        $vid = 1;

        $stmt = "select vehid from vehicles where shopid = ? order by vehid desc limit 1";

        if ($query = $conn->prepare($stmt)){

            $query->bind_param("s",$shopid);
            $query->execute();
            $query->bind_result($vid);
            $query->fetch();
            $query->close();

        }


        $vid = $vid + 1;

        $stmt = "insert into vehicles (shopid,VehID,CustomerID,Year,Make,Model,Vin,color) values (?,?,?,?,?,?,?,?)";

        if ($query = $conn->prepare($stmt)){
            $query->bind_param("siisssss",$shopid,$vid,$cid,$year,$make,$model,$vin,$color);
            $query->execute();
            $conn->commit();
            $query->close();      
        }
    }


    echo(json_encode(array('cid'=>$cid,'vid'=>$vid)));
}

elseif($type=='saveissue')
{

    $issue = isset($_REQUEST['issue']) ? filter_var(trim($_REQUEST['issue']), FILTER_SANITIZE_STRING) : "";
    $comid = isset($_REQUEST['comid']) ? filter_var($_REQUEST['comid'], FILTER_SANITIZE_STRING) : "";

    $stmt = "update quotecomplaints set complaint = ? where shopid = ? and quoteid = ? and complaintid = ?";
    if ($query = $conn->prepare($stmt)){
        $query->bind_param("ssii", $issue, $shopid, $quoteid, $comid);
        $query->execute();
        $conn->commit();
        $query->close();
    }

    echo "success";

}
?>
