<?php
require_once CONN;

$shopid = isset($_COOKIE['shopid']) ? filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING) : "";
$matrix = isset($_COOKIE['matrix']) ? filter_var($_COOKIE['matrix'], FILTER_SANITIZE_STRING) : "";
$pid = isset($_GET['pid']) ? filter_var($_GET['pid'], FILTER_SANITIZE_STRING) : "";
$quoteid = isset($_GET['quoteid']) ? filter_var($_GET['quoteid'], FILTER_SANITIZE_STRING) : 0;
$type = isset($_GET['type']) ? filter_var($_GET['type'], FILTER_SANITIZE_STRING) : "";
$comid = isset($_GET['comid']) ? filter_var($_GET['comid'], FILTER_SANITIZE_STRING) : "";
$quotepid = $_GET['quotepid']??'';
$applyDiscount = $_COOKIE['applydiscounts'] ? strtolower($_COOKIE['applydiscounts']) :'no';

$stmt = "select usepartsmatrix from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $query->bind_result($userpartsmatrix);
    $query->fetch();
    $query->close();
} else {
    echo "company prepare failed: (" . $conn->errno . ") " . $conn->error;
}
    

if(empty($quotepid))
{
    if($type=='non')
    {
        $stmt = sprintf("SELECT table_name FROM information_schema.tables WHERE table_schema = 'shopboss' AND table_name = 'partsregistry-%s' LIMIT 1", $shopid);
        $preg = "partsregistry";
        if ($query = $conn->prepare($stmt)) {
            $query->execute();
            $query->store_result();
            $query->bind_result($table_name);
            $query->fetch();
            if ($query->num_rows > 0) {
                $preg = "`partsregistry-" . $shopid . "`";
            }
            $query->close();
        } else {
            echo "info schema prepare failed: (" . $conn->errno . ") " . $conn->error;
        }
    }
    else
    $preg = "partsinventory";

    $strsql = sprintf("Select PartNumber,PartDesc,coalesce(PartPrice,0),PartCode,coalesce(PartCost,0),PartSupplier,OnHand,Allocatted,NetOnHand,ReOrderLevel,MaxOnHand,MaintainStock,OrderStatus,TransitStatus,PartCategory,bin,tax,overridematrix,partid From %s where shopid = ? and partid = ?", $preg);
    if ($query = $conn->prepare($strsql)) {
        $query->bind_param("ss", $shopid, $pid);
        $query->execute();
        $query->store_result();
        $query->bind_result($PartNumber,$PartDesc,$PartPrice,$PartCode,$PartCost,$PartSupplier,$OnHand,$Allocatted,$NetOnHand,$ReOrderLevel,$MaxOnHand,$MaintainStock,$OrderStatus,$TransitStatus,$PartCategory,$bin,$tax,$overridematrix,$partid);
        $query->fetch();
        $query->close();
    } 

    $qty = 1;
    $discount = 0;
}

else
{
    $strsql = "Select partnumber,part,coalesce(price,0),partcode,coalesce(partcost,0),supplier,matrixcat,bin,taxable,overridematrix,qty,discount From quoteparts where shopid = ? and quoteid = ? and id = ?";
    if ($query = $conn->prepare($strsql)) {
        $query->bind_param("sii", $shopid, $quoteid, $quotepid);
        $query->execute();
        $query->store_result();
        $query->bind_result($PartNumber,$PartDesc,$PartPrice,$PartCode,$PartCost,$PartSupplier,$PartCategory,$bin,$tax,$overridematrix,$qty,$discount);
        $query->fetch();
        $query->close();
    } 
}

include getHeadGlobal('');
?>
<body>

    <form name="mainform" id="mainform">

        <input type="hidden" name="shopid" id="shopid" value="<?php echo $shopid;?>">
        <input type="hidden" name="quoteid" id="quoteid" value="<?php echo $quoteid;?>">
        <input type="hidden" name="comid" id="comid" value="<?php echo $comid;?>">
        <input type="hidden" name="discountamt" id="discountamt" value="0">
        <input type="hidden" name="quotepartid" id="quotepartid" value="<?= $quotepid?>">
        <div class="d-flex">

        <div class="row content-container pb-0 mt-4">
            
            <div class="col-md-6">

                <div class="form-row mb-4">
                    <select class="select" tabindex="1" name="overridematrix" id="overridematrix">
                        <?php
                            if (strtolower($overridematrix) == "yes"){
                                $ys = ' selected="selected" ';
                                $ns = '';
                            }else{
                                $ys = '';
                                $ns = ' selected="selected" ';
                            }
                        ?>
                        <option <?php echo $ys; ?> value="yes">Yes</option>
                        <option <?php echo $ns; ?> value="no">No</option>
                    </select>
                    <label class="form-label select-label" for="overridematrix">Override Matrix Price</label>
                </div>

                <div class="form-outline mb-4">
                    <input class="form-control" tabindex="2" type="text" id="partnum" value="<?php echo strtoupper($PartNumber); ?>" name="PartNumber">
                    <label class="form-label" for="partnum">Part Number</label>
                </div>
                    
                <div class="form-outline mb-4">
                    <input class="form-control" tabindex="3" type="text" id="partdesc" value="<?php echo str_replace('"',' in ',strtoupper($PartDesc)); ?>" name="PartDesc">
                    <label class="form-label" for="partdesc">Part Description</label>
                </div>  

                <div class="form-row mb-4">
                    <select onchange="calcPrices()" class="select" tabindex="4" name="PartCategory" id="PartCategory">
                        <?php
                        $stmt = "select distinct category c from category where shopid = ? order by displayorder";
                        $matrix = strtolower($_COOKIE['matrix']);
                        if($query = $conn->prepare($stmt)){
                            $query->bind_param("s",$shopid);
                            $query->execute();
                            $result = $query->get_result();
                            $query->store_result();
                            $numrows = $result->num_rows;
                            if ($numrows > 0){
                                while($row = $result->fetch_array()) {
                                    if (strtoupper($PartCategory) == strtoupper($row['c'])){
                                        $s = "selected";
                                    }else{
                                        $s = "";
                                    }
                                    echo "<option $s value='".strtoupper($row['c'])."'>".strtoupper($row['c'])."</option>";
                                }
                            }else{
                                echo "<option value='none'>No Suppliers Entered</option>";
                            }
                        }
                        ?>
                    </select>

                    <label class="form-label select-label" for="PartCategory">Matrix Category</label>
                </div>

                <div class="form-outline mb-4">
                    <input class="form-control" onblur="calcPrices()" tabindex="5" type="text" id="qty" name="qty" value="<?= $qty?>">
                    <label class="form-label" for="qty">Quantity</label>
                </div>
            
        
                <div class="form-outline mb-4">
                    <input class="form-control" onblur="calcPrices()" tabindex="6" type="text" id="PartCost" value="<?php echo number_format($PartCost,2,'.',''); ?>" name="PartCost">
                    <label class="form-label" for="PartCost">Shop Cost</label>
                </div>
            
        
                <div class="form-outline mb-4">
                    <input class="form-control" onblur="calcPrices()" tabindex="7" type="text" id="PartPrice" value="<?php echo number_format($PartPrice,2,'.',''); ?>" name="PartPrice">
                    <label class="form-label" for="PartPrice">Selling Price</label>
                </div>
            
        
                <div class="form-outline mb-4">
                    <input class="form-control" onblur="calcPrices()" onkeyup="calcPrices()" value="<?= $discount?>" tabindex="8" type="text" id="Discount" value="0" name="Discount" <?= ($applyDiscount == "yes" ? "" : "readonly") ?> >
                    <label class="form-label" for="Discount">Discount %</label>
                </div>
            
        
                <div class="form-outline mb-4">
                    <input class="form-control" onblur="calcPrices()" tabindex="9" type="text" id="net" name="net" value="0">
                    <label class="form-label" for="net">Net Selling Price</label>
                </div>
                            
            </div>

            <div class="col-md-6">

                <div class="form-row mb-4">
                    <select onkeyup="" class="select" tabindex="11" name="PartCode" id="PartCode">
                        <option selected value="<?php echo strtoupper($PartCode); ?>"><?php echo strtoupper($PartCode); ?></option>
                        <?php
                        $stmt = "select codes from codes where shopid = ? and codes != ? order by codes asc";
                        if($query = $conn->prepare($stmt)){
                            $query->bind_param("ss",$shopid,$PartCode);
                            $query->execute();
                            $result = $query->get_result();
                            $query->store_result();
                            $numrows = $result->num_rows;
                            if ($numrows > 0){
                                while($row = $result->fetch_array()) {
                                    echo '<option value="'.strtoupper($row['codes']).'">'.strtoupper($row['codes']).'</option>';
                                }
                            }else{
                                echo '<option value="New">New</option>';
                            }
                        }
                        ?>
                    </select>
                    <label class="form-label select-label" for="PartCode">Part Code</label>
                </div>

                <div class="form-row mb-4">
                    <select onkeyup="" class="select" tabindex="12" name="PartSupplier" id="PartSupplier" data-mdb-filter="true">
                        <option selected value="<?php echo strtoupper($PartSupplier); ?>"><?php echo strtoupper($PartSupplier); ?></option>
                        <?php
                        $supplier = "";
                        $stmt = "select suppliername s from supplier where suppliername != ? and shopid = ? and active = 'YES' order by suppliername";
                        printf ( str_replace('?',"'%s'",$stmt),$PartSupplier,$shopid);
                        $matrix = strtolower($_COOKIE['matrix']);
                        if($query = $conn->prepare($stmt)){
                            $query->bind_param("ss",$PartSupplier,$shopid);
                            $query->execute();
                            $result = $query->get_result();
                            $query->store_result();
                            $numrows = $result->num_rows;
                            if ($numrows > 0){
                                while($row = $result->fetch_array()) {
                                    if (strtoupper($PartSupplier) == strtoupper($row['s'])){
                                        $s = "selected";
                                    }else{
                                        $s = "";
                                    }
                                    echo "<option $s value=\"".strtoupper($row['s'])."\">".strtoupper($row['s'])."</option>";
                                }
                            }else{
                                echo "<option value='none'>No Suppliers Entered</option>";
                            }
                        }
                        ?>
                    </select>

                    <label class="form-label select-label" for="PartSupplier">Supplier</label>
                </div>

                <div class="form-outline mb-4">
                    <input class="form-control" tabindex="14" type="text" id="bin" name="bin" value="<?php echo $bin; ?>">
                    <label class="form-label" for="bin">Bin</label>
                </div>

                <div class="form-outline mb-4">
                    <input class="form-control" tabindex="16" type="text" id="extprice" name="extprice" value="0">
                    <label class="form-label" for="extprice">Total Price</label>
                </div>
            
        
                <div class="form-outline mb-4">
                    <input class="form-control" tabindex="17" type="text" id="extcost" name="extcost" value="0">
                    <label class="form-label" for="extcost">Total Cost</label>
                </div>

                <div class="form-row mb-4">
                    <select onkeyup="" class="select" tabindex="18" name="tax" id="tax">
                        <?php
                            if (strtolower($tax) == "yes"){
                                $ys = ' selected="selected" ';
                                $ns = '';
                            }else{
                                $ys = '';
                                $ns = ' selected="selected" ';
                            }
                        ?>
                        <option <?php echo $ys; ?> value="yes">Yes</option>
                        <option <?php echo $ns; ?> value="no">No</option>
                    </select>
                    <label class="form-label select-label" for="tax">Taxable</label>
                </div>
            </div>
        </div>
    </div>

        </div>

            <nav class="fixed-bottom emodal-footer">
             <?php if(!empty($quotepid)){?>
             <button type="button" onclick="calcPrices();submitForm('savepart','no')" class="btn btn-primary btn-md">Save</button>
             <?php }else{?>
             <button type="button" onclick="calcPrices();submitForm('addpart','yes')" class="btn btn-secondary btn-md btn-savepart me-3">Save & Add Another</button>
             <button type="button" onclick="calcPrices();submitForm('addpart','no')" class="btn btn-primary btn-md btn-savepart">Save</button>
             <?php }?>
            </nav>
               
     </form>
                                  
    <?php include getScriptsGlobal(''); ?>

    <script>
       <?php if (strtolower($userpartsmatrix) == "yes"){ ?>
            function calcPrices(){

                <?php

                $stmt = "select category,factor,start,end from category where shopid = ? order by Category, Start";

                if($query = $conn->prepare($stmt)){
                    $query->bind_param("s",$shopid);
                    $query->execute();
                    $result = $query->get_result();
                    $rs = array();
                    while($row = $result->fetch_assoc()) {
                        $rs[] = $row;
                    }
                    echo "var clist = ".json_encode($rs)."\r\n";
                }
                ?>
                if ($('#overridematrix').val() == "yes"){
                    if (parseFloat($('#Discount').val()) > 0){

                                disc = parseFloat($('#Discount').val()) / 100
                                qty = $('#qty').val()
                                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                                extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val()))*disc).toFixed(2)
                                baseprice = $('#PartPrice').val()
                                discamt = baseprice * disc
                                netprice = ($('#PartPrice').val()-discamt)
                                $('#net').val(netprice.toFixed(2))
                                $('#extcost').val(extcost)
                                $('#discountamt').val(discamt.toFixed(2))
                                $('#extprice').val((netprice * qty).toFixed(2))
                                
                            }else{
                                $('#discountamt').val('0')
                                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                                extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                                $('#net').val($('#PartPrice').val())
                                $('#extcost').val(extcost)
                                $('#extprice').val(extprice)
                            }
                }else{
                   
                    srch = $('#PartCategory').val().toLowerCase()
                    amt = $('#PartCost').val()
                    $.each(clist, function(i, v){
                        if (v.category.toUpperCase() === srch.toUpperCase() && v.start <= amt && v.end >= amt){
                            $('#PartPrice').val( Math.round((amt * v.factor)*100)/100 )
                        }
                        if (parseFloat($('#qty').val()) > 0 && parseFloat($('#PartPrice').val()) >= 0 && parseFloat($('#PartCost').val()) >= 0){
                            if (parseFloat($('#Discount').val()) > 0){

                                disc = parseFloat($('#Discount').val()) / 100
                                qty = $('#qty').val()
                                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                                extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val()))*disc).toFixed(2)
                                baseprice = $('#PartPrice').val()
                                discamt = baseprice * disc
                                netprice = ($('#PartPrice').val()-discamt)
                                $('#net').val(netprice.toFixed(2))
                                $('#extcost').val(extcost)
                                $('#discountamt').val(discamt.toFixed(2))
                                $('#extprice').val((netprice * qty).toFixed(2))
                            }else{
                                $('#discountamt').val('0')
                                extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                                extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                                $('#net').val($('#PartPrice').val())
                                $('#extcost').val(extcost)
                                $('#extprice').val(extprice)
                            }
                        }

                    });
                }
            }
            <?php
            }else{
            ?>
            function calcPrices(){
                if (parseFloat($('#qty').val()) > 0 && parseFloat($('#PartPrice').val()) >= 0 && parseFloat($('#PartCost').val()) >= 0){
                    if (parseFloat($('#Discount').val()) > 0){

                        disc = parseFloat($('#Discount').val()) / 100
                        qty = $('#qty').val()
                        extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                        extprice = ((parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val()))*disc).toFixed(2)
                        baseprice = $('#PartPrice').val()
                        discamt = baseprice * disc
                        netprice = ($('#PartPrice').val()-discamt)
                        $('#net').val(netprice.toFixed(2))
                        $('#extcost').val(extcost)
                        $('#discountamt').val(discamt.toFixed(2))
                        $('#extprice').val((netprice * qty).toFixed(2))
                    }else{
                        $('#discountamt').val('0')
                        extcost = (parseFloat($('#PartCost').val()) * parseFloat($('#qty').val())).toFixed(2)
                        extprice = (parseFloat($('#PartPrice').val()) * parseFloat($('#qty').val())).toFixed(2)
                        $('#net').val($('#PartPrice').val())
                        $('#extcost').val(extcost)
                        $('#extprice').val(extprice)
                    }
                }

            }
            <?php
            }
            ?>

    function submitForm(t,addmore) {

        $('.btn-md').attr('disabled','disabled')
        showLoader()
        
        ds = $('#mainform').serialize()+"&t="+t+"&addmore="+addmore

        $.ajax({
            data: ds,
            type: "post",
            url:  "quoteactions.php",
            success: function(r){
                if(r !== 'success'){
                    sbalert(r)
                } else {
                    if(addmore=='no')
                    parent.location.href="quote.php?quoteid=<?= $quoteid?>&recalc=y"
                    else
                    location.href="addpart.php?&quoteid=<?php echo $quoteid; ?>&comid=<?= $comid?>"
                }
                hideLoader()
            }
        });
    }

    $(document).ready(function(){
    $('#qty').focus()
    calcPrices()

    });
    </script>
</body>

</html>