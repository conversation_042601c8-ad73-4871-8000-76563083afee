<?php

require CONN;

$shopid = $_COOKIE['shopid'];
$cid = $_GET['cid'];
$stmt = "SELECT LastName,FirstName, Address,City,State,Zip,HomePhone,WorkPhone,CellPhone,EMail FROM customer WHERE shopid = ? and customerid = ?";

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $cid);
    $query->execute();
    $query->bind_result($ln, $fn, $addr, $city, $state, $zip, $home, $work, $cell, $email);
    $query->fetch();
    $query->close();
} else {
    echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "SELECT coalesce(sum(totallbr+totalprts+totalsublet+totalfees+salestax-discountamt),0) as tro "
    . "from repairorders "
    . "WHERE shopid = ?"
    . "  AND customerid = ?"
    . "  AND rotype != 'No Approval'";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $cid);
    $query->execute();
    $query->bind_result($totalros);
    $query->fetch();
    $query->close();
} else {
    echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

// subtract out writeoff debt
$stmt = "SELECT coalesce(sum(amount),0) as bdamount FROM expenses WHERE shopid = ? AND cid = ? AND expensename = 'Bad Debt'";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $cid);
    $query->execute();
    $query->bind_result($bdamount);
    $query->fetch();
    $query->close();
} else {
    echo "Expense Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$totalros = sbpround(($totalros - $bdamount), 2);

?>

<div class="row">
    <div class="col-md-12 mb-2">
        <table class="table table-sm">
            <tr>
                <td style="width:50%">
                    <?php
                    echo $cid . "-" . $ln . ", " . $fn . "<BR>";
                    echo $addr . "<BR>";
                    echo $city . ", " . $state . ", " . $zip . "<BR>";
                    ?>
                </td>
                <td style="width:50%">
                    <?php
                    echo "Home: " . formatPhone($home) . "/ Work: " . formatPhone($work) . "/ Cell: " . formatPhone($cell) . "<BR>";
                    echo "Email: <span id='customeremailfromdata'>" . $email . "</span><br>";
                    echo "<b>Total RO's: $" . number_format($totalros, 2);
                    ?>
                </td>
            </tr>
            <tr>
                <td>
                   &nbsp;
                </td>

                <td id="balance" style="color:var(--primary);font-weight:bold">&nbsp;</td>
            </tr>
        </table>
    </div>

    <div class="col-md-12 mb-4">
        <table class="table table-sm">
            <thead>
            <tr>
                <th style="width: 0">
                    <div class="form-check">
                        <input class="form-check-input" onclick="checkAll()" id="checkall" name="checkall" type="checkbox">
                    </div>
                </th>
                <th>Transaction Type</th>
                <th>Number</th>
                <th class="text-center">Date</th>
                <th>Vehicle</th>
                <th>RO Status</th>
                <th class="text-end">Amount</th>
            </tr>
            </thead>
            <tbody>
            <?php
            $runningdisplaybalance = 0;
            $displaytotalro = 0;
            $rtotalro = 0;
            $rbal = 0.00;
            $psbal = 0.00;
            $displaybalance = 0;
            $psdisplaybalance = 0;

            $robdballist = "";
            $psbdballist = "";

            $stmt = "SELECT roid,StatusDate,vehinfo,TotalRO,TotalLbr,TotalPrts,TotalSublet,TotalFees,SalesTax,DiscountAmt,balance,status";
            $stmt .= " FROM repairorders ";
            $stmt .= "WHERE shopid = ? ";
            $stmt .= "  AND customerid = ? ";
            $stmt .= "  AND rotype != 'NO PROBLEM' and rotype != 'NO APPROVAL' ";
            $stmt .= "  ORDER BY roid desc ";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si", $shopid, $cid);
                $query->execute();
                $roresult = $query->get_result();

                while ($row = $roresult->fetch_array()) {
                    $bal = 0;
                    $roid = $row['roid'];
                    $statusdate = new DateTime($row["StatusDate"]);
                    $displaybalance = 0;

                    if (is_numeric(substr($row["status"], 0, 1))) {
                        $rostatus = substr($row["status"], 1);
                    } else {
                        $rostatus = $row["status"];
                    }

                    // if the ro is not quite zero, set to zero
                    if ($row["balance"] > -0.01 and $row["balance"] < 0.01) {
                        $stmt = "UPDATE repairorders SET ";
                        $stmt .= "balance  = 0 ";
                        $stmt .= "WHERE shopid = ? ";
                        $stmt .= "  AND roid = ? ";
                        $stmt .= "LIMIT 1";

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("si", $shopid, $roid);
                            if ($query->execute()) {
                                $conn->commit();
                            } else {
                                echo $conn->errno;
                            }
                        } else {
                            echo "RO Update Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }
                    }

                    $displaytotalro = $row["TotalLbr"] + $row["TotalPrts"] + $row["TotalSublet"] + $row["TotalFees"] + $row["SalesTax"] - $row["DiscountAmt"];
                    $rtotalro += $displaytotalro;
                    $displaybalance = $displaytotalro;

                    if ($row["balance"] != 0)
                        $robdballist = $robdballist . $roid . ",";

                    ?>
                    <tr>
                        <td style="width: 0;">
                            <div class="form-check">
                                <input onclick="checkBoxes()" class="form-check-input form-check-input-ro" id="ro<?php echo $row["roid"]; ?><?php echo "|"; ?><?php echo $displaytotalro; ?><?php echo "|"; ?><?php echo $row["status"]; ?>" type="checkbox">
                            </div>
                        </td>
                        <td onclick="showRO('<?php echo $row["roid"]; ?>','<?php echo $row["status"]; ?>')">Invoice/RO&nbsp;</td>
                        <td onclick="showRO('<?php echo $row["roid"]; ?>','<?php echo $row["status"]; ?>')">RO#<?php echo $row["roid"]; ?>&nbsp;</td>
                        <td class="text-center" onclick="showRO('<?php echo $row["roid"]; ?>','<?php echo $row["status"]; ?>')"><?php echo $statusdate->format('m/d/Y'); ?>&nbsp;</td>
                        <td id="vehinfo" onclick="showRO('<?php echo $row["roid"]; ?>','<?php echo $row["status"]; ?>')"><?php echo $row["vehinfo"]; ?>&nbsp;</td>
                        <td id="status" onclick="showRO('<?php echo $row["roid"]; ?>','<?php echo $row["status"]; ?>')"><?php echo $rostatus; ?>&nbsp;</td>

                        <td class="text-end" id="totalro<?php echo $row["roid"]; ?>,'<?php echo $row["status"]; ?>'" onclick="showRO('<?php echo $row["roid"]; ?>')" style="text-align:right; height: 22px;"><?php echo sbpround($displaytotalro, 2); ?>&nbsp;</td>
                    </tr>
                    <?php

                    //now get all payments.
                    $tramt = 0;
                    $ramt = 0;
                    $pdate = "";

                    $stmt = "select * ";
                    $stmt .= "from accountpayments ";
                    $stmt .= "WHERE shopid = ? ";
                    $stmt .= "  AND roid = ? and ptype!='Bad Debt' ";
                    $stmt .= "  ORDER BY pdate desc ";
                    //echo $stmt;

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("si", $shopid, $roid);
                        $query->execute();
                        $result = $query->get_result();
                        while ($ap = mysqli_fetch_assoc($result)) {
                            $ramt = $ramt + $ap["amt"];
                            $tramt = $tramt + $ap["amt"];
                            $pdate = new DateTime($ap["pdate"]);

                            ?>
                            <tr>
                                <td style="padding-left:20px; width: 0;">&nbsp;</td>
                                <td style="padding-left:20px">Payment Received&nbsp;</td>
                                <td>Ref# <?php echo $ap["pnumber"]; ?>&nbsp;</td>
                                <td class="text-center"><?php echo $pdate->format('m/d/Y'); ?>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td><?php echo $ap["ptype"]; ?>&nbsp;</td>
                                <td class="text-end"><?php echo number_format($ap["amt"], 2); ?>&nbsp;</td>
                            </tr>

                            <?php
                            $displaybalance = round($displaytotalro, 2) - round($tramt, 2);
                            $runningdisplaybalance += $displaybalance;

                            ?>
                            <?php
                        } // end of payment while

                    } // end of payment if

                    // now get the bad debt if any
                    $stmt = "SELECT amount,paiddate,ref FROM expenses WHERE shopid = ? AND cid = ? AND roid = ? and expensename = 'Bad Debt'";
                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("sii", $shopid, $cid, $row['roid']);
                        $query->execute();
                        $br = $query->get_result();
                        while ($brs = $br->fetch_assoc()) {
                            $displaybalance = round($displaybalance,2) - round($brs['amount'], 2);
                            $runningdisplaybalance += $displaybalance;

                            ?>
                            <tr>
                                <td style="padding-left:20px; width: 0;">&nbsp;</td>
                                <td style="padding-left:20px;color:var(--primary);font-weight:bold">** BAD DEBT WRITE OFF **&nbsp;</td>
                                <td>Ref# <?php echo $brs["ref"]; ?>&nbsp;</td>
                                <td class="text-center"><?php echo date("m/d/Y", strtotime($brs['paiddate'])); ?>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td class="text-end"><?php echo number_format($brs["amount"], 2); ?>&nbsp;</td>
                            </tr>
                            <?php
                        }

                    }
                    if ($displaybalance >= 0.01) {
                        $colormod = "color:var(--primary);";
                    } else {
                        $colormod = "color:black;";
                    }

                    ?>
                    <tr>
                        <td style="padding-left:20px; width: 0;">&nbsp;</td>
                        <td style="padding-left:20px">&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td class="text-end" colspan="2">
                            
                                <?php
                                if ($displaybalance < 0) {
                                    echo "<button onclick='splitPmt(" . $row["roid"] . ")' class='btn btn-secondary' type='button'>Split Payment</button> &nbsp;&nbsp;&nbsp;";
                                }
                                ?>
                                Balance This RO:&nbsp;
                            
                        </td>
                        <td class="text-end" style="<?php echo $colormod; ?>">
                            <?php echo $displaybalance; ?>&nbsp;
                        </td>
                    </tr>
                    <input type="hidden" id="robal<?= $row["roid"] ?>" value="<?= $displaybalance ?>">

                    <?php
                    $rbal += $displaybalance;
                } // end of ro while loop
            } else {
                ?>
                <tr>
                    <td style="width: 0">&nbsp;</td>
                    <td>No Repair Orders Found&nbsp;</td>
                    <td colspan="4">&nbsp;</td>
                </tr>
                <?php
            }
            ?>
            </tbody>
        </table>
    </div>

    <div class="col-md-12">
        <table class="table table-sm">
            <thead>
            <tr class="">
                <th style="width: 0">&nbsp;
                    <div class="form-check">
                        <input class="form-check-input" onclick="checkAllps()" id="checkallps" name="checkallps" type="checkbox">
                    </div>
                </th>
                <th>Transaction Type Part Sale</th>
                <th>Number</th>
                <th class="text-center">Date</th>
                <th></th>
                <th>PS Status</th>
                <th class="text-end">Amount</th>
            </tr>
            </thead>

            <tbody>
            <?php
            $psdisplaytotalro = 0;
            $psrtotalro = 0;

            $stmt = "select psid,statusdate,total,balance,status ";
            $stmt .= "from ps ";
            $stmt .= "WHERE shopid = ? ";
            $stmt .= "  AND cid = ? ";
            $stmt .= "  AND status = 'CLOSED' ";
            $stmt .= "  ORDER BY psid desc ";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si", $shopid, $cid);
                $query->execute();
                $result = $query->get_result();
                while ($psrow = mysqli_fetch_assoc($result)) {
                    $psid = $psrow['psid'];
                    $psdisplaytotalro = $psrow["total"];
                    $psrtotalro = $psrtotalro + $psdisplaytotalro;
                    $statusdate = new Datetime($psrow["statusdate"]);
                    ?>
                    <tr>
                        <td style="width: 0">
                            <div class="form-check">
                                <input onclick="checkBoxesps()" class="form-check-input form-check-input-ps" id="PS<?php echo $psrow["psid"]; ?><?php echo "|"; ?><?php echo $psdisplaytotalro; ?><?php echo "|"; ?><?php echo $psrow["status"]; ?>" type="checkbox">
                            </div>
                        </td>
                        <td onclick="showPS('<?php echo $psrow["psid"]; ?>')"><b>
                                Invoice/Part Sale</b>&nbsp;
                        </td>
                        <td onclick="showPS('<?php echo $psrow["psid"]; ?>')">PS# <?php echo $psrow["psid"]; ?>&nbsp;</td>
                        <td onclick="showPS('<?php echo $psrow["psid"]; ?>')" class="text-center"><?php echo date_format($statusdate, 'm/d/Y'); ?>&nbsp;</td>
                        <td onclick="showPS('<?php echo $psrow["psid"]; ?>')">N/A&nbsp;</td>
                        <td onclick="showPS('<?php echo $psrow["psid"]; ?>')"><?php echo $psrow["status"]; ?>&nbsp;</td>
                        <td onclick="showPS('<?php echo $psrow["psid"]; ?>')" class="text-end"><?php echo number_format($psdisplaytotalro, 2); ?>&nbsp;</td>
                    </tr>
                    <?php

                    $stmt = "select * ";
                    $stmt .= "from `accountpayments-ps` ";
                    $stmt .= "WHERE shopid = ? ";
                    $stmt .= "  AND psid = ? ";
                    $stmt .= "  ORDER BY pdate desc ";
                    $psramt = 0;
                    $pstramt = 0;
                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("si", $shopid, $psid);
                        $query->execute();
                        $apresult = $query->get_result();
                        while ($ap = mysqli_fetch_assoc($apresult)) {
                            $psramt = $psramt + $ap["amt"];
                            $pstramt = $pstramt + $ap["amt"];
                            $pdate = new Datetime($ap["pdate"]);
                            ?>
                            <tr>
                                <td style="padding-left:20px; width: 0;">&nbsp;</td>
                                <td style="padding-left:20px">Payment Received&nbsp;</td>
                                <td>Ref# <?php echo $ap["pnumber"]; ?>&nbsp;</td>
                                <td class="text-center"><?php echo date_format($pdate, 'm/d/Y'); ?>&nbsp;</td>
                                <td><?php echo $ap["ptype"]; ?>&nbsp;</td>
                                <td style="padding-left:20px; width: 0;">&nbsp;</td>
                                <td class="text-end"><?php echo number_format($ap["amt"], 2); ?>&nbsp;</td>
                            </tr>
                            <?php
                        } // end of ap part sale while
                    } else {
                        echo $conn->error;
                    }

                    $psdisplaybalance = round($psrow["total"] - $psramt, 2);
                    $runningdisplaybalance = $runningdisplaybalance + $psdisplaybalance;

                    if ($psdisplaybalance <= 0.00) {
                        $pscolormod = "color:var(--textColor) !important;";
                    } else {
                        $pscolormod = "color:var(--primary);";
                    }

                    ?>
                    <tr>
                        <td style="padding-left:20px; width: 0;">&nbsp;</td>
                        <td style="padding-left:20px">&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td class="text-end" colspan="2">Balance This Part Sale:&nbsp;</td>
                        <td class="text-end" style="<?php echo $pscolormod; ?>"><?php echo $psdisplaybalance; ?>&nbsp;</td>
                    </tr>
                    <?php
                    $rbal += $psdisplaybalance;

                    $psramt = 0;
                } // end of part sale while
            }    // do not know put in on 6/27/17

            $psamt = 0;
            $ramt = 0;
            $bd = 0;

            if ($bd > 0) {
                ?>
                <tr>
                    <td style="padding-left:20px; width: 0;">&nbsp;</td>
                    <td style="padding-left:20px">Total Written off as Bad Debt&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td class="text-end">&nbsp;</td>
                    <td>&nbsp;</td>
                    <td class="text-end"><?php echo number_format($bd, 2); ?>&nbsp;</td>
                </tr>
                <?php
            }    // end of bd if

        //    $rbal = $rbal + ($psrtotalro - $psramt) + ($rtotalro - $ramt);

            $stmt = "SELECT roid, totallbr+totalprts+totalsublet+salestax+totalfees-discountamt as tro ";
            $stmt .= "FROM repairorders ";
            $stmt .= "WHERE shopid = ? ";
            $stmt .= "  AND customerid = ? ";
            $stmt .= "  AND ucase(rotype) != 'NO APPROVAL' ";
            $stmt .= "  AND `status` = 'CLOSED' ";
            //echo $stmt;

            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si", $shopid, $cid);
                $query->execute();
                $result = $query->get_result();
            } else {
                echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            $numofrows = $result->num_rows;

            // Check for data
            $ttlro = 0;
            $rolist = "";
            $ttlps = 0;
            $ttlpspmts = 0;

            if ($numofrows > 0) {
                //echo "Number of Rows works: " . $numofrows;
                while ($bad = mysqli_fetch_assoc($result)) {
                    $rolist = $rolist . $bad['roid'] . ",";
                    //cdbl
                    $ttlro = $ttlro + $bad['tro'];

                } // end of bad loop
            } else {
                $ttlro = 0;
                $rolist = "";

            } // end of bad if

            if (substr($rolist, -1) == ",") {
                $rolist = substr($rolist, 0, strlen($rolist) - 1);
            }

            $ttlpmts = 0;

            if (strlen($rolist) > 0) {
                //get ro payments for the list of closed ros

                $query = "SELECT coalesce(sum(amt),0) as ttlpmts";
                $query .= " FROM accountpayments ";
                $query .= "WHERE shopid = '{$shopid}'";
                //$query .= "  AND cid = {$cid} ";
                $query .= "  AND roid in ($rolist)";
                //echo $query;

                $apcresult = mysqli_query($conn, $query);

                if (!$apcresult) {
                    die("Database accounts payment closed query failed.");
                }

                $apc = mysqli_fetch_assoc($apcresult);

                $ttlpmts = $apc["ttlpmts"];

            } else {
                $ttlpmts = 0;
            } // end of get ro payments for the list of closed ros

            // Get the closed part sales
            $pslist = "";

            $stmt = "SELECT psid, total";
            $stmt .= " FROM ps ";
            $stmt .= "WHERE shopid = ? ";
            $stmt .= "  AND cid = ? ";
            $stmt .= "  AND `status` = 'CLOSED'";
            //echo $stmt;

            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si", $shopid, $cid);
                $query->execute();
                $result = $query->get_result();

                $numofrows = $result->num_rows;

                if ($numofrows > 0) {
                    while ($row = $result->fetch_array()) {
                        $pslist = $pslist . $row["psid"] . ",";
                        $ttlps = $ttlps + $row["total"];

                    } // end of part sale loop
                } else {
                    $ttlps = 0;
                    $pslist = "";
                }

            } else {
                echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            if (substr($pslist, -1) == ",") {
                $pslist = substr($pslist, 0, strlen($pslist) - 1);
            }

            if (strlen($pslist) > 0) {
                // cant substitute until I can figure out the in clause for bindingsubstitute
                $query = "SELECT coalesce(sum(amt),0) as ttlpspmts";
                $query .= " FROM `accountpayments-ps` ";
                $query .= "WHERE shopid = '{$shopid}'";
                $query .= "  AND cid = {$cid} ";
                $query .= "  AND psid in ($pslist)";
                //echo $query;

                $apcresult = mysqli_query($conn, $query);

                if (!$apcresult) {
                    die("Database accounts payment ps closed query failed.");
                }

                $apc = mysqli_fetch_assoc($apcresult);

                $ttlpspmts = $apc["ttlpspmts"];

            } else {
                $ttlpspmts = 0;
            }

            // added decrement of bad debt
           // $rbal = number_format($ttlro + $ttlps - $ttlpmts - $ttlpspmts - $bd, 2);

            if ($rbal < 0.02 and $rbal > -0.02) {
                $rbal = 0.00;
            }
            ?>
            </tbody>
        </table>
    </div>
</div>

<input type="hidden" id="rbal" value="<?php echo number_format($rbal, 2); ?>">
<input type="hidden" id="robdballist" value="<?php echo $robdballist; ?>">
<input type="hidden" id="psbdballist" value="<?php echo $psbdballist; ?>">

<?php
mysqli_close($conn);
?>
