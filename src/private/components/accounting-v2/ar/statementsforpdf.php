<?php
ini_set("display_errors", "1");
error_reporting(E_ALL);

/**@var mysqli $conn */
require CONNWOSHOPID;


$shopid = isset($_REQUEST['shopid']) ? filter_var($_REQUEST['shopid'], FILTER_SANITIZE_STRING) : "";
$shopname = isset($_REQUEST['shopname']) ? filter_var($_REQUEST['shopname'], FILTER_SANITIZE_STRING) : "";
$sd = isset($_REQUEST['sd']) ? filter_var($_REQUEST['sd'], FILTER_SANITIZE_STRING) : "";
$ed = isset($_REQUEST['ed']) ? filter_var($_REQUEST['ed'], FILTER_SANITIZE_STRING) : "";

$sdate = date('Y-m-d', strtotime($sd));
$edate = date('Y-m-d', strtotime($ed));


if (empty($shopid)) {
    $shopid = $_COOKIE['shopid'];
}

$_COOKIE['shopid'] = $shopid;


function format_currency($num, $percision = 2)
{
    return "$" . number_format($num, $percision);
}

$stmt = "select * from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $results = $query->get_result();
    $rs = $results->fetch_assoc();
    $query->close();
}

$c = $rs["CompanyName"];
$a = $rs["CompanyAddress"];
$csz = $rs["CompanyCity"] . ", " . $rs["CompanyState"] . ". " . $rs["CompanyZip"];

$p = formatphone($rs["CompanyPhone"]);

$logo = "no";
if (!empty($rs['logo'])) {
    $logo = "yes";
    $logopath = $rs['logo'];
}

$extrastr = '';

if (!empty($sd) && !empty($ed)) {
    $extrastr = " and statusdate >= '$sdate' and statusdate <= '$edate'";
}

$stmt = "select distinct customerid from repairorders where shopid = ? and round(balance,2) > 0.01 and status = 'CLOSED' and ucase(rotype) != 'NO PROBLEM' and ucase(rotype) != 'NO APPROVAL' $extrastr order by lastfirst";

$carArr = array();
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $results = $query->get_result();
    if ($results->num_rows > 0) {
        while ($rs = $results->fetch_assoc()) {

            $stmt = "select roid,totalro from repairorders where shopid = ? and round(balance,2) > 0.01 and status = 'CLOSED' and ucase(rotype) != 'NO PROBLEM' and ucase(rotype) != 'NO APPROVAL' and customerid = ? and status = 'CLOSED' $extrastr ";
            if ($rquery = $conn->prepare($stmt)) {
                $rquery->bind_param("si", $shopid, $rs['customerid']);
                $rquery->execute();
                $rresults = $rquery->get_result();
                if ($rresults->num_rows > 0) {
                    while ($rrs = $rresults->fetch_assoc()) {

                        $calcbal = 0;

                        $stmt = "select coalesce(sum(amt),0) as tp from accountpayments where ptype != 'lien' and shopid = ? and roid = ?";
                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("si", $shopid, $rrs["roid"]);
                            $query->execute();
                            $query->bind_result($calcbal);
                            $query->fetch();
                            $query->close();
                        }

                        if ($rrs['totalro'] - $calcbal > 0.01) {
                            $carArr[] = $rs['customerid'];
                            break;
                        }
                    }
                }
            }

        }
    } else {
        die("No ROs Found");
    }
}

$tar = $carArr;
$carStr = implode(",", $carArr);

?>

<html>
<!-- Copyright 2011 - Boss Software Inc. -->
<head>
    <meta name="robots" content="noindex,nofollow">
    <meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
    <meta name="GENERATOR" content="Microsoft FrontPage 12.0">
    <meta name="ProgId" content="FrontPage.Editor.Document">

    <title><?= $shopname ?></title>
    <?php
    get_css_bs5();
    ?>
    <style type="text/css">
        <!--
        A {
            text-decoration: none;
        }

        A:link {
            text-decoration: none;
        }

        A:visited {
            text-decoration: none;
        }

        A:hover {
            text-decoration: none;
        }

        p, td, th, li, body {
            font-size: 10pt;
            font-family: Verdana, Arial, Helvetica
        }

        .style1 {
            text-align: right;
        }

        P.breakhere {
            page-break-after: always
        }

        .style2 {
            border-top: 1px solid #000000;
            text-align: right;
            border-bottom: 1px solid #000000;
        }

        .style3 {
            border-top: 1px solid #000000;
            border-bottom: 1px solid #000000;
        }

        .style4 {
            font-size: xx-small;
        }

        -->
    </style>

</head>
<body>
<?php include(COMPONENTS_PRIVATE_PATH . "/shared/analytics.php"); ?>
<script type="text/javascript">


    //x = setTimeout("closeSupplier()",3000)


    function closeSupplier() {

        document.getElementById("popup").style.display = "none"
        document.getElementById("popuphider").style.display = "none"
        document.getElementById("popupdropshadow").style.display = "none"
        window.print();
        location.href = '../accounttracking.asp'
    }
</script>
<style type="text/css">
    #popup {
        position: absolute;
        top: 100px;
        left: 100px;
        width: 600px;
        height: 500px;
        border: medium navy outset;
        text-align: center;
        color: black;
        display: none;
        z-index: 999;
        background-color: white;
    }

    #popupdropshadow {
        position: absolute;
        top: 115px;
        left: 115px;
        width: 600px;
        height: 500px;
        text-align: center;
        color: black;
        display: none;
        z-index: 998;
        background-color: black;
    }

    #popuphider {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 100%;
        height: 300%;
        background-color: gray;
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
        filter: alpha(opacity=70);
        -moz-opacity: .70;
        opacity: .7;
        z-index: 997;
        display: none;

    }

    .innerBox1 {
        font-weight: bold;
        background-image: url('../reports/newimages/wipheader.jpg');
        color: #FFFFFF;
        text-align: center;
    }

    .innerBox2 {
        background-color: #FFFFFF;
        text-align: center;
    }

    .auto-style1 {
        text-align: center;
        padding: 10px;
        font-size: small;
        background-color: silver;
    }
</style>
<?php
$stmt = "select * from customer where shopid = ? and customerid = ?";
$car_query = $conn->prepare($stmt);
$page_break = false;
foreach ($tar as $car) {
    $customerId = $car;
    if ($car_query->bind_param("si", $shopid, $customerId)) {
        $car_query->execute();
        $results = $car_query->get_result();
        if ($results->num_rows > 0) {
            $rs = $results->fetch_assoc();
            if ($page_break){
                ?>
                <div style="page-break-before:always;"></div>
                    <?php
            }
            $page_break = true;
            ?>
            <table class="table w-100 table-borderless table-sm mb-0">
                <tr>
                    <td style="width: 33%;"><h5><?= strtoupper($c) ?></h5></td>
                    <td style="width: 33%" rowspan="5" valign="top" align="center">
                        <?php
                        if ($logo == "yes") {
                            echo "<img style='max-height:100px; margin-top:10px' src='" . SBP . "upload/" . $shopid . "/" . $logopath . "'>";

                        }
                        ?>
                    </td>
                    <td style="width: 33%" class="style1">Statement Date: <?= date("n/j/Y") ?></td>
                </tr>
                <tr>
                    <td style="width: 33%;"><?= strtoupper($a) ?></td>
                    <td style="width: 33%" class="style1">
                        <?php
                        $ctype = $rs["customertype"];
                        if ($ctype == "" || trim(strtolower($ctype)) == "cash") {
                            echo "Payment Due Date: " . date("n/j/Y");
                        } else {
                            if (right($ctype, 2) == "10" || right($ctype, 2) == "15" || right($ctype, 2) == "30" || right($ctype, 2) == "60" || right($ctype, 2) == "90") {
                                $numdays = right($ctype, 2);
                                $due_date = strtotime("+ " . $numdays . " days");
                                echo "Payment Due Date: " . date("n/j/Y", $due_date);
                            } else {
                                echo "Payment Due Date: " . $ctype;
                            }
                            echo "<br />";
                        }
                        ?>
                    </td>
                </tr>
                <tr>
                    <td style="width: 33%; font-size:12px;"><?= strtoupper($csz) ?>&nbsp;</td>
                    <td style="width: 33%">&nbsp;</td>
                </tr>
                <tr>
                    <td style="width: 33%; font-size:12px;"><?= $p ?>&nbsp;</td>
                    <td style="width: 33%">&nbsp;</td>
                </tr>
            </table>
            <table class="table table-sm table-borderless w-100">
                <tr>
                    <td class="pb-0"><?= $rs["FirstName"] . " " . $rs["LastName"] ?>&nbsp;</td>
                </tr>
                <tr>
                    <td class="pb-0"><?= $rs["Address"] ?>&nbsp;</td>
                </tr>
                <tr>
                    <td class="pb-0"><?= $rs["City"] . ", " . $rs["State"] . ". &nbsp;" . $rs["Zip"] ?>&nbsp;</td>
                </tr>
            </table>
            <br>
            <h6 class="text-center">Repair Orders</h6>


            <table style="width: 100%" cellspacing="0">
                <tr>
                    <td style="width: 25%" class="style3">RO # / Status</td>
                    <td style="width: 13%" class="style3">Date</td>
                    <td style="width: 12%" class="style3">Date Due&nbsp;</td>
                    <td style="width: 40%" class="style3">Vehicle</td>
                    <td style="width: 10%" class="style2">Total</td>
                </tr>
                <?php

                //   $BaseDateTs = strtotime($y . "-" . $m . "-1");
                //   $laststatusdateTS = strtotime("+1 month", $BaseDateTs);
                //   $laststatusdate = date("Y-m-d", $laststatusdateTS);
                $lcntr = 0;
                $rttlro = 0;
                $tbal = 0;
                $stmt = "select * from repairorders where shopid = ? and round(balance,2) > 0.01 and status = 'CLOSED' and ucase(rotype) != 'NO PROBLEM' and ucase(rotype) != 'NO APPROVAL' and customerid = ?  $extrastr";
                $customer_id = $rs['CustomerID'];
                if ($rquery = $conn->prepare($stmt)) {
                    $rquery->bind_param("si", $shopid, $customer_id);
                    $rquery->execute();
                    $rresults = $rquery->get_result();
                    if ($rresults->num_rows > 0) {
                        while ($rrs = $rresults->fetch_assoc()) {
                            $ttlro = 0;
                            $tpmts = 0;
                            $calcbal = 0;
                            $ttlro = $rrs["TotalRO"];

                            $stmt = "select coalesce(sum(amt),0) as tp from accountpayments where ptype != 'lien' and shopid = ? and roid = ?";
                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("si", $shopid, $rrs["ROID"]);
                                $query->execute();
                                $query->bind_result($calcbal);
                                $query->fetch();
                                $query->close();
                            }

                            if ($ttlro - $calcbal < 0.01) continue;

                            if (!empty($rrs['fleetno'])) {
                                $vehdisplay = "#" . $rrs["fleetno"] . " - " . $rrs["VehInfo"];
                            } else {

                                $vehdisplay = $rrs["VehInfo"];
                            }

                            $ctype = $rs["customertype"];
                            if ($ctype == "" || trim(strtolower($ctype)) == "cash") {
                                $pdd = date("n/j/Y", strtotime($rrs["StatusDate"]));
                            } else {
                                if (right($ctype, 2) == "10" || right($ctype, 2) == "15" || right($ctype, 2) == "30") {
                                    $numdays = right($ctype, 2);
                                    $statusDate = $rrs["StatusDate"];
                                    $statusDateTS = strtotime($statusDate);
                                    $pddTs = strtotime("+ " . $numdays . " days", $statusDateTS);
                                    $pdd = date("n/j/Y", $pddTs);
                                } else {
                                    $pdd = $ctype;
                                }
                            }

                            ?>
                            <tr>
                                <td style="width: 25%"><b><?= $rrs["ROID"] . " / " . $rrs["Status"] ?></b>&nbsp;</td>
                                <td style="width: 13%"><b><?= date("n/j/Y", strtotime($rrs["FinalDate"])); ?></b>&nbsp;</td>
                                <td style="width: 12%"><?= $pdd ?>&nbsp;</td>
                                <td style="width: 40%"><b><?= $vehdisplay ?></b>&nbsp;</td>
                                <td style="width: 10%" class="style1"><?= format_currency($ttlro, 2) ?>&nbsp;</td>
                            </tr>


                            <?php
                            //***** get payment info and calc the balance
                            $runningtpmts = 0;
                            $roid = $rrs['ROID'];
                            $astmt = "select * from accountpayments where ptype != 'lien' and shopid = ? and roid = ?";
                            if ($aquery = $conn->prepare($astmt)) {
                                $aquery->bind_param("si", $shopid, $roid);
                                $aquery->execute();
                                $aresults = $aquery->get_result();
                                $tpmts = doubleval(0);
                                while ($ars = $aresults->fetch_assoc()) {
                                    $tpmts += $ars["amt"];
                                    $lcntr++;
                                    $runningtpmts += $ars["amt"];
                                    ?>
                                    <tr>
                                        <td style="width: 25%" class="style4">Payment: <?= date('n/j/Y', strtotime($ars["pdate"])) ?>&nbsp;</td>
                                        <td style="width: 25%" class="style4" colspan="2">Paid by:<?= $ars["ptype"] ?>&nbsp;
                                        </td>
                                        <td style="width: 40%" class="style4">Number:<?= $ars["pnumber"] ?>&nbsp;</td>
                                        <td style="width: 10%" class="style1">-<span
                                                    class="style4"><?= format_currency($ars["amt"], 2) ?>&nbsp;</span></td>
                                    </tr>
                                    <?php
                                }
                            }
                            $tbal = $tbal + $ttlro - $tpmts;
                            ?>
                            <tr>
                                <td style="border-bottom:1px black solid;width: 25%"><b>PO
                                        Number:<?= $rrs["ponumber"] ?> </b>
                                </td>
                                <td style="border-bottom:1px black solid;width: 25%" colspan="2">&nbsp;</td>
                                <td style="border-bottom:1px black solid;width: 40%" class="style1">
                                    Balance:
                                </td>
                                <td style="border-bottom:1px black solid;width: 10%" class="style1">
                                    <?= format_currency($ttlro - $tpmts, 2) ?>&nbsp;
                                </td>
                            </tr>
                            <tr>
                                <td style="border-bottom:1px black solid;width: 25%">&nbsp;</td>
                                <td style="border-bottom:1px black solid;width: 25%" colspan="2">&nbsp;</td>
                                <td style="border-bottom:1px black solid;width: 40%" class="style1">
                                    Running Balance:
                                </td>
                                <td style="border-bottom:1px black solid;width: 10%" class="style1">
                                    <?= format_currency($tbal, 2) ?>&nbsp;
                                </td>
                            </tr>
                            <?php
                            $rttlro += $ttlro;
                        }
                    }
                    $rquery->close();
                }

                ?>
            </table>

            <br>
            <?php

            $runningtpmts = (double)0;
            $lcntr = 0;
            $stmt = "select * from ps where shopid = ? and balance > 0.01 and cid = ? and `status` = 'CLOSED' $extrastr";
            if ($pquery = $conn->prepare($stmt)) {
                $pquery->bind_param("si", $shopid, $customer_id);
                $pquery->execute();
                $presults = $pquery->get_result();
                if ($rrs = $presults->num_rows > 0) {
                    ?>
                    <h6 class="text-center">Part Sales</h6>
                    <table class="table table-sm table-borderless w-100">
                    <tr>
                        <td style="width: 25%" class="style3">INV # / Status</td>
                        <td style="width: 13%" class="style3">Date</td>
                        <td style="width: 12%" class="style3">Date Due&nbsp;</td>
                        <td style="width: 25%" class="style3"></td>
                        <td style="width: 10%" class="style2">Total</td>
                    </tr>
                    <?php
                    while ($rrs = $presults->fetch_assoc()) {

                        $ttlro = 0;
                        $tpmts = 0;
                        $ttlro += $rrs["total"];

                        $ctype = $rs["customertype"];
                        if ($ctype = "" || trim(strtolower($ctype)) == "cash") {
                            $pdd = date("n/j/Y", strtotime($rrs["statusdate"]));
                        } else {
                            if (right($ctype, 2) == "10" || right($ctype, 2) == "15" || right($ctype, 2) == "30") {
                                $numdays = right($ctype, 2);
                                $statusDateTs = strtotime($rrs["statusdate"]);
                                $pdd = strtotime("+ " . $numdays . " days", $statusDateTS);
                                $pdd = date("n/j/Y", $pdd);
                            } else {
                                $pdd = $ctype;
                            }
                        }
                        ?>
                        <tr>
                            <td style="width: 25%"><b><?= $rrs["psid"] . " / " . $rrs["status"] ?></b>&nbsp;</td>
                            <td style="width: 13%"><b><?= date("n/j/Y", strtotime($rrs["psdate"])); ?></b>&nbsp;</td>
                            <td style="width: 12%"><?= $pdd ?>&nbsp;</td>
                            <td style="width: 25%"></td>
                            <td style="width: 10%" class="style1"><?= format_currency($rrs["total"]) ?>&nbsp;</td>
                        </tr>
                        <?php
                        $astmt = "select * from `accountpayments-ps` where shopid = ? and psid = ?";
                        if ($aquery = $conn->prepare($astmt)) {
                            $aquery->bind_param("si", $shopid, $rrs['psid']);
                            $aquery->execute();
                            $aresults = $aquery->get_result();
                            $tpmts = (double)0;
                            while ($ars = $aresults->fetch_assoc()) {
                                $tpmts += $ars["amt"];
                                $runningtpmts += $ars["amt"];
                                $lcntr++;
                                ?>
                                <tr>
                                    <td style="width: 25%" class="style4">Payment: <?= date('n/j/Y', strtotime($ars["pdate"])) ?>&nbsp;</td>
                                    <td style="width: 25%" class="style4" colspan="2">Paid by:<?= $ars["ptype"] ?>&nbsp;
                                    </td>
                                    <td style="width: 40%" class="style4">Number:<?= $ars["pnumber"] ?>&nbsp;</td>
                                    <td style="width: 10%" class="style1">-<span
                                                class="style4"><?= format_currency($ars["amt"]) ?>&nbsp;</span></td>
                                </tr>
                                <?php
                            }
                        }

                        if ($rrs["ponumber"] != "0") {
                            ?>
                            <tr>
                                <td class="style4" colspan="3"><b>PO Number: <?= $rrs["ponumber"] ?></b>&nbsp;</td>
                                <td style="width: 40%" class="style4">&nbsp;</td>
                                <td style="width: 10%" class="style1">&nbsp;</td>
                            </tr>
                            <?php
                        }
                        ?>
                        <tr>
                            <td style="border-bottom:1px black solid;width: 25%">&nbsp;</td>
                            <td style="border-bottom:1px black solid;width: 25%" colspan="2">&nbsp;</td>
                            <td style="border-bottom:1px black solid;width: 40%" class="style1">
                                Balance:
                            </td>
                            <td style="border-bottom:1px black solid;width: 10%" class="style1">
                                <?= format_currency($rrs["balance"], 2) ?>
                                &nbsp;
                            </td>
                        </tr>
                        <?php
                        $tbal += $ttlro;
                    }
                }
            }
            ?>

            </table>

            <div style="font-size:medium;text-align:center;">Total Due: <?= format_currency($tbal, 2) ?><br></div>
            <hr class="hr-blurry d-print-none"/>
            <?php

            $runningpmts = 0;
            $rttlro = 0;
            $tbal = 0;
        }
    }
}

$tbal = 0;
$ttlro = 0;
$rttlro = 0;
$tlist = array();
$list = "";
$stmt = "select distinct cid from ps where cid not in(?) and shopid = ? and balance > 0.01 and status != 'dead' $extrastr";
if ($cquery = $conn->prepare($stmt)) {
    $cquery->bind_param("ss", $carStr, $shopid);
    $cquery->execute();
    $cresults = $cquery->get_result();
    while ($cirs = $cresults->fetch_assoc()) {
        $tlist [] = $cirs['cid'];
    }
    $tlistStr = implode(",", $tlist);
}


$cstmt = "select * from customer where shopid = ? and customerid = ?";
$cquery = $conn->prepare($cstmt);

$page_break = false;
foreach ($tlist as $tar) {
    $customer_id = $tar;
    if ($cquery->bind_param("si", $shopid, $customer_id)) {
        $cquery->execute();
        $cresults = $cquery->get_result();
        if ($cresults->num_rows > 0) {

            $rs = $cresults->fetch_assoc();
            if ($page_break){
                ?>
                <div style="page-break-before:always;"></div>
                <?php
            }
            $page_break = true;
            ?>
            <table class="table table-sm table-borderless w-100">
                <tr>
                    <td style="width: 45%; font-size:16px;"><h5><?= strtoupper($c); ?></h5></td>
                    <td style="width: 33%" rowspan="5" valign="top">
                        <?php
                        if ($logo == "yes") {
                            echo "<img style='max-height:100px; margin-top:10px' src='" . SBP . "upload/" . $shopid . "/" . $logopath . "'>";
                        }
                        ?>
                    </td>
                    <td style="width: 33%" class="style1">Statement Date: <?= date("n/j/Y") ?></td>
                </tr>
                <tr>
                    <td style="width: 45% ; font-size:12px;"><?= strtoupper($a) ?>&nbsp;</td>
                    <td style="width: 50%">&nbsp;</td>
                </tr>
                <tr>
                    <td style="width: 45%; font-size:12px;"><?= strtoupper($csz) ?>&nbsp;</td>
                    <td style="width: 50%">&nbsp;</td>
                </tr>
                <tr>
                    <td style="width: 45%; font-size:12px;"><?= $p ?>&nbsp;</td>
                    <td style="width: 50%">&nbsp;</td>
                </tr>
                <tr>
                    <td style="width: 45%; font-size:12px;">&nbsp;</td>
                    <td style="width: 50%">&nbsp;</td>
                </tr>
            </table>
            <table class="table table-sm table-borderless w-100">
                <tr>
                    <td class="pb-0"><?= $rs["FirstName"] . " " . $rs["LastName"] ?>&nbsp;</td>
                </tr>
                <tr>
                    <td class="pb-0"><?= $rs["Address"] ?>&nbsp;</td>
                </tr>
                <tr>
                    <td class="pb-0"><?= $rs["City"] . ", " . $rs["State"] . ". &nbsp;" . $rs["Zip"] ?>&nbsp;</td>
                </tr>
            </table>
            <br>
            <h6 class="text-center">Repair Orders</h6>

            <table class="table table-sm table-bordlerless w-100">
                <tr>
                    <td style="width: 25%" class="style3">RO # / Status</td>
                    <td style="width: 25%" class="style3">Date</td>
                    <td style="width: 40%" class="style3">Vehicle</td>
                    <td style="width: 10%" class="style2">Total</td>
                </tr>
                <?php
                $stmt = "select * from repairorders where shopid = ? and balance > 0.01 and customerid = ? and status = 'CLOSED' $extrastr";
                if ($rquery = $conn->prepare($stmt)) {
                    $rquery->bind_param("si", $shopid, $customer_id);
                    $rquery->execute();
                    $rresults = $rquery->get_result();
                    $tbal = 0;
                    $tpmts = 0;
                    $lcntr = 0;
                    if ($rresults->num_rows > 0) {
                        while ($rrs = $rresults->fetch_assoc()) {
                            $ttlro = 0;
                            $tpmts = 0;
                            $ttlro = $rrs["TotalPrts"] + $rrs["TotalLbr"] + $rrs["TotalSublet"] + $rrs["HazardousWaste"] + $rrs["UserFee1"] + $rrs["UserFee2"] + $rrs["UserFee3"] + $rrs["SalesTax"] - $rrs["DiscountAmt"];
                            ?>
                            <tr>
                                <td style="width: 25%"><b><?= $rrs["ROID"] . " / " . $rrs["Status"] ?></b>&nbsp;
                                </td>
                                <td style="width: 25%"><b><?= $rrs["FinalDate"] ?></b>&nbsp;</td>
                                <td style="width: 40%"><b><?= $rrs["VehInfo"] ?></b>&nbsp;</td>
                                <td style="width: 10%" class="style1"><?= format_currency($ttlro, 2) ?>&nbsp;</td>
                            </tr>
                            <?php
                            $astmt = "select * from accountpayments where shopid = ? and roid = ?";
                            if ($aquery = $conn->prepare($astmt)) {
                                $aquery->bind_param("si", $shopid, $rrs['ROID']);
                                $aquery->execute();
                                $aresults = $aquery->get_result();
                                if ($aresults->num_rows > 0) {
                                    $tpmts = (double)0;
                                    while ($ars = $aresults->fetch_assoc()) {
                                        $tpmts += $ars["amt"];
                                        $lcntr += 1;
                                        $runningtpmts += $ars["amt"];
                                        ?>
                                        <tr>
                                            <td style="width: 25%" class="style4">Payment: <?= date('n/j/Y', strtotime($ars["pdate"])) ?>
                                                &nbsp;
                                            </td>
                                            <td style="width: 25%" class="style4">Paid by:<?= $ars["ptype"] ?>
                                                &nbsp;
                                            </td>
                                            <td style="width: 40%" class="style4">Number:<?= $ars["pnumber"] ?>
                                                &nbsp;
                                            </td>
                                            <td style="width: 10%" class="style1">-<span
                                                        class="style4"><?= format_currency($ars["amt"], 2) ?>&nbsp;</span>
                                            </td>
                                        </tr>
                                        <?php

                                    }
                                }
                            }
                            ?>
                            <tr>
                                <td style="border-bottom:1px black solid;width: 25%">&nbsp;</td>
                                <td style="border-bottom:1px black solid;width: 25%">&nbsp;</td>
                                <td style="border-bottom:1px black solid;width: 40%" class="style1">
                                    Balance:
                                </td>
                                <td style="border-bottom:1px black solid;width: 10%" class="style1">
                                    <?= format_currency($ttlro - $tpmts, 2) ?>
                                    &nbsp;
                                </td>
                            </tr>
                            <?php
                            $tbal = $tbal + $ttlro - $tpmts;
                            $rttlro += +$ttlro;

                        }
                    }
                }
                ?>
            </table>

            <br>
            <?php
            $runningpmts = (double)0;
            $stmt = "select * from ps where shopid = ? and balance > 0.01 and cid = ? and status != 'dead' $extrastr";
            if ($rquery = $conn->prepare($stmt)) {
                $rquery->bind_param("si", $shopid, $customer_id);
                $rquery->execute();
                $rresults = $rquery->get_result();
                if ($rresults->num_rows > 0) {
                    ?>
                    <h6 class="text-center">Part Sales</h6>
                    <table class="table table-sm table-borderless w-100">
                    <tr>
                        <td style="width: 25%" class="style3">INV # / Status</td>
                        <td style="width: 25%" class="style3">Date</td>
                        <td style="width: 25%" class="style3"></td>
                        <td style="width: 10%" class="style2">Total</td>
                    </tr>
                    <?php
                    while ($rrs = $rresults->fetch_assoc()) {
                        $ttlro += $rrs["total"];
                        ?>
                        <tr>
                            <td style="width: 25%"><b><?= $rrs["psid"] . " / " . $rrs["status"] ?></b>&nbsp;</td>
                            <td style="width: 25%"><b><?= date('n/j/Y', strtotime($rrs["statusdate"])) ?></b>&nbsp;</td>
                            <td style="width: 25%"></td>
                            <td style="width: 10%" class="style1"><?= format_currency($rrs["total"], 2) ?>&nbsp;</td>
                        </tr>
                        <?php
                        $tpmts = (double)0;
                        $astmt = "select * from `accountpayments-ps` where shopid = ? and psid = ?";
                        if ($aquery = $conn->prepare($astmt)) {
                            $aquery->bind_param("si", $shopid, $rrs['psid']);
                            $aquery->execute();
                            $aresults = $aquery->get_result();
                            while ($ars = $aresults->fetch_assoc()) {
                                $tpmts += $ars["amt"];
                                $runningtpmts += $ars["amt"];
                                $lcntr++;
                                ?>
                                <tr>
                                    <td style="width: 25%" class="style4">Payment: <?= date('n/j/Y', strtotime($ars["pdate"])) ?>&nbsp;</td>
                                    <td style="width: 25%" class="style4">Paid by:<?= $ars["ptype"] ?>&nbsp;</td>
                                    <td style="width: 40%" class="style4">Number:<?= $ars["pnumber"] ?>&nbsp;</td>
                                    <td style="width: 10%" class="style1">-<span
                                                class="style4"><?= format_currency($ars["amt"], 2) ?>&nbsp;</span>
                                    </td>
                                </tr>
                                <?php
                            }
                        }

                        if ($rrs["ponumber"] != "0") {
                            ?>
                            <tr>
                                <td class="style4" colspan="2"><b>PO Number: <?= $rrs["ponumber"] ?></b>&nbsp;</td>
                                <td style="width: 40%" class="style4">&nbsp;</td>
                                <td style="width: 10%" class="style1">&nbsp;</td>
                            </tr>
                        <?php } ?>
                        <tr>
                            <td style="border-bottom:1px black solid;width: 25%">&nbsp;</td>
                            <td style="border-bottom:1px black solid;width: 25%">&nbsp;</td>
                            <td style="border-bottom:1px black solid;width: 40%" class="style1">
                                Balance:
                            </td>
                            <td style="border-bottom:1px black solid;width: 10%" class="style1">
                                <?= format_currency($rrs["balance"], 2) ?>
                                &nbsp;
                            </td>
                        </tr>
                        <?php

                        $tbal += $rrs["balance"];
                    }
                }
                ?>
                </table>

                <div style="font-size:medium;text-align:center;">Total Due: <?= format_currency($tbal, 2) ?><br></div>
                <div style="page-break-before:always;"></div>
                <?php

                $runningpmts = 0;
                $rttlro = 0;
                $tbal = 0;
            }
        }
    }
}
?>


</body>
</html>
<?php
//Copyright 2011 - Boss Software Inc.
?>
