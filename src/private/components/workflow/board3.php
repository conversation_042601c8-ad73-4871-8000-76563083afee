<?php

require CONN;
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
$shopid = $_COOKIE['shopid'];

$stmt = "select count(*) c from kanbanstatuses where shopid = ?";

if ($query = $conn->prepare($stmt)){
	$query->bind_param("s",$shopid);
	$query->execute();
	$query->bind_result($countstatus);
	$query->fetch();
	$query->close();
}

if ($countstatus == 0){
	header ("Location: settings.php");
	exit;
}


function getContrastColor($hexcolor) {
	if ($hexcolor != ""){
	    $r = hexdec(substr($hexcolor, 1, 2));
	    $g = hexdec(substr($hexcolor, 3, 2));
	    $b = hexdec(substr($hexcolor, 5, 2));
	    $yiq = (($r * 299) + ($g * 587) + ($b * 114)) / 1000;
	    return ($yiq >= 128) ? 'black' : 'white';
	}else{
		return "black";
	}
}

function validateDate($date, $format = 'Y-m-d H:i:s'){
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) == $date;
}

function seconds2human($ss) {

	$time=$ss; //whatever
	$seconds = $time%60;
	$mins = floor($time/60)%60;
	$hours = floor($time/60/60)%24;
	$days = floor($time/60/60/24);
	
	if ($days > 0){
		return "$days:$hours:$mins";
	}else{
		return "$hours:$mins";
	}
}

$stmt = "select companyemail,definvmsgemail,requirebalancero,requirepayments,requiresource,requireoutmileage,companyname,sortwipbylastfirst,firstlastonwip,showpromiseonwip,nexpartpassword as showcommlog,mailpassword as showbalanceonwip,showemailestimateonwip, showemailinvoiceonwip, showtimeclockonwipdata, showpaymentonwip, showinspectiononwip, showinpectionemailonwip, showtechoverhours,nexpartusername showpics,shopmgr from company where shopid = ?";
//echo $stmt.";<BR>";
if($query = $conn->prepare($stmt)){
	$query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyemail,$definvmsgemail,$requirebalancero,$requirepayments,$requiresource,$requireoutmileage,$shopname,$sortwipbylastfirst,$firstlastonwip,$showpromiseonwip,$showcommlog,$showbalanceonwip,$showestemail,$showinvemail,$showtechtime,$showmoney,$showinsp,$showinspemail,$showtechoverhours,$showpics,$showelapsed);
	$query->fetch();
    $query->close();
}else{
	echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

if ($definvmsgemail == ""){
	$definvmsgemail = "Your repair order #roid# from $shopname is attached.  Please look it over and let us know if you have any questions. $shopphone";
}

$stmt = "SELECT emailinvoice,emailinvoicesubject,emailestimate,emailestimatesubject,emailesigrequest,emailesigrequestsubject from companymessages WHERE shopid = '$shopid'";
if ($query = $conn->prepare($stmt)){
	$query->execute();
	$query->store_result();
	$nummsgrows = $query->num_rows;
	if ($nummsgrows > 0){
		$query->free_result();
		$query->execute();
		$query->bind_result($emailinvoice,$emailinvoicesubject,$emailestimate,$emailestimatesubject,$emailesigrequest,$emailesigrequestsubject);
		$query->fetch();
	}else{
		$emailinvoice = str_replace("[shopname]",$_COOKIE['shopname'],$definvmsgemail);
		$emailinvoicesubject = 'Your repair invoice #roid# from '.$_COOKIE['shopname'];
		$emailestimate = str_replace("[shopname]",$_COOKIE['shopname'],$definvmsgemail);
		$emailestimatesubject = 'Your repair estimate #roid# from '.$_COOKIE['shopname'];
		$emailesigrequest = $_COOKIE['shopname'].' is requesting an E-Signature on a document. Please click the link below to E-Sign the document. Thank you for your business!';
		$emailesigrequestsubject = 'A Request for an E-Signature from '.$_COOKIE['shopname'];
	}
	$query->close();
}

?>
<!DOCTYPE html>
<html>
<head>
    <script type="text/javascript">
        var timerStart = Date.now();
    </script>
    <title>Adding Items with Kanban Header</title>
    <meta  name = "viewport" content = "initial-scale = 1.0, maximum-scale = 1.0, user-scalable = no">

	<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="https://<?= $_SERVER['SERVER_NAME'] ?>/src/public/assets/workflow/codebase/webix.css?v=7.1.1">
    <link rel="stylesheet" type="text/css" href="https://<?= $_SERVER['SERVER_NAME'] ?>/src/public/assets/workflow/codebase/kanban.css?v=7.1.1">
    <link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui_nobutton.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css">
    <link rel="stylesheet" href="<?= SCRIPT ?>/plugins/tooltipster/tooltipster.bundle.min.css">
	<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/countdown/jquery.countdown.css">
	
	<script src="https://kit.fontawesome.com/3f5f86d0d1.js" crossorigin="anonymous"></script>
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
	<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js"></script>
	<script src="//cdn.rawgit.com/saribe/eModal/1.2.67/dist/eModal.min.js"></script>
	
	
	
	<!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
	<script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
	<script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
	<script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
	<script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
	<script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
	<script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
	<script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
	<script src="<?= SCRIPT ?>/plugins/tooltipster/tooltipster.bundle.min.js"></script>
	<script src="<?= SCRIPT ?>/app.js"></script>
	<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
	<script type="text/javascript" src="<?= SCRIPT ?>/jquery.prettyPhoto.js"></script>
	<script src="<?= SCRIPT ?>/plugins/countdown/jquery.plugin.js"></script>
	<script src="<?= SCRIPT ?>/plugins/countdown/jquery.countdown.min.js"></script>

    <script src="https://<?= $_SERVER['SERVER_NAME'] ?>/src/public/assets/workflow/codebase/webix.js?v=7.1.1" type="text/javascript"></script>
    <script src="https://<?= $_SERVER['SERVER_NAME'] ?>/src/public/assets/workflow/codebase/kanban.js?v=7.1.1" type="text/javascript"></script>
    <style>
    	
    	
    	
    	.col-sm-4, .col-sm-6, .col-sm-9, .col-sm-12{
			overflow:hidden !important;
			font-size:10pt;
		}
		.text{
			background-color:red;
		}
		.kbi-account{
			display:none;
		}
		.webix_kanban_user_avatar{
			display:none;
			
		}
		.btn{
			margin:2px;
			font-weight:500
		}
		.webix_kanban_body{
			margin:0px;
			width:100%;
			padding-right:6px;
		}
		.webix_kanban_footer{
			display:none !important;
		}
		.row{
			/*border-bottom:1px #EFEFEF solid*/
		}
		
		.hover4{
			font-size:10pt;
			/*border:1px solid silver;*/
			
}
.url{
	color:#336699;
	text-decoration:underline
}
.header{
	width:98%;
	margin:auto;
	padding:5px;
	background-color:#EBEDF0;
}
body{
	background-color:#EBEDF0;
}

.btn-caution-gp{
	background-color:#FFFF99;
	color:black;
	float:right;
	/*opacity: .5;*/
}

.btn-danger-gp{
	background-color:#FF0000;
	color:white;
	float:right;
	/*opacity: .5;*/
}

.btn-success-gp{
	background-color:#009933;
	color:white;
	float:right;
	/*opacity: .5;*/
}

.fas{
	margin-right:10px;
	margin-left: 10px;
	font-size:24pt;
}

.fas-small{
	font-size: 12pt;
	margin-left: 15px;
}

#spinner{
	width:75px;
	display:none;
	height: 75px;
	margin-left:auto;
	top:300px;
}

.tooltip-inner{
    min-width: 250px;  /*the minimum width */
	max-height: 200px;
	overflow-y: scroll;
}
.table-responsive .table {
	overflow-x:scroll;
    max-width: 100%;
    -webkit-overflow-scrolling: touch !important;
}

.is-countdown {

	border: 0px !important;
	background-color: transparent !important;

<?php
$ds = array();
$stmt = "select status,color from kanbanstatuses where shopid = ? order by `order`";
//echo $stmt.";<BR>";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("s",$shopid);
	$query->execute();
	$r = $query->get_result();
	while ($rs = $r->fetch_assoc()){
		$ds[] = $rs;
		echo ".".strtoupper(str_replace(" ","_",$rs['status']))."css{ background-color:".$rs['color']."}\r\n";
	}
}

?>

    </style>
</head>

<?php

$v = '
    webix.ui({
        view: "kanban",
        id: "myBoard",
        type: "wide",
        save:function(id, operation){
        	var item = this.getItem(id);
        	if (item){
	        	status = item.status
	        	data = '.json_encode($ds).'
	        	for (i=0;i<data.length;i++){
	        		//status = status.toLowerCase()
	        		if (data[i].status == status){
	        			color = data[i].color
	        			break
	        		}
	        	}
	        	ds = "t=updateitem&id="+id+"&shopid='.$shopid.'&status="+status
	        	$.ajax({
	        		data: ds,
	        		url: "saveboard.php",
	        		type: "post",
	        		success: function(r){
	        			if (r == "success"){
							var citem = $$("myBoard").getItem(id); // 3 is the item ID
							citem.color = color;
					    	$$("myBoard").refresh(id);
						}
	        		},
					error: function (xhr, ajaxOptions, thrownError) {
						console.log(xhr.status);
						console.log(xhr.responseText);
						console.log(thrownError);
					}       		
	        	})
	        }
        },
		on:{
		    onListAfterDrop: dragStop
		},        
        cols:[
        
';

// check for any open RO's that are not here
$rocsv = "";
$stmt = "select roid from kanbandata where shopid = ?";
//echo $stmt.";<BR>";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("s",$shopid);
	$query->execute();
	$r = $query->get_result();
	while ($rs = $r->fetch_assoc()){
		$rocsv .= $rs['roid'].",";
	}
	$query->close();
}
if (substr($rocsv,-1) == ","){
	$rocsv = substr($rocsv,0,strlen($rocsv)-1);
}

if ($rocsv == ""){
	$rocsv = "0000";
}


// use $rocsv to see if any are not in the kanbandata
$stmt = "select count(*) c from repairorders where roid not in ($rocsv) and shopid = ? and status != 'closed' and rotype != 'no approval'";
//echo $stmt.";<BR>";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("s",$shopid);
	$query->execute();
	$query->bind_result($openrocount);
	$query->fetch();
	$query->close();
}

if ($openrocount > 0){
	$header = '
	  {
	    type:"clean",
	    rows:[
	      {template:"******* UNCATEGORIZED *********", type:"header"},
	      {body:{ type: "clean", view:"kanbanlist", status:"UNCATEGORIZED", $css: "test" }}
	    ]
	  },
	';
}else{
	$header = "";
}

// now check for any closed that need to be removed
$stmt = "select roid from repairorders where roid in ($rocsv) and shopid = ? and status = 'closed' and rotype != 'no approval'";
//echo $stmt.";<BR>";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("s",$shopid);
	$query->execute();
	$r = $query->get_result();
	while ($rs = $r->fetch_assoc()){
		
		// any roid found here needs to be deleted from the kanbandata table
		$dstmt = "delete from kanbandata where shopid = ? and roid = ?";
		//echo "delete from kanbandata where shopid = '$shopid' and roid = $roid";
		if ($dquery = $conn->prepare($dstmt)){
			$dquery->bind_param("si",$shopid,$rs['roid']);
			$dquery->execute();
			$conn->commit();
			$dquery->close();
		}
		
	}
}

foreach ($ds as $hval){

	$header .= '
	  {
	    type:"clean",
	    rows:[
	      {template:"'.strtoupper($hval['status']).'", type:"header"},
	      {body:{ type: "clean", view:"kanbanlist", status:"'.strtoupper($hval['status']).'", $css: "'.str_replace(" ","_",strtoupper($hval['status'])).'css" }}
	    ]
	  },
	';


}
$v .= $header."],";
$displaytime = "";

$v .= "\r\n\t\tdata:[";
$roidar = array();

// get the ros not in the kanbandata table
if ($openrocount > 0){
	
	$stmt = "SELECT writer,datein,timein,datetimepromised,balance,vehlicense,vin,ro.cellphone,ro.customerphone,"
	. "ro.customerwork,ro.customerfirst,ro.customerlast,ro.email,ro.totalro,ro.customerid,ro.shopid,ro.roid,"
	. "ro.customer,ro.vehinfo,ro.rotype FROM repairorders ro"
	. " WHERE ro.shopid = ? and ro.roid not in ($rocsv) and status != 'closed' and rotype != 'no approval'";
	//echo $stmt.";<BR>";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
		$query->execute();
		$r = $query->get_result();
		while ($rs = $r->fetch_assoc()){
		
			// get the techs on labor
			$techar = array();
			$tstmt = "select distinct tech from labor where shopid = '$shopid' and roid = ".$rs['roid'];
			if ($tquery = $conn->prepare($tstmt)){
				//$tquery->bind_param("si",$shoid,$rs['roid']);
				if ($tquery->execute()){
					$tr = $tquery->get_result();
					while ($trs = $tr->fetch_assoc()){
						array_push($techar,$trs['tech']);
					}
				}else{
					echo $conn->error;
				}
			}
			array_push($roidar,$rs['roid']);
			
			
	
			$cardbody = "<div class='row'>"
			. "<div class='col' style='font-size:12pt'>".strtoupper($rs['customerlast'].", ".$rs['customerfirst'])."</div>"
			. "<div class='col'><span style='padding:1px 5px 1px 5px;color:;background-color:' class=''>".$rs['rotype']."</span></div>"
			. "<div class='col text-right' style='font-size:12pt'>RO #".$rs['roid']."</div></div>"
			. "<div class='row'>"
			. "<div class='col-sm-12 text-center' style='font-size:12pt'>".$rs['vehinfo'];
			if (strlen($rs['vin']) > 5){
				$cardbody .= " - ".$rs['vin'];
			}
	
			$cardbody .= "</div></div>"
			
			. "<div class='row'>"
			. "<div class='col hover4 text-left'><span onclick='sendEmailMessage(".$rs['roid'].")' class='url'>".strtoupper($rs['email'])."</span></div>"
			. "<div class='col hover4 text-center'><span onclick='showPhones(".$rs['roid'].")' class='url'>PHONES</span></div>"
			. "</div>"
			. "<div class='row'>"
			. "<div class='col'><b>IN:</b>".date("m/d/Y",strtotime($rs['datein']))
			. "</div>"
			. "<div class='col'><b>PD:</b>"
			. "</div>"
			. "<div class='col'><b>ET:</b>"
			. "</div>"
			. "</div>"
			. "<div class='row'>"
			. "<div class='col-sm-12'><b>ACTIVITY:</b>";
	
			$cardbody .= "</div>"
			. "</div>"
			. "<div class='row'>"
			. "<div class='col-sm-12'><b>WRITER:</b> ".strtoupper($rs['writer'])
			. "</div>"
			. "</div>"
			. "<div class='row'>"
			. "<div class='col-sm-12'><b>TECH(S):</b> ";
			$techs = "";
			foreach ($techar as $tech){
				$techs .= $tech." <b>/</b> ";
			}
			if (substr($techs,-2) == ", "){
				$techs = substr($techs,0,strlen($techs)-2);
			}
			$cardbody .= $techs. "</div>"
			. "</div>";
	
	
			
			
			$v .= '
				{ id:'.$rs['roid'].', status:"UNCATEGORIZED", text:"'.$cardbody.'", color:"purple" },
			';
	
		}
		
		
	}
}



$stmt = "SELECT ro.estimateemailed,ro.invoiceemailed,ro.inspectionemailed,ro.tagnumber,rt.colorcode,ro.subtotal,k.vorder,ka.`status`,writer,datein,timein,datetimepromised,balance,vehlicense,vin,ro.cellphone,ro.customerphone,ro.customerwork,ro.customerfirst,ro.customerlast,ro.email,ro.totalro,ro.customerid,ro.shopid,ro.roid,ro.customer,ro.vehinfo,k.kanbanstatus,ka.color,ro.rotype,ro.gp FROM kanbandata k"
. " INNER JOIN repairorders ro ON k.shopid = ro.shopid AND k.roid = ro.roid"
. " INNER JOIN kanbanstatuses ka ON k.shopid = ka.shopid AND k.kanbanstatus = ka.`status`"
. " INNER JOIN rotype rt on k.shopid = rt.shopid and ro.rotype = rt.rotype"
. " WHERE k.shopid = ? order by k.kanbanstatus, k.vorder;";
//echo $stmt.";<BR>";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("s",$shopid);
	$query->execute();
	$r = $query->get_result();
	while ($rs = $r->fetch_assoc()){
	
		array_push($roidar,$rs['roid']);
		
    	if ($showelapsed == "yes"){
	    	$startdatetime = strtotime($rs['datein']." ".$rs['timein']);
	    	$currdatetime = strtotime(localTimeStamp($shopid));
	    	$numsecs = $currdatetime - $startdatetime;
	    	$displaytime = seconds2human($numsecs);
	    }else{
	    	$displaytime = "";
	    }

		$moneyrecd = 0;
		if (strtoupper($showmoney) == "YES"){
			$mstmt = "select count(*) as c from accountpayments where shopid = ? and roid = ?";
			if($mquery = $conn->prepare($mstmt)){
				$mquery->bind_param("si",$shopid,$rs['roid']);
			    $mquery->execute();
			    $mquery->bind_result($moneyrecd);
				$mquery->fetch();
			    $mquery->close();
			}else{
				echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}

		if (strtoupper($showpics) == "YES"){
			$mstmt = "select count(*) as c from repairorderpics where shopid = ? and roid = ?";
			if($mquery = $conn->prepare($mstmt)){
				$mquery->bind_param("si",$shopid,$rs['roid']);
			    $mquery->execute();
			    $mquery->bind_result($countpics);
				$mquery->fetch();
			    $mquery->close();
			}else{
				echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}else{
			$countpics = 0;
		}
		
		// check for an inspection completed
		
		$compcountinsp = 0;
		if (strtolower($showinsp) == "yes"){
			$icstmt = "select count(*) c from roinspectionheader where shopid = '$shopid' and completiondate != '' and roid = " . $rs['roid'] ;
			if ($icquery = $conn->prepare($icstmt)){
				$icquery->execute();
				$icquery->bind_result($compcountinsp);
				$icquery->fetch();
				$icquery->close();
			}
		}

		$tcroid = "no";
		if (strtoupper($showtechtime) == "YES"){
			$tcstmt = "select roid from labortimeclock where shopid = ? and roid = ? and enddatetime is null";
			if($tcquery = $conn->prepare($tcstmt)){
				$tcquery->bind_param("si",$shopid,$rs['roid']);
			    $tcquery->execute();
			    $tcquery->bind_result($tcroid);
				$tcquery->fetch();
			    $tcquery->close();
			}else{
				echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
		}
		$commstr = "";
		if ($showcommlog == "showcommlogyes"){

			// check for comms
			$commstmt = "select `datetime`,replace(`comm`,'\"',\"'\") as comm from repairordercommhistory where `comm` != 'ADVISOR COMMENTS UPDATED' and `comm` != 'TECH STORY UPDATED' and shopid = '$shopid' and roid = ".$rs['roid']." order by datetime desc limit 5;";
			if ($commquery = $conn->prepare($commstmt)){
				$commquery->execute();
				$commr = $commquery->get_result();
				while ($commrs = $commr->fetch_array()){
					$commstr .= date("m/d/Y",strtotime($commrs['datetime']))." - ".$commrs['comm']."<hr style='padding:0px;margin:4px;border:1px black solid'>";
				}
			}

		}

		
		// get the techs on labor
		$techar = array();
		$tstmt = "select distinct tech from labor where shopid = '$shopid' and roid = ".$rs['roid'];
		//echo $tstmt.";<BR>";
		if ($tquery = $conn->prepare($tstmt)){
			//$tquery->bind_param("si",$shoid,$rs['roid']);
			if ($tquery->execute()){
				$tr = $tquery->get_result();
				while ($trs = $tr->fetch_assoc()){
					array_push($techar,$trs['tech']);
				}
			}else{
				echo $conn->error;
			}
		}
    	if (strlen($rs['datetimepromised']) > 0){
	    	$datetimepromised = date("m/d/Y h:iA",strtotime($rs['datetimepromised']));
	    }else{
	    	$datetimepromised = "";
	    }
	    

		$totalbox = number_format($rs["totalro"],2);
		if ($showbalanceonwip == "yes"){
			$totalbox = "Balance:".number_format($rs["balance"],2);
		}

		$sa = $rs['writer'];
		if (strpos($sa," ") > 0){
			$sar = explode(" ",$sa);
			$firstinit = strtoupper(substr($sar[0],0,1));
			if (isset($sar[1])){
				$lastinit = strtoupper(substr($sar[1],0,1));
			}else{
				$lastinit = "";
			}
			if (isset($sar[2])){
				$finalinit = strtoupper(substr($sar[2],0,1));
			}else{
				$finalinit = "";
			}
			$writer = $firstinit.$lastinit.$finalinit;
		}else{
			$writer = substr($rs['writer'],0,4);
		}


		if (strlen($rs['tagnumber']) > 0){
			$roid = $rs['roid']."-".$rs['tagnumber'];
		}else{
			$roid = $rs['roid'];
		}
		$fontcolor = getContrastColor($rs['colorcode']); 
		

		$cardbody = "<div class='row'>"
		. "<div class='col' style='font-size:12pt'>".strtoupper($rs['customerlast'].", ".$rs['customerfirst'])."</div>"
		. "<div class='col'><i data-toggle='tooltip' data-placement='top' title='RO TYPE' style='border:1px silver solid; font-style:normal;padding:1px 5px 1px 5px;color:$fontcolor;background-color:".$rs['colorcode']."'>".$rs['rotype']."</i></div>"
		. "<div class='col text-right' style='font-size:12pt'>RO #".$rs['roid']."</div></div>"
		. "<div class='row'>"
		. "<div class='col-sm-12 text-center' style='font-size:12pt'>".$rs['vehinfo'];
		if (strlen($rs['vin']) > 5){
			$cardbody .= " - ".$rs['vin'];
		}

		$cardbody .= "</div></div>"
		
		. "<div class='row'>"
		. "<div class='col hover4 text-left'><span data-toggle='tooltip' data-placement='top' title='SEND EMAIL' onclick='sendEmailMessage(".$rs['roid'].")' class='url'>".strtoupper($rs['email'])."</span></div>"
		. "<div class='col hover4 text-center'><span data-toggle='tooltip' data-placement='top' title='SHOW ALL PHONES' onclick='showPhones(".$rs['roid'].")' class='url'>PHONES</span></div>"
		. "</div>"
		. "<div class='row'>"
		. "<div class='col' data-toggle='tooltip' data-placement='top' title='DATE IN' ><b>IN:</b>".date("m/d/Y",strtotime($rs['datein']))
		. "</div>"
		. "<div class='col' data-toggle='tooltip' data-placement='top' title='DATE PROMISED' ><b>PD:</b>".$datetimepromised
		. "</div>"
		. "<div class='col' data-toggle='tooltip' data-placement='top' title='ELAPSED TIME' ><b>ET:</b>".$displaytime
		. "</div>"
		. "</div>"
		. "<div class='row'>"
		. "<div id='activity".$rs['roid']."' class='col'><b>ACTIVITY:</b>";
		if (strtoupper($showmoney) == "YES" && $moneyrecd > 0){ $cardbody .=  "<i data-toggle='tooltip' title='MONEY RECEIVED' onclick='getROPayments(".$rs['roid'].")' style='color:green;font-weight:bold;font-size:large;' class='fas fa-usd fa-md'></i>"; } 
		if (strtoupper($showpics) == "YES" && $countpics > 0){ $cardbody .=  "<i style='color:green;font-weight:bold;font-size:large' data-html='true' data-toggle='tooltip' title='PICTURES UPLOADED TO THIS RO' class='fas fa-camera fa-md'></i>"; } 
		if (strtoupper($showtechtime) == "YES" && is_numeric($tcroid)){ $cardbody .=  "<i data-html='true' data-toggle='tooltip' title='TECH CLOCKED IN' style='color:orange;font-size:large' class='fas fa-clock fa-md'></i>"; }
		if (strtoupper($showtechoverhours) == "YES"){ $cardbody .=  "<i style='color:red;display:none;font-size:large' data-html='true' data-toggle='tooltip' title='Tech Time Clock has exceeded the number of hours sold on this RO. You can disable this in Settings->Custom->Misc' style='color:red;' class='fas fa-exclamation-triangle fa-md'></i>"; }
		if (strtoupper($showinsp) == "YES" && $compcountinsp > 0){ $cardbody .=  "<i onclick='openInspections(".$rs['roid'].")' style='color:blue;font-size:large;cursor:pointer'  data-html='true' style='font-size:large' data-toggle='tooltip' title='INSPECTION COMPLETE - CLICK FOR DETAILS' class='fas fa-clipboard fa-md'></i>"; }
		if (strtoupper($showinspemail) == "YES" && validateDate($rs["inspectionemailed"]) == 1){ $cardbody .=  "<i data-html='true' data-toggle='tooltip' title='INSPECTION EMAILED' style='font-size:large;color:red;' class='fas fa-envelope fa-md'></i>"; }
		if (strtoupper($showinvemail) == "YES" && validateDate($rs["invoiceemailed"]) == 1){ $cardbody .=  "<i data-html='true' data-toggle='tooltip' title='INVOICE EMAILED' style='color:navy;font-size:large' class='fas fa-envelope fa-md'></i>"; }
		if (strtoupper($showestemail) == "YES" && validateDate($rs["estimateemailed"]) == 1){ $cardbody .=  "<i data-html='true' data-toggle='tooltip' title='ESTIMATE EMAILED' style='font-size:large' class='fas fa-envelope-o fa-md'></i>"; }

		if (strlen($commstr) > 0){
			$commstr = substr($commstr,0,strlen($commstr)-4);
			$commstr = htmlspecialchars($commstr);
			$cardbody .= " <i onclick='getComms(".$rs['roid'].")' style='font-size:14pt;color:#c0f;font-weight:bold;;cursor:pointer' data-content='".$commstr."' data-html='true' data-toggle='tooltip' title='CLICK TO VIEW COMMUNICATION LOG' class='fas fa-comment'></i>";
		}


		$cardbody .= "</div>"
		. "</div>"
		. "<div class='row'>"
		. "<div class='col-sm-12'><b>WRITER:</b> ".strtoupper($rs['writer'])
		. "</div>"
		. "</div>"
		. "<div class='row'>"
		. "<div id='techs' class='col-sm-12'><b>TECH(S):</b> "
		. "</div>"
		. "</div>"
		. "<div class='text-center'>"
		. "<i data-toggle='tooltip' data-placement='top' title='PRINT RO' class='fas fa-print tooltipstered' style='color:#006699' onclick='printRO(".$rs['roid'].")'></i>"
		. "<i data-toggle='tooltip' data-placement='top' title='OPEN RO' class='fas fa-door-open tooltipstered' style='color:#090' onclick='openRO(".$rs['roid'].")'></i>"
		. "<i data-toggle='tooltip' data-placement='top' title='SEND UPDATE' onclick='openSendUpdate(".$rs['roid'].")' style='color:#0099ff' class='fas fa-envelope-open-text tooltipstered'></i>"
		. "<i data-toggle='tooltip' data-placement='top' title='CLOSE RO' class='fas fa-door-closed tooltipstered' style='color:#ff0000' onclick='closeRO(".$rs['roid'].")'></i>";

		if (strlen($rs['gp']) > 0 && $rs['gp'] >= 0){
			if (($rs['gp']*100) <= 50){
				$btnclass = "btn-danger-gp";
			}elseif (($rs['gp']*100) > 50 && ($rs['gp']*100) <= 60){
				$btnclass = "btn-caution-gp";
			}elseif (($rs['gp']*100) > 60){
				$btnclass = "btn-success-gp";
			}
			$dgp = round($rs['gp']*100,0);
		}else{
			$dgp = 0;
			$btnclass = "btn-danger-gp";
		}	

		
		$cardbody .= "<span data-toggle='tooltip' data-placement='top' title='GP' class='btn $btnclass btn-sm' onclick='showGP(".$rs['roid'].",".$rs['subtotal'].")' style='float:right'>".$dgp."</span>"
		. "</div>";

		
		
		$v .= '
			{ id:'.$rs['roid'].', status:"'.$rs['kanbanstatus'].'", text:"'.$cardbody.'", color:"'.$rs['color'].'", $css: "'.str_replace(" ","_",$rs['status']).'css" },
		';

	}
	
	
}




$v .= "]\r\n});";

$roidar = json_encode($roidar);
?>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
<input type="hidden" id="selectedroid">
<input type="hidden" id="selectedemail">
<input type="hidden" id="selectedcell">
<input type="hidden" id="invpath" value="">
<input type="hidden" id="cid" value="">
<input type="hidden" id="counterstatus" value="active">
<div id="failsafecounter" style="display:none">failsafe</div>

<div class="header">
	<span class="btn btn-warning" onclick="location.href='settings.php'"> Workflow Settings</span>
	<span class="btn btn-danger" style="" onclick="showVideo()">Watch Video</span>
	<span onclick="pauseCountdown()" style="cursor:pointer">Refresh In: </span><span id="countdown" onclick="pauseCountdown()" style="cursor:pointer;font-size:small"></span><span style="font-size:8pt;cursor:pointer" onclick="pauseCountdown()">(click to pause)</span>
	
</div>

<div id="invoicemodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="block block-themed block-transparent remove-margin-b">
				<div class="block-header bg-primary-dark">
					<ul class="block-options">
						<li>
							<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
						</li>
					</ul>
					<h3 id="tctitle" class="block-title">Repair Order</h3>
				</div>
				<div class="block-content">
					<iframe width="100%" height="100%" id="invoiceframe" style="border:0px;min-height:700px;"></iframe>
				</div>
			</div>
			<div style="margin-top:20px;" class="modal-footer">
			<button class="btn btn-md btn-primary" onclick="printROPrinter()" type="button">Print</button>
			<button class="btn btn-md btn-warning" onclick="emailRO()" type="button">Email</button>
			<button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>

<div id="sendupdatemodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="block block-themed block-transparent remove-margin-b">
				<div class="block-header bg-primary-dark">
					<ul class="block-options">
						<li>
							<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
						</li>
					</ul>
					<h3 class="block-title">Send Update</h3>
				</div>
				<div class="block-content">
					<div class="row">
						
						<div class="col-md-12">
							<h4>This will send an update to your customer with a link to see current repair status, cost and allow customer to approve the repairs</h4>
							<br><br>
							<div style="margin-bottom:20px;" class="col-md-12">
								<div class="form-material floating">
									<input class="form-control sbp-form-control" style="padding:20px;" tabindex="1" id="updateemailto" name="updateemailto" value="" type="text">
									<label style="-webkit-transform: translateY(-24px);	transform: translateY(-24px);	-ms-transform: translateY(-24px);" for="updateemailto">Email Address for Email</label>
								</div>
							</div>
							<div style="margin-bottom:20px;" class="col-md-12">
								<div class="form-material floating">
									<input class="form-control sbp-form-control" type="text" style="padding:20px;" tabindex="1" value="" id="updatecellphone" name="updatecellphone" >
									<label style="-webkit-transform: translateY(-24px);	transform: translateY(-24px);	-ms-transform: translateY(-24px);" for="updatecellphone">Cell Phone for Text</label>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div style="margin-top:20px;" class="modal-footer">
			<button class="btn btn-md btn-info" type="button" onclick="sendUpdate('email')">Send Email Update</button>
			<button class="btn btn-md btn-primary" type="button" onclick="sendUpdate('text')">Send Text Message Update</button>
			<button class="btn btn-md btn-warning" type="button" onclick="sendUpdate('both')">Send Both</button>
			<button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
			</div>
		</div>
	</div>
</div>
<div id="phonemodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-md">
		<div class="modal-content">
			<div class="block block-themed block-transparent remove-margin-b">
				<div class="block-header bg-primary-dark">
					<ul class="block-options">
						<li>
							<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
						</li>
					</ul>
					<h3 id="tctitle" class="block-title">Phones:</h3>
				</div>
				<div class="block-content" style="text-align:center">
					<span id="homephone">Home: </span><br>
					<span id="workphone">Work:</span><br>
					<span id="cellphone" class="" onclick="sendTextMessage()">Cell: </span>
				</div>
			</div>
			<div style="margin-top:20px;" class="modal-footer">
			<button class="btn btn-md btn-info" type="button" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>

<div id="textmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="block block-themed block-transparent remove-margin-b">
				<div class="block-header bg-primary-dark">
					<ul class="block-options">
						<li>
							<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
						</li>
					</ul>
					<h3 id="tctitle" class="block-title">Send a Text Message</h3>
				</div>
				<div class="block-content">
					<div class="row">
						<div class="col-md-12">
							<div style="margin-bottom:20px;" class="col-md-12">
								<div class="form-material floating">
									<input class="form-control sbp-form-control" style="padding:20px;text-transform:none" tabindex="1" id="textmessagecell" name="textmessagecell" type="text" value="">
									<label style="transform:translateY(-24px)" for="editponumber">Cell Phone</label>
								</div>
							</div>
							<div style="margin-bottom:20px;" class="col-md-12">
								<div class="form-material floating">
									<textarea class="form-control sbp-form-control" style="padding:20px;text-transform:none" tabindex="1" id="textmessage" name="textmessage"></textarea>
									<label for="textmessage">Enter a Text Message to your Customer</label>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div style="margin-top:20px;" class="modal-footer">
			<button class="btn btn-md btn-primary" type="button" onclick="sendTextMessage()">Send Message</button>
			<button class="btn btn-md btn-info" type="button" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>

<div id="commmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content" style="max-height:700px;overflow-y:auto">
			<div class="block block-themed block-transparent remove-margin-b">
				<div class="block-header bg-primary-dark">
					<ul class="block-options">
						<li>
							<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
						</li>
					</ul>
					<h3 id="tctitle" class="block-title">Communications</h3>
				</div>
				<div id="comms" class="block-content">
				</div>
			</div>
			<div style="margin-top:20px;" class="modal-footer">
			<button class="btn btn-md btn-info" type="button" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>

<div id="emailmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="block block-themed block-transparent remove-margin-b">
				<div class="block-header bg-primary-dark">
					<ul class="block-options">
						<li>
							<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
						</li>
					</ul>
					<h3 id="tctitle" class="block-title">Send an Email Message</h3>
				</div>
				<div class="block-content">
					<div class="row">
						<div class="col-md-12">
							<div style="margin-bottom:20px;" class="col-md-12">
								<div class="form-material floating">
									<input class="form-control sbp-form-control" style="padding:20px;" tabindex="1" id="emailmessageaddress" name="emailmessageaddress" type="text" value="">
									<label id="emailmsglbl" for="emailmessageaddress">Email Address</label>
								</div>
							</div>
							<div style="margin-bottom:20px;" class="col-md-12">
								<div class="form-material floating">
									<input class="form-control sbp-form-control" style="padding:20px;text-transform:none" tabindex="1" id="emailmessagesubject" name="emailmessagesubject" type="text" value="">
									<label for="emailmessagesubject">Subject</label>
								</div>
							</div>
							<div style="margin-bottom:20px;" class="col-md-12">
								<div class="form-material floating">
									<textarea class="form-control sbp-form-control" style="padding:20px;text-transform:none" tabindex="1" id="emailmessagemessage" name="emailmessagemessage"></textarea>
									<label for="emailmessagemessage">Message</label>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div style="margin-top:20px;" class="modal-footer">
			<button class="btn btn-md btn-primary" type="button" onclick="sendEmailMessage()">Send Message</button>
			<button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>

<div id="gpmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="block block-themed block-transparent remove-margin-b">
				<div class="block-header bg-primary-dark">
					<ul class="block-options">
						<li>
							<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
						</li>
					</ul>
					<h3 id="tctitle" class="block-title">Gross Profit</h3>
				</div>
				<div class="block-content">
					<iframe width="100%" height="100%" id="gpframe" style="border:0px;min-height:500px;"></iframe>
				</div>
			</div>
			<div style="margin-top:20px;" class="modal-footer">
			<button class="btn btn-md btn-info" type="button" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>

<div id="paymentmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="block block-themed block-transparent remove-margin-b">
				<div class="block-header bg-primary-dark">
					<ul class="block-options">
						<li>
							<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
						</li>
					</ul>
					<h3 id="tctitle" class="block-title">Payments Received</h3>
				</div>
				<div id="payments" class="block-content">
				</div>
			</div>
			<div style="margin-top:20px;" class="modal-footer">
			<button class="btn btn-md btn-info" type="button" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>


<div id="emailinvmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="block block-themed block-transparent remove-margin-b">
				<div class="block-header bg-primary-dark">
					<ul class="block-options">
						<li>
							<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
						</li>
					</ul>
					<h3 class="block-title">Email RO To Customer</h3>
				</div>
				<div id="vehinfo" class="block-content"></div>
				<div class="block-content">
					<div class="row">
						<div class="col-md-12">
							<div style="margin-bottom:20px;" class="col-md-12">
								<div class="form-material floating">
									<input class="form-control sbp-form-control" style="padding:20px;text-transform:none" tabindex="1" id="emailto" name="emailto" value="" type="text">
									<label id="emailrolabel" for="emailto">Email Address</label>
								</div>
							</div>
							<div style="margin-bottom:20px;" class="col-md-12">
								<div class="form-material floating">
									<input class="form-control sbp-form-control" type="text" style="display:none;padding:20px;text-transform:none" tabindex="1" value="<?php echo str_replace('"','',ucwords($emailinvoicesubject)); ?>" id="emailsubjectinv" name="emailsubjectinv" placeholder="Subject">
									<input class="form-control sbp-form-control" type="text" style="display:none;padding:20px;text-transform:none" tabindex="1" value="<?php echo str_replace('"','',ucwords($emailestimatesubject)); ?>" id="emailsubjectest" name="emailsubjectest" placeholder="Subject">
									<label for="emailsubject">Subject</label>
								</div>
							</div>
							<br><br>
							<div style="margin-bottom:20px;" class="col-md-12">
								<div class="form-material floating">
									<textarea class="form-control sbp-form-control" type="text" style="display:none;padding:20px;height:100px;text-transform:none" tabindex="1" id="emailmessageinv" name="emailmessageinv" placeholder="Message"><?php echo ucfirst($emailinvoice); ?></textarea>
									<textarea class="form-control sbp-form-control" type="text" style="display:none;padding:20px;height:100px;text-transform:none" tabindex="1" id="emailmessageest" name="emailmessageest" placeholder="Message"><?php echo ucfirst($emailestimate); ?></textarea>
									<label for="emailmessage">Message</label>
								</div>
							</div>
							<div style="margin-bottom:20px;" class="col-md-12">
								<div class="form-material floating">
									<input type="checkbox" id="updatecustomeremail" name="updatecustomeremail">
									<label style="-webkit-transform:translateY(-28px);transform:translateY(-28px);-ms-transform:translateY(-28px);font-weight:bold;" for="updatecustomeremail">Update Customer?</label>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div style="margin-top:20px;" class="modal-footer">
			<button class="btn btn-md btn-info" type="button" onclick="sendInvoiceEmail()">Send Email</button>
			<button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
			</div>
		</div>
	</div>
</div>

<img src="<?=IMAGE ?>/loaderbig.gif" id="spinner">

<script type="text/javascript">

webix.ready(function(){
    if (!webix.env.touch && webix.env.scrollSize)
        webix.CustomScroll.init();
	<?php echo $v; ?>
    
});

roids = <?php echo $roidar; ?>;


function printROPrinter(){

	//$('#emodal-box iframe').attr("id","ropdfframe")
	pdf = document.getElementById("invoiceframe")
	pdf.focus()
	pdf.contentWindow.print()

}



function sendInvoiceEmail(){

	invpath = $('#invpath').val()
	if (invpath.length > 0){
		$('#spinner').show()
		sendto = encodeURIComponent($('#emailto').val())
		
		if($('#emailsubjectinv').css('display') == 'none'){
			emailsubject = encodeURIComponent($('#emailsubjectest').val())
			emailmessage = encodeURIComponent($('#emailmessageest').val())
		}else if ($('#emailsubjectest').css('display') == 'none'){
			emailsubject = encodeURIComponent($('#emailsubjectinv').val())
			emailmessage = encodeURIComponent($('#emailmessageinv').val())
		}
		
		//emailsubject = encodeURIComponent($('#emailsubject').val())
		//emailmessage = encodeURIComponent($('#emailmessage').val())
		if($("#updatecustomeremail").is(':checked')){
		    ucstr = "updatecustomer=y&"
		}else{
		    ucstr = "updatecustomer=n&"
		}
		ds = ucstr+"roid=<?php echo $roid; ?>&cid=0&shopid=<?php echo $shopid; ?>&sendfrom=<?php echo urlencode($_COOKIE['shopname']);?>&sendto="+sendto+"&invpath="+invpath+"&subject="+emailsubject+"&message="+emailmessage+"&shopemail=<?php echo urlencode($companyemail); ?>"
		console.log(ds)
		$.ajax({
			data: ds,
			url: "<?= COMPONENTS_PRIVATE ?>/ro/roemailinvoice.php",
			error: function (xhr, ajaxOptions, thrownError) {
				console.log(xhr.status);
				console.log(xhr.responseText);
				console.log(thrownError);
			},
			success: function(r){
				//console.log(r)
				if (r == "success"){
					$('#spinner').hide()
					swal("Invoice Sent")
					$('#emailinvmodal').modal('hide')
				}else if (r == "success|"){
					$('#spinner').hide()
					$('#emailinvmodal').modal('hide')
					swal("Invoice Sent.  Reloading with new email address")
					setTimeout(function(){location.reload()},2000)
				}else{
					//console.log(r)
				}
				printit = $('#printit').val()
				if (printit == "yes"){
					location.href='<?= COMPONENTS_PRIVATE ?>/wip/wip.php'
				}
			}
		});

	}else{
    	$.ajax({
        	data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
        	url: "<?= COMPONENTS_PUBLIC ?>/invoices/printpdfro.asp",
        	success: function(r){
				$('#invpath').val(r)
        		sendto = encodeURIComponent($('#emailto').val())
        		emailsubject = encodeURIComponent($('#emailsubject').val())
        		emailmessage = encodeURIComponent($('#emailmessage').val())
				if($("#updatecustomeremail").is(':checked')){
				    ucstr = "updatecustomer=y&"
				}else{
				    ucstr = "updatecustomer=n&"
				}
				$('#spinner').show()
        		ds = ucstr+"roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&cid=0&sendfrom=<?php echo urlencode($_COOKIE['shopname']);?>&sendto="+sendto+"&invpath="+invpath+"&subject="+emailsubject+"&message="+emailmessage+"&shopemail=<?php echo urlencode($companyemail); ?>"
        		//console.log(ds)
        		$.ajax({
        			data: ds,
        			url: "<?= COMPONENTS_PRIVATE ?>/ro/roemailinvoice.php",
        			success: function(r){
        				if (r == "success"){
        					$('#spinner').hide()
        					swal("Invoice Sent")
        					$('#emailinvmodal').modal('hide')
        				}else if (r == "success|"){
        					$('#emailinvmodal').modal('hide')
        					swal("Invoice Sent.  Reloading with new email address")
        					setTimeout(function(){location.reload()},2000)
        				}else{
        					//console.log(r)
        				}
        			}
        		});

			}
		});
	}

}

function getROPayments(roid){


	$.ajax({
	
		data: "t=getpayments&shopid=<?php echo $shopid; ?>&roid="+roid,
		url: "saveboard.php",
		type: "post",
		success: function(r){
			//console.log(r)
			$('#payments').html(r);
			$('#paymentmodal').modal('show')
		},
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		}
	})


}

function emailRO(){

	// first get the status of the RO
	roid = $('#selectedroid').val()
	ds = "t=getstatus&shopid=<?php echo $shopid;?>&roid="+roid
	//console.log(ds)
	$.ajax({
		data: ds,
		url: "saveboard.php",
		type: "post",
		success: function(r){
			//console.log(r)
			rar = r.split("|")
			stat = rar[0].toLowerCase()
			em = rar[1].toLowerCase()
			if (stat == "closed" || stat == "final"){
				$('#emailsubjectinv').show()
				$('#emailmessageinv').show()
				s = $('#emailsubjectinv').val()
				if (s.indexOf("#roid#")){
					s = s.replace("#roid#",roid)
				}
				$('#emailsubjectinv').show(s)
			}else{
				$('#emailsubjectest').show()
				$('#emailmessageest').show()
				s = $('#emailsubjectest').val()
				if (s.indexOf("#roid#")){
					s = s.replace("#roid#",roid)
				}
				$('#emailsubjectest').show(s)
			}
			$('#emailto').val(em)
			$('#emailrolabel').css("-webkit-transform","translateY(-24px)").css("transform","translateY(-24px)").css("-ms-transform","translateY(-24px)")

			$('#emailinvmodal').modal('show')
			
		},
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		}	
	})

	

}

function openRO(roid){

	parent.location.href='<?= COMPONENTS_PRIVATE ?>/ro/ro.php?roid='+roid

}

function openInspections(roid){

	eModal.iframe({
		title:'Vehicle Inspection',
		url: '<?= COMPONENTS_PRIVATE ?>/inspection_classic/dvi.php?roid='+roid+'&shopid=<?php echo $shopid; ?>',
		size: eModal.size.xl,
		buttons: [
			{text: 'Close', style: 'warning', close:true}

    	]

	});

}

function dragStop(dragContext,e,list){

	// change the color of the box and background to match its new home
	id = dragContext.start
	var item = $$("myBoard").getItem(id)
	stat = item.status
	console.log(stat)
	item.$css = stat.replace(/ /g,"_")+"css";
	$$("myBoard").refresh(id);
	
	//get the new index list
	order = (" "+list.data.order).replace(" ","")
	
	// update the order of the cards
	ds = "shopid=<?php echo $shopid; ?>&roids="+order+"&t=reorder"
	$.ajax({
	
		data: ds,
		url: "saveboard.php",
		type: "post",
		success: function(r){
			//console.log(r)
		},
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		}	
	})
	
	// update status in the db
	oldstat = dragContext.from.config.status
	if (oldstat == "UNCATEGORIZED"){
		setTimeout(function(){
			$.ajax({
			
				data: "shopid=<?php echo $shopid; ?>&t=unset",
				url: "saveboard.php",
				type: "post",
				success: function(r){
					//console.log(r)
					r = parseFloat(r)
					if (r == 0){
						swal("Reloading data")
						setTimeout(function(){location.reload()},1000);
					}
				},
				error: function (xhr, ajaxOptions, thrownError) {
					console.log(xhr.status);
					console.log(xhr.responseText);
					console.log(thrownError);
				}	
			})
		},700)
	}
}

function sendUpdate(t){

	// t = email, text or both
	em = $('#updateemailto').val()
	cp = $('#updatecellphone').val()
	roid = $('#selectedroid').val()

	if (t === "email" && em.length === 0){
		swal({
			title: "Missing Information",
			text: "You must have an email to send an email update",
			type: "warning",
			confirmButtonClass: "btn-warning",
			confirmButtonText: "Ok",
			closeOnConfirm: true
		});
		return
	}
	if (t === "text" && cp.length === 0){
		swal({
			title: "Missing Information",
			text: "You must have a cell phone to send an text message update",
			type: "warning",
			confirmButtonClass: "btn-warning",
			confirmButtonText: "Ok",
			closeOnConfirm: true
		});
		return
	}
	if ((t === "both" && em.length === 0) || (t === "both" && cp.length === 0)){
		swal({
			title: "Missing Information",
			text: "You must have an email and cell phone to send an update to both",
			type: "warning",
			confirmButtonClass: "btn-warning",
			confirmButtonText: "Ok",
			closeOnConfirm: true
		});
		return
	}

	// now send the update via ajax
	$('#spinner').show()
	ds = "u=update&t="+t+"&cell="+cp+"&email="+em+"&shopid=<?php echo $shopid; ?>&roid="+roid
	console.log(ds)
	$.ajax({
		data: ds,
		url: "<?= COMPONENTS_PRIVATE ?>/ro/sendupdates.php",
		success: function(r){
			//console.log(r)
			if (r === "success"){
				swal("Your update message has been sent")
				$('#sendupdatemodal').modal('hide')
				$('#spinner').hide()
			}else{
				swal(r)
				$('#sendupdatemodal').modal('hide')
				$('#spinner').hide()
			}
		},
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		}
	})

}


function showVideo(){

	swal({
		title: "Opening in a new Window",
		text: "This will open the video in a new window.  Click OK to proceed",
		type: "warning",
		showCancelButton: true,
		confirmButtonClass: "btn-success",
		confirmButtonText: "OK",
		closeOnConfirm: true
	},
		function(){
			window.open("https://www.youtube.com/embed/9mE0ZEZ33Os")
		}
	);

}


function closeRO(roid){

	// requirebalancero, requirepayments, requiresource, requireoutmileage, 
	<?php
	if ($shopid == "5605"){
		echo "requirets = 'yes';\r\n";
	}else{
		echo "requirets = 'no';\r\n";
	}
	?>
	// get the outmiles,balance,payments and source
	$.ajax({
	
		data: "t=closerodata&shopid=<?php echo $shopid; ?>&roid="+roid,
		url: "saveboard.php",
		type: "post",
		success: function(r){
			console.log(r)
			rar = r.split("|")
			requirebalancero = rar[0].toLowerCase();
			requirepayments = rar[1].toLowerCase();
			requiresource = rar[2].toLowerCase();
			requireoutmileage = rar[3].toLowerCase();
			outmiles = rar[4];
			balance = parseFloat(rar[5]);
			pmts = parseFloat(rar[6]);
			source = rar[7];
			techstory = rar[8]
			overridestatusdate = rar[9].toLowerCase()
			
			if (requirets == "yes" && techstory == 0){
				swal({
				  title: "Tech Story is Required",
				  text: "Tech Story is Required for All Issues",
				  type: "warning",
				  confirmButtonClass: "btn-danger",
				  confirmButtonText: "Ok",
				  closeOnConfirm: true
				});
				return false
		
			}
			
			if (requirepayments == "yes" && pmts == 0){
				swal({
				  title: "Payment is Required",
				  text: "You must receive a payment to close the RO",
				  type: "warning",
				  confirmButtonClass: "btn-danger",
				  confirmButtonText: "Ok",
				  closeOnConfirm: true
				});
				return false
			}
		
			if (requiresource == "yes" && source == "NONE"){
				swal({
				  title: "Source is Required",
				  text: "You must enter a Source to close the RO",
				  type: "warning",
				  confirmButtonClass: "btn-danger",
				  confirmButtonText: "Ok",
				  closeOnConfirm: true
				});
				return false
			}
			
			if (requireoutmileage == "yes" && outmiles == ""){
				swal({
				  title: "Miles Out is Required",
				  text: "You must enter Miles Out to close the RO",
				  type: "warning",
				  confirmButtonClass: "btn-danger",
				  confirmButtonText: "Ok",
				  closeOnConfirm: true
				});
				return false
			}
			
			if (requirebalancero == "yes" && balance != 0){
				swal({
				  title: "RO Not In Balance",
				  text: "You're RO is not balanced.  You cannot close until the balance is zero",
				  type: "warning",
				  confirmButtonClass: "btn-danger",
				  confirmButtonText: "Ok",
				  closeOnConfirm: true
				});
				return false
			}
			
			swal({
				title: "Closing RO",
				text: "Ready to close the RO.  Are you sure?",
				type: "warning",
				showCancelButton: true,
				confirmButtonClass: "btn-danger",
				confirmButtonText: "Close the RO",
				closeOnConfirm: false
			},
			function(){
				
				ds = "overridestatusdate="+overridestatusdate+"&t=closero&shopid=<?php echo $shopid; ?>&roid="+roid+"&s=closed"
				//console.log(ds)
				//overridestatusdate = $('#'+eid).attr("data-overridestatusdate")
				$('#spinner').show().css("z-index","99999")
		    	$.ajax({
		    		data: ds,
		    		type: "post",
		    		url: "<?= COMPONENTS_PRIVATE ?>/ro/savedata.php",
						error: function (xhr, ajaxOptions, thrownError) {
							console.log(xhr.status);
							console.log(xhr.responseText);
							console.log(thrownError);
						},
		        		success: function(r){
		    				//console.log(r)
		    			if (r == "success"){
		    				$('#spinner').hide()
							swal({
								title: "RO is Closed",
								text: "Would you like to print a final copy of the RO?",
								type: "success",
								showCancelButton: true,
								confirmButtonClass: "btn-success",
								confirmButtonText: "Yes, Print It",
								closeOnConfirm: true,
								cancelButtonText: "No, Don't Print"
							},
							function(isConfirm){
								if (isConfirm){
									//$('#printit').val("yes");
									printRO(roid)
									
									removeCard(roid)
								}else{
									// remove the card and delete it from the db
									removeCard(roid)
								}
							});
		    			}
		    		}
		    	});
			});				
				
		
		},
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		}
	})
	
	

}

function removeCard(roid){

	$.ajax({
	
		data: "t=closero&roid="+roid+"&shopid=<?php echo $shopid; ?>",
		url: "saveboard.php",
		type: "post",
		success: function(r){
			console.log(r)
			if (r == "success"){
				$$("myBoard").remove(roid);
			}
		},
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		}
	})

}
    
function getComms(roid){

	$('#selectedroid').val(roid)
	
	// get the cell and email
	$.ajax({
		data: "t=getcomms&shopid=<?php echo $shopid; ?>&roid="+roid,
		url: "saveboard.php",
		type: "post",
		success: function(r){
			$('#comms').html(r)
			$('#commmodal').modal('show')
			
		},
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		}
	})

}

function printRO(roid){
	$('#selectedroid').val(roid)
	$('#spinner').show()
	$.ajax({
    	data: "shopid=<?php echo $shopid; ?>&roid="+roid+"&i=new",
    	url: "<?= COMPONENTS_PUBLIC ?>/invoices/printpdfro.asp",
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		},
    	success: function(r){
    		//console.log("printro:"+r)
    		$('#invpath').val(r)
    		setTimeout(function(){
    			$('#spinner').hide()
				url = '<?= COMPONENTS_PUBLIC ?>/invoices'+r,
				$('#invoiceframe').attr("src",url)
				$('#invoicemodal').modal('show')
			},500)
    	}
	});
}

function openSendUpdate(roid){

	$('#selectedroid').val(roid)
	
	// get the cell and email
	$.ajax({
		data: "t=getemailcell&shopid=<?php echo $shopid; ?>&roid="+roid,
		url: "saveboard.php",
		type: "post",
		success: function(r){
			if (r.indexOf("|") > 0){
				rar = r.split("|")
				email = rar[0]
				cell = rar[1]
			}else{
				email = ""
				cell = ""
			}
			
			$('#selectedemail').val(email)
			$('#updateemailto').val(email)
			$('#updatecellphone').val(cell)
			$('#sendupdatemodal').modal('show')
			
		},
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		}
	})
	

}

function sendEmailMessage(roid){

	if ($('#emailmodal').css("display") == "none"){
		// get the email address from the RO
		$.ajax({
			data: "t=getemailcell&shopid=<?php echo $shopid; ?>&roid="+roid,
			url: "saveboard.php",
			type: "post",
			success: function(r){
				rar = r.split("|")
				email = rar[0]
				
				$('#emailmessageaddress').val(email)
				$('#emailmsglbl').css("-webkit-transform","translateY(-24px)").css("transform","translateY(-24px)").css("-ms-transform","translateY(-24px)")

				$('#emailmodal').modal('show')
				setTimeout(function(){ $('#emailmessagesubject').focus()},500)
				
			},
			error: function (xhr, ajaxOptions, thrownError) {
				console.log(xhr.status);
				console.log(xhr.responseText);
				console.log(thrownError);
			}
		})
		
	}else{
		$('#spinner').show()
		email = $('#emailmessageaddress').val()
		msg = encodeURIComponent($('#emailmessagemessage').val())
		subj = encodeURIComponent($('#emailmessagesubject').val())
		if (email.length >= 1 && msg.length > 0 && subj.length > 0){
			$.ajax({
				data: "shopid=<?php echo $shopid; ?>&roid="+roid+"&t=sendemail&email="+email+"&subj="+subj+"&msg="+msg,
				type: "post",
				url: "<?= COMPONENTS_PRIVATE ?>/ro/saveData.php",
				error: function (xhr, ajaxOptions, thrownError) {
					console.log(xhr.status);
					console.log(xhr.responseText);
					console.log(thrownError);
				},
				success: function(r){
					$('#spinner').hide()
					if (r == "success"){
						swal("Email Message Sent")
						$('#emailmodal').modal('hide')
					}
				}
			});
		}else{
			swal("You must enter an email, subject and message")
			$('#spinner').hide()
		}

	}
}

function showGP(roid,subtotal){
            	
	if ($('#gpbutton').attr("class") == "btn "){
		swal("You have no parts or labor to calcuate GP")
	}else{
		url = "<?= COMPONENTS_PRIVATE ?>/gp/gpdetail_dev.php?shopid=<?php echo $shopid; ?>&roid="+roid+"&subtotal="+subtotal
		//console.log(url)
		$('#gpframe').attr("src",url)
		$('#gpmodal').modal('show')

		
	}
}


function showPhones(roid){

	$.ajax({
		data: "t=getphones&shopid=<?php echo $shopid; ?>&roid="+roid,
		url: "saveboard.php",
		type: "post",
		success: function(r){
			$('#homephone').html('')
			$('#workphone').html('')
			$('#cellphone').html('').removeClass("btn btn-success btn-sm")
			if (r.indexOf("|") > 0){
				rar = r.split("|")
				h = rar[0]
				w = rar[1]
				c = rar[2]
			}else{
				h = ""
				w = ""
				c = ""
			}
			
			if (h.length > 0){
				$('#homephone').html("HOME: "+h.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3'))
			}
			if (w.length > 0){
				$('#workphone').html("WORK: "+w.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3'))
			}
			if (c.length > 0){
				$('#cellphone').html("CELL: "+c.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3')).addClass("btn btn-success btn-sm")
				$('#selectedcell').val(c)
			}else{
				$('#selectedcell').val('')
			}
			
			$('#phonemodal').modal('show')
			
		},
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		}
	})

}

function sendTextMessage(c,roid){

	if ($('#textmodal').css("display") == "none"){
		c = $('#selectedcell').val()
		$('#textmessagecell').val(c)
		$('#textmodal').modal('show')
		setTimeout(function(){
			$('#textmessage').focus()
		},500)
		$('#phonemodal').modal('hide')
	}else{
		// send text message
		$('#spinner').show()
		cell = $('#textmessagecell').val()
		sms = $('#textmessage').val()
		if (cell.length == 10 && sms.length > 0){
			$.ajax({
				data: "shopname=<?php echo urlencode($shopname); ?>&shopid=<?php echo $shopid; ?>&roid="+roid+"&t=sendsms&cell="+cell+"&sms="+sms,
				type: "post",
				url: "<?= COMPONENTS_PRIVATE ?>/ro/saveData.php",
				error: function (xhr, ajaxOptions, thrownError) {
				           console.log(xhr.status);
				           console.log(xhr.responseText);
				           console.log(thrownError);
				},
				success: function(r){
					//console.log(r)
					$('#spinner').hide()
					//if (r == "success"){
						swal("Text Message Sent")
						$('#textmodal').modal('hide')
					//}
				}
			});
		}else{
			swal("You must have a 10 digit cell number and type a message to send")
			$('#spinner').hide()
		}
	}

}

function pauseCountdown(){

	cs = $('#counterstatus').val()
	if (cs == "active"){
		$('#counterstatus').val('paused')
		$('#countdown').countdown('pause')
		failsafetime = new Date()
		failsafetime.setSeconds(failsafetime.getSeconds()+300)

		$('#failsafecounter').countdown({
			until: failsafetime,
			onExpiry: function(){
				location.reload()	
			}
		})
	}else{
		$('#counterstatus').val('active')
		$('#countdown').countdown('resume')
		$('#failsafecounter').countdown('destroy')
	}

}


$(document).ready(function() {

	setTimeout(function(){
		$('[data-toggle="tooltip"]').tooltipster();
	},1000)
	
	
	setTimeout(function(){
		liftoffTime = new Date()
		liftoffTime.setSeconds(liftoffTime.getSeconds()+60)
		$('#countdown').countdown({
			until: liftoffTime,
			format: 'S',
			compact: true,
			onExpiry: function(){
				location.reload()	
			}
		});
	},100)
})


</script>

</body>
</html>