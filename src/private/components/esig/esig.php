﻿<?php
require_once INTEGRATIONS_PATH . "/FPDF/fpdf.php";
require_once INTEGRATIONS_PATH . "/FPDF/FPDI-2.3.4/vendor/autoload.php";
require_once INTEGRATIONS_PATH . "/sbp_bucket/sbp_bucket.php";
require CONNWOSHOPID;

use setasign\Fpdi\Fpdi;

if (!defined("KIOSK_PATH")) {
    define("KIOSK_PATH", PUBLIC_PATH . DS . "assets" . DS . "_kiosk");
}
if (file_exists(KIOSK_PATH)) {
    mkdir(KIOSK_PATH);
    mkdir(KIOSK_PATH . DS . "images");
}

$shopid = isset($_REQUEST['shopid']) ? filter_var($_REQUEST['shopid'], FILTER_SANITIZE_STRING) : "";
$roid = isset($_REQUEST['roid']) ? filter_var($_REQUEST['roid'], FILTER_SANITIZE_STRING) : "";
$path = isset($_REQUEST['path']) ? filter_var($_REQUEST['path'], FILTER_SANITIZE_STRING) : "";
$draw = isset($_REQUEST['draw']) ? filter_var($_REQUEST['draw'], FILTER_SANITIZE_STRING) : "";
$esigfile = isset($_REQUEST['esigfile']) ? filter_var($_REQUEST['esigfile'], FILTER_SANITIZE_STRING) : "";
$filetime = strtotime(localTimeStamp($shopid));


$pathtosig = KIOSK_PATH . DS . "images";
$pathtosig .= DS . $esigfile;

//$savepath = $_SERVER["DOCUMENT_ROOT"] . DS . "sbp" . DS . "savedinvoices";
$savepath = "\\\\fs.shopboss.aws\\share\\savedinvoices";

if (!file_exists($savepath . DS . $shopid)) {
    mkdir($savepath . DS . $shopid);
    mkdir($savepath . DS . $shopid . DS . $roid);
} else {
    if (!file_exists($savepath . DS . $shopid . DS . $roid)) {
        mkdir($savepath . DS . $shopid . DS . $roid);
    }
}

$savepath = $savepath . DS . $shopid . DS . $roid . DS;

$currdate = date('Y-n-j_h-i-s');

$newfilename = $shopid . "_" . $roid . "_" . $currdate . "_pdf.pdf";
//$path = $_SERVER['DOCUMENT_ROOT'].DS."sbp".DS.$path;
//echo $path;
//echo "<br />\n";
//echo $savepath.$newfilename;
try {
    $pdf = new Fpdi();
    //   $pdf->AddPage();
    $pageCount = $pdf->setSourceFile($path);

    $x = 15;
    $width = $height = 0;
    if (!empty($draw)) {
        $x = 25;
        $width = 50;
        $height = 20;
    }

    for ($i = 1; $i <= $pageCount; $i++) {
        $imported = $pdf->importPage($i);
        $page_size = $pdf->getTemplateSize($imported);
        $page_height = $page_size['height'];
        $page_width = $page_size['width'];

        $pdf->AddPage('P', $page_size);
        $pdf->useTemplate($imported);

        if ($shopid == '20766' && $i == $pageCount) {
            $pdf->Image($pathtosig, 55, 180, $width, $height);
        } else if ($shopid == '4639'){
            if ($i < $pageCount) {
                $pdf->Image($sig, 30, 258, $w, $h);
            } else {
                $pdf->Image($sig, 40, 258, $w, $h);
            }
        } 
        else if ($shopid == "3073") {

            $stmt = "select UCASE(`status`) from repairorders where roid = ? and shopid = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("is", $roid, $shopid);
                $query->execute();
                $query->bind_result($status);
                $query->fetch();
                $query->close();
            }

            if ($status == "1INSPECTION") {

                if ($i < $pageCount) {
                    $pdf->Image($pathtosig, 40, 240, $width, $height);
                } else {
                    $pdf->Image($pathtosig, 10, 240, $width, $height);
                }
            } else {
                if ($i == $pageCount) {
                    $pdf->Image($pathtosig, 20, $page_height - $x, $width, $height);
                }
            }
        } else {
            if ($shopid != '13915' || $i != $pageCount) {
                $pdf->Image($pathtosig, 20, $page_height - $x, $width, $height);
            }
        }

    }

    //   $tplIdx = $pdf->importPage($pageCount);
    //   $pdf->useTemplate($tplIdx);

    //   $pdf->Image($pathtosig, 70, 120, 120, 50);
    ob_end_clean();

    $pdf_string = $pdf->Output("S", $newfilename);
    $file_key = $shopid . "/invoices/RepairOrders/" . $roid . "/" . basename($newfilename);
    $file_saved = $sbp_bucket->add_file($pdf_string, $file_key);
    $presigned_url = $sbp_bucket->presigned_url($file_key);
    echo $presigned_url;

//    $pdf->Output("F", $savepath . $newfilename);
//    sleep(2);
//        echo $newfilename;
} catch (Exception $e) {
    echo $e->getMessage();
}

//echo $newfilename;
?>
