<?php
$component = "workflow-v2";
include getRulesGlobal($component);

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);

if (isset($countsettings)) {
    $countsettings = $countsettings;
} else {
    $countsettings = 0;
}

if (isset($countstatus)) {
    $countstatus = $countstatus;
} else {
    $countstatus = 0;
}

$stmt = "select * from kanbansettings where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($id, $sid, $i1, $i2, $i3, $i4, $i5, $i6, $i7, $i8, $i9, $i10, $i11);
    $query->fetch();
    $query->close();
}

// check for settings
$stmt = "select count(*) c from kanbansettings where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($countsettings);
    $query->fetch();
    $query->close();
}

if ($countsettings == 0) {
    $stmt = "insert into kanbansettings (shopid) values (?)";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $conn->commit();
        $query->close();
    }
}

$stmt = "select count(*) c from kanbanstatuses where shopid = ?";

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($countstatus);
    $query->fetch();
    $query->close();
}

if ($countstatus == 0) {
    header("Location: settings.php");
    exit;
}

include getHeadGlobal($component);

?>
<link rel="stylesheet" type="text/css"
      href="https://<?= $_SERVER['SERVER_NAME'] ?>/src/public/assets/workflow/codebase/webix.css?v=7.1.1">
<link rel="stylesheet" type="text/css"
      href="https://<?= $_SERVER['SERVER_NAME'] ?>/src/public/assets/workflow/codebase/kanban.css?v=7.1.1">

<style>

    .webix_header {
        text-align: center;
        color: var(--textColor);
    }

    body.webix_full_screen {
        overflow-y: auto !important;
    }

    .webix_kanban_list,.webix_list_item
{
    background-color: #DADFE4;
}
    .data {
        padding: 8px;
        border: 1px silver solid;
        width: 200px;
    }

    .table-responsive .table {
        overflow-x: scroll;
        max-width: 100%;
        -webkit-overflow-scrolling: touch !important;
    }

    .webix_kanban_body {
        margin: 0px;
        width: 100%;
        padding: 0px 10px 0px 4px;
    }

    .webix_kanban_footer {
        display: none !important;
    }

    .kbi-account {
        display: none;
    }

    .webix_kanban_user_avatar {
        display: none;
    }

    .btn-caution-gp {
        background-color: #FFFF99 !important;
        color: black !important;
        font-style: normal;
    }

    .btn-danger-gp {
        background-color: #FF0000 !important;
        color: white;
        font-style: normal;
    }

    .btn-success-gp {
        background-color: #009933 !important;
        color: white;
        font-style: normal;
    }

    <?php
    $ds = array();
    $stmt = "select status,color from kanbanstatuses where shopid = ? order by `order`";
    if ($query = $conn->prepare($stmt)){
        $query->bind_param("s",$shopid);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_assoc()){
            $ds[] = $rs;
            echo ".".strtoupper(str_replace(" ","_",$rs['status']))."css{ background-color:".$rs['color'].";}\r\n";
        }
    }

    ?>

</style>
<body>
<input type="hidden" id="selectedroid">
<input type="hidden" id="selectedemail">
<input type="hidden" id="selectedcell">
<input type="hidden" id="invpath" value="">
<input type="hidden" id="cid" value="">
<input type="hidden" id="counterstatus" value="active">
<div id="failsafecounter" style="display:none">failsafe</div>

<div class="row mb-2 header-row">
    <div class="col title mb-3">
        <h2 class="d-inline">Workflow <i class="fas fa-circle-info fa-2xs" data-mdb-toggle="tooltip" title="Workflow will auto refresh in 60 seconds."></i></h2>
    </div>
    
    <div class="col d-flex justify-content-end me-2">
        <div class="d-flex align-items-center">
            <input type="text" class="form-control flex-grow-1 me-2" placeholder="Search" id="srch" autocomplete="off">
            <?php if($_COOKIE['mode'] == 'full'){?>
            <button type="button" class="border-0 bg-transparent">
                <a class="nav-link" href="https://www.youtube.com/embed/9mE0ZEZ33Os" target="_blank"
                   data-mdb-toggle="tooltip" data-mdb-placement="bottom" title="Watch Video">
                    <i class="fas fa-lg fa-circle-play"></i>
                </a>
            </button>
            <button type="button" class="border-0 bg-transparent">
                <a class="nav-link" href="editsettings.php" data-mdb-toggle="tooltip" data-mdb-placement="bottom"
                   title="Workflow Settings">
                    <i class="fas fa-lg fa-gear"></i>
                </a>
            </button>
            <?php }?>
        </div>
    </div>
</div>

<?php include getModalsGlobal($component); ?>

<?php include getScriptsGlobal($component); ?>

</body>
</html>
