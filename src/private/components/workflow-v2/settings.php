<?php
$component = 'workflowsettings-v2';
$shopid = $_COOKIE['shopid'];
include getHeadGlobal($component);
include getRulesGlobal($component);


if (isset($countstatus)) {
    $countstatus = $countstatus;
} else
    $countstatus = 0;

if (isset($countdata)) {
    $countdata = $countdata;
} else
    $countdata = 0;

if (isset($countsettings)) {
    $countsettings = $countsettings;
} else
    $countsettings = 0;


$stmt = "select count(*) c from kanbanstatuses where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($countstatus);
    $query->fetch();
    $query->close();
}

if ($countstatus != 0) {
    header("Location: editsettings.php");
    exit;
}

$stmt = "select count(*) c from kanbandata where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($countdata);
    $query->fetch();
    $query->close();
}

// check for settings
$stmt = "select count(*) c from kanbansettings where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($countsettings);
    $query->fetch();
    $query->close();
}

if ($countsettings == 0) {
    $stmt = "insert into kanbansettings (shopid) values (?)";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $conn->commit();
        $query->close();
    }

}

?>
<style>

    #colorpicker {
        width: 275px;
        height: 280px;
        border: #000 thin solid;
        text-align: center;
        padding: 3px;
    }

    .colordiv {
        display: inline-block;
        width: 20px;
        height: 20px;
        margin: 0 2px;
        cursor: pointer
    }

    .colorcheck {
        color: #000;
        font-weight: bold;
    }

    #builder {
        text-align: center;
        width: 75%;
        margin: auto
    }

    #colors {
        display: none;
        text-align: center
    }

</style>

<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/spectrum/1.8.0/spectrum.min.css">

<body>
<div class="container-fluid">
    <div class="row">
        <div class="col title mb-3">
            <h2>Workflow Settings</h2>
        </div>
    </div>


    <center><h5>To create your workflow, you need to create your Workflow Categories</h5><br><h5>1. Select Number of Categories</h5></center>

    <div class="row">
        <div class="col-md-2 mx-auto">

            <select id="catcount" onchange="createTable(this.value)" class="select form-control" style="width:300px;">
                <option value="none">Select</option>
                <option value="1">1</option>
                <option value="2">2</option>
                <option value="3">3</option>
                <option value="4">4</option>
                <option value="5">5</option>
                <option value="6">6</option>
                <option value="7">7</option>
                <option value="8">8</option>
                <option value="9">9</option>
                <option value="10">10</option>
                <option value="11">11</option>
                <option value="12">12</option>
                <option value="13">13</option>
                <option value="14">14</option>
                <option value="15">15</option>
            </select>
        </div>
    </div>

    <div id="builder"></div>


    <div id="colors">
    </div>


    <br><br><br><br><br><br>

    <hr>

    <div class="row">
        <div class="col-md-4">
            <h3>Setup Video</h3>
            <iframe width="560" height="315" src="https://www.youtube.com/embed/-IgKT5DyJ-w" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
        </div>
        <div class="col-md-8" align="right">
            <h3>Example</h3><img alt="" style="max-width:50%" src="<?= IMAGE ?>/imgAA.jpg">
        </div>
    </div>
</div>
<input type="hidden" id="numcolselected" value="0">
<input type="hidden" id="cats">
<input type="hidden" id="catcolors">

<?php include getScriptsGlobal(''); ?>

<script src="https://cdnjs.cloudflare.com/ajax/libs/spectrum/1.8.0/spectrum.min.js"></script>

<script>

    function createTable(numcols) {

        numcolselected = $('#numcolselected').val()
        if (numcols != "none" && numcols > 0) {
            if (numcols > numcolselected && numcolselected > 0) {
                numcells = numcols - numcolselected
                html = ""
                for (i = 1; i <= numcells; i++) {
                    html += "<td><input type='text' id='" + (parseFloat(i) + parseFloat(numcolselected)) + "' class='form-control catclass' value='Category'></td>"
                }
                $('#catrow').append(html)

                $('#numcolselected').val(numcols)
            } else if (numcols < numcolselected && numcolselected > 0) {
                swal({
                        title: "Are you sure?",
                        text: "This will reset your Categories",
                        type: "warning",
                        showCancelButton: true,
                        confirmButtonClass: "btn-primary",
                        confirmButtonText: "Yes, Reset it!",
                        cancelButtonText: "Cancel",
                        closeOnConfirm: true,
                        closeOnCancel: true
                    },
                    function (isConfirm) {
                        if (isConfirm) {
                            html = "<br><h5>Now, enter the name of each category you want in your Workflow</h5>"
                            html += "<table class='table table-borderless table-sm'><tr id='catrow'>"
                            for (i = 1; i <= numcols; i++) {
                                html += "<td><input type='text' id='" + i + "' class='form-control catclass' value='Category'></td>"
                            }
                            html += "</tr></table>"
                            html += '<span class="btn btn-primary btn-sm" onclick="saveCats()">Save Categories</span>'

                            $('#builder').html(html)
                            $('#numcolselected').val(numcols)
                        }
                    });
            } else if (numcols != "none" && numcols > 0 && numcolselected == 0) {
                html = "<br><h5>Now, enter the name of each category you want in your Workflow</h5>"
                html += "<table class='table table-borderless table-sm'><tr id='catrow'>"
                for (i = 1; i <= numcols; i++) {
                    html += "<td><input type='text' id='" + i + "' class='form-control catclass' value='Category'></td>"
                }
                html += "</tr></table>"
                html += '<span class="btn btn-primary btn-sm" onclick="saveCats()">Save Categories</span>'
                $('#builder').html(html)
                $('#numcolselected').val(numcols)
            }
        }
    }


    function saveCats() {

        ar = []
        $('#catrow').each(function () {
            $('input', this).each(function () {
                ar.push($(this).val().toUpperCase())
            })
        })

        $('#cats').val(ar)
        html = "<br><br><center><h5>2. Now, Select a Color for each Category</h5></center><div class='row'><div class='col-md-4 mx-auto'><select size='" + ar.length + "' id='catforcolor' class='form-control text-center'>"
        for (i = 0; i < ar.length; i++) {

            html += "<option value='" + ar[i] + "'>" + ar[i] + "</option>"

        }
        html += "</select></div></div><br><br><center><div id='colorpicker'></div></center>"
        html += "<br><br><span class='btn btn-primary btn-sm' onclick='saveAll()'>Save Workflow</span>"

        $('#colors').html(html).show();

        html = ''

        var palette = ["#FFB6C1", "#FF69B4", "#FF1493", "#DB7093", "#C71585", "#E6E6FA", "#D8BFD8", "#DDA0DD", "#DA70D6", "#EE82EE", "#FF00FF", "#BA55D3", "#9932CC", "#9400D3", "#8A2BE2", "#800080", "#9370DB", "#7B68EE", "#6A5ACD", "#483D8B", "#663399", "#4B0082", "#FFA07A", "#FA8072", "#E9967A", "#F08080", "#CD5C5C", "#DC143C", "#FF0000", "#B22222", "#8B0000", "#FFA500", "#FF8C00", "#FF7F50", "#FF6347", "#FF4500", "#FFD700", "#FFFF00", "#ADFF2F", "#7FFF00", "#7CFC00", "#00FF00", "#32CD32", "#98FB98", "#90EE90", "#00FA9A", "#00FF7F", "#3CB371", "#2E8B57", "#228B22", "#008000", "#006400", "#9ACD32", "#6B8E23", "#556B2F", "#00FFFF", "#E0FFFF", "#AFEEEE", "#7FFFD4", "#40E0D0", "#5F9EA0", "#4682B4", "#B0C4DE", "#ADD8E6", "#B0E0E6", "#87CEFA", "#87CEEB", "#6495ED", "#00BFFF", "#1E90FF", "#4169E1", "#0000FF", "#0000CD", "#00008B", "#000080", "#191970", "#FFF8DC", "#FFE4C4", "#FFDEAD", "#F5DEB3", "#DEB887", "#D2B48C", "#BC8F8F", "#F4A460", "#DAA520", "#B8860B", "#CD853F", "#D2691E", "#808000", "#8B4513", "#A0522D", "#A52A2A", "#800000"];

        for (var i = 0; i < palette.length; i++) {

            html += "<div class='colordiv' style='background-color:" + palette[i] + "'><i class='fas fa-solid fa-check colorcheck' style='display:none'></i></div>"
        }

        $('#colorpicker').html(html)

        $('.colordiv').on('click', function () {

            if ($(this).find('.colorcheck').is(":hidden")) {
                $('.colorcheck').hide()
                $(this).find('.colorcheck').show()

                var selcell = $('#catforcolor').val()
                var x = $(this).css('background-color')

                $('#catrow').each(function () {
                    $('td input', this).each(function () {
                        cellval = $(this).val()
                        if (cellval.toUpperCase() == selcell.toUpperCase()) {
                            $(this).css("border", "3px " + x + " solid")
                        }
                    })
                })
            }


        })

    }

    function saveAll() {

        parent.showLoader()

        ar = []
        $('#catrow').each(function () {
            $('td input', this).each(function () {
                ar.push([$(this).val(), $(this).css("border-color")])
            })
        })
        ar = JSON.stringify(ar)
        $.ajax({

            data: "t=saveworkflow&shopid=<?php echo $shopid; ?>&data=" + ar,
            url: "saveboard.php",
            type: "post",
            success: function (r) {
                location.href = 'board.php'
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            }

        })
    }


</script>


</body>
</html>
