<?php
$component = "workflowsettings-v2";
include getRulesGlobal($component);

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);

$stmt = "select count(*) c from kanbandata where shopid = ?";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("s",$shopid);
	$query->execute();
	$query->bind_result($countdata);
	$query->fetch();
	$query->close();
}

$stmt = "select count(*) c from kanbanstatuses where shopid = ?";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("s",$shopid);
	$query->execute();
	$query->bind_result($countstatus);
	$query->fetch();
	$query->close();
}

// check for settings
$stmt = "select count(*) c from kanbansettings where shopid = ?";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("s",$shopid);
	$query->execute();
	$query->bind_result($countsettings);
	$query->fetch();
	$query->close();
}

if ($countsettings == 0){
	$stmt = "insert into kanbansettings (shopid) values (?)";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
		$query->execute();
		$conn->commit();
		$query->close();
	}

}

$stmt = "select * from kanbansettings where shopid = ?";
if ($query = $conn->prepare($stmt)){
	$query->bind_param("s",$shopid);
	$query->execute();
	$query->bind_result($id,$sid,$i1,$i2,$i3,$i4,$i5,$i6,$i7,$i8,$i9,$i10,$i11);
	$query->fetch();
	$query->close();
}

include getHeadGlobal($component);
?>
<style>

    #colorpicker {
        width: 275px;
        height: 280px;
        border: #000 thin solid;
        text-align: center;
        padding: 3px;
    }

    .colordiv {
        display: inline-block;
        width: 20px;
        height: 20px;
        margin: 0 2px;
        cursor: pointer
    }

    .colorcheck {
        color: #000;
        font-weight: bold;
    }

    #builder {
        text-align: center;
        width: 75%;
        margin: auto
    }

    #colors {
        display: none;
        text-align: center
    }

    .form-check-input:focus, .form-check-input:checked {
        background-color: transparent !important;
        border: 1px solid rgba(0,0,0,.25) !important;
    }

</style>
<body>

<div class="container-fluid">
        
    <div class="row">
        <div class="col-md-12 col-sm-12">
            <div class="title col breadcrumb d-flex align-items-center mb-0">
                <a href="board.php"
                   class="text-secondary">Workflow</a>
                <span class="text-secondary ps-3 pe-3">/</span>
                <h2>Edit Workflow <i class="fas fa-circle-info fa-2xs" data-mdb-toggle="tooltip" title="Any changes to the number of categories will require you to recategorize all RO's on your WIP. Any changes to the Category Labels, or colors will not reset your Workflow."></i> </h2>
            </div>
            <hr/>
        </div>
    </div>

    <div class="row">
        <div class="col-md-9 text-center">

    		<center><h5>1. Select Number of Categories</h5></center>
    		<div class='col-md-4 mx-auto mb-4'>
	            <select id="catcount" onchange="createTable(this.value)" class="select" style="width:300px !important;">
					<?php
					for ($j = 1; $j <= 15; $j++){
						if ($j == $countstatus){
							$s = "selected";
						}else{
							$s = "";
						}
					?>
					<option <?php echo $s; ?> value="<?php echo $j; ?>"><?php echo $j; ?></option>
					<?php
					}
					?>
	            </select>
	        </div>
        

            <div id="builder">
		      <table class="table table-border">
			   <tr id="catrow" data-mdb-sortable-init data-mdb-sortable="sortable">
				<?php
				$stmt = "select id,status,color from kanbanstatuses where shopid = ? order by `order`";
				if ($query = $conn->prepare($stmt)){
					$query->bind_param("s",$shopid);
					$query->execute();
					$r = $query->get_result();
					while ($rs = $r->fetch_assoc()){
				?>
				<td data-mdb-drag-handle=".draggable-drag-ico" contenteditable="true" class="catclass sortable-item" data-id="<?= $rs['id']?>" style="border:2px <?php echo $rs['color']; ?> solid"><i class="fas fa-arrows-alt draggable-drag-ico"></i> <?php echo $rs['status']; ?></td>
				<?php
					}
				}
				?>
					</tr>
				</table>
				<span class="btn btn-primary btn-lg" onclick="saveCats()">Save Categories</span>
			</div>


		    <div id="colors" class="text-center">
			</div>
		</div>
		<div class="col-md-3 text-left border-left border-color-silver">
			<h2>Other Settings</h2>
			<div class="row">
				<div class="col-md-12">			
					<div class="form-check form-switch">
						<input type="checkbox" id="scrollfullpage" role="switch" class="form-check-input" onchange="save(this.id)" <?php if(strtolower($i1)=='yes'){echo 'checked'; }?>>
					    <label class="form-check-label" for="scrollfullpage">Scroll Full Page</label>
                    </div>
					<div class="form-check form-switch mt-2">
						<input type="checkbox" id="showemail" role="switch" class="form-check-input" onchange="save(this.id)" <?php if(strtolower($i2)=='yes'){echo 'checked'; }?>>
						<label class="form-check-label" for="showemail">Show Email/Phones</label>
					</div>
					<div class="form-check form-switch mt-2">
						<input type="checkbox" id="showactivity" role="switch" class="form-check-input" onchange="save(this.id)" <?php if(strtolower($i3)=='yes'){echo 'checked'; }?>>
						<label class="form-check-label" for="showactivity">Activity Line</label>
					</div>
					<div class="form-check form-switch mt-2">
						<input type="checkbox" id="showdatein" role="switch" class="form-check-input" onchange="save(this.id)" <?php if(strtolower($i4)=='yes'){echo 'checked'; }?>>
						<label class="form-check-label" for="showdatein">Show Date/Time Line</label>
					</div>
					<div class="form-check form-switch mt-2">
						<input type="checkbox" id="showwriter" role="switch" class="form-check-input" onchange="save(this.id)" <?php if(strtolower($i5)=='yes'){echo 'checked'; }?>>
						<label class="form-check-label" for="showwriter">Show Writer</label>
					</div>
					<div class="form-check form-switch mt-2">
						<input type="checkbox" id="showtech" role="switch" class="form-check-input" onchange="save(this.id)" <?php if(strtolower($i6)=='yes'){echo 'checked'; }?>>
						<label class="form-check-label" for="showtech">Show Techs</label>
					</div>
					<div class="form-check form-switch mt-2">
						<input type="checkbox" id="showicons" role="switch" class="form-check-input" onchange="save(this.id)" <?php if(strtolower($i7)=='yes'){echo 'checked'; }?>>
						<label class="form-check-label" for="showicons">Show Icons</label>
					</div>
					<div class="form-check form-switch mt-2">
						<input type="checkbox" id="showgp" role="switch" class="form-check-input" onchange="save(this.id)" <?php if(strtolower($i8)=='yes'){echo 'checked'; }?>>
						<label class="form-check-label" for="showgp">Show GP</label>
					</div>
					<div class="form-check form-switch mt-2">
						<input type="checkbox" id="showvin" role="switch" class="form-check-input" onchange="save(this.id)" <?php if(strtolower($i9)=='yes'){echo 'checked'; }?>>
						<label class="form-check-label" for="showvin">Show VIN</label>
					</div>
					<div class="form-check form-switch mt-2">
						<input type="checkbox" id="showlbhrs" role="switch" class="form-check-input" onchange="save(this.id)" <?php if(strtolower($i10)=='yes'){echo 'checked'; }?>>
						<label class="form-check-label" for="showlbhrs">Show Labor Hours</label>
					</div>
					<div class="form-check form-switch mt-2">
						<input type="checkbox" id="opennewtab" role="switch" class="form-check-input" onchange="save(this.id)" <?php if(strtolower($i11)=='yes'){echo 'checked'; }?>>
						<label class="form-check-label" for="opennewtab">Open Workflow in New Tab</label>
					</div>
				
				</div>
			</div>
		</div>
	</div>
</div>
	

<input type="hidden" id="numcolselected" value="<?php echo $countstatus; ?>">
<input type="hidden" id="cats" value="">
<input type="hidden" id="catcolors">

<?php include getScriptsGlobal($component);?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/spectrum/1.8.0/spectrum.min.js"></script>

<script>

function createTable(numcols){
    
    numcols = parseInt(numcols)
	numcolselected = parseInt($('#numcolselected').val())
	if (numcols != "none" && numcols > 0){
		if (numcols > numcolselected && numcolselected > 0){
			numcells = numcols - numcolselected
			html = ""
			for (i = 1; i <= numcells; i++){
				html += "<td data-mdb-drag-handle='.draggable-drag-ico' id='"+(parseFloat(i)+parseFloat(numcolselected))+"' class='catclass sortable-item' contenteditable='true'><i class='fas fa-arrows-alt draggable-drag-ico'></i> Category</td>"
			}	
	
			$('#catrow').append(html)

			initializeSorting()

			$('#numcolselected').val(numcols)
		}else if (numcols < numcolselected && numcolselected > 0){
			sbconfirm("Reset Categories","This will reset your Categories?",
				
			function() {
					html = "<h5>Now, enter the name of each category you want in your Workflow</h5>"
					html += "<table class='table table-border'><tr id='catrow'>"
					for (i = 1; i <= numcols; i++){
						html += "<td data-mdb-drag-handle='.draggable-drag-ico' id='"+i+"' class='catclass sortable-item' style='border:2px solid grey' contenteditable='true'><i class='fas fa-arrows-alt draggable-drag-ico'></i> Category</td>"
					}
					html += "</tr></table>"
					html += '<span class="btn btn-primary btn-lg" onclick="saveCats()">Save Categories</span>'

					$('#builder').html(html)
					$('#numcolselected').val(numcols)
					initializeSorting()
			});
		}else if (numcols != "none" && numcols > 0 && numcolselected == 0){
			html = "<br><h4>Now, enter the name of each category you want in your Workflow</h4>"
			html += "<table class='table table-border'><tr id='catrow'>"
			for (i = 1; i <= numcols; i++){
				html += "<td data-mdb-drag-handle='.draggable-drag-ico' id='"+i+"' class='catclass sortable-item' contenteditable='true'><i class='fas fa-arrows-alt draggable-drag-ico'></i> Category</td>"
			}
			html += "</tr></table>"
			html += '<span class="btn btn-primary btn-lg" onclick="saveCats()">Save Categories</span>'
			$('#builder').html(html)
			$('#numcolselected').val(numcols)
			initializeSorting()
		}
	}
}

function initializeSorting()
{
	const sortableEl = document.getElementById('catrow');
    const instanceSortable = new DragAndDrop.Sortable(sortableEl);
}

function hasDuplicates(a) {

  const noDups = new Set(a);

  if (a.length !== noDups.size) {
    return true;
  } else {
    return false;
  }
}

function saveCats(){

	ar = []
	$('#catrow').each(function(){
		$('td', this).each(function () {
		ar.push($(this).html())
		})
	})

    if(hasDuplicates(ar))
    {
    	sbalert("Category names should be different")
        return;
    }
	
	$('#cats').val(ar)
    html = "<br><br><center><h5>2. Now, Select a Color for each Category</h5></center><div class='row'><div class='col-md-4 mx-auto'><select size='" + ar.length + "' id='catforcolor' class='form-control text-center'>"
    for (i = 0; i < ar.length; i++) {

        html += "<option value='" + ar[i] + "'>" + ar[i] + "</option>"

    }
    html += "</select></div></div><br><br><center><div id='colorpicker'></div></center>"
    html += "<br><span class='btn btn-primary btn-sm' onclick='saveAll()'>Save Workflow</span>"

    $('#colors').html(html).show();

    html = ''

    var palette = ["#FFB6C1", "#FF69B4", "#FF1493", "#DB7093", "#C71585", "#E6E6FA", "#D8BFD8", "#DDA0DD", "#DA70D6", "#EE82EE", "#FF00FF", "#BA55D3", "#9932CC", "#9400D3", "#8A2BE2", "#800080", "#9370DB", "#7B68EE", "#6A5ACD", "#483D8B", "#663399", "#4B0082", "#FFA07A", "#FA8072", "#E9967A", "#F08080", "#CD5C5C", "#DC143C", "#FF0000", "#B22222", "#8B0000", "#FFA500", "#FF8C00", "#FF7F50", "#FF6347", "#FF4500", "#FFD700", "#FFFF00", "#ADFF2F", "#7FFF00", "#7CFC00", "#00FF00", "#32CD32", "#98FB98", "#90EE90", "#00FA9A", "#00FF7F", "#3CB371", "#2E8B57", "#228B22", "#008000", "#006400", "#9ACD32", "#6B8E23", "#556B2F", "#00FFFF", "#E0FFFF", "#AFEEEE", "#7FFFD4", "#40E0D0", "#5F9EA0", "#4682B4", "#B0C4DE", "#ADD8E6", "#B0E0E6", "#87CEFA", "#87CEEB", "#6495ED", "#00BFFF", "#1E90FF", "#4169E1", "#0000FF", "#0000CD", "#00008B", "#000080", "#191970", "#FFF8DC", "#FFE4C4", "#FFDEAD", "#F5DEB3", "#DEB887", "#D2B48C", "#BC8F8F", "#F4A460", "#DAA520", "#B8860B", "#CD853F", "#D2691E", "#808000", "#8B4513", "#A0522D", "#A52A2A", "#800000"];

    for (var i = 0; i < palette.length; i++) {

        html += "<div class='colordiv' style='background-color:" + palette[i] + "'><i class='fas fa-solid fa-check colorcheck' style='display:none'></i></div>"
    }

    $('#colorpicker').html(html)

    $('.colordiv').on('click', function () {

        if ($(this).find('.colorcheck').is(":hidden")) {
            $('.colorcheck').hide()
            $(this).find('.colorcheck').show()

            var selcell = $('#catforcolor').val()
            var x = $(this).css('background-color')

            $('#catrow').each(function () {
                $('td', this).each(function () {
                    cellval = $(this).html()
                    if (cellval.toUpperCase() == selcell.toUpperCase()) {
                        $(this).css("border", "2px " + x + " solid")
                    }
                })
            })
        }


    })
	

}

function save(id){

	if ($('#'+id).is(':checked')){
		ds = "t=savesetting&shopid=<?php echo $shopid; ?>&id="+id+"&val=yes"
	}else{
		ds = "t=savesetting&shopid=<?php echo $shopid; ?>&id="+id+"&val=no"
	}

	$.ajax({
	
		data: ds,
		url: "saveboard.php",
		type: "post",
		success: function(r){
		},
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		}	
	
	})

}


function saveAll(){

<?php
if ($countdata > 0){
?>
	sbconfirm("Save Workflow?","Please cick confirm to save",
		
	function() {
<?php
}
?>

		ar = []
		$('#catrow').each(function(){
			$('td', this).each(function () {

				var id=''
				if(typeof $(this).data('id') !== 'undefined' && $(this).data('id')!='')
			    id=$(this).data('id')

				ar.push([$(this).html(),$(this).css("border-color"),id])
			})
		})
		ar = JSON.stringify(ar)
		showLoader()
		$.ajax({
		
			data: "t=saveworkflow&shopid=<?php echo $shopid; ?>&data="+ar,
			url: "saveboard.php",
			type: "post",
			success: function(r){
				location.href='board.php'
			},
			error: function (xhr, ajaxOptions, thrownError) {
				console.log(xhr.status);
				console.log(xhr.responseText);
				console.log(thrownError);
			}	
		
		})
<?php
if ($countdata > 0){
?>
	});
<?php
}
?>
}


</script>


</body>
</html>