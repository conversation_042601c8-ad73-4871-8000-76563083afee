<?php
require CONN;
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$shopid = $_POST['shopid'];
$t = $_POST['t'];

if ($t == "updateitem") {
    $status = strtoupper($_POST['status']);
    $roid = $_POST['id'];
    //see if the ro is in the kanbandata table
    $stmt = "select count(*) c from kanbandata where shopid = ? and roid = ?";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $roid);
        $query->execute();
        $query->bind_result($ecnt);
        $query->fetch();
        $query->close();
    }

    if ($ecnt > 0) {
        $stmt = "update kanbandata set kanbanstatus = ? where shopid = ? and roid = ?";

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("ssi", $status, $shopid, $roid);
            $query->execute();
            $conn->commit();
            $query->close();
            echo "success";
        }
    } else {
        $stmt = "insert into kanbandata (roid,shopid,kanbanstatus) values (?,?,?)";

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("iss", $roid, $shopid, $status);
            $query->execute();
            $conn->commit();
            $query->close();
            echo "success";
        }
    }
} elseif ($t == "unset") {
    $rocsv = "";
    $stmt = "select roid from kanbandata where shopid = ?";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_assoc()) {
            $rocsv .= $rs['roid'].",";
        }
        $query->close();
    }

    if (substr($rocsv, -1) == ",") {
        $rocsv = substr($rocsv, 0, strlen($rocsv) - 1);
    }

    // use $rocsv to see if any are not in the kanbandata
    $stmt = "select count(*) c from repairorders where roid not in ($rocsv) and shopid = ? and status != 'closed' and rotype != 'no approval'";
    //echo $stmt."<BR>";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($openrocount);
        $query->fetch();
        $query->close();
    }

    echo $openrocount;
} elseif ($t == "saveworkflow") {
    $data = $_POST['data'];
    $json = json_decode($data);
    $newst = array();

    if (!empty($json)) {
        foreach ($json as $v) {
            $cat = strtoupper(strip_tags($v[0]));
            $id = "";
            if (isset($v[2]) && !empty($v[2])) {
                $id = $v[2];
            }
            if (!empty($id)) {
                $stmt = "select `status` from kanbanstatuses where shopid=? and id=?";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("ss", $shopid, $id);
                    $query->execute();
                    $query->bind_result($status);
                    $query->fetch();
                    $query->close();

                    $newst[] = "'".$cat."'";

                    $ustmt = "update kanbandata set kanbanstatus=? where shopid=? and kanbanstatus=?";
                    if ($query = $conn->prepare($ustmt)) {
                        $query->bind_param("sss", $cat, $shopid, $status);
                        $query->execute();
                        $conn->commit();
                        $query->close();
                    }
                }
            }
        }
    }

    //echo $data."\r\n";
    $stmt = "delete from kanbanstatuses where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $conn->commit();
        $query->close();
    }

    if (!empty($newst)) {
        $stmt = "delete from kanbandata where shopid = ? and kanbanstatus not in(".implode(',', $newst).")";
    } else {
        $stmt = "delete from kanbandata where shopid = ?";
    }

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $conn->commit();
        $query->close();
    }

    $c = 1;

    if (!empty($json)) {
        foreach ($json as $v) {
            $cat = strtoupper(strip_tags($v[0]));
            $col = $v[1];
            $stmt = "insert into kanbanstatuses (`status`,shopid,color,`order`) values (?,?,?,?)";

            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sssi", $cat, $shopid, $col, $c);
                $query->execute();
                $conn->commit();
                $query->close();
            }

            $c += 1;
        }
    }
} elseif ($t == "getpayments") {
    $roid = $_POST['roid'];
    $stmt = "select totalro,balance from repairorders where shopid = ? and roid = ?";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $roid);
        $query->execute();
        $query->bind_result($tro, $balance);
        $query->fetch();
        $query->close();
        $balance = number_format($balance, 2);
        $tro = number_format($tro, 2);
    }

    $stmt = "select ptype,amt,pdate from accountpayments where shopid = ? and roid = ?";
    $pmts = 0;

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $roid);
        $query->execute();
        $r = $query->get_result();
        echo "<table class='table table-condensed table-header-bg'>";
        echo "<thead><tr><td>Date</td><td>Type</td><td class='text-right'>Amount</td></tr></thead><tbody>";
        while ($rs = $r->fetch_assoc()) {
            $d = date("m/d/Y", strtotime($rs['pdate']));
            $a = number_format($rs['amt'], 2);
            $type = strtoupper($rs['ptype']);
            $pmts += $rs['amt'];
            echo "<tr><td>$d</td><td>$type</td><td class='text-right'>$a</td></tr>";
        }
        $query->close();
        echo "</tbody></table>";
    }

    $pmts = number_format($pmts, 2);
    echo "<div class='row'><div class='col-sm-9 text-right'><h4>Total RO: </h4></div><div class='col text-right'><h4>$tro</h4></div></div>";
    echo "<div class='row'><div class='col-sm-9 text-right'><h4>Payments Rec'd: </h4></div><div class='col text-right'><h4>$pmts</h4></div></div>";
    echo "<div class='row'><div class='col-sm-9 text-right'><h4>Balance Due: </h4></div><div class='col text-right'><h4>$balance</h4></div></div></div>";
} elseif ($t == "getstatus") {
    $roid = $_POST['roid'];
    $stmt = "select status,email from repairorders where shopid = ? and roid = ?";
    echo "select status,email from repairorders where shopid = '$shopid' and roid = $roid";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $roid);
        $query->execute();
        $query->bind_result($status, $email);
        $query->fetch();
        $query->close();
        echo $status."|".$email;
    }
} elseif ($t == "getemailcell") {
    $roid = $_POST['roid'];
    $stmt = "select email,cellphone from repairorders where shopid = ? and roid = ?";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ss", $shopid, $roid);
        $query->execute();
        $query->bind_result($email, $cell);
        $query->fetch();
        $query->close();
        echo $email."|".$cell;
    }
} elseif ($t == "getphones") {
    $roid = $_POST['roid'];
    $stmt = "select customerphone,customerwork,cellphone from repairorders where shopid = ? and roid = ?";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ss", $shopid, $roid);
        $query->execute();
        $query->bind_result($customerphone, $customerwork, $cellphone);
        $query->fetch();
        $query->close();
        echo $customerphone."|".$customerwork."|".$cellphone;
    }
} elseif ($t == "getcomms") {
    $roid = $_POST['roid'];
    $stmt = "select `datetime`,`by`,comm from repairordercommhistory where shopid = ? and roid = ?";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $roid);
        $query->execute();
        $result = $query->get_result();
        $query->store_result();
        while ($row = $result->fetch_assoc()) {
            ?>
            <div class="row">
                <div style="font-size:12pt;" class="col-md-4"><?php echo $row['datetime'].' - '.$row['by']; ?></div>
                <div style="font-size:12pt;border-bottom:1px silver solid"
                     class="col-md-8"><?php echo strtoupper($row['comm']); ?></div>
            </div>
            <?php
        }
    }
} elseif ($t == "closero") {
    $roid = $_POST['roid'];
    $stmt = "delete from kanbandata where shopid = ? and roid = ?";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $roid);
        $query->execute();
        $conn->commit();
        $query->close();
        echo "success";
    }
} elseif ($t == "savesetting") {
    $id = $_POST['id'];
    $val = $_POST['val'];
    $stmt = "update kanbansettings set $id = ? where shopid = ?";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ss", $val, $shopid);
        $query->execute();
        $conn->commit();
        $query->close();
    }
} elseif ($t == "reorder") {
    $roids = $_POST['roids'];
    $roar = explode(",", $roids);
    $c = 0;
    foreach ($roar as $v) {
        $stmt = "update kanbandata set vorder = $c where shopid = ? and roid = ?";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("si", $shopid, $v);
            $query->execute();
            $conn->commit();
            $query->close();
        }
        $c += 1;
    }
} elseif ($t == "closerodata") {
    $roid = $_POST['roid'];
    $stmt = "select milesout,balance,source,overridestatusdate from repairorders where shopid = ? and roid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $roid);
        $query->execute();
        $query->bind_result($milesout, $balance, $source, $overridestatusdate);
        $query->fetch();
        $query->close();
    }

    // now get from company table
    $stmt = "select RequireBalanceRO,RequirePayments,RequireSource,RequireOutMileage from company where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($RequireBalanceRO, $RequirePayments, $RequireSource, $RequireOutMileage);
        $query->fetch();
        $query->close();
    }

    $stmt = "select count(*) c from accountpayments where shopid = ? and roid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $roid);
        $query->execute();
        $query->bind_result($pmtcnt);
        $query->fetch();
        $query->close();
    }

    $stmt = "select count(*) c from complaints where (techreport = '' or isnull(techreport)) and shopid = ? and roid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $roid);
        $query->execute();
        $query->bind_result($notscnt);
        $query->fetch();
        $query->close();
    }

    echo $RequireBalanceRO."|".$RequirePayments."|".$RequireSource."|".$RequireOutMileage."|".$milesout."|".$balance."|".$pmtcnt."|".$source."|".$notscnt."|".$overridestatusdate;
}
