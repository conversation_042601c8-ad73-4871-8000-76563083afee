<script src="https://<?= $_SERVER['SERVER_NAME'] ?>/src/public/assets/workflow/codebase/webix.js?v=7.1.1"
        type="text/javascript"></script>
<script src="https://<?= $_SERVER['SERVER_NAME'] ?>/src/public/assets/workflow/codebase/kanban.js?v=7.1.1"
        type="text/javascript"></script>

<script type="text/javascript">
    let requests = [];

    function createWebixConfig(columnsData) {

        return {
            view: "scrollview",
            scroll: "y",
            height: 20000,
            body: {
                view: "kanban",
                id: "myBoard",
                type: "wide",
                save: function (id, operation) {
                    var item = this.getItem(id);

                    if (item) {
                        status = item.status
                        data = <?php echo json_encode($ds, true); ?>

                        for (i = 0; i < data.length; i++) {
                            if (data[i].status == status) {
                                color = data[i].color
                                break
                            }
                        }
                        ds = "t=updateitem&id=" + id + "&shopid=<?php echo $shopid; ?>&status=" + status
                        let request = $.ajax({
                            data: ds,
                            url: "saveboard.php",
                            type: "post",
                            success: function (r) {
                                if (r == "success") {
                                    var citem = $$("myBoard").getItem(id); // 3 is the item ID
                                    citem.color = color;
                                    $$("myBoard").refresh(id);
                                }
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                            }
                        })

                        requests.push(request);
                    }
                },
                on: {
                    onListAfterDrop: dragStop,
                    onAfterLoad: setBottom
                },
                cols: columnsData.cols,
                data: columnsData.data,
            }
        };
    }

    webix.ready(function () {
    if (!webix.env.touch && webix.env.scrollSize)
        webix.CustomScroll.init();

    updateKanbanData()

    setInterval(updateKanbanData, 60000);

   });


function updateKanbanData() {
    let writerfilter = '';
    let techfilter = '';

    // Check if the dropdown with the specified ID exists
    const dropdown = parent.document.getElementById('WriterDropDownMenu');
    if (dropdown) {
        let writerArr = [];
        let techArr = [];

        // Iterate through all checkboxes within the dropdown to find the selected ones
        dropdown.querySelectorAll('.dropdown-checkbox').forEach(function (checkbox) {
            if (checkbox.checked) {
                if (checkbox.dataset.type === 'writer') {
                    writerArr.push(checkbox.value);
                } else if (checkbox.dataset.type === 'tech') {
                    techArr.push(checkbox.value);
                }
            }
        });

        // Combine the selected values into the filter strings
        writerfilter = writerArr.join(":");
        techfilter = techArr.join(":");
    } else {
        writerfilter = '';
        techfilter = '';
    }

    webix.ajax().post("getdata.php", { writer: writerfilter, tech : techfilter }).then(function(data)
    {
     console.log("updating kanban data..")
     var columnsData = data.json();
     webix_config = createWebixConfig(columnsData);
     webix.ui(webix_config, $$("$scrollview1"),$$("myBoard"));

    }).catch(function(err){
        console.error("Error while fetching data:", err);
    });
}


function dragStop(dragContext, e, list) {
    id = dragContext.start
    var item = $$("myBoard").getItem(id)
    stat = item.status
    item.$css = stat.replace(/ /g,"_")+"css";
    $$("myBoard").refresh(id);

    order = (" " + list.data.order).replace(" ", "")

    ds = "shopid=<?php echo $shopid; ?>&roids=" + order + "&t=reorder"
    let request = $.ajax({
        data: ds,
        url: "saveboard.php",
        type: "post",
        success: function (r) {
        },
        error: function (xhr, ajaxOptions, thrownError) {
        }
    })

    requests.push(request);

    // update status in the db
    oldstat = dragContext.from.config.status
    if (oldstat == "UNCATEGORIZED") {
        setTimeout(function () {
            $.ajax({

                data: "shopid=<?php echo $shopid; ?>&t=unset",
                url: "saveboard.php",
                type: "post",
                success: function (r) {
                    r = parseFloat(r)
                    if (r == 0) {
                    updateKanbanData()
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                }
            })
        }, 700)
    }
}

function printROPrinter() {
    pdf = document.getElementById("invoiceframe")
    pdf.focus()
    pdf.contentWindow.print()
}

function sendInvoiceEmail() {
    roid = $('#selectedroid').val()
    invpath = $('#invpath').val()
    if (invpath.length > 0) {
        showLoader()
        sendto = encodeURIComponent($('#emailto').val())

        if ($('#emailsubjectinv').css('display') == 'none') {
            emailsubject = encodeURIComponent($('#emailsubjectest').val())
            emailmessage = encodeURIComponent($('#emailmessageest').val())
        } else if ($('#emailsubjectest').css('display') == 'none') {
            emailsubject = encodeURIComponent($('#emailsubjectinv').val())
            emailmessage = encodeURIComponent($('#emailmessageinv').val())
        }

        if ($("#updatecustomeremail").is(':checked')) {
            ucstr = "updatecustomer=y&"
        } else {
            ucstr = "updatecustomer=n&"
        }
        ds = ucstr + "roid=" + roid + "&cid=0&shopid=<?php echo $shopid; ?>&sendfrom=<?php echo urlencode($_COOKIE['shopname']);?>&sendto=" + sendto + "&invpath=" + invpath + "&subject=" + emailsubject + "&message=" + emailmessage + "&shopemail=<?php echo urlencode($companyemail); ?>"
        $.ajax({
            data: ds,
            url: "<?= COMPONENTS_PRIVATE ?>/ro/roemailinvoice.php",
            error: function (xhr, ajaxOptions, thrownError) {
            },
            success: function (r) {
                if (r == "success") {
                    hideLoader()
                    sbalert("Invoice Sent")
                    $('#emailinvmodal').modal('hide')
                } else if (r == "success|") {
                    hideLoader()
                    $('#emailinvmodal').modal('hide')
                    sbalert("Invoice Sent.  Reloading with new email address")
                    setTimeout(function () {
                        location.reload()
                    }, 2000)
                }

                printit = $('#printit').val()
                if (printit == "yes") {
                    location.href = '<?= COMPONENTS_PRIVATE ?>/v2/wip/wip.php'
                }
            }
        });

    } else {
        $.ajax({
            data: "shopid=<?php echo $shopid; ?>&roid=" + roid,
            url: "<?= COMPONENTS_PUBLIC ?>/invoices/printpdfro.asp",
            success: function (r) {
                $('#invpath').val(r)
                sendto = encodeURIComponent($('#emailto').val())
                emailsubject = encodeURIComponent($('#emailsubject').val())
                emailmessage = encodeURIComponent($('#emailmessage').val())
                if ($("#updatecustomeremail").is(':checked')) {
                    ucstr = "updatecustomer=y&"
                } else {
                    ucstr = "updatecustomer=n&"
                }
                showLoader()
                ds = ucstr + "roid=" + roid + "&shopid=<?php echo $shopid; ?>&cid=0&sendfrom=<?php echo urlencode($_COOKIE['shopname']);?>&sendto=" + sendto + "&invpath=" + invpath + "&subject=" + emailsubject + "&message=" + emailmessage + "&shopemail=<?php echo urlencode($companyemail); ?>"
                $.ajax({
                    data: ds,
                    url: "<?= COMPONENTS_PRIVATE ?>/ro/roemailinvoice.php",
                    success: function (r) {
                        if (r == "success") {
                            hideLoader()
                            sbalert("Invoice Sent")
                            $('#emailinvmodal').modal('hide')
                        } else if (r == "success|") {
                            $('#emailinvmodal').modal('hide')
                            sbalert("Invoice Sent.  Reloading with new email address")
                            setTimeout(function () {
                                location.reload()
                            }, 2000)
                        }
                    }
                });

            }
        });
    }

}

function getROPayments(roid) {
    showLoader()

    $.ajax({

        data: "t=getpayments&shopid=<?php echo $shopid; ?>&roid=" + roid,
        url: "saveboard.php",
        type: "post",
        success: function (r) {
            $('#payments').html(r);
            hideLoader()
            $('#paymentmodal').modal('show')
        },
        error: function (xhr, ajaxOptions, thrownError) {
        }
    })


}

function emailRO() {
    // first get the status of the RO
    roid = $('#selectedroid').val()
    ds = "t=getstatus&shopid=<?php echo $shopid;?>&roid=" + roid
    $.ajax({
        data: ds,
        url: "saveboard.php",
        type: "post",
        success: function (r) {
            rar = r.split("|")
            stat = rar[0].toLowerCase()
            em = rar[1].toLowerCase()
            if (stat == "closed" || stat == "final") {
                $('#emailsubjectinv').show()
                $('#emailmessageinv').show()
                $('#emailsubjectinvlabel').show()
                $('#emailmessageinvlabel').show()
                s = $('#emailsubjectinv').val()
                if (s.indexOf("#roid#") > -1) {
                    s = s.replace("#roid#", roid)
                }
                $('#emailsubjectinv').val(s)
            } else {
                $('#emailsubjectest').show()
                $('#emailmessageest').show()
                $('#emailsubjectestlabel').show()
                $('#emailmessageestlabel').show()
                s = $('#emailsubjectest').val()
                if (s.indexOf("#roid#") > -1) {
                    s = s.replace("#roid#", roid)
                }
                $('#emailsubjectest').val(s)
            }
            $('#emailto').val(em)

            $('#emailinvmodal').modal('show')

        },
        error: function (xhr, ajaxOptions, thrownError) {
        }
    })


}

function openRO(roid) {
    parent.location.href = '<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?roid=' + roid
}

function openInspections(roid) {
    eModal.iframe({
        title: 'Vehicle Inspection',
        url: '<?= COMPONENTS_PRIVATE ?>/inspection_classic/dvi.php?roid=' + roid + '&shopid=<?php echo $shopid; ?>',
        size: eModal.size.xl,
        buttons: [
            {text: 'Close', style: 'warning', close: true}
        ]
    });
}

function sendUpdate(t) {
    // t = email, text or both
    em = $('#updateemailto').val()
    cp = $('#updatecellphone').val()
    roid = $('#selectedroid').val()

    if (t === "email" && em.length === 0) {
        sbalert("You must have an email to send an email update");
        return
    }
    if (t === "text" && cp.length === 0) {
        sbalert("You must have a cell phone to send an text message update");
        return
    }
    if ((t === "both" && em.length === 0) || (t === "both" && cp.length === 0)) {
        sbalert("You must have an email and cell phone to send an update to both");
        return
    }

    // now send the update via ajax
    showLoader()
    $('.btn-md').attr('disabled', 'disabled')
    ds = "u=update&t=" + t + "&cell=" + cp + "&email=" + em + "&shopid=<?php echo $shopid; ?>&roid=" + roid

    $.ajax({
        data: ds,
        url: "<?= COMPONENTS_PRIVATE ?>/ro/sendupdates.php",
        success: function (r) {
            if (r === "success") {
                sbalert("Your update message has been sent")
                $('#sendupdatemodal').modal('hide')
                hideLoader()
            } else {
                sbalert(r)
                $('#sendupdatemodal').modal('hide')
                hideLoader()
            }
            $('.btn-md').attr('disabled', false)
        },
        error: function (xhr, ajaxOptions, thrownError) {
        }
    })
}

function showVideo() {
    sbconfirm("Opening in a new Window", "This will open the video in a new window.  Click Confirm to proceed",

        function () {
            window.open("https://www.youtube.com/embed/9mE0ZEZ33Os")
        }
    );
}

function closeRO(roid) {
    // requirebalancero, requirepayments, requiresource, requireoutmileage,
    <?php
    if ($shopid == "5605") {
        echo "requirets = 'yes';\r\n";
    } else {
        echo "requirets = 'no';\r\n";
    }
    ?>
    // get the outmiles,balance,payments and source
    $.ajax({
        data: "t=closerodata&shopid=<?php echo $shopid; ?>&roid=" + roid,
        url: "saveboard.php",
        type: "post",
        success: function (r) {
            rar = r.split("|")
            requirebalancero = rar[0].toLowerCase();
            requirepayments = rar[1].toLowerCase();
            requiresource = rar[2].toLowerCase();
            requireoutmileage = rar[3].toLowerCase();
            outmiles = rar[4];
            balance = parseFloat(rar[5]);
            pmts = parseFloat(rar[6]);
            source = rar[7];
            techstory = rar[8]
            overridestatusdate = rar[9].toLowerCase()

            if (requirets == "yes" && techstory == 0) {
                sbalert("Tech Story is Required for All Issues");
                return false

            }

            if (requirepayments == "yes" && pmts == 0) {
                sbalert("You must receive a payment to close the RO");
                return false
            }

            if (requiresource == "yes" && source == "NONE") {
                sbalert("You must enter a Source to close the RO");
                return false
            }

            if (requireoutmileage == "yes" && outmiles == "") {
                sbalert("You must enter Miles Out to close the RO");
                return false
            }

            if (requirebalancero == "yes" && balance != 0) {
                sbalert("You're RO is not balanced.  You cannot close until the balance is zero");
                return false
            }

            sbconfirm("Closing RO", "Ready to close the RO.  Are you sure?",
                function () {
                    ds = "overridestatusdate=" + overridestatusdate + "&t=closero&shopid=<?php echo $shopid; ?>&roid=" + roid + "&s=closed"
                    showLoader()
                    $.ajax({
                        data: ds,
                        type: "post",
                        url: "<?= COMPONENTS_PRIVATE ?>/ro/savedata.php",
                        error: function (xhr, ajaxOptions, thrownError) {
                        },
                        success: function (r) {
                            if (r == "success") {
                                hideLoader()
                                sbconfirm("RO is Closed", "Would you like to print a final copy of the RO?",

                                    function () {
                                        printRO(roid)
                                        removeCard(roid)
                                    }, function () {
                                        removeCard(roid)
                                    });
                            }
                        }
                    });
                });
        },
        error: function (xhr, ajaxOptions, thrownError) {
        }
    })
}

function removeCard(roid) {
    $.ajax({
        data: "t=closero&roid=" + roid + "&shopid=<?php echo $shopid; ?>",
        url: "saveboard.php",
        type: "post",
        success: function (r) {
            if (r == "success") {
                $$("myBoard").remove(roid);
            }
        },
        error: function (xhr, ajaxOptions, thrownError) {
        }
    })
}

function getComms(roid) {
    $('#selectedroid').val(roid)

    // get the cell and email
    $.ajax({
        data: "t=getcomms&shopid=<?php echo $shopid; ?>&roid=" + roid,
        url: "saveboard.php",
        type: "post",
        success: function (r) {
            $('#comms').html(r)
            $('#commmodal').modal('show')

        },
        error: function (xhr, ajaxOptions, thrownError) {
        }
    })
}

function printRO(roid) {
    $('#selectedroid').val(roid)
    showLoader()
    $.ajax({
        data: "shopid=<?php echo $shopid; ?>&roid=" + roid + "&i=new",
        url: "<?= COMPONENTS_PUBLIC ?>/invoices/printpdfro.asp",
        success: function (r) {
            $('#invpath').val(r)

            hideLoader()

            eModal.iframe({
                title: 'Repair Order - buttons are at the bottom of the window',
                url: '<?= COMPONENTS_PUBLIC ?>/invoices' + r,
                size: eModal.size.xl,
                buttons: [
                    {
                        text: 'Print',
                        style: 'primary',
                        close: false,
                        click: function () {
                            $('iframe.embed-responsive-item').attr("id", "ropdfframe")
                            pdf = document.getElementById("ropdfframe")
                            pdf.focus()
                            pdf.contentWindow.print()
                        }
                    },
                    {
                        text: 'Email',
                        style: 'secondary',
                        close: true,
                        click: emailRO
                    }
                ]
            })
        }
    });
}

function openSendUpdate(roid) {
    $('#selectedroid').val(roid)

    // get the cell and email
    $.ajax({
        data: "t=getemailcell&shopid=<?php echo $shopid; ?>&roid=" + roid,
        url: "saveboard.php",
        type: "post",
        success: function (r) {
            if (r.indexOf("|") > -1) {
                rar = r.split("|")
                email = rar[0]
                cell = rar[1]
            } else {
                email = ""
                cell = ""
            }

            $('#selectedemail').val(email)
            $('#updateemailto').val(email)
            $('#updatecellphone').val(cell)
            $('#sendupdatemodal').modal('show')

        },
        error: function (xhr, ajaxOptions, thrownError) {
        }
    })
}

function sendEmailMessage(roid) {
    showLoader()

    if ($('#emailmodal').css("display") == "none") {
        // get the email address from the RO
        $.ajax({
            data: "t=getemailcell&shopid=<?php echo $shopid; ?>&roid=" + roid,
            url: "saveboard.php",
            type: "post",
            success: function (r) {
                rar = r.split("|")
                email = rar[0]

                $('#emailmessageaddress').val(email)

                $('#emailmodal').modal('show')
                setTimeout(function () {
                    $('#emailmessagesubject').focus()
                }, 500)

                hideLoader()

            },
            error: function (xhr, ajaxOptions, thrownError) {
            }
        })
    } else {
        email = $('#emailmessageaddress').val()
        msg = encodeURIComponent($('#emailmessagemessage').val())
        subj = encodeURIComponent($('#emailmessagesubject').val())
        if (email.length >= 1 && msg.length > 0 && subj.length > 0) {
            $.ajax({
                data: "shopid=<?php echo $shopid; ?>&roid=" + roid + "&t=sendemail&email=" + email + "&subj=" + subj + "&msg=" + msg,
                type: "post",
                url: "<?= COMPONENTS_PRIVATE ?>/ro/saveData.php",
                error: function (xhr, ajaxOptions, thrownError) {
                },
                success: function (r) {
                    hideLoader()
                    if (r == "success") {
                        sbalert("Email Message Sent")
                        $('#emailmodal').modal('hide')
                    }
                }
            });
        } else {
            sbalert("You must enter an email, subject and message")
            hideLoader()
        }
    }
}

function showGP(roid, subtotal) {
    if ($('#gpbutton').attr("class") == "btn ") {
        sbalert("You have no parts or labor to calcuate GP")
    } else {
        url = "<?= COMPONENTS_PRIVATE ?>/v2/gp/gpdetail.php?shopid=<?php echo $shopid; ?>&roid=" + roid + "&subtotal=" + subtotal
        $('#gpframe').attr("src", url)
        $('#gpmodal').modal('show')
    }
}

function showPhones(roid) {
    showLoader()

    $.ajax({
        data: "t=getphones&shopid=<?php echo $shopid; ?>&roid=" + roid,
        url: "saveboard.php",
        type: "post",
        success: function (r) {
            $('#homephone').html('')
            $('#workphone').html('')
            $('#cellphone').html('')
            $('#nophone').css('display', 'none');
            if (r.indexOf("|") >= 0) {
                rar = r.split("|")
                h = rar[0]
                w = rar[1]
                c = rar[2]
            } else {
                h = ""
                w = ""
                c = ""
            }

            if (h.length > 0) {
                $('#homephone').html("HOME: " + h.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3'))
            }
            if (w.length > 0) {
                $('#workphone').html("WORK: " + w.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3'))
            }
            if (c.length > 0) {
                $('#cellphone').html("CELL: " + c.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3'))
                $('#selectedcell').val(c)
            }
            if (h.length == 0 && w.length == 0 && c.length == 0) {
                $('#nophone').css('display', 'block');
                $('#selectedcell').val('')
            }

            hideLoader()

            $('#phonemodal').modal('show')

        },
        error: function (xhr, ajaxOptions, thrownError) {
        }
    })
}

function sendTextMessage(c, roid) {
    if ($('#textmodal').css("display") == "none") {
        c = $('#selectedcell').val()
        $('#textmessagecell').val(c)
        $('#textmodal').modal('show')
        setTimeout(function () {
            $('#textmessage').focus()
        }, 500)
        $('#phonemodal').modal('hide')
    } else {
        // send text message
        showLoader()
        cell = $('#textmessagecell').val()
        sms = $('#textmessage').val()
        if (cell.length == 10 && sms.length > 0) {
            $.ajax({
                data: "shopname=<?php echo urlencode($shopname); ?>&shopid=<?php echo $shopid; ?>&roid=" + roid + "&t=sendsms&cell=" + cell + "&sms=" + sms,
                type: "post",
                url: "<?= COMPONENTS_PRIVATE ?>/ro/saveData.php",
                error: function (xhr, ajaxOptions, thrownError) {
                },
                success: function (r) {
                    hideLoader()
                    sbalert("Text Message Sent")
                    $('#textmodal').modal('hide')
                }
            });
        } else {
            sbalert("You must have a 10 digit cell number and type a message to send")
            hideLoader()
        }
    }
}

function pauseCountdown() {
    cs = $('#counterstatus').val()
    if (cs == "active") {
        $('#counterstatus').val('paused')
        $('#countdown').countdown('pause')
        failsafetime = new Date()
        failsafetime.setSeconds(failsafetime.getSeconds() + 300)

        $('#failsafecounter').countdown({
            until: failsafetime,
            onExpiry: function () {
                location.reload()
            }
        })
    } else {
        $('#counterstatus').val('active')
        $('#countdown').countdown('resume')
        $('#failsafecounter').countdown('destroy')
    }
}

$(document).ready(function () {
    $('#srch').bind('keyup change', function (ev) {
        var searchTerm = $(this).val().toLowerCase();

        $("div.webix_list_item").filter(function () {

            var $this = $(this)
            if (searchTerm != '' && $this.text().toLowerCase().indexOf(searchTerm) == -1) {
                $this.hide();
            } else
                $this.show()
        })
    });

    $('#srch').focus()
})

function setBottom() {
    $(".webix_full_screen").css("overflow-y", "auto")

    <?php if ($i1 == "yes") { ?>

    setTimeout(function () {
        t = 0
        currt = 0
        $('.webix_view.webix_layout_clean').each(function () {

            viewid = $(this).attr("view_id")

            if ($("div[view_id='" + viewid + "'] .webix_list_item.webix_kanban_list_item").last().offset()) {

                currt = $("div[view_id='" + viewid + "'] .webix_list_item.webix_kanban_list_item").last().offset().top
                currt = parseFloat(currt)
                if (currt > t) {
                    t = currt
                }
            }
        });
        t = t + 500

        $('.webix_view.webix_layout_clean').css("height", t + "px")
        $('.webix_view.webix_kanban.webix_layout_wide').css("height", t + "px")
        $('.webix_view.webix_scrollview').css("height", t + "px")
        $('.webix_view.webix_accordionitem.vertical').css("height", t + "px")
        $('.webix_view.webix_list.webix_kanban_list').css("height", t + "px")
    }, 1000)
    <?php } ?>
}

            
</script>
