<?php
require CONN;

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$writerfilter = $_POST['writer'];
$techfilter = $_POST['tech'];
$empid = $_COOKIE['empid'] ?? '';
$data = $cols = array();
$filter = $loggedtech = '';
$techsupervisor = $showcustomerinfo = 'yes';

if (!empty($writerfilter) && $writerfilter != 'all') {
    $writerfilter_clause = "'".str_replace(":", "','", $writerfilter)."'";
    $filter .= " and writer IN ($writerfilter_clause) ";
}

$stmt = "select * from kanbansettings where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($id, $sid, $i1, $i2, $i3, $i4, $i5, $i6, $i7, $i8, $i9, $i10, $i11);
    $query->fetch();
    $query->close();
}

$stmt = "select count(*) c from kanbanstatuses where shopid = ?";

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($countstatus);
    $query->fetch();
    $query->close();
}

if($_COOKIE['mode'] == 'tech2' && $empid!='Admin')
{
    $stmt = "select lower(techsupervisor),lower(showcustomerinfo) from techpermissions where shopid = '$shopid' and empid = $empid";

    if ($query = $conn->prepare($stmt)){
        $query->execute();
        $query->store_result();
        $query->bind_result($techsupervisor,$showcustomerinfo);
        $query->fetch();
    }

    $stmt = "select employeelast,employeefirst from employees where id = ? and shopid = ?";

    if ($query = $conn->prepare($stmt)) {

        $query->bind_param("is", $empid, $shopid);
        $query->execute();
        $query->bind_result($employeelast,$employeefirst);
        $query->fetch();
        $query->close();
    } 

    $loggedtech = strtoupper($employeelast.", ".$employeefirst);

}


function getContrastColor($hexcolor)
{
    if ($hexcolor != "") {
        $r = hexdec(substr($hexcolor, 1, 2));
        $g = hexdec(substr($hexcolor, 3, 2));
        $b = hexdec(substr($hexcolor, 5, 2));
        $yiq = (($r * 299) + ($g * 587) + ($b * 114)) / 1000;
        return ($yiq >= 128) ? 'black' : 'white';
    } else {
        return "black";
    }
}

function validateDate($date, $format = 'Y-m-d H:i:s')
{
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) == $date;
}

function seconds2human($ss)
{
    $time = $ss; //whatever
    $seconds = $time % 60;
    $mins = floor($time / 60) % 60;
    $hours = floor($time / 60 / 60) % 24;
    $days = floor($time / 60 / 60 / 24);

    if ($days > 0) {
        return "$days:$hours:$mins";
    } else {
        return "$hours:$mins";
    }
}

$stmt = "select companyphone,companyemail,definvmsgemail,requirebalancero,requirepayments,requiresource,requireoutmileage,companyname,sortwipbylastfirst,firstlastonwip,showpromiseonwip,nexpartpassword as showcommlog,mailpassword as showbalanceonwip,showemailestimateonwip, showemailinvoiceonwip, showtimeclockonwipdata, showpaymentonwip, showinspectiononwip, showinpectionemailonwip, showtechoverhours,nexpartusername showpics,shopmgr from company where shopid = ?";
//echo $stmt.";<BR>";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($shopphone, $companyemail, $definvmsgemail, $requirebalancero, $requirepayments, $requiresource,
        $requireoutmileage, $shopname, $sortwipbylastfirst, $firstlastonwip, $showpromiseonwip, $showcommlog,
        $showbalanceonwip, $showestemail, $showinvemail, $showtechtime, $showmoney, $showinsp, $showinspemail,
        $showtechoverhours, $showpics, $showelapsed);
    $query->fetch();
    $query->close();
} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

if ($definvmsgemail == "") {
    $definvmsgemail = "Your repair order #roid# from $shopname is attached.  Please look it over and let us know if you have any questions. $shopphone";
}

$stmt = "SELECT emailinvoice,emailinvoicesubject,emailestimate,emailestimatesubject,emailesigrequest,emailesigrequestsubject from companymessages WHERE shopid = '$shopid'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $query->store_result();
    $nummsgrows = $query->num_rows;
    if ($nummsgrows > 0) {
        $query->free_result();
        $query->execute();
        $query->bind_result($emailinvoice, $emailinvoicesubject, $emailestimate, $emailestimatesubject,
            $emailesigrequest, $emailesigrequestsubject);
        $query->fetch();
    } else {
        $emailinvoice = str_replace("[shopname]", $_COOKIE['shopname'], $definvmsgemail);
        $emailinvoicesubject = 'Your repair invoice #roid# from ' . $_COOKIE['shopname'];
        $emailestimate = str_replace("[shopname]", $_COOKIE['shopname'], $definvmsgemail);
        $emailestimatesubject = 'Your repair estimate #roid# from ' . $_COOKIE['shopname'];
        $emailesigrequest = $_COOKIE['shopname'] . ' is requesting an E-Signature on a document. Please click the link below to E-Sign the document. Thank you for your business!';
        $emailesigrequestsubject = 'A Request for an E-Signature from ' . $_COOKIE['shopname'];
    }
    $query->close();
}


// check for any open RO's that are not here
$rocsv = "";
$stmt = "select roid from kanbandata where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_assoc()) {
        $rocsv .= $rs['roid'] . ",";
    }
    $query->close();
}
if (substr($rocsv, -1) == ",") {
    $rocsv = substr($rocsv, 0, strlen($rocsv) - 1);
}

if ($rocsv == "") {
    $rocsv = "0000";
}

// use $rocsv to see if any are not in the kanbandata
$stmt = "select count(*) c from repairorders where roid not in ($rocsv) and shopid = ? and status != 'closed' and rotype != 'no approval'";
//echo $stmt.";<BR>";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($openrocount);
    $query->fetch();
    $query->close();
}

if ($openrocount > 0) {

    $cols[] = array("type" => "clean",
            "rows" => array(
                array("template" => "UNCATEGORIZED", "type" => "header"),
                array("body" => array("type" => "clean", "view" => "kanbanlist", "status" => "UNCATEGORIZED", '$css' => "test"))
            )
        );

} 

$stmt = "select count(*) c from kanbandata where shopid = ? and kanbanstatus = 'uncategorized'";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($uncatcount);
    $query->fetch();
    $query->close();
}

if ($uncatcount > 0 && empty($cols)) {
    $cols[] = array("type" => "clean",
            "rows" => array(
                array("template" => "UNCATEGORIZED", "type" => "header"),
                array("body" => array("type" => "clean", "view" => "kanbanlist", "status" => "UNCATEGORIZED", '$css' => "test"))
            )
        );
}

// now check for any closed that need to be removed
$stmt = "select roid from repairorders where roid in ($rocsv) and shopid = ? and status = 'closed' and rotype != 'no approval'";
//echo $stmt.";<BR>";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_assoc()) {
        // any roid found here needs to be deleted from the kanbandata table
        $dstmt = "delete from kanbandata where shopid = ? and roid = ?";
        //echo "delete from kanbandata where shopid = '$shopid' and roid = $roid";
        if ($dquery = $conn->prepare($dstmt)) {
            $dquery->bind_param("si", $shopid, $rs['roid']);
            $dquery->execute();
            $conn->commit();
            $dquery->close();
        }
    }
}

$ds = array();
    $stmt = "select status,color from kanbanstatuses where shopid = ? order by `order`";
    if ($query = $conn->prepare($stmt)){
        $query->bind_param("s",$shopid);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_assoc()){
            $ds[] = $rs;
        }
    }


foreach ($ds as $hval) {

    $cols[] = array("type" => "clean",
            "rows" => array(
                array("template" => strtoupper($hval['status']), "type" => "header"),
                array("body" => array("type" => "clean", "view" => "kanbanlist", "status" => strtoupper($hval['status']), '$css' =>  str_replace(" ","_", strtoupper($hval['status'])) . "css"))
            )
        );

    
}
$displaytime = "";

$roidar = array();
$cardbody = "";

// get the ros not in the kanbandata table
if ($openrocount > 0) {
    $stmt = "SELECT writer,datein,timein,datetimepromised,balance,vehlicense,vin,ro.cellphone,ro.customerphone,"
        . "ro.customerwork,ro.customerfirst,ro.customerlast,ro.email,ro.totalro,ro.customerid,ro.shopid,ro.roid,"
        . "ro.customer,ro.vehinfo,ro.rotype,ro.totallbrhrs, ro.tagnumber FROM repairorders ro"
        . " WHERE ro.shopid = ? and ro.roid not in ($rocsv) and status != 'closed' and rotype != 'no approval' {$filter} order by roid desc";
    //echo $stmt.";<BR>";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_assoc()) {
            // get the techs on labor

            if (!empty($techfilter) && $techfilter != 'all') {
                $techfilter_arr = explode(":", $techfilter);
                $tstmt = "select tech,laborhours from labor where tech != 'discount, discount' and deleted = 'no' and shopid = '" . $shopid . "' and roid = " . $rs["roid"];
                $tstmt .= " AND ( ";
                $count = 0;
                foreach ($techfilter_arr as $tech) {
                    if ($count) {
                        $tstmt .= " OR ";
                    }
                    $tstmt .= " tech = '$tech' ";
                    $count++;
                }
                $tstmt .= " )";
                //die ($tstmt);
                $tresult = $conn->query($tstmt);
                if ($tresult->num_rows <= 0) {
                    continue;
                }
            }

            $techar = array();
            $techmatch=false;
            $tstmt = "select distinct tech from labor where shopid = '$shopid' and roid = " . $rs['roid'];
            if ($tquery = $conn->prepare($tstmt)) {
                //$tquery->bind_param("si",$shoid,$rs['roid']);
                if ($tquery->execute()) {
                    $tr = $tquery->get_result();
                    while ($trs = $tr->fetch_assoc()) {

                        if(htmlspecialchars_decode(strtoupper($loggedtech),ENT_QUOTES)==htmlspecialchars_decode(strtoupper($trs['tech']),ENT_QUOTES))
                        $techmatch = true;

                        array_push($techar, $trs['tech']);
                    }
                } else {
                    echo $conn->error;
                }
            }

            if($_COOKIE['mode'] == 'tech2' && $empid!='Admin' && $techsupervisor!='yes' && !$techmatch)continue;

            array_push($roidar, $rs['roid']);

            $customer = '';
            if (!empty($rs['customerlast'])) {
                $customer .= $rs['customerlast'];
            }
            if (!empty($rs['customerfirst'])) {
                $customer .= (!empty($rs['customerlast']) ? ", " . $rs['customerfirst'] : $rs['customerfirst']);
            }

            $cardbody = "<div class='row'>"
                . "<div class='col'>" . ucwords(strtolower($customer)) . "</div>"
                . "<div class='col' align='right'><a class='text-primary' target='_blank' href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?roid=" . $rs['roid'] . "'>RO #" . $rs['roid'] . (isset($rs['tagnumber']) && $rs['tagnumber'] ? (" - " . $rs['tagnumber']) : "") . "</a></div></div>"
                . "<div class='row'><div class='col'>" . strtoupper($rs['rotype']) . "</div>";

            if ($showcustomerinfo == "yes" && $i2 == "yes") {
                $cardbody .= "<div class='col' align='right'><i class='fas fa-sm fa-envelope me-2' data-mdb-toggle='tooltip' data-mdb-placement='top' title='SEND EMAIL' onclick='sendEmailMessage(" . $rs['roid'] . ")'></i> <i class='fas fa-sm fa-phone' data-mdb-toggle='tooltip' data-mdb-placement='top' title='SHOW ALL PHONES' onclick='showPhones(" . $rs['roid'] . ")'></i></div>";
            }

            $cardbody .= "</div><div class='row text-center'>"
                . "<div class='col-sm-12'>" . addslashes($rs['vehinfo']);
            if ($i9 == 'yes' && strlen($rs['vin']) > 5) {
                $cardbody .= " - " . $rs['vin'];
            }

            $cardbody .= "</div></div>"

                . "<div class='row'>"
                . "<div class='col'>IN:" . date("m/d/Y", strtotime($rs['datein']))
                . "</div>"
                . "</div>"
                . "<div class='row'>"
                . "<div class='col-sm-12'>Activity:";

            $cardbody .= "</div>"
                . "</div>"
                . "<div class='row'>"
                . "<div class='col-sm-12'>Writer: " . strtoupper($rs['writer'])
                . "</div>"
                . "</div>";

                if($techsupervisor == 'yes')
                {
                   $cardbody .= "<div class='row'>"
                    . "<div class='col-sm-12'>Tech(s): ";
                $techs = "";
                foreach ($techar as $tech) {
                    $techs .= $tech . " <b>/</b> ";
                }
                if (substr($techs, -2) == ", ") {
                    $techs = substr($techs, 0, strlen($techs) - 2);
                }

                
                $cardbody .= $techs . "</div>"
                    . "</div>";
                }

            if ($i10 == "yes") {
                $cardbody .= "<div class='row'><div class='col-sm-12'>Labor Hours: " . $rs['totallbrhrs'] . "</div></div>";
            }

            $data[] = array("id" => $rs['roid'], "status" => "UNCATEGORIZED", "text" => $cardbody, "color" => "purple");

        }
    }
}

//  get all from kanbandata table with status UNCATEGORIZED

$stmt = "SELECT k.roid,k.kanbanstatus,writer,customerlast,customerfirst,vehinfo,vin,rotype,email,datein,totallbrhrs from kanbandata k inner join repairorders r on k.shopid = r.shopid and k.roid = r.roid where k.shopid = ? and kanbanstatus = 'UNCATEGORIZED' {$filter}";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);

    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_assoc()) {

        if (!empty($techfilter) && $techfilter != 'all') {
            $techfilter_arr = explode(":", $techfilter);
            $tstmt = "select tech,laborhours from labor where tech != 'discount, discount' and deleted = 'no' and shopid = '" . $shopid . "' and roid = " . $rs["roid"];
            $tstmt .= " AND ( ";
            $count = 0;
            foreach ($techfilter_arr as $tech) {
                if ($count) {
                    $tstmt .= " OR ";
                }
                $tstmt .= " tech = '$tech' ";
                $count++;
            }
            $tstmt .= " )";
            //die ($tstmt);
            $tresult = $conn->query($tstmt);
            if ($tresult->num_rows <= 0) {
                continue;
            }
        }

        // get the techs on labor
        $techar = array();
        $techmatch=false;
        $tstmt = "select distinct tech from labor where shopid = '$shopid' and roid = " . $rs['roid'];
        if ($tquery = $conn->prepare($tstmt)) {
            //$tquery->bind_param("si",$shoid,$rs['roid']);
            if ($tquery->execute()) {
                $tr = $tquery->get_result();
                while ($trs = $tr->fetch_assoc()) {

                    if(htmlspecialchars_decode(strtoupper($loggedtech),ENT_QUOTES)==htmlspecialchars_decode(strtoupper($trs['tech']),ENT_QUOTES))
                    $techmatch = true;

                    array_push($techar, $trs['tech']);
                }
            } else {
                echo $conn->error;
            }
        }

        if($_COOKIE['mode'] == 'tech2' && $empid!='Admin' && $techsupervisor!='yes' && !$techmatch)continue;

        array_push($roidar, $rs['roid']);

        $customer = '';
        if (!empty($rs['customerlast'])) {
            $customer .= $rs['customerlast'];
        }
        if (!empty($rs['customerfirst'])) {
            $customer .= (!empty($rs['customerlast']) ? ", " . $rs['customerfirst'] : $rs['customerfirst']);
        }

        $cardbody = "<div class='row'>"
            . "<div class='col'>" . ucwords(strtolower($customer)) . "</div>"
            . "<div class='col' align='right'><a class='text-primary' target='_blank' href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?roid=" . $rs['roid'] . "'>RO #" . $rs['roid'] . (isset($rs['tagnumber']) && $rs['tagnumber'] ? (" - " . $rs['tagnumber']) : "") . "</a></div></div>"
            . "<div class='row'><div class='col'>" . strtoupper($rs['rotype']) . "</div>";

        if ($showcustomerinfo == "yes" && $i2 == "yes") {
            $cardbody .= "<div class='col' align='right'>";
            
            if($_COOKIE['mode'] == 'full')
            $cardbody .= "<i class='fas fa-sm fa-envelope me-2' data-mdb-toggle='tooltip' data-mdb-placement='top' title='SEND EMAIL' onclick='sendEmailMessage(" . $rs['roid'] . ")'></i> ";

            $cardbody .= "<i class='fas fa-sm fa-phone' data-mdb-toggle='tooltip' data-mdb-placement='top' title='SHOW ALL PHONES' onclick='showPhones(" . $rs['roid'] . ")'></i></div>";
        }

        $cardbody .= "</div><div class='row text-center'>"
            . "<div class='col-sm-12'>" . addslashes($rs['vehinfo']);
        if ($i9 == 'yes' && strlen($rs['vin']) > 5) {
            $cardbody .= " - " . $rs['vin'];
        }

        $cardbody .= "</div></div>"

            . "<div class='row'>"
            . "<div class='col'>IN:" . date("m/d/Y", strtotime($rs['datein']))
            . "</div>"
            . "</div>"
            . "<div class='row'>"
            . "<div class='col-sm-12'>Activity:";

        $cardbody .= "</div>"
            . "</div>"
            . "<div class='row'>"
            . "<div class='col-sm-12'>Writer: " . strtoupper($rs['writer'])
            . "</div>"
            . "</div>";

            if($techsupervisor == 'yes')
            {
               $cardbody .= "<div class='row'>"
                . "<div class='col-sm-12'>Tech(s): ";
            $techs = "";
            foreach ($techar as $tech) {
                $techs .= $tech . " <b>/</b> ";
            }
            if (substr($techs, -2) == ", ") {
                $techs = substr($techs, 0, strlen($techs) - 2);
            }

            
            $cardbody .= $techs . "</div>"
                . "</div>";
            }

            
            if ($i10 == "yes") {
                $cardbody .= "<div class='row'><div class='col-sm-12'>Labor Hours: " . $rs['totallbrhrs'] . "</div></div>";
            }

            $data[] = array("id" => $rs['roid'], "status" => "UNCATEGORIZED", "text" => $cardbody, "color" => "purple");

       
    }
}

$stmt = "SELECT ro.fleetno,ro.estimateemailed,ro.invoiceemailed,ro.inspectionemailed,ro.tagnumber,rt.colorcode,ro.subtotal,k.vorder,ka.`status`,writer,datein,timein,datetimepromised,balance,vehlicense,vin,ro.cellphone,ro.customerphone,ro.customerwork,ro.customerfirst,ro.customerlast,ro.email,ro.totalro,ro.customerid,ro.shopid,ro.roid,ro.customer,ro.vehinfo,k.kanbanstatus,ka.color,ro.rotype,ro.gp,ro.totallbrhrs FROM kanbandata k"
    . " INNER JOIN repairorders ro ON k.shopid = ro.shopid AND k.roid = ro.roid"
    . " INNER JOIN kanbanstatuses ka ON k.shopid = ka.shopid AND k.kanbanstatus = ka.`status`"
    . " LEFT JOIN rotype rt on k.shopid = rt.shopid and ro.rotype = rt.rotype"
    . " WHERE k.shopid = ? and ro.rotype!='no approval' {$filter} order by k.kanbanstatus, k.vorder;";

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);

    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_assoc()) {
        
        if (!empty($techfilter) && $techfilter != 'all') {
            $techfilter_arr = explode(":", $techfilter);
            $tstmt = "select tech,laborhours from labor where tech != 'discount, discount' and deleted = 'no' and shopid = '" . $shopid . "' and roid = " . $rs["roid"];
            $tstmt .= " AND ( ";
            $count = 0;
            foreach ($techfilter_arr as $tech) {
                if ($count) {
                    $tstmt .= " OR ";
                }
                $tstmt .= " tech = '$tech' ";
                $count++;
            }
            $tstmt .= " )";
            //die ($tstmt);
            $tresult = $conn->query($tstmt);
            if ($tresult->num_rows <= 0) {
                continue;
            }
        }

        $techar = array();
        $techmatch=false;
        $tstmt = "select distinct tech from labor where shopid = '$shopid' and roid = " . $rs['roid'];
        if ($tquery = $conn->prepare($tstmt)) {
            //$tquery->bind_param("si",$shoid,$rs['roid']);
            if ($tquery->execute()) {
                $tr = $tquery->get_result();
                while ($trs = $tr->fetch_assoc()) {

                    if(htmlspecialchars_decode(strtoupper($loggedtech),ENT_QUOTES)==htmlspecialchars_decode(strtoupper($trs['tech']),ENT_QUOTES))
                    $techmatch = true;

                    array_push($techar, $trs['tech']);
                }
            } else {
                echo $conn->error;
            }
        }

        if($_COOKIE['mode'] == 'tech2' && $empid!='Admin' && $techsupervisor!='yes' && !$techmatch)continue;

        array_push($roidar, $rs['roid']);

        if ($showelapsed == "yes") {
            $startdatetime = strtotime($rs['datein'] . " " . $rs['timein']);
            $currdatetime = strtotime(localTimeStamp($shopid));
            $numsecs = $currdatetime - $startdatetime;
            $displaytime = seconds2human($numsecs);
        } else {
            $displaytime = "";
        }

        $moneyrecd = 0;
        if (strtoupper($showmoney) == "YES") {
            $mstmt = "select count(*) as c from accountpayments where shopid = ? and roid = ?";
            if ($mquery = $conn->prepare($mstmt)) {
                $mquery->bind_param("si", $shopid, $rs['roid']);
                $mquery->execute();
                $mquery->bind_result($moneyrecd);
                $mquery->fetch();
                $mquery->close();
            } else {
                echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }
        }

        if ($_COOKIE['mode'] == 'full' && strtoupper($showpics) == "YES") {
            $mstmt = "select count(*) as c from repairorderpics where shopid = ? and roid = ?";
            if ($mquery = $conn->prepare($mstmt)) {
                $mquery->bind_param("si", $shopid, $rs['roid']);
                $mquery->execute();
                $mquery->bind_result($countpics);
                $mquery->fetch();
                $mquery->close();
            } else {
                echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }
        } else {
            $countpics = 0;
        }

        // check for an inspection completed

        $compcountinsp = 0;
        if (strtolower($showinsp) == "yes") {
            $icstmt = "select count(*) c from roinspectionheader where shopid = '$shopid' and completiondate != '' and roid = " . $rs['roid'];
            if ($icquery = $conn->prepare($icstmt)) {
                $icquery->execute();
                $icquery->bind_result($compcountinsp);
                $icquery->fetch();
                $icquery->close();
            }
        }

        $tcroid = "no";
        if (strtoupper($showtechtime) == "YES") {
            $tcstmt = "select roid from labortimeclock where shopid = ? and roid = ? and enddatetime is null";
            if ($tcquery = $conn->prepare($tcstmt)) {
                $tcquery->bind_param("si", $shopid, $rs['roid']);
                $tcquery->execute();
                $tcquery->bind_result($tcroid);
                $tcquery->fetch();
                $tcquery->close();
            } else {
                echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }
        }
        $commstr = "";
        if ($_COOKIE['mode'] == 'full' && $showcommlog == "showcommlogyes") {
            $commstmt = "select `datetime`,replace(`comm`,'\"',\"'\") as comm from repairordercommhistory where `comm` != 'ADVISOR COMMENTS UPDATED' and `comm` != 'TECH STORY UPDATED' and shopid = '$shopid' and roid = " . $rs['roid'] . " order by datetime desc limit 5;";
            if ($commquery = $conn->prepare($commstmt)) {
                $commquery->execute();
                $commr = $commquery->get_result();
                while ($commrs = $commr->fetch_array()) {
                    $commstr .= date("m/d/Y",
                            strtotime($commrs['datetime'])) . " - " . $commrs['comm'] . "<hr style='padding:0px;margin:4px;border:1px black solid'>";
                }
            }
        }

        if (strlen($rs['datetimepromised']) > 0) {
            $datetimepromised = date("m/d/Y h:iA", strtotime($rs['datetimepromised']));
        } else {
            $datetimepromised = "";
        }

        $totalbox = number_format($rs["totalro"], 2);
        if ($showbalanceonwip == "yes") {
            $totalbox = "Balance:" . number_format($rs["balance"], 2);
        }

        $sa = $rs['writer'];
        if (strpos($sa, " ") > 0) {
            $sar = explode(" ", $sa);
            $firstinit = strtoupper(substr($sar[0], 0, 1));
            if (isset($sar[1])) {
                $lastinit = strtoupper(substr($sar[1], 0, 1));
            } else {
                $lastinit = "";
            }
            if (isset($sar[2])) {
                $finalinit = strtoupper(substr($sar[2], 0, 1));
            } else {
                $finalinit = "";
            }
            $writer = $firstinit . $lastinit . $finalinit;
        } else {
            $writer = substr($rs['writer'], 0, 4);
        }

        if (strlen($rs['tagnumber']) > 0) {
            $roid = $rs['roid'] . "-" . $rs['tagnumber'];
        } else {
            $roid = $rs['roid'];
        }
        $fontcolor = getContrastColor($rs['colorcode']);
        $rotype = strtoupper(str_replace(" ", "_", $rs['rotype']));

        $customer = '';
        if (!empty($rs['customerlast'])) {
            $customer .= $rs['customerlast'];
        }
        if (!empty($rs['customerfirst'])) {
            $customer .= (!empty($rs['customerlast']) ? ", " . $rs['customerfirst'] : $rs['customerfirst']);
        }

        $cardbody = "<div class='row'>"
            . "<div class='col'>" . ucwords(strtolower($customer)) . "</div>"
            . "<div class='col' align='right'><a class='text-primary' target='_blank' href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?roid=" . $rs['roid'] . "'>RO #" . $rs['roid'] . (isset($rs['tagnumber']) && $rs['tagnumber'] ? (" - " . $rs['tagnumber']) : "") . "</a></div></div>"
            . "<div class='row'><div class='col'><i title='RO TYPE' class='rotype' style='border:1px silver solid; font-style:normal;font-size:10pt;padding:1px 5px 1px 5px;color:$fontcolor;background-color:".$rs['colorcode']."'>".$rotype."</i></div>";

        if ($showcustomerinfo == "yes" && $i2 == "yes") {
            $cardbody .= "<div class='col' align='right'>";

            if($_COOKIE['mode'] == 'full')
            $cardbody .= "<i class='fas fa-sm fa-envelope me-2' data-mdb-toggle='tooltip' data-mdb-placement='top' title='SEND EMAIL' onclick='sendEmailMessage(" . $rs['roid'] . ")'></i> ";

            $cardbody .= "<i class='fas fa-sm fa-phone' data-mdb-toggle='tooltip' data-mdb-placement='top' title='SHOW ALL PHONES' onclick='showPhones(" . $rs['roid'] . ")'></i></div>";
        }

        $cardbody .= "</div><div class='row text-center'>"
            . "<div class='col-sm-12'>" . addslashes($rs['vehinfo']);
        if ($i9 == 'yes' && strlen($rs['vin']) > 5) {
            $cardbody .= " - " . $rs['vin'];
        }
        if (strlen($rs['fleetno']) > 1) {
            $cardbody .= " #" . $rs['fleetno'];
        }

        $cardbody .= "</div></div>";

        if ($i4 == "yes") {
            $cardbody .= "<div class='row'>"
                . "<div class='col' data-mdb-toggle='tooltip' data-mdb-placement='top' title='DATE IN' >IN:" . date("m/d/Y",
                    strtotime($rs['datein']))
                . "</div></div>"
                . "<div class='row'><div class='col' data-mdb-toggle='tooltip' data-mdb-placement='top' title='DATE PROMISED' >PD:" . $datetimepromised
                . "</div></div>"
                . "<div class='row'><div class='col' data-mdb-toggle='tooltip' data-mdb-placement='top' title='ELAPSED TIME' >ET:" . $displaytime
                . "</div></div>"
                . "";
        }
        if ($i3 == "yes") {
            $cardbody .= "<div class='row'>"
                . "<div id='activity" . $rs['roid'] . "' class='col activity'>Activity: ";
            if (strtoupper($showmoney) == "YES" && $moneyrecd > 0) {
                $cardbody .= "<i data-mdb-toggle='tooltip' title='MONEY RECEIVED' onclick='getROPayments(" . $rs['roid'] . ")'  class='fas fa-sm fa-lg fa-hand-holding-dollar me-2'></i>";
            }
            if ($_COOKIE['mode'] == 'full' && strtoupper($showpics) == "YES" && $countpics > 0) {
                $cardbody .= "<i data-mdb-html='true' data-mdb-toggle='tooltip' title='PICTURES UPLOADED TO THIS RO' class='fas fa-sm fa-camera me-2'></i>";
            }
            if (strtoupper($showtechtime) == "YES" && is_numeric($tcroid)) {
                $cardbody .= "<i data-mdb-html='true' data-mdb-toggle='tooltip' title='TECH CLOCKED IN' class='fas fa-sm fa-clock me-2'></i>";
            }
            if (strtoupper($showinsp) == "YES" && $compcountinsp > 0) {
                $cardbody .= "<i onclick='openInspections(" . $rs['roid'] . ")'  data-mdb-html='true' data-mdb-toggle='tooltip' title='INSPECTION COMPLETE - CLICK FOR DETAILS' class='fas fa-sm fa-clipboard'></i>";
            }
            if (strtoupper($showinspemail) == "YES" && validateDate($rs["inspectionemailed"]) == 1) {
                $cardbody .= "<i data-mdb-html='true' data-mdb-toggle='tooltip' title='INSPECTION EMAILED' class='fas fa-envelope fa-sm'></i>";
            }
            if (strtoupper($showinvemail) == "YES" && validateDate($rs["invoiceemailed"]) == 1) {
                $cardbody .= "<i data-mdb-html='true' data-mdb-toggle='tooltip' title='INVOICE EMAILED' class='fas fa-envelope fa-sm'></i>";
            }
            if (strtoupper($showestemail) == "YES" && validateDate($rs["estimateemailed"]) == 1) {
                $cardbody .= "<i data-mdb-html='true' data-mdb-toggle='tooltip' title='ESTIMATE EMAILED' class='fas fa-envelope-o fa-sm'></i>";
            }

            if ($_COOKIE['mode'] == 'full' && strlen($commstr) > 0) {
                $commstr = substr($commstr, 0, strlen($commstr) - 4);
                $commstr = htmlspecialchars($commstr);
                $commstr = str_replace("\r", " ", str_replace("\n", " ", str_replace("\r\n", " ", $commstr)));
                $cardbody .= " <i onclick='getComms(" . $rs['roid'] . ")' data-mdb-html='true' data-mdb-toggle='tooltip' title='CLICK TO VIEW COMMUNICATION LOG' class='fas fa-sm fa-comment'></i>";
            }

            $cardbody .= "</div>"
                . "</div>";
        }

        if ($i5 == "yes") {
            $cardbody .= "<div class='row'>"
                . "<div class='col-sm-12'>Writer: " . strtoupper($rs['writer'])
                . "</div>"
                . "</div>";
        }

        if ($i6 == "yes" && $techsupervisor == 'yes') {
            $cardbody .= "<div class='row'>"
                . "<div id='techs' class='col-sm-12'>Tech(s): ";
            foreach ($techar as $techname) {
                $cardbody .= $techname . " / ";
            }

            $cardbody .= "</div>"
                . "</div>";
        }

        if ($i10 == "yes" || $i7 == "yes") {
            $cardbody .= "<div class='row'>";
        }

        if ($i10 == "yes") {
            $cardbody .= "<div class='col pe-0'>Labor Hrs: " . $rs['totallbrhrs'] . "</div>";
        }

        if ($i7 == "yes" || $i8 == "yes") {
            $cardbody .= "<div class='col' align='right'>";
        }

        if ($_COOKIE['mode'] == 'full' && $i7 == "yes") {
            $cardbody .= "<i data-mdb-toggle='tooltip' data-mdb-placement='top' title='PRINT RO' class='fas fa-sm fa-print me-1' onclick='printRO(" . $rs['roid'] . ")'></i>"
                . "<i data-mdb-toggle='tooltip' data-mdb-placement='top' title='OPEN RO' class='fas fa-sm fa-door-open me-1' onclick='openRO(" . $rs['roid'] . ")'></i>"
                . "<i data-mdb-toggle='tooltip' data-mdb-placement='top' title='SEND UPDATE' onclick='openSendUpdate(" . $rs['roid'] . ")' class='fas fa-sm fa-paper-plane me-1'></i>"
                . "<i data-mdb-toggle='tooltip' data-mdb-placement='top' title='CLOSE RO' class='fas fa-sm fa-door-closed me-1' onclick='closeRO(" . $rs['roid'] . ")'></i>";
        }

        if ($_COOKIE['mode'] == 'full' && $i8 == "yes") {
            if (strlen($rs['gp']) > 0 && $rs['gp'] >= 0) {
                if (($rs['gp'] * 100) <= 50) {
                    $btnclass = "btn-danger-gp";
                } elseif (($rs['gp'] * 100) > 50 && ($rs['gp'] * 100) <= 60) {
                    $btnclass = "btn-caution-gp";
                } elseif (($rs['gp'] * 100) > 60) {
                    $btnclass = "btn-success-gp";
                }
                $dgp = round($rs['gp'] * 100, 0);
            } else {
                $dgp = 0;
                $btnclass = "btn-danger-gp";
            }

            $cardbody .= "<span data-mdb-toggle='tooltip' data-mdb-placement='top' title='GP' class='badge $btnclass' onclick='showGP(" . $rs['roid'] . "," . $rs['subtotal'] . ")'>" . $dgp . "</span>";
        }

        if ($i7 == "yes" || $i8 == "yes")
        $cardbody .= "</div>";

        if ($i10 == "yes" || $i7 == "yes")
        $cardbody .= "</div>";

        $data[] = array("id" => $rs['roid'], "status" => $rs['kanbanstatus'], "text" => $cardbody, "color" => $rs['color'], '$css' => str_replace(" ","_",$rs['status']).'css');
			
    }
}


$response = array(
    "cols" => $cols,
    "data" => $data
    );

header('Content-Type: application/json');

echo json_encode($response);
