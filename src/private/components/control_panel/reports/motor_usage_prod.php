<?php

set_time_limit(-1);
ob_start();
require CONNCONTROL;
//require_once("functions.php");
if (isset($_COOKIE['loggedin'])) {
    if ($_COOKIE["loggedin"] != 'yes') {
        redirect_to("login.php");
    }
} else {
    redirect_to("login.php");
}

// Global Variables
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? filter_var($_GET['sd'], FILTER_SANITIZE_STRING) : '';
$ed = isset($_GET['ed']) ? filter_var($_GET['ed'], FILTER_SANITIZE_STRING) : '';

$sdate = date('Y-m-d', strtotime($sd));
$edate = date('Y-m-d', strtotime($ed));
$path = $_SERVER['HTTP_HOST'];

// Page Variables
$title = 'Motor Usage Report With ProDemand & Shop Category';  // Report Title Goes Here
$subtitle
    = "This Report shows Motor Usage based on tracked clicks";  // Report SubTitle Goes Here - Comment out if not needed
$template = COMPONENTS_PRIVATE
    . "/reports/templates/excelexport_autosize_cp.php"; //Only change if a custom PHPExcel is created in the template folder

include(COMPONENTS_PRIVATE_PATH . '/reports/includes/header_reports.php');

function formatPhoneCP($p)
{
    if (strlen($p) == 10) {
        $ac = substr($p, 0, 3);
        $pf = substr($p, 3, 3);
        $lf = substr($p, 6, 4);
        return "$ac-$pf-$lf";
    } elseif (strlen($p) > 10) {
        $ac = substr($p, 0, 3);
        $pf = substr($p, 3, 3);
        $lf = substr($p, 6, 4);
        $ex = substr($p, 10, strlen($p) - 10);
        return "$ac-$pf-$lf x $ex";
    } else {
        return $p;
    }
}

?>
<title><?= $title; ?></title>

<!DOCTYPE html>
<html>
<body>
<?php
include(COMPONENTS_PRIVATE_PATH . '/reports/includes/report_buttons.php');

$tablefields = array(
    'Shopid', 'Company Name', 'Package', 'Status','Prodemand','Category', 'Date Started', 'Last Payment', 'Churn Date',  'Month', 'Total Clicks','Contact',
    'Email', 'Phone');
$alldata = array();

$ttlclks = 0;

?>

<table class="report_table" style="font-size: 12px">
    <tr class="table_header">
        <td>Shopid</td>
        <td>Company Name</td>
        <td>Package</td>
        <td>Status</td>
        <td>Prodemand</td>
        <td>Category</td>
        <td>Date Started</td>
        <td>Last Payment</td>
        <td>Churn Date</td>
        <td>Month</td>
        <td class="text-right">Total Clicks</td>
        <td>&nbsp;&nbsp;&nbsp;Contact</td>
        <td style="width: 400px; word-break: break-word">Email</td>
        <td>Phone</td>
    </tr>

    <?php
    $stmt = "SELECT dates.shopid,  _company_view.companyname,
       _company_view.newpackagetype AS Package,
       _company_view.status AS Status,
       DATE_FORMAT(_company_view.datestarted, '%m/%d/%y') AS date_started,
       DATE_FORMAT(_company_view.lastpaymentdate, '%m/%d/%y') AS 'last_payment',
       UCASE(cat.category) as category,
       IF(a.apikey is NULL, 'NO','YES') as prodemand,
       CASE WHEN _company_view.churndate = '0000-00-00' THEN '-'
            WHEN _company_view.churndate >= '2022-10-01' THEN DATE_FORMAT(_company_view.churndate, '%m/%d/%y')
            ELSE '-'
       END AS churn_date,
    
       DATE_FORMAT(motortrack.dt, '%M %Y') AS month,
       COALESCE(SUM(motortrack.counter), 0) AS total_clicks,
       _company_view.contact AS Contact,
       _company_view.companyemail AS Email,
       _company_view.companyphone AS Phone
        FROM (
          SELECT shopid, '$sdate' AS start_date
          FROM _company_view
        ) AS dates
        LEFT JOIN motortrack
          ON dates.shopid = motortrack.shopid 
          AND dt >= '$sdate' AND dt <= '$edate'
        JOIN _company_view
          ON dates.shopid = _company_view.shopid
        LEFT JOIN companycategories cat on _company_view.shopid = cat.shopid
        LEFT JOIN apilogin a on _company_view.shopid = a.shopid AND a.companyname = 'prodemand'
        WHERE (_company_view.status NOT IN ('suspended') AND (_company_view.lastpaymentdate >= '2022-10-01'))
          AND _company_view.newpackagetype != 'silver'
          AND (_company_view.churndate = '0000-00-00' OR _company_view.churndate >= '2022-10-01')
        GROUP BY dates.shopid, MONTH(motortrack.dt) 
        order by dates.shopid asc, MONTH(motortrack.dt) asc";

    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $results = $query->get_result();
        $query->close();
    }

    if ($results->num_rows > 0) {
        while ($rs = $results->fetch_assoc()) {

            ?>
            <tr class="table_data">
                <td><?= $rs['shopid'] ?></td>
                <td><?= $rs['companyname'] ?></td>
                <td><?= $rs['Package'] ?></td>
                <td><?= $rs['Status'] ?></td>
                <td><?= $rs['prodemand'] ?></td>
                <td><?= $rs['category'] ?></td>
                <td><?= $rs['date_started'] ?></td>
                <td><?= $rs['last_payment'] ?></td>
                <td><?= $rs['churn_date'] ?></td>
                <td><?= $rs['month'] ?></td>
                <td class="text-right"><?= $rs['total_clicks'] ?></td>
                <td>&nbsp;&nbsp;&nbsp;<?= $rs['Contact'] ?></td>
                <td style="width: 400px; word-break: break-word"><?= $rs['Email'] ?></td>
                <td data-ph="<?= $rs['Phone'] ?>"><?= formatPhoneCP(
                        $rs['Phone']
                    ) ?></td>
            </tr>
            <?php

            $alldata[] = array(
                $rs['shopid'],
                $rs['companyname'],
                $rs['Package'],
                $rs['Status'],
                $rs['prodemand'],
                $rs['category'],
                $rs['date_started'],
                $rs['last_payment'],
                $rs['churn_date'],
                $rs['month'],
                $rs['total_clicks'],
                $rs['Contact'],
                $rs['Email'],
                formatPhoneCP($rs['Phone'])
            );

            $ttlclks += $rs['total_clicks'];
        }

        ?>
        <tr class="table_total">
            <td colspan="10">TOTALS</td>
            <td class="text-right"><?= $ttlclks ?></td>
            <td colspan="3"></td>
        </tr>
        <?php
        $alldata[] = array(
            'TOTALS', '', '', '','','', '', '', '', '', $ttlclks, '', '', ''
        );
    }

    ?>
</table>


<?php
include(COMPONENTS_PRIVATE_PATH . '/reports/includes/report_form.php');
include(COMPONENTS_PRIVATE_PATH . '/reports/includes/footer_reports.php');
?>
<script type="text/javascript">
    $(document).ready(function () {
        $('#donebtn').attr("onclick", "location.href='<?= COMPONENTS_PRIVATE ?>/control_panel/reports.php'");
    })
</script>
</body>
</html>
<?php
if (isset($conn)) {
    mysqli_close($conn);
}
?>
