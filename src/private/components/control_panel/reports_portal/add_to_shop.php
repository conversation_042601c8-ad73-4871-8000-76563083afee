<?php
require CONNCONTROL;
$report_shop = isset($_REQUEST['report_shop'])? filter_var($_REQUEST['report_shop'], FILTER_SANITIZE_STRING): "";
$report_dir_id = isset($_REQUEST['report_id'])? filter_var($_REQUEST['report_id'], FILTER_SANITIZE_STRING): "";
$report_name_new = isset($_REQUEST['report_name'])? filter_var($_REQUEST['report_name'], FILTER_SANITIZE_STRING):"";

if (!empty($report_shop) && !empty($report_dir_id)) {
    $stmt = "SELECT name, description, location FROM report_directory WHERE id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("i", $report_dir_id);
        $query->execute();
        $query->bind_result($report_name, $report_desc, $report_location);
        $query->fetch();
        $query->close();
    }

    if (!empty($report_name_new)) {
        $report_name = $report_name_new;
    }

//check if same report was added to the same shop
    $stmt = "SELECT * FROM customreports WHERE shared_id = ? AND shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("is", $report_dir_id, $report_shop);
        $query->execute();
        $results = $query->get_result();
        $query->close();
    }

    if ($results && $results->num_rows > 0) {
        //shop already has same report
        echo "Shop already has this shared report added";
        return;
    }

    $stmt = "INSERT INTO customreports (shopid, name, `desc`, location, displayat, shared_id) VALUES (?, ?, ?, ?, 'reports',?)";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("ssssi", $report_shop, $report_name, $report_desc, $report_location, $report_dir_id);
        if ($query->execute()) {
            echo "success";
            $conn->commit();
        }
        $query->close();
    }
} else {
    echo "Something went wrong, Please try again";
}