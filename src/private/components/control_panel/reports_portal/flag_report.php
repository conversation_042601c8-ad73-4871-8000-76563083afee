<?php
ini_set("display_errors", "1");
error_reporting(E_ALL);
require CONNCONTROL;
$report_data = isset($_REQUEST['report_params'])? $_REQUEST['report_params'] : "";
function rcopy($src, $dst) {
    if (file_exists($dst)) rrmdir($dst);
    if (is_dir($src)) {
        mkdir($dst);
        $files = scandir($src);
        foreach ($files as $file)
            if ($file != "." && $file != "..") rcopy("$src/$file", "$dst/$file");
    }
    else if (file_exists($src)) copy($src, $dst);
}

parse_str($report_data, $data);


$name = $data['name'];
$description = $data['description'];
$category = ucwords(strtolower($data['category']));
$columns = $data['columns'];
$report_id = $data['id'];


//check if a shared report with same name exists
//unchcked for now
/*
$shared_stmt = "SELECT * FROM report_directory WHERE name = ?";
if ($query = $conn->prepare($shared_stmt)){
    $query->bind_param("s", $name);
    $query->execute();
    $results = $query->get_result();
    $query->close();
}

if ($results && $results->num_rows > 0){
    //non unique shared report name, do not continue
    echo "A Shared Report with same name already exists, Please Enter a new Name";
    return;
}
*/

$stmt = "SELECT shopid, location FROM customreports WHERE id = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("i", $report_id);
    $query->execute();
    $query->bind_result($cr_shopid, $cr_location);
    $query->fetch();
    $query->close();
}

$reports_DIR = COMPONENTS_PRIVATE_PATH.DS."customreports_classic".DS."shared_reports";

$pathinfo = pathinfo($cr_location);

$dirname = $pathinfo['dirname'];
$basename = $pathinfo['basename'];

$stmt = "INSERT INTO report_directory (report_id, name, description, category, columns) VALUE (?,?,?,?,?)";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("issss", $report_id, $name, $description, $category, $columns);
    if($query->execute()){
        $dir_id = $conn->insert_id;
     //   echo "success";
        $conn->commit();
    } else {
        echo "Error : ".$conn->error;
        return;
    }
    $query->close();
}


$src_path = COMPONENTS_PRIVATE_PATH.DS."customreports_classic".DS.str_replace("customreports", "", $dirname);
$dest_path = $reports_DIR.DS.$dir_id;
$dest_url = "customreports/shared_reports/".$dir_id."/".$basename;

/*
echo $src_path;
echo "<br />";
echo $dest_path;
echo "<br />";
echo $dest_url;
echo "<br />";
*/
if (file_exists($dest_path)){
    //prompt that the folder already exists
    echo "A Shared Report with same name already exists in the directory, Please input a new Name";
    return;
} else {
    @mkdir($reports_DIR);
}

$stmt = "UPDATE report_directory SET location = ? WHERE id = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("si", $dest_url,$dir_id);
    if($query->execute()){
        $dir_id = $conn->insert_id;
        echo "success";
        $conn->commit();
    } else {
        echo "Error : ". $conn->error;
        return;
    }
    $query->close();
}
rcopy($src_path, $dest_path);