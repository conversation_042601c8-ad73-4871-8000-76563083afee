<?php
require CONN;

if (isset($_GET['i'])){$fieldname = filter_var($_GET['i'],FILTER_SANITIZE_STRING);}
if (isset($_GET['v'])){$fieldval = filter_var($_GET['v'],FILTER_SANITIZE_STRING);}
$shopid = filter_var($_GET['shopid'],FILTER_SANITIZE_STRING);
if (isset($_GET['oshopid'])){
	$oshopid = filter_var($_GET['oshopid'],FILTER_SANITIZE_STRING);
}else{
	$oshopid = $shopid;
}
$cid = filter_var($_GET['cid'],FILTER_SANITIZE_STRING);
//echo "Shop is : " . $shopid ;
//echo "Customer Id is : " . $cid ;

//echo $fieldname.":".$fieldval."\r\n";

if (!isset($_GET['savetype'])){

	if ($fieldname == 'year' || $fieldname == 'make' || $fieldname == 'model' || $fieldname == 'currentmileage' || $fieldname == 'miles' || $fieldname == 'licnumber' || $fieldname == 'vin' || $fieldname == 'engine' || $fieldname == 'drivetype' || $fieldname == 'transmission' || $fieldname == 'cyl' || $fieldname == 'fleetno' || $fieldname == 'licstate' || $fieldname == 'color' || $fieldname == 'custom1' || $fieldname == 'custom2' || $fieldname == 'custom3' || $fieldname == 'custom4' || $fieldname == 'custom5' || $fieldname == 'custom6' || $fieldname == 'custom7' || $fieldname == 'custom8'){
		$vid = $_GET['vid'];
		//echo $fieldname.":".$fieldval."\r\n";
		$fieldval = str_replace('"','',$fieldval);
		if ($shopid != $oshopid){
			$stmt = "update vehicles set $fieldname = ? where shopid = '$oshopid' and vehid = $vid";
		}else{
			$stmt = "update vehicles set $fieldname = ? where shopid = '$shopid' and vehid = $vid";
		}
		//echo $stmt."\r\n";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("s",$fieldval);
		    $query->execute();
		    $conn->commit();
		    echo "success";

		    $query->close();

		}else{
			echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

	}else{
		if ($shopid != $oshopid){
			$stmt = "update customer set $fieldname = '".str_replace("'","''",$fieldval)."' where shopid = '$oshopid' and customerid = $cid";
		}else{
			$stmt = "update customer set $fieldname = '".str_replace("'","''",$fieldval)."' where shopid = '$shopid' and customerid = $cid";
		}

		echo $stmt."\r\n";
		if ($query = $conn->prepare($stmt)){
			//$query->bind_param("s",$fieldval);
		    $query->execute();
		    $conn->commit();
		    echo "success";

		   if ($fieldname == 'taxexempt') {
			 	recordAudit("TaxExemptChange", "Tax Exempt status changed on Customer#$cid");
		   }

		    $query->close();

		}else{
			echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

	}

}

if (isset($_GET['roid'])){

	// update the ro too
	$roid = $_GET['roid'];

	if (!isset($_GET['savetype'])){

		switch ($fieldname){
			case "lastname":
				$stmt = "update repairorders set customerlast = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "firstname":
				$stmt = "update repairorders set customerfirst = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "address":
				$stmt = "update repairorders set customeraddress = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "zip":
				$stmt = "update repairorders set customerzip = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "city":
				$stmt = "update repairorders set customercity = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "state":
				$stmt = "update repairorders set customerstate = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "email":
				$stmt = "update repairorders set email = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "homephone":
				$stmt = "update repairorders set customerphone = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "workphone":
				$stmt = "update repairorders set customerwork = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "cellphone":
				$stmt = "update repairorders set cellphone = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "contact":
				$stmt = "update repairorders set contact = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "fax":
				$stmt = "update repairorders set fax = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "spousename":
				$stmt = "update repairorders set spousename = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "spousework":
				$stmt = "update repairorders set spousework = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "spousecell":
				$stmt = "update repairorders set spousecell = ? where shopid = '$shopid' and roid = $roid";
				break;
			case "taxexempt":
				$stmt = "";
				break;
		}
		//echo $stmt;
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("s",$fieldval);
		    if ($query->execute()){
			    $conn->commit();
			    $r = "success";
		    }else{
		    	$r = "error";
		    }
		    $query->close();

		}else{
			echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

	    // now fire the update query to update csz and customer and lastfirst
	    if ($r == "success"){
		    $nstmt = "update repairorders set customer = concat(customerfirst,' ',customerlast), lastfirst = concat(customerlast,', ',customerfirst), customercsz = concat(customercity,', ',customerstate,'. ',customerzip) where shopid = '$shopid' and roid = $roid";
		    if ($nquery = $conn->prepare($nstmt)){
		    	if ($nquery->execute()){
		    		$conn->commit();
		    	}
		    }
		}
	}else{

		// this is to update all

		$ln = filter_var($_GET['lastname'],FILTER_SANITIZE_STRING);  // last
		$fn = filter_var($_GET['firstname'],FILTER_SANITIZE_STRING);  // first
		$a = filter_var($_GET['address'],FILTER_SANITIZE_STRING);  // addre
		$z = filter_var($_GET['zip'],FILTER_SANITIZE_STRING);  // zip
		$c = filter_var($_GET['city'],FILTER_SANITIZE_STRING);  // city
		$s = filter_var($_GET['state'],FILTER_SANITIZE_STRING);
		$e = filter_var($_GET['email'],FILTER_SANITIZE_STRING);  // email
		$con = filter_var($_GET['contact'],FILTER_SANITIZE_STRING); // contact
		$hp = str_replace("-","",filter_var($_GET['homephone'],FILTER_SANITIZE_STRING));  // home phone
		$wp = str_replace("-","",filter_var($_GET['workphone'],FILTER_SANITIZE_STRING));
		$ext = filter_var($_GET['ext'],FILTER_SANITIZE_STRING);
		$cp = str_replace("-","",filter_var($_GET['cellphone'],FILTER_SANITIZE_STRING));
		$fax = filter_var($_GET['fax'],FILTER_SANITIZE_STRING);
		$sn = filter_var($_GET['spousename'],FILTER_SANITIZE_STRING);  // spousename
		$sw = str_replace("-","",filter_var($_GET['spousework'],FILTER_SANITIZE_STRING));  // spousework
		$sc = str_replace("-","",filter_var($_GET['spousecell'],FILTER_SANITIZE_STRING));  // spousecell
		$ct = filter_var($_GET['customertype'],FILTER_SANITIZE_STRING);   // customer type
		$cu1 = filter_var($_GET['customuserfield1'],FILTER_SANITIZE_STRING);
		$cu2 = filter_var($_GET['customuserfield2'],FILTER_SANITIZE_STRING);
		$cu3 = filter_var($_GET['customuserfield'],FILTER_SANITIZE_STRING);
		$sht = filter_var($_GET['shippingto'],FILTER_SANITIZE_STRING);  // ship to
		$sha = filter_var($_GET['shippingaddress'],FILTER_SANITIZE_STRING);
		$shc = filter_var($_GET['shippingcity'],FILTER_SANITIZE_STRING);
		$shs = filter_var($_GET['shippingstate'],FILTER_SANITIZE_STRING);
		$shz = filter_var($_GET['shippingzip'],FILTER_SANITIZE_STRING);
		$shp = str_replace("-","",filter_var($_GET['shippingphone'],FILTER_SANITIZE_STRING));
		$cl = filter_var($_GET['creditlimit'],FILTER_SANITIZE_STRING);  // credit limit
		$bt = filter_var($_GET['billto'],FILTER_SANITIZE_STRING);
		$ba = filter_var($_GET['billtoaddress'],FILTER_SANITIZE_STRING);
		$bc = filter_var($_GET['billtocity'],FILTER_SANITIZE_STRING);
		$bs = filter_var($_GET['billtostate'],FILTER_SANITIZE_STRING);
		$bz = filter_var($_GET['billtozip'],FILTER_SANITIZE_STRING);
		$accounttype = (isset($_GET['accounttype'])?strtoupper(filter_var(trim($_GET['accounttype']),FILTER_SANITIZE_STRING)):'');
		$bp = str_replace("-","",filter_var($_GET['billtophone'],FILTER_SANITIZE_STRING));
		if (isset($_GET['comments'])){
			$comments = filter_var($_GET['comments'],FILTER_SANITIZE_STRING);
		}else{
			$comments = '';
		}

		$stmt = "update customer set lastname = ?,firstname = ?, address = ?, city = ?, state = ?, zip = ?, homephone = ?, workphone = ?, extension = ?, cellphone = ?, fax = ?"
		. ", email = ?, UserDefined1 = ?, UserDefined2 = ?, UserDefined3 = ?, contact = ?, spousename = ?, spousecell = ?, spousework = ?, shippingto = ?, shippingaddress = ?, shippingcity = ?, shippingstate = ?, shippingzip = ?, shippingphone = ?, billto = ?"
		. ", billtoaddress = ?, billtocity = ?, billtostate = ?, billtozip = ?, billtophone = ?, creditlimit = ?, accounttype = ?, customertype = ?";
		if ($comments != ''){
			$stmt .= ", comments = ?";
		}
		$stmt .= " where shopid = '$oshopid' and customerid = $cid";

		//$stmt = "update customer set lastname = '$ln',firstname = '$fn', address = '$a', city = '$c', state = '$s', zip = '$z', homephone = '$hp', workphone = '$wp', cellphone = '$cp', fax = '$fax'"
		//. ", email = '$e', UserDefined1 = '$cu1', UserDefined2 = '$cu2', UserDefined3 = '$cu3', contact = '$con', spousename = '$sn', spousecell = '$sc', spousework = '$sw', shippingto = '$sht', shippingaddress = '$sha', shippingcity = '$shc', shippingstate = '$shs', shippingzip = '$shz', shippingphone = '$shp', billto = '$bt'"
		//. ", billaddress = '$ba', billcity = '$bc', billstate = '$bs', billzip = '$bz', billphone = '$bp', creditlimit = '$cl', customertype = '$ct'"
		//. " where shopid = '$shopid' and customerid = $cid";
		//echo $stmt;

		if ($query = $conn->prepare($stmt)){

			if ($comments != ''){
				$query->bind_param("sssssssssssssssssssssssssssssssssss",$ln,$fn,$a,$c,$s,$z,$hp,$wp,$ext,$cp,$fax,$e,$cu1,$cu2,$cu3,$con,$sn,$sc,$sw,$sht,$sha,$shc,$shs,$shz,$shp,$bt,$ba,$bc,$bs,$bz,$bp,$cl,$accounttype,$ct,$comments);
			}else{
				$query->bind_param("ssssssssssssssssssssssssssssssssss",$ln,$fn,$a,$c,$s,$z,$hp,$wp,$ext,$cp,$fax,$e,$cu1,$cu2,$cu3,$con,$sn,$sc,$sw,$sht,$sha,$shc,$shs,$shz,$shp,$bt,$ba,$bc,$bs,$bz,$bp,$cl,$accounttype,$ct);
			}
			$query->execute();
			$conn->commit();
			$query->close();
			//echo "success";
		}else{
			echo $conn->error;
		}

		$stmt = "update repairorders set customer = ?, customeraddress = ?, customercsz = ?, customercity = ?, customerstate = ?, customerzip = ?, email = ?, customerphone = ?, customerwork = ?, cellphone = ?, lastfirst = ?, customerfirst = ?, customerlast = ?,contact = ?,spousename = ?,spousework = ?,spousecell = ?   where shopid = '$shopid' and roid = $roid";
		if ($query = $conn->prepare($stmt)){

			$cn = $fn.' '.$ln;
			$lf = $ln.' ' .$fn;
			$csz = $c.", ".$s.". ".$z;
			$query->bind_param("sssssssssssssssss",$cn,$a,$csz,$c,$s,$z,$e,$hp,$wp,$cp,$lf,$fn,$ln,$con,$sn,$sw,$sc);
			$query->execute();
			$conn->commit();
			$query->close();

			//notification
			$stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='20'";
			$query = $conn->prepare($stmt);
			$query->execute();
			$query->store_result();
			$numrows = $query->num_rows();
			if ($numrows > 0)
			{
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
	        $emailcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $emailcontent));
	        $popupcontent=str_replace("*|RO|*",$roid,str_replace("*|SN|*", $sn, $popupcontent));
			$textcontent=str_replace("*|RO|*",$roid,$textcontent);
	        $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'20',?,?,?)";
            if ($query = $conn->prepare($stmt))
            {
	        $query->bind_param('isss',$shopid,$popupcontent,$textcontent,$emailcontent);
	        $query->execute();
	        $conn->commit();
	        $query->close();
	        }

	        }

			echo "success";
		}else{
			echo $conn->error;
		}

	}

}


mysqli_close($conn);




?>
