<?php
require_once 'BaseController.php';

class EmployeeController extends BaseController {
    public function update($request) {
        $employee_id = $request['EmployeeID'];
        unset($request['EmployeeID']);

        $request = $this->validateAndSanitize($request);

        if (empty($request)){
            echo json_encode(['status' => 'info', 'message' => 'No changes were made.']);
            return;
        }
        
        $columns = array_keys($request);
        $set_clause = implode(" = ?, ", $columns) . " = ?";

        $sql = "UPDATE employees SET $set_clause WHERE shopid = ? AND EmployeeID = ?";

        $column_types = $this->getColumnTypes($columns);
        $column_types .= "s";
        $column_values = array_values($request);
        $column_values[] = $this->shopid;
        $column_values[] = $employee_id;

        $this->updateTable($sql, $column_types, $column_values);      
        
        echo json_encode($this->response);
    }

    public function store($request) {
        $empid = strtoupper($this->shopid . "-" . generateRandomStr());
        
        $request = $this->validateAndSanitize($request, 'store');

        if (empty($request)){
            echo json_encode(['status' => 'info', 'message' => 'No changes were made.']);
            return;
        }
        
        $request['EmployeeID'] = $empid;
        $request['DefaultWriter'] = 'NO';
        $request['passwordenc'] = password_hash($request['password'], PASSWORD_DEFAULT);

        $columns = array_keys($request);
        $columns[] = 'shopid';
        $placeholders = implode(", ", array_fill(0, count($columns), "?"));
    
        $columns_string = implode(", ", $columns);
        $sql = "INSERT INTO employees ($columns_string) VALUES ($placeholders)";
    
        $column_types = substr($this->getColumnTypes($columns), 0, -1);
        $column_values = array_values($request);
        $column_values[] = $this->shopid; 
   
        $this->updateTable($sql, $column_types, $column_values, 'store');
        
        $this->response['employee_id'] = $empid;
        $this->response['mode'] = $request['mode'];
        echo json_encode($this->response);
    }
    

    // public function destroy($id) {
    //     $request = $this->validateAndSanitize($id);

    //     $sql = "DELETE FROM employees WHERE id = ? AND shopid = ?";

    //     $this->updateTable($sql, 'is', [$id, $this->shopid], 'destroy');

    //     echo json_encode($this->response);
    // }

    protected function columnTypes($column) {
        $decimal_columns = [];
        $integer_columns = [];
        $date_columns = ['DateHired'];

        if (in_array($column, $decimal_columns)) {
            return 'd';
        } elseif (in_array($column, $integer_columns)) {
            return 'i';
        } elseif (in_array($column, $date_columns)) {
            return 's';
        } else {
            return 's';
        }
    }

    protected function isValidColumn($column) {
        $valid_columns = [
            "EmployeeID",
            "employeefirst",
            "employeelast",
            "employeephone",
            "position",
            "password",
            "mechanicnumber",
            "showtechlist",
            "Active",
            "hourlyrate",
            "paytype",
            "pin",
            "DateHired",
            "color",
            "logintosbp",
            "EditInventory",
            "CompanyAccess",
            "ReOpenRO",
            "EmployeeAccess",
            "changerodate",
            "ReportAccess",
            "ChangePartMatrix",
            "CreateRO",
            "ChangePartCodes",
            "CreateCT",
            "ChangeJobDescription",
            "EditSupplier",
            "ChangeSources",
            "InventoryLookup",
            "ChangeRepairOrderTypes",
            "candelete",
            "sendupdates",
            "changeshopnotice",
            "deletepaymentsreceived",
            "accounting",
            "deletecustomer",
            "downloaddata",
            "editnotifications",
            "showgpinro",
            "edittechpaidlog",
            "editcommentsinro",
            "DashboardAccess",
            "IntegrationAccess",
            "changerostatus",
            "partsordering",
            "mergecustomers",
            "JobDesc",
            "mode"
        ];

        return in_array(strtolower($column), array_map('strtolower', $valid_columns));
    }
}
?>
