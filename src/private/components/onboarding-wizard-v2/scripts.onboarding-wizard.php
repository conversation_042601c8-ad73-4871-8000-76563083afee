<script>
    var testing = true;

    // Turn Off Action While SideMenu Hidden
    let customerOnboarding = <?= json_encode($customer_onboarding); ?>;
    if ( !customerOnboarding ){
        sideMenuActions()
    }
    if ( customerOnboarding ) {
        if( !customerOnboarding['general_info'] && 
        !customerOnboarding['settings'] && 
        !customerOnboarding['employees'] && 
        !customerOnboarding['suppliers'] && 
        !customerOnboarding['customize']) sideMenuActions()
    }

    function sideMenuActions(turnOff = true){
        let menutoggler = document.querySelector('.menutoggler')
        let logo = document.querySelector('#logo')
        let logoRedirection = logo.querySelector('a')
        let mainContainer = document.querySelector('#main-container')
        let sidenavMenu = document.querySelector('#main-sidenav')

        if ( turnOff ){
            sidenavMenu.style.display = 'none'
            
            menutoggler.style.visibility = "hidden"

            logoRedirection.href='#'
            
            mainContainer.classList.remove('container-sidebar');
        }else{
            sidenavMenu.style.display = 'block'

            menutoggler.style.visibility = "visible"

            logoRedirection.href='/v2/wip/wip.php'

            mainContainer.classList.add('container-sidebar');
        }
    }

    // UTILS
        // Function to get URL parameters
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Function to add or update a URL parameter
        function addParameterToURL(param) {
            let [key, value] = param.split('=');
            let _url = new URL(window.location);

            if (_url.searchParams.has(key)) {
                _url.searchParams.set(key, value);
            } else {
                _url.searchParams.append(key, value);
            }

            history.pushState(null, '', _url.toString());
        }

        // ClassList Function
        function removeClassFromElements(which_class, element_class){
            document.querySelectorAll("." + element_class).forEach(function(element) {
                element.classList.remove(which_class);
            }); 
        }

        // Form Reinitialize For Label
        function reinitializeMDBForm() {
            var formOutlines = document.querySelectorAll('.form-outline');

            formOutlines.forEach(function(formOutline) {
                new mdb.Input(formOutline).update();
            });
        }

        // Make Datatable
        function dataTable(elementId) {
            $('#' + elementId).DataTable({});
        }

    // Form Submit
    function submitStep(step, controller_methods, element, reset_value = false, form_same_name_array = false) {
        var form = element.parentElement.parentElement;
        form = form.tagName != 'FORM' 
            ? form.querySelector('form') 
            : form;
        var form_data = new FormData(form);

        if (form.checkValidity()) {           
            for (controller_method of controller_methods){
                reset_value 
                    ? update(controller_method, form_data, step, element, form_same_name_array)
                    : update(controller_method, form_data, step, false, form_same_name_array);
            }
        } else {
            form.reportValidity();
        }
    }

    // Function to change the active step
    function changeStep(step, step_completed=false, step_completed_number=false) {
        removeClassFromElements('active', 'step');

        let next_step = document.getElementById('step' + step);
        if (next_step) {
            next_step.classList.add('active');
        }

        if (step_completed_number) {
            updateOnboardingNav(step_completed_number);
            updateOnboardingCompletedStep('OnboardingStepsController@update', step_completed);
        }

        reinitializeMDBForm();
        addParameterToURL('step=' + step);
    }

    function updateOnboardingNav(step){
        removeClassFromElements('active', 'nav-step-item');

        if ( step <= 5 ){
            let current_navigation_step = document.getElementById("nav_step" + step);
            current_navigation_step.classList.add('active');
        }
        
        let previous_navigation_step = document.getElementById("nav_step" + (step-1));       
        previous_navigation_step.classList.add('completed');
        sideMenuActions(false)
    }

    function onboardingNavChangeStep(nav_step, onboarding_step){
        removeClassFromElements('active', 'nav-step-item');
        removeClassFromElements('current', 'nav-step-item');

        let current_navigation_step = document.getElementById("nav_step" + nav_step);   
        current_navigation_step.classList.add('active');
        current_navigation_step.classList.add('current');
        
        changeStep(onboarding_step);
    }

    // On page load, get the step parameter from the URL and call changeStep
    document.addEventListener('DOMContentLoaded', () => {
        const step = getUrlParameter('step');
        
        var customerOnboarding = <?= json_encode($customer_onboarding); ?>;
        var activeStep = 0; 
        var navStepCompleted = 0;
        
        if ( customerOnboarding ){
            var steps = [
                { key: 'general_info', step: 2, fallback: 1 },
                { key: 'settings', step: 3, fallback: 7 },
                { key: 'employees', step: 4, fallback: 13 },
                { key: 'suppliers', step: 5, fallback: 19 },
                { key: 'customize', step: 6, fallback: 21 }
            ];

            steps.forEach(function(step) {
                if (customerOnboarding[step.key]) {
                    updateOnboardingNav(step.step);
                    navStepCompleted++;
                }
            });

            activeStep = navStepCompleted < 5 
                ? steps[navStepCompleted]['fallback'] 
                : 34;
        }
       
        if ( step == 0 && navStepCompleted == 0){
            changeStep(0);
        } else if (step >= 1 && step <= 6) {
            onboardingNavChangeStep(1, step);
        } else if ( (step >= 7 && step <= 12) || (step >= 70 && step <= 79)) {
            onboardingNavChangeStep(2, step);
        } else if (step >= 13 && step <= 18) {
            onboardingNavChangeStep(3, step);
        } else if (step >= 19 && step <= 20) {
            onboardingNavChangeStep(4, step);
        } else if (step >= 21) {
            onboardingNavChangeStep(5, step);
        }else if ( navStepCompleted < 5 ){
            changeStep(activeStep);
        }else if ( navStepCompleted == 5 ){
            changeStep(34)
            updateOnboardingNav(6)
        }

        // Shop Hours Time Picker
        $(".timepick").datetimepickerbs({
            format: "HH:mm",
        });

        // Datatables Defined
        dataTable('datatable_staff');
        dataTable('datatable_employee_job_descriptions');
        dataTable('datatable_customer_concern_categories');
        dataTable('datatable_canned_discounts');
        dataTable('datatable_calendar_settings');
        dataTable('datatable_ro_types');
        dataTable('datatable_ro_statuses');
        dataTable('datatable_part_codes');

        // Employee ADD Date Hired 
        const options = {
            confirmDateOnSelect: true,
            disableFuture: true,
            format: "mm/dd/yyyy",
            inline: true
        }
        datePicker = document.getElementById('datehired_wrapper');
        const dateHiredPicker = new mdb.Datepicker(datePicker, options);
    });
    
    function update(action, data, step, element = false, form_same_name_array = false){
        let requestData = {};
        data.forEach((value, key) => {
            if (form_same_name_array) {
                if (!requestData[key]) {
                    requestData[key] = [];
                }
                requestData[key].push(value);
            } else {
                requestData[key] = value;
            }
        });

        const employeeID = localStorage.getItem('employee_id');
        const employeeMode = localStorage.getItem('employee_mode')?.toLowerCase();

        if (action === 'EmployeeController@update' || step === 17) {
            requestData['EmployeeID'] = employeeID;
        }

        if (step === 160 && employeeMode === 'full') {
            step = 17;
        }

        $.ajax({
            url: 'api/routes.php',
            type: 'POST',
            data: {action: action, data : requestData},
            success: function(response) {
                response = JSON.parse(response);

                testing && console.log('Response:', response);
                
                addParameterToURL('step=' + step);

                handleResponse(response,step);

                //Set Value to empty after insert statement
                if ( element != false ){
                    let form = element.parentNode.parentNode;
                    form.querySelectorAll('input').forEach(function(inputElement){
                        inputElement.value = ""
                    })
                }
                
                // Save employee_id in local storage
                if (response.employee_id) {
                    localStorage.setItem('employee_id', response.employee_id);
                    localStorage.setItem('employee_mode', response.mode);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                console.error('AJAX xhr:', xhr.responseText);
                console.error('AJAX status:', status);
            }
        });
    }

    function handleResponse(response,step) {
        if (Array.isArray(response)) {
            let anySuccess = response.some(item => item.status === 'success');
            let anyInfo = response.some(item => item.status === 'info');

            if (anySuccess) {
                location.reload();
            } else if (anyInfo) {
                changeStep(step);
            } else {
                console.error('No operations were successful or informational:', response);
            }
        } else {
            if (response.status === 'success') {
                location.reload();
            } else if (response.status === 'info') {
                changeStep(step);
            } else {
                console.error('Operation failed:', response);
            }
        }
    }

    function destroy(action, id, element){
        $.ajax({
            url: 'api/routes.php',
            type: 'POST',
            data: {action: action, data : id},
            success: function(response) { 
                if (element.parentElement) {
                    element.parentElement.style.display = 'none';
                }
                testing && console.log('Response:', response);
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                console.error('AJAX xhr:', xhr.responseText);
                console.error('AJAX status:', status);
            }
        });
    }

    function updateOnboardingCompletedStep(action, id){
        let requestData = {};
        requestData[id] = 1;
        $.ajax({
            url: 'api/routes.php',
            type: 'POST',
            data: {action: action, data : requestData},
            
            success: function(response) { 
                testing && console.log('Response:', response);
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                console.error('AJAX xhr:', xhr.responseText);
                console.error('AJAX status:', status);
            }
        });
        
        // IF COMPLETED
        if (id == 'customize') {
            window.open('https://calendly.com/d/2my-8bt-2sg/shop-boss-onboarding', '_blank');
            window.location.href = "/v2/wip/wip.php";
        };
    }

    document.querySelectorAll('input[type="checkbox"]:not(.payment-methods input[type="checkbox"])').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            this.value = this.checked ? 'yes' : 'no';
        });
    });

    // COMPANY LOGO ACTIONS
    Dropzone.options.sbpdropzone = {
        maxFilesize: 5, // MB
        uploadMultiple: false,
        acceptedFiles: "image/*",
        success: function (file, response) {
            testing && console.log(response);

            let delete_logo_button = document.getElementById('delete_logo_button');
            delete_logo_button.classList.remove('hidden');

            let delete_logo_id = document.getElementById('delete_logo_id');
            delete_logo_id.classList.add('hidden');
            
            let myDropzone = Dropzone.forElement("#sbpdropzone");
            if (myDropzone.files.length > 1) {
                for (let i = 0; i < myDropzone.files.length - 1; i++) {
                    myDropzone.removeFile(myDropzone.files[i]);
                }
            }
        },
        error: function (file, response) {
            console.log(file + ":" + response);
            console.log(JSON.stringify(response));
        }
    };


    function deleteCompanyLogo(element) {
        logo = encodeURIComponent("<?php echo $logo; ?>");

        $.ajax({
            data: "logo=" + logo + "&t=deletelogo&shopid=<?php echo $shopid; ?>",
            url: "../settings/miscaction.php",
            type: "post",
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },
            success: function (r) {
                // handleResponse({'status' : 'success'}, 10)
                if ( element == "delete_logo_id" ){
                    let delete_logo_id = document.getElementById('delete_logo_id');
                    delete_logo_id.classList.toggle('hidden');
                }else{
                    Dropzone.forElement("#sbpdropzone").removeAllFiles(true);
                    Dropzone.forElement("#sbpdropzone").enable();

                    let delete_logo_button = document.getElementById('delete_logo_button');
                    delete_logo_button.classList.add('hidden');
                }
            }
        });
    }

    reinitializeMDBForm()
</script>