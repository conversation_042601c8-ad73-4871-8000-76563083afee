<?php
    $component = "onboarding-wizard-v2";
    include getRulesGlobal($component);

    include getHeadGlobal($component);
    include getHeaderGlobal($component);

    include getMenuGlobal($component);
?>
       
    <main id="main-container">
        <h2>Customer Onboarding</h2>
        <hr>

        <div class="wizard-container">
                <div class="welcome-screen step <?= !$customer_onboarding ? 'active' : ''; ?>" id="step0" onclick="changeStep(1)">
                    <div class="center-conent">
                        <img src="https://staging.shopbosspro.com/src/private/components/onboarding-wizard-v2/assets/logo.svg" alt="">
                        <h1>Welcome</h1>
                        <h4>New Setup</h4>
                            
                        <button type="button" class="btn btn-primary">Let's Get Started</button>
                    </div>
                </div>
                <div class="onboarding-wizard">
                    <!-- Navigation -->
                    <div>
                        <div class="onboarding-nav">
                            <div id="nav_step1" class="nav-step-item active" onclick="onboardingNavChangeStep(1,1)">
                                <svg class="step-logo" xmlns="http://www.w3.org/2000/svg" width="51" height="51" viewBox="0 0 51 51" fill="none">
                                    <path d="M9.03353 13V8.83334H42.3669V13H9.03353ZM9.03353 42.1667V29.6667H6.9502V25.5L9.03353 15.0833H42.3669L44.4502 25.5V29.6667H42.3669V42.1667H38.2002V29.6667H29.8669V42.1667H9.03353ZM13.2002 38H25.7002V29.6667H13.2002V38Z"/>
                                </svg>
                                <img class="step-check-mark" src="https://staging.shopbosspro.com/src/private/components/onboarding-wizard-v2/assets/check-mark.svg" alt="">
                                <span class="step-header">1</span>
                                <span class="step-title">General Info</span>
                            </div>
                            <div id="nav_step2" class="nav-step-item" onclick="onboardingNavChangeStep(2,7)">
                                <svg class="step-logo" xmlns="http://www.w3.org/2000/svg" width="43" height="42" viewBox="0 0 43 42" fill="none">
                                    <path d="M16.6873 39.2727L15.9455 33.3091C15.5437 33.1538 15.1651 32.9674 14.8097 32.75C14.4543 32.5326 14.1066 32.2996 13.7666 32.0511L8.24989 34.3807L3.15039 25.5284L7.92538 21.8943C7.89447 21.6769 7.87902 21.4672 7.87902 21.2653V20.0074C7.87902 19.8055 7.89447 19.5958 7.92538 19.3784L3.15039 15.7443L8.24989 6.89205L13.7666 9.22159C14.1066 8.97311 14.462 8.74015 14.8329 8.52273C15.2038 8.3053 15.5746 8.11894 15.9455 7.96364L16.6873 2H26.8863L27.628 7.96364C28.0298 8.11894 28.4084 8.3053 28.7638 8.52273C29.1192 8.74015 29.4669 8.97311 29.8069 9.22159L35.3236 6.89205L40.4231 15.7443L35.6481 19.3784C35.679 19.5958 35.6945 19.8055 35.6945 20.0074V21.2653C35.6945 21.4672 35.6636 21.6769 35.6018 21.8943L40.3768 25.5284L35.2773 34.3807L29.8069 32.0511C29.4669 32.2996 29.1115 32.5326 28.7406 32.75C28.3697 32.9674 27.9989 33.1538 27.628 33.3091L26.8863 39.2727H16.6873ZM21.8795 27.1591C23.672 27.1591 25.2019 26.5223 26.469 25.2489C27.7362 23.9754 28.3697 22.4379 28.3697 20.6364C28.3697 18.8348 27.7362 17.2973 26.469 16.0239C25.2019 14.7504 23.672 14.1136 21.8795 14.1136C20.056 14.1136 18.5184 14.7504 17.2667 16.0239C16.015 17.2973 15.3892 18.8348 15.3892 20.6364C15.3892 22.4379 16.015 23.9754 17.2667 25.2489C18.5184 26.5223 20.056 27.1591 21.8795 27.1591Z"/>
                                </svg>
                                <img class="step-check-mark" src="https://staging.shopbosspro.com/src/private/components/onboarding-wizard-v2/assets/check-mark.svg" alt="">
                                <span class="step-header">2</span>
                                <span class="step-title">Settings</span>
                            </div>
                            <div id="nav_step3" class="nav-step-item" onclick="onboardingNavChangeStep(3,13)">
                                <svg class="step-logo" xmlns="http://www.w3.org/2000/svg" version="1.0" width="107.000000pt" height="105.000000pt" viewBox="0 0 107.000000 105.000000" preserveAspectRatio="xMidYMid meet">
                                    <g transform="translate(0.000000,105.000000) scale(0.100000,-0.100000)" stroke="none">
                                        <path d="M460 813 c-86 -77 -18 -218 97 -200 70 12 115 85 93 152 -25 76 -129 102 -190 48z"/>
                                        <path d="M181 717 c-48 -17 -76 -58 -76 -114 0 -39 5 -49 37 -79 59 -54 135 -44 179 22 60 91 -34 207 -140 171z"/>
                                        <path d="M823 723 c-12 -2 -36 -20 -53 -39 -73 -83 -6 -206 104 -191 78 11 119 95 84 171 -19 44 -80 70 -135 59z"/>
                                        <path d="M475 559 c-100 -16 -180 -60 -135 -74 11 -4 46 -29 77 -56 93 -81 160 -80 254 7 32 30 67 54 76 54 30 1 -26 37 -82 54 -68 20 -130 25 -190 15z"/>
                                        <path d="M154 441 c-42 -18 -54 -50 -54 -143 l0 -88 140 0 140 0 0 45 0 45 43 -23 c59 -31 174 -31 235 1 l42 22 0 -45 0 -45 135 0 135 0 0 98 c0 131 -6 137 -136 137 l-93 0 -43 -41 c-57 -56 -97 -74 -158 -74 -61 0 -101 18 -158 74 -43 41 -43 41 -125 43 -45 2 -92 -1 -103 -6z"/>
                                    </g>
                                </svg>
                                <img class="step-check-mark" src="https://staging.shopbosspro.com/src/private/components/onboarding-wizard-v2/assets/check-mark.svg" alt="">
                                <span class="step-header">3</span>
                                <span class="step-title">Employees</span>
                            </div>
                            <div id="nav_step4" class="nav-step-item" onclick="onboardingNavChangeStep(4,19)">
                                <svg class="step-logo" xmlns="http://www.w3.org/2000/svg" width="43" height="42" viewBox="0 0 43 42" fill="none">
                                    <path d="M11.2998 35C9.84147 35 8.60189 34.4896 7.58105 33.4688C6.56022 32.4479 6.0498 31.2083 6.0498 29.75H2.5498V10.5C2.5498 9.5375 2.89251 8.71354 3.57793 8.02812C4.26335 7.34271 5.0873 7 6.0498 7H30.5498V14H35.7998L41.0498 21V29.75H37.5498C37.5498 31.2083 37.0394 32.4479 36.0186 33.4688C34.9977 34.4896 33.7581 35 32.2998 35C30.8415 35 29.6019 34.4896 28.5811 33.4688C27.5602 32.4479 27.0498 31.2083 27.0498 29.75H16.5498C16.5498 31.2083 16.0394 32.4479 15.0186 33.4688C13.9977 34.4896 12.7581 35 11.2998 35ZM11.2998 31.5C11.7956 31.5 12.2113 31.3323 12.5467 30.9969C12.8821 30.6615 13.0498 30.2458 13.0498 29.75C13.0498 29.2542 12.8821 28.8385 12.5467 28.5031C12.2113 28.1677 11.7956 28 11.2998 28C10.804 28 10.3883 28.1677 10.0529 28.5031C9.71751 28.8385 9.5498 29.2542 9.5498 29.75C9.5498 30.2458 9.71751 30.6615 10.0529 30.9969C10.3883 31.3323 10.804 31.5 11.2998 31.5ZM32.2998 31.5C32.7956 31.5 33.2113 31.3323 33.5467 30.9969C33.8821 30.6615 34.0498 30.2458 34.0498 29.75C34.0498 29.2542 33.8821 28.8385 33.5467 28.5031C33.2113 28.1677 32.7956 28 32.2998 28C31.804 28 31.3883 28.1677 31.0529 28.5031C30.7175 28.8385 30.5498 29.2542 30.5498 29.75C30.5498 30.2458 30.7175 30.6615 31.0529 30.9969C31.3883 31.3323 31.804 31.5 32.2998 31.5ZM30.5498 22.75H37.9873L34.0498 17.5H30.5498V22.75Z"/>
                                </svg>
                                <img class="step-check-mark" src="https://staging.shopbosspro.com/src/private/components/onboarding-wizard-v2/assets/check-mark.svg" alt="">
                                    <span class="step-header">4</span>
                                    <span class="step-title">Suppliers</span>
                                </div>
                                <div id="nav_step5" class="nav-step-item" onclick="onboardingNavChangeStep(5,21)">
                                <svg class="step-logo" width="45" height="45" viewBox="0 0 45 45" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9.375 37.5V24.375H5.625V20.625H16.875V24.375H13.125V37.5H9.375ZM9.375 16.875V7.5H13.125V16.875H9.375ZM16.875 16.875V13.125H20.625V7.5H24.375V13.125H28.125V16.875H16.875ZM20.625 37.5V20.625H24.375V37.5H20.625ZM31.875 37.5V31.875H28.125V28.125H39.375V31.875H35.625V37.5H31.875ZM31.875 24.375V7.5H35.625V24.375H31.875Z"/>
                                </svg>
                                <img class="step-check-mark" src="https://staging.shopbosspro.com/src/private/components/onboarding-wizard-v2/assets/check-mark.svg" alt="">
                                <span class="step-header">5</span>
                                <span class="step-title">Customize</span>
                            </div>
                        </div>
                    </div>
    
                    <!-- Steps -->
                    <div class="steps-container">
                        <div class="steps">
                            <!-- GENERAL -->
                            <div id="step1" class="step">
                                <h1>Can you tell us about your business?</h1>
                                <form id="formStep1">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content">
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" required tabindex="1" type="text" id="CompanyName" name="CompanyName"
                                                        value="<?= $company['name']; ?>">
                                                    <label for="CompanyName" class="form-label">Company Name *</label>
                                                </div>
                                                <div></div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" required tabindex="2" type="text" id="CompanyAddress" name="CompanyAddress"
                                                        value="<?= $company['address']; ?>">
                                                    <label for="CompanyAddress" class="form-label">Address *</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" required tabindex="3" type="text" id="CompanyCity" name="CompanyCity"
                                                        value="<?= $company['city']; ?>">
                                                    <label for="CompanyCity" class="form-label">City *</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" required tabindex="4" type="text" id="CompanyState" name="CompanyState"
                                                        value="<?= $company['state']; ?>">
                                                    <label for="CompanyState" class="form-label">State *</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" required tabindex="5" type="text" id="CompanyZip" name="CompanyZip"
                                                        value="<?= $company['zip_code']; ?>">
                                                    <label class="form-label" for="CompanyZip" id="cityfloatinglabel">Zip/Postal Code *</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" required tabindex="6" type="tel" id="CompanyPhone" name="CompanyPhone" data-mdb-input-mask="(*************"
                                                        value="<?= $company['phone']; ?>">
                                                    <label for="CompanyPhone" class="form-label" id="statefloatinglabel">Main Phone *</label>
                                                </div>
                                                <div></div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" required type="email" tabindex="8" id="CompanyEMail" name="CompanyEMail"
                                                        value="<?= $company['email']; ?>">
                                                    <label class="form-label" for="CompanyEMail">Shop Email *</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" type="string" tabindex="9" id="CompanyURL" name="CompanyURL"
                                                        value="<?= $company['website_url']; ?>">
                                                    <label for="CompanyURL" class="form-label">Shop Website</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-primary" onclick="submitStep(2,['CompanyController@update'], this)">Next</button>
                                    </div>                                
                                </form>
                            </div>
            
                            <div id="step2" class="step">
                                <h1>Additional Details</h1>
                                <form id="formStep2">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content">
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" type="text" tabindex="1" id="EPANo" name="EPANo"
                                                        value="<?= $company['epa_number']; ?>">
                                                    <label class="form-label" for="EPANo">EPA Number</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" type="text" tabindex="2" id="BarNo" name="BarNo"
                                                        value="<?= $company['state_local_license']; ?>">
                                                    <label for="BarNo" class="form-label">State/Local License #</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" type="text" tabindex="3" id="EinNo" name="EinNo"
                                                        value="<?= $company['ein_number']; ?>">
                                                    <label for="EinNo" class="form-label">EIN Number</label>
                                                </div>
                                                <div></div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" type="text" tabindex="4" id="DefaultWarrMos" name="DefaultWarrMos"
                                                        value="<?= $company['warranty_months']; ?>">
                                                    <label for="DefaultWarrMos" class="form-label">Warranty Months</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" type="text" tabindex="5" id="DefaultWarrMiles" name="DefaultWarrMiles"
                                                        value="<?= $company['warranty_miles']; ?>">
                                                    <label class="form-label" for="DefaultWarrMiles">Warranty Miles</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" type="text" tabindex="6" id="pswarrdays" name="pswarrdays"
                                                        value="<?= $company['part_sale_warranty_days']; ?>">
                                                    <label class="form-label" for="pswarrdays">Part Sale Warranty in Days</label>
                                                </div>
                                                <div></div>
                                                <div style="position: relative;">
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control rounded" type="text" tabindex="7" id="UserPassword" name="UserPassword"
                                                            value="<?= $company['kiosk_password']; ?>">
                                                        <label class="form-label" for="UserPassword">Kiosk Password</label>
                                                    </div>
                                                    <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="At least 6 characters, one capital letter and one number" style="position:absolute; top:-10px; left:96px; background:white; border-radius:50%"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary" onclick="changeStep(1)">Back</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(3,['CompanyController@update'], this)">Next</button>
                                    </div>  
                                </form>
                            </div>
            
                            <div id="step3" class="step">
                                <h1>Taxes and Fees</h1>
                                <form id="formStep3">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content">
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" type="text" tabindex="1" id="DefaultTaxRate" name="DefaultTaxRate"
                                                        value="<?= $company['parts_tax_rate']; ?>">
                                                    <label class="form-label" for="DefaultTaxRate">Parts Tax Rate</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" type="text" tabindex="2" id="DefaultLaborTaxRate" name="DefaultLaborTaxRate"
                                                        value="<?= $company['labor_tax_rate']; ?>">
                                                    <label class="form-label" for="DefaultLaborTaxRate">Labor Tax Rate</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" type="text" tabindex="3" id="DefaultSubletTaxRate" name="DefaultSubletTaxRate"
                                                        value="<?= $company['sublet_tax_rate']; ?>">
                                                    <label class="form-label" for="DefaultSubletTaxRate">Sublet Tax Rate</label>
                                                </div>
                                                <div></div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" type="text" tabindex="4" id="StorageFee" name="StorageFee"
                                                        value="<?= $company['storage_fee']; ?>"                                                    >
                                                    <label for="StorageFee" class="form-label">Storage Fee</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" type="text" tabindex="5" id="HazardousWaste" name="HazardousWaste"
                                                        value="<?= $company['hazardous_waste_fee']; ?>">
                                                    <label class="form-label" for="HazardousWaste">Hazardous Waste</label>
                                                </div>
                                                <?php if ($shop_is_canadian) {?>
                                                    <div class="accordion mb-4 grid-2" id="accordion-cantax">
                                                        <div class="accordion-item">
                                                            <div class="accordion-header" id="flush-cantax">
                                                                <button class="accordion-button collapsed p-3" type="button"
                                                                        data-mdb-toggle="collapse"
                                                                        data-mdb-target="#cantax" aria-expanded="false" aria-controls="advisors">
                                                                    <p class="mb-0">Canadian Tax
                                                                        <i class="fa fa-circle-info" title="Canadian Sales Tax Only. Values in these boxes will overwrite above tax rates<br>In setting the correct rates for Canada, you MUST enter the tax rate And turn on the tax type below." data-mdb-toggle="tooltip" data-mdb-html="true"></i>
                                                                    </p>
                                                                </button>
                                                            </div>
                                                            <div id="cantax" class="accordion-collapse collapse" aria-labelledby="flush-headingOne" data-mdb-parent="#cantax">
                                                                <div class="accordion-body">
                                                                    <div class="row mb-4">
                                                                        <div class="col-md-3 me-2">
                                                                            <div class="form-outline">
                                                                                <input class="form-control" type="text" tabindex="6" id="hst" name="hst"
                                                                                    value="<?= $company['hst_rate']; ?>">
                                                                                <label class="form-label" for="hst">HST</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2">
                                                                            <div class="form-check p-0 pt-1">
                                                                                <input type="checkbox" name="hstapplyon" tabindex="7" id="hstapplyon" class="form-check-input" value="P"
                                                                                    <?= stripos($company['hst_apply_on'], 'P') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="phst">Parts</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2 p-0 pt-1">
                                                                            <div class="form-check">
                                                                                <input type="checkbox" id="lhst" name="hstapplyon" tabindex="8" class="form-check-input" value="L"
                                                                                    <?= stripos($company['hst_apply_on'], 'L') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="hstapplyon">Labor</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2 p-0 pt-1">
                                                                            <div class="form-check">
                                                                                <input type="checkbox" name="hstapplyon" tabindex="9" class="form-check-input" id="hstapplyon" value="S"
                                                                                    <?= stripos($company['hst_apply_on'], 'S') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="hstapplyon">Sublet</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2 p-0 pt-1">
                                                                            <div class="form-check">
                                                                                <input type="checkbox" name="hstapplyon" tabindex="10" class="form-check-input" id="hstapplyon" value="A"
                                                                                    <?= stripos($company['hst_apply_on'], 'A') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="hstapplyon">All</label>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="row mb-4">
                                                                        <div class="col-md-3 me-2">
                                                                            <div class="form-outline">
                                                                                <input class="form-control" type="text" tabindex="11" id="gst" name="gst"
                                                                                    value="<?= $company['gst_rate']; ?>">
                                                                                <label class="form-label" for="gst">GST</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2">
                                                                            <div class="form-check p-0 pt-1">
                                                                                <input type="checkbox" name="gstapplyon" tabindex="12" id="pgst" class="form-check-input" value="P"
                                                                                    <?= stripos($company['gst_apply_on'], 'P') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="pgst">Parts</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2 p-0 pt-1">
                                                                            <div class="form-check">
                                                                                <input type="checkbox" id="lgst" name="gstapplyon" tabindex="13" class="form-check-input" value="L"
                                                                                    <?= stripos($company['gst_apply_on'], 'L') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="lgst">Labor</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2 p-0 pt-1">
                                                                            <div class="form-check">
                                                                                <input type="checkbox" name="gstapplyon" tabindex="14" class="form-check-input" id="sgst" value="S"
                                                                                    <?= stripos($company['gst_apply_on'], 'S') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="sgst">Sublet</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2 p-0 pt-1">
                                                                            <div class="form-check">
                                                                                <input type="checkbox" name="gstapplyon" tabindex="15" class="form-check-input" id="agst" value="A"
                                                                                    <?= stripos($company['gst_apply_on'], 'A') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="agst">All</label>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="row mb-4">
                                                                        <div class="col-md-3 me-2">
                                                                            <div class="form-outline">
                                                                                <input class="form-control" type="text" tabindex="16" id="pst" name="pst"
                                                                                    value="<?= $company['pst_rate']; ?>">
                                                                                <label class="form-label" for="pst">PST</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2">
                                                                            <div class="form-check p-0 pt-1">
                                                                                <input type="checkbox" name="pstapplyon" tabindex="17" id="ppst" class="form-check-input" value="P"
                                                                                    <?= stripos($company['pst_apply_on'], 'P') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="ppst">Parts</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2 p-0 pt-1">
                                                                            <div class="form-check">
                                                                                <input type="checkbox" id="lpst" name="pstapplyon" tabindex="18" class="form-check-input" value="L"
                                                                                    <?= stripos($company['pst_apply_on'], 'L') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="lpst">Labor</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2 p-0 pt-1">
                                                                            <div class="form-check">
                                                                                <input type="checkbox" name="pstapplyon" tabindex="19" class="form-check-input" id="spst" value="S"
                                                                                    <?= stripos($company['pst_apply_on'], 'S') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="spst">Sublet</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2 p-0 pt-1">
                                                                            <div class="form-check">
                                                                                <input type="checkbox" name="pstapplyon" tabindex="20" class="form-check-input" id="apst" value="A"
                                                                                    <?= stripos($company['pst_apply_on'], 'A') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="apst">All</label>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="row mb-4">
                                                                        <div class="col-md-3 me-2">
                                                                            <div class="form-outline">
                                                                                <input class="form-control" type="text" tabindex="21" id="qst" name="qst"
                                                                                    value="<?= $company['qst_rate']; ?>">
                                                                                <label class="form-label" for="hst">QST</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2">
                                                                            <div class="form-check p-0 pt-1">
                                                                                <input type="checkbox" name="qstapplyon" tabindex="22" id="pqst" class="form-check-input" value="P"
                                                                                    <?= stripos($company['qst_apply_on'], 'P') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="pqst">Parts</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2 p-0 pt-1">
                                                                            <div class="form-check">
                                                                                <input type="checkbox" id="lqst" name="qstapplyon" tabindex="23" class="form-check-input" value="L"
                                                                                    <?= stripos($company['qst_apply_on'], 'L') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="lqst">Labor</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2 p-0 pt-1">
                                                                            <div class="form-check">
                                                                                <input type="checkbox" name="qstapplyon" tabindex="24" class="form-check-input" id="sqst" value="S"
                                                                                    <?= stripos($company['qst_apply_on'], 'S') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="sqst">Sublet</label>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-2 p-0 pt-1">
                                                                            <div class="form-check">
                                                                                <input type="checkbox" name="qstapplyon" tabindex="25" class="form-check-input" id="aqst" value="A"
                                                                                    <?= stripos($company['qst_apply_on'], 'A') !== false ? "checked='checked'" : '' ?>>
                                                                                <label class="form-check-label" for="aqst">All</label>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="form-group mb-4">
                                                                        <div class="form-check  form-switch form-check-inline">
                                                                            <input type="checkbox" tabindex="26" id="chargehst" class="form-check-input" name="chargehst"
                                                                                <?= $company['charge_hst'] == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                                            <label for="chargehst" class="form-check-label">HST</label>
                                                                        </div>
                                                                        <div class="form-check  form-switch form-check-inline">
                                                                            <input type="checkbox" tabindex="27" id="chargegst" name="chargegst" class="form-check-input"
                                                                                <?= $company['charge_gst'] == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                                            <label for="chargegst" class="form-check-label">GST</label>
                                                                        </div>
                                                                        <div class="form-check  form-switch form-check-inline">
                                                                            <input type="checkbox" tabindex="28" id="chargepst" name="chargepst" class="form-check-input"
                                                                                <?= $company['charge_pst'] == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                                            <label for="chargepst" class="form-check-label">PST</label>
                                                                        </div>
                                                                        <div class="form-check  form-switch form-check-inline">
                                                                            <input type="checkbox" tabindex="29" id="chargeqst" name="chargeqst" class="form-check-input"
                                                                                <?= $company['charge_qst'] == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                                            <label for="chargeqst" class="form-check-label">QST</label>
                                                                        </div>
                                                                    </div>
                                    
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary" onclick="changeStep(2)">Back</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(4,['CompanyController@update'], this)">Next</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step4" class="step">
                                <h1>
                                    Shop Hours 
                                    <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Hours are in Military/24 hours format."></i>
                                </h1>
                                <form id="formStep4">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content-block">
                                                <div class="row mb-4">                      
                                                    <div class='col-sm-2'>Mon:</div>
                                                    <div class='col-sm-5 mb-2'>
                                                        <div class="form-outline" data-mdb-inline="true">
                                                            <input type="text" name="open1" class="timepick form-control"
                                                                value="<?= isset($company_working_hours['1']) ? $company_working_hours['1']['start'] : '' ?>">
                                                        </div>
                                                    </div>
                                                    <div class='col-sm-5 mb-2'>
                                                        <div class="form-outline" data-mdb-inline="true">
                                                            <input type="text" class="timepick form-control" name="close1"
                                                                value="<?= isset($company_working_hours['1']) ? $company_working_hours['1']['end'] : '' ?>">
                                                        </div>
                                                    </div>
                                                    <br><br>
                                                    <div class='col-sm-2'>Tue:</div>
                                                    <div class='col-sm-5 mb-2'>
                                                        <div class="form-outline" data-mdb-inline="true">
                                                            <input type="text" name="open2" class="timepick form-control"
                                                                value="<?= isset($company_working_hours['1']) ? $company_working_hours['2']['start'] : '' ?>">
                                                        </div>
                                                    </div>
                                                    <div class='col-sm-5 mb-2'>
                                                        <div class="form-outline" data-mdb-inline="true">
                                                            <input type="text" class="timepick form-control" name="close2" 
                                                                value="<?= isset($company_working_hours['1']) ? $company_working_hours['2']['end'] : '' ?>">
                                                        </div>
                                                    </div>
                                                    <br><br>
                                                    <div class='col-sm-2'>Wed:</div>
                                                    <div class='col-sm-5 mb-2'>
                                                        <div class="form-outline" data-mdb-inline="true">
                                                            <input type="text" name="open3" class="timepick form-control"
                                                                value="<?= isset($company_working_hours['1']) ? $company_working_hours['3']['start'] : '' ?>">
                                                        </div>
                                                    </div>
                                                    <div class='col-sm-5 mb-2'>
                                                        <div class="form-outline" data-mdb-inline="true">
                                                            <input type="text" class="timepick form-control" name="close3"
                                                                value="<?= isset($company_working_hours['1']) ? $company_working_hours['3']['end'] : '' ?>">
                                                        </div>
                                                    </div>
                                                    <br><br>
                                                    <div class='col-sm-2'>Thu:</div>
                                                    <div class='col-sm-5 mb-2'>
                                                        <div class="form-outline" data-mdb-inline="true">
                                                            <input type="text" name="open4" class="timepick form-control"
                                                                value="<?= isset($company_working_hours['1']) ? $company_working_hours['4']['start'] : '' ?>">
                                                        </div>
                                                    </div>
                                                    <div class='col-sm-5 mb-2'>
                                                        <div class="form-outline" data-mdb-inline="true">
                                                            <input type="text" class="timepick form-control" name="close4"
                                                                value="<?= isset($company_working_hours['1']) ? $company_working_hours['4']['end'] : '' ?>">
                                                        </div>
                                                    </div>
                                                    <br><br>
                                                    <div class='col-sm-2'>Fri:</div>
                                                    <div class='col-sm-5 mb-2'>
                                                        <div class="form-outline" data-mdb-inline="true">
                                                            <input type="text" name="open5" class="timepick form-control"
                                                                value="<?= isset($company_working_hours['1']) ? $company_working_hours['5']['start'] : '' ?>">
                                                        </div>
                                                    </div>
                                                    <div class='col-sm-5 mb-2'>
                                                        <div class="form-outline" data-mdb-inline="true">
                                                            <input type="text" class="timepick form-control" name="close5"
                                                                value="<?= isset($company_working_hours['1']) ? $company_working_hours['5']['end'] : '' ?>">
                                                        </div>
                                                    </div>
                                                    <br><br>
                                                    <div class='col-sm-2'>Sat:</div>
                                                    <div class='col-sm-5 mb-2'>
                                                        <div class="form-outline" data-mdb-inline="true">
                                                            <input type="text" name="open6" class="timepick form-control"
                                                                value="<?= isset($company_working_hours['1']) ? $company_working_hours['6']['start'] : '' ?>">
                                                        </div>
                                                    </div>
                                                    <div class='col-sm-5 mb-2'>
                                                        <div class="form-outline" data-mdb-inline="true">
                                                            <input type="text" class="timepick form-control" name="close6"
                                                                value="<?= isset($company_working_hours['1']) ? $company_working_hours['6']['end'] : '' ?>">
                                                        </div>
                                                    </div>
                                                    <br><br>
                                                    <div class='col-sm-2'>Sun:</div>
                                                    <div class='col-sm-5 mb-2'>
                                                        <div class="form-outline" data-mdb-inline="true">
                                                            <input type="text" name="open0" class="timepick form-control"
                                                                value="<?= isset($company_working_hours['1']) ? $company_working_hours['0']['start'] : '' ?>">
                                                        </div>
                                                    </div>
                                                    <div class='col-sm-5 mb-2'>
                                                        <div class="form-outline" data-mdb-inline="true">
                                                            <input type="text" class="timepick form-control" name="close0"
                                                                value="<?= isset($company_working_hours['1']) ? $company_working_hours['0']['end'] : '' ?>">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary" onclick="changeStep(3)">Back</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(5,['ShopHoursController@update_or_store'], this)">Next</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step5" class="step">
                                <h1>
                                    Review your disclosures
                                    <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Any changes to disclosure statements will be for all estimates and invoices from that point moving forward. Changes will not back date existing Repair Orders/Part Sales/Quotes. It is very important to have this completed prior to writing any repair orders."></i>
                                </h1>
                                <form id="formStep5">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content-block">
                                                <div class="form-outline mb-4">
                                                    <textarea class="form-control" tabindex="1" id="rodisclosure" name="rodisclosure" rows="6"><?= $company['ro_disclosure']; ?></textarea>
                                                    <label class="form-label" for="rodisclosure">Signature - This will show on your printed RO's where the customer signs</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <textarea class="form-control" tabindex="2" id="rowarrdisclosure" name="rowarrdisclosure" rows="6"><?= $company['ro_warrany_disclosure']; ?></textarea>
                                                    <label for="rowarrdisclosure" class="form-label">Warranty - This explains your warranty and prints on all RO's</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <textarea class="form-control" tabindex="2" id="psdisclosure" name="psdisclosure" rows="6"><?= $company['ps_disclosure']; ?></textarea>
                                                    <label class="form-label" for="psdisclosure">Part Sale - This is the full disclosure for any Part Sale Tickets</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <textarea class="form-control" tabindex="2" id="quotedisclosure" name="quotedisclosure" rows="6"><?= $company['quote_disclosure']; ?></textarea>
                                                    <label class="form-label" for="quotedisclosure">Quote - This is the full disclosure for any Quotes</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary" onclick="changeStep(4)">Back</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(6,['CompanyController@update'], this)">Next</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step6" class="step complete-intro">
                                <h1>You have completed the <b>General Information</b> section of the setup!</h1>
                                <h4>Just a few more steps and you are off and running.</h4>
                                    
                                <div class="button-wrapper-center">
                                    <button type="button" class="btn btn-secondary btn-wide" onclick="window.location.href='/v2/wip/wip.php'">Save and Customize Later</button>
                                    <button type="button" class="btn btn-primary btn-wide-3" onclick="changeStep(7,'general_info',2)">Save and Continue to Settings</button>
                                </div>
                            </div>


                            <!-- SETTINGS -->
                            <div id="step7" class="step">
                                <h1>Do you have connections?</h1>
                                <form id="formStep7">
                                    <div class="form-wrapper">
                                        <div class="form-item settings">
                                            <div class="fi-content">
                                                <div class="c-item <?= $partstech_count > 0 ? 'completed' : '' ?>" onclick="window.open('https://www.partstech.com/signup')">
                                                    <div class="header">
                                                        <img src="https://staging.shopbosspro.com/src/public/assets/img/partstech-logo.png" alt="">
                                                    </div>
                                                </div>
                                                <div class="c-item <?= $nextpart_count > 0 ? 'completed' : '' ?>" onclick="changeStep(70)">
                                                    <div class="header">
                                                        <img src="https://staging.shopbosspro.com/src/public/assets/img/nexpart-logo.png" alt="">
                                                    </div>
                                                </div>
                                                <div class="c-item completed" onclick="changeStep(71)">
                                                    <div class="header">
                                                        <img src="https://staging.shopbosspro.com/src/public/assets/img/worldpac-logo.png" alt="">
                                                    </div>
                                                </div>
                                                <div class="c-item completed" onclick="changeStep(72)">
                                                    <div class="header">
                                                        <img src="https://staging.shopbosspro.com/src/public/assets/img/autoshopsolutions.png" alt="">
                                                    </div>
                                                </div>
                                                <div class="c-item completed" onclick="changeStep(73)">
                                                    <div class="header">
                                                        <img src="https://staging.shopbosspro.com/src/public/assets/img/landinglogo3.png" alt="">
                                                    </div>
                                                </div>
                                                <div class="c-item completed" onclick="changeStep(74)">
                                                    <div class="header">
                                                        <img src="https://staging.shopbosspro.com/src/public/assets/img/integrations/kukui-footer-logo.png" alt="">
                                                    </div>
                                                </div>
                                                <div class="c-item <?= $pay360_count > 0 ? 'completed' : '' ?>" onclick="changeStep(75)">
                                                    <div class="header">
                                                        <img src="https://staging.shopbosspro.com/src/public/assets/img/360pmts_dark.png" alt="">
                                                    </div>
                                                </div>
                                                <div class="c-item <?= !empty($backoffice_order_id) ? 'completed' : '' ?>" onclick="changeStep(78)">
                                                    <div class="header">
                                                        <img src="https://staging.shopbosspro.com/sbp/newimages/QB_intuitlogo.png" alt="">
                                                    </div>
                                                </div>
                                                <div class="c-item <?= $carfax_done > 0 ? 'completed' : '' ?>" onclick="changeStep(77)">
                                                    <div class="header">
                                                        <img src="https://staging.shopbosspro.com/src/public/assets/img/carfax-logo.png" alt="">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <h5 class="text-center mt-5 semi-black">Additional connections can be added later in your Settings</h5>
                                    </div>                               
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-primary" onclick="changeStep(8)">Next</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step70" class="step">
                                <h1>Nexpart Integration</h1>
                                <form id="formStep70">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content">
                                                <div class="form-inputs">
                                                    <div class="form-outline mb-4">
                                                        <input type="text" class="form-control" id="nexpartsupplier" name="desc">
                                                        <label for="nexpartsupplier" class="form-label">Enter Nexpart Supplier Name</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input type="text" class="form-control" id="nexpartusername" name="username">
                                                        <label for="nexpartusername" class="form-label">Enter Nexpart Username (provided by your part supplier)</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input type="text" class="form-control" id="nexpartpassword" name="password">
                                                        <label for="nexpartpassword" class="form-label">Enter Nexpart Password</label>
                                                    </div>
                                                </div>
                                                <div class="data-table">
                                                    <table id="nextable" class="sbdatatable w-100">
                                                        <tr>
                                                            <th>Supplier</th>
                                                            <th>Username</th>
                                                            <th>Password</th>
                                                            <th class="text-right">Delete</th>
                                                        </tr>
                                                        <?php foreach ($nextpart_users as $nextpart_user): ?>
                                                            <tr>
                                                                <td><?= $nextpart_user['supplier_name']; ?></td>
                                                                <td><?= $nextpart_user['username']; ?></td>
                                                                <td><?= $nextpart_user['password']; ?></td>
                                                                <td class="text-right" onclick="destroy('NextPartController@destroy', <?= $nextpart_user['id']; ?>, this)"><i class="fa fa-trash"></i></td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary" onclick="changeStep(7)">Skip</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(7, ['NextPartController@store'], this)">Done</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step71" class="step">
                                <h1>WorldPac Integration</h1>
                                <form id="formStep71">
                                    <div class="form-wrapper">
                                        <div class="form-item settings">
                                            <div class="fi-content-block">
                                                <?php
                                                    if (in_array($company['package'], ['none', 'platinum', 'premier', 'premier plus', 'gold'])) {
                                                        ?>
                                                            <p>WorldPac is automatically part of your Gold, Platinum or Premier account. To activate it in SpeedDial, please follow these instructions.</p>
                                                            <p>Instructions for configuring SpeedDial:</p>
                                                            <ol>
                                                                <li>Open SpeedDial</li>
                                                                <li>At the top left, click on View -&gt; Preferences</li>
                                                                <li>On the left side of the new window, click on Shop Software</li>
                                                                <li>On the right side click the checkbox labeled "Enable browser-based Shop Software Interface</li>
                                                                <li>Click Ok to save your changes</li>
                                                                <li>Open Shop Boss and you will be ready to transfer/export parts from SpeedDial to Shop Boss</li>
                                                            </ol>
                                                        <?php
                                                    }else{
                                                        ?>
                                                            <p>*WorldPac Parts Ordering is only available on Gold, Platinum or Premier plans.  Please contact Support to upgrade your account.</p>
                                                        <?php
                                                    }
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-primary" onclick="changeStep(7)">Done</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step72" class="step">
                                <h1>Autoshop SOLUTIONS Integration</h1>
                                <form id="formStep72">
                                    <div class="form-wrapper">
                                        <div class="form-item settings">
                                            <div class="fi-content-block">
                                                <p>
                                                    Autoshop Solutions, an automotive services-based digital marketing company, has integrated with Shop Boss to offer a ROI integration tool allowing shops to
                                                    see the actual return on their marketing investment.<br>
                                                    With this integration, Shop Boss data is pulled into the Autoshop Solutions’ dashboard, matching marketing data with the repair orders from the SMS. <br>
                                                    The outcome is real-life results on how marketing dollars are being spent, showing shops where the business came in and how to tie it to an actual
                                                    customer.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-primary" onclick="changeStep(7)">Done</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step73" class="step">
                                <h1>Referral Rock Integration</h1>
                                <form id="formStep73">
                                    <div class="form-wrapper">
                                        <div class="form-item settings">
                                            <div class="fi-content-block">
                                                <p>Your Customers - Referrals</p>
                                                <ul>
                                                    <li>Run multiple refer a friend campaigns</li>
                                                    <li>Setup notifications and statistics tracking</li>
                                                    <li>Personalize the referral experience</li>
                                                    <li>Reward with coupons, gift cards, or Paypal</li>
                                                    <li>Referral tracking through every stage</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-primary" onclick="changeStep(7)">Done</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step74" class="step">
                                <h1>KUKUI Integration</h1>
                                <form id="formStep74">
                                    <div class="form-wrapper">
                                        <div class="form-item settings">
                                            <div class="fi-content-block">
                                                <p>Kukui CRM and Web Hosting</p>
                                                <p>Kukui offers customized Web marketing, Search Engine Marketing and Optimization and Customer Relationship Management.</p>
                                                <ul class="mb-4">
                                                    <li>Customized Online Marketing - Great Online Marketing Is Not About Taking A One Size Fits All Approach</li>
                                                    <li>Conversion Rate Optimization - 300% Higher Conversion Rates Than Industry Average</li>
                                                    <li>Customer Relationship Management - A Business Philosophy, Not A Technical Solution</li>
                                                    <li>Revenue Tracking - View Your Marketing Success In One Place</li>
                                                    <li>Search Engine Marketing - Intelligent Design And High Visibility</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-primary" onclick="changeStep(7)">Done</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step75" class="step">
                                <h1>360 Payments Integration</h1>
                                <form id="formStep75">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content-block">
                                                <div class="devices mb-5">
                                                    <table id="nextable" class="sbdatatable w-100">
                                                        <tr>
                                                            <th>Device / Terminal Label</th>
                                                            <th>Device / Terminal Serial Number</th>
                                                            <th>Device / Terimnal ID</th>
                                                            <th class="text-right">Remove Device</th>
                                                        </tr>
                                                        <?php foreach ($pay360 as $p360): ?>
                                                            <tr>
                                                                <td><?= $p360['terminal_label']; ?></td>
                                                                <td><?= $p360['terminal_sn']; ?></td>
                                                                <td><?= $p360['terminal_id']; ?></td>
                                                                <td class="text-right" onclick="destroy('Devices360Controller@destroy', <?= $p360['id']; ?>, this)"><i class="fa fa-trash"></i></td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </table>
                                                </div>
                                                <div class="form">
                                                    <div class="form-outline mb-4 custom-label">
                                                        <input class="form-control" tabindex="6" type="text" id="cfpid" name="cfpid"
                                                            value="<?= $company['360_merchant_id']; ?>">
                                                        <label for="cfpid" class="form-label">360 Velox Merchant ID</label>
                                                    </div>
                                                    <div class="form-check form-switch mb-4 custom-label">
                                                        <input type="hidden" name="showcfp" class="form-check-input" value='no'>
                                                        <input type="checkbox" id="showcfp" name="showcfp" class="form-check-input"
                                                            <?= strtolower($settings['show_CFP']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                        <label class="form-check-label" for="showcfp">CFP</label>
                                                    </div>
                                                    <div class="form-outline mb-4 custom-label">
                                                        <input class="form-control" type="text" id="360password" name="360password"
                                                        value="<?= $refund_360_password; ?>">
                                                        <label for="360password" class="form-label">Void / Refund Password</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary" onclick="changeStep(7)">Skip</button>                                        
                                        <button type="button" class="btn btn-primary btn-wide" onclick="changeStep(76)">New Terimal</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(7, ['SettingsController@update','CompanyController@update', ['Password360Controller@update_or_store']], this)">Done</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step76" class="step">
                                <h1>360 Payments Integration | New Terminal</h1>
                                <form id="formStep76">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content-block">
                                                <div class="form">
                                                    <div class="form-outline mb-4 custom-label">
                                                        <input class="form-control" type="text" id="terminallabel" name="terminallabel">
                                                        <label for="terminallabel" class="form-label">Terminal Label</label>
                                                    </div>
                                                    <div class="form-outline mb-4 custom-label">
                                                        <input class="form-control" type="text" id="terminalid" name="terminalid">
                                                        <label for="terminalid" class="form-label">Terminal ID</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary" onclick="changeStep(75)">Skip</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(75, ['Devices360Controller@store'], this)">Done</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step77" class="step">
                                <h1>CARFAX Integration</h1>
                                <form id="formStep77">
                                    <div class="form-wrapper">
                                        <div class="form-item settings">
                                            <div class="fi-content-block">
                                                <?php
                                                    if ( $carfax_done ) {
                                                        ?>
                                                            <p>QuickVIN and Service History</p>
                                                            <p>Access vehicle Service History and utilize the QuickVIN decoder.</p>

                                                            <ul>
                                                                <li>Enables you to decode vehicle information from License Plate and / or VIN number.</li>
                                                                <li>Get the vehicle Service History</li>
                                                                <li>Free for any account</li>
                                                            </ul>
                                                        <?php
                                                    }else{
                                                        ?>
                                                            <div class="form-outline mb-4">
                                                                <input class="form-control" id="carfaxcontact" name="carfax_contact">
                                                                <label for="carfaxcontact" class="form-label">Business Contact at your Shop</label>
                                                            </div>
                                                            <div class="form-outline mb-4">
                                                                <input class="form-control" id="carfaxemail" name="carfax_email">
                                                                <label for="carfaxemail" class="form-label">Contact Email Address</label>
                                                            </div>
                                                        <?php
                                                    }
                                                ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="carfaxEULA" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content p-4">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="spdLabel">Carfax End User License Agreement</h5>
                                                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-md-12">
                                                            <p class="small">
                                                                By joining the CARFAX Service Network ("Service Network"), the facility (“Facility" or “us” or “we” or “our”) agrees that the terms herein are a legally binding
                                                                agreement between Facility and Carfax, Inc. (“CARFAX”). Facility authorizes the release of all information from the service records of Facility (including, but
                                                                not limited to, VIN or license plate with state, date, mileage, service and repair order information) to CARFAX directly or through Facility's SMS provider for
                                                                perpetual use in CARFAX's products or services. In addition, Facility authorizes CARFAX or its designee to (i) populate our records from time to time with VINs
                                                                and VIN-derived vehicle information and (ii) provide Facility with access to additional products or services that may be offered by CARFAX from time to time
                                                                (all such VINs and VIN-derived information, products or services provided by CARFAX is collectively referred to as the “Vehicle Information”). As a member of
                                                                the Service Network, we will receive, at no cost, the Service Network benefits that CARFAX offers to members from time to time, which currently include online
                                                                advertising after CARFAX receives the necessary information from Facility (which advertising may be delayed or withheld for archived records or records provided
                                                                without a valid VIN).
                                                            </p>
                                                            <p class="small">
                                                                Should we receive and/or use any Vehicle Information, Facility agrees (i) to comply with all then-current CARFAX terms and conditions applicable to such Vehicle
                                                                Information and (ii) not to provide or make available any of the Vehicle Information and/or any of our records as enhanced by the Vehicle Information to any
                                                                other provider of vehicle history products or services. We understand that if we do not comply with the foregoing commitments, CARFAX will no longer be
                                                                obligated to provide us with the Vehicle Information, free advertising or any other benefits of the Service Network.
                                                            </p>
                                                            <p class="small">
                                                                We understand that CARFAX will only use vehicle-specific information, and CARFAX will not use personal information about Facility or its customers. We
                                                                acknowledge that (i) Facility's name, address, telephone number and website address (if applicable) shall be published on CARFAX products and services in
                                                                connection with the information provided by Facility so long as we continue to remain a member of the Service Network.
                                                                We also understand that CARFAX relies on its sources for the accuracy and reliability of its information and cannot verify every record we provide. Should we
                                                                discover that erroneous information was delivered by Facility, we agree to notify CARFAX of the error(s) and CARFAX will work with Facility to correct the
                                                                erroneous information.
                                                            </p>
                                                            <p class="small">
                                                                The Service Network is subject to change or termination at any time in CARFAX's sole discretion.
                                                                Should Facility stop being a member of the Service Network for any reason, CARFAX may continue to use our records that were previously provided to CARFAX
                                                                pursuant to this authorization without any further benefits of the Service Network to Facility.
                                                            </p>
                                                            <p class="small">
                                                                We understand that CARFAX collects data from public records and other third-party sources to provide the Vehicle Information, and that the Vehicle Information
                                                                may contain errors and omissions. CARFAX does not guarantee the correctness of the Vehicle Information, and CARFAX shall have no liability for any loss or
                                                                damage (including, without limitation, any indirect or consequential damages) (i) caused by errors or omissions in Vehicle Information, (ii) resulting from
                                                                errors in transmitting the Vehicle Information or interruptions and/or errors in the functioning of the services that transmit the Vehicle Information, or (iii)
                                                                resulting from any use of the Vehicle Information.
                                                            </p>
                                                            <p class="small">
                                                                Facility may be asked to confirm the accuracy of the provided Vehicle Information and agrees to ensure that our employees and contractors complete the
                                                                confirmation process before accepting the Vehicle Information.
                                                                By enrolling Facility in the Service Network, I certify that I am authorized to enter into this Service Network agreement on behalf of Facility.
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="modal-footer d-flex justify-content-center">
                                                    <button class="btn btn-md btn-primary" type="button" onclick="submitStep(7, ['CarfaxController@update'], document.getElementById('carfaxButton'))">Accept Agreement</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-primary" id="carfaxButton"
                                            onclick='<?php echo $company['carfax_location'] == $shopid ? 'changeStep(7)' : '$("#carfaxEULA").modal("show")'; ?>'>
                                            Done
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <div id="step78" class="step">
                                <h1>QuickBooks Integration</h1>
                                <form id="formStep78">
                                    <div class="form-wrapper">
                                        <div class="form-item settings">
                                            <div class="fi-content-block">
                                                <ul class="mb-5">
                                                    <li>Integrator for both desktop and online versions of Quickbooks.</li>
                                                    <li>Integrator for Sage Simply Accounting Systems, and Sage Business Works.</li>
                                                    <li>Desktop installed application, provided by Accounting Link, sends information to your accounting software.</li>
                                                    <li>Accounting Link does not work on Mac devices.</li>
                                                    <li>One time integration fee of <b>$350.00</b> and Monthly fee of
                                                        <b>$50.00</b>. This fee ensures ongoing development and live support and setup help from Accounting Link directly.
                                                    </li>
                                                </ul>
                                                <?php
                                                    if ( !empty($backoffice_order_id) ) {
                                                        ?>
                                                            <p class="active">
                                                                <?= 
                                                                    !empty($backoffice_key) 
                                                                        ? "You have successfully initiated the Back Office Integration process. Back Office Support will reach out soon."
                                                                        : "Back Office Integrated."  
                                                                ?>
                                                            </p>
                                                        <?php
                                                    }else{
                                                        ?>
                                                            <div class="company-info">
                                                                <div class="form-outline mb-4">
                                                                    <input class="form-control" required tabindex="1" type="text" id="CompanyName" name="CompanyName"
                                                                        value="<?= $company['name']; ?>">
                                                                    <label for="CompanyName" class="form-label">Company Name *</label>
                                                                </div>
                                                                <div></div>
                                                                <div class="form-outline mb-4">
                                                                    <input class="form-control" required tabindex="2" type="text" id="CompanyAddress" name="CompanyAddress"
                                                                        value="<?= $company['address']; ?>">
                                                                    <label for="CompanyAddress" class="form-label">Company Address *</label>
                                                                </div>
                                                                <div class="form-outline mb-4">
                                                                    <input class="form-control" required tabindex="3" type="text" id="CompanyCity" name="CompanyCity"
                                                                        value="<?= $company['city']; ?>">
                                                                    <label for="CompanyCity" class="form-label">Company City *</label>
                                                                </div>
                                                                <div class="form-outline mb-4">
                                                                    <input class="form-control" required tabindex="4" type="text" id="CompanyState" name="CompanyState"
                                                                        value="<?= $company['state']; ?>">
                                                                    <label for="CompanyState" class="form-label">Company State *</label>
                                                                </div>
                                                                <div class="form-outline mb-4">
                                                                    <input class="form-control" required tabindex="5" type="text" id="CompanyZip" name="CompanyZip"
                                                                        value="<?= $company['zip_code']; ?>">
                                                                    <label class="form-label" for="CompanyZip" id="cityfloatinglabel">Company Zip *</label>
                                                                </div>
                                                                <div class="form-outline mb-4">
                                                                    <input class="form-control" required tabindex="6" type="tel" id="CompanyPhone" name="CompanyPhone" data-mdb-input-mask="(*************"
                                                                        value="<?= $company['phone']; ?>">
                                                                    <label for="CompanyPhone" class="form-label" id="statefloatinglabel">Company Phone *</label>
                                                                </div>
                                                                <div class="form-outline mb-4">
                                                                    <input class="form-control" required tabindex="7" type="text" id="EmployeeFirstName" name="EmployeeFirstName"
                                                                        value="<?= $employee_first; ?>">
                                                                    <label for="sms_number" class="form-label" id="EmployeeFirstName">Contact First Name *</label>
                                                                </div>
                                                                <div class="form-outline mb-4">
                                                                    <input class="form-control" required type="text" tabindex="8" id="EmployeeLastName" name="EmployeeLastName"
                                                                        value="<?= $employee_last; ?>">
                                                                    <label class="form-label" for="EmployeeLastName">Contact Last Name *</label>
                                                                </div>
                                                                <div class="form-outline mb-4">
                                                                    <input class="form-control" required type="email" tabindex="9" id="EmployeeEmail" name="EmployeeEmail"
                                                                        value="<?= $employee_email; ?>">
                                                                    <label for="CompanyURL" class="form-label">Contact Email *</label>
                                                                </div>
                                                                <div class="form-outline mb-4">
                                                                    <input class="form-control" required type="text" tabindex="9" id="EmployeePhone" name="EmployeePhone"
                                                                        value="<?= $employee_phone; ?>">
                                                                    <label for="EmployeePhone" class="form-label">Contact Phone *</label>
                                                                </div>
                                                            </div>
                                                        <?php
                                                    }
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <?php 
                                            if ( empty($backoffice_order_id) ) { ?>
                                                <button type="button" class="btn btn-secondary" onclick="changeStep(7)">Back</button>
                                                <button type="button" class="btn btn-primary" onclick="submitStep(7, ['QuickBooksController@store'], this)">Done</button>
                                                <?php 
                                            } else{
                                                ?>
                                                <button type="button" class="btn btn-secondary" onclick="changeStep(7)">Back</button>
                                                <?php
                                            }
                                        ?>
                                    </div>
                                </form>
                            </div>
                
                            <div id="step8" class="step">
                                <h1>Invoice Preferences</h1>
                                <form id="formStep8">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                        <div class="fi-content">
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="printtechstory" class="form-check-input" value='no'>
                                                    <input type="checkbox" id="printtechstory" name="printtechstory" class="form-check-input"
                                                        <?= strtolower($company['print_tech_story']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label class="form-check-label" for="printtechstory">
                                                        Print Tech Story on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays technician notes added to a customer concern in a repair order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="printvitotals" class="form-check-input" value='no'>                                                        
                                                    <input type="checkbox" class="form-check-input" id="printvitotals" name="printvitotals"
                                                        <?= strtolower($company['print_vi_totals']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="printvitotals" class="form-check-label">
                                                        Show Subtotals for each Concern
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Shows a dollar amount of each concern along with the total of the repair order. "></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="printadvisorcomments" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="printadvisorcomments" name="printadvisorcomments"
                                                        <?= strtolower($company['print_advisor_comments']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="printadvisorcomments" class="form-check-label">
                                                        Print Advisor Comments on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays advisor notes added to a customer concern in a repair order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="replacerowithtag" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="replacerowithtag" name="replacerowithtag"
                                                        <?= strtolower($company['replace_ro_with_tag']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label class="form-check-label">
                                                        Print Tag Number in place of RO Number
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Display input Service Dispatch Tag number in place of the Repair Order Number."></i>
                                                    </label>
                                                </div>
                                                <?php if ($company['matco'] == 'yes'){ ?>
                                                    <div class="form-check form-switch mb-4 custom-label">
                                                        <input type="hidden" name="printscanresults" class="form-check-input" value='no'>
                                                        <input type="checkbox" class="form-check-input" id="printscanresults" name="printscanresults"
                                                            <?= strtolower($settings['print_scan_result']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                        <label for="printscanresults" class="form-check-label">
                                                            Print Scan Tool Results on Invoice
                                                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays Scan Tool results on printed invoice."></i>
                                                        </label>
                                                    </div>
                                                <?php } ?>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="showtechoninvoice" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="showtechoninvoice" name="showtechoninvoice"
                                                        <?= strtolower($company['show_tech_on_invoice']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="showtechoninvoice" class="form-check-label">
                                                        Show Tech on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays the Technicians name that performed the labor."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="showtirepressure" class="form-check-input" value='no'>   
                                                    <input type="checkbox" class="form-check-input" id="showtirepressure" name="showtirepressure"
                                                        <?= strtolower($company['show_tire_pressure']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="showtirepressure" class="form-check-label">
                                                        Show Tire Pressure & Tread Depth on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays 4-wheel vehicle diagram at the top of the invoice to indicate tread depth and pressure. Will be displayed on all invoices."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="showlaborhoursonro" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="showlaborhoursonro" name="showlaborhoursonro"
                                                        <?= strtolower($company['show_labor_hours_on_ro']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="showlaborhoursonro" class="form-check-label">
                                                        Show Labor Hours on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays hours billed per labor line."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="printbar" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="printbar" name="printbar"
                                                        <?= strtolower($company['print_bar']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="printbar" class="form-check-label">
                                                        Print State/Local License # on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays Company State/Local License number input in the Company settings."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="itemizefeesprintedro" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="itemizefeesprintedro" name="itemizefeesprintedro"
                                                        <?= strtolower($company['itemize_fees_printed_ro']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="itemizefeesprintedro" class="form-check-label">
                                                        Itemize Fees on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="A breakdown of any fees associated with the repair."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="showdeclined" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="showdeclined" name="showdeclined"
                                                        <?= strtolower($company['show_declined']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="showdeclined" class="form-check-label">
                                                        Show Declined Items on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Declined repairs will be shown on the invoice as declined by the customer."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="showpayments" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="showpayments" name="showpayments"
                                                        <?= strtolower($company['show_payments']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="showpayments" class="form-check-label">
                                                        Show Payments on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Indicates any payments made on the repair order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="nexpartusername" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="nexpartusername" name="nexpartusername"
                                                        <?= strtolower($company['next_part_user_name']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="nexpartusername" class="form-check-label">
                                                        Show Line Item Prices
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays individual Part and Labor line prices in addition to the total of the concern."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="printpayments" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="printpayments" name="printpayments"
                                                        <?= strtolower($company['print_payments']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="printpayments" class="form-check-label">
                                                        Itemize Payments on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Gives a breakdown of payments made on repair order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="showadvisoroninvoice" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="showadvisoroninvoice" name="showadvisoroninvoice"
                                                        <?= strtolower($company['show_advisor_on_invoice']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="showadvisoroninvoice" class="form-check-label">
                                                        Show Service Writer on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Shows who the service writer was assigned to the repair order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="partsdiscountonro" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="partsdiscountonro" name="partsdiscountonro"
                                                        <?= strtolower($company['parts_discount_on_ro']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="partsdiscountonro" class="form-check-label">
                                                        Show Parts Discount on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Makes the parts discount savings visible to the customer on the invoice."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="showinvoicenumber" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="showinvoicenumber" name="showinvoicenumber"
                                                        <?= strtolower($company['show_invoice_number']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="showinvoicenumber" class="form-check-label">
                                                        Show Invoice Number on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays the invoice number associated with the repair order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="showpartnumberonprintedro" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="showpartnumberonprintedro" name="showpartnumberonprintedro"
                                                        <?= strtolower($company['show_part_number_on_printed_ro']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="showpartnumberonprintedro" class="form-check-label">
                                                        Show Part Numbers on RO & Quote Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Part Numbers will be displayed on Repair order and Quote invoices in addition to the part description."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="showpartnumberonprintedps" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="showpartnumberonprintedps" name="showpartnumberonprintedps"
                                                        <?= strtolower($company['show_part_number_on_printed_ps']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="showpartnumberonprintedps" class="form-check-label">
                                                        Show Part Numbers on PS Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Part Numbers will be displayed for over-the-counter part sale invoices in addition to the part description."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="customropage" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="customropage" name="customropage"
                                                        <?= strtolower($company['show_tech_hours']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label class="form-check-label">
                                                        Show Labor Hours on Tech Worksheet
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technicians will see how many hours were assigned per labor line on printed worksheet."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="showcustphonewo" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="showcustphonewo" name="showcustphonewo"
                                                        <?= strtolower($company['show_cust_phone_wo']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label class="form-check-label">
                                                        Show Customer Phone on Tech Worksheet
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technicians will see the customer's phone number on printed worksheets."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="showsourceonprintedro" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="showsourceonprintedro" name="showsourceonprintedro"
                                                        <?= strtolower($company['show_source_on_printed_ro']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="showsourceonprintedro" class="form-check-label">
                                                        Show Source on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays notated marketing source on the invoice. This is notated in the source field on a repair order. Used to track Marketing ROI."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="printcommlog" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="printcommlog" name="printcommlog"
                                                        <?= strtolower($company['print_comm_log']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="printcommlog" class="form-check-label">
                                                        Print Communication Log on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays text and email communication you have had with the customer on the invoice for the customer to see."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="showrevapps" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="showrevapps" name="showrevapps"
                                                        <?= strtolower($company['show_rev_apps']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="showrevapps" class="form-check-label">
                                                        Display Revision Approvals on Invoices
                                                        <i class="fa fa-circle-info" title="For California shops to meet the BAR requirements stated here https://www.bar.ca.gov/pdf/writeitright.pdf" data-mdb-toggle="tooltip" data-mdb-html="true"></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4 custom-label">
                                                    <input type="hidden" name="showpcodeoninvoice" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="showpcodeoninvoice" name="showpcodeoninvoice"
                                                        <?= strtolower($company['show_pcode_on_invoice']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="showpcodeoninvoice" class="form-check-label">
                                                        Show Part Code on Invoice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays part codes based on what is assigned to each part. Ex. New, Used, Re-Manufactured."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="signedinvoicesonstmts" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="signedinvoicesonstmts" name="signedinvoicesonstmts" 
                                                        <?= strtolower($settings['signed_invoice_son_stmts']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="signedinvoicesonstmts" class="form-check-label">
                                                        Display Signed Invoices on Statements
                                                        <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-html="true" data-mdb-placement="right" title="With this on, it will pull most recent signed invoice"></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="showpromisedateoninvoice" class="form-check-input" value='no'>
                                                    <input type="checkbox" class="form-check-input" id="showpromisedateoninvoice" name="showpromisedateoninvoice" 
                                                        <?= strtolower($settings['show_promise_date_on_invoice']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                    <label for="showpromisedateoninvoice" class="form-check-label">
                                                        Show Promise Date on Invoice
                                                        <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-html="true" data-mdb-placement="right" title="With this on, it will it will display promise date on invoice"></i>
                                                    </label>
                                                </div>
                                                <div class="form-outline mb-4 custom-label">
                                                    <input class="form-control" tabindex="6" type="text" id="invoicetitle" name="invoicetitle"
                                                        value="<?= $company['invoice_title']; ?>">
                                                    <label for="invoicetitle" class="form-label">
                                                        Invoice Label
                                                    </label>
                                                </div>
                                                <div class="form-outline mb-4 custom-label">
                                                    <input class="form-control" tabindex="6" type="text" id="estimatetitle" name="estimatetitle"
                                                        value="<?= $company['estimate_title']; ?>">
                                                    <label id="estimatetitle" class="form-label">
                                                        Estimate Label
                                                    </label>
                                                </div>
                                                <div class="form-outline mb-4 custom-label">
                                                    <input class="form-control" tabindex="6" type="text" id="milesinlabel" name="milesinlabel"
                                                        value="<?= $company['miles_in_label']; ?>">
                                                    <label for="milesinlabel" class="form-label">
                                                        Miles In Label
                                                    </label>
                                                </div>
                                                <div class="form-outline mb-4 custom-label">
                                                    <input class="form-control" tabindex="6" type="text" id="milesoutlabel" name="milesoutlabel"
                                                        value="<?= $company['miles_out_label']; ?>">
                                                    <label for="milesoutlabel" class="form-label">
                                                        Miles Out Label
                                                    </label>
                                                </div>
                                                <div class="form-outline mb-4 custom-label">
                                                    <input class="form-control" tabindex="6" type="text" id="quotelabel" name="quotelabel"
                                                        value="<?= $company['quote_label']; ?>">
                                                    <label for="quotelabel" class="form-label">
                                                        Quote Label
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary" onclick="changeStep(7)">Back</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(9,['CompanyController@update','SettingsController@update'], this)">Next</button>
                                    </div>
                                </form>
                            </div>


                            <div id="step9" class="step">
                                <h1>Repair Order Preferences</h1>
                                <form id="formStep9">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content-block">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <h3>RO Preferences</h3>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="RequirePayments" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="RequirePayments" name="RequirePayments" class="form-check-input" 
                                                                <?= strtolower($company['require_payments']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label for="RequirePayments" class="form-check-label">
                                                                Require Payments to Close RO
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Requires a payment to be notated on the repair to close the ticket."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="RequireBalanceRO" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="RequireBalanceRO" name="RequireBalanceRO" class="form-check-input"
                                                                <?= strtolower($company['require_balance_ro']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label for="RequireBalanceRO" class="form-check-label">
                                                                Balance RO Against Payments
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Requires the entire repair order to be paid before closing the ticket. Prevents the repair order from closing with a partial payment or deposit."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="requireoutmileage" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="requireoutmileage" name="requireoutmileage" class="form-check-input"
                                                                <?= strtolower($company['require_out_mileage']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label for="requireoutmileage" class="form-check-label">
                                                                Require Mileage Out to Close RO
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Requires current vehicle mileage after the repair is completed to be notated before you can close the repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="requiremileagetoprintro" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="requiremileagetoprintro" name="requiremileagetoprintro" class="form-check-input" 
                                                                <?= strtolower($company['require_mileage_to_print_ro']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label for="requiremileagetoprintro" class="form-check-label">
                                                                Require Mileage In to Print RO
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Requires vehicle mileage at the point of check in/drop off to be input before printing the repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="requireinspectiontofinal" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="requireinspectiontofinal" name="requireinspectiontofinal" class="form-check-input"
                                                                <?= strtolower($settings['require_inspection_to_final']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label for="requireinspectiontofinal" class="form-check-label">
                                                                Require Vehicle Inspection to Close RO
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Requires an Inspection to be completed for every repair order. Can be used to enforce inspections to be performed."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="usepreviousmileage" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="usepreviousmileage" name="usepreviousmileage" class="form-check-input"
                                                                <?= strtolower($settings['use_previous_mileage']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label for="usepreviousmileage" class="form-check-label">
                                                                Use Previous Mileage to Populate Mileage Box?
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Automatically populates the last recorded milage from previous service into the Mileage in field."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="sourcerequired" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="sourcerequired" name="sourcerequired" class="form-check-input"
                                                                <?= strtolower($settings['source_required']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label for="sourcerequired" class="form-check-label">
                                                                Source Required to Change RO Status
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Requires Marketing source to be entered in the RO to track return on investments. Helpful to track Repeat customers along with Referred business."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showwaiting" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showwaiting" name="showwaiting" class="form-check-input"
                                                                <?= strtolower($company['show_waiting']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label for="showwaiting" class="form-check-label">
                                                                Show Waiting vs Drop Off
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Populates a prompt in the repair order to notate whether the customer is waiting with the vehicle or dropping off. If notated Waiting, the ticket will highlight on the work in process to notate it as a priority ticket."></i>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <h3>Select Default Revisions/Payments/Fees/More section</h3>
                                                        <div class="form-row mb-4">
                                                            <select class="select" id="defaultrochild" name="defaultrochild" size="1">
                                                                <option <?php echo $communication_log_section; ?> value="commlog">Communication Log Section</option>
                                                                <option <?php echo $fees_section; ?> value="fees">Fees Section</option>
                                                                <option <?php echo $revisions_section; ?> value="revs">Revisions Section</option>
                                                                <option <?php echo $warranty_section; ?> value="warr">Warranty Section</option>
                                                                <option <?php echo $payments_section; ?> value="pmts">Payments Section</option>
                                                            </select>
                                                            <label for="defaultrochild" class="form-label select-label">Default Section Selection</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row mt-4">
                                                    <div class="col-md-12">
                                                        <h3>
                                                            Custom Shop Fees
                                                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Fees will be charged on every repair order based as a percentage or fixed dollar amount. Existing fees can be edited in the edit fees taxes section of the repair order on a case-by-case basis. Fees adjusted in the settings will not back date to any current repair orders. The changes will only apply to repair orders made moving forward."></i>
                                                        </h3>
                                                    </div>
                                                </div>
                                                <div class="custom_row">
                                                    <div>
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="UserFee1" name="UserFee1"
                                                                value="<?= $company['user_fee_1']; ?>">
                                                            <label for="UserFee1" class="form-label">Custom Fee 1</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="UserFee1amount" name="UserFee1amount"
                                                                value="<?= number_format($company['user_fee_1_amount'],2); ?>">
                                                            <label for="UserFee1amount" class="form-label">Amount</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-row mb-4">
                                                            <select class="select" id="userfee1type" name="userfee1type" size="1">
                                                                <option value="$" <?= $company['user_fee_1_type'] == '$' ? 'selected' : ''; ?>>$</option>
                                                                <option value="%" <?= $company['user_fee_1_type'] == '%' ? 'selected' : ''; ?>>%</option>
                                                            </select>
                                                            <label for="userfee1type" class="form-label select-label">Dollar or Percent</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="userfee1max" name="userfee1max"
                                                                value="<?= number_format($company['user_fee_1_max'], 2); ?>">
                                                            <label class="form-label" for="userfee1max">Fee Max</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-row mb-4">
                                                            <select class="select" id="userfee1taxable" name="userfee1taxable" size="1">
                                                                <option value="Taxable" <?= strtolower($company['user_fee_1_taxable']) == 'taxable' ? 'selected' : ''; ?>>Taxable</option>
                                                                <option value="Non-Taxable" <?= strtolower($company['user_fee_1_taxable']) == 'non-taxable' ? 'selected' : ''; ?>>Non-Taxable</option>
                                                            </select>
                                                            <label class="form-label select-label" for="userfee1taxable">Taxable</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-row mb-4">
                                                            <select class="select" id="userfee1applyon" name="userfee1applyon" size="1">
                                                                <option value="parts" <?= $company['user_fee_1_apply_on'] == 'parts' ? 'selected' : ''; ?>>Parts</option>
                                                                <option value="labor" <?= $company['user_fee_1_apply_on'] == 'labor' ? 'selected' : ''; ?>>Labor</option>
                                                                <option value="all" <?= $company['user_fee_1_apply_on'] == 'all' ? 'selected' : ''; ?>>All</option>
                                                            </select>
                                                            <label for="userfee1applyon" class="form-label select-label">Charge custom fee based on?</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="custom_row">
                                                    <div>
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="UserFee2" name="UserFee2"
                                                                value="<?= $company['user_fee_2']; ?>">
                                                            <label for="UserFee2" class="form-label">Custom Fee 2</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="UserFee2amount" name="UserFee2amount"
                                                                value="<?= number_format($company['user_fee_2_amount'], 2); ?>">
                                                            <label for="UserFee2amount" class="form-label">Amount</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-row mb-4">
                                                            <select class="select" id="userfee2type" name="userfee2type" size="1">
                                                                <option value="$" <?= $company['user_fee_2_type'] == '$' ? 'selected' : ''; ?>>$</option>
                                                                <option value="%" <?= $company['user_fee_2_type'] == '%' ? 'selected' : ''; ?>>%</option>
                                                            </select>
                                                            <label for="userfee1type" class="form-label select-label">Dollar or Percent</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="userfee2max" name="userfee2max"
                                                                value="<?= number_format($company['user_fee_2_max'], 2); ?>">
                                                            <label for="userfee2max" class="form-label">Fee Max</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-row mb-4">
                                                            <select class="select" id="userfee2taxable" name="userfee2taxable" size="1">
                                                                <option value="Taxable" <?= strtolower($company['user_fee_2_taxable']) == 'taxable' ? 'selected' : ''; ?>>Taxable</option>
                                                                <option value="Non-Taxable" <?= strtolower($company['user_fee_2_taxable']) == 'non-taxable' ? 'selected' : ''; ?>>Non-Taxable</option>
                                                            </select>
                                                            <label class="form-label select-label" for="userfee2taxable">Taxable</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-row mb-4">
                                                            <select class="select" id="userfee2applyon" name="userfee2applyon" size="1">
                                                                <option value="parts" <?= $company['user_fee_2_apply_on'] == 'parts' ? 'selected' : ''; ?>>Parts</option>
                                                                <option value="labor" <?= $company['user_fee_2_apply_on'] == 'labor' ? 'selected' : ''; ?>>Labor</option>
                                                                <option value="all" <?= $company['user_fee_2_apply_on'] == 'all' ? 'selected' : ''; ?>>All</option>
                                                            </select>
                                                            <label for="userfee2applyon" class="form-label select-label">Charge custom fee based on?</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="custom_row">
                                                    <div>
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="UserFee3" name="UserFee3" 
                                                                value="<?= $company['user_fee_3']; ?>">
                                                            <label for="UserFee3" class="form-label">Custom Fee 3</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="UserFee3amount" name="UserFee3amount"
                                                                value="<?= number_format($company['user_fee_3_amount'], 2); ?>">
                                                            <label for="UserFee3amount" class="form-label">Amount</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-row mb-4">
                                                            <select class="select" id="userfee3type" name="userfee3type" size="1">
                                                                <option value="$" <?= $company['user_fee_3_type'] == '$' ? 'selected' : ''; ?>>$</option>
                                                                <option value="%" <?= $company['user_fee_3_type'] == '%' ? 'selected' : ''; ?>>%</option>
                                                            </select>
                                                            <label for="userfee3type" class="form-label select-label">Dollar or Percent</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="userfee3max" name="userfee3max"
                                                                value="<?= number_format($company['user_fee_3_max'], 2); ?>">
                                                            <label for="userfee3max" class="form-label">Fee Max</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-row mb-4">
                                                            <select class="select" id="userfee3taxable" name="userfee3taxable" size="1">
                                                                <option value="Taxable" <?= strtolower($company['user_fee_3_taxable']) == 'taxable' ? 'selected' : ''; ?>>Taxable</option>
                                                                <option value="Non-Taxable" <?= strtolower($company['user_fee_3_taxable']) == 'non-taxable' ? 'selected' : ''; ?>>Non-Taxable</option>
                                                            </select>
                                                            <label class="form-label select-label" for="userfee3taxable">Taxable</label>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <div class="form-row mb-4">
                                                            <select class="select" id="userfee3applyon" name="userfee3applyon" size="1">
                                                                <option value="parts" <?= $company['user_fee_3_apply_on'] == 'parts' ? 'selected' : ''; ?>>Parts</option>
                                                                <option value="labor" <?= $company['user_fee_3_apply_on'] == 'labor' ? 'selected' : ''; ?>>Labor</option>
                                                                <option value="all" <?= $company['user_fee_3_apply_on'] == 'all' ? 'selected' : ''; ?>>All</option>
                                                            </select>
                                                            <label for="userfee3applyon" class="form-label select-label">Charge custom fee based on?</label>
                                                        </div>
                                                    </div>
                                                </div>
                                        
                                                <div class="row mt-4">
                                                    <div class="col-md-12">
                                                        <h3>
                                                            Shop Labor Rates
                                                            <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Create up to ten custom labor rates. Any changes made to hourly rates will only reflect on labor lines created after the change was made."></i>
                                                        </h3>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="hourlyrate1label" name="hourlyrate1label"
                                                                value="<?= $company['hourly_rate_1_label']; ?>">
                                                            <label class="form-label" for="hourlyrate1label">Shop Labor Rate 1</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="phone" id="hourlyrate" name="hourlyrate"
                                                                value="<?= number_format($company['hourly_rate_1'], 2); ?>">
                                                            <label class="form-label" for="hourlyrate">Rate 1</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="hourlyrate2label" name="hourlyrate2label"
                                                                value="<?= $company['hourly_rate_2_label']; ?>">
                                                            <label class="form-label" for="hourlyrate2label">Shop Labor Rate 2</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="phone" id="hourlyrate2" name="hourlyrate2"
                                                                value="<?= number_format($company['hourly_rate_2'], 2); ?>">
                                                            <label class="form-label" for="hourlyrate2">Rate 2</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="hourlyrate3label" name="hourlyrate3label"
                                                                value="<?= $company['hourly_rate_3_label']; ?>">
                                                            <label class="form-label" for="hourlyrate3label">Shop Labor Rate 3</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="phone" id="hourlyrate3" name="hourlyrate3"
                                                                value="<?= number_format($company['hourly_rate_3'], 2); ?>">
                                                            <label class="form-label" for="hourlyrate3">Rate 3</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="hourlyrate4label" name="hourlyrate4label"
                                                                value="<?= $company['hourly_rate_4_label']; ?>">
                                                            <label class="form-label" for="hourlyrate4label">Shop Labor Rate 4</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="phone" id="hourlyrate4" name="hourlyrate4"
                                                                value="<?= number_format($company['hourly_rate_4'], 2); ?>">
                                                            <label class="form-label" for="hourlyrate4">Rate 4</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="hourlyrate5label" name="hourlyrate5label"
                                                                value="<?= $company['hourly_rate_5_label']; ?>" >
                                                            <label class="form-label" for="hourlyrate5label">Shop Labor Rate 5</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="phone" id="hourlyrate5" name="hourlyrate5"
                                                                value="<?= number_format($company['hourly_rate_5'], 2); ?>">
                                                            <label class="form-label" for="hourlyrate5">Rate 5</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="hourlyrate6label" name="hourlyrate6label"
                                                                value="<?= $company['hourly_rate_6_label']; ?>">
                                                            <label class="form-label" for="hourlyrate6label">Shop Labor Rate 6</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="phone" id="hourlyrate6" name="hourlyrate6"
                                                                value="<?= number_format($company['hourly_rate_6'], 2); ?>">
                                                            <label class="form-label" for="hourlyrate6">Rate 6</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="hourlyrate7label" name="hourlyrate7label"
                                                                value="<?= $company['hourly_rate_7_label']; ?>">
                                                            <label class="form-label" for="hourlyrate7label">Shop Labor Rate 7</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="hourlyrate7" name="hourlyrate7"
                                                                value="<?= number_format($company['hourly_rate_7'], 2); ?>">
                                                            <label class="form-label" for="hourlyrate7">Rate 7</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="hourlyrate8label" name="hourlyrate8label"
                                                                value="<?= $company['hourly_rate_8_label']; ?>">
                                                            <label class="form-label" for="hourlyrate8label">Shop Labor Rate 8</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="hourlyrate8" name="hourlyrate8"
                                                                value="<?= number_format($company['hourly_rate_8'], 2); ?>">
                                                            <label class="form-label" for="hourlyrate8">Rate 8</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="hourlyrate9label" name="hourlyrate9label"
                                                                value="<?= $company['hourly_rate_9_label']; ?>">
                                                            <label class="form-label" for="hourlyrate9label">Shop Labor Rate 9</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="hourlyrate9" name="hourlyrate9"
                                                                value="<?= number_format($company['hourly_rate_9'], 2); ?>">
                                                            <label class="form-label" for="hourlyrate9">Rate 9</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="hourlyrate10label" name="hourlyrate10label"
                                                                value="<?= $company['hourly_rate_10_label']; ?>">
                                                            <label class="form-label" for="hourlyrate10label">Shop Labor Rate 10</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="form-outline mb-4">
                                                            <input class="form-control" type="text" id="hourlyrate10" name="hourlyrate10"
                                                                value="<?= number_format($company['hourly_rate_10'], 2); ?>">
                                                            <label class="form-label" for="hourlyrate10">Rate 10</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary" onclick="changeStep(8)">Back</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(10, ['CompanyController@update','SettingsController@update'], this)">Next</button>
                                    </div>
                                </form>
                            </div>

                            <!-- IT CONTAINS SETTINGS AND COMPANY -->
                            <div id="step10" class="step">
                                <h1>Misc Settings</h1>
                                <div class="form-wrapper">
                                    <div class="form-item">
                                        <div class="fi-content-block">
                                            <form id="formStep10">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="shopmgr" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="shopmgr" name="shopmgr" class="form-check-input"
                                                                <?= strtolower($company['shop_mgr']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="shopmgr">
                                                                Show Elapsed Time on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Tracks how long a repair order has been opened to the point that it is closed."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showtimeclockonwipdata" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showtimeclockonwipdata" name="showtimeclockonwipdata" class="form-check-input"
                                                                <?= strtolower($company['show_timeclock_on_wipdata']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showtimeclockonwipdata">
                                                                Show Labor Timeclock on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays how much time a technician has spent clocked into labor lines on a repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showpaymentonwip" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showpaymentonwip" name="showpaymentonwip" class="form-check-input"
                                                                <?= strtolower($company['show_payment_on_wip']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showpaymentonwip">
                                                                Show Payments Received on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Creates a visual Indicator to display when a payment has been made on a repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showemailestimateonwip" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showemailestimateonwip" name="showemailestimateonwip" class="form-check-input"
                                                                <?= strtolower($company['show_email_estimate_on_wip']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showemailestimateonwip">
                                                                Show Estimates Emailed on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Creates a visual indicator when an estimate has been emailed to the customer."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showemailinvoiceonwip" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showemailinvoiceonwip" name="showemailinvoiceonwip" class="form-check-input"
                                                                <?= strtolower($company['show_email_invoice_on_wip']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showemailinvoiceonwip">
                                                                Show Invoices Emailed on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Creates a visual indicator to when an invoice has been emailed to the customer."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showtextemail" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showtextemail" name="showtextemail" class="form-check-input"
                                                                <?= strtolower($company['show_text_email']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showtextemail">
                                                                Show Text/Email Sent on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Creates a visual indicator when a text/email has been sent to the customer."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showinspectiononwip" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showinspectiononwip" name="showinspectiononwip" class="form-check-input"
                                                                <?= strtolower($company['show_inspection_on_wip']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showinspectiononwip">
                                                                Show Completed Inspections on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Creates a visual indicator when an inspection is marked complete."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="nexpartpassword" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="nexpartpassword" name="nexpartpassword" class="form-check-input"
                                                                <?= strtolower($company['show_comm_log']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="nexpartpassword">
                                                                Show Communication Log on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays communication log on work in progress in addition to the repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="gponwiplist" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="gponwiplist" name="gponwiplist" class="form-check-input"
                                                                <?= strtolower($company['gp_on_wip_list']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="gponwiplist">
                                                                Show GP on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Shows Gross Profit calculations on the work in progress."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="autoshowta" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="autoshowta" name="autoshowta" class="form-check-input"
                                                                <?= strtolower($company['auto_show_ta']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="autoshowta">
                                                                Show Tech Activities on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays all activity done by the tech as an alert on the work in progress."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showwtk" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showwtk" name="showwtk" class="form-check-input"
                                                                <?= strtolower($company['show_wtk']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showwtk">
                                                                Show W+TK on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays who the service writer and technician is assigned to the repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showvehcolor" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showvehcolor" name="showvehcolor" class="form-check-input"
                                                                <?= strtolower($company['show_veh_color']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showvehcolor">
                                                                Show Vehicle Color on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays the vehicle color as a field on the work in progress for tracking purposes."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showhrsonwip" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showhrsonwip" name="showhrsonwip" class="form-check-input"
                                                                <?= strtolower($company['show_hrs_on_wip']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showhrsonwip">
                                                                Show Labor Hours on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Indicates how many labor hours are assigned to the repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showstatsonwip" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showstatsonwip" name="showstatsonwip" class="form-check-input"
                                                                <?= strtolower($company['show_stats_on_wip']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showstatsonwip">
                                                                Show Stats on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays current month key performance indicators along with profit per hour Net Variance if enabled on the Work in progress."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showsigonwip" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showsigonwip" name="showsigonwip" class="form-check-input"
                                                                <?= strtolower($company['show_sig_on_wip']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showsigonwip">
                                                                Show Signature on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Indicates when a repair order has been signed by the customer."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showpromiseonwip" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showpromiseonwip" name="showpromiseonwip" class="form-check-input"
                                                                <?= strtolower($company['show_promise_on_wip']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showpromiseonwip">
                                                                Show Promise Date/Time on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays promised time of completion on the work in progress."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="firstlastonwip" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="firstlastonwip" name="firstlastonwip" class="form-check-input"
                                                                <?= strtolower($company['first_last_on_wip']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="firstlastonwip">
                                                                Show Customer as First Last on WIP?
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays Customer name as First, Last instead of Last First."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="sortwipbysa" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="sortwipbysa" name="sortwipbysa" class="form-check-input"
                                                                <?= strtolower($settings['sort_wip_by_sa']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="sortwipbysa">
                                                                Sort WIP By Service Writer/Advisor
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Allows the work in process to be filtered by Service advisor to only display jobs they are working on."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="nexpartusername" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="nexpartusername" name="nexpartusername" class="form-check-input"
                                                                <?= strtolower($company['show_pics']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="nexpartusername">
                                                                Show Pics Uploaded to RO
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Visual indicator that shows when a picture is attached to a repair ticket."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showgp" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showgp" name="showgp" class="form-check-input"
                                                                <?= strtolower($company['show_gp']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showgp">
                                                                Calculate Gross Profit on RO's
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Calculates gross profit from repair order. Info will be displayed in reports."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showgponro" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showgponro" name="showgponro" class="form-check-input"
                                                                <?= strtolower($settings['show_gp_on_ro']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showgponro">
                                                                Show GP on RO
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays the Gross profit in the repair order once calculated."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showpartscostonro" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showpartscostonro" name="showpartscostonro" class="form-check-input"
                                                                <?= strtolower($company['show_parts_cost_on_ro']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showpartscostonro">
                                                                Show Parts Cost on RO Screen
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Part cost will display on the part line in a repair order for the shop to see. Employee permissions can prevent technicians from seeing this. All full mode users will be able to see part cost if turned on."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="sourceoptimized" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="sourceoptimized" name="sourceoptimized" class="form-check-input"
                                                                <?= strtolower($settings['source_optimized']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="sourceoptimized">
                                                                Source Optimized
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Default setting for Source type to help track marketing ROI. Will display last selected source type as a default."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showinstockparts" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showinstockparts" name="showinstockparts" class="form-check-input"
                                                                <?= strtolower($company['show_in_stock_parts']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showinstockparts">
                                                                Show In Stock Parts on RO
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays on-hand amount of inventory in repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="updateinvonadd" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="updateinvonadd" name="updateinvonadd" class="form-check-input"
                                                                <?= strtolower($company['update_inv_on_add']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="updateinvonadd">
                                                                Update Inventory When Saving a Part on an RO
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Deducts/adds to the on-hand amount of your inventory when a part is added or removed from a concern line in a repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="updateinvprices" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="updateinvprices" name="updateinvprices" class="form-check-input"
                                                                <?= strtolower($settings['update_inv_prices']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="updateinvprices">
                                                                Update Inventory Prices When Saving a Part on an RO
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Current inventory prices will adjust to reflect new part costs. If the part costs more, it will increase the cost and selling price to factor. If the part costs less, it will decrease the cost and selling price to factor."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="collapseissues" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="collapseissues" name="collapseissues" class="form-check-input"
                                                                <?= strtolower($settings['collapse_issues']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="collapseissues">
                                                                Auto Collapse All Vehicle Issues
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Collapses customer concern categories to hide parts and labor. Helpful for repair orders with multiple concern lines."></i>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="ccshop" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="ccshop" name="ccshop" class="form-check-input"
                                                                <?= strtolower($settings['cc_shop']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="ccshop">
                                                                Auto CC Company Email in all RO Email Communications
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Forwards all email communication to company email. This ensures a record of the email bodies for reference."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="scrolltotalswindow" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="scrolltotalswindow" name="scrolltotalswindow" class="form-check-input"
                                                                <?= strtolower($company['scroll_totals_window']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="scrolltotalswindow">
                                                                Allow Totals Box to Scroll
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Allows totals section in the repair order to move up and down on the page as you scroll instead of being fixed in place."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="conamereports" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="conamereports" name="conamereports" class="form-check-input"
                                                                <?= strtolower($company['coname_reports']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label for="conamereports" class="form-check-label">
                                                                Company Name on Reports
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays Company name on all reports."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="usepartsmatrix" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="usepartsmatrix" name="usepartsmatrix" class="form-check-input"
                                                                <?= strtolower($company['use_parts_matrix']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label for="usepartsmatrix" class="form-check-label">
                                                                Use Parts Matrix
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Turns on the parts matrix as the default setting for marking up parts. Will need to override the matrix in a repair order to adjust selling price if turned on."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="requiretechclockout" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="requiretechclockout" name="requiretechclockout" class="form-check-input"
                                                                <?= strtolower($company['require_tech_clockout']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="requiretechclockout">
                                                                Require Techs to clock out for clocking in
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Sends an alert to the technicians as a reminder to clock out at the end of the day."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="360popup" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="360popup" name="360popup" class="form-check-input"
                                                                <?= strtolower($settings['popup_360']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="360popup">
                                                                360 Pop-Up Display
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Enables 360 payments pop up alerts to display."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="sendprequal" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="sendprequal" name="sendprequal" class="form-check-input"
                                                                <?= strtolower($settings['send_prequal']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="sendprequal">
                                                                Send CFP Pre-Qual Link in All Emails
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="If equipped with 360 Payments, the system will send out consumer financing Pre-qualification links with all emails sent from the repair order."></i>
                                                            </label>
                                                        </div>

                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="overwriteqbtrans" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="overwriteqbtrans" name="overwriteqbtrans" class="form-check-input"
                                                                <?= strtolower($company['overwrite_qb_trans']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="overwriteqbtrans">
                                                                Allow Duplicate Transactions in QB
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Allows multiple transactions to transfer over to quick books instead of one at a time."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="inventoryappreciation" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="inventoryappreciation" name="inventoryappreciation" class="form-check-input"
                                                                <?= strtolower($company['inventory_appreciation']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="inventoryappreciation">
                                                                Use Inventory Appreciation/Depreciation
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Tracks if inventory has increased or decreased in value due to current costs based on purchase cost."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="useim" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="useim" name="useim" class="form-check-input"
                                                                <?= strtolower($company['use_instant_messenger']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="useim">
                                                                Use Internal Instant Messenger
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Messaging platform that can be used to message any employee that has an employee profile in the account."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="useschmaint" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="useschmaint" name="useschmaint" class="form-check-input"
                                                                <?= strtolower($company['use_sch_maint']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="useschmaint">
                                                                Enable Scheduled Maintenance Lookup
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Allows you to check/add factory OEM scheduled maintenance based on warranty periods for the vehicle. Pulls information based on proper vin decode or license plate decode through Carfax."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showtechoverhours" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showtechoverhours" name="showtechoverhours" class="form-check-input"
                                                                <?= strtolower($company['show_tech_over_hours']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showtechoverhours">
                                                                Show Tech Clock Hours Exceed Sold Hours
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Indicates when the technician has been clocked in on a labor line longer than it called for."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="feesonquote" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="feesonquote" name="feesonquote" class="form-check-input"
                                                                <?= strtolower($company['fees_on_quote']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="feesonquote">
                                                                Show Fees on Quote?
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Indicates estimated fees to provide a more accurate quote."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="requirerevapp" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="requirerevapp" name="requirerevapp" class="form-check-input"
                                                                <?= strtolower($settings['require_rev_app']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="requirerevapp">
                                                                Make Revision Approval Method Required?
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Requires a new signature capture for all revisions."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="commoldtonew" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="commoldtonew" name="commoldtonew" class="form-check-input"
                                                                <?= strtolower($settings['comm_old_to_new']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="commoldtonew">
                                                                Comm. Log Display Order Oldest to Newest?
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="The communication log will display the oldest communication to the most current."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="paymentonfinal" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="paymentonfinal" name="paymentonfinal" class="form-check-input"
                                                                <?= strtolower($settings['payment_on_final']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="paymentonfinal">
                                                                Send Update with Make Payment only on Final Status?
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Payment links will only generate to send during an update as an invoice not as an estimate."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="notesalert" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="notesalert" name="notesalert" class="form-check-input"
                                                                <?= strtolower($settings['notes_alert']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="notesalert">
                                                                Customer Note Alert
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Creates a note field in the customer’s profile to store notes on that customer. These notes will pop up an alert display the next time you do a service for that customer."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showphotos" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showphotos" name="showphotos" class="form-check-input"
                                                                <?= strtolower($settings['show_photos']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showphotos">
                                                                Show Employee photo and Company logo in menu
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays the employees’ photo and the company logo on the work in progress for that employee’s login. Photo must be added to an employees’ profile for it to display."></i>
                                                            </label>
                                                        </div>
                                                        <?php if ( !empty($parts_tech_list_price) ) { ?>
                                                            <div class="form-check form-switch mb-4">
                                                                <input type="hidden" name="uselistprice" class="form-check-input" value='no'>
                                                                <input type="checkbox" id="spf" name="uselistprice" class="form-check-input"
                                                                    <?= strtolower($parts_tech_list_price) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                                <label class="form-check-label" for="spf">
                                                                    Make List Price the Default for PartsTech orders
                                                                    <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Defaults all aftermarket part orders through parts tech to list pricing instead of matrix pricing."></i>
                                                                </label>
                                                            </div>
                                                        <?php } ?>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showpartfeeaspart" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showpartfeeaspart" name="showpartfeeaspart" class="form-check-input"
                                                                <?= strtolower($settings['show_part_fee_as_part']) == "yes" ? "checked value='yes'" : "value='no'" ?>>
                                                            <label class="form-check-label" for="showpartfeeaspart">
                                                                Show Partfee as Part
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Displays as a part fee line in the customer concern for parts that have an attached part fee in inventory. If turned off, part fees will display in the fees section of the repair order instead of as a part fee line in the customer concern."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-row mb-4">
                                                            <select id="showcarfaxonly" name="showcarfaxonly" class="select">
                                                                <option <?= $company['show_carfax_only'] == "yes" ? "selected" : "" ?> value="yes">Carfax Approved Only</option>
                                                                <option <?= $company['show_carfax_only'] == "no" ? "selected" : "" ?> value="no">No Carfax Descriptions</option>
                                                                <option <?= $company['show_carfax_only'] == "both" ? "selected" : "" ?> value="both">Carfax Approved and Standard Descriptions</option>
                                                            </select>
                                                            <label class="form-label select-label" for="showcarfaxonly">Use Carfax Labor Descriptions</label>
                                                        </div>
                                                        <div class="form-row mb-4">
                                                            <select id="calendardefault" name="calendardefault" class="select">
                                                                <option <?= $company['calendar_default'] == "agendaDay" ? "selected" : "" ?> value="agendaDay">Day</option>
                                                                <option <?= $company['calendar_default'] == "agendaTwoDay" ? "selected" : "" ?> value="agendaTwoDay">3 Days</option>
                                                                <option <?= $company['calendar_default'] == "agendaWeek" ? "selected" : "" ?> value="agendaWeek">Week</option>
                                                                <option <?= $company['calendar_default'] == "month" ? "selected" : "" ?> value="month">Month</option>
                                                            </select>
                                                            <label class="form-label select-label" for="calendardefault">Default Calendar View</label>
                                                        </div>
                                                        <div class="form-row mb-4">
                                                            <select class="select" id="defaultinspectionvalue" name="defaultinspectionvalue">
                                                                <option <?= $default_inspection_0; ?> value="0">Not Applicable</option>
                                                                <option <?= $default_inspection_1; ?> value="1">BAD</option>
                                                                <option <?= $default_inspection_2; ?> value="2">POOR</option>
                                                                <option <?= $default_inspection_3; ?> value="3">FAIR</option>
                                                                <option <?= $default_inspection_4; ?> value="4">GOOD</option>
                                                                <option <?= $default_inspection_5; ?> value="5">EXCELLENT</option>
                                                            </select>
                                                            <label for="defaultinspectionvalue" class="form-label select-label">Select Default Inspection Value</label>
                                                        </div>
                                                        <div class="form-row mb-4">
                                                            <select class="select" id="timezone" name="timezone">
                                                                <option <?php echo $timezones["pst"]; ?> value="pst">Pacific</option>
                                                                <option <?php echo $timezones["azst"]; ?> value="azst">Arizona</option>
                                                                <option <?php echo $timezones["mst"]; ?> value="mst">Mountain</option>
                                                                <option <?php echo $timezones["cst"]; ?> value="cst">Central</option>
                                                                <option <?php echo $timezones["est"]; ?> value="est">Eastern</option>
                                                                <option <?php echo $timezones["ast"]; ?> value="ast">Atlantic</option>
                                                                <option <?php echo $timezones["astnd"]; ?> value="astnd">Atlantic no DST</option>
                                                                <option <?php echo $timezones["akst"]; ?> value="akst">Alaska</option>
                                                                <option <?php echo $timezones["hst"]; ?> value="hst">Hawaii</option>
                                                                <option <?php echo $timezones["eat"]; ?> value="eat">East Africa</option>
                                                                <option <?php echo $timezones["chst"]; ?> value="chst">Chamorro</option>
                                                                <option <?php echo $timezones["aest"]; ?> value="aest">Australia</option>
                                                            </select>
                                                            <label for="timezone" class="form-label select-label">Select Shop Timezone</label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </form>
                                            <div class="row">
                                                <?php
                                                if (!empty($logo)) {
                                                    ?>
                                                    <div class="col-md-6 text-center" id="delete_logo_id">
                                                        <div class="text-primary float-end">
                                                            <i class="fas fa-trash pe-auto" style='cursor:pointer' onclick="deleteCompanyLogo('delete_logo_id')"></i>
                                                        </div>
                                                        <?= "<img id='logoimage' class='img-thumbnail ripple' src='" . UPLOAD_URL . "/$shopid/$logo'>"; ?>
                                                    </div>
                                                    <?php
                                                }
                                                ?>
                                                <div class="col-md-6 text-center mt-3">
                                                    <div class="text-primary float-end p-3 hidden" id="delete_logo_button">
                                                        <i class="fas fa-trash pe-auto" style='cursor:pointer' onclick="deleteCompanyLogo('delete_logo_button')"></i>
                                                    </div>
                                                    <form action="../settings/miscfiles/uploadlogo.php" class="dropzone" id="sbpdropzone">
                                                        <div class="dz-message" data-dz-message>
                                                            <span>Click the box or Drag and Drop your company logo here</span>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="button-wrapper-center">
                                    <button type="button" class="btn btn-secondary" onclick="changeStep(9)">Back</button>
                                    <button type="button" class="btn btn-primary" onclick="submitStep(11, ['CompanyController@update', 'SettingsController@update', 'PartsTechShopsController@update'], this)">Next</button>
                                </div>
                            </div>


                            <div id="step11" class="step">
                                <h1>
                                    Payment Methods
                                    <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Payment methods that are accepted with your current payment processor. They must match the payment methods that you use to track payments in accounting. These payment methods will transfer to your accounting once you notate a payment in a repair order and mark it closed."></i>
                                </h1>
                                <form id="formStep11">
                                    <div class="form-wrapper">
                                        <div class="form-item settings">
                                            <div class="fi-content payment-methods">
                                                <div class="c-item">
                                                    <input class="form-check-input" type="checkbox" name="method" value="Cash" id="cash" 
                                                    <?= $payment_method['cash'] ? "checked" : "" ?>>
                                                    <label class="form-check-label" for="cash">
                                                        <div class="header">
                                                            <img src="https://staging.shopbosspro.com/src/private/components/onboarding-wizard-v2/assets/cash.svg" alt="">
                                                        </div>
                                                    </label>
                                                </div>
                                                <div class="c-item">
                                                    <input class="form-check-input" type="checkbox" name="method" value="Check" id="check" 
                                                    <?= $payment_method['check'] ? "checked" : "" ?>>
                                                    <label class="form-check-label" for="check">
                                                        <div class="header">
                                                            <img src="https://staging.shopbosspro.com/src/private/components/onboarding-wizard-v2/assets/check.svg" alt="">
                                                        </div>
                                                    </label>
                                                </div>
                                                <div class="c-item">
                                                    <input class="form-check-input" type="checkbox" name="method" value="ACH" id="ach" 
                                                    <?= $payment_method['ach'] ? "checked" : "" ?>>
                                                    <label class="form-check-label" for="ach">
                                                        <div class="header">
                                                            <img src="https://staging.shopbosspro.com/src/private/components/onboarding-wizard-v2/assets/ach.svg" alt="">
                                                        </div>
                                                    </label>
                                                </div>
                                                <div class="c-item">
                                                    <input class="form-check-input" type="checkbox" name="method" value="EFT" id="eft"
                                                    <?= $payment_method['eft'] ? "checked" : "" ?>>
                                                    <label class="form-check-label" for="eft">
                                                        <div class="header">
                                                            <img src="https://staging.shopbosspro.com/src/private/components/onboarding-wizard-v2/assets/eft.svg" alt="">
                                                        </div>
                                                    </label>
                                                </div>
                                                <div class="c-item">
                                                    <input class="form-check-input" type="checkbox" name="method" value="Visa" id="visa"
                                                    <?= $payment_method['visa'] ? "checked" : "" ?>>
                                                    <label class="form-check-label" for="visa">
                                                        <div class="header">
                                                            <img src="https://staging.shopbosspro.com/src/private/components/onboarding-wizard-v2/assets/visa.svg" alt="">
                                                        </div>
                                                    </label>
                                                </div>
                                                <div class="c-item">
                                                    <input class="form-check-input" type="checkbox" name="method" value="Mastercard" id="mastercard"
                                                    <?= $payment_method['mastercard'] ? "checked" : "" ?>>
                                                    <label class="form-check-label" for="mastercard">
                                                        <div class="header">
                                                            <img src="https://staging.shopbosspro.com/src/private/components/onboarding-wizard-v2/assets/mastercard.svg" alt="">
                                                        </div>
                                                    </label>
                                                </div>
                                                <div class="c-item">
                                                    <input class="form-check-input" type="checkbox" name="method" value="American Express" id="american_express"
                                                    <?= $payment_method['american_express'] ? "checked" : "" ?>>
                                                    <label class="form-check-label" for="american_express">
                                                        <div class="header">
                                                            <img src="https://staging.shopbosspro.com/src/private/components/onboarding-wizard-v2/assets/american-express.svg" alt="">
                                                        </div>
                                                    </label>
                                                </div>
                                                <div class="c-item">
                                                    <input class="form-check-input" type="checkbox" name="method" value="Discover" id="discover" 
                                                    <?= $payment_method['discover'] ? "checked" : "" ?>>
                                                    <label class="form-check-label" for="discover">
                                                        <div class="header">
                                                            <img src="https://staging.shopbosspro.com/src/private/components/onboarding-wizard-v2/assets/discover.svg" alt="">
                                                        </div>
                                                    </label>
                                                </div>
                                                <?php foreach ($new_payment_methods as $payment): ?>
                                                    <div class="c-item new-payment">
                                                        <input class="form-check-input" type="checkbox" name="method" 
                                                            value="<?= $payment; ?>" id="id_<?= $payment; ?>" checked>
                                                        <label class="form-check-label" for="id_<?= $payment; ?>">
                                                            <div class="header">
                                                                <p><?= $payment; ?></p>
                                                            </div>
                                                        </label>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary" onclick="changeStep(10)">Back</button>
                                        <button type="button" class="btn btn-secondary btn-wide-2" onclick="submitStep(79, ['PaymentsMethodsController@store_defaults'], this, false, true)">Add New Payment Method</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(12, ['PaymentsMethodsController@store_defaults'], this, false, true)">Next</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step79" class="step">
                                <h1>New Payment Method</h1>
                                <form id="formStep79">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content-block">
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="1" type="text" id="payment_method" name="payment_method">
                                                    <label class="form-label" for="payment_method">Payment Method</label>
                                                </div>                                           
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary btn-wide-2" onclick="submitStep(79, ['PaymentsMethodsController@store'], this, true)">Add Another Payment Method</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(11, ['PaymentsMethodsController@store'], this, true)">Next</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step12" class="step complete-intro">
                                <h1>You have completed the <b>Settings</b> section of the setup!</h1>
                                <h4>Integrations, reports, repair orders, and payments are ready to go.</h4>
                                    
                                <div class="button-wrapper-center">
                                    <button type="button" class="btn btn-secondary btn-wide" onclick="window.location.href='/v2/wip/wip.php'">Save and Customize Later</button>
                                    <button type="button" class="btn btn-primary btn-wide-3" onclick="changeStep(13,'settings',3)">Save and Continue to Employees</button>
                                </div>
                            </div>


                            <!-- EMPLOYESS -->
                            <div id="step13" class="step">
                                <h1>Who is on your team?</h1>
                                <form id="formStep13">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content-block">
                                                <table id="datatable_employee_job_descriptions" class="sbdatatable w-100">
                                                    <thead>
                                                    <tr>
                                                        <th>Job Descriptions</th>
                                                        <th class="text-right">Delete</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($job_descriptions as $jd): ?>
                                                            <tr>
                                                                <td><?= $jd['job_description']; ?></td>
                                                                <td class="text-right" onclick="destroy('JobDescriptionController@destroy', <?= $jd['id']; ?>, this)"><i class="fa fa-trash"></i></td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                            
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary wide-button" onclick="changeStep(14)">Add New Job Description</button>
                                        <button type="button" class="btn btn-primary" onclick="changeStep(15)">Next</button>
                                    </div>
                                </form>
                            </div>

                            <!-- jobdesc TABLE -->
                            <div id="step14" class="step">
                                <h1>New Job Description</h1>
                                <form id="formStep14">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content-block">
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="1" type="text" id="jobdesc" name="jobdesc">
                                                    <label class="form-label" for="jobdesc">Job Description</label>
                                                </div>                                           
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary wide-button" onclick="submitStep(14, ['JobDescriptionController@store'], this, true)">Add Another Job Description</button>
                                        <button type="button" class="btn btn-primary"               onclick="submitStep(15, ['JobDescriptionController@store'], this, true)">Next</button>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- employees TABLE -->
                            <div id="step15" class="step">
                                <h1>Add Employee</h1>
                                <form id="formStep15">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content">
                                                <div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="1" value="" type="text" id="first" name="employeefirst" required>
                                                        <label class="form-label" for="first">First Name *</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="2" type="text" value="" id="last" name="employeelast" required>
                                                        <label class="form-label" for="last">Last Name *</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="3" value="" type="text" id="phone" name="employeephone">
                                                        <label class="form-label" for="phone">Phone</label>
                                                    </div>
                                                    <div class="form-group mb-4">
                                                        <select class="select" name="jobdesc" id="jobdesc2" tabindex="4">
                                                            <?php foreach ($job_descriptions as $jd): ?>
                                                                <option value="<?= $jd['job_description']; ?>"><?= $jd['job_description']; ?></option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                        <label class="form-label select-label" for="jobdesc">Position</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="5" type="text" value="" id="password" name="password">
                                                        <label for="password" class="form-label">Password</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="6" id="michigan_mech" name="mechanicnumber">
                                                        <label class="form-label" for="rate">Michigan Mech #</label>
                                                    </div>
                                                    <div class="form-group mb-4" style="position: relative;">
                                                        <select class="select" id="employee_technician" name="showtechlist" tabindex="7">
                                                            <option value="YES">YES</option>
                                                            <option value="NO">NO</option>
                                                        </select>
                                                        <label class="form-label select-label" for="active">
                                                            Is Employee a Technician
                                                        </label>
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Must be marked yes to be assigned to a labor line." style="position:absolute; top:-10px; left:145px; background:white; border-radius:50%"></i>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="form-group mb-4">
                                                        <select class="select" name="Active" id="active" tabindex="8">
                                                            <option value="YES">YES</option>
                                                            <option value="NO">NO</option>
                                                        </select>
                                                        <label class="form-label select-label" for="active">Active</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" value="0" tabindex="9" id="pay_rate" name="hourlyrate">
                                                        <label class="form-label" for="rate">Pay Rate</label>
                                                    </div>
                                                    <div class="form-group mb-4">
                                                        <select name="paytype" class="select" id="paytype" tabindex="10">
                                                            <option value="FLATRATE">Flat Rate</option>
                                                            <option value="HOURLY">Hourly</option>
                                                        </select>
                                                        <label for="paytype" class="form-label select-label">Pay Type</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="11" value="" id="pin" name="pin">
                                                        <label class="form-label select-label">Timeclock PIN Number</label>
                                                    </div>
                                                    <div class="form-outline mb-4" id="datehired_wrapper">
                                                        <input class="form-control form-icon-trailing" tabindex="12" type="text" value="" id="DateHired" name="DateHired" required>
                                                        <label class="form-label" for="DateHired">Date Hired *</label>
                                                    </div>
                                                    <div class="form-group mb-4">
                                                        <select class="select" name="mode" id="mode" tabindex="13">
                                                            <option value="Full">Full</option>
                                                            <option value="Tech2">Tech</option>
                                                        </select>
                                                        <label class="form-label select-label" for="mode">User Mode</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="color" style="height: 32.23px;" tabindex="14" id="color" name="color">
                                                        <label class="form-label" for="schedule_color">Schedule Color</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary" onclick="changeStep(13)">Back</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(16, ['EmployeeController@store'], this)">Next</button>
                                    </div>
                                </form>
                            </div>
                            <!-- employees TABLE -->
                            <div id="step16" class="step">
                                <h1>Employee Permissions</h1>
                                <form id="formStep16">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content">
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="logintosbp" class="form-check-input" value='no'>
                                                    <input type="checkbox" name="logintosbp" value="YES" id="logintosbp" checked class="form-check-input">
                                                    <label class="form-check-label" for="logintosbp">
                                                        Login to Shop Boss Pro
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Allows employee to login to their Shop Boss Account."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="EditInventory" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="EditInventory" value="YES" id="EditInventory" class="form-check-input">
                                                    <label class="form-check-label" for="EditInventory">
                                                        Edit Inventory
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can add/edit employee profiles."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="CompanyAccess" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="CompanyAccess" value="YES" id="CompanyAccess" class="form-check-input">
                                                    <label class="form-check-label" for="CompanyAccess">
                                                        Settings Access
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Has access to system settings."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="ReOpenRO" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="ReOpenRO" value="YES" id="ReOpenRO" class="form-check-input">
                                                    <label class="form-check-label" for="ReOpenRO">
                                                        Re-Open RO
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can re-open a closed repair order for adjustment."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="EmployeeAccess" class="form-check-input" value='no'>
                                                    <input type="checkbox" name="EmployeeAccess" value="YES" id="EmployeeAccess" checked class="form-check-input">
                                                    <label class="form-check-label" for="EmployeeAccess">
                                                        Add/Edit Employees
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can add/edit employee profiles."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="changerodate" class="form-check-input" value='no'>
                                                    <input type="checkbox" name="changerodate" value="YES" id="changerodate" class="form-check-input">
                                                    <label class="form-check-label" for="changerodate">
                                                        Change RO Dates
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can edit Status date in a repair order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="ReportAccess" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="ReportAccess" value="YES" id="ReportAccess" class="form-check-input">
                                                    <label class="form-check-label" for="ReportAccess">
                                                        Report Access
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Allows access to system reports."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="ChangePartMatrix" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="ChangePartMatrix" value="YES" id="ChangePartMatrix" class="form-check-input">
                                                    <label class="form-check-label" for="ChangePartMatrix">
                                                        Change Parts Matrix
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can adjust parts matrix."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="CreateRO" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="CreateRO" value="YES" id="CreateRO" class="form-check-input">
                                                    <label class="form-check-label" for="CreateRO">
                                                        Create RO
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Ability to create a repair order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="ChangePartCodes" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="ChangePartCodes" value="YES" id="ChangePartCodes" class="form-check-input">
                                                    <label class="form-check-label" for="ChangePartCodes">
                                                        Change Parts Codes
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can change Part code descriptions."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="CreateCT" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="CreateCT" value="YES" id="CreateCT" class="form-check-input">
                                                    <label class="form-check-label" for="CreateCT">
                                                        Create Part Sale
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Ability to do a part sale/over the counter sale."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="ChangeJobDescription" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="ChangeJobDescription" value="YES" id="ChangeJobDescription" class="form-check-input">
                                                    <label class="form-check-label" for="ChangeJobDescription">
                                                        Change Job Descriptions
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can create and change employee job descriptions."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="EditSupplier" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="EditSupplier" value="YES" id="EditSupplier" class="form-check-input">
                                                    <label class="form-check-label" for="EditSupplier">
                                                        Edit Supplier
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Ability to add/edit part suppliers."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="ChangeSources" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="ChangeSources" value="YES" id="ChangeSources" class="form-check-input">
                                                    <label class="form-check-label" for="ChangeSources">
                                                        Change Advertising Sources
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can change advertising sources in a repair order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="InventoryLookup" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="InventoryLookup" value="YES" id="InventoryLookup" class="form-check-input">
                                                    <label class="form-check-label" for="InventoryLookup">
                                                        Inventory Lookup
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can search current Inventory."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="ChangeRepairOrderTypes" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="ChangeRepairOrderTypes" value="YES" id="ChangeRepairOrderTypes" class="form-check-input">
                                                    <label class="form-check-label" for="ChangeRepairOrderTypes">
                                                        Change RO Types
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can change RO Types in a repair order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="candelete" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="candelete" value="YES" id="candelete" class="form-check-input">
                                                    <label class="form-check-label" for="candelete">
                                                        Delete Labor and Parts from RO
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can delete labor/parts from a repair order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="sendupdates" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="sendupdates" value="YES" id="sendupdates" class="form-check-input">
                                                    <label class="form-check-label" for="sendupdates">
                                                        Allow Send Updates to Customer?
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can send out a repair order update link to the customer from a repair order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="changeshopnotice" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="changeshopnotice" value="YES" id="changeshopnotice" class="form-check-input">
                                                    <label class="form-check-label" for="changeshopnotice">
                                                        Change Shop Notice
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can update shop notice alerts."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="deletepaymentsreceived" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="deletepaymentsreceived" value="YES" id="deletepaymentsreceived" class="form-check-input">
                                                    <label class="form-check-label" for="deletepaymentsreceived">
                                                        Can Delete Payments?
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Ability to delete a payment in a repair order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="accounting" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="accounting" value="YES" id="accounting" class="form-check-input">
                                                    <label class="form-check-label" for="accounting">
                                                        Accounting Access
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can access accounting."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="deletecustomer" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="deletecustomer" value="YES" id="deletecustomer" class="form-check-input">
                                                    <label class="form-check-label" for="deletecustomer">
                                                        Can Delete Customer?
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Ability to delete a customer profile from the Customer list."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="downloaddata" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="downloaddata" value="YES" id="downloaddata" class="form-check-input">
                                                    <label class="form-check-label" for="downloaddata">
                                                        Download Data
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can download and backup system data."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="editnotifications" class="form-check-input" value='no'>
                                                    <input type="checkbox" name="editnotifications" value="YES" id="editnotifications" class="form-check-input">
                                                    <label class="form-check-label" for="editnotifications">
                                                        Edit Notifications
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Ability to change what notifications the system reports on in settings."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="showgpinro" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="showgpinro" value="YES" id="showgpinro" class="form-check-input">
                                                    <label class="form-check-label" for="showgpinro">
                                                        Show GP in RO
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Gross Profit is visible in a Repair Order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="edittechpaidlog" class="form-check-input" value='no'>
                                                    <input type="checkbox" name="edittechpaidlog" value="YES" id="edittechpaidlog" class="form-check-input">
                                                    <label class="form-check-label" for="edittechpaidlog">
                                                        Edit Tech Paid Log in RO
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Ability to add and adjust technician paid hours in a repair order."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="editcommentsinro" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="editcommentsinro" value="YES" id="editcommentsinro" class="form-check-input">
                                                    <label class="form-check-label" for="editcommentsinro">
                                                        Edit Comments in RO
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Ability to edit Communication log notes."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="DashboardAccess" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="DashboardAccess" value="YES" id="DashboardAccess" class="form-check-input">
                                                    <label class="form-check-label" for="DashboardAccess">
                                                        BOSS Board Access
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Access to the analytics dashboard (Must be apart of your package tier)"></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="IntegrationAccess" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="IntegrationAccess" value="YES" id="IntegrationAccess" class="form-check-input">
                                                    <label class="form-check-label" for="IntegrationAccess">
                                                        Integrations Access
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Access to system integrations."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="changerostatus" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="changerostatus" value="YES" id="changerostatus" class="form-check-input">
                                                    <label class="form-check-label" for="changerostatus">
                                                        Change RO Status
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can change the RO Status in a repair order including marking a ticket closed."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="partsordering" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="partsordering" value="YES" id="partsordering" class="form-check-input">
                                                    <label class="form-check-label" for="partsordering">
                                                        Order Parts Online
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Ability to order parts through parts integrations."></i>
                                                    </label>
                                                </div>
                                                <div class="form-check form-switch mb-4">
                                                    <input type="hidden" name="mergecustomers" class="form-check-input" value='no'>
                                                    <input type="checkbox" checked name="mergecustomers" value="YES" id="mergecustomers" class="form-check-input">
                                                    <label class="form-check-label" for="mergecustomers">
                                                        Merge Customers
                                                        <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Permission to merge two customers" ></i>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-primary" onclick="submitStep(160, ['EmployeeController@update'], this)">Next</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step160" class="step">
                                <h1>Tech Mode Permissions</h1>
                                <form id="formStep160">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content-block">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="editparts" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techeditparts" name="editparts" class="form-check-input" value="YES" checked>
                                                            <label for="techeditparts" class="form-check-label">
                                                                Edit Parts
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technicians can edit part lines in a repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="editlabor" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techeditlabor" name="editlabor" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techeditlabor">
                                                                Edit Labor
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technicians can edit labor lines in a repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="editsublet" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techeditsublet" name="editsublet" class="form-check-input" value="YES" checked>
                                                            <label for="techeditsublet" class="form-check-label">
                                                                Edit Sublet
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technicians can edit sublet lines in a repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="editcannedjobs" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techeditcannedjobs" name="editcannedjobs" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techeditcannedjobs">
                                                                Edit Canned Jobs
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technicians can edit canned jobs in a repair order?"></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showsublet" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techshowsublet" name="showsublet" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techshowsublet">
                                                                Show Sublet
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technicians can see sublet jobs in a repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="onlytechissues" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techonlytechissues" name="onlytechissues" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techonlytechissues">
                                                                Show ONLY Tech Issues
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title=""></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showwtkonwip" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techshowcustonwip" name="showwtkonwip" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techshowcustonwip">
                                                                Show Customer on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technicians can only see labor lines assigned to them."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showwtkonwip" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showwtkonwip" name="showwtkonwip" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="showwtkonwip">
                                                                Show Writer & Technician on WIP
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technician can see the service writer and technician assigned to a repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="viewschedule" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techviewschedule" name="viewschedule" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techviewschedule">
                                                                View Customer Schedule
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technician can view the customer schedule for upcoming appointments."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="editschedule" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techeditschedule" name="editschedule" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techeditschedule">
                                                                Edit Customer Schedule
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technician can edit the customer schedule for scheduled appointments."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="techsupervisor" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techsupervisor" name="techsupervisor" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techsupervisor">
                                                                Tech Supervisor/Shop Foreman
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Allows the technician to see all assigned labor lines."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showpartscostonro" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="showpartscostonro" name="showpartscostonro" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="showpartscostonro">
                                                                Show Parts Cost in RO
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Parts cost will be visible to technicians in a repair order."></i>
                                                            </label>
                                                        </div>
                                                    </div>

                                                    <div class="col-md-6">
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="changevistatus" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techchangevistatus" name="changevistatus" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techchangevistatus">
                                                                Change Vehicle Issue Status
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technician can change vehicle issue status per customer concern – [pending, approved, scheduled, parts ordered, assembly, job complete, and declined]."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="viewhistory" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techviewhistory" name="viewhistory" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techviewhistory">
                                                                View History
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technician can view previous service history performed at the shop on a customer’s vehicle."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="editmilesin" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techeditmilesin" name="editmilesin" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techeditmilesin">
                                                                Edit Miles In
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technician can edit the mileage in field in a repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="editmilesout" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techeditmilesout" name="editmilesout" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techeditmilesout">
                                                                Edit Miles Out
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technician can edit the mileage out field in a repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="vieweditcomments" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techvieweditcomments" name="vieweditcomments" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techvieweditcomments">
                                                                Edit Comments
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technician can edit comments field in a repair order. Can be used in addition to tech notes."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="changerostatus" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techchangerostatus" name="changerostatus" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techchangerostatus">
                                                                Change RO Status
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Can change the RO Status in a repair order including marking a ticket closed."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="showcustomerinfo" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techshowcustomerinfo" name="showcustomerinfo" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techshowcustomerinfo">
                                                                Show Customer Info
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technician can view customer information in a repair order."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="sendinspection" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techsendinspection" name="sendinspection" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techsendinspection">
                                                                Send Inspection to Customer
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technician can send completed inspections to a customer."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="techreport" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="techreport" name="techreport" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="techreport">
                                                                View Tech Production Report
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technician can view Technician Production Report by date range including Hours worked, total labor, and tech pay."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="orderparts" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="orderparts" name="orderparts" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="orderparts">
                                                                Order Parts Online
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technician can access online parts integrations to order parts."></i>
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-switch mb-4">
                                                            <input type="hidden" name="accessworkflow" class="form-check-input" value='no'>
                                                            <input type="checkbox" id="accessworkflow" name="accessworkflow" class="form-check-input" value="YES" checked>
                                                            <label class="form-check-label" for="accessworkflow">
                                                                Workflow Access
                                                                <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Technician can view the Kanban workflow job board in addition to the work in process."></i>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-primary" onclick="submitStep(17, ['TechPermissionsController@store'], this)">Next</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step17" class="step">
                                <h1>Your Staff</h1>
                                <form id="formStep17">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content-block">
                                                <div class="border">
                                                    <section class="container-fluid">
                                                        <table id="datatable_staff" class="sbdatatable w-100">
                                                            <thead>
                                                                <tr>
                                                                    <th>Employee</th>
                                                                    <th>Job Description</th>
                                                                    <th>Phone</th>
                                                                    <th>Mode</th>
                                                                    <th>Active</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <?php foreach ($employees as $employee): ?>
                                                                    <tr>
                                                                        <td><?= $employee['firstname'] . " " . $employee['lastname']; ?></td>
                                                                        <td><?= $employee['job_description']; ?></td>
                                                                        <td><?= $employee['employee_phone']; ?></td>
                                                                        <td><?= $employee['mode']; ?></td>
                                                                        <td><?= $employee['active']; ?></td>
                                                                    </tr>
                                                                <?php endforeach; ?>
                                                            </tbody>
                                                        </table>
                                                    </section>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary btn-wide" onclick="changeStep(15)">Add Another Employee</button>
                                        <button type="button" class="btn btn-primary btn-wide" onclick="changeStep(18)">Done Adding Employees</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step18" class="step complete-intro">
                                <h1>You have completed the <b>Employee</b> section of the setup!</h1>
                                <h4><?= $employee_names; ?> are ready for work.</h4>
                                    
                                <div class="button-wrapper-center">
                                    <button type="button" class="btn btn-secondary btn-wide" onclick="window.location.href='/v2/wip/wip.php'">Save and Customize Later</button>
                                    <button type="button" class="btn btn-primary btn-wide-3" onclick="changeStep(19,'employees',4)">Save and Continue to Suppliers</button>
                                </div>
                            </div>

                            <!-- SUPPLIERS -->
                            <div id="step19" class="step">
                                <h1>Who are your suppliers?</h1>
                                <form id="formStep19">
                                    <div class="form-wrapper">
                                        <div class="form-item">
                                            <div class="fi-content">
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="1" type="text" value="" id="supplier" name="supplierName">
                                                    <label class="form-label">Supplier Name</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="2" type="text" value="" tabindex="8" id="contact" name="supplierContact">
                                                    <label class="form-label">Contact Name</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="3" value="" type="text" id="address" name="supplierAddress">
                                                    <label class="form-label">Address</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="4" value="" type="text" id="city" name="supplierCity">
                                                    <label class="form-label">City</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="5" type="text" value="" id="state" name="supplierState">
                                                    <label class="form-label">State</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="6" type="text" value="" id="zip" name="supplierZip">
                                                    <label class="form-label" id="cityfloatinglabel">Zip/Postal Code</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="7" value="" type="text" id="phone" name="supplierPhone">
                                                    <label class="form-label">Phone</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="8" value="" type="text" id="fax" name="supplierFax">
                                                    <label class="form-label">Fax</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="9" type="text" value="" tabindex="9" id="email" name="supplierEmail">
                                                    <label class="form-label">Email</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="10" type="text" tabindex="10" value="" id="account" name="accountNumber">
                                                    <label class="form-label">Account Number</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="11" type="text" tabindex="11" value="" id="terms" name="terms">
                                                    <label class="form-label">Terms</label>
                                                </div>
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="12" type="text" tabindex="12" value="" id="taxexempt" name="taxexempt">
                                                    <label class="form-label">Tax Exempt</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary btn-wide" onclick="submitStep(19, ['SuppliersController@store'], this, true)">Add Another Supplier</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(20, ['SuppliersController@store'], this, true)">Done</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step20" class="step complete-intro">
                                <h1>You have completed the <b>Supplier</b> section of the setup!</h1>
                                <h4>You are ready to order from <?= $suppliers; ?>.</h4>
                                    
                                <div class="button-wrapper-center">
                                    <button type="button" class="btn btn-secondary btn-wide" onclick="window.location.href='/v2/wip/wip.php'">Save and Customize Later</button>
                                    <button type="button" class="btn btn-primary btn-wide-3" onclick="changeStep(21,'suppliers',5)">Save and Continue to Customize</button>
                                </div>
                            </div>

                            <!-- CUSTOMIZE -->
                            <div id="step21" class="step">
                                <h1>Want it your way?</h1>
                                <form id="formStep21">
                                    <div class="form-wrapper">
                                        <div class="form-item form-item-customizations">
                                            <div class="fi-sidebar">
                                                <h4>Customizations</h4>
                                                <?= getCustomizationsSidebar(0) ?>
                                            </div>
                                            <div class="fi-content">  
                                                <div>
                                                    <h3 class="mb-4">Customer <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Any additional information you want to store about your customers."></i>
                                                    </h3>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="1" type="text" id="customuserfield1" name="customuserfield1"
                                                            value="<?= $company['custom_user_field1']; ?>">
                                                        <label class="form-label" for="customuserfield1">Customer Field 1</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="2" type="text" id="customuserfield2" name="customuserfield2"
                                                            value="<?= $company['custom_user_field2']; ?>">
                                                        <label class="form-label" for="customuserfield2">Customer Field 2</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="3" type="text" id="customuserfield3" name="customuserfield3"
                                                            value="<?= $company['custom_user_field3']; ?>">
                                                        <label class="form-label" for="customuserfield3">Customer Field 3</label>
                                                    </div>
                                                    <h3 class="mb-4">Vehicle <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Any additional information you want to store about vehicles."></i></h3>
                                    
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="4" type="text" id="vehiclefield1label" name="vehiclefield1label"
                                                            value="<?= $company['vehicle_field1_label']; ?>">
                                                        <label class="form-label" for="vehiclefield1label">Vehicle Field 1</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="4" type="text" id="vehiclefield2label" name="vehiclefield2label"
                                                            value="<?= $company['vehicle_field2_label']; ?>">
                                                        <label class="form-label" for="vehiclefield2label">Vehicle Field 2</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="4" type="text" id="vehiclefield3label" name="vehiclefield3label"
                                                            value="<?= $company['vehicle_field3_label']; ?>">
                                                        <label class="form-label" for="veh3">Vehicle Field 3</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="4" type="text" id="vehiclefield4label" name="vehiclefield4label"
                                                            value="<?= $company['vehicle_field4_label']; ?>">
                                                        <label class="form-label" for="vehiclefield4label">Vehicle Field 4</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="4" type="text" id="vehiclefield5label" name="vehiclefield5label"
                                                            value="<?= $company['vehicle_field5_label']; ?>">
                                                        <label class="form-label" for="vehiclefield5label">Vehicle Field 5</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="4" type="text" id="vehiclefield6label" name="vehiclefield6label"
                                                            value="<?= $company['vehicle_field6_label']; ?>">
                                                        <label class="form-label" for="vehiclefield6label">Vehicle Field 6</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="4" type="text" id="vehiclefield7label" name="vehiclefield7label"
                                                            value="<?= $company['vehicle_field7_label']; ?>">
                                                        <label class="form-label" for="vehiclefield7label">Vehicle Field 7</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" tabindex="4" type="text" id="vehiclefield8label" name="vehiclefield8label"
                                                            value="<?= $company['vehicle_field8_label']; ?>">
                                                        <label class="form-label" for="vehiclefield8label">Vehicle Field 8</label>
                                                    </div>
                                                </div> 
                                                <div>
                                                    <h3 class="mb-4">Standard Vehicle
                                                        <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="For non-automotive repair shops, you can change the name of standard vehicle fields to other names such as Serial Number, Engine Number etc."></i>
                                                    </h3>
                                    
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="12" id="year" name="yearlabel"
                                                            value="<?= $vehicle_label['year']; ?>">
                                                        <label class="form-label" for="year">Year</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="12" id="make" name="makelabel"
                                                            value="<?= $vehicle_label['make']; ?>">
                                                        <label class="form-label" for="make">Make</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="12" id="model" name="modellabel"
                                                            value="<?= $vehicle_label['model']; ?>">
                                                        <label class="form-label" for="model">Model</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="12" id="engine" name="enginelabel"
                                                            value="<?= $vehicle_label['engine']; ?>">
                                                        <label class="form-label" for="engine">Engine</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="12" id="cylinder" name="cylinderlabel"
                                                            value="<?= $vehicle_label['cylinder']; ?>">
                                                        <label class="form-label" for="cylinders">Cylinders</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="12" id="trans" name="translabel"
                                                            value="<?= $vehicle_label['transmission']; ?>">
                                                        <label class="form-label" for="trans">Transmission</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="12" id="drive" name="drivelabel"
                                                            value="<?= $vehicle_label['drive']; ?>">
                                                        <label class="form-label" for="drive">Drive Type</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="12" id="vin" name="vinlabel"
                                                            value="<?= $vehicle_label['vin']; ?>">
                                                        <label class="form-label" for="vin">VIN</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="12" id="color" name="colorlabel"
                                                            value="<?= $vehicle_label['color']; ?>">
                                                        <label class="form-label" for="color">Color</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="12" id="license" name="licenselabel"
                                                            value="<?= $vehicle_label['license']; ?>">
                                                        <label class="form-label" for="license">License</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="12" id="state" name="statelabel"
                                                            value="<?= $vehicle_label['state']; ?>">
                                                        <label class="form-label" for="state">License State</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="12" id="fleet" name="fleetlabel"
                                                            value="<?= $vehicle_label['fleet']; ?>">
                                                        <label class="form-label" for="fleet">Fleet Number</label>
                                                    </div>
                                                    <div class="form-outline mb-4">
                                                        <input class="form-control" type="text" tabindex="12" id="currmileage" name="currmileagelabel"
                                                            value="<?= $vehicle_label['current_mileage']; ?>">
                                                        <label class="form-label" for="currmileage">Current Mileage</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-primary" onclick="submitStep(22, ['CompanyController@update','VehicleLabelsController@update'], this)">Next</button>
                                    </div>
                                </form>
                            </div>
                            
                            <div id="step22" class="step">
                                <h1>
                                    Customer Concern Categories
                                    <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Custom categories that can be assigned to customer concerns to track different services a shop performs. Reporting can be run based on these categories to track how many of these services have been performed along with any parts and labor that have been involved."></i>
                                </h1>
                                <form id="formStep22">
                                    <div class="form-wrapper">
                                        <div class="form-item form-item-customizations">
                                            <div class="fi-sidebar">
                                                <h4>Customizations</h4>
                                                <?= getCustomizationsSidebar(1) ?>
                                            </div>
                                            <div class="fi-content-block">
                                                <div class="row">
                                                    <div class="col-md-12 col-sm-12">
                                                        <table class="sbdatatable w-100" id="datatable_customer_concern_categories">
                                                            <thead>
                                                            <tr>
                                                                <th class="">Categories</th>
                                                                <th class="text-right">Delete</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                                <?php foreach ($concerns as $concern): ?>
                                                                    <tr>
                                                                        <td><?= $concern['category']; ?></td>
                                                                        <td class="text-right" onclick="destroy('ComplaintcatsController@destroy', <?= $concern['id']; ?>, this)"><i class="fa fa-trash"></i></td>
                                                                    </tr>
                                                                <?php endforeach; ?>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary btn-wide-2" onclick="changeStep(23)">Add New Category</button>
                                        <button type="button" class="btn btn-primary btn-wide-2" onclick="changeStep(24)">Done with Customer Concerns</button>
                                    </div>                                
                                </form>
                            </div>

                            <div id="step23" class="step">
                                <h1>New Category</h1>
                                <form id="formStep23">
                                    <div class="form-wrapper">
                                        <div class="form-item form-item-customizations">
                                            <div class="fi-sidebar">
                                                <h4>Customizations</h4>
                                                <?= getCustomizationsSidebar(1) ?>
                                            </div>
                                            <div class="fi-content-block">
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="1" type="text" id="conecern_category" name="catname">
                                                    <label class="form-label" for="conecern_category">Category</label>
                                                </div>                                           
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary wide-button" onclick="submitStep(23, ['ComplaintcatsController@store'], this, true)">Add Another Category</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(24, ['ComplaintcatsController@store'], this, true)">Next</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step24" class="step">
                                <h1>
                                    Canned Discounts
                                    <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Custom categories that can be assigned to customer concerns to track different services a shop performs. Reporting can be run based on these categories to track how many of these services have been performed along with any parts and labor that have been involved."></i>
                                </h1>
                                <form id="formStep24">
                                    <div class="form-wrapper">
                                        <div class="form-item form-item-customizations">
                                            <div class="fi-sidebar">
                                                <h4>Customizations</h4>
                                                <?= getCustomizationsSidebar(2) ?>
                                            </div>
                                            <div class="fi-content-block">
                                                <div class="row">
                                                    <div class="col-md-12 col-sm-12">
                                                        <table class="sbdatatable w-100" id="datatable_canned_discounts">
                                                            <thead>
                                                            <tr>
                                                                <th>Name</th>
                                                                <th>Parts Discount</th>
                                                                <th>Labor Discount</th>
                                                                <th class="text-right">Delete</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                                <?php foreach ($canned_discounts as $canned_discount): ?>
                                                                    <tr>
                                                                        <td><?= $canned_discount['name']; ?></td>
                                                                        <td>
                                                                            <?= formatDiscount($canned_discount['parts'], $canned_discount['type'], $canned_discount['parts_max']); ?>
                                                                        </td>
                                                                        <td>
                                                                            <?= formatDiscount($canned_discount['labor'], $canned_discount['type'], $canned_discount['labor_max']); ?>
                                                                        </td>
                                                                        <td class="text-right" onclick="destroy('DiscountReasonsController@destroy', <?= $canned_discount['id']; ?>, this)"><i class="fa fa-trash"></i></td>
                                                                    </tr>
                                                                <?php endforeach; ?>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary btn-wide" onclick="changeStep(25)">Add New Discount</button>
                                        <button type="button" class="btn btn-primary btn-wide" onclick="changeStep(26)">Done Adding Discounts</button>
                                    </div>                                
                                </form>
                            </div>

                            <div id="step25" class="step">
                                <h1>New Canned Discount</h1>
                                <form id="formStep25">
                                    <div class="form-wrapper">
                                        <div class="form-item form-item-customizations">
                                            <div class="fi-sidebar">
                                                <?= getCustomizationsSidebar(2) ?>
                                            </div>
                                            <div class="fi-content-block">
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="form-outline mb-4">
                                                            <input type="text" class="form-control" id="editdiscounttype" name="discountreason">
                                                            <label class="form-label" for="editdiscounttype">Discount Type</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-outline mb-4">
                                                            <input type="text" class="form-control" id="editpartsdiscount" name="parts">
                                                            <label class="form-label" for="editpartsdiscount">Parts Discount Amount</label>
                                                        </div>
                                                        <div class="form-outline mb-4">
                                                            <input type="text" class="form-control" id="editlabordiscount" name="labor">
                                                            <label for="editlabordiscount" class="form-label">Labor Discount Amount</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-outline mb-4">
                                                            <input type="text" class="form-control" id="editpartsdiscountmax" name="partsmax">
                                                            <label class="form-label" for="editpartsdiscountmax">Parts Discount Max ($)</label>
                                                        </div>
                                                        <div class="form-outline mb-4">
                                                            <input type="text" class="form-control" id="editlabordiscountmax" name="labormax">
                                                            <label for="editlabordiscount" class="form-label">Labor Discount Max ($)</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="form-row mb-4">
                                                            <select id="editdiscountcalc" class="select" name="type">
                                                                <option value="percent">Percent</option>
                                                                <option value="dollar">Dollar</option>
                                                            </select>
                                                            <label class="form-label select-label" for="type">Calculation Type</label>
                                                        </div>
                                                    </div>
                                                </div>                                          
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary wide-button" onclick="submitStep(25, ['DiscountReasonsController@store'], this, true)">Add Another Discount</button>
                                        <button type="button" class="btn btn-primary" onclick="submitStep(26, ['DiscountReasonsController@store'], this, true)">Next</button>
                                    </div>
                                </form>
                            </div>

                            <div id="step26" class="step">
                                <h1>
                                    Calendar Settings
                                    <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="You can customize by adding, deleting and editing Calendar Labels."></i>
                                </h1>
                                <form id="formStep26">
                                    <div class="form-wrapper">
                                        <div class="form-item form-item-customizations">
                                            <div class="fi-sidebar">
                                                <h4>Customizations</h4>
                                                <?= getCustomizationsSidebar(3) ?>
                                            </div>
                                            <div class="fi-content-block">
                                                <div class="row mb-4">
                                                    <div class="col">
                                                        <table class="sbdatatable w-100" id="datatable_calendar_settings">
                                                            <thead>
                                                            <tr>
                                                                <th>Calendar Label</th>
                                                                <th class="text-right">Delete</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                                <?php foreach ($calendars as $calendar): ?>
                                                                        <tr>
                                                                            <td><?= $calendar['title']; ?></td>
                                                                            <td class="text-right" onclick="destroy('ColorCodingController@destroy', <?= $calendar['id']; ?>, this)"><i class="fa fa-trash"></i></td>
                                                                        </tr>
                                                                <?php endforeach; ?>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                                <div class="row mb-4">
                                                    <div class="col-md-12">
                                                        <div class="card card-square w-100">
                                                            <div class="card-body">
                                                                <div class="row p-2 border-bottom">
                                                                    <h5 class="d-inline card-title text-primary text-center">Calendar Reminders</h5>
                                                                </div>
                                                                <div class="card-text pt-3 pb-3 pe-3 lh-lg">
                                                                    <div class="row d-flex">
                                                                        <div class="col-md-12 mb-3">
                                                                            <p class="mb-1">When adding an item to the Schedule, you can set the default Reminder value. You can choose to default to No reminder, Text Message reminder, Email reminder or Both reminder. You can change that selection here</p>
                                                                            <select id="schedulesendreminderdefault" name="schedulesendreminderdefault" class="select">
                                                                                <?php
                                                                                $options = ['no' => 'No', 'text' => 'Text Message', 'email' => 'Email', 'both' => 'Both'];
                                                                                $selectedOption = strtolower($company['schedules_end_reminder_default']);
                                                                                
                                                                                foreach ($options as $value => $label) {
                                                                                    $selected = ($selectedOption == $value) ? "selected='selected'" : "";
                                                                                    echo "<option value='$value' $selected>$label</option>";
                                                                                }
                                                                                ?>
                                                                            </select>
                                                                        </div>
                                                                        <div class="col-md-12">
                                                                            <p class="mb-1">You can adjust the time for the automatic reminder that will be sent the day before the appointment here.</p>
                                                                            <select class="select" id="schedulesendremindertime" name="schedulesendremindertime">
                                                                                <?php 
                                                                                    for ($hour = 12; $hour <= 22; $hour++) {
                                                                                        $pstTime = new DateTime("$hour:00", new DateTimeZone('America/Los_Angeles'));
                                                                                        
                                                                                        $shopTime = $pstTime->setTimezone(new DateTimeZone($timezone))->format('g:i A');

                                                                                        if (stripos($shopTime, 'AM') !== false) {
                                                                                            continue;
                                                                                        }

                                                                                        $timeValue = "$hour:00:00";
                                                                                        
                                                                                        $selected = ($timeValue === $company['schedulesendremindertime']) ? 'selected' : '';
                                                                                        
                                                                                        echo "<option value='$timeValue' $selected>$shopTime</option>";
                                                                                    }
                                                                                ?>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row mb-4">
                                                    <div class="col-md-12">
                                                        <div class="card card-square w-100">
                                                            <div class="card-body">
                                                                <div class="row p-2 border-bottom">
                                                                    <h5 class="d-inline card-title text-primary text-center">Max Booking Hours Per Day</h5></div>
                                                                <div class="card-text pt-3 pb-3 pe-3 lh-lg">
                                                                    <div class="row d-flex">
                                                                        <div class="col-md-12">
                                                                            <div class="form-outline mb-4">
                                                                                <input type="text" class="form-control" id="max_booking_hour_1" name="max_booking_hour_1"
                                                                                    value="<?= $shop_booking_hours[1]; ?>">
                                                                                <label for="editlabordiscount" class="form-label">Monday</label>
                                                                            </div>
                                                                            <div class="form-outline mb-4">
                                                                                <input type="text" class="form-control" id="max_booking_hour_2" name="max_booking_hour_2"
                                                                                    value="<?= $shop_booking_hours[2]; ?>">
                                                                                <label for="editlabordiscount" class="form-label">Tuesday</label>
                                                                            </div>
                                                                            <div class="form-outline mb-4">
                                                                                <input type="text" class="form-control" id="max_booking_hour_3" name="max_booking_hour_3"
                                                                                    value="<?= $shop_booking_hours[3]; ?>">
                                                                                <label for="editlabordiscount" class="form-label">Wednesday</label>
                                                                            </div>
                                                                            <div class="form-outline mb-4">
                                                                                <input type="text" class="form-control" id="max_booking_hour_4" name="max_booking_hour_4"
                                                                                    value="<?= $shop_booking_hours[4]; ?>">
                                                                                <label for="editlabordiscount" class="form-label">Thursday</label>
                                                                            </div>
                                                                            <div class="form-outline mb-4">
                                                                                <input type="text" class="form-control" id="max_booking_hour_5" name="max_booking_hour_5"
                                                                                    value="<?= $shop_booking_hours[5]; ?>">
                                                                                <label for="editlabordiscount" class="form-label">Friday</label>
                                                                            </div>
                                                                            <div class="form-outline mb-4">
                                                                                <input type="text" class="form-control" id="max_booking_hour_6" name="max_booking_hour_6"
                                                                                    value="<?= $shop_booking_hours[6]; ?>">
                                                                                <label for="editlabordiscount" class="form-label">Saturday</label>
                                                                            </div>
                                                                            <div class="form-outline mb-4">
                                                                                <input type="text" class="form-control" id="max_booking_hour_0" name="max_booking_hour_0"
                                                                                    value="<?= $shop_booking_hours[0]; ?>">
                                                                                <label for="editlabordiscount" class="form-label">Sunday</label>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary btn-wide" onclick="submitStep(27, ['CompanyController@update', 'ShopMaxHoursController@update_or_store'], this)">Add New Label</button>
                                        <button type="button" class="btn btn-primary btn-wide-3" onclick="submitStep(28, ['CompanyController@update', 'ShopMaxHoursController@update_or_store'], this)">Done Customizing Calendar Settings</button>
                                    </div>                                
                                </form>
                            </div>

                            <!-- colorcoding TABLE -->
                            <div id="step27" class="step">
                                <h1>New Calendar Label</h1>
                                <form id="formStep27">
                                    <div class="form-wrapper">
                                        <div class="form-item form-item-customizations">
                                            <div class="fi-sidebar">
                                                <h4>Customizations</h4>
                                                <?= getCustomizationsSidebar(3) ?>
                                            </div>
                                            <div class="fi-content-block">
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="1" type="text" id="title" name="title">
                                                    <label class="form-label" for="title" style="margin-left: 0px;">Enter Calendar Label (colors will be auto assigned)</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary btn-wide" onclick="submitStep(27, ['ColorCodingController@store'], this, true)">Add Another Label</button>
                                        <button type="button" class="btn btn-primary btn-wide-2" onclick="submitStep(28, ['ColorCodingController@store'], this, true)">Next</button>
                                    </div>                                
                                </form>
                            </div>

    
                            <div id="step28" class="step">
                                <h1>
                                    RO Types
                                    <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="You can customize by adding, deleting and editing our standard RO Types.  You can select the default RO Type (the one that is chosen automatically when you create a new RO) and you can color code them so your Work In Process list can be even more informative."></i>
                                </h1>
                                <form id="formStep28">
                                    <div class="form-wrapper">
                                        <div class="form-item form-item-customizations">
                                            <div class="fi-sidebar">
                                                <h4>Customizations</h4>
                                                <?= getCustomizationsSidebar(4) ?>
                                            </div>
                                            <div class="fi-content-block">
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <table class="sbdatatable w-100" id="datatable_ro_types">
                                                            <thead>
                                                            <tr>
                                                                <th>Color Code & RO Type</th>
                                                                <th>Is Default</th>
                                                                <th class="text-right">Delete</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                                <?php foreach ($ro_types as $ro_type): ?>
                                                                        <tr>
                                                                            <td>
                                                                                <i class="dot align-self-center mx-2 p-2" style="background-color:<?= $ro_type['color_code']; ?>"></i>
                                                                                <span><?= $ro_type['ro_type']; ?></span></td>
                                                                            </td>
                                                                            <td><?= $ro_type['is_default']; ?></td>
                                                                            <td class="text-right" onclick="destroy('RoTypeController@destroy', <?= $ro_type['id']; ?>, this)"><i class="fa fa-trash"></i></td>
                                                                        </tr>
                                                                <?php endforeach; ?>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary btn-wide" onclick="changeStep(29)">Add Another RO Type</button>
                                        <button type="button" class="btn btn-primary btn-wide" onclick="changeStep(30)">Done Adding RO Types</button>
                                    </div>                                
                                </form>
                            </div>

                            <!-- rotype TABLE -->
                            <div id="step29" class="step">
                                <h1>New RO Type</h1>
                                <form id="formStep29">
                                    <div class="form-wrapper">
                                        <div class="form-item form-item-customizations">
                                            <div class="fi-sidebar">
                                                <h4>Customizations</h4>
                                                <?= getCustomizationsSidebar(4) ?>
                                            </div>
                                            <div class="fi-content-block">
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="1" type="text" id="ROType" name="ROType">
                                                    <label for="ROType" class="form-label">Enter New RO Type</label>
                                                </div>
                                                <div class="form-row mb-4">
                                                    <label for="colorcode" class="form-label">Select a Color</label>
                                                    <input class="form-control form-control-color" tabindex="2" type="color" id="colorcode" name="colorcode">
                                                </div>
                                                <div class="form-row mb-4">
                                                    <select class="select" tabindex="3" id="isdefault" name="isdefault">
                                                        <option selected value="No">No</option>
                                                        <option value="Yes">Yes</option>
                                                    </select>
                                                    <label for="isdefault" class="form-label select-label">Is the Default RO Type for new RO's</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary btn-wide" onclick="submitStep(29, ['RoTypeController@store'], this, true)">Add New RO Type</button>
                                        <button type="button" class="btn btn-primary btn-wide" onclick="submitStep(30,['RoTypeController@store'], this, true)">Done Adding RO Types</button>
                                    </div>                                
                                </form>
                            </div>


                            <div id="step30" class="step">
                                <h1>
                                    RO Statuses
                                    <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Ability to add to the current set of status flags that are hard coded in the system. Anything marked between Inspection and Q-Check is an estimate, Final is the invoice stage, and closed is when a payment has been received and you are ready to send it to your reports and accounting. You may add to the current list of status flags to customize your workflow."></i>
                                </h1>
                                <form id="formStep30">
                                    <div class="form-wrapper">
                                        <div class="form-item form-item-customizations">
                                            <div class="fi-sidebar">
                                                <h4>Customizations</h4>
                                                <?= getCustomizationsSidebar(5) ?>
                                            </div>
                                            <div class="fi-content-block">
                                                <table class="sbdatatable w-100" id="datatable_ro_statuses">
                                                    <thead>
                                                    <tr>
                                                        <th class="ps-2">Status</th>
                                                        <th class="text-right">Delete</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($ro_statuses as $ro_status): ?>
                                                            <tr>
                                                                <td>
                                                                    <i class="dot align-self-center mx-2 p-2" style="background-color:<?= $ro_status['color_code']; ?>"></i>
                                                                    <span><?= $ro_status['status']; ?></span></td>
                                                                </td>
                                                                <td class="text-right">
                                                                    <?php if ($ro_status['is_default'] == "no"){ ?>
                                                                    <span onclick="destroy('RoStatusController@destroy', <?= $ro_status['id']; ?>, this)"><i class="fa fa-trash"></i></span>
                                                                    <?php
                                                                    } ?>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary btn-wide" onclick="changeStep(31)">Add New RO Status</button>
                                        <button type="button" class="btn btn-primary btn-wide" onclick="changeStep(32)">Done Adding RO Statuses</button>
                                    </div>                                
                                </form>
                            </div>

                            <div id="step31" class="step">
                                <h1>New RO Status</h1>
                                <form id="formStep31">
                                    <div class="form-wrapper">
                                        <div class="form-item form-item-customizations">
                                            <div class="fi-sidebar">
                                                <h4>Customizations</h4>
                                                <?= getCustomizationsSidebar(5) ?>
                                            </div>
                                            <div class="fi-content-block">
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="1" type="text" id="ro_status" name="status">
                                                    <label for="ro_status" class="form-label">Enter New RO Status</label>
                                                </div>
                                                <div class="form-row mb-4">
                                                    <label for="colorcode" class="form-label">Select a Color</label>
                                                    <input class="form-control form-control-color" tabindex="2" type="color" id="colorcode" name="colorcode">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary btn-wide" onclick="submitStep(31, ['RoStatusController@store'], this, true)">Add Another RO Status</button>
                                        <button type="button" class="btn btn-primary btn-wide" onclick="submitStep(32, ['RoStatusController@store'], this, true)">Done Adding RO Statuses</button>
                                    </div>                                
                                </form>
                            </div>



                            <div id="step32" class="step">
                                <h1>
                                    Part Codes
                                    <i class="fas fa-circle-info" data-mdb-toggle="tooltip" title="Standard list of Part Codes. Feel free to modify them to suit your shop."></i>
                                </h1>
                                <form id="formStep32">
                                    <div class="form-wrapper">
                                        <div class="form-item form-item-customizations">
                                            <div class="fi-sidebar">
                                                <h4>Customizations</h4>
                                                <?= getCustomizationsSidebar(6) ?>
                                            </div>
                                            <div class="fi-content-block">
                                                <table id="datatable_part_codes" class="sbdatatable w-100">
                                                    <thead>
                                                    <tr>
                                                        <th>Part Codes</th>
                                                        <th class="text-right">Delete</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($codes as $code): ?>
                                                            <tr>
                                                                <td>
                                                                    <span><?= $code['codes']; ?></span></td>
                                                                </td>
                                                                <td class="text-right" onclick="destroy('CodesController@destroy', <?= $code['id']; ?>, this)"><i class="fa fa-trash"></i></td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary btn-wide" onclick="changeStep(33)">Add New Part Code</button>
                                        <button type="button" class="btn btn-primary btn-wide" onclick="changeStep(34)">Done Adding Part Codes</button>
                                    </div>                                
                                </form>
                            </div>

                            <div id="step33" class="step">
                                <h1>New Parts Code</h1>
                                <form id="formStep33">
                                    <div class="form-wrapper">
                                        <div class="form-item form-item-customizations">
                                            <div class="fi-sidebar">
                                                <h4>Customizations</h4>
                                                <?= getCustomizationsSidebar(6) ?>
                                            </div>
                                            <div class="fi-content-block">
                                                <div class="form-outline mb-4">
                                                    <input class="form-control" tabindex="1" type="text" id="parts_code" name="codes">
                                                    <label for="parts_code" class="form-label">Enter New Parts Code</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="button-wrapper-center">
                                        <button type="button" class="btn btn-secondary btn-wide" onclick="submitStep(33, ['CodesController@store'], this, true)">Add Another Parts Code</button>
                                        <button type="button" class="btn btn-primary btn-wide" onclick="submitStep(34, ['CodesController@store'], this, true)">Next</button>
                                    </div>                                
                                </form>
                            </div>

                            <div id="step34" class="step complete-intro">
                                <h1>Congratulations, you have completed the New Setup.</h1>
                                <h4>The next step is schedule your onboarding training.</h4>
                                    
                                <div class="button-wrapper-center">
                                    <button type="button" class="btn btn-primary btn-wide-4" onclick="updateOnboardingCompletedStep('OnboardingStepsController@update','customize')">Save And Schedule Onboarding Appointment</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

    </main>


    <link rel="stylesheet" href="https://staging.shopbosspro.com/src/public/js/plugins/dropzonejs/dropzone.min.css">
    <script src="https://staging.shopbosspro.com/src/public/js/plugins/dropzonejs/dropzone.min.js"></script>

<?php
include getScriptsGlobal($component);
include getFooterGlobal($component);
?>
