<!DOCTYPE html>
<html>
<?php
require CONN;

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$shopid = $_COOKIE['shopid'];
$stmt = "select smsnum from smsnumbers where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)){
	$query->execute();
	$query->store_result();
	$rc = $query->num_rows();
	if ($rc > 0){
		$query->bind_result($smsnum);
		$query->fetch();
	}else{
		$smsnum = "";
	}
	$query->close();
}

?>
<head>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400italic,600,700%7COpen+Sans:300,400,400italic,600,700">
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">

<!-- Page JS Plugins CSS -->
<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick.min.css">
<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/slick/slick-theme.min.css">

<!-- Bootstrap and OneUI CSS framework -->
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css">
<link rel="stylesheet" href="<?= CSS ?>/tipped/tipped.css">
<link rel="stylesheet" id="css-main" href="<?= CSS ?>/oneui.css">
<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css">

<link rel="stylesheet" href="<?= CSS ?>/prettyPhoto.css" type="text/css" media="screen">
<style>
.sbpcolumn{
	border:1px gray solid;height:250px;overflow-y:auto;border-radius:5px;margin:0px;
	-webkit-box-shadow: 3px 3px 5px 0px rgba(0,0,0,0.75);
	-moz-box-shadow: 3px 3px 5px 0px rgba(0,0,0,0.75);
	box-shadow: 3px 3px 5px 0px rgba(0,0,0,0.75);
	background-color:white;cursor:pointer
}

.sbpcolumn:hover{
-webkit-box-shadow: 1px 0px 6px 6px rgba(10,128,2,1);
-moz-box-shadow: 1px 0px 6px 6px rgba(10,128,2,1);
box-shadow: 1px 0px 6px 6px rgba(10,128,2,1);
}

.msg{
	border:1px silver solid;
	margin:5px;
	border-radius:3px;
	margin-left:10px;
	padding:2px;
	padding-left:10px;
}
.msgheader{
	border-bottom:1px black solid;
	margin-bottom:10px;
	padding-bottom:5px;
	padding-top:5px;
	cursor:pointer;
}
</style>
</head>

<body style="margin:5px;">

		<div id="smsmodalany" class="modal fade" id="modal-large" tabindex="-1" role="dialog" aria-hidden="true">
			<input id="customerid" type="hidden">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<div class="block block-themed block-transparent remove-margin-b">
						<div class="block-header bg-primary-dark">
							<ul class="block-options">
								<li>
									<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
								</li>
							</ul>
							<h3 class="block-title">Send Text Message</h3>
						</div>
						<div id="vehinfo" class="block-content"></div>
						<div class="block-content">
							<div class="row">
								<div class="col-md-12">

									<p>Here you can send a text message to anyone. If you want to send to an active RO, go to the RO or Manage Customers. To send to a supplier, go to Settings - Suppliers</p>
								<div class="col-sm-12">
										<div class="form-material floating">
											<input class="form-control sbp-form-control" style="padding:20px;" tabindex="1" type="text" value="" id="smscell" name="smscell">
											<label id="smscelllabel" for="material-text2">Cell Number</label>
										</div>
									</div>
									<div class="col-sm-12">
										<div class="form-material floating">
											<textarea class="form-control sbp-form-control" style="padding:20px;text-transform: none;" tabindex="2" id="smsmsg" name="smsmsg"></textarea>
											<label for="material-text2">Message</label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div style="margin-top:20px;" class="modal-footer">
					<button class="btn btn-md btn-warning" type="button" onclick="sendSMSMessage()">Send Message</button>
					<button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
					</div>
				</div>
			</div>
		</div>

	<div class="container-fluid">
		<h4>Live Text Messages (click to send / reply) <?php if ($smsnum != ""){ ?><span onclick="sendSMS()" class="btn btn-primary btn-sm">Send Text Message</span><?php } ?> <span onclick="markAllRead()" class="btn btn-warning btn-sm">Read All</span></h4>
		<ol>
			<li>To start a new 2 way text conversation with a customer, go to their open RO and click their Cell Phone.</li>
			<li>Replies to your text messages will be seen here and on the RO
			screen</li>
			<li>The red LIVE Text button will turn green and animate to alert
			you to a received message</li>
		</ol>
		<div id="results" class="row">
			<?php
			$oldts = date('Y-m-d H:i:s', mktime(0, 0, 0, date("m"), date("d")-1, date("Y")));
			if (isset($_GET['p'])){
				$p = $_GET['p'];
				if(isset($_GET['roid']))
				$filterstr = " and (`from` = '$p' || `roid` = '".$_GET['roid']."')";
				else
				$filterstr = " and `from` = '$p'";
			}
			else{
				$filterstr = "";
			}


			$stmt = "select `from`,roid,min(markread) AS markread,max(ts) AS mts from sms where `from` REGEXP ('[0-9]') and shopid = '$shopid' and dismiss = 'no'$filterstr group by `from`,`roid` order by mts desc";
			//echo $stmt;
			//if ($shopid == "6062"){echo $stmt."<BR>";}
			$result = $conn->query($stmt);
			//$c = 1;
			while($row = $result->fetch_array()) {
				$cp = formatPhone(str_replace("+1","",$row['from']));
				$scp = str_replace("+1","",$row['from']);
				$roid = $row['roid'];
				$cstmt = "select customer,vehinfo,roid,totalro,status from repairorders where roid = $roid and shopid = '$shopid' order by roid asc";
				//echo $cstmt;
				//if ($shopid == "6062"){echo $cstmt."<BR>";}
				if ($cquery = $conn->prepare($cstmt)){
				    $cquery->execute();
				    $cquery->store_result();
				    $num_roid_rows = $cquery->num_rows;
				    if ($num_roid_rows > 0){
				    	$cquery->bind_result($cname,$veh,$roid,$totalro,$status);
				    	$cquery->fetch();
				    }else{
				    	$cname = ""; $veh = ""; $roid = "none"; $totalro = 0;
				    }
				    $cquery->close();


				}else{
					echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}
				if ($roid != "none"){

					if (is_numeric(substr($status,0,1))){
						$status = substr($status,1);
					}
					switch(strtoupper($status)){
						case "INSPECTION";
							$flagcolor = "yellow";
							break;
						case "APPROVAL";
							$flagcolor = "orange";
							break;
						case "PARTS";
							$flagcolor = "red";
							break;
						case "ASSEMBLY";
							$flagcolor = "green";
							break;
						case "Q-CHECK";
							$flagcolor = "pink";
							break;
						case "FINAL";
							$flagcolor = "blue";
							break;
						case "CLOSED";
							$flagcolor = "blue";
							break;
					}
					$veh = substr($veh,0,15);
					echo '<div class="col-md-4 sbpcolumn">';
					echo "<div class='msgheader'><i onclick=\"dismiss('$scp')\"  class='fa fa-times-circle' style='color:red;float:right;font-size:16pt;position:absolute;top:3px;right:3px;cursor:pointer'></i><b>RO #$roid</b> / <b>$status <i style='color:$flagcolor;text-shadow:-1px -1px 0 #000,1px -1px 0 #000,-1px 1px 0 #000,1px 1px 0 #000;' class='fa fa-flag fa-md'></i>";

					if($row['markread']=='yes')echo "  <i data-from='".$row['from']."' data-roid='$roid' style='margin-left:15px;' class='fa fa-envelope-open readicon' title='Message is Read'></i>";else echo "  <i data-from='".$row['from']."' data-roid='$roid' style='margin-left:15px;' class='fa fa-envelope unreadicon' title='Message is Unread'></i>"; 

					echo "<span style='float:right;margin-right:10px;'>Total: $".number_format($totalro,2)."</span><br>$cname - $veh - <span style='float:right'>Cell: $cp</b></span></div>";
					echo "<table class=\"table table-condensed table-striped\">";
				}else{
					echo '<div class="col-md-4 sbpcolumn">';
					echo "<div class='msgheader'><i onclick=\"dismiss('$scp')\"  class='fa fa-times-circle' style='color:red;float:right;font-size:16pt;position:relative;top:3px;right:3px;cursor:pointer'></i><b>ORPHAN TEXT, NO RO";
					if($row['markread']=='yes')echo "  <i data-from='".$row['from']."' data-roid='0' class='fa fa-envelope-open readicon' title='Message is Read'></i>  ";else echo "  <i data-from='".$row['from']."' data-roid='0' class='fa fa-envelope unreadicon' title='Message is Unread'></i>  "; 
					echo " - ".formatPhone($row['from'])."</b></div>";
					echo "<table style='cursor:pointer' class=\"table table-condensed table-striped\">";
				}

				// now get all of the conversation for this number
				$usr = $_COOKIE['usr'];
				$astmt = "select `from`,msg,name,markread,ts,roid,id from sms where roid = '$roid' and shopid = '$shopid' and `from` = '".$row['from']."' order by ts desc";
				//echo $astmt;
			    $aresult = $conn->query($astmt);
			    $cntr = 1;
			    while ($arow = $aresult->fetch_array()){
			    	if ($arow['markread'] == "no"){
			    		$styleline = " style='width:25%;font-weight:bold ";
			    		if ($cntr == 1){
			    			$styleline2 = " style='width:75%;background-color:#FFC4C4;color:black;font-weight:bold;font-size:9pt ' ";
			    		}else{
			    			$styleline2 = " style='width:75%;background-color:#FFC4C4;color:black;font-size:9pt ' ";
			    		}

					}else{
						$styleline = " style='width:25% ";
			    		if ($cntr == 1){
			    			$styleline2 = " style='width:75%;color:black;font-weight:bold;font-size:9pt ' ";
			    		}else{
			    			$styleline2 = " style='width:75%;color:black;font-size:9pt ' ";
			    		}

					}

					if ($arow['name'] != $usr){
						if ($styleline == ""){
							$styleline = " style='color:red;font-size:8pt ' ";
						}else{
							$styleline .= ";color:red;font-size:8pt '";
						}
					}else{
						if ($styleline == ""){
							$styleline = " style='color:blue;font-size:8pt ' ";
						}else{
							$styleline .= ";color:blue;font-size:8pt '";
						}

					}
					$cntr = $cntr + 1;
					//echo $roid."<BR>";
					if ($roid != 'none' and $roid != 0){
						$dname = strtoupper($arow['name']);
						$dar = explode(" ",$dname);
						$displayname = $dar[0];
						$onclick = "openSMS('".$row['from']."','".$roid."')";
					}else{
						$displayname = strtoupper($arow['name']);
						$onclick = "openSMS('".$row['from']."','0')";
					}

					if ($roid != "none"){

					}
			?>
					<tr>
						<td onclick="viewLine(<?php echo strtoupper($arow['id']); ?>)"><img style="max-height:20px;" alt="" src="<?= IMAGE ?>/magglass.png"></td>
						<td onclick="<?php echo $onclick; ?>" <?php echo $styleline; ?>><?php echo $displayname; ?><div style="font-size:7pt;font-weight:normal;"><?php echo date("m/d/Y h:i:s a",strtotime($arow['ts'])); ?></div></td>
						<td id="msgtd<?php echo strtoupper($arow['id']); ?>" onclick="<?php echo $onclick; ?>" <?php echo $styleline2; ?> ><?php echo $arow['msg']; ?></td>
					</tr>
			<?php
				}
			?>
				</table>


			</div>
			<?php
			}
			?>



	</div>
<?php

// get last record
$stmt = "select id from sms where shopid = '$shopid' order by id desc limit 1";
if ($query = $conn->prepare($stmt)){
    $query->execute();
    $query->bind_result($lastid);
    $query->fetch();
    $query->close();
}else{
	$lastid = 0;
}

echo "<input type='hidden' id='lastid' value='$lastid'>";
?>


<div id="smsmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
	<input type="hidden" id="cellnumber">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="block block-themed block-transparent remove-margin-b">
				<div class="block-header bg-primary-dark">
					<ul class="block-options">
						<li>
							<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
						</li>
					</ul>
					<h3 id="tctitle" class="block-title">Send a Text Message</h3>
				</div>
				<div class="block-content">
					<div class="row">
						<div class="col-md-12">
							<div style="margin-bottom:20px;" class="col-md-12">
								<div class="form-material floating">
									<textarea class="form-control sbp-form-control" style="padding:20px;text-transform:none" tabindex="1" id="textmessage" name="textmessage"></textarea>
									<label for="editponumber">Enter a Text Message to your Customer</label>
								</div>
							</div>
							<div style="margin-bottom:20px;" class="col-md-12">
								<div class="form-material floating">
									<input type="text" class="form-control sbp-form-control" style="padding:20px;" tabindex="1" id="roid" name="roid">
									<label id="roidlabel" for="roid">RO Number (override if necessary)</label>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div style="margin-top:20px;" class="modal-footer">
			<button class="btn btn-md btn-primary" type="button" onclick="saveSMS()">Send Message</button>
			<button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>

<div id="viewmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
	<input type="hidden" id="cellnumber">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="block block-themed block-transparent remove-margin-b">
				<div class="block-header bg-primary-dark">
					<ul class="block-options">
						<li>
							<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
						</li>
					</ul>
					<h3 id="tctitle" class="block-title">View Line - You cannot edit, only view</h3>
				</div>
				<div class="block-content">
					<div class="row">
						<div class="col-md-12">
							<div style="margin-bottom:20px;" class="col-md-12">
								<div class="form-material floating">
									<textarea class="form-control sbp-form-control" style="padding:20px;height:250px" tabindex="1" id="textmsg" name="textmsg"></textarea>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div style="margin-top:20px;" class="modal-footer">
			<button class="btn btn-md btn-default" type="button" data-dismiss="modal">Close</button>
			</div>
		</div>
	</div>
</div>


<script src="<?= SCRIPT ?>/core/jquery.min.js"></script>
<script src="<?= SCRIPT ?>/tipped.js"></script>
<script src="<?= SCRIPT ?>/core/bootstrap.min.js"></script>

<!-- OneUI Core JS: jQuery, Bootstrap, slimScroll, scrollLock, Appear, CountTo, Placeholder, Cookie and App.js -->
<script src="<?= SCRIPT ?>/core/jquery.slimscroll.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.scrollLock.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.appear.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.countTo.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.placeholder.min.js"></script>
<script src="<?= SCRIPT ?>/core/js.cookie.min.js"></script>
<script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
<script src="<?= SCRIPT ?>/app.js"></script>
<script src="<?= SCRIPT ?>/jquery.floatThead.js"></script>
<script type="text/javascript" src="<?= SCRIPT ?>/jquery.prettyPhoto.js"></script>

<script>

function viewLine(id){

	msg = $('#msgtd'+id).text();
	$('#textmsg').val(msg)
	$('#viewmodal').modal('show')

}

function sendSMS(){

	$('#smsmodalany').modal('show')
	adjustLabel("smscelllabel")
	setTimeout(function(){
		$('#smscell').focus()
	},500)

}

function sendSMSMessage(){

	to = $('#smscell').val();
	msg = encodeURIComponent($('#smsmsg').val())

	$.ajax({
		data: "sendsms=save&from=<?php echo $smsnum; ?>&to="+to+"&msg="+msg,
		type: "post",
		url:  "<?php echo INTEGRATIONS; ?>/bandwidth/sendsmsv2.php",
		success: function(r){
			console.log(r)
			if (r == "success"){
				$('#smsmodalany').modal('hide')
				swal("Message sent successfully")
				setTimeout(function(){
					location.reload();
				},1000)
			}
		},
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		}
	})

}

function adjustLabel(labelname){

	// addmakelabel
	$('#'+labelname).css("-webkit-transform","translateY(-24px)").css("-ms-transform","translateY(-24px)").css("transform","translateY(-24px)").css("font-size","small").css("font-color","gray").css("font-weight","bold")


}


function markRead(f,bgcolor){

		// mark all for this number as read
		$.ajax({
			data: "t=markread&shopid=<?php echo $shopid; ?>&cell="+f,
			url: "<?= COMPONENTS_PRIVATE ?>/shared/smsliveaction.php",
			type: "post",
			success: function(r){
				//console.log(r)
				refreshList()
			}
		});
}


function markAllRead(){

	swal({
		title: "Are you sure?",
		text: "This will mark all the chats as read for all the employees",
		type: "warning",
		showCancelButton: true,
		confirmButtonClass: "btn-danger",
		confirmButtonText: "Yes",
		closeOnConfirm: true
	},
	function(){

		$.ajax({
			data: "t=markallread&shopid=<?php echo $shopid; ?>",
			url: "<?= COMPONENTS_PRIVATE ?>/shared/smsliveaction.php",
			type: "post",
			success: function(r){
				refreshList()
			}
		});
	});
}

function openSMS(cell,roid){

	$('#smsmodal').modal('show')
	setTimeout(function(){$('#textmessage').focus()},500);
	$('#cellnumber').val(cell)
	$('#roid').val(roid)
	$('#roidlabel').css("-webkit-transform","translateY(-24px)").css("transform","translateY(-24px)").css("-ms-transform","translateY(-24px)")
}

function formatPhone(p){

	console.log(p.length)
	if (p.length == 10){
		return "(" + p.substring(0,3) + ") " + p.substring(3,6) + "-" + p.substring(6,10)
	}else{
		return p
	}

}

function dismiss(p){

	swal({
		title: "Are you sure?",
		text: "This will dismiss all text messages from \r\n"+formatPhone(p)+".\r\n\r\nIf the customer sends a new text message it will be displayed here",
		type: "warning",
		showCancelButton: true,
		confirmButtonClass: "btn-danger",
		confirmButtonText: "Yes, Dismiss Them",
		closeOnConfirm: true
	},
	function(){
		$.ajax({
			data: "shopid=<?php echo $shopid; ?>&t=dismiss&p="+p,
			url: "<?= COMPONENTS_PRIVATE ?>/shared/smsliveaction.php",
			type: "post",
			error: function (xhr, ajaxOptions, thrownError) {
				console.log(xhr.status);
				console.log(xhr.responseText);
				console.log(thrownError);
			},

			success: function(r){
				if (r == "success"){
					refreshList()
				}
			}
		});
	});

}

function saveSMS(){

	cell = $('#cellnumber').val()
	sms = $('#textmessage').val()
	roid = $('#roid').val()
	ds = "roid="+roid+"&shopid=<?php echo $shopid; ?>&t=sendsms&cell="+cell+"&sms="+sms
	console.log(ds)
	$.ajax({
		data: ds,
		url: "<?= COMPONENTS_PRIVATE ?>/shared/smsliveaction.php",
		success: function(r){
			if (r == "success"){
				$('#smsmodal').modal('hide')
				refreshList()
			}
		},
		type: "post",
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		},

	});


}

function refreshList(){
	<?php
	if (isset($_GET['p'])){
		$p = $_GET['p'];
		$pfilterstr = "&p=$p";
	}else{
		$pfilterstr = "";
	}
	?>
	$.ajax({
		data: "t=getsms&shopid=<?php echo $shopid.$pfilterstr; ?>",
		url: "<?= COMPONENTS_PRIVATE ?>/shared/smsliveaction.php",
		success: function(r){
			$('#results').html(r)
		},
		type: "post",
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		},

	});


}


setInterval(function(){
	console.log("refresh")
	r = Math.random()
	$.ajax({
		data: "t=getsms&shopid=<?php echo $shopid; ?>&r="+r,
		url: "<?= COMPONENTS_PRIVATE ?>/shared/smsliveaction.php",
		success: function(r){
			$('#results').html(r)
		},
		type: "post",
		error: function (xhr, ajaxOptions, thrownError) {
			console.log(xhr.status);
			console.log(xhr.responseText);
			console.log(thrownError);
		},

	});


},30000);

$(document).ready(function(){

	$('#results').on('click','.sbpcolumn .readicon',function(){

		var from = $(this).attr('data-from')
		var roid = $(this).attr('data-roid')
		$.ajax({
			data: "t=markunread&shopid=<?php echo $shopid; ?>&roid="+roid+"&cell="+from,
			url: "<?= COMPONENTS_PRIVATE ?>/shared/smsliveaction.php",
			type: "post",
			success: function(r){
				refreshList()
			}
		});
	})

	$('#results').on('click','.sbpcolumn .unreadicon',function(){

		var from = $(this).attr('data-from')
		var roid = $(this).attr('data-roid')
		$.ajax({
			data: "t=markread&shopid=<?php echo $shopid; ?>&roid="+roid+"&cell="+from,
			url: "<?= COMPONENTS_PRIVATE ?>/shared/smsliveaction.php",
			type: "post",
			success: function(r){
				refreshList()
			}
		});
	})
})
</script>

</body>
<?php if(isset($conn)){mysqli_close($conn);} ?>
</html>
