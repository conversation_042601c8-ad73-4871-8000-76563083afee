<?php
$component = "settings-v2";
$sub_component = "Miscellaneous";
$shopid = $_COOKIE['shopid'];

include getRulesComponent($component);
include getHeadGlobal($component);


$stmt = "select feesonquote,firstlastonwip,updateinvonadd,showpromiseonwip,UsePartsMatrix,showtimeclockonwipdata,showpaymentonwip,showemailestimateonwip,showemailinvoiceonwip,showinspectiononwip,showgp,requiretechclockout,"
    . "showpartscostonro,overwriteqbtrans,inventoryappreciation,useim,useschmaint,timezone,autoshowta,showtextemail,logo,scrolltotalswindow,masterinterface,showcarfaxonly,showinstockparts,showtechoverhours,"
    . "merchantaccount,defaultinspectionvalue,nexpartusername showpics,nexpartpassword as showcommlog,calendardefault,gponwiplist,showwtk,showvehcolor,showhrsonwip,showstatsonwip,showsigonwip, conamereports, shopmgr from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($feesonquote, $firstlastonwip, $updateinvonadd, $showpromiseonwip, $i1, $i2, $i3, $i4, $i5, $i6, $i7, $i9, $i10, $i11, $i12, $i13, $i14, $i15, $i16, $i17, $logo, $scrolltotalswindow, $masterinterface, $showcarfax, $showinstockparts, $showtechoverhours, $merchantaccount, $defaultinspectionvalue, $showpics, $showcommlog, $calendardefault, $gponwiplist, $showwtk, $showvehcolor, $showhrsonwip, $showstatsonwip, $showsigonwip, $conamereports, $showelapsed);
    $query->fetch();
    $query->close();
} else {
    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$stmt = "select sendprequal,sortwipbysa,showcfp,360popup,notesalert,ccshop,paymentonfinal,sourceoptimized,commoldtonew,requirerevapp,showgponro, updateinvprices, collapseissues, showphotos, showpartfeeaspart, showtaxonpo, aiActive from settings where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $query->bind_result($sendprequal, $sortwipbysa, $showcfp, $popup360, $notesalert, $ccshop, $paymentonfinal, $sourceoptimized, $commoldtonew, $requirerevapp, $showgponro, $updateinvprices, $collapseissues, $showPhotos, $showpartfeeaspart, $showTaxOnPO, $aiActive);
    $query->fetch();
    $query->close();
}

$remotecc = "na";
$merchantaccount = "no";
if ($merchantaccount == "authorize.net" || $merchantaccount == "cardknox") {
    $stmt = "select count(*) c from companysettings where shopid = '$shopid' and setting = 'remotecc'";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($remotecount);
        $query->fetch();
        $query->close();
    }
    if ($remotecount == 0) {
        $remotecc = "no";
    } else {
        $remotecc = "yes";
    }
} else {
    $remotecc = "na";
}

$partstechlistprice = '';

$stmt = "select uselistprice from partstechshops where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->store_result();
    $numrows = $query->num_rows();
    if ($numrows > 0) {
        $query->bind_result($partstechlistprice);
        $query->fetch();
    }
}

$betaFeatures = [];
$stmt = "SELECT feature_id FROM beta_features WHERE shopid = ?";
if($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_assoc())
    $betaFeatures[] = $rs['feature_id'];
    $query->close();
}

$aiBetaFeatureActive = is_array($betaFeatures) && in_array('1', $betaFeatures) 
    ? true
    : false;

?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);

?>
<main id="settings" class="min-vh-100">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-12 col-sm-12">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/settings/settings.php"
                           class="text-secondary">Settings</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <span class="text-secondary">Custom Settings</span>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">Miscellaneous Settings</h2>
                    </div>
                    <hr/>
                </div>
            </div>
        </div>
    </div>
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="shopmgr" class="form-check-input"
                       onchange="save(this.id)" <?php if (strtolower($showelapsed) == 'yes') {
                    echo 'checked';
                } ?>>
                <label class="form-check-label" for="shopmgr">Show Elapsed Time on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Tracks how long a repair order has been opened to the point that it is closed." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showtimeclockonwipdata"
                       onchange="save(this.id)" <?php if (strtolower($i2) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showtimeclockonwipdata">Show Labor Timeclock on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Displays how much time a technician has spent clocked into labor lines on a repair order." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showpaymentonwip"
                       onchange="save(this.id)" <?php if (strtolower($i3) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showpaymentonwip">Show Payments Received on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Creates a visual Indicator to display when a payment has been made on a repair order." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showemailestimateonwip"
                       onchange="save(this.id)" <?php if (strtolower($i4) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showemailestimateonwip">Show Estimates Emailed on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Creates a visual indicator when an estimate has been emailed to the customer." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showemailinvoiceonwip"
                       onchange="save(this.id)" <?php if (strtolower($i5) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showemailinvoiceonwip">Show Invoices Emailed on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Creates a visual indicator to when an invoice has been emailed to the customer." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showtextemail"
                       onchange="save(this.id)" <?php if (strtolower($i17) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showtextemail">Show Text/Email Sent on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Creates a visual indicator when a text/email has been sent to the customer." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showinspectiononwip"
                       onchange="save(this.id)" <?php if (strtolower($i6) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showinspectiononwip">Show Completed Inspections on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Creates a visual indicator when an inspection is marked complete." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="nexpartpassword"
                       onchange="save(this.id)" <?php if (strtolower($showcommlog) == 'showcommlogyes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="nexpartpassword">Show Communication Log on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Displays communication log on work in progress in addition to the repair order. " ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="gponwiplist"
                       onchange="save(this.id)" <?php if (strtolower($gponwiplist) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="gponwiplist">Show GP on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Shows Gross Profit calculations on the work in progress." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="autoshowta" onchange="save(this.id)" <?php if (strtolower($i16) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="autoshowta">Show Tech Activities on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Displays all activity done by the tech as an alert on the work in progress." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showwtk" onchange="save(this.id)" <?php if (strtolower($showwtk) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showwtk">Show W+TK on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Displays who the service writer and technician is assigned to the repair order. " ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showvehcolor"
                       onchange="save(this.id)" <?php if (strtolower($showvehcolor) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showvehcolor">Show Vehicle Color on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Displays the vehicle color as a field on the work in progress for tracking purposes." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showhrsonwip"
                       onchange="save(this.id)" <?php if (strtolower($showhrsonwip) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showhrsonwip">Show Labor Hours on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Indicates how many labor hours are assigned to the repair order." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showstatsonwip"
                       onchange="save(this.id)" <?php if (strtolower($showstatsonwip) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showstatsonwip">Show Stats on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Displays current month key performance indicators along with profit per hour Net Variance if enabled on the Work in progress." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showsigonwip"
                       onchange="save(this.id)" <?php if (strtolower($showsigonwip) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showsigonwip">Show Signature on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Indicates when a repair order has been signed by the customer." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showpromiseonwip"
                       onchange="save(this.id)" <?php if (strtolower($showpromiseonwip) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showpromiseonwip">Show Promise Date/Time on WIP</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title=" Displays promised time of completion on the work in progress." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="firstlastonwip"
                       onchange="save(this.id)" <?php if (strtolower($firstlastonwip) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="firstlastonwip">Show Customer as First Last on WIP?</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Displays Customer name as First, Last instead of Last First." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="sortwipbysa"
                       onchange="save(this.id,'settings')" <?php if (strtolower($sortwipbysa) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="sortwipbysa">Sort WIP By Service Writer/Advisor</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Allows the work in process to be filtered by Service advisor to only display jobs they are working on." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="nexpartusername"
                       onchange="save(this.id)" <?php if (strtolower($showpics) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="nexpartusername">Show Pics Uploaded to RO</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Visual indicator that shows when a picture is attached to a repair ticket. " ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showgp" onchange="save(this.id)" <?php if (strtolower($i7) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showgp">Calculate Gross Profit on RO's</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Calculates gross profit from repair order. Info will be displayed in reports." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showgponro"
                       onchange="save(this.id,'settings')" <?php if (strtolower($showgponro) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showgponro">Show GP on RO</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Displays the Gross profit in the repair order once calculated." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showpartscostonro"
                       onchange="save(this.id)" <?php if (strtolower($i10) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showpartscostonro">Show Parts Cost on RO Screen</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Part cost will display on the part line in a repair order for the shop to see. Employee permissions can prevent technicians from seeing this. All full mode users will be able to see part cost if turned on. " ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="sourceoptimized"
                       onchange="save(this.id,'settings')" <?php if (strtolower($f) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="sourceoptimized">Source Optimized</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Default setting for Source type to help track marketing ROI. Will display last selected source type as a default." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showinstockparts"
                       onchange="save(this.id)" <?php if (strtolower($showinstockparts) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showinstockparts">Show In Stock Parts on RO</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Displays on-hand amount of inventory in repair order." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="updateinvonadd"
                       onchange="save(this.id)" <?php if (strtolower($updateinvonadd) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="updateinvonadd">Update Inventory When Saving a Part on an
                    RO</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Deducts/adds to the on-hand amount of your inventory when a part is added or removed from a concern line in a repair order." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="updateinvprices"
                       onchange="save(this.id,'settings')" <?php if (strtolower($updateinvprices) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="updateinvprices">Update Inventory Prices When Saving a Part on an RO</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title=" Current inventory prices will adjust to reflect new part costs. If the part costs more, it will increase the cost and selling price to factor. If the part costs less, it will decrease the cost and selling price to factor." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="collapseissues"
                       onchange="save(this.id,'settings')" <?php if (strtolower($collapseissues) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="collapseissues">Auto Collapse All Vehicle Issues</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Collapses customer concern categories to hide parts and labor. Helpful for repair orders with multiple concern lines. " ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="ccshop"
                       onchange="save(this.id,'settings')" <?php if (strtolower($ccshop) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="ccshop">Auto CC Company Email in all RO Email
                    Communications</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Forwards all email communication to company email. This ensures a record of the email bodies for reference." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="scrolltotalswindow"
                       onchange="save(this.id)" <?php if (strtolower($scrolltotalswindow) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="scrolltotalswindow">Allow Totals Box to Scroll</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Allows totals section in the repair order to move up and down on the page as you scroll instead of being fixed in place." ></i>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="conamereports" class="form-check-input"
                       onchange="save(this.id)" <?php if (strtolower($conamereports) == 'yes') {
                    echo 'checked';
                } ?>><label for="conamereports" class="form-check-label">Company Name on Reports</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Displays Company name on all reports." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="usepartsmatrix"
                       onchange="save(this.id)" <?php if (strtolower($i1) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label for="usepartsmatrix" class="form-check-label">Use Parts Matrix</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Turns on the parts matrix as the default setting for marking up parts. Will need to override the matrix in a repair order to adjust selling price if turned on." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="requiretechclockout"
                       onchange="save(this.id)" <?php if (strtolower($i9) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="requiretechclockout">Require Techs to clock out for clocking
                    in</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Sends an alert to the technicians as a reminder to clock out at the end of the day." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="360popup"
                       onchange="save(this.id,'settings')" <?php if (strtolower($popup360) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="360popup">360 Pop-Up Display</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Enables 360 payments pop up alerts to display." ></i>
            </div>
            <?php if (strtolower($showcfp) == 'yes') { ?>
                <div class="form-check form-switch mb-4">
                    <input type="checkbox" id="sendprequal"
                           onchange="save(this.id,'settings')" <?php if (strtolower($sendprequal) == 'yes') {
                        echo 'checked';
                    } ?> class="form-check-input">
                    <label class="form-check-label" for="sendprequal">Send CFP Pre-Qual Link in All Emails</label>
                    <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="If equipped with 360 Payments, the system will send out consumer financing Pre-qualification links with all emails sent from the repair order." ></i>
                </div>
            <?php } ?>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="overwriteqbtrans"
                       onchange="save(this.id)" <?php if (strtolower($i11) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="overwriteqbtrans">Allow Duplicate Transactions in QB</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Allows multiple transactions to transfer over to quick books instead of one at a time." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="inventoryappreciation"
                       onchange="save(this.id)" <?php if (strtolower($i12) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="inventoryappreciation">Use Inventory
                    Appreciation/Depreciation</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Tracks if inventory has increased or decreased in value due to current costs based on purchase cost." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="useim" onchange="save(this.id)" <?php if (strtolower($i13) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="useim">Use Internal Instant Messenger</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Messaging platform that can be used to message any employee that has an employee profile in the account. " ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="useschmaint" onchange="save(this.id)" <?php if (strtolower($i14) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="useschmaint">Enable Scheduled Maintenance Lookup</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Allows you to check/add factory OEM scheduled maintenance based on warranty periods for the vehicle. Pulls information based on proper vin decode or license plate decode through Carfax." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showtechoverhours"
                       onchange="save(this.id)" <?php if (strtolower($showtechoverhours) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showtechoverhours">Show Tech Clock Hours Exceed Sold Hours</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Indicates when the technician has been clocked in on a labor line longer than it called for." ></i>
            </div>
            <?php
            if ($remotecc != "na") {
                ?>
                <div class="form-check form-switch mb-4">
                    <input type="checkbox" id="remotecc"
                           onchange="save(this.id)" <?php if (strtolower($remotecc) == 'yes') {
                        echo 'checked';
                    } ?> class="form-check-input">
                    <label class="form-check-label" for="remotecc">Enable Remote CC Payments</label>
                </div>
                <?php
            }
            ?>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="feesonquote"
                       onchange="save(this.id)" <?php if (strtolower($feesonquote) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="feesonquote">Show Fees on Quote?</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Indicates estimated fees to provide a more accurate quote." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="requirerevapp"
                       onchange="save(this.id,'settings')" <?php if (strtolower($requirerevapp) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="requirerevapp">Make Revision Approval Method Required?</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Requires a new signature capture for all revisions." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="commoldtonew"
                       onchange="save(this.id,'settings')" <?php if (strtolower($commoldtonew) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="commoldtonew">Comm. Log Display Order Oldest to Newest?</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="The communication log will display the oldest communication to the most current. " ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="paymentonfinal"
                       onchange="save(this.id,'settings')" <?php if (strtolower($paymentonfinal) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="paymentonfinal">Send Update with Make Payment only on Final
                    Status?</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Payment links will only generate to send during an update as an invoice not as an estimate." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="notesalert"
                       onchange="save(this.id,'settings')" <?php if (strtolower($notesalert) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="notesalert">Customer Note Alert</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Creates a note field in the customer’s profile to store notes on that customer. These notes will pop up an alert display the next time you do a service for that customer." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showphotos"
                       onchange="save(this.id,'settings')" <?php if (strtolower($showPhotos) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showphotos">Show Employee photo and Company logo in menu</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Displays the employees’ photo and the company logo on the work in progress for that employee’s login. Photo must be added to an employees’ profile for it to display. " ></i>
            </div>
            <?php if (!empty($partstechlistprice)) { ?>
                <div class="form-check form-switch mb-4">
                    <input type="checkbox" id="uselistprice"
                           onchange="save(this.id,'partstechshops')" <?php if (strtolower($partstechlistprice) == 'yes') {
                        echo 'checked';
                    } ?> class="form-check-input">
                    <label class="form-check-label" for="uselistprice">Make List Price the Default for PartsTech
                        orders</label>
                    <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Defaults all aftermarket part orders through parts tech to list pricing instead of matrix pricing." ></i>
                </div>
            <?php } ?>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showpartfeeaspart"
                       onchange="save(this.id,'settings')" <?php if (strtolower($showpartfeeaspart) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showpartfeeaspart">Show Partfee as Part</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Displays as a part fee line in the customer concern for parts that have an attached part fee in inventory. If turned off, part fees will display in the fees section of the repair order instead of as a part fee line in the customer concern." ></i>
            </div>
            <div class="form-check form-switch mb-4">
                <input type="checkbox" id="showtaxonpo"
                       onchange="save(this.id,'settings')" <?php if (strtolower($showTaxOnPO) == 'yes') {
                    echo 'checked';
                } ?> class="form-check-input">
                <label class="form-check-label" for="showtaxonpo">Show Tax on PO</label>
                <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Adds an extra column showing tax on PO" ></i>
            </div>
            <?php if ( $aiBetaFeatureActive ): ?>
                <div class="form-check form-switch mb-4">
                    <input type="checkbox" id="aiActive"
                        onchange="save(this.id,'settings')" <?php if (strtolower($aiActive) == 'yes') {
                        echo 'checked';
                    } ?> class="form-check-input">
                    <label class="form-check-label" for="aiActive">Show AI Writing Tool</label>
                    <i class="fa fa-info-circle simple-tooltip" data-mdb-toggle="tooltip" data-mdb-original-title="Enhance your writing with intelligent suggestions." ></i>
                </div>
            <?php endif; ?>
            <div class="form-row mb-4">
                <select id="showcarfaxonly" onchange="saveCarfax(this.value)" class="select">
                    <option <?php if ($showcarfax == "yes") {
                        echo "selected";
                    } ?> value="yes">Carfax Approved Only
                    </option>
                    <option <?php if ($showcarfax == "no") {
                        echo "selected";
                    } ?> value="no">No Carfax Descriptions
                    </option>
                    <option <?php if ($showcarfax == "both") {
                        echo "selected";
                    } ?> value="both">Carfax Approved and Standard Descriptions
                    </option>
                </select>
                <label class="form-label select-label" for="showcarfaxonly">Use Carfax Labor Descriptions</label>
            </div>
            <div class="form-row mb-4">
                <select id="calendardefault" onchange="saveCalendar(this.value)" class="select">
                    <option <?php if ($calendardefault == "agendaDay") {
                        echo "selected";
                    } ?> value="agendaDay">Day
                    </option>
                    <option <?php if ($calendardefault == "agendaTwoDay") {
                        echo "selected";
                    } ?> value="agendaTwoDay">3 Days
                    </option>
                    <option <?php if ($calendardefault == "agendaWeek") {
                        echo "selected";
                    } ?> value="agendaWeek">Week
                    </option>
                    <option <?php if ($calendardefault == "month") {
                        echo "selected";
                    } ?> value="month">Month
                    </option>
                </select>
                <label class="form-label select-label" for="calendardefault">Default Calendar View</label>
            </div>
            <?php if ($_COOKIE['dvilite'] == 'yes') { ?>
                <div class="form-row mb-4">
                    <select onchange="saveinspvalue(this.value)" class="select" id="defaultinspectionvalue"
                            name="defaultinspectionvalue">
                        <?php
                        if ($defaultinspectionvalue == "0") {
                            $st0 = " selected ";
                            $st1 = "";
                            $st2 = "";
                            $st3 = "";
                            $st4 = "";
                            $st5 = "";
                        } elseif ($defaultinspectionvalue == "1") {
                            $st0 = "";
                            $st1 = " selected ";
                            $st2 = "";
                            $st3 = "";
                            $st4 = "";
                            $st5 = "";
                        } elseif ($defaultinspectionvalue == "2") {
                            $st0 = "";
                            $st1 = "";
                            $st2 = " selected ";
                            $st3 = "";
                            $st4 = "";
                            $st5 = "";
                        } elseif ($defaultinspectionvalue == "3") {
                            $st0 = "";
                            $st1 = "";
                            $st2 = "";
                            $st3 = " selected ";
                            $st4 = "";
                            $st5 = "";
                        } elseif ($defaultinspectionvalue == "4") {
                            $st0 = "";
                            $st1 = "";
                            $st2 = "";
                            $st3 = "";
                            $st4 = " selected ";
                            $st5 = "";
                        } elseif ($defaultinspectionvalue == "5") {
                            $st0 = "";
                            $st1 = "";
                            $st2 = "";
                            $st3 = "";
                            $st4 = "";
                            $st5 = " selected ";
                        }

                        ?>
                        <option <?php echo $st0; ?> value="0">Not Applicable</option>
                        <option <?php echo $st1; ?> value="1">BAD</option>
                        <option <?php echo $st2; ?> value="2">POOR</option>
                        <option <?php echo $st3; ?> value="3">FAIR</option>
                        <option <?php echo $st4; ?> value="4">GOOD</option>
                        <option <?php echo $st5; ?> value="5">EXCELLENT</option>
                    </select>
                    <label for="defaultinspectionvalue" class="form-label select-label">Select Default Inspection
                        Value</label>

                </div>
            <?php } ?>
            <?php
            $pst = "";
            $azst = "";
            $mst = "";
            $cst = "";
            $est = "";
            $ast = "";
            $astnd = "";
            $akst = "";
            $hst = "";
            $eat = "";
            switch ($i15) {
                case "pst":
                    $pst = " selected='selected' ";
                    break;
                case "azst":
                    $azst = " selected='selected' ";
                    break;
                case "mst":
                    $mst = " selected='selected' ";
                    break;
                case "cst":
                    $cst = " selected='selected' ";
                    break;
                case "est":
                    $est = " selected='selected' ";
                    break;
                case "ast":
                    $ast = " selected='selected' ";
                    break;
                case "astnd":
                    $astnd = " selected='selected' ";
                    break;
                case "akst":
                    $akst = " selected='selected' ";
                    break;
                case "hst":
                    $hst = " selected='selected' ";
                    break;
                case "eat":
                    $eat = " selected='selected' ";
                    break;
                case "chst":
                    $chst = " selected='selected' ";
                    break;
                case "aest":
                    $aest = " selected='selected' ";
                    break;
            }
            ?>
            <div class="form-row mb-4">
                <select onchange="saveTZ(this.value)" class="select" id="timezone" name="timezone">
                    <option <?php echo $pst; ?> value="pst">Pacific</option>
                    <option <?php echo $azst; ?> value="azst">Arizona</option>
                    <option <?php echo $mst; ?> value="mst">Mountain</option>
                    <option <?php echo $cst; ?> value="cst">Central</option>
                    <option <?php echo $est; ?> value="est">Eastern</option>
                    <option <?php echo $ast; ?> value="ast">Atlantic</option>
                    <option <?php echo $astnd; ?> value="astnd">Atlantic no DST</option>
                    <option <?php echo $akst; ?> value="akst">Alaska</option>
                    <option <?php echo $hst; ?> value="hst">Hawaii</option>
                    <option <?php echo $eat; ?> value="eat">East Africa</option>
                    <option <?php echo $chst; ?> value="chst">Chamorro</option>
                    <option <?php echo $aest; ?> value="aest">Australia</option>
                </select>
                <label for="timezone" class="form-label select-label">Select Shop Timezone</label>

            </div>


            <div class="row mt-5 p-2 mb-5">
                <?php
                if (!empty($logo)) {
                    ?>
                    <div class="col-md text-center">
                        <div class="text-primary float-end">
                            <i class="fas fa-trash pe-auto" style='cursor:pointer' onclick="deleteLogo()"></i>
                        </div>
                        <?php
                        echo "<img id='logoimage' class='img-thumbnail ripple' onclick='editLogo()' style='cursor:pointer' src='" . UPLOAD_URL . "/$shopid/$logo'>";
                        ?>
                    </div>
                    <?php
                }
                ?>
                <div class="col-md text-center">
                    <form action="miscfiles/uploadlogo.php" class="dropzone" id="sbpdropzone">
                        <div class="dz-message" data-dz-message><span>
                            Click the box or Drag and Drop your company logo here
                                </span>
                        </div>
                    </form>
                </div>
            </div>

        </div>
    </div>
</main>

<div id="remoteccmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-light">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Remote CC Processing</h3>
                </div>
                <div id="vehinfo" class="block-content"></div>
                <div class="block-content">
                    <div class="row">
                        <div class="col-md-12">
                            By clicking below, you agree to the following terms and conditions:
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">

                <button class="btn btn-md btn-warning" type="button" onclick="remoteCC()">Accept Agreement</button>
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<?php
$component = '';
include getScriptsGlobal('');
// include getFooterComponent($component);
?>
<link rel="stylesheet" href="https://staging.shopbosspro.com/src/public/js/plugins/dropzonejs/dropzone.min.css">
<script src="https://staging.shopbosspro.com/src/public/js/plugins/dropzonejs/dropzone.min.js"></script>
<script>
    Dropzone.options.sbpdropzone = {
        maxFilesize: 5, // MB
        uploadMultiple: false,
        acceptedFiles: "image/*,android/force-camera-workaround",
        success: function (file, response) {
            //console.log(response)
            location.reload()
        },
        error: function (file, response) {
            //console.log(file+":"+response)
            //console.log(JSON.stringify(response));
        }
    };

    function saveinspvalue(v) {
        ds = "t=definspval&shopid=<?php echo $shopid; ?>&id=defaultinspectionvalue&val=" + v
        $.ajax({
            type: "post",
            data: ds,
            url: "miscaction.php",
            success: function (r) {
                //console.log(r)
                setTimeout(function () {
                    $('#saved').fadeOut(1500)
                }, 1000);

            }
        });
    }

    function saveCarfax(v) {

        ds = "t=carfax&shopid=<?php echo $shopid; ?>&id=showcarfaxonly&val=" + v
        $.ajax({
            type: "post",
            data: ds,
            url: "miscaction.php",
            success: function (r) {
                //console.log(r)
                setTimeout(function () {
                    $('#saved').fadeOut(1500)
                }, 1000);

            }
        });


    }

    function deleteLogo() {

        logo = encodeURIComponent("<?php echo $logo; ?>");
        sbconfirm("Are you sure?", "This will delete your logo so you can upload another.  Are you sure?", function () {
            showLoader();
            $.ajax({
                data: "logo=" + logo + "&t=deletelogo&shopid=<?php echo $shopid; ?>",
                url: "miscaction.php",
                type: "post",
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                    hideLoader();
                },
                success: function (r) {
                    console.log(r)
                    if (r == "success") {
                        location.reload()
                    }
                    hideLoader();
                }
            });
        });

    }

    function editLogo() {

        imgurl = encodeURIComponent($('#logoimage').attr("src"))
        eModal.iframe({
            title: 'Edit Logo',
            url: 'miscfiles/editlogo.php?imgurl=' + imgurl,
            size: eModal.size.xl,
        });
    }

    function saveTZ(v) {
        ds = "t=tz&shopid=<?php echo $shopid; ?>&id=timezone&val=" + v
        $('#saved').show()
        $.ajax({
            type: "post",
            data: ds,
            url: "miscaction.php",
            success: function (r) {
                //console.log(r)
                setTimeout(function () {
                    $('#saved').fadeOut(1500)
                }, 1000);

            }
        });

    }

    function saveCalendar(v) {
        ds = "t=calendar&shopid=<?php echo $shopid; ?>&val=" + v
        $('#saved').show()
        $.ajax({
            type: "post",
            data: ds,
            url: "miscaction.php",
            success: function (r) {
                setTimeout(function () {
                    $('#saved').fadeOut(1500)
                }, 1000);

            }
        });

    }

    function remoteCC() {

        $('#saved').show()
        $.ajax({
            type: "post",
            data: ds,
            url: "miscaction.php",
            success: function (r) {
                setTimeout(function () {
                    $('#saved').fadeOut(1500)
                    $('#remoteccmodal').modal('hide')
                }, 1000);
                console.log(r)
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },
        });

    }

    function save(id, table = 'company') {

        if ($('#' + id).is(':checked')) {
            if (id == "masterinterface") {
                ds = "t=checkbox&shopid=<?php echo $shopid; ?>&id=" + id + "&val=modern&table=" + table
                //console.log("1")
            } else {
                if (id == "nexpartpassword") {
                    ds = "t=checkbox&shopid=<?php echo $shopid; ?>&id=" + id + "&val=showcommlogyes&table=" + table
                } else {
                    ds = "t=checkbox&shopid=<?php echo $shopid; ?>&id=" + id + "&val=yes&table=" + table
                }
                //console.log("2")
            }
        } else {
            if (id == "masterinterface") {
                ds = "t=checkbox&shopid=<?php echo $shopid; ?>&id=" + id + "&val=classic&table=" + table
                //console.log("3")
            } else {
                if (id == "nexpartpassword") {
                    ds = "t=checkbox&shopid=<?php echo $shopid; ?>&id=" + id + "&val=showcommlogno&table=" + table
                } else {
                    ds = "t=checkbox&shopid=<?php echo $shopid; ?>&id=" + id + "&val=no&table=" + table
                }
                //console.log("4")
            }
        }


        //console.log(id)
        if (id == "remotecc") {
            console.log($('#remotecc').is(':checked'))
            if ($('#remotecc').is(':checked')) {
                $('#remoteccmodal').modal('show')
                return
            }
        }
        $('#saved').show()
        $.ajax({
            type: "post",
            data: ds,
            url: "miscaction.php",
            success: function (r) {
                setTimeout(function () {
                    $('#saved').fadeOut(1500)
                }, 1000);
                console.log(r)
            },
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },
        });

    }


    function saveAll() {

        mil = encodeURIComponent($('#milesinlabel').val())
        mol = encodeURIComponent($('#milesoutlabel').val())
        it = encodeURIComponent($('#invoicetitle').val())
        et = encodeURIComponent($('#estimatetitle').val())
        ds = "t=miles&shopid=<?php echo $shopid; ?>&mil=" + mil + "&mol=" + mol + "&it=" + it + "&et=" + et

        console.log(ds)
        $.ajax({
            data: ds,
            type: "post",
            url: "printingprefsaction.php",
            success: function (r) {
                swal("Changes Saved")
            }
        });


    }
</script>
</body>

</html>
<?php
mysqli_close($conn);
?>
