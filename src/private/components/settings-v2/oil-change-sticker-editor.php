<?php
    $stmt = "SELECT logo, CompanyName FROM company WHERE shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($logo, $companyName);
        $query->fetch();
        $query->close();
    } else {
        echo "Prepare failed: " . $conn->error;
    }
    $logoPath = "\\fs.shopboss.aws\share\upload\\$shopid\\$logo";
?>

<style>
    #stickerPreview{
        position: sticky;
        top: 80px;
    }

    .sticker-preview {
        display: grid;
        width: 1.75in;
        height: 2.5in;
        margin: 0 auto;
        background-color: #fff;

        border: 1px solid grey;
    }
    .sticker-section {
        font-size: 12px;
        text-align: center;
        padding: 5.8px 0;
    }

    .sticker-section img{
        max-height: 50px;
        max-width: 80px;
        -webkit-filter: grayscale(100%);
        filter: grayscale(100%);
    }

    .sticker-section span{
        display: block;
    }

    #noteSection{
        font-size: 10px;
    }

    .colorChooser{
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .colorChooser label{
        margin: 0;
    }

    @media screen and (max-width: 767px) {
        .colorChooser{
            justify-content: left;
        }
    }
</style>

<div id="oil-change-sticker-editor">
    <h2 class="text-center mb-4">Oil Change Sticker Design Editor</h2>
    <form action="oil-change-sticker-editor-save.php" method="POST" id="sticker_form">
        <div class="row">
            <div class="col-md-8">
                <div class="mb-4">
                    <h4>Sticker</h4>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-outline mb-3">
                                <input type="text" name="label" id="label" class="form-control" required>
                                <label for="label" class="form-label">Label</label>
                            </div>
                        </div>
                        <!-- <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="sticker_background" class="form-label">Background Color</label>
                                <input type="color" id="sticker_background" name="sticker_background" value="#ffffff" class="form-control form-control-color">
                            </div>
                        </div> -->
                    </div>
                </div>

               <!-- <div class="mb-4">
                    <h4 class="mb-3">Header</h4>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-outline mb-3">
                                <input type="text" name="header_text" id="header_text" class="form-control" value="Oil Change Sticker">
                                <label for="header_text" class="form-label">Custom Text</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="header_background" class="form-label">Background Color</label>
                                <input type="color" id="header_background" name="header_background" value="#e01717" class="form-control form-control-color">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="header_color" class="form-label">Text Color</label>
                                <input type="color" name="header_color" id="header_color" value="#ffffff" class="form-control form-control-color">
                            </div>
                        </div> 
                    </div>
                </div>-->

                <div class="mb-4">
                    <h4>Company Logo|Name</h4>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-row mb-3">
                                <select name="company_order" id="company_order" name="company_order" class="select">
                                    <option value="1" selected>1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5">5</option>
                                    <option value="6">6</option>
                                </select>
                                <label for="company_order" class="form-label select-label">Row Order</label>
                            </div>
                        </div>
                        <!-- <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="company_background" class="form-label">Background Color</label>
                                <input type="color" id="company_background" name="company_background" value="#f4f4f4" class="form-control form-control-color">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="company_color" class="form-label">Text Color</label>
                                <input type="color" name="company_color" id="company_color" value="#333333" class="form-control form-control-color">
                            </div>
                        </div> -->
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="company_logo" name="company_logo" checked>
                                <label class="form-check-label" for="company_logo">Show Logo</label>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="company_name" name="company_name" checked>
                                <label class="form-check-label" for="company_name">Show Company Name</label>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="form-outline mb-3">
                                <input type="text" name="qr_text" id="qr_text" class="form-control" value="Oil Change Sticker">
                                <label for="qr_text" class="form-label">QR Text</label>
                            </div>
                        </div>
                        <div class="col-md-3"></div>
                        <div class="col-md-5">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="qr" name="qr" checked>
                                <label class="form-check-label" for="qr">Show QR</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <h4>Actual Mileage</h4>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-row mb-3">
                                <select name="actual_mileage_order" id="actual_mileage_order" class="select">
                                    <option value="1">1</option>
                                    <option value="2" selected>2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5">5</option>
                                    <option value="6">6</option>
                                </select>
                                <label for="actual_mileage_order" class="form-label select-label">Row Order</label>
                            </div>
                        </div>
                        <!-- <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="actual_mileage_background" class="form-label">Background Color</label>
                                <input type="color" id="actual_mileage_background" name="actual_mileage_background" value="#f4f4f4" class="form-control form-control-color">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="actual_mileage_color" class="form-label">Text Color</label>
                                <input type="color" name="actual_mileage_color" id="actual_mileage_color" value="#333333" class="form-control form-control-color">
                            </div>
                        </div> -->
                    </div>
                </div>

                <div class="mb-4">
                    <h4 class="mb-3">Next Service Due</h4>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-outline mb-3">
                                <input type="text" name="next_service_text" id="next_service_text" class="form-control" value="Next Service Due">
                                <label for="next_service_text" class="form-label">Label</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-row mb-3">
                                <select name="next_order" id="next_order" class="select">
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3" selected>3</option>
                                    <option value="4">4</option>
                                    <option value="5">5</option>
                                    <option value="6">6</option>
                                </select>
                                <label for="next_order" class="form-label select-label">Row Order</label>
                            </div>
                        </div>
                        <!-- <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="next_background" class="form-label">Background Color</label>
                                <input type="color" id="next_background" name="next_background" value="#fffbf2" class="form-control form-control-color">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="next_color" class="form-label">Text Color</label>
                                <input type="color" name="next_color" id="next_color" value="#555555" class="form-control form-control-color">
                            </div>
                        </div> -->
                    </div>
                </div>

                <div class="mb-4">
                    <h4>Mileage</h4>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-row mb-3">
                                <select name="mileage_order" id="mileage_order" class="select">
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4" selected>4</option>
                                    <option value="5">5</option>
                                    <option value="6">6</option>
                                </select>
                                <label for="mileage_order" class="form-label select-label">Row Order</label>
                            </div>
                        </div>
                        <!-- <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="mileage_background" class="form-label">Background Color</label>
                                <input type="color" name="mileage_background" id="mileage_background" value="#f4f4f4" class="form-control form-control-color">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="mileage_color" class="form-label">Text Color</label>
                                <input type="color" name="mileage_color" id="mileage_color" value="#333333" class="form-control form-control-color">
                            </div>
                        </div> -->
                    </div>
                </div>

                <div class="mb-4">
                    <h4>Date</h4>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-row mb-3">
                                <select name="date_order" id="date_order" class="select">
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5" selected>5</option>
                                    <option value="6">6</option>
                                </select>
                                <label for="date_order" class="form-label select-label">Row Order</label>
                            </div>
                        </div>
                        <!-- <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="date_background" class="form-label">Background Color</label>
                                <input type="color" name="date_background" id="date_background" value="#f4f4f4" class="form-control form-control-color">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="date_color" class="form-label">Text Color</label>
                                <input type="color" name="date_color" id="date_color" value="#333333" class="form-control form-control-color">
                            </div>
                        </div> -->
                    </div>
                </div>

                <div class="mb-4">
                    <h4>Notes</h4>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-row mb-3">
                                <select name="note_order" id="note_order" class="select">
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5">5</option>
                                    <option value="6" selected>6</option>
                                </select>
                                <label for="note_order" class="form-label select-label">Row Order</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-outline mb-3">
                                <input type="text" name="note_text" id="note_text" class="form-control" maxlength="32">
                                <label for="note_text" class="form-label">Notes Text</label>
                            </div>
                            <p id="note_alert" class="text-danger d-none small">Maximum character limit reached!</p>
                        </div>
                        <!-- <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="note_background" class="form-label">Background Color</label>
                                <input type="color" name="note_background" id="note_background" value="#ffffff" class="form-control form-control-color">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3 colorChooser">
                                <label for="note_color" class="form-label">Text Color</label>
                                <input type="color" name="note_color" id="note_color" value="#333333" class="form-control form-control-color">
                            </div>
                        </div> -->
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="sticker-preview" id="stickerPreview">
                    <!-- <div id="headerSection" class="sticker-section" style="background-color: #000000; color: #ffffff;">Oil Change Sticker</div> -->
                    <div id="companySection" class="sticker-section" style="color: #000000;">
                        <div>
                            <img src="https://quickchart.io/qr?text=https://example.com&size=300" alt="QR Code" id="previewQR">
                            <img src="https://staging.shopbosspro.com/sbp/upload/6062/shopboss-logo-nut.png" alt="Logo" id="previewLogo">
                        </div>
                        <span id="previewName" style="padding: 6.4px 0;">Company Name</span>
                    </div>
                    <div id="actualMileageSection" class="sticker-section" style="background-color: #ffffff; color: #000000; padding: 5px 0;">39500 mi</div>
                    <div id="nextSection" class="sticker-section" style="background-color: #ffffff; color: #000000; padding: 5px 0;">Next Service Due</div>
                    <div id="dateSection" class="sticker-section" style="background-color: #ffffff; color: #000000; text-align: left; padding: 5px 8px;">
                        <div class="row">
                            <div class="col-md-6">
                                DATE
                            </div>
                            <div class="col-md-6">12-25-2024</div>
                        </div>
                    </div>
                    <div id="mileageSection" class="sticker-section" style="background-color: #ffffff; color: #000000; text-align: left; padding: 5px 8px;">
                        <div class="row">
                            <div class="col-md-6">
                                MILES
                            </div>
                            <div class="col-md-6">55 000</div>
                        </div>
                    </div>
                    <div id="noteSection" class="sticker-section" style="color: #000000;">Some notes here</div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <button type="submit" class="btn btn-primary">Save</button>
        </div>
    </form>
</div>

<script>
    document.getElementById('note_text').addEventListener('input', function () {
        let alertBox = document.getElementById('note_alert');
        if (this.value.length >= this.maxLength) {
            alertBox.classList.remove('d-none');
        } else {
            alertBox.classList.add('d-none');
        }
    });

    function updatePreview() {
        // Sticker Section
        // const sticker_background = document.getElementById('sticker_background').value
        const preview = document.getElementById('stickerPreview');
        // preview.style.backgroundColor = sticker_background;

        // Header Section
        // const header_text = document.getElementById('header_text').value;
        // const header_background = document.getElementById('header_background').value;
        // const header_color = document.getElementById('header_color').value;
        // const headerSection = document.getElementById('headerSection');
        // headerSection.textContent = header_text;
        // headerSection.style.backgroundColor = header_background;
        // headerSection.style.color = header_color;

        // Logo | Company Name Section
        const company_logo = document.getElementById('company_logo').checked;
        const company_name = document.getElementById('company_name').checked;
        // const company_background = document.getElementById('company_background').value;
        // const company_color = document.getElementById('company_color').value;
        const previewLogo = document.getElementById('previewLogo');
        const previewName = document.getElementById('previewName');
        previewLogo.style.display = company_logo ? 'inline-block' : 'none';
        previewName.style.display = company_name ? 'inline-block' : 'none';
        const companySection = document.getElementById('companySection');

        // QR Code
        const qr_checked = document.getElementById('qr').checked;
        const previewQR = document.getElementById('previewQR');
        const QRText = document.getElementById('qr_text').value;
        const baseQRLink = `https://quickchart.io/qr?text=${QRText}&size=300`;
        previewQR.style.display = qr_checked ? 'inline-block' : 'none';
        previewQR.src = baseQRLink;

        // companySection.style.backgroundColor = company_background;
        // companySection.style.color = company_color;

        // Actual Mileage Section
        // const mileage_background = document.getElementById('mileage_background').value;
        // const mileage_color = document.getElementById('mileage_color').value;
        const mileageSection = document.getElementById('mileageSection');
        // mileageSection.style.backgroundColor = mileage_background;
        // mileageSection.style.color = mileage_color;

        // Next Service Due Section
        // const next_background = document.getElementById('next_background').value;
        // const next_color = document.getElementById('next_color').value;
        const nextSection = document.getElementById('nextSection');
        const next_service_text = document.getElementById('next_service_text');
        // nextSection.style.backgroundColor = next_background;
        // nextSection.style.color = next_color;
        nextSection.textContent = next_service_text.value;

        // Mileage Section
        // const actual_mileage_background = document.getElementById('actual_mileage_background').value;
        // const actual_mileage_color = document.getElementById('actual_mileage_color').value;
        const actualMileageSection = document.getElementById('actualMileageSection');
        // actualMileageSection.style.backgroundColor = actual_mileage_background;
        // actualMileageSection.style.color = actual_mileage_color;

        // Date Section
        // const date_background = document.getElementById('date_background').value;
        // const date_color = document.getElementById('date_color').value;
        const dateSection = document.getElementById('dateSection');
        // dateSection.style.backgroundColor = date_background;
        // dateSection.style.color = date_color;

        // Notes Section
        // const note_background = document.getElementById('note_background').value;
        // const note_color = document.getElementById('note_color').value;
        const noteSection = document.getElementById('noteSection');
        const notesText = document.getElementById('note_text');
        noteSection.innerText = notesText.value ? notesText.value : "Some notes here";
        // noteSection.style.backgroundColor = note_background;
        // noteSection.style.color = note_color;

        // Sections Order
        const sections = [
            { order: parseInt(document.getElementById('company_order').value), element: companySection },
            { order: parseInt(document.getElementById('actual_mileage_order').value), element: actualMileageSection },
            { order: parseInt(document.getElementById('date_order').value), element: dateSection },
            { order: parseInt(document.getElementById('mileage_order').value), element: mileageSection },
            { order: parseInt(document.getElementById('next_order').value), element: nextSection },
            { order: parseInt(document.getElementById('note_order').value), element: noteSection },
            // { order: 0, element: document.getElementById('headerSection') },
        ];

        sections.sort((a, b) => a.order - b.order);

        sections.forEach(section => {
            preview.appendChild(section.element);
        });
    }

    document.querySelectorAll('input, select').forEach(input => {
        input.addEventListener('input', updatePreview);
        input.addEventListener('change', updatePreview);
    });

    updatePreview();

    document.getElementById('sticker_form').addEventListener('submit', function (event) {
        event.preventDefault();

        const formData = new FormData(this);
        formData.append('action', 'newStickerDesign');

        fetch('oil-change-sticker-controller.php', {
            method: 'POST',
            body: formData,
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                window.location.reload()
            } else {
                alert(data.error || 'An error occurred while saving settings.');
            }
        })
        .catch(error => {
            console.error('There was an error with the fetch operation:', error);
            alert('Failed to save settings. Please try again.');
        });
    });
</script>
