<?php
    require CONN;

    header('Content-Type: application/json');
    ini_set('zlib.output_compression', 'Off');
    ob_end_clean();

    $shopid = $_COOKIE['shopid'] ?? null;

    if (!$shopid) {
        echo json_encode(["error" => "Shop ID is required."]);
        exit;
    }

    function savePreferences($conn, $shopid){
        $label_1 = $_POST['label_1'] ?? 'Good';
        $label_1_odometer = $_POST['label_1_odometer'] ?? 3000;
        $label_1_days = $_POST['label_1_days'] ?? 90;
    
        $label_2 = $_POST['label_2'] ?? 'Better';
        $label_2_odometer = $_POST['label_2_odometer'] ?? 5000;
        $label_2_days = $_POST['label_2_days'] ?? 150;
    
        $label_3 = $_POST['label_3'] ?? 'Best';
        $label_3_odometer = $_POST['label_3_odometer'] ?? 7500;
        $label_3_days = $_POST['label_3_days'] ?? 180;
    
        $odometer_units = $_POST['odometer_units'] ?? 'Miles';
        
        $default = $_POST['default'] ?? 2;
        $default_sticker_1 = $_POST['default_sticker_1'] ?? NULL;
        $default_sticker_2 = $_POST['default_sticker_2'] ?? NULL;
        $default_sticker_3 = $_POST['default_sticker_3'] ?? NULL;

        $printer = $_POST['printer'] ?? "1.75x2.50";
        $label_width = explode("x", $printer)[0];
        $label_height = explode("x", $printer)[1];

        $stmt = $conn->prepare("SELECT 1 FROM oil_change_sticker_settings WHERE shopid = ?");
        $stmt->bind_param("s", $shopid);
        $stmt->execute();
        $stmt->store_result();
   
        if ($stmt->num_rows > 0) {
            $query = "UPDATE oil_change_sticker_settings SET 
                        label_1 = ?, label_1_odometer = ?, label_1_days = ?, 
                        label_2 = ?, label_2_odometer = ?, label_2_days = ?, 
                        label_3 = ?, label_3_odometer = ?, label_3_days = ?, 
                        `default` = ?, label_1_default_sticker = ?, label_2_default_sticker = ?, label_3_default_sticker = ?, odometer_units = ?,
                        template_width = ?, template_height = ?
                    WHERE shopid = ?";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("siisiisiisiiisdds", 
                $label_1, $label_1_odometer, $label_1_days, 
                $label_2, $label_2_odometer, $label_2_days, 
                $label_3, $label_3_odometer, $label_3_days, 
                $default, $default_sticker_1, $default_sticker_2, $default_sticker_3, 
                $odometer_units, $label_width, $label_height, $shopid
            );
        } else {
            $query = "INSERT INTO oil_change_sticker_settings 
                      (shopid, label_1, label_2, label_3, odometer_units, label_1_odometer, label_2_odometer, label_3_odometer, label_1_days, label_2_days, label_3_days, `default`, label_1_default_sticker, label_2_default_sticker, label_3_default_sticker, template_width, template_height) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("sssssiiiiiiiiiidd", 
                $shopid, 
                $label_1, $label_2, $label_3, $odometer_units,
                $label_1_odometer, $label_2_odometer, $label_3_odometer, 
                $label_1_days, $label_2_days, $label_3_days, 
                $default, $default_sticker_1, $default_sticker_2, $default_sticker_3,
                $label_width, $label_height
            );
        }
    
        if ($stmt->execute()) {
            $stmt->close();
            $conn->commit();
            echo json_encode(["success" => true, "message" => "Settings saved successfully.", "query" => $query]);
        } else {
            echo json_encode(["error" => "Database error: " . $stmt->error]);
        }
    }

    function newStickerDesign($conn, $shopid){
        $label = $_POST['label'] ?? 'Test Sticker';
        $sticker_background = $_POST['sticker_background'] ?? '#ffffff';
        $header_text = $_POST['header_text'] ?? 'Oil Change Sticker';
        $header_background = $_POST['header_background'] ?? '#000000';
        $header_color = $_POST['header_color'] ?? '#ffffff';
        $company_order = $_POST['company_order'] ?? 1;
        $company_background = $_POST['company_background'] ?? '#ffffff';
        $company_color = $_POST['company_color'] ?? '#000000';
        $company_logo = $_POST['company_logo'] ?? 'off';
        $company_name = $_POST['company_name'] ?? 'off';
        $actual_mileage_order = $_POST['actual_mileage_order'] ?? 2;
        $actual_mileage_background = $_POST['actual_mileage_background'] ?? '#ffffff';
        $actual_mileage_color = $_POST['actual_mileage_color'] ?? '#000000';
        $next_service_text = $_POST['next_service_text'] ?? 'Next Service Due';
        $next_order = $_POST['next_order'] ?? 3;
        $next_background = $_POST['next_background'] ?? '#ffffff';
        $next_color = $_POST['next_color'] ?? '#000000';
        $mileage_order = $_POST['mileage_order'] ?? 4;
        $mileage_background = $_POST['mileage_background'] ?? '#ffffff';
        $mileage_color = $_POST['mileage_color'] ?? '#000000';
        $date_order = $_POST['date_order'] ?? 5;
        $date_background = $_POST['date_background'] ?? '#ffffff';
        $date_color = $_POST['date_color'] ?? '#000000';
        $notes_order = $_POST['note_order'] ?? NULL;
        $notes_background = $_POST['note_background'] ?? '#ffffff';
        $notes_color = $_POST['note_color'] ?? '#000000';
        $notes_text = $_POST['note_text'] ?? '';
        $qr = $_POST['qr'] ?? 'off';
        $qr_text = $_POST['qr_text'] ?? '';

        $query = "INSERT INTO oil_change_stickers 
                    (shopid, label, sticker_background, header_text, header_background, header_color, company_order, 
                    company_logo, company_name, company_background, company_color, actual_mileage_order, actual_mileage_background, actual_mileage_color, 
                    next_service_text, next_order, next_background, next_color, 
                    mileage_order, mileage_background, mileage_color, 
                    date_order, date_background, date_color,
                    notes_order, notes_background, notes_color, notes_text,
                    qr, qr_text
                    ) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($query);
        if ($stmt === false) {
            echo json_encode(["error" => "Failed to prepare SQL query: " . $conn->error]);
            exit;
        }
        $stmt->bind_param("ssssssissssisssississississsss", 
            $shopid, $label, $sticker_background, $header_text, $header_background, $header_color, $company_order, 
            $company_logo, $company_name, $company_background, $company_color, $actual_mileage_order, $actual_mileage_background, $actual_mileage_color, 
            $next_service_text, $next_order, $next_background, $next_color, 
            $mileage_order, $mileage_background, $mileage_color, 
            $date_order, $date_background, $date_color,
            $notes_order, $notes_background, $notes_color, $notes_text,
            $qr, $qr_text
        );

        if ($stmt->execute()) {
            $stmt->close();
            $conn->commit();
            echo json_encode(["success" => true, "message" => "Settings saved successfully."]);
        } else {
            echo json_encode(["error" => "Database error: " . $stmt->error]);
        }
    }

    function deleteStickerDesign($conn, $shopid){
        $id = $_POST['id'] ?? null;

        if ($id === null) {
            echo json_encode(["error" => "ID is required for deletion."]);
            exit;
        }

        $query = "DELETE FROM oil_change_stickers WHERE id = ? AND shopid = ?";
        $stmt = $conn->prepare($query);

        if ($stmt === false) {
            echo json_encode(["error" => "Failed to prepare SQL query: " . $conn->error]);
            exit;
        }

        $stmt->bind_param("is", $id, $shopid);

        if ($stmt->execute()) {
            $stmt->close();
            $conn->commit();
            echo json_encode(["success" => true, "message" => "Record deleted successfully."]);
        } else {
            echo json_encode(["error" => "Database error: " . $stmt->error]);
        }
    }

    $action = $_POST['action'] ?? die(json_encode(["error" => "Action is required."]));

    switch ($action) {
        case 'savePreferences':
            savePreferences($conn, $shopid);
            break;
    
        case 'newStickerDesign':
            newStickerDesign($conn, $shopid);
            break;

        case 'deleteStickerDesign':
            deleteStickerDesign($conn, $shopid);
            break;
    
        default:
            echo json_encode(["error" => "Invalid action specified."]);
            break;
    }
    mysqli_close($conn);
?>