<?php
    $stmt = "SELECT logo, CompanyName FROM company WHERE shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($logo, $companyName);
        $query->fetch();
        $query->close();
    } else {
        echo "Prepare failed: " . $conn->error;
    }
    $logoPath = "/fs/shopboss/aws/share/upload/$shopid/$logo";
    $logoURL = "https://" . $_SERVER['SERVER_NAME'] . "/sbp/upload/$shopid/$logo";

    if (file_exists($logoPath)) {
        $finalLogo = $logoPath;
    } else {
        $finalLogo = $logoURL;
    }   

    // Sticker Designs
    $stmt = "SELECT * FROM oil_change_stickers WHERE shopid = ?";
    $stickers = [];

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $result = $query->get_result();

        $stickers = $result->fetch_all(MYSQLI_ASSOC);

        $query->close();
    } else {
        echo "Prepare failed: " . $conn->error;
    }
?>

<style>
    #oil-change-sticker-designs{
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(1.75in, 1fr));
        grid-gap: 30px;
    }
    .sticker{
        width: 1.75in;
    }
    .sticker-preview {
        width: 1.75in;
        height: 2.5in;
        background-color: #fff;
    }
    .pointer{
        cursor: pointer
    }
</style>

<?php if ( count($stickers) ) : ?>
    <h2 class="text-center mb-4">Existing Oil Change Sticker Designs</h2>
<?php endif; ?>
<div id="oil-change-sticker-designs"></div>

<script>
    const stickers = <?= json_encode($stickers);?>;

    function generateStickerPreview(sticker) {
        const sections = [
            { 
                order: sticker.company_order, 
                element: createCompanySection('companySection', sticker.company_background, sticker.company_color, sticker.company_logo, sticker.company_name, sticker.qr, sticker.qr_text) 
            },
            { 
                order: sticker.actual_mileage_order, 
                element: createSection('actualMileageSection', sticker.actual_mileage_background, sticker.actual_mileage_color, '39500 mi', '') 
            },
            { 
                order: sticker.date_order, 
                element: createSection('dateSection', sticker.date_background, sticker.date_color, '12-25-2024', 'DATE') 
            },
            { 
                order: sticker.mileage_order, 
                element: createSection('mileageSection', sticker.mileage_background, sticker.mileage_color, '55 000', 'MILES') 
            },
            { 
                order: sticker.next_order, 
                element: createSection('nextSection', sticker.next_background, sticker.next_color, sticker.next_service_text, '') 
            },
            { 
                order: sticker.notes_order, 
                element: createSection('noteSection', sticker.sticker_background, '', sticker.notes_text || 'Some notes here', '') 
            },
            // { 
            //     order: 0, 
            //     element: createHeaderSection(sticker.header_background, sticker.header_color, sticker.header_text) 
            // }
        ];

        sections.sort((a, b) => a.order - b.order);

        const stickerHTML = sections.map(section => section.element).join('');

        return `
            <div class="sticker">
                <p>${sticker.label}</p>
                <div class="sticker-preview" style="position: relative; background-color: ${sticker.sticker_background}">
                    <i class="fa fa-trash pointer" style="position: absolute; top: 10px; right: -20px" onclick="deleteSticker(${sticker.id})"></i>
                    ${stickerHTML}
                </div>
            </div>
        `;
    }

    function createSection(id, background, color, text, label) {
        return `
            <div id="${id}" class="sticker-section" style="background-color: ${background}; color: ${color}; padding: 5px 0;">
                ${label ? `<div class="row"><div class="col-md-6">${label}</div><div class="col-md-6">${text}</div></div>` : text}
            </div>
        `;
    }

    function createCompanySection(id, background, color, showCompanyLogo, showCompanyName, showQR, qrText) {
        const padding = <?= $logo ? "'5.8px 0'" : "'19px 0'" ?>;
        return `
            <div id="${id}" class="sticker-section" style="padding: ${padding}; background-color: ${background}">
                <div>
                    ${showQR === 'on' ? `<img src="https://quickchart.io/qr?text=${qrText}&size=80" alt="QR" style="max-height: 50px; max-width: 80px;" id="previewQR">` : ''}
                    
                    ${showCompanyLogo === 'on' && <?php echo json_encode($logo ? 'true' : 'false'); ?> === 'true' ? 
                        `<img src="<?= $finalLogo; ?>" alt="Logo" style="max-height: 50px; max-width: 80px;" id="previewLogo">` : ''}
                </div>
                
                ${showCompanyName === 'on' ? 
                    `<span id="previewName" style="padding: 6.4px 0; color: ${color}"><?= $companyName; ?></span>` : ''}
            </div>
        `;
    }


    function createHeaderSection(background, color, text) {
        return `
            <div id="headerSection" class="sticker-section" style="background-color: ${background}; color: ${color};">${text}</div>
        `;
    }

    function updatePreview() {
        const container = document.getElementById('oil-change-sticker-designs');
        container.innerHTML = '';

        stickers.forEach(sticker => {
            container.innerHTML += generateStickerPreview(sticker);
        });
    }

    updatePreview();

    async function deleteSticker(id){
        sbconfirm(
            'Are you sure?',
            'This oil sticker design will be deleted. Are you sure?',
            async function () {
                const formData = new FormData();
                formData.append('action', 'deleteStickerDesign');
                formData.append('id', id);

                try {
                    const response = await fetch('oil-change-sticker-controller.php', {
                        method: 'POST',
                        body: formData,
                    });

                    const result = await response.json();
                    if (result.success) {
                        location.reload()
                    } else {
                        alert(result.error || "An unexpected error occurred.");
                    }
                } catch (error) {
                    console.error("Fetch error: ", error);
                    alert("Network or server error. Please try again.");
                }
            }
        );
    }
</script>