<style>
    input[type='checkbox'] {
        padding: 0 !important;
    }

    .form-switch input[type='checkbox'] {
        background-color: rgba(0, 0, 0, .25) !important;
    }

    .timepicker-inline-minutes-icons .active {
        color: white !important;
    }

    .timepicker-icon-inline-hour,
    .timepicker-icon-inline-hour:hover,
    .timepicker-icon-inline-minute,
    .timepicker-icon-inline-minute:hover {
        color: white !important;
    }

    .form-check-input:focus,
    .form-check-input:checked {
        background-color: transparent !important;
        border: 1px solid rgba(0, 0, 0, .25) !important;
    }

    /*
    table.dataTable thead > tr > th.sorting,
    table.dataTable thead > tr > th.sorting_asc,
    table.dataTable thead > tr > th.sorting_desc,
    table.dataTable thead > tr > th.sorting_asc_disabled,
    table.dataTable thead > tr > th.sorting_desc_disabled,
    table.dataTable thead > tr > td.sorting,
    table.dataTable thead > tr > td.sorting_asc,
    table.dataTable thead > tr > td.sorting_desc,
    table.dataTable thead > tr > td.sorting_asc_disabled,
    table.dataTable thead > tr > td.sorting_desc_disabled {
        cursor: pointer;
        position: relative;
        padding-right: 26px;
        border-bottom: 1px solid var(--secondary) !important;
        font-weight: normal;
    }
    */

    .vh-50 {
        min-height: 50vh;
    }

    .vh-75 {
        min-height: 75vh;
    }

    #settings {
        margin-top: 143px;
    }
    #reports .dropdown-toggle:hover{
        border: 1px solid var(--primary)
    }

    #integration_cards .card-body img {
        max-height: 60px;
        max-width: 100%;
        display: inline;
    }

    #integration_cards .card-square .logo_container {
        height: 80px;
    }

    #integration_cards .card-square .card-body {
        overflow: hidden;
        /*
        height: 25vh;   */
        height: 415px;
    }

    #integration_cards .card-body-expanded .card-body {
        overflow: visible;
        height: auto !important;
        min-height: 100%;

        transform: scaleY(1);
        transform-origin: top;
        transition: min-height 1s ease-in-out;

    }

    .expand_box {
        cursor: pointer;
        visibility: hidden;
    }

    .sbdatatable I {
        color: var(--primary) !important;
    }

    .cursor-move {
        cursor: move;
    }

    .dt-rowReorder-moving {
        background-color: white !important;
        border: 1px dashed var(--primary)
    }

    .autocomplete-dropdown-container, .select-dropdown-container, #sbalertmodal, #sbconfirmmodal, #loader-container{
        z-index : 9999;
    }

    .badge-secondary, .badge-light{
        color: var(--white) !important;
        background-color: var(--secondary) !important;
    }
    a i.fa-trash{
        color: var(--primary) !important;
    }
    .accordian_focus{
        border: 1px solid var(--primary);
    }
</style>
