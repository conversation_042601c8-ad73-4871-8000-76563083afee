<?php

require(CONN);
require REDISCONN;

if ($_POST['t'] == "save"){
	
	$shopid = $_POST['shopid'];
	$company = $_POST['company'];
	$address = $_POST['address'];
	$city = $_POST['city'];
	$state = $_POST['state'];
	$zip = $_POST['zip'];
	$phone = cleanPhone($_POST['phone']);
	$fax = cleanPhone($_POST['fax']);
	$email = $_POST['email'];
	$url = $_POST['url'];
	$epa = $_POST['epa'];
	$bar = $_POST['bar'];
	$ein = $_POST['ein'];
	$warrmos = $_POST['warrmos'];
	$warrmiles = $_POST['warrmiles'];
	$pswarr = $_POST['pswarr'];
	$haz = $_POST['haz'];
	$partstax = $_POST['partstax'];
	$labortax = $_POST['labortax'];
	$sublettax = $_POST['sublettax'];
	$storage = $_POST['storage'];
	$hst = !empty($_POST['hst']) ? $_POST['hst']: 0;
	$pst = !empty($_POST['pst']) ? $_POST['pst'] : 0;
	$gst = !empty($_POST['gst']) ? $_POST['gst'] : 0;
	$qst = !empty($_POST['qst']) ? $_POST['qst'] : 0;
	$hstapplyon = (isset($_POST['hstapplyon'])?implode(',',$_POST['hstapplyon']):'');
	$gstapplyon = (isset($_POST['gstapplyon'])?implode(',',$_POST['gstapplyon']):'');
	$pstapplyon = (isset($_POST['pstapplyon'])?implode(',',$_POST['pstapplyon']):'');
	$qstapplyon = (isset($_POST['qstapplyon'])?implode(',',$_POST['qstapplyon']):'');
	if($hstapplyon=='P,L,S,A')$hstapplyon='A';
	if($gstapplyon=='P,L,S,A')$gstapplyon='A';
	if($pstapplyon=='P,L,S,A')$pstapplyon='A';
	if($qstapplyon=='P,L,S,A')$qstapplyon='A';
	$definvmsgemail = $_POST['definvmsgemail'];
	
	if (isset($_POST['chargehst'])){$chargehst = "yes";}else{$chargehst = "no";}
	if (isset($_POST['chargepst'])){$chargepst = "yes";}else{$chargepst = "no";}
	if (isset($_POST['chargegst'])){$chargegst = "yes";}else{$chargegst = "no";}
	if (isset($_POST['chargeqst'])){$chargeqst = "yes";}else{$chargeqst = "no";}

	for($i=0;$i<=6;$i++)
	{
	    if(!empty($_POST['open'.$i]) && !empty($_POST['close'.$i]) && $_POST['open'.$i]!='00:00' && $_POST['close'.$i]!='00:00' && strtotime($_POST['open'.$i]) > strtotime($_POST['close'.$i]))
	    die("Hours MUST be in Military/24 hour times. Closing hours cannot be earlier than Opening hours");
	}
	
	
	/*$sathrs = $_POST['sathrs'];
	$sunhrs = $_POST['sunhrs'];
	$dailyhrs = $_POST['dailyhrs'];
	$daysopen = $_POST['daysopen'];*/
	
	$mstrinterface = $_POST['mstrinterface'];

	
	$userpassword = $_POST['kioskpassword'];
	

	
	
	
	$stmt = "update company set definvmsgemail = ?,chargehst = ?, chargegst = ?, chargepst = ?, chargeqst = ?, qst = ?, hst = ?, gst = ?, pst = ?,hstapplyon = ?,pstapplyon = ?,gstapplyon = ?,qstapplyon = ?, companyname = ?, companyaddress = ?, companycity = ?, companystate = ?, companyzip = ?, companyphone = ?, companyfax = ?, companyemail = ?, companyurl = ?,"
	. "barno = ?, epano = ?, defaulttaxrate = ?, defaultlabortaxrate = ?, defaultsublettaxrate = ?, defaultwarrmos = ?, defaultwarrmiles = ?, storagefee = ?, hazardouswaste = ?, PSWarrDays = ?, masterinterface = ?,UserPassword = ?, einno = ? "
	. "where shopid = ?";
	
	//printf (str_replace('?',"'%s'",$stmt),$company,$address,$city,$state,$zip,$phone,$fax,$email,$url,$bar,$epa,$partstax,$labortax,$sublettax,$warrmos,$warrmiles,$storage,$haz,$pswarr,$sathrs,$sunhrs,$dailyhrs,$daysopen,$shopid);
	
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("sssssddddsssssssssssssssdddssddsssss",$definvmsgemail,$chargehst,$chargegst,$chargepst,$chargeqst,$qst,$hst,$gst,$pst,$hstapplyon,$pstapplyon,$gstapplyon,$qstapplyon,$company,$address,$city,$state,$zip,$phone,$fax,$email,$url,$bar,$epa,$partstax,$labortax,$sublettax,$warrmos,$warrmiles,$storage,$haz,$pswarr,$mstrinterface,$userpassword,$ein,$shopid);
	    if (!$query->execute()){
	    	echo $conn->errno;
	    }
	    $conn->commit();
	    $query->close();


	    $stmt = "delete from shophours where shopid=?";
        if ($query = $conn->prepare($stmt))
     	{
	 	   $query->bind_param('s',$shopid);
		   $query->execute();
		   $conn->commit();
		   $query->close();
	    }

        if ($redis) {
            $CompanySettings = "shop:$shopid:company";
            $companySettingsArr = [
                'definvmsgemail' => $definvmsgemail,
                'chargehst' => $chargehst,
                'chargegst' => $chargegst,
                'chargepst' => $chargepst,
                'chargeqst' => $chargeqst,
                'qst' => $qst,
                'hst' => $hst,
                'gst' => $gst,
                'pst' => $pst,
                'hstapplyon' => $hstapplyon,
                'pstapplyon' => $pstapplyon,
                'gstapplyon' => $gstapplyon,
                'qstapplyon' => $qstapplyon,
                'companyname' => $company,
                'companyaddress' => $address,
                'companycity' => $city,
                'companystate' => $state,
                'companyzip' => $zip,
                'companyphone' => $phone,
                'companyfax' => $fax,
                'companyemail' => $email,
                'companyurl' => $url,
                'barno' => $bar,
                'epano' => $epa,
                'defaulttaxrate' => $partstax,
                'defaultlabortaxrate' => $labortax,
                'defaultsublettaxrate' => $sublettax,
                'defaultwarrmos' => $warrmos,
                'defaultwarrmiles' => $warrmiles,
                'storagefee' => $storage,
                'hazardouswaste' => $haz,
                'PSWarrDays' => $pswarr,
                'masterinterface' => $mstrinterface,
                'UserPassword' => $userpassword,
                'einno' => $ein
            ];
            $redis->hMSet($CompanySettings, $companySettingsArr);
            $redis->expire($CompanySettings, $redis->longTermExpiry);
        }


	    for($i=0;$i<=6;$i++)
	    {
	    	if(!empty($_POST['open'.$i]) && !empty($_POST['close'.$i]) && $_POST['open'.$i]!='00:00' && $_POST['close'.$i]!='00:00')
	    	{
	    	 $stmt = "insert into shophours (shopid,day,start,end) values (?,?,?,?)";
     		 if ($query = $conn->prepare($stmt))
     		 {
	 		   $query->bind_param('siss',$shopid,$i,$_POST['open'.$i],$_POST['close'.$i]);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			 }
		    }
	    }

	    //Notification
	     $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='125'";
		 $query = $conn->prepare($stmt);
		 $query->execute();
		 $query->store_result();
		 $numrows = $query->num_rows();
		 if ($numrows > 0)
		 {
			$query->bind_result($textcontent,$emailcontent,$popupcontent);
	        $query->fetch();
			$stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'125',?,?,?)";
     		if ($query = $conn->prepare($stmt))
     		{
	 		   $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
			   $query->execute();
			   $conn->commit();
			   $query->close();
			}

		 }
	    
		echo "success";
	
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

}else{


	$shopid = $_POST['shopid'];
	$id = $_POST['id'];
	$v = $_POST['val'];

	$stmt = "update company set ".$id." = '".$v."' where shopid = ?";
	echo $stmt;
	
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
	    if ($query->execute()){
	    	echo $conn->errno;
	    }
	    $conn->commit();
        if ($redis){
            $CompanySettings = "shop:$shopid:company";
            $redis->hSet($CompanySettings, $id, $v);
        }
	    $query->close();
	    
		echo "success";
	
	}else{
		echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
	
	
}


mysqli_close($conn);

?>