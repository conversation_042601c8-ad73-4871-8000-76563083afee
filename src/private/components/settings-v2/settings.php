<main id="reports" class="min-vh-100">
    <div class="d-flex justify-content-between">
        <div class="title">
            <h2>Company Information</h2>
        </div>
    </div>
    <form id="mainform" class="form-horizontal push-10-t" name="mainform">
        <input type="hidden" name="shopid" value="<?php echo $shopid; ?>" id="shopid">
        <div class="row mt-4">
            <div class="col-lg-4 col-md-6">
                <div class="form-outline mb-4">
                    <input class="form-control" type="text"
                           value="<?php echo $name; ?>" id="company" name="company">
                    <label for="company" class="form-label">Company Name</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" value="<?php echo $address; ?>" type="text" id="address"
                           name="address">
                    <label for="address" class="form-label">Address</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control"
                           value="<?php echo $city; ?>" type="text" id="city" name="city">
                    <label for="city" class="form-label">City</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" type="text"
                           value="<?php echo $state; ?>" id="state" name="state">
                    <label for="state" class="form-label">State</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" type="text" value="<?php echo $zip; ?>" id="zip"
                           name="zip">
                    <label class="form-label" for="zip" id="cityfloatinglabel">Zip/Postal Code</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" type="tel"
                           value="<?php echo $phone; ?>" id="phone" name="phone" data-mdb-input-mask="(*************">
                    <label for="phone" class="form-label" id="statefloatinglabel">Main Phone</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" readonly disabled
                           type="text" value="<?php echo formatPhone($smsnum); ?>" id="sms_number" name="">
                    <label for="sms_number" class="form-label" id="sms_number">SMS Number</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" type="text" data-mdb-input-mask="(*************"
                           value="<?php echo $fax; ?>" id="fax" name="fax">
                    <label class="form-label" for="fax">Fax</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" type="text"
                           value="<?php echo $email; ?>" id="email" name="email">
                    <label class="form-label" for="email">Shop Email</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" type="text"
                           value="<?php echo $url; ?>" id="url" name="url">
                    <label for="url" class="form-label">Shop Website</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" type="text"
                           value="<?php echo $epa; ?>" id="epa" name="epa">
                    <label class="form-label" for="epa">EPA Number</label>
                </div>

                <div class="form-group mb-4">
                    <?php if ((int)$shopid > 5000) {
                        $d = 'disabled';
                    } else {
                        $d = '';
                    }
                    ?>
                    <select <?php echo $d; ?> class="select" id="mstrinterface"
                                              style="padding:9px;" name="mstrinterface" size="1">
                        <?php
                        if (strtolower($mstrinterface) == "modern") {
                            $modern = " selected='selected' ";
                            $classic = "";
                        } else {
                            $modern = "";
                            $classic = " selected='selected' ";
                        }
                        ?>

                        <option <?php echo $modern; ?> value="modern">Modern</option>
                        <option <?php echo $classic; ?> value="classic">Classic</option>
                    </select>
                    <label for="mstrinterface" class="form-label select-label">Interface</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" type="text"
                           value="<?php echo $bar; ?>" id="bar" name="bar">
                    <label for="bar" class="form-label">State/Local License #</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" type="text"
                           value="<?php echo $ein; ?>" id="ein" name="ein">
                    <label for="ein" class="form-label">EIN Number</label>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="form-outline mb-4">
                    <input class="form-control" type="text"
                           value="<?php echo $warrmos; ?>" id="warrmos" name="warrmos">
                    <label for="warrmos" class="form-label">Warranty Months</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" type="text"
                           value="<?php echo $warrmiles; ?>" id="warrmiles" name="warrmiles">
                    <label class="form-label" for="warrmiles">Warranty Miles</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" type="text"
                           value="<?php echo $pswarr; ?>" id="pswarr" name="pswarr">
                    <label class="form-label" for="pswarr">Part Sale Warranty in Days</label>
                </div>
                <div class="row mb-4">
                    <div class="col-md-7">
                        <div class="form-outline">
                            <input class="form-control" type="text"
                                   value="<?php echo $storagefee; ?>" id="storage" name="storage">
                            <label for="storage" class="form-label">Storage Fee</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check form-switch mt-1">
                            <input onchange="save(this.id)" <?= strtolower($storagetaxable) == 'yes' ? 'checked' : '' ?>
                                   class="form-check-input"
                                   type="checkbox"
                                   role="switch"
                                   id="storagetaxable"/>
                            <label class="form-check-label" for="storagetaxable">Taxable?</label>
                        </div>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-7">
                        <div class="form-outline">
                            <input class="form-control" type="text"
                                   value="<?php echo $hazwaste; ?>" id="haz" name="haz">
                            <label class="form-label" for="haz">Hazardous Waste</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check form-switch mt-1">
                            <input class="form-check-input" role="switch" type="checkbox" id="hazwastetaxable"
                                   onchange="save(this.id)" <?= strtolower($hazwastetaxable) == 'yes' ? 'checked' : ''; ?> />
                            <label class="form-check-label" for="hazwastetaxable">Taxable?</label>
                        </div>
                    </div>

                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" type="text"
                           value="<?php echo $parttax; ?>" id="partstax" name="partstax">
                    <label class="form-label" for="partstax">Parts Tax Rate (Ex. 6.75)</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" type="text"
                           value="<?php echo $labortax; ?>" id="labortax" name="labortax">
                    <label class="form-label" for="labortax">Labor Tax Rate (Ex. 6.75)</label>
                </div>
                <div class="form-outline mb-4">
                    <input class="form-control" type="text"
                           value="<?php echo $sublettax; ?>" id="sublettax" name="sublettax">
                    <label class="form-label" for="sublettax">Sublet Tax Rate (Ex. 6.75)</label>
                </div>
                <div class="accordion mb-4" id="accordion-cantax">
                    <div class="accordion-item">
                        <div class="accordion-header" id="flush-cantax">
                            <button class="accordion-button collapsed p-3" type="button"
                                    data-mdb-toggle="collapse"
                                    data-mdb-target="#cantax" aria-expanded="false" aria-controls="advisors">
                                <h3 class="mb-0">Canadian Tax
                                    <i class="fa fa-circle-info" title="Canadian Sales Tax Only. Values in these boxes will overwrite above tax rates<br>In setting the correct rates for Canada, you MUST enter the tax rate And turn on the tax type below." data-mdb-toggle="tooltip" data-mdb-html="true"></i>
                                </h3>
                            </button>
                        </div>
                        <div id="cantax" class="accordion-collapse collapse" aria-labelledby="flush-headingOne"
                             data-mdb-parent="#cantax">
                            <div class="accordion-body">
                                <div class="row mb-4">
                                    <div class="col-md-3 me-2">
                                        <div class="form-outline">
                                            <input class="form-control"
                                                   onblur="if(this.value.length === 0){this.value=0; this.classList.add('active')}"
                                                   type="text" value="<?= !empty($hst) ? $hst : ''; ?>" id="hst"
                                                   name="hst">
                                            <label class="form-label" for="hst">HST</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-check p-0 pt-1">
                                            <input type="checkbox" name="hstapplyon[]" id="phst"
                                                   class="form-check-input"
                                                   value="P" <?= stripos($hstapplyon, 'P') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="phst">Parts</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 p-0 pt-1">
                                        <div class="form-check">
                                            <input type="checkbox" id="lhst" name="hstapplyon[]"
                                                   class="form-check-input"
                                                   value="L" <?= stripos($hstapplyon, 'L') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="lhst">Labor</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 p-0 pt-1">
                                        <div class="form-check">
                                            <input type="checkbox" name="hstapplyon[]" class="form-check-input"
                                                   id="shst"
                                                   value="S" <?= stripos($hstapplyon, 'S') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="shst">Sublet</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 p-0 pt-1">
                                        <div class="form-check">
                                            <input type="checkbox" name="hstapplyon[]" class="form-check-input"
                                                   id="ahst"
                                                   value="A" <?= stripos($hstapplyon, 'A') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="ahst">All</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-4">
                                    <div class="col-md-3 me-2">
                                        <div class="form-outline">
                                            <input class="form-control"
                                                   onblur="if(this.value.length === 0){this.value=0; this.classList.add('active')}"
                                                   type="text" value="<?= !empty($gst) ? $gst : ''; ?>" id="gst"
                                                   name="gst">
                                            <label class="form-label" for="gst">GST</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-check p-0 pt-1">
                                            <input type="checkbox" name="gstapplyon[]" id="pgst"
                                                   class="form-check-input"
                                                   value="P" <?= stripos($gstapplyon, 'P') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="pgst">Parts</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 p-0 pt-1">
                                        <div class="form-check">
                                            <input type="checkbox" id="lgst" name="gstapplyon[]"
                                                   class="form-check-input"
                                                   value="L" <?= stripos($gstapplyon, 'L') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="lgst">Labor</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 p-0 pt-1">
                                        <div class="form-check">
                                            <input type="checkbox" name="gstapplyon[]" class="form-check-input"
                                                   id="sgst"
                                                   value="S" <?= stripos($gstapplyon, 'S') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="sgst">Sublet</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 p-0 pt-1">
                                        <div class="form-check">
                                            <input type="checkbox" name="gstapplyon[]" class="form-check-input"
                                                   id="agst"
                                                   value="A" <?= stripos($gstapplyon, 'A') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="agst">All</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-4">
                                    <div class="col-md-3 me-2">
                                        <div class="form-outline">
                                            <input class="form-control"
                                                   onblur="if(this.value.length === 0){this.value=0; this.classList.add('active')}"
                                                   type="text" value="<?= !empty($pst) ? $pst : ''; ?>" id="pst"
                                                   name="pst">
                                            <label class="form-label" for="pst">PST</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-check p-0 pt-1">
                                            <input type="checkbox" name="pstapplyon[]" id="ppst"
                                                   class="form-check-input"
                                                   value="P" <?= stripos($pstapplyon, 'P') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="ppst">Parts</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 p-0 pt-1">
                                        <div class="form-check">
                                            <input type="checkbox" id="lpst" name="pstapplyon[]"
                                                   class="form-check-input"
                                                   value="L" <?= stripos($pstapplyon, 'L') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="lpst">Labor</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 p-0 pt-1">
                                        <div class="form-check">
                                            <input type="checkbox" name="pstapplyon[]" class="form-check-input"
                                                   id="spst"
                                                   value="S" <?= stripos($pstapplyon, 'S') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="spst">Sublet</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 p-0 pt-1">
                                        <div class="form-check">
                                            <input type="checkbox" name="pstapplyon[]" class="form-check-input"
                                                   id="apst"
                                                   value="A" <?= stripos($pstapplyon, 'A') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="apst">All</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mb-4">
                                    <div class="col-md-3 me-2">
                                        <div class="form-outline">
                                            <input class="form-control"
                                                   onblur="if(this.value.length === 0){this.value=0; this.classList.add('active')}"
                                                   type="text" value="<?= !empty($qst) ? $qst : ''; ?>" id="qst"
                                                   name="qst">
                                            <label class="form-label" for="hst">QST</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-check p-0 pt-1">
                                            <input type="checkbox" name="qstapplyon[]" id="pqst"
                                                   class="form-check-input"
                                                   value="P" <?= stripos($qstapplyon, 'P') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="pqst">Parts</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 p-0 pt-1">
                                        <div class="form-check">
                                            <input type="checkbox" id="lqst" name="qstapplyon[]"
                                                   class="form-check-input"
                                                   value="L" <?= stripos($qstapplyon, 'L') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="lqst">Labor</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 p-0 pt-1">
                                        <div class="form-check">
                                            <input type="checkbox" name="qstapplyon[]" class="form-check-input"
                                                   id="sqst"
                                                   value="S" <?= stripos($qstapplyon, 'S') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="sqst">Sublet</label>
                                        </div>
                                    </div>
                                    <div class="col-md-2 p-0 pt-1">
                                        <div class="form-check">
                                            <input type="checkbox" name="qstapplyon[]" class="form-check-input"
                                                   id="aqst"
                                                   value="A" <?= stripos($qstapplyon, 'A') !== false ? "checked='checked'" : '' ?>>
                                            <label class="form-check-label" for="aqst">All</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group mb-4">
                                    <div class="form-check  form-switch form-check-inline">
                                        <input type="checkbox" id="chargehst" class="form-check-input" name="chargehst"
                                               onchange="save(this.id)"
                                               value="yes" <?= $chargehst == "yes" ? "checked" : "" ?>>
                                        <label for="chargehst" class="form-check-label">HST</label>
                                    </div>
                                    <div class="form-check  form-switch form-check-inline">
                                        <input type="checkbox" id="chargegst" name="chargegst" class="form-check-input"
                                               onchange="save(this.id)"
                                               value="yes" <?= $chargegst == "yes" ? "checked" : ""; ?>>
                                        <label for="chargegst" class="form-check-label">GST</label>
                                    </div>
                                    <div class="form-check  form-switch form-check-inline">
                                        <input type="checkbox" id="chargepst" name="chargepst" class="form-check-input"
                                               onchange="save(this.id)"
                                               value="yes" <?= $chargepst == "yes" ? "checked" : "" ?>>
                                        <label for="chargepst" class="form-check-label">PST</label>
                                    </div>
                                    <div class="form-check  form-switch form-check-inline">
                                        <input type="checkbox" id="chargeqst" name="chargeqst" class="form-check-input"
                                               onchange="save(this.id)"
                                               value="yes" <?= $chargeqst == "yes" ? "checked" : "" ?>>
                                        <label for="chargeqst" class="form-check-label">QST</label>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="input-group mb-4">
                    <div class="form-outline">
                        <input class="form-control rounded" type="text" value="<?php echo $kioskpassword; ?>"
                               id="kioskpassword"
                               name="kioskpassword">

                        <label class="form-label" for="kioskpassword">Kiosk Password</label>
                    </div>
                    <div class="input-group-text border-0">
                        <i class="fa fa-circle-info" title="At least 6 characters, one capital letter and one number" data-mdb-toggle="tooltip"></i>
                    </div>
                </div>
                <div class="row">
                    <h3>
                        Shop Hours <i class="fa fa-circle-info" title="Hours are in Military/24 hours format" data-mdb-toggle="tooltip" data-mdb-html="true"></i>
                    </h3>
                </div>
                <div class="row mb-4">
                    <div class="col-sm-2"></div>
                    <div class="col-sm-5 text-center"><b>Open</b></div>
                    <div class="col-sm-5 text-center"><b>Close</b></div>
                    <br>
                    <div class='col-sm-2'>Mon:</div>
                    <div class='col-sm-5'>
                        <div class="form-outline" data-mdb-inline="true">
                            <input type="text" name="open1" class="timepick form-control"  value="<?= isset($shophours['1']) ? $shophours['1']['start'] : '' ?>">
                        </div>
                    </div>
                    <div class='col-sm-5'>
                        <div class="form-outline" data-mdb-inline="true">
                            <input type="text" class="timepick form-control" name="close1"  value="<?= isset($shophours['1']) ? $shophours['1']['end'] : '' ?>">
                        </div>
                    </div>
                    <br><br>
                    <div class='col-sm-2'>Tue:</div>
                    <div class='col-sm-5'>
                        <div class="form-outline" data-mdb-inline="true">
                            <input type="text" name="open2" class="timepick form-control" 
                                   value="<?= isset($shophours['2']) ? $shophours['2']['start'] : '' ?>">
                        </div>
                    </div>
                    <div class='col-sm-5'>
                        <div class="form-outline" data-mdb-inline="true">
                            <input type="text" class="timepick form-control" name="close2" 
                                   value="<?= isset($shophours['2']) ? $shophours['2']['end'] : '' ?>">
                        </div>
                    </div>
                    <br><br>
                    <div class='col-sm-2'>Wed:</div>
                    <div class='col-sm-5'>
                        <div class="form-outline" data-mdb-inline="true">
                            <input type="text" name="open3" class="timepick form-control" 
                                   value="<?= isset($shophours['3']) ? $shophours['3']['start'] : '' ?>">
                        </div>
                    </div>
                    <div class='col-sm-5'>
                        <div class="form-outline" data-mdb-inline="true">
                            <input type="text" class="timepick form-control" name="close3" 
                                   value="<?= isset($shophours['3']) ? $shophours['3']['end'] : '' ?>">
                        </div>
                    </div>
                    <br><br>
                    <div class='col-sm-2'>Thu:</div>
                    <div class='col-sm-5'>
                        <div class="form-outline" data-mdb-inline="true">
                            <input type="text" name="open4" class="timepick form-control" 
                                   value="<?= isset($shophours['4']) ? $shophours['4']['start'] : '' ?>">
                        </div>
                    </div>
                    <div class='col-sm-5'>
                        <div class="form-outline" data-mdb-inline="true">
                            <input type="text" class="timepick form-control" name="close4" 
                                   value="<?= isset($shophours['4']) ? $shophours['4']['end'] : '' ?>">
                        </div>
                    </div>
                    <br><br>
                    <div class='col-sm-2'>Fri:</div>
                    <div class='col-sm-5'>
                        <div class="form-outline" data-mdb-inline="true">
                            <input type="text" name="open5" class="timepick form-control" 
                                   value="<?= isset($shophours['5']) ? $shophours['5']['start'] : '' ?>">
                        </div>
                    </div>
                    <div class='col-sm-5'>
                        <div class="form-outline" data-mdb-inline="true">
                            <input type="text" class="timepick form-control" name="close5" 
                                   value="<?= isset($shophours['5']) ? $shophours['5']['end'] : '' ?>">
                        </div>
                    </div>
                    <br><br>
                    <div class='col-sm-2'>Sat:</div>
                    <div class='col-sm-5'>
                        <div class="form-outline" data-mdb-inline="true">
                            <input type="text" name="open6" class="timepick form-control" 
                                   value="<?= isset($shophours['6']) ? $shophours['6']['start'] : '' ?>">
                        </div>
                    </div>
                    <div class='col-sm-5'>
                        <div class="form-outline" data-mdb-inline="true">
                            <input type="text" class="timepick form-control" name="close6" 
                                   value="<?= isset($shophours['6']) ? $shophours['6']['end'] : '' ?>">
                        </div>
                    </div>
                    <br><br>
                    <div class='col-sm-2'>Sun:</div>
                    <div class='col-sm-5'>
                        <div class="form-outline" data-mdb-inline="true">
                            <input type="text" name="open0" class="timepick form-control" 
                                   value="<?= isset($shophours['0']) ? $shophours['0']['start'] : '' ?>">
                        </div>
                    </div>
                    <div class='col-sm-5'>
                        <div class="form-outline" data-mdb-inline="true">
                            <input type="text" class="timepick form-control" name="close0" 
                                   value="<?= isset($shophours['0']) ? $shophours['0']['end'] : '' ?>">
                        </div>
                    </div>
                </div>
                <div class="form-outline mb-4">
                            <textarea class="form-control" rows="5"
                                      type="text" id="definvmsgemail"
                                      name="definvmsgemail"><?php echo $definvmsgemail; ?></textarea>
                    <label class="form-label" for="definvmsgemail">Default Email Message</label>
                </div>
                <?php if ($matco == 'yes') { ?>
                    <br><br><br>
                    <div class="form-group" style="margin-top:10px;">
                            <span class="label label-success"
                                  style="font-size:15px; margin-left: 20px;">Matco API Key: <?= $matcokey ?></span>
                    </div>
                <?php } ?>

                <div class="form-group">
                    <button class="btn btn-primary btn-md float-end" onclick="saveSettings()" type="button">Save</button>
                </div>
            </div>

        </div>

    </form>
</main>
<!-- END Main Container -->

<!-- Apps Modal -->
<div id="carfaxmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <input id="customerid" type="hidden">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="block block-themed block-transparent remove-margin-b">
                <div class="block-header bg-primary-light">
                    <ul class="block-options">
                        <li>
                            <button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
                        </li>
                    </ul>
                    <h3 class="block-title">Carfax Vehicle Lookup</h3>
                </div>
                <div id="vehinfo" class="block-content"></div>
                <div class="block-content">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <input class="form-control"
                                           type="text" id="cfvin" name="cfvin">
                                    <label for="material-text2">VIN</label>
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <input class="form-control"
                                           type="text" id="cflic" name="cflic">
                                    <label for="material-text2">License</label>
                                </div>
                            </div>
                            <div class="col-sm-12">
                                <div class="form-material floating">
                                    <input class="form-control"
                                           type="text" id="cfst" name="cfst">
                                    <label for="material-text2">License State</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="margin-top:20px;" class="modal-footer">
                <button class="btn btn-info btn-md" type="button" onclick="scanVIN()">SCAN VIN</button>
                <button class="btn btn-md btn-warning" type="button" onclick="addVehicle()">Lookup</button>
                <button class="btn btn-md btn-default" type="button" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>
