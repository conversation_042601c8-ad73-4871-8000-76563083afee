<script>
const iframe = document.getElementById("invoiceFrame");
const baseUrl = iframe.src.split('?')[0]; // Get base URL without parameters
let iframeParams = new URLSearchParams();

const iframeSRC = (name, value) => {
    iframeParams.set(name, value);

    // Check if URL would be too long (limit to ~2000 characters to be safe)
    const newUrl = baseUrl + '?' + iframeParams.toString();
    if (newUrl.length > 2000) {
        console.warn('URL too long, using POST method instead');
        // Use POST method to send data
        sendDataViaPost();
        return;
    }

    iframe.src = newUrl;
};

// Function to send data via POST when URL gets too long
const sendDataViaPost = () => {
    const form = document.createElement('form');
    form.method = 'POST';
    form.target = 'invoiceFrame';
    form.action = baseUrl;
    form.style.display = 'none';

    // Add all parameters as hidden inputs
    for (const [key, value] of iframeParams.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
    }

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
};

document.addEventListener("DOMContentLoaded", function () {
    // Base Sizes
    const baseLogoSize = 90;

    // Company Settings
    const companyLogoAlign = document.querySelector("#companyPlacement");
    const showCompanyLogo = document.querySelector("#showCompanyLogo");
    const logoSize = document.querySelector("#logoSize");

    iframeSRC("companyLogoAlign", companyLogoAlign.value);
    companyLogoAlign.addEventListener("change", function () {
        iframeSRC("companyLogoAlign", this.value);
    });

    iframeSRC("showCompanyLogo", showCompanyLogo.checked);
    showCompanyLogo.addEventListener("change", function () {
        iframeSRC("showCompanyLogo", this.checked);
    });

    iframeSRC("companyLogoSize", (baseLogoSize + parseInt(logoSize.value)) + "px");
    logoSize.addEventListener("input", function () {
        let newSize = (baseLogoSize + parseInt(this.value)) + "px";

        iframeSRC("companyLogoSize", newSize);
    });
    

    // Font Settings
    let headerFontSize = 11;
    let titleFontSize = 18;

    document.getElementById("fontStyle").addEventListener("change", function () {       
        iframeSRC("fontStyle", this.value);
    });

    document.getElementById("headerFontSize").addEventListener("input", function () {
        let newSize = (headerFontSize + parseInt(this.value)) + "px";
        
        iframeSRC("headerFontSize", newSize);
    });

    document.getElementById("invoiceTitleFontSize").addEventListener("input", function () {
        let newSize = (titleFontSize + parseInt(this.value)) + "px";
        
        iframeSRC("titleFontSize", newSize);
    });

    let contentFontSize = 10;

    document.getElementById("contentFontSize").addEventListener("input", function () {
        let newSize = (contentFontSize + parseInt(this.value)) + "px";
        
        iframeSRC("bodyFontSize", newSize);
    });

    let footerFontSize = 9;
    document.getElementById("footerFontSize").addEventListener("input", function () {
        let newSize = (footerFontSize + parseInt(this.value)) + "px";
        
        iframeSRC("footerFontSize", newSize);
    });

    document.getElementById("disclosureFontSize").addEventListener("input", function () {
        let newSize = (contentFontSize + parseInt(this.value)) + "px";
        
        iframeSRC("disclosureFontSize", newSize);
    });


    // Color Settings
    const contentColor = document.getElementById("contentColor");
    iframeSRC("bodyColor", contentColor.value);

    contentColor.addEventListener("input", function () {
        iframeSRC("bodyColor", this.value);
    });

    const invoiceTitleColor = document.getElementById("invoiceTitleColor");
    iframeSRC("invoiceTitleColor", invoiceTitleColor.value);

    invoiceTitleColor.addEventListener("input", function () {
        iframeSRC("invoiceTitleColor", this.value);
    });

    const tableHeaderColor = document.getElementById("tableHeaderColor");
    iframeSRC("tableHeaderColor", tableHeaderColor.value);

    tableHeaderColor.addEventListener("input", function () {
        iframeSRC("tableHeaderColor", this.value);
    });

    const tableHeaderBgColor = document.getElementById("tableHeaderBackground");
    iframeSRC("tableHeaderBgColor", tableHeaderBgColor.value);

    tableHeaderBgColor.addEventListener("input", function () {
        iframeSRC("tableHeaderBgColor", this.value);
    });

    // Disclosure Settings
    const customDisclosurePage = document.getElementById('customDisclosurePage');
    const disclosureBox = document.getElementById('disclosureBox');
    const disclosure = document.getElementById('disclosure');
    const basicDisclosures = document.getElementById('basicDisclosures');

    disclosureBox.style.display = customDisclosurePage.checked ? 'block' : 'none';
    disclosureBox.style.display = customDisclosurePage.checked ? 'block' : 'none';
    basicDisclosures.style.display = customDisclosurePage.checked ? 'none' : 'block';
    iframeSRC("newPageForDisclosure", customDisclosurePage.checked);
    
    customDisclosurePage.addEventListener("change", function () {
        disclosureBox.style.display = this.checked ? 'block' : 'none';
        basicDisclosures.style.display = this.checked ? 'none' : 'block';

        iframeSRC("newPageForDisclosure", this.checked);
    });


    const signatureDisclosure = document.getElementById("signatureDisclosure");
    iframeSRC("signatureDisclosure", signatureDisclosure.value);

    signatureDisclosure.addEventListener("input", function () {
        iframeSRC("signatureDisclosure", this.value);
    });

    const warrantyDisclosure = document.getElementById("warrantyDisclosure");
    iframeSRC("warrantyDisclosure", warrantyDisclosure.value);

    warrantyDisclosure.addEventListener("input", function () {
        iframeSRC("warrantyDisclosure", this.value);
    });

    // Header | Footer Margins
    const headerSpace = document.getElementById("headerSpace");
    const footerSpace = document.getElementById("footerSpace");
    iframeSRC("headerSpace", headerSpace.value);
    iframeSRC("footerSpace", footerSpace.value);

    headerSpace.addEventListener("input", function () {
        iframeSRC("headerSpace", this.value);
    });

    footerSpace.addEventListener("input", function () {
        iframeSRC("footerSpace", this.value);
    });

    // QR Text
    const qrText = document.getElementById("qr_text");
    iframeSRC("qr", qrText.value);

    qrText.addEventListener("input", function () {
         iframeSRC("qr", this.value);
    });

    // Watermark Settings
    const watermark = document.getElementById("watermark");
    iframeSRC("whatermark", watermark.checked);

    watermark.addEventListener("change", function () {
        iframeSRC("whatermark", this.checked);
    });

    
    // Slider Events
    // const slider = document.getElementById('logoSize');
    // updateSliderBubble(slider, baseLogoSize);
    initializeSlider();
});

function updateSliderBubble(rangeInput, baseSize) {
    const parentDiv = rangeInput.parentElement;
    const bubble = parentDiv.querySelector(".range-bubble");

    const sliderWidth = rangeInput.offsetWidth;
    const thumbWidth = 60;

    const min = parseInt(rangeInput.min);
    const max = parseInt(rangeInput.max);

    const val = parseInt(rangeInput.value);
    const base = parseInt(baseSize);

    const ratio = (val - min) / (max - min);
    const posX = ratio * (sliderWidth - thumbWidth) + thumbWidth / 2;

    bubble.style.left = `${posX}px`;
    bubble.style.transform = 'translateX(-50%)';

    if ( baseSize ){
        if (isNaN(val)) {
            bubble.innerText = base + "px";
        } else {
            bubble.innerText = (val + base) + "px";
        }
    }
}

function initializeSlider(rangeInput, baseSize) {
    const slider = document.querySelectorAll(".slider input[type='range']");
    
    slider.forEach(element => {
        updateSliderBubble(element, 0); 
    });
}

window.addEventListener('resize', () => {
    initializeSlider()
});

// Invoice Preferences
function savePreference(id, value, method){
    console.log(id, value, method);
    
    const requestData = {};
    requestData[id] = value;
    
    let changeIframe = id + value + method;

    $.ajax({
        url: "/src/private/components/onboarding-wizard-v2/api/routes.php",
        type: 'POST',
        data: {
            action: method, 
            data: requestData
        },
        success: function(response) {
            iframeSRC(changeIframe, value);
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
        }
    });
}

document.getElementById('disclosurePDF').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Show file selection feedback
        const container = document.querySelector('.disclosure-upload-container');
        const existingStatus = container.querySelector('.disclosure-status, .disclosure-upload-prompt');

        // Create temporary status showing selected file
        const tempStatus = document.createElement('div');
        tempStatus.className = 'disclosure-status mb-3 p-3 border rounded bg-warning-subtle';
        tempStatus.innerHTML = `
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <i class="fa-solid fa-file-pdf text-warning me-2"></i>
                    <strong>Selected: ${file.name}</strong>
                    <div class="text-muted small">
                        Size: ${(file.size / 1024).toFixed(1)} KB | Ready to upload
                    </div>
                </div>
                <div onclick="document.getElementById('disclosurePDF').click();">
                    <span class="badge bg-warning">Pending Upload</span>
                </div>
            </div>
        `;

        // Replace existing status with temp status
        existingStatus.replaceWith(tempStatus);

        // Show save button or trigger form submission hint
        const saveButton = document.querySelector('button[type="submit"], .btn-success');
        if (saveButton) {
            saveButton.classList.add('btn-pulse');
            saveButton.innerHTML = '<i class="fa-solid fa-save me-1"></i>Save Changes to Upload PDF';
        }
    }
});

function saveInvoice() {
    const form = document.getElementById('invoiceForm');
    const formData = new FormData(form);
    
    fetch('api/saveInvoice.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        console.log('Success:', data);
        if (data.success) {
            sbalert('Settings saved successfully!');
        } else {
            console.log('Error: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        sbalert('Error saving settings: ' + error.message, 'error');
    });
}
</script>