<li class="sidenav-item">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/wip/wip.php">
        <i class="fas fa-wrench fa-lg"></i><span class="ms-2">Work In Process</span>
    </a>
</li>
<li class="sidenav-item <?= ($menuItem == 'company') ? "sidenav-selected" : "" ?>">
    <a href="<?= COMPONENTS_PRIVATE ?>/v2/settings/settings.php" class="sidenav-link ripple-surface-primary">
        <i class="fas fa-building fa-lg"></i><span class="ms-2">Company</span>
    </a>
</li>
<li class="sidenav-item">
    <a id="customsettings" class="sidenav-link" href=""><i class="fa-solid fa-gears fa-lg"></i><span class="ms-2">Custom</span></a>
    <ul class="sidenav-collapse <?= ($menuItem == "custom_settings") ? "show" : "" ?>" id="custom_settings_collapse">
        <li class="sidenav-item">
            <a class="sidenav-link <?= ($curr_page == 'goals.php') ? "sidenav-selected" : "" ?>" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/goals.php"><i class="fa fa-xxs fa-circle"></i>Monthly Sales
                Goals</a>
        </li>
        <li class="sidenav-item <?= ($curr_page == 'printingprefs.php') ? "sidenav-selected" : "" ?>">
            <a class="sidenav-link" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/printingprefs.php"><i class="fa fa-xxs fa-circle"></i>Printing</a>
        </li>
        <?php if($oilchangestickers == 'yes'){?>
        <li class="sidenav-item <?= ($curr_page == 'oil-change-sticker.php') ? "sidenav-selected" : "" ?>">
            <a class="sidenav-link" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/oil-change-sticker.php"><i class="fa fa-xxs fa-circle"></i>Oil Change Sticker</a>
        </li>
        <?php }?>
        <li class="sidenav-item <?= ($curr_page == 'customfields.php') ? "sidenav-selected" : "" ?>">
            <a class="sidenav-link" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/customfields.php"><i class="fa fa-xxs fa-circle"></i>Custom Fields</a>
        </li>
        <li class="sidenav-item <?= ($curr_page == 'disclosures.php') ? "sidenav-selected" : "" ?>">
            <a class="sidenav-link" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/disclosures.php"><i class="fa fa-xxs fa-circle"></i>Disclosures</a>
        </li>
        <li class="sidenav-item <?= ($curr_page == 'roprefs.php') ? "sidenav-selected" : "" ?>">
            <a class="sidenav-link" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/roprefs.php"><i class="fa fa-xxs fa-circle"></i>Repair Order (RO)</a>
        </li>
        <li class="sidenav-item <?= ($curr_page == 'misc.php') ? "sidenav-selected" : "" ?>">
            <a class="sidenav-link" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/misc.php"><i class="fa fa-xxs fa-circle"></i>Miscellaneous</a>
        </li>
        <li class="sidenav-item <?= ($curr_page == 'concerncats.php') ? "sidenav-selected" : "" ?>">
            <a class="sidenav-link" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/concerncats.php"><i class="fa fa-xxs fa-circle"></i>Concern Categories</a>
        </li>
        <?php
        if ($merchant == 'authorize.net') {
            ?>
            <li class="sidenav-item">
                <a class="sidenav-link <?= ($curr_page == 'auth.net.php.php') ? "sidenav-selected" : "" ?>" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/auth.net.php"><i class="fa fa-xxs fa-circle"></i>Authorize.net
                    Credentials</a>
            </li>
            <?php
        }
        ?>
        <li class="sidenav-item">
            <a class="sidenav-link <?= ($curr_page == 'discounts.php') ? "sidenav-selected" : "" ?>" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/discounts.php"><i class="fa fa-xxs fa-circle"></i>Discount
                Type Labels</a>
        </li>
        <?php
        if ($jobdesc == 'owner') {
            ?>
            <li class="sidenav-item <?= ($curr_page == 'loginhours.php') ? "sidenav-selected" : "" ?>">
                <a class="sidenav-link" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/loginhours.php"><i class="fa fa-xxs fa-circle"></i>Login Hours</a>
            </li>
            <?php
        }
        ?>
    </ul>
</li>
<?php if ($isprofitboost == 'no' && $haspph == 'yes' && $pphAccess == 'yes') { ?>
    <li class="sidenav-item">
        <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/profitboost.php"><i class="fas fa-list fa-lg"></i> PPH
            Settings</a>
    </li>
<?php } elseif ($isprofitboost == 'yes' && $pphAccess == 'yes') { ?>
    <li class="sidenav-item">
        <a class="sidenav-link ripple-surface-primary" href=""><i class="fas fa-gauge-high fa-lg"></i><span
                    class="ms-2">Profitboost - PPH</span></a>
        <ul class="sidenav-collapse <?= ($menuItem == "profitboost_settings") ? "show" : "" ?>">
            <li class="sidenav-item <?= ($curr_page == 'accounttypes.php') ? "sidenav-selected" : "" ?>">
                <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/accounttypes.php"><i
                            class="fa fa-xxs fa-circle"></i> Account Types</a></li>
            <li class="sidenav-item <?= ($curr_page == 'sources.php') ? "sidenav-selected" : "" ?>">
                <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/sources.php"><i
                            class="fa fa-xxs fa-circle"></i> Lead Source</a></li>
            <li class="sidenav-item <?= ($curr_page == 'referralsource.php') ? "sidenav-selected" : "" ?>">
                <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/referralsource.php"><i
                            class="fa fa-xxs fa-circle"></i> Referral Source</a></li>
            <?php if ($haspph == 'yes') { ?>
                <li class="sidenav-item <?= ($curr_page == 'profitboost.php') ? "sidenav-selected" : "" ?>">
                    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/profitboost.php"><i
                                class="fa fa-xxs fa-circle"></i> PPH - Profit Per Hour</a>
                </li>
            <?php } ?>
        </ul>

    </li>
<?php } ?>
<li class="sidenav-item <?= ($curr_page == 'audit.php') ? "sidenav-selected" : "" ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/audit.php"><i class="fas fa-list fa-lg"></i><span
                class="ms-2">Audit Log</span></a>
</li>
<li class="sidenav-item <?= ($curr_page == 'lyftaccount.php') ? "sidenav-selected" : "" ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/lyftaccount.php">
        <!-- <img alt="" src="<?= IMAGE ?>/lyftlogo.png" style="max-height:30px;"> -->
        <i class="fa-brands fa-lyft fa-lg"></i><span class="ms-2">Lyft Account</span></a>
</li>
<?php
$newpackagetype = strtolower($newpackagetype);
if ($_COOKIE['dvilite'] == 'yes' && ($newpackagetype != 'silver' || ($newpackagetype == 'silver' && strtotime($datestarted) < strtotime('2022-06-09')))) {
    ?>
    <li class="sidenav-item <?= ($curr_page == 'inspections.php') ? "sidenav-selected" : "" ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/inspections.php"><i
                    class="fas fa-clipboard fa-lg"></i><span
                    class="ms-2">Inspections</span></a>
    </li>
    <?php
}
if ($integrationaccess == 'YES') {
    ?>
    <li class="sidenav-item <?= ($curr_page == 'integrations.php') ? "sidenav-selected" : "" ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/integrations.php"><i
                    class="fas fa-plus-square fa-lg"></i><span
                    class="ms-2">Integrations</span></a>
    </li>
    <?php
}
if (strtolower($_COOKIE['employeeaccess']) == "yes") {
    ?>
    <li class="sidenav-item <?= ($menuItem == 'employees') ? "sidenav-selected" : "" ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/employees.php"><i class="fas fa-users fa-lg"></i><span
                    class="ms-2">Employees</span></a>
    </li>
    <?php
}
?>
<li class="sidenav-item <?= ($curr_page == 'labortimeclock.php') ? "sidenav-selected" : "" ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/labortimeclock.php"><i
                class="fa-regular fa-clock fa-lg"></i><span
                class="ms-2">Labor Time Clock</span></a>
</li>
<li class="sidenav-item <?= ($curr_page == 'labormatrix.php') ? "sidenav-selected" : "" ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/labormatrix.php"><i class="fas fa-screwdriver-wrench"></i><span
                class="ms-2">Labor Matrix</span></a>
</li>
<li class="sidenav-item <?= ($curr_page == 'timeclock.php') ? "sidenav-selected" : "" ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/timeclock.php"><i class="fas fa-business-time fa-lg"></i><span
                class="ms-2">Employee Time Clock</span></a>
</li>
<li class="sidenav-item <?= ($curr_page == 'suppliers.php') ? "sidenav-selected" : "" ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/suppliers.php">
        <i class="fas fa-truck-fast"></i>
        <span class="ms-2">Suppliers</span></a>
</li>
<?php if ($ChangePartMatrix == "YES") { ?>
    <li class="sidenav-item <?= ($curr_page == 'matrix.php') ? "sidenav-selected" : "" ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/matrix.php"><i class="fas fa-table-cells-large"></i><span
                    class="ms-2">Parts Matrix</span></a>
    </li>
<?php }
if ($reopenro == "YES") { ?>
    <li class="sidenav-item <?= ($curr_page == 'reopenro.php') ? "sidenav-selected" : "" ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/reopenro.php"><i class="fas fa-folder-open fa-lg"></i><span
                    class="ms-2">Re-Open RO</span></a>
    </li>
<?php } ?>
<li class="sidenav-item <?= ($curr_page == 'cannedjobs_new.php') ? "sidenav-selected" : "" ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/cannedjobs_new.php"><i
                class="fas fa-object-group fa-lg"></i><span
                class="ms-2">Canned Jobs</span></a>
</li>

<li class="sidenav-item <?= ($curr_page == 'calendarcats.php') ? "sidenav-selected" : "" ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/calendarcats.php"><i class="fas fa-calendar-days"></i><span
                class="ms-2">Calendar</span></a>
</li>
<li class="sidenav-item <?= ($curr_page == 'paymentmethods.php') ? "sidenav-selected" : "" ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/paymentmethods.php"><i class="fas fa-dollar fa-lg"></i><span
                class="ms-2">Payment Methods</span></a>
</li>
<?php if ($isprofitboost == 'no' && $ChangeSources == "YES") { ?>
    <li class="sidenav-item <?= ($curr_page == 'sources.php') ? "sidenav-selected" : "" ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/sources.php"><i class="fas fa-television fa-lg"></i><span
                    class="ms-2">Sources</span></a>
    </li>
<?php } ?>
<li class="sidenav-item <?= ($curr_page == 'rotypes.php') ? "sidenav-selected" : "" ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/rotypes.php"><i class="fas fa-file-text fa-lg"></i><span
                class="ms-2">RO Types</span></a>
</li>
<li class="sidenav-item <?= ($curr_page == 'rostatuses.php') ? "sidenav-selected" : "" ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/rostatuses.php"><i class="fas fa-signal-bars fa-lg"></i><span
                class="ms-2">RO Statuses</span></a>
</li>
<li class="sidenav-item <?= ($curr_page == 'reminders.php') ? "sidenav-selected" : "" ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/reminders.php"><i class="fas fa-calendar-day"></i><span
                class="ms-2">Reminders</span></a>
</li>
<?php if ($ChangeJobDescription == "YES") { ?>
    <li class="sidenav-item <?= ($curr_page == 'jobdesc.php') ? "sidenav-selected" : "" ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/jobdesc.php"><i class="fas fa-cog fa-lg"></i><span
                    class="ms-2">Job Descriptions</span></a>
    </li>
<?php }
if ($ChangePartCodes == "YES") { ?>
    <li class="sidenav-item <?= ($curr_page == 'partcodes.php') ? "sidenav-selected" : "" ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/partcodes.php"><i class="fas fa-barcode"></i><span
                    class="ms-2">Part Codes</span></a>
    </li>
<?php } ?>
<li class="sidenav-item <?= ($curr_page == 'backup.php') ? "sidenav-selected" : "" ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/backup.php"><i class="fas fa-undo fa-lg"></i><span
                class="ms-2">Backup Data</span></a>
</li>
<?php if ($editnotifications == 'YES'): ?>
    <li class="sidenav-item <?= ($curr_page == 'notifications.php') ? "sidenav-selected" : "" ?>">
        <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/notifications.php"><i class="fas fa-bell fa-lg"></i><span
                    class="ms-2">Notifications</span></a>
    </li>
<?php endif; ?>
<li class="sidenav-item <?= ($curr_page == 'account.php') ? "sidenav-selected" : "" ?>">
    <a class="sidenav-link ripple-surface-primary" href="<?= COMPONENTS_PRIVATE ?>/v2/settings/account.php"><i class="fas fa-user fa-lg"></i><span
                class="ms-2">Account</span></a>
</li>
