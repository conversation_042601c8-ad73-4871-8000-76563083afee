<?php

$component = "settings-v2";
$sub_component = "Job Descriptions";
$shopid = $_COOKIE['shopid'];

include getRulesComponent($component);
include getHeadGlobal($component);

$empid = $_COOKIE['empid'];
if ($empid == "Admin")
    $ChangeJobDescription = "YES";
else {
    $stmt = "select upper(ChangeJobDescription) from employees where id = ? and shopid = ?";

    if ($query = $conn->prepare($stmt)) {

        $query->bind_param("is", $empid, $shopid);
        $query->execute();
        $query->bind_result($ChangeJobDescription);
        $query->fetch();
        $query->close();
    } else {
        echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
}
?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);
?>
<main id="settings" class="min-vh-100">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-9 col-sm-6">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/settings/settings.php"
                           class="text-secondary">Settings</a>
                        <span class="text-secondary ps-3 pe-3">/</span>
                        <h2 class="">Job Descriptions
                            <i class="fa fa-circle-info" title="Our standard list of job descriptions are pre-set, but you can customize them as you like" data-mdb-toggle="tooltip"></i>
                        </h2>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 justify-content-end">
                    <?php if ($ChangeJobDescription == 'YES') { ?>
                        <button class="btn btn-primary float-end" onclick="openAddDesc(0)" type="button">Add Job Description</button><?php } ?>
                </div>
            </div>
            <hr/>
        </div>
    </div>
    <div class="container-fluid">
        <div class="row mt-4">
            <div class="col-lg-10 col-md-12">
                <table id="jobdesc_table" class="sbdatatable w-100">
                    <thead>
                    <tr>
                        <th>Job Descriptions</th>
                        <?php if ($ChangeJobDescription == 'YES') { ?>
                        <th class="text-center">Delete</th>
                        <?php } ?>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                    $stmt = "select jobdesc,id from jobdesc where shopid = '" . $shopid . "' order by jobdesc asc";
                    $result = $conn->query($stmt);
                    while ($row = $result->fetch_array()) {
                        ?>
                        <tr>
                            <td onclick="openAddDesc('<?php echo $row['id']; ?>')"><?php echo $row['jobdesc']; ?></td>
                            <?php if ($ChangeJobDescription == 'YES') { ?>
                                <td class="text-center"><i class="fa fa-trash" onclick="deleteJobDesc('<?= $row['jobdesc'] ?>', '<?= $row['id'] ?>')"></i></td>
                            <?php } ?>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                </table>

            </div>
        </div>
    </div>
    <!-- Apps Modal -->
    <div id="jobdescmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
        <input id="jobdescid" type="hidden">
        <div class="modal-dialog modal-lg">
            <div class="modal-content p-4">
                <div class="modal-header">
                    <h5 class="modal-title" id="spdLabel">Job Description</h5>
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="col-sm-12">
                                <div class="form-outline mb-4">
                                    <input class="form-control" tabindex="1" type="text" id="addjobdesc" name="addjobdesc">
                                    <label class="form-label" for="addjobdesc">Job Description</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-center">
                    <?php if ($ChangeJobDescription == 'YES') { ?>
                        <button class="btn btn-md btn-primary" type="button" onclick="saveJobDesc()">Save</button>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>


    <?php
    $component = '';
    include getScriptsGlobal('');
    // include getFooterComponent($component);
    ?>
    <script>
        $(document).ready(function () {
            $(".sbdatatable").dataTable({
                responsive: true,
                fixedHeader: {
                    headerOffset: 68
                },
                searching: false,
                colReorder: true,
                select: true,
                scrollY: false,
                scrollX: false,
                scroller: false,
                paging: false,
            });
        });

        function openAddDesc(id) {
            console.log(id)
            $('#delbutton').hide()
            if (id == 0) {
                $('#jobdescmodal').modal('show')
                $('#addjobdesc').val('')
            }
            $('#jobdescid').val(id)
            if (id != 0) {
                $('#delbutton').show()
                showLoader();
                $.ajax({
                    data: "shopid=<?php echo $shopid; ?>&id=" + id + "&t=get",
                    url: "jobdescfiles/jobdesc-action.php",
                    type: "get",
                    success: function (r) {
                        console.log(r)
                        $('#addjobdesc').val(r)
                        $('#jobdescmodal').modal('show')
                        hideLoader();
                    }
                })
            } else {
                setTimeout(function () {
                    $('#addjobdesc').focus()
                }, 500)
            }
        }

        function deleteJobDesc(typename, id) {
            sbconfirm("Are you sure?", "Delete the \"" + typename + "\" Job Description?", function () {
                showLoader();
                $.ajax({
                    type: "get",
                    data: "t=delete&id=" + id + "&shopid=<?php echo $shopid; ?>",
                    url: "jobdescfiles/jobdesc-action.php",
                    success: function (r) {
                        location.reload()
                    }
                });
            });
        }

        function saveJobDesc() {
            s = encodeURIComponent($('#addjobdesc').val())
            id = $('#jobdescid').val()
            if (s == "") {
                sbalert("Please enter a job description")
            } else {
                showLoader();
                $.ajax({
                    type: "get",
                    data: "t=save&id=" + id + "&shopid=<?php echo $shopid; ?>&s=" + s,
                    url: "jobdescfiles/jobdesc-action.php",
                    success: function (r) {
                        //console.log(r)
                        location.reload()
                    }

                })
            }

        }
    </script>

</body>
</html>