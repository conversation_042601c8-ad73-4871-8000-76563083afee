<?php
$component = "settings-v2";

include getRulesComponent($component);
include getHeadGlobal($component);
$shopid = $_COOKIE['shopid'];


$stmt = "select year(datestarted) from company where shopid = ?";

if ($query = $conn->prepare($stmt)) {

    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($yearstarted);
    $query->fetch();
    $query->close();
}

?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);

?>
<main id="settings" class="min-vh-100">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-9 col-sm-6">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/settings/settings.php"
                           class="text-secondary d-print-none">Settings</a>
                        <span class="text-secondary d-print-none ps-3 pe-3">/</span>
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/settings/employees.php"
                           class="text-secondary d-print-none">Employee</a>
                        <span class="text-secondary d-print-none ps-3 pe-3">/</span>
                        <h2 class="d-print-none">Employee Add</span></h2>
                    </div>
                </div>
            </div>
            <hr/>
        </div>
    </div>
    <div class="d-flex">
        <section class="container-fluid" id="">
            <div class="row">
                <div class="col-6 col-lg-4 p-4">
                    <form action="upload-create-photo.php" class="dropzone" id="sbpdropzone">
                        <div class="dz-message p-4" data-dz-message>
                            <span>Click the box or Drag and Drop your employee photo here</span>
                        </div>
                    </form>
                </div>
            </div>

            <form id="mainform" class="form-horizontal push-10-t" name="mainform">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text" value="" id="last" name="last">
                            <label class="form-label" for="last">Last Name*</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" value="" type="text" id="first" name="first">
                            <label class="form-label" for="first">First Name*</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" value="" type="text" id="phone" name="phone"
                                   onKeyUp="javascript:return mask(this.value,this,'3,7','-');"
                                   onBlur="javascript:return mask(this.value,this,'3,7','-');">
                            <label class="form-label" for="phone">Phone</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" type="email" id="empemail" name="empemail">
                            <label class="form-label" for="empemail">Email</label>
                        </div>
                        <div class="form-group mb-4">
                            <select name="Position" class="select" name="position" id="position">
                                <?php
                                $stmt = "select JobDesc from jobdesc where shopid = '" . $shopid . "'";
                                $result = $conn->query($stmt);
                                while ($row = $result->fetch_array()) {
                                    echo "<option value='" . $row['JobDesc'] . "'>" . $row['JobDesc'] . "</option>";
                                }
                                ?>
                            </select>
                            <label class="form-label select-label" for="position">Position</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text" value="" id="password"
                                   name="password">
                            <label for="password" class="form-label">Password</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text" value="" id="michmech"
                                   name="michmech">
                            <label for="michmech" class="form-label" id="cityfloatinglabel">Michigan Mech #</label>
                        </div>
                        <div class="form-group mb-4">
                            <select class="select" name="showtechlist" id="showtechlist">
                                <option value="YES">YES</option>
                                <option selected value="NO">NO</option>
                            </select>
                            <label class="form-label select-label" id="statefloatinglabel">Is Employee a
                                Technician</label>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group mb-4">
                            <select class="select" name="Active" id="Active">
                                <option value="YES">YES</option>
                                <option value="NO">NO</option>
                            </select>
                            <label class="form-label select-label" for="active">Active</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text" value="0" id="rate" name="rate">
                            <label class="form-label" for="rate">Pay Rate</label>
                        </div>
                        <div class="form-group mb-4">
                            <select name="paytype" class="select" id="paytype">
                                <option value="FLATRATE">Flat Rate</option>
                                <option value="HOURLY">Hourly</option>
                                <?php if ($shopid == '1932' || $shopid == '11440') { ?>
                                    <option value="PERCENTAGE">Percentage</option>
                                    <?php
                                }
                                ?>
                            </select>
                            <label for="paytype" class="form-label select-label">Pay Type</label>
                        </div>
                        <div class="form-outline mb-4">
                            <input class="form-control" type="text"  value="" id="pin" name="pin">
                            <label class="form-label select-label">Timeclock PIN Number</label>
                        </div>
                        <div class="form-group mb-4">
                            <select name="mode" class="select" id="mode">
                                <option value="Full">Full</option>
                                <?php if($yearstarted<=2024){?><option value="Tech">Tech</option><?php }?>
                                <option value="Tech2">Tech 2.0</option>
                            </select>
                            <label for="mode" class="form-label select-label">User Mode</label>
                        </div>
                        <div class="form-outline mb-4" id="datehired_wrapper">
                            <input class="form-control" type="text" value="" id="datehired"
                                   name="datehired">
                            <label for="datehired" class="form-label">Date Hired</label>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-sm-12 text-center">
                        <button class="btn btn-secondary btn-md" data-mdb-toggle="modal"
                                data-mdb-target="#permissionsmodal" type="button">Permissions
                        </button>
                        <button class="btn btn-primary btn-md" onclick="saveAll()" type="button">Save</button>
                    </div>
                </div>

                <?php if (in_array($shopid, $profitboost)) { ?>
                    <div class="row">
                        <div class="col-md-9 text-center" style="margin-left:100px;color: red;">
                            <br><br>*Make sure to update the PPH Calculator with the new employee settings
                        </div>
                    </div>
                <?php } ?>
                <input type="hidden" name="photo_name" id="photo_name" value="">
            </form>
        </section>
    </div>
</main>

<div id="permissionsmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-lg modal-dialog modal-dialog-top">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h5 class="modal-title" id="spdLabel">Employee Permissions</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="permform">
                    <?php //$InventoryLookup,$EditInventory,$ReOpenRO,$ChangeUserSecurity,$ChangeSources,$ChangeRepairOrderTypes,$logintosbp,$accounting,$sendupdates,$showtechlist,$deletecustomer ?>
                    <input id="customerid" type="hidden">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" name="logintosbp" value="YES"
                                       id="logintosbp" checked class="form-check-input">
                                <label class="form-check-label" for="logintosbp">Login to Shop Boss Pro</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="CompanyAccess" value="YES"
                                       id="CompanyAccess" class="form-check-input">
                                <label class="form-check-label" for="CompanyAccess">Settings Access</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" name="EmployeeAccess" value="YES"
                                       id="EmployeeAccess" checked class="form-check-input">
                                <label class="form-check-label" for="EmployeeAccess">Add/Edit Employees</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="ReportAccess" value="YES"
                                       id="ReportAccess" class="form-check-input">
                                <label class="form-check-label" for="ReportAccess">Report Access</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="CreateRO" value="YES"
                                       id="CreateRO" class="form-check-input">
                                <label class="form-check-label" for="CreateRO">Create RO</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="CreateCT" value="YES"
                                       id="CreateCT" class="form-check-input">
                                <label class="form-check-label" for="CreateCT">Create Part Sale</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="EditSupplier" value="YES"
                                       id="EditSupplier" class="form-check-input">
                                <label class="form-check-label" for="EditSupplier">Edit Supplier</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="InventoryLookup" value="YES"
                                       id="InventoryLookup" class="form-check-input">
                                <label class="form-check-label" for="InventoryLookup">Inventory Lookup</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" name="candelete" value="YES"
                                       id="candelete" class="form-check-input">
                                <label class="form-check-label" for="candelete">Delete Labor and Parts from
                                    RO</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="changeshopnotice" value="YES"
                                       id="changeshopnotice" class="form-check-input">
                                <label class="form-check-label" for="changeshopnotice">Change Shop Notice</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="accounting" value="YES"
                                       id="accounting" class="form-check-input">
                                <label class="form-check-label" for="accounting">Accounting Access</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="downloaddata" value="YES"
                                       id="downloaddata" class="form-check-input">
                                <label class="form-check-label" for="downloaddata">Download Data</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="showgpinro" value="YES"
                                       id="showgpinro" class="form-check-input">
                                <label class="form-check-label" for="showgpinro">Show GP in RO</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="editcommentsinro" value="YES"
                                       id="editcommentsinro" class="form-check-input">
                                <label class="form-check-label" for="editcommentsinro">Edit Comments in RO</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="IntegrationAccess" value="YES"
                                       id="IntegrationAccess" class="form-check-input">
                                <label class="form-check-label" for="IntegrationAccess">Integrations Access</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" id="partsordering" checked value="YES" class="form-check-input">
                                <label class="form-check-label" for="partsordering">Order Parts Online</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="EditInventory" value="YES"
                                       id="EditInventory" class="form-check-input">
                                <label class="form-check-label" for="EditInventory">Edit Inventory</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="ReOpenRO" value="YES"
                                       id="ReOpenRO" class="form-check-input">
                                <label class="form-check-label" for="ReOpenRO">Re-Open RO</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="changerodate" value="YES"
                                       id="changerodate" class="form-check-input">
                                <label class="form-check-label" for="changerodate">Change RO Dates</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="ChangePartMatrix" value="YES"
                                       id="ChangePartMatrix" class="form-check-input">
                                <label class="form-check-label" for="ChangePartMatrix">Change Parts Matrix</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="ChangePartCodes" value="YES"
                                       id="ChangePartCodes" class="form-check-input">
                                <label class="form-check-label" for="ChangePartCodes">Change Parts Codes</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="ChangeJobDescription" value="YES"
                                       id="ChangeJobDescription" class="form-check-input">
                                <label class="form-check-label" for="ChangeJobDescription">Change Job
                                    Descriptions</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="ChangeSources" value="YES"
                                       id="ChangeSources" class="form-check-input">
                                <label class="form-check-label" for="ChangeSources">Change Advertising
                                    Sources</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="ChangeRepairOrderTypes" value="YES"
                                       id="ChangeRepairOrderTypes" class="form-check-input">
                                <label class="form-check-label" for="ChangeRepairOrderTypes">Change RO Types</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="sendupdates" value="YES"
                                       id="sendupdates" class="form-check-input">
                                <label class="form-check-label" for="sendupdates">Allow Send Updates to
                                    Customer?</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="deletepaymentsreceived" value="YES"
                                       id="deletepaymentsreceived" class="form-check-input">
                                <label class="form-check-label" for="deletepaymentsreceived">Can Delete
                                    Payments?</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" checked name="deletecustomer" value="YES"
                                       id="deletecustomer" class="form-check-input">
                                <label class="form-check-label" for="deletecustomer">Can Delete Customer?</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" name="editnotifications" value="YES"
                                       id="editnotifications" class="form-check-input">
                                <label class="form-check-label" for="editnotifications">Edit Notifications</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" name="edittechpaidlog" value="YES"
                                       id="edittechpaidlog" class="form-check-input">
                                <label class="form-check-label" for="edittechpaidlog">Edit Tech Paid Log in
                                    RO</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" name="DashboardAccess" value="YES"
                                       id="DashboardAccess" class="form-check-input">
                                <label class="form-check-label" for="DashboardAccess">BOSS Board Access</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" name="changerostatus" value="YES"
                                       id="changerostatus" class="form-check-input">
                                <label class="form-check-label" for="changerostatus">Change RO Status</label>
                            </div>
                            <div class="form-check form-switch mb-4">
                                <input type="checkbox" name="mergecustomers" value="YES"
                                       id="mergecustomers" class="form-check-input">
                                <label class="form-check-label" for="mergecustomers">Merge Customers</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="modal-footer d-flex justify-content-center">

            </div>
        </div>

    </div>
</div>
<?php
$component = '';
include getScriptsGlobal('');
?>
<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/dropzonejs/dropzone.min.css">
<script src="<?= SCRIPT ?>/plugins/dropzonejs/dropzone.min.js"></script>

<script>
    Dropzone.options.sbpdropzone = {
        maxFilesize: 5, // MB
        uploadMultiple: false,
        acceptedFiles: "image/*",
        success: (file, response) => {
            $('#photo_name').val(response);
        },
    };

    function mask(str, textbox, loc, delim) {
        var x = event.which || event.keyCode;
        if (x != 8) {
            var locs = loc.split(',');
            for (var i = 0; i <= locs.length; i++) {
                for (var k = 0; k <= str.length; k++) {
                    if (k == locs[i]) {
                        if (str.substring(k, k + 1) != delim) {
                            str = str.substring(0, k) + delim + str.substring(k, str.length)
                        }

                    }

                }

            }
            textbox.value = str
        }
    }


    function saveAll() {

        fn = $('#first').val()
        ln = $('#last').val()

        if (fn.length == 0 || ln.length == 0) {
            sbalert("First and Last name are required fields")
        } else {

            ds = "t=add&shopid=<?php echo $shopid; ?>&" + $('#mainform').serialize() + "&" + $('#permform').serialize()

            console.log(ds)
            $.ajax({
                data: ds,
                type: "post",
                url: "employee-action.php",
                success: function (r) {
                    console.log(r)
                    sbalert("Employee Added");
                    setTimeout(function () {
                        location.href = '../employees.php'
                    }, 1000);
                }
            });
        }

    }


    $(document).ready(function () {
        const options = {
            confirmDateOnSelect: true,
            disableFuture: true,
            format: "mm/dd/yyyy",
            inline: true
        }

        datePicker = document.getElementById('datehired_wrapper');
        const dateHiredPicker = new mdb.Datepicker(datePicker, options);

    });
</script>
</body>

</html>
<?php
mysqli_close($conn);
?>
