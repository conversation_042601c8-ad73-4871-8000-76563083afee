<?php
require REDISCONN;
require CONN;

$shopid = $_POST['shopid'];
$t = $_POST['t'];

if ($t == "changereminderdefault"){

	$v = $_POST['v'];
	$stmt = "update company set schedulesendreminderdefault = ? where shopid = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("ss",$v,$shopid);
	    if ($query->execute()){
	    	$conn->commit();
	    	echo "success";
            $CompanySettings = "shop:$shopid:company";
            if ($redis && $redis->exists("shop:$shopid:company")) {
                $redis->hSet($CompanySettings, 'schedulesendreminderdefault', $v);
            }
	    }else{
	    	echo $conn->errno;
	    }
	    
	    $query->close();
	    
	}else{
		echo "Update Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
	

}

elseif ($t == "changeremindertime"){

	$v = $_POST['v'];
	$stmt = "update company set schedulesendremindertime = ? where shopid = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("ss",$v,$shopid);
	    if ($query->execute()){
	    	$conn->commit();
	    	echo "success";
            $CompanySettings = "shop:$shopid:company";
            if ($redis && $redis->exists("shop:$shopid:company")) {
                $redis->hSet($CompanySettings, 'schedulesendremindertime', $v);
            }
	    }else{
	    	echo $conn->errno;
	    }
	    
	    $query->close();
	    
	}else{
		echo "Update Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
	

}

if ($t == "get"){

	$id = $_POST['id'];
	$rotype = "";
	$colorcode = "";
	$isdefault = "";
	$stmt = "select title,colorhex from colorcoding where shopid = ? and id = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("ss",$shopid,$id);
	    $query->execute();
	    $query->bind_result($title,$colorhex);
	    $query->fetch();
	    echo $title."|".$colorhex;
	    $query->close();

	}else{
		echo "Get Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

	

}

if ($t == "delete"){

	$id = $_POST['id'];
	
	$stmt = "delete from colorcoding where shopid = ? and id = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("si",$shopid,$id);
	    if ($query->execute()){
	    	$conn->commit();
	    	//echo "success";
	    }else{
	    	echo $conn->errno;
	    }
	    
	    $query->close();
	    
	}else{
		echo "Delete Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

}

if ($t == "save"){

	$id = $_POST['id'];
	$rot = $_POST['rot'];
	$col = $_POST['col'];
	
	// get a random col value
	function generateRandomString($length = 10) {
	    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
	    $charactersLength = strlen($characters);
	    $randomString = '';
	    for ($i = 0; $i < $length; $i++) {
	        $randomString .= $characters[rand(0, $charactersLength - 1)];
	    }
	    return $randomString;
	}
	
	if ($col == ""){
		$col = generateRandomString(18);
	}
	
	if ($id == 0){
		$stmt = "insert into colorcoding (shopid,title,colorhex) values (?,?,?)";
		
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sss",$shopid,$rot,$col);
		    if ($query->execute()){
		    	$conn->commit();
		    	echo "success";
		    }else{
		    	echo $conn->errno;
		    }
		    
		    $query->close();
		    
		}else{
			echo "Update Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
		
	}else{
	
		$stmt = "update colorcoding set title = ?, colorhex = ? where id = ? and shopid = ?";
		//echo $stmt;
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ssis",$rot,$col,$id,$shopid);
		    if ($query->execute()){
		    	$conn->commit();
		    	echo "success";
		    }else{
		    	echo $conn->errno;
		    }
		    
		    $query->close();
		    
		}else{
			echo "Update Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
	
	}


}

if ($t == "maxhours"){

	$stmt = "delete from shopmaxhours where shopid=?";
    if ($query = $conn->prepare($stmt))
 	{
 	   $query->bind_param('s',$shopid);
	   $query->execute();
	   $conn->commit();
	   $query->close();
    }


    for($i=0;$i<=6;$i++)
    {
    	if(isset($_POST['max'.$i]) && $_POST['max'.$i]>=0)
    	{
    	 $stmt = "insert into shopmaxhours (shopid,day,hours) values (?,?,?)";
 		 if ($query = $conn->prepare($stmt))
 		 {
 		   $query->bind_param('sis',$shopid,$i,$_POST['max'.$i]);
		   $query->execute();
		   $conn->commit();
		   $query->close();
		 }
	    }
    }

    echo("success");

}

mysqli_close($conn);

?>