<?php

require CONN;

$shopid = $_GET['shopid'];
$t = $_GET['t'];

if ($t == "get"){

	$id = $_GET['id'];
	$ptype = "";
	$stmt = "select jobdesc from jobdesc where shopid = ? and id = ?";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("ss",$shopid,$id);
	    $query->execute();
	    $query->bind_result($ptype);
	    $query->fetch();
	    echo $ptype;
	    $query->close();

	}else{
		echo "GET Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}



}

if ($t == "delete"){

	$id = $_GET['id'];

	$stmt = "delete from jobdesc where shopid = ? and id = ?";
	echo $stmt;
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("si",$shopid,$id);
	    if ($query->execute()){
	    	$conn->commit();
	    	//echo "success";
	    }else{
	    	echo $conn->errno;
	    }

	    $query->close();

	}else{
		echo "Delete Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

}

if ($t == "save"){

	$id = $_GET['id'];
	$s = $_GET['s'];
	//echo $id."-".$pt;

	if ($id == 0){
		$stmt = "insert into jobdesc (shopid,jobdesc) values (?,?)";

		if ($query = $conn->prepare($stmt)){
			$query->bind_param("ss",$shopid,$s);
		    if ($query->execute()){
		    	$conn->commit();
		    	echo "success";
		    }else{
		    	echo $conn->errno;
		    }

		    $query->close();

		}else{
			echo "Update Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

	}else{

		$stmt = "update jobdesc set jobdesc = ? where id = ? and shopid = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sis",$s,$id,$shopid);
		    if ($query->execute()){
		    	$conn->commit();
		    	echo "success";
		    }else{
		    	echo $conn->errno;
		    }

		    $query->close();

		}else{
			echo "Update Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

	}


}

mysqli_close($conn);

?>
