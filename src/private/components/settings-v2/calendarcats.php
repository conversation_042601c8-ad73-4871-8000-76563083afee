<?php
$component = "settings-v2";
$sub_component = "Calendar";

include getRulesComponent($component);
include getHeadGlobal($component);

$shopid = $_COOKIE['shopid'];

$stmt = "select schedulesendreminderdefault,schedulesendremindertime,timezone from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($schedulesendreminderdefault,$schedulesendremindertime,$tz);
    $query->fetch();
    $query->close();
}
$shophours = array();

$stmt = "select * from shopmaxhours where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        $shophours[$rs['day']] = $rs['hours'];
    }
}
function get_shop_timezone($ttz)
{
    switch ($ttz) {
        case "est":
            $phptz = 'America/New_York';
            break;
        case "mst":
            $phptz = 'America/Denver';
            break;
        case "pst":
            $phptz = 'America/Los_Angeles';
            break;
        case "cst":
            $phptz = 'America/Chicago';
            break;
        case "azst":
            $phptz = 'America/Phoenix';
            break;
        case "hst":
            $phptz = 'Pacific/Honolulu';
            break;
        case "akst":
            $phptz = 'America/Anchorage';
            break;
        case "gst":
            $phptz = 'Pacific/Guam';
            break;
        case "ast":
            $phptz = 'America/Halifax';
            break;
        case "astnd":
            $phptz = 'America/Blanc-Sablon';
            break;
        default:
            $phptz = "unknown";

    }
    return $phptz;
}

$timezone = get_shop_timezone($tz);
?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);

?>
<main id="settings" class="min-vh-100">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-9 col-sm-6">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/settings/settings.php"
                           class="text-secondary d-print-none">Settings</a>
                        <span class="text-secondary d-print-none ps-3 pe-3">/</span>
                        <h2 class="d-print-none">Calendar Settings
                            <i class="fas fa-circle-info fa-2xs" data-mdb-toggle="tooltip" title="You can customize by adding, deleting and editing Calendar Labels"></i>
                        </h2>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6 justify-content-end">
                    <button class="btn btn-primary btn-md float-end" onclick="openAddROType(0)" type="button">Add New Label</button>
                </div>
            </div>
            <hr/>
        </div>
    </div>
    <div class="row mt-4 mb-4">
        <div class="col-lg-10 col-md-12">
            <table class="sbdatatable w-100" id="cal_table">
                <thead>
                <tr>
                    <th>Calendar Label</th>
                    <th class="text-center">Delete</th>
                </tr>
                </thead>
                <tbody>
                <?php
                $stmt = "select `id`,`title` from colorcoding where shopid = '" . $shopid . "' order by title asc";
                $result = $conn->query($stmt);
                while ($row = $result->fetch_array()) {
                    ?>
                    <tr>
                        <td onclick="openAddROType('<?php echo $row['id']; ?>')"><?php echo $row['title']; ?></td>
                        <td class="text-center"><i class="fa fa-trash" onclick="deleteROType('<?php echo $row['title']; ?>', '<?php echo $row['id']; ?>')"></i></td>
                    </tr>
                    <?php
                }
                ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-lg-10 col-md-12">
            <div class="card card-square w-100">
                <div class="card-body">
                    <div class="row p-2 border-bottom"><h5 class="d-inline card-title text-primary text-center">Calendar Reminders
                        </h5></div>
                    <div class="card-text pt-3 pb-3 ps-2 lh-lg">
                        <div class="row d-flex">
                            <div class="col-md-12">
                                <p>When adding an item to the Schedule, you can set the default Reminder value. You can choose to default to No reminder, Text Message reminder, Email reminder or Both reminder. You can change that selection here</p>
                                <select onchange="setVal(this.value)" id="sendreminderdefault" class="select">
                                    <option <?php if (strtolower($schedulesendreminderdefault) == "no") {
                                        echo "selected='selected'";
                                    } ?> value="no">No
                                    </option>
                                    <option <?php if (strtolower($schedulesendreminderdefault) == "text") {
                                        echo "selected='selected'";
                                    } ?> value="text">Text Message
                                    </option>
                                    <option <?php if (strtolower($schedulesendreminderdefault) == "email") {
                                        echo "selected='selected'";
                                    } ?> value="email">Email
                                    </option>
                                    <option <?php if (strtolower($schedulesendreminderdefault) == "both") {
                                        echo "selected='selected'";
                                    } ?> value="both">Both
                                    </option>
                                </select>

                                <?php
                                if($timezone !='unknown'){?>

                                <br><p>You can adjust the time for the automatic reminder that will be sent the day before the appointment here.</p>

                                <select class="select" id="sendremindertime" onchange="setTime(this.value)">
                                <?php

                                for($i=12;$i<=22;$i++)
                                {
                                    $pstTime = new DateTime($i.':00', new DateTimeZone('America/Los_Angeles'));
                                    $shopTime = $pstTime->setTimezone(new DateTimeZone($timezone))->format('g:i A');

                                    if(stripos($shopTime, 'AM') !==false)continue;

                                    echo("<option value='".$i.':00:00'."' ".($i.':00:00' == $schedulesendremindertime ?'selected':'').">".$shopTime."</option>");
                                }

                                ?>

                                </select>

                                <?php }?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-10 col-md-12">
            <div class="card card-square w-100">
                <div class="card-body">
                    <div class="row p-2 border-bottom"><h5 class="d-inline card-title text-primary text-center">Max Booking Hours per day
                        </h5></div>
                    <div class="card-text pt-3 pb-3 ps-2 lh-lg">
                        <div class="row d-flex">
                            <div class="col-md-12">
                                <form id="maxhours">
                                    <div class="row mb-4">
                                        <div class='col-sm-2'>Mon:</div>
                                        <div class='col-sm-2'><input type="text" name="max1" class="form-control" value="<?= $shophours['1'] ?? '' ?>"></div>
                                    </div>
                                    <div class="row mb-4">
                                        <div class='col-sm-2'>Tue:</div>
                                        <div class='col-sm-2'><input type="text" name="max2" class="form-control" value="<?= $shophours['2'] ?? '' ?>"></div>
                                    </div>
                                    <div class="row mb-4">
                                        <div class='col-sm-2'>Wed:</div>
                                        <div class='col-sm-2'><input type="text" name="max3" class="form-control" value="<?= $shophours['3'] ?? '' ?>"></div>
                                    </div>
                                    <div class="row mb-4">
                                        <div class='col-sm-2'>Thu:</div>
                                        <div class='col-sm-2'><input type="text" name="max4" class="form-control" value="<?= $shophours['4'] ?? '' ?>"></div>
                                    </div>
                                    <div class="row mb-4">
                                        <div class='col-sm-2'>Fri:</div>
                                        <div class='col-sm-2'><input type="text" name="max5" class="form-control" value="<?= $shophours['5'] ?? '' ?>"></div>
                                    </div>
                                    <div class="row mb-4">
                                        <div class='col-sm-2'>Sat:</div>
                                        <div class='col-sm-2'><input type="text" name="max6" class="form-control" value="<?= $shophours['6'] ?? '' ?>"></div>
                                    </div>
                                    <div class="row mb-4">
                                        <div class='col-sm-2'>Sun:</div>
                                        <div class='col-sm-2'><input type="text" name="max0" class="form-control" value="<?= $shophours['0'] ?? '' ?>"></div>
                                    </div>
                                    <div class="row mb-4">
                                        <div class="col-sm-12 text-center">
                                            <button type="button" class="btn btn-sm btn-primary" id="btn-save" onclick="saveMaxHours()">Save</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>


<div id="rotypemodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h5 class="modal-title" id="spdLabel">Add a Calendar Label</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div id="newrotype" class="form-outline mb-4">
                            <input class="form-control" tabindex="1" type="text" id="addrotype" name="addrotype">
                            <label class="form-label" for="addrotype">Enter Calendar Label (colors will be auto assigned)</label>
                        </div>
                        <input id="rotypeid" type="hidden">
                        <input id="cccol" type="hidden">
                    </div>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-center">
                <button class="btn btn-primary" type="button" onclick="saveROType()">Save</button>
            </div>
        </div>
    </div>
</div>


<?php
$component = '';
include getScriptsGlobal('');
// include getFooterComponent($component);
?>


<script>

    $(document).ready(function () {
        $("#cal_table").dataTable({
            responsive: true,
            fixedHeader: {
                headerOffset: 68
            },
            searching: false,
            colReorder: true,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
        })
    });

    function saveMaxHours() {
        $('#btn-save').attr('disabled', 'disabled');

        ds = "shopid=<?php echo $shopid; ?>&t=maxhours&" + $('#maxhours').serialize()
        showLoader();
        $.ajax({
            data: ds,
            type: 'post',
            url: "calendarfiles/calendar-action.php",
            success: function (r) {
                if (r == 'success') {
                    sbalert("Saved")
                    location.reload();
                } else {
                    sbalert(r)

                    $('#btn-save').attr('disabled', false);
                    hideLoader();
                }
            }
        })

    }

    function setVal(v) {
        showLoader();
        $.ajax({
            data: "shopid=<?php echo $shopid; ?>&v=" + v + "&t=changereminderdefault",
            type: "post",
            url: "calendarfiles/calendar-action.php",
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
                hideLoader();
            },
            success: function (r) {
                if (r == "success") {
                    sbalert("Change Saved");
                    hideLoader();
                }
            }
        })

    }

    function setTime(v) {

            showLoader();

            $.ajax({
                data: "shopid=<?php echo $shopid; ?>&v=" + v + "&t=changeremindertime",
                type: "post",
                url: "calendarfiles/calendar-action.php",
                error: function(xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },
                success: function(r) {
                    if (r == "success") {
                        sbalert("Change Saved");
                        hideLoader();
                    }
                }
            })

        }

    function openAddROType(id) {
        console.log(id)
        $('#cccol').val('')
        if (id == 0) {
            $('#addrotype').val('')
            $('#rotypemodal').modal('show')
        }
        if (id != 0) {
            $('#rotypeid').val(id)
            $('#delbutton').show()
            showLoader();
            $.ajax({
                data: "shopid=<?php echo $shopid; ?>&id=" + id + "&t=get",
                type: "post",
                url: "calendarfiles/calendar-action.php",
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                },
                success: function (r) {
                    if (r.indexOf("|") >= 0) {
                        rar = r.split("|")
                        caltype = rar[0]
                        cc = rar[1]
                        $('#cccol').val(cc)
                        $('#addrotype').val(caltype);
                        $('#rotypemodal').modal('show')
                        hideLoader();
                        //$('#addrotype').css("background-color",cc)
                    }
                }
            })
        } else {
            setTimeout(function () {
                $('#addrotype').focus()
            }, 500)
        }
    }

    function deleteROType(typename, id) {

        sbconfirm("Are you sure?", "Delete the \"" + typename + "\" Calendar Label?", function () {
            showLoader();
            $.ajax({

                data: "t=delete&id=" + id + "&shopid=<?php echo $shopid; ?>",
                url: "calendarfiles/calendar-action.php",
                type: "post",
                success: function (r) {
                    location.reload()
                }

            });
        });
    }

    function saveROType() {
        rot = encodeURIComponent($('#addrotype').val())
        col = $('#cccol').val()
        id = $('#rotypeid').val()
        if (rot == ""){
            sbalert("Please enter a calendar label")
        } else {
            showLoader();
            $.ajax({

                data: "t=save&id=" + id + "&shopid=<?php echo $shopid; ?>&rot=" + rot + "&col=" + col,
                url: "calendarfiles/calendar-action.php",
                type: "post",
                success: function (r) {
                    r = r.trim()
                    if (r == "duplicate") {
                        swal("The color you selected has already been used.  Please select another color")
                    } else if (r == "success") {
                        location.reload()
                    } else {
                        console.log(r)
                    }
                    hideLoader();
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log(xhr.status);
                    console.log(xhr.responseText);
                    console.log(thrownError);
                }
            })
        }

    }
</script>

</body>

</html>