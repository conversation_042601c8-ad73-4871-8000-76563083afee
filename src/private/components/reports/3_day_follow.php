<?php
$title = 'Follow Up Report';  // Report Title Goes Here
// Use this for Gen Pop Reports
require("includes/header_reports.php");
require(CONN);
//require("../php/functions.php");

// Use this for Custom Reports
//require("../../../php/includes/reports/header_reports.php");
//require("../../../php/conn.php");
//require("../../functions.php");

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1=date('Y-m-d',strtotime($sd));
$ed1=date('Y-m-d',strtotime($ed));

// Page Variables
//$subtitle = "$sdate";  // Report SubTitle Goes Here - Hide if not needed
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport_autosize.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>

<title><?= $title;?></title>  <!-- Meta title for page. Change in variable above-->

<!DOCTYPE html>
<html>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>

<?php

// Use this for Gen Pop Reports
include("includes/report_buttons.php");

// Use this for Custom Reports
//include("../../../php/includes/reports/report_buttons.php");
?>

<!-- Column Headers Insert Report Variables here -->

	<table class=report_table>
	<tr class="table_header table_head">
		<td>Customer</td>
		<td>Vehicle</td>
		<td>Vehicle Issues</td>
		<td>Phone #'s</td>
		<td style="text-align:right">Date RO Closed</td>
	</tr>

    <?php
    $tablefields=array('Customer','Vehicle','Vehicle Issues','Home Phone','Work Phone','Cell Phone','Date RO Closed');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel


// Insert DB Query Here

// Template Query Begins - Replace entire section
		$stmt = "select roid, `customer`, vehinfo, customerphone, customerwork, cellphone, statusdate from repairorders where shopid = ? and status = 'closed' and rotype != 'no approval' and statusdate >= ? and statusdate <= ?";

		if($query = $conn->prepare($stmt)){
			$query->bind_param("sss",$shopid,$sd1,$ed1);
			$query->execute();
			$roresult = $query->get_result();
		}else{
			echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

			while($ro = $roresult->fetch_array()) {
				$statdate = date_format(new DateTime($ro["statusdate"]),'m/d/Y');
				$roid = $ro["roid"];
				$complaint_arr=array();
		?>
			<tr>
				<td><?= strtoupper($ro["customer"]); ?></td>
				<td><?= strtoupper($ro["vehinfo"]); ?></td>
				<td><?php
					$cstmt = "select complaint from complaints where cstatus = 'no' and shopid = ? and roid = $roid";
					if($query = $conn->prepare($cstmt)){
						$query->bind_param("s",$shopid);
						$query->execute();
						$rocresult = $query->get_result();
					}else{
						echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
					}
					while($cro = $rocresult->fetch_array())
					$complaint_arr[]=$cro["complaint"];

					echo strtoupper(implode(' ,',$complaint_arr));
					?>
				</td>
				<td><?php
						if (strlen($ro["customerphone"]) > 5) {
							echo " <b>H:</b>".formatPhone($ro["customerphone"]);
						}if (strlen($ro["customerwork"]) > 5) {
							echo " <b>W:</b>".formatPhone($ro["customerwork"]);
						}if (strlen($ro["cellphone"]) >5) {
							echo " <b>C:</b>".formatPhone($ro["cellphone"]);
						}
					 ?>
				</td>
				<td style="text-align:right"><?= $statdate; ?></td>
			</tr>
		    <?php
		        $alldata[]=array(strtoupper($ro["customer"]), strtoupper($ro["vehinfo"]), strtoupper(implode(' ,',$complaint_arr)), formatPhone($ro["customerphone"]), formatPhone($ro["customerwork"]), formatPhone($ro["cellphone"]), $statdate);//fill up the alldata array with the arrays of data to be shown in excel export
		        } // end of while for loop
		     // end if for end of file


				$alldata[]=array('','','','','','','');
		    ?>
		</table>
<?php

// Use this for Gen Pop Reports
include("includes/footer_reports.php");
include("includes/report_form.php");

// Use this for Custom Reports
//include("../../../php/includes/reports/footer_reports.php");
//include("../../../php/includes/reports/report_form.php");
?>

</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
