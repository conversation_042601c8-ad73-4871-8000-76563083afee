﻿<?php

$title = "Inventory Appreciation / Depreciation";

include_once "includes/report_config.php";
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}
?>

<script type="text/javascript">
    var cal_obj2 = null;

    var format = '%m/%d/%Y';

    // show calendar
    function show_cal(el, a) {

        if (cal_obj2) return;
        document.getElementById("dfield").value = a
        var text_field = document.getElementById(document.getElementById("dfield").value);
        text_field.disabled = true;
        cal_obj2 = new RichCalendar();
        cal_obj2.start_week_day = 0;
        cal_obj2.show_time = false;
        cal_obj2.language = 'en';
        cal_obj2.user_onchange_handler = cal2_on_change;
        cal_obj2.user_onclose_handler = cal2_on_close;
        cal_obj2.user_onautoclose_handler = cal2_on_autoclose;

        cal_obj2.parse_date(text_field.value, format);

        cal_obj2.show_at_element(text_field, "adj_right-");
        //cal_obj2.change_skin('alt');

    }

    // user defined onchange handler
    function cal2_on_change(cal, object_code) {
        if (object_code == 'day') {
            document.getElementById(document.getElementById("dfield").value).disabled = false;
            document.getElementById(document.getElementById("dfield").value).value = cal.get_formatted_date(format);

            cal.hide();
            cal_obj2 = null;
        }
    }

    // user defined onclose handler (used in pop-up mode - when auto_close is true)
    function cal2_on_close(cal) {
        document.getElementById(document.getElementById("dfield").value).disabled = false;
        cal.hide();
        cal_obj2 = null;

    }

    // user defined onautoclose handler
    function cal2_on_autoclose(cal) {
        document.getElementById(document.getElementById("dfield").value).disabled = false;
        cal_obj2 = null;
    }


    function loadXMLDoc() {
        document.getElementById("results").src = "";
        sd = document.getElementById("sd").value
        ed = document.getElementById("ed").value
        url = "inventoryappreciationreport-data.php?sd=" + sd + "&ed=" + ed
        document.getElementById("results").src = url
    }

</script>
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
<h3 class="text-center"><?= $title; ?></h3>
<input type="hidden" name="dfield" id="dfield"/>
<br/>
<div class="row">
    <div class="col-md-6 col-md-offset-3">
        <div class="row">
            <label for="sd" class="col-md-3">Start Date</label>
            <div class="col-md-6"><input class="form-control" onfocus="" id="sd" name="sd" type="text"/></div>
        </div>
        <div class="row" style="margin-top: 5px;">
            <label for="ed" class="col-md-3">End Date</label>
            <div class="col-md-6"><input class="form-control" onfocus="" id="ed" name="ed" type="text"/></div>
        </div>
        <div class="row" style="margin-top: 5px; margin-bottom: 5px; text-align: center">
            <div class="col-md-3">
                <ul class="inputul list-unstyled">
                    <li class="dropdown input-top">
                        <div id="datestat" style="color:white" class="btn btn-warning" role="button"
                             class="dropdownselected" data-toggle="dropdown">Select Date Range
                        </div>
                        <ul style="width:100%" id="statusselect" class="dropdown-menu">
                            <li id="assembly-li" class="input-li"><a href="#" class="nav-link"
                                                                     onclick="setdates('This Week')">This Week</a></li>
                            <li id="q-check-li" class="input-li"><a href="#" class="nav-link"
                                                                    onclick="setdates('This Month')">This Month</a></li>
                            <li id="final-li" class="input-li"><a href="#" class="nav-link"
                                                                  onclick="setdates('This Year')">This Year</a></li>
                            <li id="inspection-li" class="input-li"><a href="#" class="nav-link"
                                                                       onclick="setdates('Last Week')">Last Week</a>
                            </li>
                            <li id="approval-li" class="input-li"><a href="#" class="nav-link active"
                                                                     onclick="setdates('Last Month')">Last Month</a>
                            </li>
                            <li id="parts-li" class="input-li"><a href="#" class="nav-link"
                                                                  onclick="setdates('Last Year')">Last Year</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
            <div class="col-md-6">
                <input onclick="loadXMLDoc()" name="Button1" class="btn btn-primary" type="button" value="Run Report"/>
                <input onclick="location.href='<?= COMPONENTS_PRIVATE ?>/reports/reports.php'" class="btn btn-default" name="Button2" type="button"
                       value="Back to Reports"/></td>
            </div>
        </div>
    </div>
</div>

<iframe style="width:100%;height:300px;" id="results"></iframe>
<?php
include_once "includes/footer_reports.php";
?>
<script>
    $(document).ready(function () {

        $('#sd').datetimepicker({
            format: 'MM/DD/YYYY'
        });
        $('#ed').datetimepicker({
            format: 'MM/DD/YYYY'
            //useCurrent: false
        });
        $("#sd").on("dp.change", function (e) {
            $('#ed').focus()
        });
        $('#sd').datetimepicker({
            ignoreReadonly: true,
            useCurrent: true,
            format: "MM/DD/Y"

        })

        $('#ed').datetimepicker({
            ignoreReadonly: true,
            useCurrent: true,
            format: "MM/DD/Y"

        })
    });

    function setdates(t) {
        <?php
        echo "\r\nvar lw = '" . date("m/d/Y", strtotime("last week monday")) . "|" . date("m/d/Y", strtotime("last week sunday")) . "';\r\n";
        echo "var lm = '" . date("m/d/Y", strtotime("first day of previous month")) . "|" . date("m/d/Y", strtotime("last day of previous month")) . "';\r\n";
        $ly = date("Y") - 1;
        echo "var ly = '" . date("m/d/Y", strtotime("01/01/" . $ly)) . "|" . date("m/d/Y", strtotime("12/31/" . $ly)) . "';\r\n";
        echo "var tw = '" . date("m/d/Y", strtotime("this week monday")) . "|" . date("m/d/Y", strtotime("this week sunday")) . "';\r\n";
        echo "var tm = '" . date("m/d/Y", strtotime("first day of this month")) . "|" . date("m/d/Y", strtotime("last day of this month")) . "';\r\n";
        $ty = date("Y");
        echo "var ty = '" . date("m/d/Y", strtotime("01/01/" . $ty)) . "|" . date("m/d/Y", strtotime("12/31/" . $ty)) . "';\r\n";
        ?>

        if (t == "This Week") {
            tar = tw.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "This Month") {
            tar = tm.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "This Year") {
            tar = ty.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "Last Week") {
            tar = lw.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "Last Month") {
            tar = lm.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
        if (t == "Last Year") {
            tar = ly.split("|")
            $('#sd').val(tar[0])
            $('#ed').val(tar[1]);
        }
    }
</script>

</body>

</html>
