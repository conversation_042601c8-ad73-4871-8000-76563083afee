<!DOCTYPE html>
<html>
<?php
$title = "Back-Ordered Parts Sales";

include_once "includes/report_config.php";

$shopid = $_COOKIE['shopid'];
$todaydate = 'todaydate';

$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}

$template = COMPONENTS_PRIVATE."/reports/templates/excelexport.php";
?>
<body >
<?php
    include_once "includes/report_buttons_noexcel.php";
?>
<?php


$stmt = "select psid,lastname,firstname,address,city,state,zip,statusdate,subtotal,tax,total,homephone,workphone from ps left join customer on ps.shopid = customer.shopid and ps.cid = customer.customerid where ps.shopid = ? and ps.status = 'Backorder'";
if($query = $conn->prepare($stmt)){
    $query->bind_param("s", $shopid);
    $query->execute();
    $results = $query->get_result();
    $query->close();
}

if($results->num_rows > 0){
?>
<table class="report_table">
	<tr class="table_header">
		<td class="no-print" style="width: 18%"><strong>Receive Part</strong></td>
		<td class="" style="width: 30%"><strong>Invoice #/Customer
		Information</strong></td>
		<td class="" style="width: 45%"><strong>Parts</strong></td>
		<td class="" style="width: 10%"><strong>Date</strong></td>
		<td class="" style="width: 5%"><strong>Subtotal</strong></td>
		<td class="" style="width: 5%"><strong>Tax</strong></td>
		<td class="" style="width: 5%"><strong>Total</strong></td>
	</tr>
    <?php
        $cntr = 0;
        $s = 0;
        $tx = 0;
        $t = 0;
        while($rs = $results->fetch_assoc()){
            $cntr = $cntr + 1;
		$lineval = "style='border-top:1px black solid'";
		$s = $s + $rs["subtotal"];
		$tx = $tx + $rs["tax"];
		$t = $t + $rs["total"];
		$c = $rs["lastname"] . ", " . $rs["firstname"];
		$a = $rs["address"] . "&nbsp;&nbsp;&nbsp;" . $rs["city"] . ", " . $rs["state"] . ". " . $rs["zip"];
		$p = $rs["homephone"] . " - " . $rs["workphone"];

		$stmt = "select * from psdetail where shopid = ? and psid = ?";
            if($dquery = $conn->prepare($stmt)){
                $dquery->bind_param("si", $shopid, $rs['psid']);
                $dquery->execute();
                $dresults = $dquery->get_result();
                $dquery->close();
            }
    ?>
	<tr>
		<td <?php if ($cntr > 1) { echo $lineval; } ?> valign="top" class="no-print" style="width: 18%">
		<a href="<?= COMPONENTS_PRIVATE ?>/partsale/partsale.php?psid=<?= $rs["psid"] ?>">
		<img alt="" border="0" height="35" src="<?= IMAGE ?>/newimages/receiveshipment.png" width="37"><br>
		Click here to</a><br>
		<a href="<?= COMPONENTS_PRIVATE ?>/partsale/partsale.php?psid=<?= $rs["psid"] ?>">
		recieve order</a></td>
		<td <?php if ($cntr > 1) echo $lineval ?> valign="top">
		#<?= $rs["psid"] . "-". $c ."<br>". $a ." ". $p ?>&nbsp;</td>
		<td <?php if ($cntr > 1) { echo $lineval; } ?>>
		<?php
            if($dresults->num_rows > 0) {
                while ($drs = $dresults->fetch_assoc()) {
                    echo "Part Number: &nbsp;&nbsp;" . $drs["pnumber"] . "<br>";
                    echo "Description: &nbsp;&nbsp;&nbsp;" . $drs["desc"] . "<hr>";
                }
            }
		?></td>
		<td <?php if ($cntr > 1) { echo $lineval; } ?> valign="top"><?= date('n/j/Y', strtotime($rs["statusdate"])) ?>&nbsp;</td>
		<td <?php if ($cntr > 1) { echo $lineval; } ?> class="" style="width: 0%" valign="top"><?= asDollars($rs["subtotal"]) ?>&nbsp;</td>
		<td <?php if ($cntr > 1) { echo $lineval; } ?> class="" style="width: 0%" valign="top"><?= asDollars($rs["tax"]) ?>&nbsp;</td>
		<td <?php if ($cntr > 1) { echo $lineval; } ?> class="" style="width: 0%" valign="top"><?= asDollars($rs["total"]) ?>&nbsp;</td>
	</tr>
    <?php
        }
    ?>
	<tr class="table_total">
		<td style="border-top:1px black solid; width: 18%;">&nbsp;</td>
		<td style="border-top:1px black solid" colspan="2"><b>Totals:</b>&nbsp;</td>
		<td style="border-top:1px black solid" valign="top">&nbsp;</td>
		<td class="" style="border-top:1px black solid" valign="top"><?= asDollars($s); ?>&nbsp;</td>
		<td class="" style="border-top:1px black solid" valign="top"><?= asDollars($tx); ?>&nbsp;</td>
		<td class="" style="border-top:1px black solid" valign="top"><?= asDollars($t); ?>&nbsp;</td>
	</tr>

</table>
<?php
} else {
    echo "No Records Found";
}
?>
<?php
include_once "includes/footer_reports.php";
?>
</body>
<script language="javascript" type="text/javascript">
	function printme(){
		document.getElementById('pbut').style.display='none'
		window.print()
		document.getElementById('pbut').style.display='block'
	}
</script>
</html>
