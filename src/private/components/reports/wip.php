<!DOCTYPE html>
<html>
<?php

// Use this for Gen Pop Reports
 $title = 'Work In Progress';  // Report Title Goes Here
 require "includes/header_reports.php";
 require CONN;
 //require("../php/functions.php");

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = "";
$ed = "";
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

// Page Variables
if($conamereports == 'yes'){
    $title .= " - $companyname";
}
//$subtitle = '';  // Report SubTitle Goes Here - Hide if not needed
$todaydate = "todaydate";
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder
?>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>

<?php

// Use this for Gen Pop Reports
require "includes/report_buttons.php";
?>

<!-- Column Headers Insert Report Variables here -->

	<table class=report_table>
  <thead>
	<tr class="table_header table_head">
		<td>Date In</td>
		<td>Status</td>
		<td>RO #</td>
		<td>Customer</td>
		<td>Vehicle</td>
		<td style="text-align:right;">Sold Hours</td>
		<td style="text-align:right;">Labor Clock</td>
		<td style="text-align:right;">%Complete</td>
		<td style="text-align:right;">Total RO</td>
        <td style="text-align:right;">Balance Owed</td>

	</tr>
</thead>

    <?php
    $tablefields=array('Date In','Status','RO #','Customer','Vehicle','Sold Hours','Labor Clock','%Complete','Total RO','Balance Owed');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel


// Insert DB Query Here

// Template Query Begins - Replace entire section
	$runttl=$runbal=0;
	$query="select Writer, MajorComplaint, ROID, ucase(Status) as stat, DateIn, Customer, VehInfo, TotalRO, totalfees, VehLicNum, CustomerID, ROType, balance from repairorders where shopid ='$shopid' and Status <> 'Closed' and ROType <> 'No Approval' ORDER BY Status ASC, ROID DESC";
	$roresult = mysqli_query($conn, $query);
    if (!$roresult) {
        die("No Data To Display!");
    }
    if ($roresult->num_rows > 0) {
        while ($rs = $roresult->fetch_array()) {

    $stmt = "select sum(laborhours) as lh from labor where deleted = 'no' and shopid = '$shopid' and roid = '".$rs['ROID']."'";
    $query = mysqli_query($conn, $stmt);
    $trs = $query->fetch_array();
    if(!empty($trs['lh'])){
	    $soldhours = round($trs["lh"],2);
    }else{
	    $soldhours = 0;
	}
    $stmt = "select sum(round((timestampdiff(second,startdatetime,enddatetime)/60)/60,2)) as thours, labortimeclock.* from labortimeclock where shopid = '$shopid' and roid = '".$rs['ROID']."'";
    $query = mysqli_query($conn, $stmt);
    $trs = $query->fetch_array();
    if(!empty($trs['thours'])){
	    $laborclock = $trs["thours"];
    }else{
	    $laborclock = 0;
	}
    if($laborclock > 0 && $soldhours > 0){
	    $percentcomplete = round(($laborclock / $soldhours)*100,2);
    }else{
	    $percentcomplete = 0;
	}
    if(is_numeric(substr($rs['stat'],0,1))){
	    $thestat = substr($rs['stat'],1);
    }else{
	    $thestat = $rs["stat"];
	}
    $runttl += $rs["TotalRO"];
    $runbal += $rs["balance"];
    // $stmt = "select complaintid, roid, complaint FROM complaints WHERE roid = '".$rs['ROID']."' AND shopid = '$shopid' AND acceptdecline != 'Declined'";
   	// $query = mysqli_query($conn, $stmt);
	// $custcomplain = $query->fetch_array();
	// if (!$custcomplain) {
	// 	die("No Customer Complaint");
	// }

// Template Query Ends

    ?>

<!-- Table Results Begin -->
    <tr class="table_data">
		<td><?= date('n/j/Y',strtotime($rs["DateIn"]))?></td>
		<td><?= $thestat?></td>
		<td><?= $rs["ROID"]?></td>
		<td><?= strtoupper($rs["Customer"])?></td>
		<td><?= strtoupper($rs["VehInfo"])?></td>
		<td style="text-align:right;"><?= $soldhours?></td>
		<td style="text-align:right;"><?= $laborclock?></td>
		<td style="text-align:right;"><?= number_format($percentcomplete,2).'%'?></td>
		<td style="text-align:right;">$<?= number_format($rs['TotalRO'],2)?></td>
        <td style="text-align:right;">$<?= number_format($rs['balance'],2)?></td>
    </tr>

    <?php
          $alldata[]=array(date('n/j/Y',strtotime($rs["DateIn"])),$thestat,$rs['ROID'],$rs["Customer"],$rs["VehInfo"],$soldhours,$laborclock,number_format($percentcomplete,2).'%','$'.number_format($rs['TotalRO'],2),'$'.number_format($rs['balance'],2));
          //$alldata[]=array("Customer Concern",$rs["MajorComplaint"],'','','','','','',''); //fill up the alldata array with the arrays of data to be shown in excel export
        } // end of while for loop
    } // end if for end of file

	$statement="SELECT ps.shopid,psid,`status`,psdate,concat(customer.firstname,' ',customer.LastName) AS customer,`total`,cid,statusdate
				FROM ps 
				LEFT JOIN customer ON ps.shopid = customer.shopid AND ps.cid = customer.CustomerID 
				WHERE ps.`status` != 'CLOSED' AND ps.`status` != 'DEAD' AND ps.shopid = ?";

	if ($query = $conn->prepare($statement)) {
		$query->bind_param("s", $shopid);
		$query->execute();
		$result = $query->get_result();

		$psTotal = 0;
		if ($result->num_rows > 0) {
			while ($ps = $result->fetch_assoc()) {
				$psTotal += $ps['total'];
				?>
					<tr class="table_data">
						<td><?= date('n/j/Y',strtotime($ps["statusdate"])) ?></td>
						<td><?= strtoupper($ps['status'])?></td>
						<td>PS - <?= $ps["psid"]?></td>
						<td><?= strtoupper($ps["customer"])?></td>
						<td></td>
						<td></td>
						<td style="text-align:right;"></td>
						<td style="text-align:right;"></td>
						<td style="text-align:right;"><?= asDollars($ps['total'],2)?></td>
						<td style="text-align:right;"></td>
					</tr>
				<?php
				$alldata[]=array(
					date('n/j/Y',strtotime($ps["statusdate"])),
					strtoupper($ps['status']),
					"PS - " . $ps["psid"],
					strtoupper($ps["customer"]), '', '', '', '',
					asDollars($ps['total'],2), ''
				);
			}
			$runttl += $psTotal;
		}
		$query->close();
	} else {
		echo "PS Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

		 $alldata[]=array('','','','','','','','','','');
		 $alldata[]=array('TOTAL IN PROCESS','','','','','','','','$'.number_format($runttl,2),'$'.number_format($runbal,2));

    ?>

    <!-- Table totals go here, if needed-->

      	<tr class="table_total">
			<td><b>TOTAL IN PROCESS</b></td>    
			<td></td>
			<td></td>
			<td></td>
			<td></td>
			<td></td>
			<td></td>
			<td></td>
			<td style="text-align:right;"><b>$<?= number_format($runttl,2)?></b></td>
			<td style="text-align:right;"><b>$<?= number_format($runbal,2)?></b></td>
     	</tr>

    </table>

<?php

// Use this for Gen Pop Reports
require "includes/footer_reports.php";
include("includes/report_form_todaydate.php"); //only for reports with today date req
?>


</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
