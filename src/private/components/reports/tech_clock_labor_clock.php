<?php
$title = 'TimeClock vs. Labor TimeClock';  // Report Title Goes Here
// Use this for Gen Pop Reports
require "includes/header_reports.php";
require(CONN);
//require("../php/functions.php");

// Use this for Custom Reports
//require("../../../php/includes/reports/header_reports.php");
//require("../../../php/conn.php");
//require("../../functions.php");

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1=date('Y-m-d',strtotime($sd))." 00:00:00";
$ed1=date('Y-m-d',strtotime($ed))." 23:59:59";

// Page Variables
//$subtitle = 'my subtitle';  // Report SubTitle Goes Here - Hide if not needed
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport_autosize.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';

function SEC_TO_TIME($seconds) {
  $t = round($seconds);
  return sprintf('%02d:%02d:%02d', ($t/3600),($t/60%60), $t%60);
}

?>

<title><?= $title;?></title>  <!-- Meta title for page. Change in variable above-->

<!DOCTYPE html>
<html>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>

<?php

// Use this for Gen Pop Reports
require "includes/report_buttons.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/report_buttons.php");
?>

<!-- Column Headers Insert Report Variables here -->

	<table class=report_table>
	<tr class="table_header table_head">
		<td>Technician</td>
		<td>TimeClock Hours</td>
		<td>Labor TimeClock</td>
		<td>Productivity</td>
	</tr>

    <?php
    $tablefields=array('Technician','TimeClock Hours','Labor TimeClock','Productivity');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel

// Insert DB Query Here

// Template Query Begins - Replace entire section
	$stmt = "select employeefirst ef,employeelast el, SUM(TIMESTAMPDIFF(SECOND, startdatetime, enddatetime)) td, startdatetime, enddatetime FROM timeclock tc LEFT JOIN employees e ON tc.shopid = e.shopid and tc.emp = e.id WHERE tc.shopid = ? AND tc.startdatetime >= ? AND tc.enddatetime <= ? and not isnull(enddatetime) and enddatetime != '0000-00-00 00:00:00' GROUP BY tc.emp ORDER BY employeelast, employeefirst";
	if($query = $conn->prepare($stmt)){
		$query->bind_param("sss",$shopid,$sd1,$ed1);
		$query->execute();
		$rsresult = $query->get_result();
	}else{
		echo "Timeclock Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
	if ($rsresult->num_rows > 0) {
		while($rs = $rsresult->fetch_array()) {
			$el = $rs["el"];
			$ef = $rs["ef"];
			$tech = $rs["el"] . ", " . $rs["ef"];
			$rs['td'] = SEC_TO_TIME($rs['td']);

		$stmt = "SELECT tech, SUM(TIME_TO_SEC(enddatetime) - TIME_TO_SEC(startdatetime)) td from labortimeclock WHERE shopid = ? AND tech = ? AND startdatetime >= ? AND enddatetime <= ? ";
		if($query = $conn->prepare($stmt)){
			$query->bind_param("ssss",$shopid,$tech,$sd1,$ed1);
			$query->execute();
			$xrsresult = $query->get_result();
		}else{
			echo "Labor Time Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
		$xrs = $xrsresult->fetch_array();
		$xrs['td'] = SEC_TO_TIME($xrs['td']);
		$tcnumsectcar = 0;
		$tchourtosecs = 0;
		$tcmintosecs = 0;
		$tcsecstosecs = 0;
		$tctsecs = 0;
		$ltcnumsectcar = 0;
		$ltchourtosecs = 0;
		$ltcmintosecs = 0;
		$ltcsecstosecs = 0;
		$ltctsecs = 0;
		if (strpos($rs["td"],":") ) {
			$tcnumsectcar = explode(":", $rs["td"]);
			$tchourtosecs = $tcnumsectcar[0] * 60 * 60;
			$tcmintosecs = $tcnumsectcar[1] * 60;
			$tcsecstosecs = $tcnumsectcar[2];
			$tctsecs = $tchourtosecs + $tcmintosecs + $tcsecstosecs;
			//echo " time clock " . $tctsecs . "<br />";
		}else{
			$tcsecs = 1;
		}
		if (strpos($xrs["td"],":") ) {
		//if instr(xrs("td"),":") then
			// now same for ltc times
			$ltcnumsectcar = explode(":", $xrs["td"]);
			$ltchourtosecs = $ltcnumsectcar[0] * 60 * 60;
			$ltcmintosecs = $ltcnumsectcar[1] * 60;
			$ltcsecstosecs = $ltcnumsectcar[2];
			$ltctsecs = $ltchourtosecs + $ltcmintosecs + $ltcsecstosecs;
			//echo " labor " . $ltctsecs . "<br />";
		}else{
			$ltctsecs = 1;
		}
?>
			<tr>
				<td><?= strtoupper($rs["el"] . ", " . $rs["ef"]); ?></td>
				<td><?= $rs["td"]; ?></td>
				<td><?= $xrs["td"]; ?></td>
				<td><?php
						// should be 0.somehting
						if ($tctsecs > 0 ) {
							//echo ($ltctsecs / $tctsecs) . "<br />";
							$productivity = sbpround(($ltctsecs / $tctsecs),2) * 100 ;
							if ($productivity < 101) {
								echo $productivity . "%";
							}else{
								echo $productivity . "%";
							}
						}else{
							echo $productivity . "%";
						}
					?>
				</td>
			</tr>
    <?php
          $alldata[]=array(strtoupper($rs["el"] . ", " . $rs["ef"]), $rs["td"], $xrs["td"], $productivity . "%");//fill up the alldata array with the arrays of data to be shown in excel export
        } // end of while for loop
     }// end if for end of file

		 $alldata[]=array('','','','');
    ?>
		</table>
<?php

// Use this for Gen Pop Reports
require "includes/footer_reports.php";
require "includes/report_form.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/footer_reports.php");
//include("../../../php/includes/reports/report_form.php");
?>

</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
