<?php
$title = 'Inventory Summary';  // Report Title Goes Here
// Use this for Gen Pop Reports
require "includes/header_reports.php";
require CONN;
//require("../php/functions.php");

// Use this for Custom Reports
//require("../../../php/includes/reports/header_reports.php");
//require("../../../php/conn.php");
//require("../../functions.php");

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = date('m/d/Y');

// Page Variables
//$subtitle = 'my subtitle';  // Report SubTitle Goes Here - Hide if not needed
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport_autosize.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>

<title><?= $title;?></title>  <!-- Meta title for page. Change in variable above-->

<!DOCTYPE html>
<html>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>

<?php

// Use this for Gen Pop Reports
require "includes/report_buttons.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/report_buttons.php");
?>

<!-- Column Headers Insert Report Variables here -->

	<table class=report_table>
		<thead>
			<tr class="table_header table_head">
			<td>Part Category</td>
			<td>Inventory Cost</td>
			<td>Quantity</td>
		</tr>
	</thead>

    <?php
    $tablefields=array('Part Category','Inventory Cost','Quantity');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel
// Insert DB Query Here

// Template Query Begins - Replace entire section
	$gtcost = 0;
	$gtq = 0;
	$stmt = "select distinct category from category where shopid = ? order by category";
	if($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
		$query->execute();
		$roresult = $query->get_result();
	}else{
		echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
		while($ro = $roresult->fetch_array()) {
			$istmt = "select sum(PartCost) as ttlcost, count(*) as pcntr from partsinventory where shopid = ? and PartCategory = ?";
			if($query = $conn->prepare($istmt)){
				$query->bind_param("ss",$shopid,$ro["category"]);
				$query->execute();
				$iresult = $query->get_result();
			}else{
				echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
				while($iro = $iresult->fetch_array()) {
					$gtcost += $iro["ttlcost"];
					$gtq += $iro["pcntr"];
	?>
		<tr>
			<td><?= strtoupper($ro["category"]); ?></td>
			<td><?= asDollars($iro["ttlcost"]); ?></td>
			<td><?= $iro["pcntr"]; ?></td>
		</tr>
	    <?php
			$alldata[]=array(strtoupper($ro["category"]), asDollars($iro["ttlcost"]), $iro["pcntr"]);//fill up the alldata array with the arrays of data to be shown in excel export
	        } // end of while for loop
	     }// end if for end of file
			$alldata[]=array('','','');
			$alldata[]=array('TOTALS',asDollars($gtcost),$gtq);
	    ?>
	    <tr class="table_total">
	    <td><b>TOTAL</b></td>
	    <td><b><?= asDollars($gtcost);?></b></td>
	    <td><b><?= $gtq;?></b></td>
	    </tr>
	</table>

<?php

// Use this for Gen Pop Reports
require "includes/footer_reports.php";
require "includes/report_form_todaydate.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/footer_reports.php");
//include("../../../php/includes/reports/report_form.php");
?>

</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
