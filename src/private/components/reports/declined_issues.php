<?php
$title = 'Declined Vehicle Issues';
// Use this for Gen Pop Reports
require "includes/header_reports.php";
require CONN;
//require("../php/functions.php");

// Use this for Custom Reports
//require("../../../php/includes/reports/header_reports.php");
//require("../../../php/conn.php");
//require("../../functions.php");

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1=date('Y-m-d',strtotime($sd));
$ed1=date('Y-m-d',strtotime($ed));

$ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
$roTypeSQL = "AND rotype != 'No Approval'";
if ($ro_type != "all") {
    $roTypeSQL = "AND rotype = '" . $ro_type . "'";
}

// Page Variables
$title = 'Declined Vehicle Issues - ' . ucwords(strtolower($ro_type));  // Report Title Goes Here
//$subtitle = 'my subtitle';  // Report SubTitle Goes Here - Hide if not needed
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport_autosize.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>

<title><?= $title;?></title>  <!-- Meta title for page. Change in variable above-->

<!DOCTYPE html>
<html>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>

<?php

// Use this for Gen Pop Reports
require "includes/report_buttons.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/report_buttons.php");
?>

<!-- Column Headers Insert Report Variables here -->

	<table class=report_table>
	<tr class="table_header table_head">
		<td>RO #</td>
		<td>Date In</td>
		<td>Customer</td>
		<td>Cell Phone</td>
		<td>Vehicle</td>
		<td>Veh Issue</td>
		<td style="text-align:right">Total</td>
	</tr>

    <?php
    $tablefields=array('RO #','Date In','Customer','Cell Phone','Vehicle','Veh Issue','Total');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel


// Insert DB Query Here

// Template Query Begins - Replace entire section
		$runttlrec = 0;
		$stmt = "select ro.roid, datein, `customer`, cellphone, vehinfo,c.complaint,c.complaintid from repairorders ro,complaints c where c.shopid = ro.shopid and c.roid = ro.roid and ro.shopid = ? and datein >= ? and datein <= ? $roTypeSQL and c.acceptdecline = 'declined'";
		if($query = $conn->prepare($stmt)){
			$query->bind_param("sss",$shopid,$sd1,$ed1);
			$query->execute();
			$roresult = $query->get_result();
		}else{
			echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
			while($ro = $roresult->fetch_array()) {
				$roid = $ro["roid"];
				$statdate = date('m/d/Y',strtotime($ro["datein"]));
		?>
			<tr>
				<td><?= $ro["roid"]; ?></td>
				<td><?= $statdate; ?></td>
				<td><?= strtoupper($ro["customer"]); ?></td>
				<td><?= formatPhone($ro["cellphone"]); ?></td>
				<td><?= strtoupper($ro["vehinfo"]); ?></td>
				<td><?= strtoupper($ro["complaint"]); ?></td>
				<td style="text-align:right">
				<?php
					$cstmt = "select totalrec from recommend where comid = '".$ro['complaintid']."' and shopid = '$shopid' and roid = '$roid'";
					if($query = $conn->prepare($cstmt)){
						$query->execute();
						$query->bind_result($ttlrec);
						$query->fetch();
						$query->close();
					}else{
						echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
					}

					echo asDollars($ttlrec,2);
					$runttlrec += $ttlrec;
				?>
				</td>
			</tr>
		    <?php
					$alldata[]=array(strtoupper($ro["roid"]), $statdate, strtoupper($ro["customer"]), formatPhone($ro["cellphone"]), strtoupper($ro["vehinfo"]), strtoupper($ro["complaint"]), asDollars($ttlrec));//fill up the alldata array with the arrays of data to be shown in excel export
		        } // end of while for loop
		     // end if for end of file

					$alldata[]=array('','','','','','','');
					$alldata[]=array('TOTALS','','','','','',asDollars($runttlrec))
		    ?>
		    <tr>
				<td><b>Total Declined Issue Amount</b></td>
		    	<td></td>
		    	<td></td>
		    	<td></td>
		    	<td></td>
		    	<td></td>
		    	<td style="text-align:right"><b><?= asDollars($runttlrec); ?></b></td>
		    </tr>
		</table>
<?php

// Use this for Gen Pop Reports
require "includes/footer_reports.php";
require "includes/report_form.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/footer_reports.php");
//include("../../../php/includes/reports/report_form.php");
?>

</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
