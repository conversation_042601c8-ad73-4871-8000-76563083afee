<!DOCTYPE html>
<html>

<?php

/***********************************************************
12/2/19
Cloned after 14593...used template.php
***********************************************************/
	$title = 'Shop Production Detail Report';

	include('includes/report_config.php');


// Global Variables
	$shopid = $_COOKIE['shopid'];
	$date = new DateTime('now');
	$sd = $_GET['sdate'];
	$ed = $_GET['edate'];

	$ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
	$roTypeSQL = "AND rotype != 'No Approval'";
	if ($ro_type != "all") {
		$roTypeSQL = "AND rotype = '" . $ro_type . "'";
	}
// Page Variables

	$title .= ' - ' . ucwords(strtolower($ro_type));  // Report Title Goes Here
	$subtitle = 'This report has been modified to include only Closed Repair Orders. ';  // Report SubTitle Goes Here - Hide if not needed
// $subtitle = 'my subtitle';
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}


// Use this for Gen Pop Reports
// $template = 'excelexport.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
	//$template = '/sbpi2/reports/templates/excelexport_shopdetail.php';
	$template = COMPONENTS_PRIVATE."/reports/templates/excelexport_shopdetail.php"; //Only change if a custom PHPExcel is created in the template folder


	$sdate = date_format(new DateTime($sd),'Y-m-d');
	$edate = date_format(new DateTime($ed),'Y-m-d');

// Bringing in default tax rates
	$stmt = "SELECT replacerowithtag,defaulttaxrate,defaultlabortaxrate,defaultSubletTaxRate,userfee1taxable,userfee2taxable,userfee3taxable,hazwastetaxable,profitboost ";
	$stmt .= "FROM company ";
	$stmt .= "WHERE shopid = ? ";
	//echo $stmt;

	if($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
		$query->execute();
		$coresult = $query->get_result();
	}else{
		echo "Company Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

	$co = $coresult->fetch_array();

	$rrwt = strtolower($co["replacerowithtag"]);
	$defaulttaxrate = $co["defaulttaxrate"];
	$defaultlabortaxrate = $co["defaultlabortaxrate"];
	$defaultSubletTaxRate = $co["defaultSubletTaxRate"];
	$userfee1taxable = $co["userfee1taxable"];
	$userfee2taxable = $co["userfee2taxable"];
	$userfee3taxable = $co["userfee3taxable"];
	$hazwastetaxable = $co["hazwastetaxable"];
    $profitboost = $co['profitboost'];

// if there are any labor items with count(*) where schedulelength = 'no' then show both columns. Coming from canned jobs
// if shop labor ltr = 0 exclude
	$nlcount = 0;

	if ($defaultlabortaxrate != 0) {
		$nlstmt = "SELECT count(l.schedulelength) as 'NonLaborCount' ";
		$nlstmt .= " FROM repairorders ro";
		$nlstmt .= " JOIN labor l";
		$nlstmt .= "   ON ro.shopid = l.shopid and ro.roid = l.roid";
		$nlstmt .= " WHERE ro.shopid = ? ";
		$nlstmt .= "  AND ro.statusdate >= ? ";
		$nlstmt .= "  AND ro.status = 'Closed' $roTypeSQL";
		$nlstmt .= "  AND ro.statusdate <= ? ";
		$nlstmt .= "  AND l.schedulelength = 'no'";
		$nlstmt .= " ORDER BY ro.statusdate ";

		//echo $stmt;

		if($query = $conn->prepare($nlstmt)){
			$query->bind_param("sss",$shopid,$sdate,$edate);
			$query->execute();
			$nlresult = $query->get_result();
		}else{
			echo "Non Labor Count Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$nl = $nlresult->fetch_array();

		if ($nlresult->num_rows > 0) {
			$nlcount = $nl["NonLaborCount"];
			//echo "Non Labor Count is : " . $nlcount;
		}
	}

?>
<title><?= $title;?></title>  <!-- Meta title for page. Change in variable above-->
<!DOCTYPE html>
<html>
<head>
<link rel="stylesheet" type="text/css" href="<?= CSS ?>/reports.css">
</head>

<style>
tbody {
    height:750px;
    overflow:auto;
}

thead, tbody tr {
    display:table;
    width:100%;
    table-layout:fixed;
}

.auto-style1 {
	background-color: #5c90d2;
	color:white;
	font-weight:bold
}

tbody{
	display:inline;
}

.report_table2 {
	page-break-before:avoid !important;
}

@media print{
	tbody{
		display:inline !important;
	}
	tr{
		font-size:9px !important;
	}

}
</style>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>

<?php

// Use this for Gen Pop Reports
// require "includes/report_buttons.php";

// Use this for Custom Reports
include("includes/report_buttons_shopproddet.php");

?>

<table class=report_table2>
<thead>
	<tr class="table_header table_head">
		<td class="auto-style1">RO#</td>
		<td class="auto-style1">Status Date</td>
		<td class="auto-style1" style="text-align:left">Customer</td>
		<?php
		if ($defaultlabortaxrate > 0 || $nlcount > 0 ) {
		?>
		<td class="auto-style1" style="text-align:right">Taxable Labor</td>
		<td class="auto-style1" style="text-align:right">Non-Tax Labor</td>
		<?php
		}else{
		?>
		<td class="auto-style1" style="text-align:right">Labor</td>
		<?php
		}
		?>
		<td class="auto-style1" style="text-align:right">Taxable Parts</td>
		<td class="auto-style1" style="text-align:right">Non-Tax Parts</td>
		<td class="auto-style1" style="text-align:right">Sublet</td>
		<td class="auto-style1" style="text-align:right">Fees</td>
		<td class="auto-style1" style="text-align:right">Taxable Subtotal</td>
		<td class="auto-style1" style="text-align:right">Non-Tax Subtotal</td>
		<td class="auto-style1" style="text-align:right">Tax</td>
		<td class="auto-style1" style="text-align:right">Discount</td>
		<td class="auto-style1" style="text-align:right">Total RO</td>
		<td class="auto-style1" style="text-align:right">Parts Cost</td>
        <?php
            if($profitboost == "yes"){
                ?>
                <td class="auto-style1" style="text-align:right">PPH</td>
        <?php
            }
        ?>
	</tr>
</thead>
<?php

	//$tablefields=array('RO#','Status Date','Customer','Labor','Parts','Non Tax Parts','Tax Parts','Sublet','Fees','Taxable Subtotal','Non-Tax Subtotal','Tax','Discount','Total RO','Parts Cost');//set table headings array for excel export
    //$alldata=array();//this will hold all the data arrays to be exported to excel
	if ($defaultlabortaxrate > 0 || $nlcount > 0) {
		$alldata[]=array('TABLETITLE1','Repair Order Details');
		$alldataArr=array('TABLEHEAD','RO#','Status Date','Customer','Taxable Labor','Non-Tax Labor','Taxable Parts','Non-Tax Parts','Sublet','Fees','Taxable Subtotal','Non-Tax Subtotal','Tax','Discount','Total RO','Parts Cost');
	}else{
		$alldata[]=array('TABLETITLE2','Repair Order Details');
        $alldataArr=array('TABLEHEAD','RO#','Status Date','Customer','Labor','Taxable Parts','Non-Tax Parts','Sublet','Fees','Taxable Subtotal','Non-Tax Subtotal','Tax','Discount','Total RO','Parts Cost');
	}
    if($profitboost == "yes"){
        $alldataArr[] = "PPH";
    }
    $alldata[] = $alldataArr;

	$stmt = "SELECT roid,tagnumber,statusdate,totalprts,totallbr,totalro,totalsublet,totalfees,discountamt,salestax,customer,fleetno,partscost,taxrate,labortaxrate,sublettaxrate,userfee1,userfee2,userfee3,HazardousWaste,storagefee,PPH ";
	$stmt .= "FROM repairorders ";
	$stmt .= "WHERE shopid = ? ";
	$stmt .= "  AND statusdate >= ? ";
	$stmt .= "  AND repairorders.status = 'Closed' $roTypeSQL";
	$stmt .= "  AND statusdate <= ? ";
	$stmt .= " ORDER BY statusdate ";

	//echo $stmt;

	if($query = $conn->prepare($stmt)){
		$query->bind_param("sss",$shopid,$sdate,$edate);
		$query->execute();
		$roresult = $query->get_result();
	}else{
		echo "Company Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}

	$ro = mysqli_fetch_assoc($roresult);

	// resetting the result set pointer to beginning
			mysqli_data_seek($roresult, 0);
	?>

<?php
		$runningpmts = 0;
		$labor = 0;
		$parts = 0;
		$sublet = 0;
		$fees = 0;
		$slstax = 0;
		$pcost = 0;
		$ttldisc = 0;
		$ttlntparts = 0;
		$ttltparts = 0;
		$runntsubtotal = 0;
		$runsubtotal = 0;
		$ttldisc = 0;
		$ttldiscparts = 0;

		$tro = 0;
		$dfirstdate = "";

		$runtaxlabor = 0;
		$runnontaxlabor = 0;

		$gtruntaxlabor = 0;
		$gtrunnontaxlabor = 0;
		$gtttltparts = 0;
		$gtttlntparts = 0;
		$gtsublet = 0;
		$gtfees = 0;
		$gtrunsubtotal = 0;
		$gtrunntsubtotal = 0;
		$gtslstax = 0;
		$gtttldisc = 0;
		$gttro = 0;
		$gtpcost = 0;
		$rocount = $roresult->num_rows;
        $ttlPPH = 0;
		if ($rocount > 0) {
			while($ro = $roresult->fetch_array()) {
				$roid =  $ro["roid"];

				$statusdate = new DateTime($ro["statusdate"]);
				$statusdate = date_format($statusdate,'m/d/Y');

				$today = new DateTime('now');
				$today = date_format($today,'m/d/Y');

				if (strlen($ro["fleetno"]) > 0 ) {
					$fleetno = " (#" . $ro["fleetno"] .  ")";
				}else{
					$fleetno = "";
				}

				// Get sum of valid parts cost for roid

				$stmt = "SELECT coalesce(sum(cost*quantity),0) as pcost ";
				$stmt .= "FROM parts ";
				$stmt .= "WHERE shopid = ? ";
				$stmt .= "  AND roid = ? ";
				$stmt .= "  AND deleted = 'no'";
				//echo $stmt;

				if($query = $conn->prepare($stmt)){
					$query->bind_param("si",$shopid,$roid);
	   				$query->execute();
	    			$partresult = $query->get_result();
				}else{
					echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}

				if ($partresult->num_rows > 0) {

					$part = $partresult->fetch_array();

					if (is_null($part["pcost"]) ) {
						$mypcost = 0;
					}else{
						$mypcost = $part["pcost"];
					} // end of is null

				} // end of parts end if

				// all instances except adding into RO going to after labor read
				//$labor = $labor + $ro["totallbr"];

				$parts = $parts + $ro["totalprts"];
				$sublet = $sublet + $ro["totalsublet"];
				$fees =  $fees + $ro["totalfees"];
				$slstax = $slstax + $ro["salestax"];
				$pcost = $pcost + $mypcost;
				$ttldisc = $ttldisc + $ro["discountamt"];

				if ($rrwt == "yes") {
					$displayroid = $ro["tagnumber"];
				}else{
					$displayroid = $ro["roid"];
				}

				$ntparts = 0;
				$tparts = 0;
				$ntsubtotal = 0;
				$partsdiscount = 0;
				$discountamt = 0;

				// add back in the non taxable parts
				$stmt = "SELECT linettlprice as ntprice ";
				$stmt .= " FROM parts p ";
				$stmt .= " LEFT JOIN complaints c ";
				$stmt .= "   on p.shopid = c.shopid and p.complaintid = c.complaintid and p.ROID = c.roid ";
				$stmt .= "WHERE p.shopid = ? ";
				$stmt .= "  AND cstatus = 'no' ";
				$stmt .= "  AND deleted = 'no' AND tax = 'NO'";
				$stmt .= "  AND p.roid = ? ";
				//echo $stmt;

				if($query = $conn->prepare($stmt)){
					$query->bind_param("si",$shopid,$roid);
					$query->execute();
					$prsresult = $query->get_result();
				}else{
					echo "Parts Complaints Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}

				if ($prsresult->num_rows > 0) {

					while ($prs = $prsresult->fetch_array()) {

						$ntparts = $ntparts + $prs["ntprice"];

					} // end of while for parts complaint

				} // end if of parts complaint


				// moving this down below taxexempt
				//$ttlntparts = $ttlntparts + $ntparts;

				// add back in the taxable parts
				$stmt = "SELECT linettlprice as tprice ";
				$stmt .= " FROM parts p ";
				$stmt .= " LEFT JOIN complaints c ";
				$stmt .= "   on p.shopid = c.shopid and p.complaintid = c.complaintid and p.ROID = c.roid ";
				$stmt .= "WHERE p.shopid = ? ";
				$stmt .= "  AND cstatus = 'no' ";
				$stmt .= "  AND deleted = 'no' AND tax = 'YES'";
				$stmt .= "  AND p.roid = ? ";
				//echo $stmt;

				if($query = $conn->prepare($stmt)){
					$query->bind_param("si",$shopid,$roid);
					$query->execute();
					$prsresult = $query->get_result();
				}else{
					echo "Parts Complaints Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}

				if ($prsresult->num_rows > 0) {

					while ($prs = $prsresult->fetch_array()) {

						$tparts = $tparts + $prs["tprice"];
						//echo "Taxable Parts Line 347 " . $tparts . "</br>";

					} // end of while for parts complaint

				} // end if of parts complaint

				//tax exempt this trumps everything
				// keep from doubling up by seeing if non taxable part was already accounted for
//				if ($ro["taxrate"] == 0 && $ntparts == 0 ) {
				if ($ro["taxrate"] == 0 ) {
					if ($tparts > 0) {
						$ntparts = $ntparts + $tparts;
						$tparts =  0;
					}else{
						// because all parts regardless are non taxable when ro taxrate = 0 ..just pick up totalprts 3/12/20
						$ntparts = $ro["totalprts"];
						$tparts =  0;
					}
				} // end

				$ttlntparts = $ttlntparts + $ntparts;

				/*
				Move the discount read to here so tparts can be updated with parts discounts
				*/
				// add the discount amount on parts amount
				$stmt = "SELECT coalesce(sum(linettlprice),0) partsdiscount ";
				$stmt .= " FROM parts  ";
				$stmt .= "WHERE shopid = ? ";
				$stmt .= "  AND partnumber = 'DISCOUNT' ";
				$stmt .= "  AND roid = ? ";
				//echo $stmt;

				if($query = $conn->prepare($stmt)){
					$query->bind_param("si",$shopid,$roid);
					$query->execute();
					$prsresult = $query->get_result();
				}else{
					echo "Discount Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}

				// had numeric_format originally
				if ($prsresult->num_rows > 0) {
					while ($prs= $prsresult->fetch_array()) {
						$partsdiscount = $partsdiscount + $prs["partsdiscount"];
						$discountamt =  $discountamt + $partsdiscount + $ro["discountamt"];
					} // end while
				}

				// adding the discount to tparts rlk 1/31/20
				if ($ro["taxrate"] == 0 && $ro['labortaxrate']==0 && $ro['sublettaxrate']==0)
				{
				 $ntparts = $ntparts + $partsdiscount;
				 $ttlntparts = $ttlntparts + $partsdiscount;
			    }
				else
				{
				 $tparts = $tparts + $partsdiscount;
				 $ttltparts = $ttltparts + $tparts;
				}

				//echo "Taxable Parts Line 397 " . $tparts . "</br>";


				$gtttltparts = $gtttltparts + $tparts;

				// moving below labor
				//$ttldisc = $ttldisc + $discountamt;
				$ttldiscparts = $ttldiscparts + $partsdiscount;


				// check for labor discounts
				$discountlabortotal = 0;
				$labordiscount = 0;
				$totallabor = 0;

				$stmt = "SELECT coalesce(sum(LineTotal),0) labordiscount ";
				$stmt .= " FROM labor  ";
				$stmt .= "WHERE shopid = ? ";
				$stmt .= "  AND labor = 'DISCOUNT' ";
				$stmt .= "  AND roid = ? ";
				//echo $stmt;

				if($query = $conn->prepare($stmt)){
					$query->bind_param("si",$shopid,$roid);
					$query->execute();
					$lrsresult = $query->get_result();
				}else{
					echo "Lbr Discount Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}

				$lrs = $lrsresult->fetch_array();

				$labordiscount = $labordiscount + $lrs["labordiscount"];
				$discountamt =  $discountamt + $labordiscount + $ro["discountamt"];


				//need to sub out the labor discount for $totallabor
				//$totallabor = $ro["totallbr"] + ($labordiscount * -1);

				// need to check this ro totallbr is accurate here... for 10223
				//$totallabor = $ro["totallbr"] + $lrs["labordiscount"];

				// rlk 2/14/20 ... this needs a read out to labor
				//check for non taxable labor

				$taxlabor = 0;
				$nontaxlabor = 0;
				$regularlabor = 0;
				$ntrlabor = 0;

				$stmt = "select coalesce(sum(linetotal),0) linetotal from labor where shopid = ? and roid = ? and schedulelength = 'no'";

				if($query = $conn->prepare($stmt)){
					$query->bind_param("si",$shopid,$roid);
	   				$query->execute();
	    			$lbrresult = $query->get_result();
				}else{
					echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}

				if ($lbrresult->num_rows > 0) {

					$lbr = $lbrresult->fetch_array();
					// if labor tax rate = 0 roll into labor
					if ($defaultlabortaxrate == 0) {
					//$ntrlabor =	$lbr["linetotal"];
					}else{
						$nontaxlabor = $lbr["linetotal"];
					}
				} // end of nontax labor


				// if defaultlabor tax rate = 0 everything falls into the labor column (no tax)
				if ($defaultlabortaxrate == 0) {

					$regularlabor = $ro["totallbr"] ;
					$taxlabor = $regularlabor - $nontaxlabor;

					$runtaxlabor = $runtaxlabor + $taxlabor;
					$gtruntaxlabor = $gtruntaxlabor + $taxlabor;

					$runnontaxlabor = $runnontaxlabor + $nontaxlabor;
					$gtrunnontaxlabor = $gtrunnontaxlabor + $nontaxlabor;

				}else{
				//tax exempt this trumps everything change rlk 2/26/20 and defaulttaxrate != 0

					if ($ro["labortaxrate"] == 0) {
						$nontaxlabor =  $ro["totallbr"];
						$runnontaxlabor = $runnontaxlabor + $ro["totallbr"];
					}else{

						$regularlabor = $ro["totallbr"] ;
						$taxlabor = $regularlabor - $nontaxlabor;

						$runtaxlabor = $runtaxlabor + $taxlabor;
						$gtruntaxlabor = $gtruntaxlabor + $taxlabor;

						$runnontaxlabor = $runnontaxlabor + $nontaxlabor;
						$gtrunnontaxlabor = $gtrunnontaxlabor + $nontaxlabor;
					}
				}
				// adding the labordiscount to total discounts
				$ttldisc = $ttldisc + $discountamt ;
				// totalling labor after read of labor

				// start of subtotal logic
				$ntsubtotal = 0;
				$subtotal = 0;
				$ntflag = '';

				// need to qualify which taxrates to use rlk 1/27/20
				// assuming that ro tax rate = 0 is the same as defaultaxrate = 0

		//tax exempt this trumps everything
				if ($ro["taxrate"] == 0 && $ro['labortaxrate']==0 && $ro['sublettaxrate']==0) {
					$ntsubtotal = $ro["totallbr"] + $ro["totalprts"] + $ro["totalsublet"] + $ro["totalfees"];
					$runntsubtotal = $runntsubtotal + $ntsubtotal;
					$ntflag = " <b>(TaxExempt)</b>";
					$ntflagExB = " (TaxExempt)";
				}else{
					// does not need the $nlt because it is added above wiht shchedule length
					if ($defaultlabortaxrate == 0 ) {
						// change to correct ntsubtotal
						$ntsubtotal = $ntsubtotal + $ro["totallbr"];
						//$ntsubtotal = $ntsubtotal + $nontaxlabor;
					}
					if ($defaultSubletTaxRate == 0) {
						$ntsubtotal =  $ntsubtotal + $ro["totalsublet"];
					}
					if ($defaulttaxrate == 0) {
						$ntsubtotal =  $ntsubtotal + $ro["totalprts"];
					}
					if ($defaultlabortaxrate > 0 ) {
						$subtotal = $subtotal + $ro["totallbr"] ;
					}
					if ($defaultSubletTaxRate > 0) {
						$subtotal = $subtotal + $ro["totalsublet"] ;
					}
					// adjust based on non tax parts
					if ($defaulttaxrate > 0) {
						$subtotal =  $subtotal + ($ro["totalprts"] - $ntparts);
						$ntsubtotal =  $ntsubtotal + $ntparts;
					}

					//check for taxable userfees
					if ($userfee1taxable == 'Non-Taxable') {
						$ntsubtotal = $ntsubtotal + sbpround($ro["userfee1"]);
					}else{
						$subtotal = $subtotal + sbpround($ro["userfee1"]);
					}
					if ($userfee2taxable == 'Non-Taxable') {
						$ntsubtotal = $ntsubtotal + sbpround($ro["userfee2"]);
					}else{
						$subtotal = $subtotal + sbpround($ro["userfee2"]);
					}
					if ($userfee3taxable == 'Non-Taxable') {
						$ntsubtotal = $ntsubtotal + sbpround($ro["userfee3"]);
					}else{
						$subtotal = $subtotal + sbpround($ro["userfee3"]);
					}
					//check for hazwaste taxable

					if ($hazwastetaxable == 'yes') {
						$subtotal = $subtotal + $ro["HazardousWaste"];
					}else{
						$ntsubtotal = $ntsubtotal + $ro["HazardousWaste"];
					}

					//adding in storage rlk 3/6/20
					if ($ro["taxrate"] != 0) {
						$subtotal = $subtotal + $ro["storagefee"];
					}

					// ok adjusting subtotals based on tax labor and nontax labor

					$ntsubtotal = $ntsubtotal + $nontaxlabor;
					$subtotal = $subtotal - $nontaxlabor;

					$runntsubtotal = $runntsubtotal + $ntsubtotal;
					$runsubtotal = $runsubtotal + $subtotal;
				}

				// dont change this rlk
				$totalro = $ro["totallbr"]+$ro["totalprts"]+$ro["totalsublet"]+$ro["totalfees"]+$ro["salestax"]-$ro["discountamt"];
				$tro = $tro + $totalro;

?>
			<tr class="">
				<td style="cursor:pointer;" onclick="showRO(<?php echo $displayroid;?>)" ><?= $displayroid; ?></td>
				<td><?= $statusdate; ?></td>
				<td style="text-align:left"><?= strtoupper(left($ro["customer"],35)) . $fleetno .  $ntflag; ?></td>
				<?php
				if ($defaultlabortaxrate > 0 || $nlcount > 0 ) {
				?>
				<td style="text-align:right"><?php echo asDollars($taxlabor,2,".",$thousands_sep = ",");?></td>
				<td style="text-align:right"><?php echo asDollars($nontaxlabor,2,".",$thousands_sep = ",");?></td>
				<?php
				}else{
				?>
				<td style="text-align:right"><?php echo asDollars($taxlabor);?></td>
				<?php
				}
				?>
				<!-- rlk 2/14/20
				<td style="text-align:right"><?= asDollars($totallabor,2,".",$thousands_sep = ","); ?></td>
				-->
				<!-- rlk 2/18/20 removed from display
				<td style="text-align:right"><?= asDollars($ro["totalprts"],2,".",$thousands_sep = ","); ?></td>
				-->
				<td style="text-align:right"><?= asDollars($tparts,2,".",$thousands_sep = ","); ?></td>
				<td style="text-align:right"><?= asDollars($ntparts,2,".",$thousands_sep = ","); ?></td>
				<td style="text-align:right"><?= asDollars($ro["totalsublet"],2,".",$thousands_sep = ","); ?></td>
				<td style="text-align:right"><?= asDollars($ro["totalfees"],2,".",$thousands_sep = ","); ?></td>
				<td style="text-align:right"><?= asDollars($subtotal,2,".",$thousands_sep = ",");?></td>
				<td style="text-align:right"><?= asDollars($ntsubtotal,2,".",$thousands_sep = ",");?></td>
				<td style="text-align:right"><?= asDollars($ro["salestax"],2,".",$thousands_sep = ","); ?></td>
				<td style="text-align:right"><?= asDollars($discountamt,2,".",$thousands_sep = ","); ?></td>
				<td style="text-align:right"><?= asDollars($totalro,2,".",$thousands_sep = ","); ?></td>
				<td style="text-align:right"><?= asDollars($mypcost,2,".",$thousands_sep = ","); ?></td>
                <?php
                if($profitboost == "yes"){
                    $ttlPPH += $ro['PPH'];
                    ?>
                    <td style="text-align:right"><?= asDollars($ro["PPH"]) ?></td>
                    <?php
                }
                ?>
			</tr>

<?php

				if ($defaultlabortaxrate > 0  || $nlcount > 0) {
					$alldataArr=array($displayroid,$statusdate, strtoupper(left($ro["customer"],35)) . $fleetno .  $ntflagExB, asDollars($taxlabor,2,".",$thousands_sep = ","),asDollars($nontaxlabor,2,".",$thousands_sep = ","),asDollars($tparts,2,".",$thousands_sep = ","),asDollars($ntparts,2,".",$thousands_sep = ","),asDollars($ro["totalsublet"],2,".",$thousands_sep = ","),asDollars($ro["totalfees"],2,".",$thousands_sep = ","),asDollars($subtotal,2,".",$thousands_sep = ","),asDollars($ntsubtotal,2,".",$thousands_sep = ","),asDollars($ro["salestax"],2,".",$thousands_sep = ","),asDollars($discountamt,2,".",$thousands_sep = ",")	,asDollars($totalro,2,".",$thousands_sep = ","),asDollars($mypcost,2,".",$thousands_sep = ",")); //fill up the alldata array with the arrays of data to be shown in excel export
				}else{
					$alldataArr = array($displayroid, $statusdate, strtoupper(left($ro["customer"],35)) . $fleetno .  $ntflagExB, asDollars($taxlabor,2,".",$thousands_sep = ","),asDollars($tparts,2,".",$thousands_sep = ","),asDollars($ntparts,2,".",$thousands_sep = ","),asDollars($ro["totalsublet"],2,".",$thousands_sep = ","),asDollars($ro["totalfees"],2,".",$thousands_sep = ","),asDollars($subtotal,2,".",$thousands_sep = ","),asDollars($ntsubtotal,2,".",$thousands_sep = ","),asDollars($ro["salestax"],2,".",$thousands_sep = ","),asDollars($discountamt,2,".",$thousands_sep = ",")	,asDollars($totalro,2,".",$thousands_sep = ","),asDollars($mypcost,2,".",$thousands_sep = ",")); //fill up the alldata array with the arrays of data to be shown in excel export
				}
                if($profitboost == "yes"){
                    $alldataArr[] = asDollars($ro['PPH']);
                }
                $alldata[] = $alldataArr;

			} // end of while

			if ($defaultlabortaxrate > 0 || $nlcount > 0) {
				$alldata[]=array('','','','','','','','','','','','','','','');
				$alldataArr = array('RO Totals', '('.$rocount.')','',asDollars($runtaxlabor,2,".",$thousands_sep = ","),asDollars($runnontaxlabor,2,".",$thousands_sep = ","),asDollars($ttltparts,2,".",$thousands_sep = ",")
				,asDollars($ttlntparts,2,".",$thousands_sep = ","),asDollars($sublet,2,".",$thousands_sep = ",")
				,asDollars($fees,2,".",$thousands_sep = ","),asDollars($runsubtotal,2,".",$thousands_sep = ","),asDollars($runntsubtotal,2,".",$thousands_sep = ",")
				,asDollars($slstax,2,".",$thousands_sep = ","),asDollars($ttldisc,2,".",$thousands_sep = ","),asDollars($tro,2,".",$thousands_sep = ","),asDollars($pcost,2,".",$thousands_sep = ",")
				);

			}else{
				$alldata[]=array('','','','','','','','','','','','','','');
				$alldataArr = array('RO Totals', '('.$rocount.')','',asDollars($runtaxlabor,2,".",$thousands_sep = ","),asDollars($ttltparts,2,".",$thousands_sep = ",")
				,asDollars($ttlntparts,2,".",$thousands_sep = ","),asDollars($sublet,2,".",$thousands_sep = ",")
				,asDollars($fees,2,".",$thousands_sep = ","),asDollars($runsubtotal,2,".",$thousands_sep = ","),asDollars($runntsubtotal,2,".",$thousands_sep = ",")
				,asDollars($slstax,2,".",$thousands_sep = ","),asDollars($ttldisc,2,".",$thousands_sep = ","),asDollars($tro,2,".",$thousands_sep = ","),asDollars($pcost,2,".",$thousands_sep = ",")
				);
			}
            if($profitboost == "yes"){
                $alldataArr[] = asDollars(($rocount > 0) ? ($ttlPPH / $rocount) : 0);
            }
            $alldata[] = $alldataArr;

		}else{
			echo "<tr><td colspan='14' class='alert-warning text-center'>No Repair Orders found</td></tr>";
		} //end if
?>
<tr></tr>
<tr class="table_total">

		<td><b>RO Totals &nbsp;(<?= $rocount ?>)</b></td>
		<td class="text-right"></td>
		<td class="text-right"></td>
		<?php
		if ($defaultlabortaxrate > 0 || $nlcount > 0) {
		?>
		<td class="text-right"><b><?= asDollars($runtaxlabor,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($runnontaxlabor,2,".",$thousands_sep = ","); ?></b></td>
		<?php
		}else{
		?>
		<td class="text-right"><b><?= asDollars($runtaxlabor,2,".",$thousands_sep = ","); ?></b></td>
		<?php
		}
		?>
		<td class="text-right"><b><?= asDollars($ttltparts,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($ttlntparts,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($sublet,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($fees,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($runsubtotal,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right" ><b><?= asDollars($runntsubtotal,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($slstax,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($ttldisc,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($tro,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($pcost,2,".",$thousands_sep = ","); ?></b></td>
    <?php
    if($profitboost == "yes"){
        ?>
        <td style="text-align:right"><?= asDollars(($rocount > 0) ? ($ttlPPH / $rocount) : 0) ?></td>
        <?php
    }
    ?>
</tr>

</table>

<!--now get the part sales  -->


<table class="report_table">


<?php
	$tntparts = 0;
	$ttparts = 0;
	$ntflag = "";

	$pttltparts = 0;
	$pttlntparts = 0;
	$psublet = 0;
	$pfees = 0;
	$prunsubtotal = 0;
	$prunntsubtotal = 0;
	$pslstax = 0;
	$pttldisc = 0;
	$ptro = 0;
	$ppcost = 0;

	$stmt = "SELECT * ";
	$stmt .= "FROM ps ";
	$stmt .= "WHERE shopid = ? ";
	$stmt .= "  AND statusdate >= ? ";
	$stmt .= "  AND statusdate <= ? ";
	$stmt .= "  AND `status` = 'Closed'";

	if($query = $conn->prepare($stmt)){
		$query->bind_param("sss",$shopid,$sdate,$edate);
		$query->execute();
		$psresult = $query->get_result();
	}else{
		echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
    $pscount = $psresult->num_rows;
	if ($psresult->num_rows > 0) {

?>
<thead>
	<tr class="table_header table_head">
		<td class="auto-style1">PS#</td>
		<td class="auto-style1">Status Date</td>
		<td class="auto-style1" style="text-align:left">Customer</td>
<?php
		if ($defaultlabortaxrate > 0  || $nlcount > 0) {
?>
		<td class="auto-style1" style="text-align:right"></td>
		<td class="auto-style1" style="text-align:right"></td>
<?php
		}else{
?>
		<td class="auto-style1" style="text-align:right"></td>
<?php
		}
?>
		<td class="auto-style1" style="text-align:right">Taxable Parts</td>
		<td class="auto-style1" style="text-align:right">Non-Tax Parts</td>
		<td class="auto-style1" style="text-align:right"></td>
		<td class="auto-style1" style="text-align:right"></td>
		<td class="auto-style1" style="text-align:right">Taxable Subtotal</td>
		<td class="auto-style1" style="text-align:right">Non-Tax Subtotal</td>
		<td class="auto-style1" style="text-align:right">Tax</td>
		<td class="auto-style1" style="text-align:right">Discount</td>
		<td class="auto-style1" style="text-align:right">Total Sales</td>
		<td class="auto-style1" style="text-align:right">Parts Cost</td>
        <?php
        if($profitboost == "yes"){
            ?>
            <td class="auto-style1" style="text-align:right"></td>
            <?php
        }
        ?>
	</tr>
</thead>

<?php

	//$tablefields=array('PS#','Sale Date','Customer','','Parts','','','','','','','Tax','','Total Sale','Parts Cost');//set table headings array for excel export
    //$alldata=array();//this will hold all the data arrays to be exported to excel
	$alldata[]=array('');

	if ($defaultlabortaxrate > 0 || $nlcount > 0) {

		$alldata[]=array('TABLETITLE3','Part Sale Details');
		$alldataArr=array('TABLEHEAD','PS#','Status Date','Customer','','','Taxable Parts','Non-Tax Parts','','','Taxable Subtotal','Non-Tax Subtotal','Tax','Discount','Total Sale','Parts Cost');
	}else{
		$alldata[]=array('TABLETITLE4','Part Sale Details');
		$alldataArr=array('TABLEHEAD','PS#','Status Date','Customer','','Taxable Parts','Non-Tax Parts','','','Taxable Subtotal','Non-Tax Subtotal','Tax','Discount','Total Sale','Parts Cost');
	}
    if($profitboost == "yes"){
        $alldataArr[] = "";
    }
    $alldata[] = $alldataArr;


	while($ps = $psresult->fetch_array()) {
		$customerid = $ps["cid"];
		$psid = $ps["psid"];
		// changed from psdate to status date
		$statusdate =  new DateTime($ps["statusdate"]);
		$statusdate = date_format($statusdate,'m/d/Y');

		$stmt = "select concat(lastname,',',firstname) as lf,taxexempt from customer where customerid = ? ";
		$stmt = $stmt .  " and shopid = ?";

		if($query = $conn->prepare($stmt)){
			$query->bind_param("ss",$customerid,$shopid);
			$query->execute();
			$trsresult = $query->get_result();
		}else{
			echo "customer Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$trs = mysqli_fetch_assoc($trsresult);

		$lf = $trs["lf"];

		// check for tax exempt
		$taxexempt = $trs["taxexempt"];

		if ($taxexempt == "yes") {
			$ntflag = " <b>(TaxExempt)</b>";
			$ntflagExB = " (TaxExempt)";
		}else{
			$ntflag = "";
			$ntflagExB = "";
		}

		$parts = $parts + ($ps["subtotal"] - $ps["fees"]);
		$pslstax = $pslstax + $ps["tax"];

		// adding discount
		$pttldisc = $pttldisc + $ps["discount"];

		$ptro = $ptro + $ps["total"];

		//get total part cost
		$stmt = "select sum(qty*cost) as extcost from psdetail where psid = ? ";
		$stmt = $stmt . " and shopid = ? ";

		if($query = $conn->prepare($stmt)){
			$query->bind_param("ss",$psid,$shopid);
			$query->execute();
			$trsresult = $query->get_result();
		}else{
			echo "customer Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$trs = mysqli_fetch_assoc($trsresult);

		if (!empty($trsresult)) {
			if (strlen($trs["extcost"]) > 0 ){
				$mypcost = $trs["extcost"];
			}else{
				$mypcost = 0;
			}
		}else{
			$mypcost = 0;
		}

		$ppcost = $ppcost + $mypcost;
		// get non taxable parts
		$stmt = "select sum(ext) as ntprice from psdetail where lcase(tax) = 'no' and shopid = ? and psid = ? ";

		if($query = $conn->prepare($stmt)){
			$query->bind_param("si",$shopid,$psid);
			$query->execute();
			$ntpresult = $query->get_result();
		}else{
			echo "Net Price Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$ntp = mysqli_fetch_assoc($ntpresult);

		if (!empty($ntpresult)) {

			if (!is_null($ntp["ntprice"])) {
				// number format out here
				$ntparts = $ntp["ntprice"];
			}else{
				$ntparts = "0.00";
			}

		}else{
			$ntparts = 0;
		}

		// get taxable parts
		$stmt = "select sum(ext) as ntprice from psdetail where lcase(tax) = 'yes' and shopid = ? and psid = ? ";

		if($query = $conn->prepare($stmt)){
			$query->bind_param("si",$shopid,$psid);
			$query->execute();
			$prsresult = $query->get_result();
		}else{
			echo "Net Price Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}

		$prs = mysqli_fetch_assoc($prsresult);

		if (!empty($prsresult)) {

			if (!is_null($prs["ntprice"])) {
				$tparts = $prs["ntprice"];
			}else{
				$tparts = "0.00";
			}

		}else{
			$tparts = 0;
		}
		//tax exempt this trumps everything
		if ($taxexempt == 'yes') {
			$ntparts = $ps["subtotal"];
			$tparts =  0;
		} // end

		$pttlntparts = $pttlntparts + $ntparts;
		$pttltparts = $pttltparts + $tparts;

		$prunntsubtotal = $prunntsubtotal + $ntparts;
		$prunsubtotal = $prunsubtotal + $tparts;

		//Note ntpart and tparts is also non taxaxable subtotal and taxable subtotal
?>
	<tr class="">
		<td ><?= $ps["psid"]; ?></td>
		<td><?= $statusdate; ?></td>
		<td style="text-align:left"><?= strtoupper(left($lf,35)) . $ntflag ; ?></td>
		<?php
		if ($defaultlabortaxrate > 0 || $nlcount > 0) {
		?>
		<td style="text-align:right"></td>
		<td style="text-align:right"></td>
		<?php
		}else{
		?>
		<td style="text-align:right"></td>
		<?php
		}
		?>
		<td style="text-align:right"><?php echo asDollars($tparts,2); ?></td>
		<td style="text-align:right"><?php echo asDollars($ntparts,2);?></td>
		<td style="text-align:right"></td>
		<td style="text-align:right"></td>
		<td style="text-align:right"><?php echo asDollars($tparts); ?></td>
		<td style="text-align:right"><?php echo asDollars($ntparts); ?></td>
		<td style="text-align:right"><?php echo asDollars($ps["tax"],2); ?></td>
		<td style="text-align:right"><?php echo asDollars($ps["discount"],2); ?></td>
		<td style="text-align:right"><?php echo asDollars($ps["total"],2); ?></td>
		<td style="text-align:right"><?php echo asDollars($mypcost,2); ?></td>
        <?php
        if($profitboost == "yes"){
            ?>
            <td style="text-align:right"></td>
            <?php
        }
        ?>
	</tr>

<?php

		if ($defaultlabortaxrate > 0  || $nlcount > 0) {
			$alldataArr=array($ps["psid"],$statusdate,strtoupper(left($lf,25)),'','',asDollars($tparts,2),asDollars($ntparts,2),'','',asDollars($tparts,2),asDollars($ntparts,2),asDollars($ps["tax"],2),'',asDollars($ps["total"],2),asDollars($mypcost,2)); //fill up the alldata array with the arrays of data to be shown in excel export
		}else{
			$alldataArr=array($ps["psid"],$statusdate,strtoupper(left($lf,25)),'',asDollars($tparts,2),asDollars($ntparts,2),'','',asDollars($tparts,2),asDollars($ntparts,2),asDollars($ps["tax"],2),'',asDollars($ps["total"],2),asDollars($mypcost,2)); //fill up the alldata array with the arrays of data to be shown in excel export
		}
        if($profitboost == "yes"){
            $alldataArr[] = "";
        }
        $alldata[] = $alldataArr;
	} // end of while
} //end if
?>
<tr class="table_total">

		<td><b>PS Totals &nbsp;(<?= $pscount ?>)</b></td>
		<td class="text-right"></td>
		<td class="text-right"></td>
		<?php
		if ($defaultlabortaxrate > 0 || $nlcount > 0) {
		?>
		<td class="text-right"><b><?= ''; ?></b></td>
		<td class="text-right"><b><?= ''; ?></b></td>
		<?php
		}else{
		?>
		<td class="text-right"><b><?= ''; ?></b></td>
		<?php
		}
		?>
		<td class="text-right"><b><?= asDollars($pttltparts,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($pttlntparts,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($psublet,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($pfees,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($prunsubtotal,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right" ><b><?= asDollars($prunntsubtotal,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($pslstax,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($pttldisc,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($ptro,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($ppcost,2,".",$thousands_sep = ","); ?></b></td>
        <?php
        if($profitboost == "yes"){
            ?>
            <td style="text-align:right"></td>
            <?php
        }
        ?>
</tr>
<?php
		if ($defaultlabortaxrate > 0 || $nlcount > 0) {
				$alldata[]=array('','','','','','','','','','','','','','','');
				$alldataArr =array('PS Totals', '('.$pscount.')','','','',asDollars($pttltparts,2,".",$thousands_sep = ",")
				,asDollars($pttlntparts,2,".",$thousands_sep = ","),asDollars($psublet,2,".",$thousands_sep = ",")
				,asDollars($pfees,2,".",$thousands_sep = ","),asDollars($prunsubtotal,2,".",$thousands_sep = ","),asDollars($prunntsubtotal,2,".",$thousands_sep = ",")
				,asDollars($pslstax,2,".",$thousands_sep = ","),asDollars($pttldisc,2,".",$thousands_sep = ","),asDollars($ptro,2,".",$thousands_sep = ","),asDollars($ppcost,2,".",$thousands_sep = ",")
				);

		}else{
				$alldata[]=array('','','','','','','','','','','','','','');
				$alldataArr=array('PS Totals', '('.$pscount.')','','',asDollars($pttltparts,2,".",$thousands_sep = ",")
				,asDollars($pttlntparts,2,".",$thousands_sep = ","),asDollars($psublet,2,".",$thousands_sep = ",")
				,asDollars($pfees,2,".",$thousands_sep = ","),asDollars($prunsubtotal,2,".",$thousands_sep = ","),asDollars($prunntsubtotal,2,".",$thousands_sep = ",")
				,asDollars($pslstax,2,".",$thousands_sep = ","),asDollars($pttldisc,2,".",$thousands_sep = ","),asDollars($ptro,2,".",$thousands_sep = ","),asDollars($ppcost,2,".",$thousands_sep = ",")
				);
		}
        if($profitboost == "yes"){
             $alldataArr[] = "";
        }
        $alldata[] = $alldataArr;

		$gtttltparts = $pttltparts + $ttltparts;
		$gtttlntparts = $pttlntparts + $ttlntparts;
		$gtsublet = $psublet + $sublet;
		$gtfees = $pfees + $fees;
		$gtrunsubtotal = $prunsubtotal + $runsubtotal;
		$gtrunntsubtotal = $prunntsubtotal + $runntsubtotal;
		$gtslstax = $pslstax + $slstax;
		$gtttldisc = $pttldisc + $ttldisc;
		$gttro = $ptro + $tro;
		$gtpcost = $ppcost + $pcost;

		// this should be fine
		$gtrunnontaxlabor = $runnontaxlabor;

		$totalCount = $rocount + $pscount;

?>


<tr class="table_total">

		<td><b>Grand Totals &nbsp;(<?= $totalCount ?>)</b></td>
		<td class="text-right"></td>
		<td class="text-right"></td>
		<?php
		if ($defaultlabortaxrate > 0 || $nlcount > 0) {
		?>
		<td class="text-right"><b><?= asDollars($gtruntaxlabor,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($gtrunnontaxlabor,2,".",$thousands_sep = ","); ?></b></td>
		<?php
		}else{
		?>
		<td class="text-right"><b><?= asDollars($gtruntaxlabor,2,".",$thousands_sep = ","); ?></b></td>
		<?php
		}
		?>
		<td class="text-right"><b><?= asDollars($gtttltparts,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($gtttlntparts,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($gtsublet,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($gtfees,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($gtrunsubtotal,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right" ><b><?= asDollars($gtrunntsubtotal,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($gtslstax,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($gtttldisc,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($gttro,2,".",$thousands_sep = ","); ?></b></td>
		<td class="text-right"><b><?= asDollars($gtpcost,2,".",$thousands_sep = ","); ?></b></td>
        <?php
        if($profitboost == "yes"){
            ?>
            <td style="text-align:right"><?= asDollars(($rocount > 0) ? ($ttlPPH / $rocount) : 0) ?></td>
            <?php
        }
        ?>
</tr>
<?php
		if ($defaultlabortaxrate > 0 || $nlcount > 0) {
			$alldata[]=array('','','','','','','','','','','','','','','');
			$alldataArr = array('Grand Totals', '('.$totalCount.')','',asDollars($gtruntaxlabor,2,".",$thousands_sep = ","),asDollars($gtrunnontaxlabor,2,".",$thousands_sep = ","),asDollars($gtttltparts,2,".",$thousands_sep = ",")
			,asDollars($gtttlntparts,2,".",$thousands_sep = ","),asDollars($gtsublet,2,".",$thousands_sep = ",")
			,asDollars($gtfees,2,".",$thousands_sep = ","),asDollars($gtrunsubtotal,2,".",$thousands_sep = ","),asDollars($gtrunntsubtotal,2,".",$thousands_sep = ",")
			,asDollars($gtslstax,2,".",$thousands_sep = ","),asDollars($gtttldisc,2,".",$thousands_sep = ","),asDollars($gttro,2,".",$thousands_sep = ","),asDollars($gtpcost,2,".",$thousands_sep = ",")
			);

		}else{
			$alldata[]=array('','','','','','','','','','','','','','');
			$alldataArr = array('Grand Totals', '('.$totalCount.')','',asDollars($runtaxlabor,2,".",$thousands_sep = ","),asDollars($ttltparts,2,".",$thousands_sep = ",")
			,asDollars($gtttlntparts,2,".",$thousands_sep = ","),asDollars($gtsublet,2,".",$thousands_sep = ",")
			,asDollars($gtfees,2,".",$thousands_sep = ","),asDollars($gtrunsubtotal,2,".",$thousands_sep = ","),asDollars($gtrunntsubtotal,2,".",$thousands_sep = ",")
			,asDollars($gtslstax,2,".",$thousands_sep = ","),asDollars($gtttldisc,2,".",$thousands_sep = ","),asDollars($gttro,2,".",$thousands_sep = ","),asDollars($gtpcost,2,".",$thousands_sep = ",")
			);
		}
        if($profitboost == "yes"){
            $alldataArr[] = asDollars(($rocount > 0) ? ($ttlPPH / $rocount) : 0);
        }
        $alldata[] = $alldataArr;
?>

</table>

<?php

	include('includes/footer_reports.php');
	include('includes/report_form.php');

?>

</body>
<script language="javascript">
function showRO(roid){
	t = "View RO"
	p = "<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid="+roid
	eModal.iframe({
		title:t,
		url: p,
		size: eModal.size.xl,
		buttons: [
			{text: 'Close', style: 'warning', close:true}
    	]
	});

}
</script>

</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
