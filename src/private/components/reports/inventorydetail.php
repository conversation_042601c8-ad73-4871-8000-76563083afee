<!DOCTYPE html>
<html>
<?php

    $title = "Inventory Report";
    $sd = date('m/d/Y');

    include_once "includes/report_config.php";

    $shopid = $_COOKIE['shopid'];

    $template = COMPONENTS_PRIVATE."/reports/templates/excelexport.php";
    $stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
    if ($query = $conn->prepare($stmt)){
        $query->bind_param("s",$shopid);
        $query->execute();
        $query->bind_result($companyname,$conamereports);
        $query->fetch();
        $query->close();
    }

    if($conamereports == 'yes'){
        $title .= " - $companyname";
    }
?>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
<?php
    include_once "includes/report_buttons.php";

    $tablefields = array('CNT', 'Part Number', 'Part Description', 'Price', 'Cost', 'Quantity', 'TTL Cost');
    $alldata = array();
?>

</div>
<table class="report_table">
    <tr class="table_header">
        <td class="auto-style1"><strong>CNT</strong></td>
        <td class="auto-style1"><strong>Part Number</strong></td>
        <td class="auto-style1"><strong>Part Description</strong></td>
        <td class="auto-style3" style="text-align: right;"><strong>Price</strong></td>
        <td class="auto-style3" style="text-align: right;"><strong>Cost</strong></td>
        <td class="auto-style3" style="text-align: right;"><strong>Qty</strong></td>
        <td class="auto-style3" style="text-align: right;"><strong>TTL Cost</strong></td>
    </tr>
    <?php
    $runningcost = 0;
    $runningprice = 0;
    $runningqty = 0;

    $stmt = "select * from partsinventory where shopid = ? order by partnumber";
    if($query = $conn->prepare($stmt)){
        $query->bind_param("s", $shopid);
        $query->execute();
        $results = $query->get_result();
        $query->close();
    }
    $c = 0;
    if($results->num_rows > 0){
        while($rs = $results->fetch_assoc()){
    $c = $c + 1;
    $runningcost = $runningcost + ($rs["PartCost"] * $rs["NetOnHand"]);
    $runningprice = $runningprice + ($rs["PartPrice"] * $rs["NetOnHand"]);
    $runningqty = $runningqty + $rs["NetOnHand"];
    ?>
    <tr>
        <td><?= $c ?>&nbsp;</td>
        <td><?= strtoupper($rs["PartNumber"]) ?>&nbsp;</td>
        <td><?= strtoupper($rs["PartDesc"]) ?>&nbsp;</td>
        <td class="text-right"><?= asDollars($rs["PartPrice"]) ?>&nbsp;</td>
        <td class="text-right"><?= asDollars($rs["PartCost"]) ?>&nbsp;</td>
        <td class="text-right"><?= $rs["NetOnHand"] ?>&nbsp;</td>
        <td class="text-right"><?= asDollars($rs["PartCost"] * $rs["NetOnHand"]) ?>&nbsp;</td>
    </tr>
    <?php
            $alldata[] = array($c, strtoupper($rs['PartNumber']), strtoupper($rs['PartDesc']),asDollars($rs["PartPrice"]),asDollars($rs["PartCost"]), $rs["NetOnHand"], asDollars($rs["PartCost"] * $rs["NetOnHand"]));
        }
    ?>
    <tr class="table_total" style="font-weight: bold">
        <td class="text-right"><strong>TOTALS:</strong></td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td class="text-right"><?= asDollars($runningprice) ?>&nbsp;</td>
        <td class="text-right">&nbsp;</td>
        <td class="text-right"><?= number_format($runningqty,2) ?>&nbsp;</td>
        <td class="text-right"><?= asDollars($runningcost) ?>&nbsp;</td>
    </tr>
    <?php
        $alldata[] = array('','','','','','','');
        $alldata[] = array('TOTALS', '','', asDollars($runningprice), '', number_format($runningqty, 2), asDollars($runningcost));
    }
    ?>
</table>
<?php
    require "includes/report_form_todaydate.php";
    include_once "includes/footer_reports.php";
?>
</body>

</html>
