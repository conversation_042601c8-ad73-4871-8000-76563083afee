<!DOCTYPE html>
<html>
<?php

$shopid = $_COOKIE['shopid'];

$sd = isset($_GET['sdate']) ? filter_var($_GET['sdate'], FILTER_SANITIZE_STRING) : "";
$ed = isset($_GET['edate']) ? filter_var($_GET['edate'], FILTER_SANITIZE_STRING) : "";
$cannedjob = isset($_GET['cannedjob']) ? filter_var($_GET['cannedjob'], FILTER_SANITIZE_STRING) : "all";

$sdate = date("Y-m-d", strtotime($sd));
$edate = date("Y-m-d", strtotime($ed));

$ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
$roTypeSQL = "AND ro.rotype != 'No Approval'";
if ($ro_type != "all") {
    $roTypeSQL = "AND rotype = '" . $ro_type . "'";
}

$title = "Closed Repair Orders with Canned Jobs Detail - " . ucwords(strtolower($ro_type));

include_once  COMPONENTS_PRIVATE_PATH. "/reports/includes/report_config.php";
$template = COMPONENTS_PRIVATE."/reports/templates/excelexport_multiple.php";
?>

<body>

<?php
include_once  COMPONENTS_PRIVATE_PATH. "/reports/includes/report_buttons.php";
?>


<?php
$cjSQL = "cannedjobsid != 0";
if($cannedjob != "all"){

}

$totlbrflatprice = 0;
$totpartsprice = 0;
$totpartscost = 0;
$rolist = $complaint_list = "";
$ttlcount = $ttllbrcost = $ttllbrprice = $ttlpartscost  = $ttlpartsprice = $ttllbrhrs = $ttlCost = $ttlprice  = 0;

$complaints = array();
if ($query = $conn->prepare("SET SESSION group_concat_max_len = 1000000")) {
    $query->execute();
}
$stmt = "SELECT group_concat(ro.roid) as rostr FROM repairorders ro WHERE ro.shopid = ?  AND ro.statusdate >= ?  AND ro.statusdate <= ?  AND ro.status = 'Closed' $roTypeSQL order by StatusDate desc";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("sss", $shopid, $sdate, $edate);
    $query->execute();
    $query->bind_result($rolist);
    $query->fetch();
    $query->close();
}
//echo $rolist." <br /> \n";
$stmt = "SELECT complaintid, complaint FROM complaints c WHERE c.shopid = ? AND roid IN ($rolist) AND cstatus = 'no'";

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($comp_id, $complaint);
    while ($query->fetch()) {
        $complaints[$comp_id] = strtoupper($complaint);
    }
    $query->close();
}

//print_r($complaints);
//echo "<br /> \n";

$cannedjobids = array();
if($cannedjob == "all") {
    $stmt = "SELECT cannedjobsid FROM parts where deleted = 'no' AND shopid = ? AND ROID IN ($rolist) AND cannedjobsid != 0";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($cj_id);
        while ($query->fetch()) {
            $cannedjobids[] = $cj_id;
        }
        $query->close();
    }

    $stmt = "SELECT cannedjobsid FROM labor where deleted = 'no' AND shopid = ? AND labor.ROID IN ($rolist) AND cannedjobsid != 0";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($cj_id);
        while ($query->fetch()) {
            $cannedjobids[] = $cj_id;
        }
        $query->close();
    }
//populated canned jobs unique array from parts and labor used in RO during the date span.
    $cannedjobids = array_unique($cannedjobids);
} else {
    $cannedjobids[] = $cannedjob;
}


$tablefields = array("RO", "Vehicle Issue", "Job", "Job Labor", "Count", "Labor Hours", "Labor Cost", "Parts cost");
$alldata = array();
?>
<table class="report_table">
    <?php
    foreach ($cannedjobids as $cj_id) {
        //GET canned job name and labor
        $num_canned = 0;

        $subttlcount = $subttllbrcost = $subttllbrprice = $subttlpartscost  = $subttlpartsprice = $subttllbrhrs = $subttlCost = $subttlprice  = 0;

        $stmt = "SELECT cj.jobname,cl.labor,cl.flatprice FROM cannedjobs cj 
                LEFT JOIN cannedlabor cl 
                ON cj.shopid =  cl.shopid and cj.id = cl.cannedjobsid 
                WHERE cj.shopid = ? AND cj.id = ?";

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("si", $shopid, $cj_id);
            $query->execute();
            $query->bind_result($cj_name, $cj_labor, $cj_flatprice);
            $query->fetch();
            $query->close();
        } else {
            die("Canned Labor Prepare failed: (" . $conn->errno . ") " . $conn->error);
        }

        $cannedJobRes = array();
        $parts_stmt = "SELECT roid, complaintid, SUM(LineTTLCost) parts_cost, SUM(LineTTLPrice) parts_price FROM parts where cannedjobsid = ? AND shopid = ? AND deleted = 'no' AND roid IN($rolist) group by roid,complaintid, cannedjobsid order by ROID desc";
        //     echo sprintf(str_replace("?", "%s", $parts_stmt), $cj_id, "'$shopid'")."<br />";
        if ($pquery = $conn->prepare($parts_stmt)) {
            $pquery->bind_param("is", $cj_id, $shopid);
            $pquery->execute();
            $pquery->bind_result($roid, $complaint_id, $parts_cost, $parts_price);
            while ($pquery->fetch()) {
                $cannedJobRes[$roid][$complaint_id] = array(
                    'roid' => $roid,
                    'parts_cost' => $parts_cost,
                    'parts_price' => $parts_price,
                    'labor_hours' => 0,
                    'labor_price' => 0,
                    'labor_cost' => 0,
                    'total_price' => 0,
                    'total_cost' => 0
                );
            }
            $pquery->close();
        } else {
            die("184 Prepare Failed : " . $conn->error);
        }
        $totallaborhours = 0;
        $labor_price = 0;
        $labor_cost = 0;
        $stmt = "select tech,roid,complaintid, laborhours,LineTotal from labor WHERE shopid = ? AND deleted = 'no' AND cannedjobsid = ? AND roid IN ($rolist) order by roid desc";
//echo $stmt."<BR>";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("si", $shopid, $cj_id);
            $query->execute();
            $r = $query->get_result();
            while ($rs = $r->fetch_assoc()) {
                // get the hourly rate for the tech
                $roid = $rs['roid'];
                $labor_hours = $rs['laborhours'];
                $labor_price = $rs['LineTotal'];
                $tech = $rs['tech'];
                $complaint_id = $rs['complaintid'];
                $labor_cost = 0;
                //echo $tech."<BR>";
                if (strpos($tech, ",") > 0) {
                    $tar = explode(",", $tech);
                    $techlast = trim($tar[0], " ");
                    $techfirst = trim($tar[1], " ");
                    $techrate = 0;

                    // get the hourly rate for this tech
                    $estmt = "select hourlyrate from employees where shopid = '$shopid' and employeelast = '$techlast' and employeefirst = '$techfirst'";
                    //echo $estmt."<BR>";
                    if ($equery = $conn->prepare($estmt)) {
                        $equery->execute();
                        $equery->bind_result($techrate);
                        $equery->fetch();
                        $equery->close();
                    } else {
                        die("214 Prepare Failed : " . $conn->error);
                    }

                    if ($techrate > 0) {
                        $currlaborcost = $techrate * $rs['laborhours'];
                        $labor_cost += $currlaborcost;
                    }
                }
                if(isset($cannedJobRes[$roid][$complaint_id]['labor_hours'])) {
                    $cannedJobRes[$roid][$complaint_id]['labor_hours'] = $cannedJobRes[$roid][$complaint_id]['labor_hours'] + $labor_hours;
                } else {
                    $cannedJobRes[$roid][$complaint_id]['labor_hours'] = $labor_hours;
                }
                $cannedJobRes[$roid][$complaint_id]['labor_price'] = $labor_price;
                $cannedJobRes[$roid][$complaint_id]['labor_cost'] = $labor_cost;

            }
        } else {
            die("224 Prepare Failed : " . $conn->error);
        }

        $alldata[] = array("TABLETITLE", $cj_name, "(".sizeof($cannedJobRes).")","","","","","","","","");
        $alldata[] = array("TABLEHEAD", "RO","Vehicle Issue","Job Labor","Labor Hours","Labor Sales","Labor Cost","Parts Sales","Parts Cost","Total Sales","Total Cost");
        ?>
        <tr>
            <td colspan="16">&nbsp;</td>
        </tr>
        <tr>
            <td style="background-color: #658ec1; color: white; font-weight: bold" colspan="3"><?= $cj_name ?> (<?= sizeof($cannedJobRes) ?>)</td>
            <td colspan="7"></td>
        </tr>
        <tr class="table_header">
            <td class="">RO</td>
            <td class="">Vehicle Issue</td>
            <td class="">Job Labor</td>
            <td class="text-right">Labor Hours</td>
            <td class="text-right">Labor Sales</td>
            <td class="text-right">Labor Cost</td>
            <td class="text-right">Parts Sales</td>
            <td class="text-right">Parts Cost</td>
            <td class="text-right">Total Sales</td>
            <td class="text-right">Total Cost</td>
        </tr>
    <?php
        foreach ($cannedJobRes as $roid => $comp_arr) {
            foreach ($comp_arr as $comp_id => $arr) {

                $parts_cost = $arr['parts_cost'];
                $labor_cost = $arr['labor_cost'];
                $parts_price = $arr['parts_price'];
                $labor_price = $arr['labor_price'];
                $totallaborhours = $arr['labor_hours'];

                $complaint = $complaints[$comp_id];

                $total_cost = $parts_cost + $labor_cost;
                $total_price = $parts_price + $labor_price;
                ?>
                <tr>
                    <td><?php echo $roid; ?></td>
                    <td><?= strtoupper(substr($complaint, 0, 40)); ?></td>
                    <td><?php echo strtoupper(substr($cj_labor, 0, 25)); ?></td>
                    <td class="text-right"><?= number_format($totallaborhours, 2) ?></td>
                    <td class="text-right"><?php echo asDollars($labor_price); ?></td>
                    <td class="text-right"><?php echo asDollars($labor_cost); ?></td>
                    <td class="text-right"><?php echo asDollars($parts_price); ?></td>
                    <td class="text-right"><?php echo asDollars($parts_cost); ?></td>
                    <td class="text-right"><?php echo asDollars($total_price); ?></td>
                    <td class="text-right"><?php echo asDollars($total_cost); ?></td>
                </tr>
                <?php

                $alldata[] = array($roid, strtoupper(substr($complaint, 0, 40)), strtoupper(substr($cj_labor, 0, 25)), number_format($totallaborhours, 2), asDollars($labor_price), asDollars($labor_cost), asDollars($parts_price), asDollars($parts_cost), asDollars($total_price), asDollars($total_cost));

                $subttlcount += $num_canned;
                $subttllbrcost += $labor_cost;
                $subttllbrprice += $labor_price;
                $subttlpartscost += $parts_cost;
                $subttlpartsprice += $parts_price;
                $subttllbrhrs += $totallaborhours;
                $subttlCost += $total_cost;
                $subttlprice += $total_price;


                $ttlcount += $num_canned;
                $ttllbrcost += $labor_cost;
                $ttllbrprice += $labor_price;
                $ttlpartscost += $parts_cost;
                $ttlpartsprice += $parts_price;
                $ttllbrhrs += $totallaborhours;
                $ttlCost += $total_cost;
                $ttlprice += $total_price;
            }
        }
        ?>
        <tr class="table_total">
            <td>Subtotals</td>
            <td></td>
            <td></td>
            <td class="text-right"><?= number_format($subttllbrhrs, 2) ?></td>
            <td class="text-right"><?php echo asDollars($subttllbrprice); ?></td>
            <td class="text-right"><?php echo asDollars($subttllbrcost); ?></td>
            <td class="text-right"><?php echo asDollars($subttlpartsprice); ?></td>
            <td class="text-right"><?php echo asDollars($subttlpartscost); ?></td>
            <td class="text-right"><?php echo asDollars($subttlprice); ?></td>
            <td class="text-right"><?php echo asDollars($subttlCost); ?></td>
        </tr>
        <?php
        $alldata[] = array("TOTALS", "", "", number_format($subttllbrhrs, 2), asDollars($subttllbrprice), asDollars($subttllbrcost), asDollars($subttlpartsprice), asDollars($subttlpartscost), asDollars($subttlprice), asDollars($subttlCost));
        $alldata[] = array("","","","","","","","","","");
    } //end of canned job for each loop

    ?>
    <?php if ( $cannedJobRes ): ?>
        <tr class="table_total">
            <td>Totals</td>
            <td></td>
            <td></td>
            <td class="text-right"><?= number_format($ttllbrhrs, 2) ?></td>
            <td class="text-right"><?php echo asDollars($ttllbrprice); ?></td>
            <td class="text-right"><?php echo asDollars($ttllbrcost); ?></td>
            <td class="text-right"><?php echo asDollars($ttlpartsprice); ?></td>
            <td class="text-right"><?php echo asDollars($ttlpartscost); ?></td>
            <td class="text-right"><?php echo asDollars($ttlprice); ?></td>
            <td class="text-right"><?php echo asDollars($ttlCost); ?></td>
        </tr>
        <?php
        $alldata[] = array("TOTALS", "", "", number_format($ttllbrhrs, 2), asDollars($ttllbrprice), asDollars($ttllbrcost),asDollars($ttlpartsprice), asDollars($ttlpartscost), asDollars($ttlprice), asDollars($ttlCost));
        ?>
    <?php endif; ?>
</table>

<?php
include_once  COMPONENTS_PRIVATE_PATH. "/reports/includes/report_form.php";
include_once  COMPONENTS_PRIVATE_PATH. "/reports/includes/footer_reports.php";
?>


</body>

</html>
