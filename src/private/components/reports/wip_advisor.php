<?php
$title = 'Work In Process + Advisor ';  // Report Title Goes Here
// Use this for Gen Pop Reports
require "includes/header_reports.php";
require CONN;
//require("../php/functions.php");

// Use this for Custom Reports
//require("../../../php/includes/reports/header_reports.php");
//require("../../../php/conn.php");
//require("../../functions.php");

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = '';
$ed = '';
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

// Page Variables
if($conamereports == 'yes'){
    $title .= " - $companyname";
}

$subtitle = 'This Report Is Sorted By Advisor';  // Report SubTitle Goes Here - Hide if not needed
$todaydate = "todaydate";
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport_multiple.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>

<title><?= $title;?></title>  <!-- Meta title for page. Change in variable above-->
<!DOCTYPE html>
<html>
<style>
.blue-header {
	background-color: #5c90d2;
	color:white;
	font-weight:bold
}
.ltblue-header {
	background-color:#70b9eb;
	color:white;
	font-weight:bold;
}

</style>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>

<?php

// Use this for Gen Pop Reports
require "includes/report_buttons.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/report_buttons.php");
?>

<!-- Column Headers Insert Report Variables here -->


 		<table class="report_table">
			<?php
         	    $alldata=array();//this will hold all the data arrays to be exported to excel

			    $gttl=0;
				$stmt = "select distinct writer from repairorders where shopid = ? and Status <> 'Closed' and ROType <> 'No Approval'";
				if($query = $conn->prepare($stmt)){
					$query->bind_param("s",$shopid);
					$query->execute();
					$row = $query->get_result();
				}else{
					echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
				}
					while($ro = $row->fetch_array())
					{
						$alldata[]=array('TABLETITLE',$ro['writer']);
		                $alldata[]=array('TABLEHEAD','Date In','Status','RO #','Customer','Vehicle','Sold Hours','Labor Clock','%Complete','Total RO');
						?>
		             <tr class="table_header table_head">
						<td class="ltblue-header" colspan="2"><?= $ro["writer"]?></td>
					 </tr>

		<tr class="table_header table_head">
			<td class="blue-header">Date In</td>
			<td class="blue-header">Status</td>
			<td class="blue-header" style="padding:10px;">RO #</td>
			<td class="blue-header">Customer</td>
			<td class="blue-header">Vehicle</td>
			<td class="blue-header" style="text-align:center;">Sold Hours</td>
			<td class="blue-header" style="text-align:center;">Labor Clock</td>
			<td class="blue-header" style="text-align:center;">%Complete</td>
			<td class="blue-header" style="text-align:right;">Total RO</td>
		</tr>


	    <?php

// Insert DB Query Here

// Template Query Begins - Replace entire section
		$runttl=0;
		$query="select Writer, MajorComplaint, ROID, ucase(Status) as stat, DateIn, Customer, VehInfo, TotalRO, totalfees, VehLicNum, CustomerID, ROType from repairorders where shopid ='$shopid' and Writer = '".$ro['writer']."' and Status <> 'Closed' and ROType <> 'No Approval' ORDER BY Status ASC, ROID DESC";
		$roresult = mysqli_query($conn, $query);
	    if (!$roresult) {
	        die("No Data To Display!");
	    }
	    if ($roresult->num_rows > 0) {
	        while ($rs = $roresult->fetch_array()) {

	    $stmt = "select sum(laborhours) as lh from labor where deleted = 'no' and shopid = '$shopid' and roid = '".$rs['ROID']."'";
	    $query = mysqli_query($conn, $stmt);
	    $trs = $query->fetch_array();
	    if(!empty($trs['lh'])){
		    $soldhours = round($trs["lh"],2);
	    }else{
		    $soldhours = 0;
		}
	    $stmt = "select sum(round((timestampdiff(second,startdatetime,enddatetime)/60)/60,2)) as thours, labortimeclock.* from labortimeclock where shopid = '$shopid' and roid = '".$rs['ROID']."'";
	    $query = mysqli_query($conn, $stmt);
	    $trs = $query->fetch_array();
	    if(!empty($trs['thours'])){
		    $laborclock = $trs["thours"];
	    }else{
		    $laborclock = 0;
		}
	    if($laborclock > 0 && $soldhours > 0){
		    $percentcomplete = round(($laborclock / $soldhours)*100,2);
	    }else{
		    $percentcomplete = 0;
		}
	    if(is_numeric(substr($rs['stat'],0,1))){
		    $thestat = substr($rs['stat'],1);
	    }else{
		    $thestat = $rs["stat"];
		}
	    $runttl += $rs["TotalRO"];
	    $gttl += $rs["TotalRO"];
	    $comp = "";
		$cstmt = "select * from complaints where cstatus = 'no' and shopid = ? and acceptdecline != 'declined' and roid = '".$rs['ROID']."'";
		if ($cquery = $conn->prepare($cstmt)){
			$cquery->bind_param("s",$shopid);
			$cquery->execute();
			$cr = $cquery->get_result();
			if($cr->num_rows==0)continue;
			while ($crs = $cr->fetch_assoc()){
				$comp .= $crs['complaint'].", ";
			}
			$comp=rtrim($comp,', ');
		}


	// Template Query Ends

	    ?>

	<!-- Table Results Begin -->
	    <tr class="table_data">
			<td><?= date('m/d/Y',strtotime($rs["DateIn"]))?></td>
			<td><?= $thestat?></td>
			<td style="text-align:center;"><?= $rs["ROID"]?></td>
			<td><?= strtoupper($rs["Customer"])?></td>
			<td><?= strtoupper($rs["VehInfo"])?></td>
			<td style="text-align:center;"><?= $soldhours?></td>
			<td style="text-align:center;"><?= $laborclock?></td>
			<td style="text-align:center;"><?= number_format($percentcomplete,2).'%'?></td>
			<td style="text-align:right;">$<?= number_format($rs['TotalRO'],2)?></td>
	    </tr>
	    <tr style="font-size:11pt;">
	    	<td><b>Issues:</b></td>
			<td colspan="8"><i><?= strtoupper($comp);?></i></td>
	    </tr>
	    <?php
			$alldata[]=array(date('m/d/Y',strtotime($rs["DateIn"])),$thestat,$rs['ROID'],strtoupper($rs["Customer"]),strtoupper($rs["VehInfo"]),$soldhours,$laborclock,number_format($percentcomplete,2).'%','$'.number_format($rs['TotalRO'],2));
	    	$alldata[]=array('Vehicle Issues',strtoupper($comp));
	        } // end of while for loop
	    } // end if for end of file
	        $alldata[]=array('');
			$alldata[]=array('TOTAL IN PROCESS FOR ' . $ro["writer"],'','','','','','','','$'.number_format($runttl,2));
	    ?>

    <!-- Table totals go here, if needed-->

		<tr class="table_total">
			<td><b>TOTAL IN PROCESS FOR <?= $ro["writer"];?></b></td>
			<td></td>
			<td></td>
			<td></td>
			<td></td>
			<td></td>
			<td></td>
			<td></td>
			<td style="text-align:right;"><b>$<?= number_format($runttl,2)?></b></td>
		</tr>

<?php
}
?>

<tr class="table_total">
	<td><b>GRAND TOTAL</b></td>
	<td></td>
	<td></td>
	<td></td>
	<td></td>
	<td></td>
	<td></td>
	<td></td>
	<td style="text-align:right;"><b>$<?= number_format($gttl,2)?></b></td>
</tr>
</table>
<?php
$alldata[]=array('');
$alldata[]=array('GRAND TOTAL','','','','','','','','$'.number_format($gttl,2));
// Use this for Gen Pop Reports
require "includes/footer_reports.php";
require "includes/report_form_todaydate.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/footer_reports.php");
//include("../../../php/includes/reports/report_form.php");
?>

</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
