<?php
$data = unserialize(base64_decode($_POST['data'])); //Table content as a Post from the form
$fields = unserialize(base64_decode($_POST['fields'])); //Table heading as a post from the form
require_once INTEGRATIONS_PATH.'/php_excel/PHPExcel.php';

// Instantiate a new PHPExcel object
$objPHPExcel = new PHPExcel();
$objPHPExcel->setActiveSheetIndex(0);

//set style array for the title of the page
$HeadingstyleArray = array(
        'font'  => array(
        'bold'  => true,
        'size'  => 18,
        'name'  => 'Verdana'
    ));

$lastchar=''; //this will hold the last character or column name of excel produced, which means it will tell how many columns will be there in the excel sheet.

$showsubtitle=false; //Boolean to decide if we need to show subtitle or not

$headerstart=4; //index from which table header should start 

$rowCount=5; // row count for table data start from row 4

if (!empty($_POST['subtitle']) && stripos($_POST['subtitle'], "my subtitle") === false)
{
$showsubtitle=true;
$headerstart=5;
$rowCount=6;
}

for ($i=0;$i<count($fields);$i++) //loop through the fields array to set the table headings got from the post value of the form
{
    $y = chr(66+$i); //converts numeric value to characters like A,B etc , which denotes the cell name in excel
    $lastchar=$y;
    $objPHPExcel->getActiveSheet()->SetCellValue($y.$headerstart,$fields[$i]); //sets heading value in respective cell of third row
}

// $objPHPExcel->getActiveSheet()->SetCellValue('A1',$_POST['heading']); //set heading of the page from the post value "Heading" from the form
// $objPHPExcel->getActiveSheet()->getStyle('A1')->applyFromArray($HeadingstyleArray); //Assign above style array to this cell
// $objPHPExcel->getActiveSheet()->mergeCells('A1:'.$lastchar.'1');
// $objPHPExcel->getActiveSheet()->getStyle('A1:'.$lastchar.'1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER); //Assign above style array to this cell



$objPHPExcel->getActiveSheet()->SetCellValue('A1',$_POST['heading']); //set heading of the page from the post value "Heading" from the form
$objPHPExcel->getActiveSheet()->getStyle('A1')->applyFromArray($HeadingstyleArray); //Assign above style array to this cell
$objPHPExcel->getActiveSheet()->mergeCells('A1:'.$lastchar.'1');
$objPHPExcel->getActiveSheet()->getStyle('A1:'.$lastchar.'1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER); //Assign above style array to this cell

$objPHPExcel->getActiveSheet()->SetCellValue('A2',$_POST['sub_heading']); //set sub_heading of the page from the post value "Sub Heading" from the form
$objPHPExcel->getActiveSheet()->getStyle('A2')->applyFromArray($HeadingstyleArray); //Assign above style array to this cell
$objPHPExcel->getActiveSheet()->mergeCells('A2:'.$lastchar.'2');
$objPHPExcel->getActiveSheet()->getStyle('A2:'.$lastchar.'2')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER); //Assign above style array to this cell

if($showsubtitle)
{
 $objPHPExcel->getActiveSheet()->SetCellValue('A3',$_POST['subtitle']); //set sub title of the page from the post value "Sub Title" from the form
 $objPHPExcel->getActiveSheet()->getStyle('A3')->applyFromArray($HeadingstyleArray); //Assign above style array to this cell
 $objPHPExcel->getActiveSheet()->mergeCells('A3:'.$lastchar.'3');
 $objPHPExcel->getActiveSheet()->getStyle('A3:'.$lastchar.'3')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER); //Assign above style array to this cell
}

//set style array for the table headings of the page
$FieldTitlestyleArray = array(
        'font'  => array(
        'bold'  => true,
        'underline' => true,
        'size'  => 12,
        'name'  => 'Verdana'
    ));

$objPHPExcel->getActiveSheet()->getStyle("B".$headerstart.":".$lastchar.$headerstart)->applyFromArray($FieldTitlestyleArray); //set above table heading style from B3 to third row and 'last char' column
$objPHPExcel->getActiveSheet()->getStyle("A".$headerstart.":".$lastchar.$headerstart)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('C0C0C0'); //set background color from B3 to third row and 'last char' column


//set style array for the table content of the page
$DatastyleArray = array(
        'font'  => array(
        'size'  => 12,
        'name'  => 'Verdana'
    ));

if(!empty($data))
{
 $count=1; //internal count for the serial#
 foreach($data as $row)//loop through each data array
 {
   if(!empty($row[0]) && strpos(strtoupper($row[0]), 'TOTALS') === false)
   $objPHPExcel->getActiveSheet()->SetCellValue('A'.$rowCount,$count++.')'); //set serial # form the $count variable and increment by 1

   for ($i=0;$i<count($fields);$i++) //loop through the fields array to fill them with the data value
  {
    $y = chr(66+$i);
    $objPHPExcel->getActiveSheet()->setCellValueExplicit($y.$rowCount,$row[$i],PHPExcel_Cell_DataType::TYPE_STRING); // set cell with the data value
  }
  $objPHPExcel->getDefaultStyle()->getAlignment()->setWrapText(true);
  $objPHPExcel->getActiveSheet()->getStyle("B".$rowCount.":".$lastchar.$rowCount)->applyFromArray($DatastyleArray); //set data style from above style array

  if(!empty($row[0]) && strpos(strtoupper($row[0]), 'TOTALS') !== false)
  $objPHPExcel->getActiveSheet()->getStyle("B".$rowCount.":".$lastchar.$rowCount)->getFont()->setBold( true );

  $rowCount++; //increment the row count and shift pointer to next row in excel

 }

}

//set columns auto resizing from column B to last column
foreach(range('B',$lastchar) as $columnID)
{
      // $objPHPExcel->getDefaultStyle()->getAlignment()->setWrapText(true);
      // $objPHPExcel->getActiveSheet()->getColumnDimension($columnID)
      //   ->setAutoSize(true);

        $objPHPExcel->getDefaultStyle()->getAlignment()->setWrapText(false);
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(0);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(30);
        $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(30);
        $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension($columnID)->setWidth(20);



}

//hide grid lines in excel
$objPHPExcel->getActiveSheet()->setShowGridlines(false);


// Instantiate a Writer to create an OfficeOpenXML Excel .xlsx file
$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);

// We'll be outputting an excel file
header('Content-type: application/vnd.ms-excel');

// It will be called filename.xlsx
header('Content-Disposition: attachment; filename="'.$_POST['filename'].'"');

// Download file
$objWriter->save('php://output');
