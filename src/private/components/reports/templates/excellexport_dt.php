<?php
ini_set('memory_limit', '500M');
$data = unserialize(base64_decode($_POST['data'])); // Table content as a Post from the form
$fields = unserialize(base64_decode($_POST['fields'])); // Table heading as a post from the form
require_once INTEGRATIONS_PATH.'/php_excel/PHPExcel.php';

// Instantiate a new PHPExcel object
$objPHPExcel = new PHPExcel();
$objPHPExcel->setActiveSheetIndex(0);

// set style array for the title of the page
$HeadingstyleArray = array(
    'font' => array(
        'bold' => true,
        'size' => 18,
        'name' => 'Verdana'
    )
);

$lastchar = ''; // this will hold the last character or column name of excel produced, which means it will tell how many columns will be there in the excel sheet.

$showsubtitle = false; // Boolean to decide if we need to show subtitle or not

$headerstart = 4; // index from which the table header should start

$rowCount = 5; // row count for table data starts from row 4

if (!empty($_POST['subtitle']) && stripos($_POST['subtitle'], "my subtitle") === false) {
    $showsubtitle = true;
    $headerstart = 5;
    $rowCount = 6;
}

for ($i = 0; $i < count($fields); $i++) // loop through the fields array to set the table headings got from the post value of the form
{
    $y = chr(65 + $i); // converts numeric value to characters like A, B, etc., which denotes the cell name in Excel
    $lastchar = $y;
    $objPHPExcel->getActiveSheet()->SetCellValue($y . $headerstart, $fields[$i]); // sets heading value in the respective cell of the third row
}

$objPHPExcel->getActiveSheet()->SetCellValue('A1', $_POST['heading']); // set heading of the page from the post value "Heading" from the form
$objPHPExcel->getActiveSheet()->getStyle('A1')->applyFromArray($HeadingstyleArray); // Assign the style array to this cell
$objPHPExcel->getActiveSheet()->mergeCells('A1:' . $lastchar . '1');
$objPHPExcel->getActiveSheet()->getStyle('A1:' . $lastchar . '1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER); // Assign the style array to this cell

$objPHPExcel->getActiveSheet()->SetCellValue('A2', $_POST['sub_heading']); // set sub-heading of the page from the post value "Sub Heading" from the form
$objPHPExcel->getActiveSheet()->getStyle('A2')->applyFromArray($HeadingstyleArray); // Assign the style array to this cell
$objPHPExcel->getActiveSheet()->mergeCells('A2:' . $lastchar . '2');
$objPHPExcel->getActiveSheet()->getStyle('A2:' . $lastchar . '2')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER); // Assign the style array to this cell

if ($showsubtitle) {
    $objPHPExcel->getActiveSheet()->SetCellValue('A3', $_POST['subtitle']); // set sub-title of the page from the post value "Sub Title" from the form
    $objPHPExcel->getActiveSheet()->getStyle('A3')->applyFromArray($HeadingstyleArray); // Assign the style array to this cell
    $objPHPExcel->getActiveSheet()->mergeCells('A3:' . $lastchar . '3');
    $objPHPExcel->getActiveSheet()->getStyle('A3:' . $lastchar . '3')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER); // Assign the style array to this cell
}

// set style array for the table headings of the page
$FieldTitlestyleArray = array(
    'font' => array(
        'bold' => true,
        'underline' => true,
        'size' => 12,
        'name' => 'Verdana'
    ));

$objPHPExcel->getActiveSheet()->getStyle("A" . $headerstart . ":" . $lastchar . $headerstart)->applyFromArray($FieldTitlestyleArray); // set the above table heading style from B3 to the third row and 'last char' column
$objPHPExcel->getActiveSheet()->getStyle("A" . $headerstart . ":" . $lastchar . $headerstart)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('C0C0C0'); // set the background color from B3 to the third row and 'last char' column

// set style array for the table content of the page
$DatastyleArray = array(
    'font' => array(
        'size' => 12,
        'name' => 'Verdana'
    ));

if (!empty($data)) {
    $count = 1; // internal count for the serial#
    foreach ($data as $row) // loop through each data array
    {
        if (!empty($row[0]) && strtoupper($row[0]) == 'TABLEHEADS') {
            for ($i = 0; $i < count($fields); $i++) // loop through the fields array to fill them with the data value
            {
                $y = chr(65 + $i);
                $objPHPExcel->getActiveSheet()->setCellValueExplicit($y . $rowCount, $fields[$i], PHPExcel_Cell_DataType::TYPE_STRING); // set cell with the data value as TYPE_STRING
            }
            $objPHPExcel->getActiveSheet()->getStyle("A" . $rowCount . ":" . $lastchar . $rowCount)->applyFromArray($FieldTitlestyleArray); // set the above table heading style from B3 to the third row and 'last char' column
            $objPHPExcel->getActiveSheet()->getStyle("A" . $rowCount . ":" . $lastchar . $rowCount)->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB('C0C0C0'); // set the background color from B3 to the third row and 'last char' column
        } else {
            /*
            if (!empty($row[0]) && strpos(strtoupper($row[0]), 'TOTALS') === false) {
                $objPHPExcel->getActiveSheet()->SetCellValue('A' . $rowCount, $count++ . ')'); // set serial # from the $count variable and increment by 1
            }
            */

            for ($i = 0; $i < count($fields); $i++) {
                $y = chr(65 + $i);
            
                if (is_int($row[$i])){
                    $objPHPExcel->getActiveSheet()->setCellValueExplicit($y . $rowCount, $row[$i], PHPExcel_Cell_DataType::TYPE_NUMERIC);
                    $objPHPExcel->getActiveSheet()->getStyle($y . $rowCount)->getNumberFormat()->setFormatCode("#");
                } else if(is_float($row[$i])) {
                    $objPHPExcel->getActiveSheet()->setCellValueExplicit($y . $rowCount, $row[$i], PHPExcel_Cell_DataType::TYPE_NUMERIC);
                    $objPHPExcel->getActiveSheet()->getStyle($y . $rowCount)->getNumberFormat()->setFormatCode("#,##0.00");
                } else {
                    if (strpos($row[$i], '$') === 0) {
                        $curr_val = str_replace("$", "", $row[$i]);
                        $curr_val = str_replace(",", "", $curr_val);
                        $objPHPExcel->getActiveSheet()->setCellValueExplicit($y . $rowCount, $curr_val, PHPExcel_Cell_DataType::TYPE_NUMERIC);
                        $objPHPExcel->getActiveSheet()->getStyle($y . $rowCount)->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_CURRENCY_USD_SIMPLE);
                    } else {
                        $objPHPExcel->getActiveSheet()->setCellValueExplicit($y . $rowCount, $row[$i], PHPExcel_Cell_DataType::TYPE_STRING);
                    }
                }

                $objPHPExcel->getDefaultStyle()->getAlignment()->setWrapText(true);
                $objPHPExcel->getActiveSheet()->getStyle("A" . $rowCount . ":" . $lastchar . $rowCount)->applyFromArray($DatastyleArray);
            }


            if (!empty($row[0]) && strpos(strtoupper($row[0]), 'TOTALS') !== false) {
                $objPHPExcel->getActiveSheet()->getStyle("A" . $rowCount . ":" . $lastchar . $rowCount)->getFont()->setBold(true);
            }
        }

        $rowCount++; // increment the row count and shift the pointer to the next row in Excel
    }
}

// set columns auto resizing from column B to the last column
foreach (range('B', $lastchar) as $columnID) {
    $objPHPExcel->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
}

// hide grid lines in Excel
$objPHPExcel->getActiveSheet()->setShowGridlines(false);

// Instantiate a Writer to create an OfficeOpenXML Excel .xlsx file
$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);

// We'll be outputting an Excel file
header('Content-type: application/vnd.ms-excel');

// It will be called filename.xlsx
header('Content-Disposition: attachment; filename="' . $_POST['filename'] . '"');

// Download the file
$objWriter->save('php://output');
?>
