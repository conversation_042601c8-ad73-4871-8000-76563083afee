<!DOCTYPE html>
<html>
<?php

// Use this for Gen Pop Reports
 $title = 'Work In Progress Detail';  // Report Title Goes Here
 require "includes/header_reports.php";
 require CONN;
 //require("../php/functions.php");

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = "";
$ed = "";
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

// Page Variables
if($conamereports == 'yes'){
    $title .= " - $companyname";
}
$subtitle = 'Issue Subtotal Does Not Include Fees';  // Report SubTitle Goes Here - Hide if not needed
$todaydate = "todaydate";
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder
?>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>

<?php

// Use this for Gen Pop Reports
require "includes/report_buttons.php";
?>

<!-- Column Headers Insert Report Variables here -->

	<table class=report_table>
  <thead>
	<tr class="table_header table_head">
		<td>Date In</td>
		<td>Status</td>
		<td>RO #</td>
		<td>Customer</td>
        <td>Phone</td>
        <td>Email</td>
		<td>Vehicle</td>
		<td style="text-align:right;">Sold Hours</td>
		<td style="text-align:right;">Labor Clock</td>
		<td style="text-align:right;">%Complete</td>
		<td style="text-align:right;">Total RO</td>
        <td style="text-align:right;">Balance Owed</td>

	</tr>
</thead>

    <?php
    $tablefields=array('Date In','Status','RO #','Customer','Phone','Email','Vehicle','Sold Hours','Labor Clock','%Complete','Total RO','Balance Owed');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel


// Insert DB Query Here

// Template Query Begins - Replace entire section
	$runttl=$runbal=0;
	$query="select Writer, MajorComplaint, ROID, ucase(Status) as stat, DateIn, Customer, CustomerPhone, CustomerWork, CellPhone, email, VehInfo, TotalRO, totalfees, VehLicNum, CustomerID, ROType, balance from repairorders where shopid ='$shopid' and Status <> 'Closed' and ROType <> 'No Approval' ORDER BY Status ASC, ROID DESC";
	$roresult = mysqli_query($conn, $query);
    if (!$roresult) {
        die("No Data To Display!");
    }
    if ($roresult->num_rows > 0) {
        while ($rs = $roresult->fetch_array()) {
            $roid = $rs["ROID"];

            $stmt = "select sum(laborhours) as lh from labor where deleted = 'no' and shopid = '$shopid' and roid = '".$rs['ROID']."'";
            $query = mysqli_query($conn, $stmt);
            $trs = $query->fetch_array();
            if(!empty($trs['lh'])){
                $soldhours = round($trs["lh"],2);
            }else{
                $soldhours = 0;
            }
            $stmt = "select sum(round((timestampdiff(second,startdatetime,enddatetime)/60)/60,2)) as thours, labortimeclock.* from labortimeclock where shopid = '$shopid' and roid = '".$rs['ROID']."'";
            $query = mysqli_query($conn, $stmt);
            $trs = $query->fetch_array();
            if(!empty($trs['thours'])){
                $laborclock = $trs["thours"];
            }else{
                $laborclock = 0;
            }
            if($laborclock > 0 && $soldhours > 0){
                $percentcomplete = round(($laborclock / $soldhours)*100,2);
            }else{
                $percentcomplete = 0;
            }
            if(is_numeric(substr($rs['stat'],0,1))){
                $thestat = substr($rs['stat'],1);
            }else{
                $thestat = $rs["stat"];
            }
            $runttl += $rs["TotalRO"];
            $runbal += $rs["balance"];
            // $stmt = "select complaintid, roid, complaint FROM complaints WHERE roid = '".$rs['ROID']."' AND shopid = '$shopid' AND acceptdecline != 'Declined'";
            // $query = mysqli_query($conn, $stmt);
            // $custcomplain = $query->fetch_array();
            // if (!$custcomplain) {
            // 	die("No Customer Complaint");
            // }

        // Template Query Ends

    ?>

<!-- Table Results Begin -->
    <tr class="table_data">
		<td><?= date('n/j/Y',strtotime($rs["DateIn"]))?></td>
		<td><?= $thestat?></td>
		<td><?= $roid?></td>
		<td><?= strtoupper($rs["Customer"])?></td>
        <td><?php
            if (strlen($rs["CustomerPhone"]) > 5) {
                echo " <b>H:</b>".formatPhone($rs["CustomerPhone"]);
            }if (strlen($rs["CustomerWork"]) > 5) {
                echo " <b>W:</b>".formatPhone($rs["CustomerWork"]);
            }if (strlen($rs["CellPhone"]) >5) {
                echo " <b>C:</b>".formatPhone($rs["CellPhone"]);
            }?>
        </td>
        <td><?= strtoupper($rs["email"])?></td>
		<td><?= strtoupper($rs["VehInfo"])?></td>
		<td style="text-align:right;"><?= $soldhours?></td>
		<td style="text-align:right;"><?= $laborclock?></td>
		<td style="text-align:right;"><?= number_format($percentcomplete,2).'%'?></td>
		<td style="text-align:right;">$<?= number_format($rs['TotalRO'],2)?></td>
        <td style="text-align:right;">$<?= number_format($rs['balance'],2)?></td>
    </tr>
    <?php

            $alldata[]=array(date('n/j/Y',strtotime($rs["DateIn"])),$thestat,$rs['ROID'],$rs["Customer"],"H:".$rs["CustomerPhone"]." W:".$rs["CustomerWork"]." C:".$rs["CellPhone"], $rs['email'], $rs["VehInfo"],$soldhours,$laborclock,number_format($percentcomplete,2).'%','$'.number_format($rs['TotalRO'],2),'$'.number_format($rs['balance'],2));

    $compid = $compdesc = "";
    $subtotal = $ttlpartsprice = $ttllaborprice = $ttlsubletprice = 0;
    $cstmt = "select * from complaints where cstatus = 'no' and shopid = ? and acceptdecline != 'declined' and roid = ?";
    if ($cquery = $conn->prepare($cstmt)){
        $cquery->bind_param("ss",$shopid,$roid);
        $cquery->execute();
        $cr = $cquery->get_result();
        if($cr->num_rows==0)continue;
        while ($crs = $cr->fetch_assoc()){
            $compid = $crs["complaintid"];
            $compdesc = $crs["complaint"];
            $pstmt = "SELECT SUM(linettlprice) FROM parts WHERE shopid = ? AND complaintid = ?";
            if ($query = $conn->prepare($pstmt)){
                $query->bind_param("ss",$shopid,$compid);
                $query->execute();
                $query->bind_result($ttlpartsprice);
                $query->fetch();
                $query->close();
            }
            $lstmt = "SELECT SUM(linetotal) FROM labor WHERE shopid = ? AND complaintid = ?";
            if ($query = $conn->prepare($lstmt)){
                $query->bind_param("ss",$shopid,$compid);
                $query->execute();
                $query->bind_result($ttllaborprice);
                $query->fetch();
                $query->close();
            }
            $sstmt = "SELECT SUM(subletprice) FROM sublet WHERE shopid = ? AND complaintid = ?";
            if ($query = $conn->prepare($sstmt)){
                $query->bind_param("ss",$shopid,$compid);
                $query->execute();
                $query->bind_result($ttlsubletprice);
                $query->fetch();
                $query->close();
            }

            $subtotal = $ttlpartsprice + $ttllaborprice + $ttlsubletprice;
                ?>
    <tr style="font-size:11pt;">
        <td><b>Issue:</b></td>
        <td colspan="9"><i><?= $compdesc;?></i></td>
        <td>Subtotal</td>
        <td style="text-align: right;"><?= asDollars($subtotal);?></td>
    </tr>

    <?php

        $alldata[] = array('Issue:' . $compdesc, '', '', '', '', '', '', '', '', '', 'Subtotal', asDollars($subtotal));

        }
    //    $comp=rtrim($comp,', ');
    }


          //$alldata[]=array("Customer Concern",$rs["MajorComplaint"],'','','','','','',''); //fill up the alldata array with the arrays of data to be shown in excel export
        } // end of while for loop
    } // end if for end of file


		 $alldata[]=array('','','','','','','','','','');
		 $alldata[]=array('TOTAL IN PROCESS','','','','','','','','','','$'.number_format($runttl,2),'$'.number_format($runbal,2));

    ?>

    <!-- Table totals go here, if needed-->

      <tr class="table_total">
        <td><b>TOTAL IN PROCESS</b></td>
          <td></td>
   	      <td></td>
	      <td></td>
	      <td></td>
	   	  <td></td>
          <td></td>
	      <td></td>
	      <td></td>
	      <td></td>
	      <td style="text-align:right;"><b>$<?= number_format($runttl,2)?></b></td>
          <td style="text-align:right;"><b>$<?= number_format($runbal,2)?></b></td>
     </tr>

    </table>

<?php

// Use this for Gen Pop Reports
require "includes/footer_reports.php";
include("includes/report_form_todaydate.php"); //only for reports with today date req
?>


</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
