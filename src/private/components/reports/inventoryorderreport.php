<?php
$title = "Inventory Order Report";

include_once "includes/report_config.php";

$shopid = $_COOKIE['shopid'];
$supplier = isset($_REQUEST['supplier']) ? filter_var($_REQUEST['supplier'], FILTER_SANITIZE_STRING) : "all";
$supplier = str_replace("'", "''", $supplier);
$todaydate = 'todaydate';

$template = COMPONENTS_PRIVATE."/reports/templates/excelexport_multiple.php";
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}
?>


<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>

<?php
include_once "includes/report_buttons_supp.php";
?>
<!--
<input name="Hidden1" type="hidden" id="emailaddress"><input name="Hidden1" type="hidden" id="shopemailaddress">
<input id="suppliername" name="suppliername" type="hidden">
<input onclick="sendEmail()" id="email" style="visibility:hidden" name="Button1" type="button" value="Email to Supplier">
-->
<?php
$tablefields = array();
$alldata = array();

$catlistArr = array();
$catlist = "";
//get categories
$stmt = "select distinct UCASE(Category) from category where shopid = ? order by Category";
if ($query = $conn->prepare($stmt)){
$query->bind_param("s", $shopid);
$query->execute();
$query->bind_result($Category);
$query->store_result();
if ($query->num_rows > 0){

while ($query->fetch()){
//get the parts
//get sum cost
$lineorderamt = 0;
if ($supplier == "all") {
    $cstmt = "select * from partsinventory where shopid = '$shopid' and netonhand <= reorderlevel
and reorderlevel >= 0 and maxonhand > 0 and (maxonhand - netonhand) <> 0 and PartCategory = '$Category' order by PartDesc, PartNumber";
    $showemail = "no";
} else {
    $cstmt = "select * from partsinventory where partsupplier = '$supplier' and shopid = '$shopid' and netonhand <= reorderlevel and reorderlevel >= 0 and maxonhand > 0 and (maxonhand - netonhand) <> 0 and PartCategory = '$Category' order by PartDesc, PartNumber";
    $showemail = "yes";
    //get the supplier info
    $stmt = "select * from supplier where suppliername = '$supplier' and shopid = '$shopid'";
    if ($xquery = $conn->prepare($stmt)) {
        $xquery->execute();
        $xresults = $xquery->get_result();
        if ($xresults->num_rows > 0) {
            $xrs = $xresults->fetch_assoc();
            $sname = $xrs["SupplierName"] . " - " . $xrs["SupplierPhone"];
        }
        $xquery->close();
    }
}

if ($cquery = $conn->prepare($cstmt)) {
    $cquery->execute();
    $cresults = $cquery->get_result();
    $cquery->close();
}

if ($cresults->num_rows > 0){
echo "<strong>" . (empty($sname) ? "": $sname) . "</strong>";
if(!empty($sname)) {
    $alldata[] = array('TABLETITLE', $sname);
}
$alldata[]=array('','','','','','','','','','','');
$alldata[]=array('TABLEHEAD','Category',$Category,'','','','','','','','','');
$alldata[]=array('TABLEHEAD','Part #','Description','Supplier','Cost','Alloc','Net','Reorder','Max','90_Days','12_Mos','Order');
?>
<table class="report_table" style="page-break-after: avoid">
    <tr>
        <td colspan="2" style="background-color: #658ec1; color: white">Category: <?= $Category ?></td>
        <td colspan="9"></td>
    </tr>
    <tr class="table_header">
        <td>Part #</td>
        <td>Description</td>
        <td style="width: 18%">Supplier</td>
        <td class="text-right">Cost</td>
        <td class="text-right">Alloc</td>
        <td class="text-right">Net</td>
        <td class="text-right">Reorder</td>
        <td class="text-right">Max</td>
        <td class="text-right">90_Days</td>
        <td class="text-right">12_Mos</td>
        <td class="text-right">Order</td>
    </tr>
        <?php
        $cntr = 0;
        while ($crs = $cresults->fetch_assoc()){
        $cntr++;
        //get 90 day sales
        $startdate = date("Y-m-d", strtotime("-90 days"));
        $date = date('Y-m-d');
        $ninetystmt = "select count(*) as pcnt from parts where deleted = 'no' and shopid = ? and `date` <= ? and `date` >= ? and PartNumber = ?";
        if ($nquery = $conn->prepare($ninetystmt)) {
            $nquery->bind_param("ssss", $shopid, $date, $startdate, $crs['PartNumber']);
            $nquery->execute();
            $nquery->bind_result($ninetydays);
            $nquery->fetch();
            $nquery->close();
        }

        if (empty($ninetydays)) {
            $ninetydays = 0;
        }
        //'get 12 mos sales
        $startdate = date("Y-m-d", strtotime("-12 months"));
        $m12stmt = "select count(*) as pcnt from parts where deleted = 'no' and shopid = ? and `date` <= ? and `date` >= ? and PartNumber = ?";
        if ($mquery = $conn->prepare($m12stmt)) {
            $mquery->bind_param("ssss", $shopid, $date, $startdate, $crs['PartNumber']);
            $mquery->execute();
            $mquery->bind_result($twelvemos);
            $mquery->fetch();
            $mquery->close();
        }
        if (empty($twelvemos)) {
            $twelvemos = 0;
        }
        if ($cntr >= 25){
        $cntr = 0;
        ?>
    <tr class="table_header">
        <td>Part #</td>
        <td style="width: 36px">Description</td>
        <td style="width: 18%">Supplier</td>
        <td class="text-right">Cost</td>
        <td class="text-right">Alloc</td>
        <td class="text-right">Net</td>
        <td class="text-right">Reorder</td>
        <td class="text-right">Max</td>
        <td class="text-right">90_Days</td>
        <td class="text-right">12_Mos</td>
        <td class="text-right">Order</td>
    </tr>
    <?php
            $alldata[]=array('TABLEHEAD','Part #','Description','Supplier','Cost','Alloc','Net','Reorder','Max','90_Days','12_Mos','Order');
    }

    $noh = $crs["NetOnHand"];
    $rol = $crs["ReOrderLevel"];
    $moh = $crs["MaxOnHand"];

    $allocated = 0;

    $aStmt = "SELECT COALESCE(SUM(p.quantity), 0) AS q
               FROM repairorders ro
               LEFT JOIN parts p ON ro.roid = p.roid
               WHERE ro.shopid = ? 
               AND ro.status != 'CLOSED' 
               AND ro.ROType != 'No Approval'
               AND p.partnumber = ?
               AND ro.roid IN (SELECT roid FROM repairorders WHERE shopid = ? AND status != 'CLOSED' AND ROType != 'No Approval')";

    if ($aquery = $conn->prepare($aStmt)) {
        $aquery->bind_param("sss", $shopid, $crs['PartNumber'], $shopid);
        $aquery->execute();
        $aquery->bind_result($allocated);
        $aquery->fetch();
        $aquery->close();
    }

    if ($moh > $noh) {
        ?>
        <tr>
            <td width="10%">
                <a class="no-print" style="color:black;" target="_top"
                               href='<?= COMPONENTS_PRIVATE ?>/inventory/editinventory.php?pn=<?= urlencode($crs["partid"]) ?>'><?= strtoupper($crs["PartNumber"]) ?></a>
                <span class="visible-print"><?= strtoupper($crs["PartNumber"]) ?></span>
            </td>
            <td style="width: 36px"><?= strtoupper($crs["PartDesc"]) ?></td>
            <td width="36%" style="width: 18%;"><?= strtoupper($crs["PartSupplier"]) ?></td>
            <td width="6%" class="text-right"><?= asDollars($crs["PartCost"]) ?></td>
            <td width="6%" class="text-right"><?= $allocated ?></td>
            <td width="6%" class="text-right"><?= $crs["NetOnHand"] ?></td>
            <td width="6%" class="text-right"><?= $crs["ReOrderLevel"] ?></td>
            <td width="6%" class="text-right"><?= $crs["MaxOnHand"] ?></td>
            <td width="6%" class="text-right"><?= $ninetydays ?></td>
            <td width="6%" class="text-right"><?= $twelvemos ?></td>
            <td width="6%" class="text-right">
                <?php
                $order = "";
                if ($moh > $noh) {
                    $order = $moh - $noh;
                    $lineorderamt = $lineorderamt + (($moh - $noh) * $crs["PartCost"]);
                }
                echo $order;
                ?>
            </td>
        </tr>
        <?php
        $alldata[]=array(strtoupper($crs["PartNumber"]),strtoupper($crs["PartDesc"]), strtoupper($crs["PartSupplier"]), asDollars($crs["PartCost"]), $allocated, $crs["NetOnHand"], $crs["ReOrderLevel"], $crs["MaxOnHand"], $ninetydays, $twelvemos, $order);
    }
    }
    } //crs enf if

        $alldata[]=array('','','','','','','','','','','');
    $catlist = $catlist . $Category . "~" . $lineorderamt . ",";

    $catlistArr[] = $Category . "~" . $lineorderamt;
    }
    } else {
        echo "No Parts Matrix Category found";
    }
    $query->close();
    }
    ?>
</table>
<p></p>
    <table class="table table-bordered" style="width: 50%; margin-top: 10px; margin-bottom: 15px; page-break-before: avoid; float: right">
        <?php
        $alldata[]=array('TABLEHEAD','','','','','','','','Category','','Amount','');

        //$car = explode(",", $catlist);
        $runcost = 0;
        foreach ($catlistArr as $car) {
            $tar = explode("~", $car);
            ?>
            <tr>
                <td width="50%"><?= $tar[0] ?>
                </td>
                <td align=right width="50%"><?= asDollars($tar[1]) ?></td>
            </tr>
            <?php
            $runcost = $runcost + $tar[1];
            $alldata[]=array('','','','','','','',$tar[0],'',asDollars($tar[1]),'');
        }

        if ($runcost > 0) {
            ?>
            <tr>
                <td width="50%">Total Order Cost:</td>
                <td align=right width="50%"><?= asDollars($runcost) ?>
                </td>
            </tr>
            <?php
            $alldata[]=array('','','','','','','','Total Order','',asDollars($runcost),'');
        }
        ?>
    </table>
<div class="clearfix"></div>
<br><br>
<?php
    include_once "includes/footer_reports.php";
    include_once "includes/report_form_todaydate.php";
?>
<?php
//get supplier email
if ($showemail == "yes") {
    $supplieremail = $suppliername = $companyname = "";
    $stmt = "select supplieremail,suppliername from supplier where shopid = ? and suppliername = ?";
    if ($tquery = $conn->prepare($stmt)) {
        $tquery->bind_param("ss", $shopid, $supplier);
        $tquery->execute();
        $tquery->bind_result($supplieremail, $suppliername);
        $tquery->fetch();
        $tquery->close();
    }
    if (!empty($supplieremail)) {
        $stmt = "select companyemail from company where shopid = ?";
        if ($xquery = $conn->prepare($stmt)) {
            $xquery->bind_param("s", $shopid);
            $xquery->execute();
            $xquery->bind_result($companyemail);
            $xquery->fetch();
            $xquery->close();
        }

        ?>
        <script type="text/javascript">
            document.getElementById("email").style.visibility = "visible"
            document.getElementById("emailaddress").value = '<?= $supplieremail?>'
            document.getElementById("shopemailaddress").value = '<?= $companyemail ?>'
            document.getElementById("suppliername").value = '<?= $suppliername ?>'
        </script>
        <?php
    }
}
?>
<script type="text/javascript">


    x = setTimeout("closeSupplier()", 3000)


    function closeSupplier() {

        //document.getElementById("popup").style.display = "none"
        //document.getElementById("popuphider").style.display = "none"
        //document.getElementById("popupdropshadow").style.display = "none"
        //window.print();
        //location.href='../reportsnew.asp'
    }

    function printit() {
        document.getElementById("print").style.display = "none"
        window.print()
        setTimeout("showemaildiv()", 2000)
    }

    function showemaildiv() {
        document.getElementById("print").style.display = "block"
    }

    function GetXmlHttpObject() {
        var objXMLHttp = null
        if (window.XMLHttpRequest) {
            objXMLHttp = new XMLHttpRequest()
        } else if (window.ActiveXObject) {
            objXMLHttp = new ActiveXObject("Microsoft.XMLHTTP")
        }
        return objXMLHttp
    }

    function sendEmail(t) {

        xmlHttp = GetXmlHttpObject()
        if (xmlHttp == null) {
            alert("Browser does not support HTTP Request")
            return
        }

        // first get the next po number
        url = "pogetnextponumber.asp?shopid=<?= $shopid ?>"

        xmlHttp.onreadystatechange = stateChanged
        xmlHttp.open("GET", url, true)
        xmlHttp.send(null)
    }

    function stateChanged() {
        //
        if (xmlHttp.readyState == 4 || xmlHttp.readyState == "complete") {
            ponumber = xmlHttp.responseText
            c = confirm("A new PO Number " + ponumber + " will be created and sent to " + document.getElementById("suppliername").value + ". Click OK to continue or Cancel to stop")

            if (c) {
                xmlHttp = GetXmlHttpObject()
                if (xmlHttp == null) {
                    alert("Browser does not support HTTP Request")
                    return
                }

                path = 'sendinventoryorder.asp?shopid=<?= $shopid ?>&ponumber=' + ponumber + '&shopemail=' + document.getElementById("shopemailaddress").value + '&sendemail=' + document.getElementById("emailaddress").value + '&suppliername=<?= urlencode($supplier) ?>'
                xmlHttp.onreadystatechange = sendEmailStateChanged
                xmlHttp.open("GET", path, true)
                xmlHttp.send(null)

            }
        }
    }

    function sendEmailStateChanged() {
        //
        if (xmlHttp.readyState == 4 || xmlHttp.readyState == "complete") {
            rt = xmlHttp.responseText
            alert(rt)
        }
    }

</script>
</body>
</html>
