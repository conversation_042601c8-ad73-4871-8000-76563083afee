<form method="post" action="<?=$template?>" id="excelform">
<input type="hidden" name="data" value="<?= base64_encode(serialize($alldata))?>">	<!-- this stores the table data in a serialized manner to be able to post to the export script -->
<input type="hidden" name="fields" value="<?= base64_encode(serialize($tablefields))?>">	<!-- this stores the tablefields array in a serialized manner to be able to post to the export script -->
<input type="hidden" name="heading" value="<?=$title?>"  >
<input type="hidden" name="sub_heading" value="<?= date('m/d/Y')?>">
<input type="hidden" name="subtitle" value="<?= !empty($subtitle)?$subtitle:"" ?>"  >
<input type="hidden" name="filename" value="<?=$title?>.xlsx">	<!-- excel file name goes here -->
</form>
