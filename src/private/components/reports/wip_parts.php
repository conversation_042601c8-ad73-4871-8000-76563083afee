<?php
$title = 'Parts On WIP (Allocated Parts)';  // Report Title Goes Here
// Use this for Gen Pop Reports
require "includes/header_reports.php";
require CONN;
//require("../php/functions.php");

// Use this for Custom Reports
//require("../../../php/includes/reports/header_reports.php");
//require("../../../php/conn.php");
//require("../../functions.php");

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = date('m/d/Y');

// Page Variables
//$subtitle = 'my subtitle';  // Report SubTitle Goes Here - Hide if not needed
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport_autosize.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
<title><?= $title;?></title>  <!-- Meta title for page. Change in variable above-->

<!DOCTYPE html>
<html>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
<?php

// Use this for Gen Pop Reports
require "includes/report_buttons.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/report_buttons.php");
?>

<!-- Column Headers Insert Report Variables here -->

	<table class=report_table>
	<thead>
	<tr class="table_header table_head">
		<td>RO #</td>
		<td>Part #</td>
		<td>Description</td>
		<td>Qty</td>
		<td style="text-align:right">Cost Each</td>
		<td style="text-align:right">Total Cost</td>
	</tr>
	</thead>
    <?php
    $tablefields=array('RO #','Part #','Description','Qty','Cost Each','Total Cost');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel

// Insert DB Query Here
	$qty = 0;
	$c = 0;
	$tc = 0;
	$gtqty = 0;
	$gtc = 0;
	$gttc = 0;
	$stmt = "select r.roid, partnumber, partdesc, cost, quantity from repairorders r inner join parts p on r.shopid = p.shopid and r.roid = p.roid where r.shopid = ? and r.`status` != 'closed' and r.`rotype` != 'no approval' order by partnumber";
	if($query = $conn->prepare($stmt)){
		$query->bind_param("s",$shopid);
		$query->execute();
		$roresult = $query->get_result();
	}else{
		echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
	}
		while($ro = $roresult->fetch_array()) {
			$qty = $ro["quantity"];
			$c = $ro["cost"];
			$tc = $qty * $c;
			$pstmt = "select partnumber from partsinventory where shopid = ? and partnumber = ?";
			if($query = $conn->prepare($pstmt)){
				$query->bind_param("ss",$shopid,$ro['partnumber']);
				$query->execute();
				$proresult = $query->get_result();
			}else{
				echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
			}
    ?>
<!-- Table Results Begin -->
    <tr class="table_data">
		<td><?= $ro["roid"];?></td>
		<td><?= strtoupper($ro["partnumber"]);?></td>
		<td><?= strtoupper($ro["partdesc"]);?></td>
		<td><?= $qty;?></td>
		<td style="text-align:right"><?= asDollars($c);?></td>
		<td style="text-align:right"><?= asDollars($tc);?></td>
    </tr>

    <?php
    	$gtqty += $qty;
    	$gtc += $c;
    	$gttc += $tc;
        $alldata[]=array($ro['roid'],strtoupper($ro["partnumber"]),strtoupper($ro["partdesc"]),$qty,asDollars($c),asDollars($tc)); //fill up the alldata array with the arrays of data to be shown in excel export
        } // end of while for loop
		$alldata[]=array('TOTALS','','',$gtqty,asDollars($gtc),asDollars($gttc));
    ?>
	<tr class="table_total">
		<td><b>TOTALS</b></td>
		<td></td>
		<td></td>
		<td><b><?= $gtqty; ?></b></td>
		<td style="text-align:right"><b><?= asDollars($gtc); ?></b></td>
		<td style="text-align:right"><b><?= asDollars($gttc); ?></b></td>
	</tr>

    </table>
<?php

// Use this for Gen Pop Reports
require "includes/footer_reports.php";
require "includes/report_form_todaydate.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/footer_reports.php");
//include("../../../php/includes/reports/report_form.php");
?>

</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
