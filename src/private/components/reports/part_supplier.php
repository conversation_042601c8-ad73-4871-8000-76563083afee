<?php
$title = 'Part Supplier Report';  // Report Title Goes Here
// Use this for Gen Pop Reports
require "includes/header_reports.php";
require CONN;
//require("../php/functions.php");

// Use this for Custom Reports
//require("../../../php/includes/reports/header_reports.php");
//require("../../../php/conn.php");
//require("../../functions.php");

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1=date('Y-m-d',strtotime($sd));
$ed1=date('Y-m-d',strtotime($ed));

// Page Variables
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}
//$subtitle = 'my subtitle';  // Report SubTitle Goes Here - Hide if not needed
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport_part_supplier.php'; //Only change if a custom PHPExcel is created in the template folder
// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
<title><?= $title;?></title>  <!-- Meta title for page. Change in variable above-->
<!DOCTYPE html>
<html>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>
<?php
// Use this for Gen Pop Reports
require "includes/report_buttons.php";
// Use this for Custom Reports
//include("../../../php/includes/reports/report_buttons.php");
?>
<!-- Column Headers Insert Report Variables here -->
	<table class=report_table>
		<thead>
	<tr class="table_header table_head">
		<td>Supplier</td>
		<td>RO#</td>
		<td>Date</td>
		<td>Part Number</td>
		<td>Description</td>
		<td>Qty</td>
		<td style="text-align:right">Cost</td>
		<td style="text-align:right">Total</td>
		<td style="text-align:right">Part Invoice #</td>
	</tr>
</thead>
    <?php
    $tablefields=array('Supplier','RO #','Date','Part Number','Description','Qty','Cost','Total','Part Invoice #');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel
// Insert DB Query Here

// Template Query Begins - Replace entire section
	$srunningcost = 0;
	$trunningcost = 0;
	$stmt = "SELECT distinct supplier FROM parts WHERE shopid = ? AND `date` >= ? AND `date` <= ? ";
	if ($query = $conn->prepare($stmt)) {
		$query->bind_param("sss", $shopid,$sd1,$ed1);
		$query->execute();
		$partresult = $query->get_result();
	}else{
		"Parts Prepare failed (" . $conn->errorno . ")" . $conn->error;
	}
	if ($partresult->num_rows > 0) {
		while($part = $partresult->fetch_array()) {
			//echo "Supplier is " . $part["supplier"] . "</br>";
			$supplier = str_replace($part["supplier"],"'","''");
			$supplier = $part["supplier"];
			//echo "Supplier after replace is " . $supplier . "</br>";
			$stmt = "SELECT ro.shopid, ro.roid ,p.`supplier`, p.roid, p.`date`,p.partnumber,p.partdesc,p.quantity, p.cost,p.linettlcost,p.partinvoicenumber FROM repairorders ro JOIN parts p on ro.shopid = p.shopid and ro.roid = p.roid WHERE p.shopid = ? AND p.deleted = 'no' AND p.`date` >= ? AND p.`date` <= ? AND p.supplier = ? AND ro.rotype != 'NO APPROVAL' ORDER BY p.roid ";
			if ($query = $conn->prepare($stmt)) {
				$query->bind_param("ssss", $shopid,$sd1,$ed1,$supplier);
				$query->execute();
				$part2result = $query->get_result();
			}else{
				"Parts 2 Prepare failed (" . $conn->errorno . ")" . $conn->error;
			}
			if ($part2result->num_rows > 0) {
				while($part2 = $part2result->fetch_array()) {
					$partdate = date_format(new DateTime($part2["date"]),'m/d/Y');
		?>
			<tr>
				<td><?= strtoupper($part2["supplier"]); ?></td>
				<td><?= $part2["roid"]; ?></td>
				<td><?= $partdate; ?></td>
				<td><?= strtoupper($part2["partnumber"]); ?></td>
				<td><?= strtoupper($part2["partdesc"]); ?></td>
				<td><?= $part2["quantity"]; ?></td>
				<td style="text-align:right"><?= asDollars($part2["cost"],2); ?></td>
				<td style="text-align:right"><?= asDollars($part2["linettlcost"],2); ?></td>
				<td style="text-align:right"><?= $part2["partinvoicenumber"]; ?></td>
			</tr>
<?php
				$srunningcost = $srunningcost + $part2["linettlcost"];
				$trunningcost = $trunningcost + $part2["linettlcost"];
				$alldata[] = array(strtoupper($part2["supplier"]),$part2["roid"],$partdate,strtoupper($part2["partnumber"]),strtoupper($part2["partdesc"]),$part2["quantity"],asDollars($part2["cost"],2),asDollars($part2["linettlcost"]),$part2["partinvoicenumber"]);
				} // end of part 2 while
			} // end of part 2
?>
		<tr>
		<td><strong>TOTAL FOR <?= strtoupper($part["supplier"]); ?></strong></td>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td style="text-align:right"><strong><?= asDollars($srunningcost,2); ?></strong></td>
		<td></td>
	</tr>
<?php
			$alldata[] = array('TOTALS','','','','','','',asDollars($srunningcost,2),'');
			$alldata[] = array('','','','','','','','','');
			$srunningcost = 0;
		} // end of part while
	} // end of part iff
?>
	<tr>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td></td>
		<td style="text-align:right"><strong>TOTAL ALL:</strong></td>
		<td style="text-align:right"><strong><?= asDollars($trunningcost,2);?></strong></td>
		<td></td>
	</tr>
	<?php
		$alldata[] = array('TOTALS','','','','','','',asDollars($trunningcost,2),'');
	?>
		</table>
<?php

// Use this for Gen Pop Reports
require "includes/footer_reports.php";
require "includes/report_form.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/footer_reports.php");
//include("../../../php/includes/reports/report_form.php");
?>
</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
