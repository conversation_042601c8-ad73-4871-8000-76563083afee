<?php
$title = "DVI Report";

require "includes/header_reports.php";
require CONN;

$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1=date('Y-m-d',strtotime($sd));
$ed1=date('Y-m-d',strtotime($ed));

function asDollarsNeg($value) {
    $num = number_format($value, 2);
    if ($num < 0){
        $num = "($".abs($num).")";
        return $num;
    }
    return '$' . $num;
}
?>
<body>
<style>

</style>
<?php
include_once "includes/report_buttons_noexcel.php";
?>

<?php

$num_inspections = $ins_totalro = $ins_count_ros = 0;
$num_ros = $total_ro = $ttlro = $total_media = $total_video = $total_image = 0;
$total_found = $total_recommended = 0;
$rolist = "";
$tech_dvi_list = "";
$techArr = array();

$stmt = "SELECT group_concat(roid), COUNT(*), SUM(TotalRO) FROM repairorders ro WHERE ro.shopid = ? AND ro.Status = 'CLOSED' AND ro.StatusDate >= ? AND ro.StatusDate <= ? AND ro.ROType != 'No Approval'";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("sss", $shopid, $sd1, $ed1);
    
    $query->execute();
    $query->bind_result($rolist, $num_ros, $total_ro);
    $query->fetch();
    
    $query->close();
}

if (!empty($rolist)) {
    $stmt = "SELECT COALESCE(COUNT(*), 0), COALESCE(COUNT(DISTINCT dvi.roid), 0) , COALESCE( NULLIF(dvi.technician, ''),0) as tech_id, COALESCE(CONCAT(e.employeeFirst,' ',e.employeeLast), '') as tech, COALESCE(CONCAT(e.employeeLast,', ',e.employeeFirst), '') as techname,  group_concat(dvi.roid), COALESCE(SUM(totalro), 0) as tech_ro, group_concat(dvi.id) FROM dvi LEFT JOIN employees e ON e.id = dvi.technician AND e.shopid = dvi.shopid JOIN repairorders r ON r.roid = dvi.roid AND r.shopid = dvi.shopid WHERE dvi.shopid = ($shopid) AND dvi.roid IN ($rolist) AND dvi.deleted = 'no' GROUP BY tech_id";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($tech_num_inspections, $tech_ins_count_ros, $tech_id, $tech,$techname,  $trolist, $tech_totalro, $tech_dvi_list);
        $query->store_result();
        while ($query->fetch()) {
            $non_ins_tro = $non_ins_count = 0;
            $tech_image = $tech_video = 0;
            $found_items = 0;
            if (!empty($trolist)) {
                $imgstmt = "SELECT 
                                COALESCE(COUNT(DISTINCT CASE WHEN img.type = 'img' THEN img.id END), 0) AS imageCount,
                                COALESCE(COUNT(DISTINCT CASE WHEN img.type = 'vid' THEN img.id END), 0) AS videoCount
                            FROM inspection_item_finding_image_inputs img 
                            JOIN inspection_item_finding_inputs fin 
                                ON img.shopid = fin.shopid 
                                AND img.item_finding_input_id = fin.id 
                            JOIN inspection_item_inputs inp 
                                ON inp.shopid = img.shopid 
                                AND inp.id = fin.item_input_id 
                            JOIN dvi 
                                ON dvi.shopid = inp.shopid 
                                AND dvi.id = inp.inspection_id 
                            WHERE img.shopid = ($shopid) 
                                AND dvi.technician = ? 
                                AND dvi.roid IN ($trolist)";

                            if ($imgquery = $conn->prepare($imgstmt)) {
                    $imgquery->bind_param("i", $tech_id);
                    $imgquery->execute();
                    $imgquery->bind_result($tech_image, $tech_video);
                    $imgquery->fetch();
                    $imgquery->close();
                } else {
                    echo "prepare failed l77" . $conn->error;
                }

                $imgstmt = "select COALESCE(COUNT(DISTINCT fin.id), 0) from inspection_item_finding_inputs fin JOIN inspection_item_inputs inp ON inp.shopid = fin.shopid AND inp.id = fin.item_input_id JOIN dvi ON dvi.shopid = inp.shopid AND dvi.id = inp.inspection_id WHERE fin.shopid = ($shopid) AND dvi.technician = ? AND dvi.roid IN ($trolist) and fin.`status` != 'good'";
                if ($imgquery = $conn->prepare($imgstmt)) {
                    $imgquery->bind_param("i", $tech_id);
                    $imgquery->execute();
                    $imgquery->bind_result($found_items);
                    $imgquery->fetch();
                    $imgquery->close();
                } else {
                    echo "prepare failed l89" . $conn->error;
                }

                $rstmt = "select COALESCE(SUM(totalro), 0), COALESCE(COUNT(r.roid), 0) from repairorders r WHERE r.shopid = ? AND r.roid IN (SELECT DISTINCT roid FROM labor where shopid = ? AND roid IN ($rolist) AND tech = ?) AND r.roid NOT IN ($trolist)";

                if ($imgquery = $conn->prepare($rstmt)) {
                    $imgquery->bind_param("sss", $shopid, $shopid, $techname);
                    $imgquery->execute();
                    $imgquery->bind_result($non_ins_tro, $non_ins_count);
                    $imgquery->fetch();
                    $imgquery->close();
                } else {
                    echo "prepare failed l105" . $conn->error;
                }

            }

            $total_media += $tech_image + $tech_video;
            $total_video += $tech_video;
            $total_image += $tech_image;
            $total_found += $found_items;

            $techArr[$tech_id] = array(
                'name' => empty($tech) ? "NO TECH" : strtoupper($tech),
                'lnfn' => $techname,
                'ins_count' => $tech_num_inspections,
                'ro_count' => $tech_ins_count_ros,
                'rolist' => $trolist,
                'totalro' => $tech_totalro,
                'media_count' => $tech_image + $tech_video,
                'image_count' => $tech_image,
                'video_count' => $tech_video,
                'find_count' => $found_items,
                'non_ins_tro' => $non_ins_tro,
                'non_ins_count' => $non_ins_count,
                'dvi_list' => $tech_dvi_list
            );
            $num_inspections += $tech_num_inspections;
            $ins_count_ros += $tech_ins_count_ros;
            $ttlro += $tech_totalro;
        }
        $query->close();
    } else {
        echo "Prepare failed l73 " . $conn->error;
    }
}
?>
<table class="report_table table table-sm table-striped table-bordered" style="font-size: 90%">
    <tr class="table_header" style="background-color: #5c90d2">
        <td>METRIC</td>
        <?php
        foreach ($techArr as $tid => $tech) {
            echo "<td data-dvilist='".$tech['dvi_list']."' colspan='2' id='" . $tid . "' class='text-right'>" . $tech['name'] . "</td>";
        }
        ?>
        <td  colspan='2' class='text-right'>All Techs</td>
    </tr>
    <tr>
        <td title="Total number of inspections done by a technician">NUMBER OF INSPECTIONS</td>
        <?php
        foreach ($techArr as $tech) {
            echo "<td colspan='2' class='text-right'>" . $tech['ins_count'] . "</td>";
        }
        ?>
        <td  colspan='2' class='text-right'><?= $num_inspections ?></td>
    </tr>
    <tr>
        <td title="Numbner of RO's closed without an inspection within the date range">RO's WITHOUT INSPECTIONS</td>
        <?php
        foreach ($techArr as $tech) {
            echo "<td colspan='2' class='text-right'>".$tech['non_ins_count']."</td>";
        }
        ?>
        <td class='text-right' colspan='2'><?= $num_ros - $ins_count_ros ?></td>
    </tr>
    <tr>
        <td title="Number of RO's with inspection">RO's WITH INSPECTIONS</td>
        <?php
        foreach ($techArr as $tech) {
            echo "<td colspan='2' class='text-right'>" . $tech['ro_count'] . "</td>";
        }
        ?>
        <td class="text-right" colspan='2'><?= $ins_count_ros ?></td>
    </tr>
    <tr>
        <td title="Total RO Sales with an inspection">TOTAL RO SALES WITH INSPECTIONS</td>
        <?php
        foreach ($techArr as $tech) {
            echo "<td colspan='2' class='text-right'>" . asDollarsNeg($tech['totalro']) . "</td>";
        }
        ?>
        <td class="text-right" colspan="2"><?= asDollarsNeg($total_ro) ?></td>
    </tr>
    <tr>
        <td title="Percentage number of RO's with completed inspections">% OF RO's WITH COMPLETED INSPECTIONS</td>
        <?php
        foreach ($techArr as $tech) {
            echo "<td colspan='2' class='text-right'>" . number_format(($tech['ins_count'] / $num_inspections) * 100, 2) . "%</td>";
        }
        ?>
        <td  colspan='2'></td>
    </tr>
    <tr>
        <td title="Average RO sales with inspections">ARO WITH INSPECTIONS</td>
        <?php
        foreach ($techArr as $tech) {
            echo "<td colspan='2' class='text-right'>" . asDollarsNeg($tech['ro_count'] > 0 ?($tech['totalro'] / $tech['ro_count']):0) . "</td>";
        }
        ?>
        <td  colspan='2' class="text-right"><?= asDollarsNeg($ins_count_ros > 0 ? ($ttlro / $ins_count_ros) : 0) ?></td>
    </tr>
    <tr>
        <td title="Average RO sales without inspection">ARO WITHOUT INSPECTIONS</td>
        <?php
        foreach ($techArr as $tech) {
            echo "<td colspan='2' class='text-right'>". asDollarsNeg($tech['non_ins_count'] > 0 ? ($tech['non_ins_tro'] / $tech['non_ins_count']) : 0)."</td>";
        }
        ?>
        <td  colspan='2' class="text-right"><?= asDollarsNeg($num_ros > 0 ? ($total_ro / $num_ros): 0) ?></td>
    </tr>
    <tr>
        <td title="Average RO sales difference between RO's with inspections vs without inspections">ARO DIFFERENCE</td>
        <?php
        foreach ($techArr as $tech) {
            $non_ins_tro = $tech['non_ins_count'] > 0 ? ($tech['non_ins_tro'] / $tech['non_ins_count']) : 0;
            $ins_tro = $tech['ro_count'] > 0 ?($tech['totalro'] / $tech['ro_count']):0;
            echo "<td colspan='2' class='text-right'>". asDollarsNeg($ins_tro - $non_ins_tro)."</td>";
        }
        $non_ins_tro = $num_ros > 0 ? ($total_ro / $num_ros): 0;
        $ins_tro = $ins_count_ros > 0 ? ($ttlro / $ins_count_ros) : 0;
        ?>
        <td  colspan='2' class="text-right"><?= asDollarsNeg($ins_tro - $non_ins_tro) ?></td>
    </tr>
    <tr>
        <td title="Average number of Image of video uploaded per inspection">AVG MEDIA PER INSPECTION - (PIC | VID)</td>
        <?php
        foreach ($techArr as $tech) {
            $image_ratio = $tech['ins_count'] > 0 ? $tech['image_count'] / $tech['ins_count'] : 0;
            $video_ratio = $tech['ins_count'] > 0 ? $tech['video_count'] / $tech['ins_count'] : 0;

            echo "<td colspan='2' class='text-right'>" . number_format($image_ratio, 2) . " | " . number_format($video_ratio, 2) . "</td>";
        }

        $total_video_ratio = number_format($num_inspections > 0 ? ($total_video / $num_inspections) : 0, 2);
        $total_image_ratio = number_format($num_inspections > 0 ? ($total_image / $num_inspections) : 0, 2);
        ?>
        
        <td  colspan='2' class="text-right"><?= $total_image_ratio . " | " . $total_video_ratio ?></td>
    </tr>
    <tr>
        <td title="Total number of Image of video uploaded per inspection">MEDIA PER INSPECTION - (PIC | VID)</td>
        <?php
        foreach ($techArr as $tech) {
            echo "<td colspan='2' class='text-right'>" . $tech['image_count'] . " | " . $tech['video_count'] . "</td>";
        }
        ?>
        <td  colspan='2' class="text-right"><?= $total_image . " | " . $total_video ?></td>
    </tr>
    <!--
    <tr>
        <td>AVG TIME PER INSPECTION</td>
        <?php
        foreach ($techArr as $tech) {
            echo "<td  colspan='2' class='text-right'></td>";
        }
        ?>
        <td  colspan='2' class="text-right"></td>
    </tr>
    -->
    <tr >
        <td title="Average Number of findings per inspection">AVG NUMBER OF FOUND ITEMS PER INSPECTION</td>
        <?php
        foreach ($techArr as $tech) {
            echo "<td  colspan='2' class='text-right'>" . number_format($tech['ins_count'] > 0 ?($tech['find_count'] / $tech['ins_count']) : 0, 2) . "</td>";
        }
        ?>
        <td  colspan='2' class="text-right"><?= number_format($num_inspections > 0 ? ($total_found / $num_inspections) : 0, 2) ?></td>
    </tr>
    <tr>
        <td colspan="<?= (sizeof($techArr) * 2) + 3 ?>">&nbsp;</td>
    </tr>
    <tr>
        <td colspan="<?= (sizeof($techArr) * 2) + 3 ?>"><h5 class="text-info">Breakdown of techs and percentages Technicians recommended line items for repair.</h5></td>
    </tr>
    <?php
    $total_findings = 0;
    $stmt = "select icat.id as cat_id, UCASE(icat.name) as category, COALESCE(COUNT(ip.id), 0) from inspection_item_inputs ip JOIN inspection_item_models im ON ip.model_id = im.id AND im.shopid = ip.shopid LEFT JOIN inspection_categories icat ON icat.shopid = im.shopid AND im.category_id = icat.id
JOIN dvi ON dvi.shopid = ip.shopid AND dvi.id = ip.inspection_id where dvi.shopid = ($shopid) AND dvi.roid IN ($rolist) GROUP BY icat.id order by icat.name";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($cat_id, $category, $total_findings);
        $query->store_result();
        while ($query->fetch()) {
            ?>
            <tr class="table_header" style="background-color: #5c90d2">
                <td class="text-left"><?= $category ?></td>
                <?php
                foreach ($techArr as $tid => $tech) {
                    echo "<td colspan='2' data-rolist='".$tech['rolist']."' id='" . $tid . "' class='text-right'>" . $tech['name'] . "</td>";
                }
                ?>
                <td colspan="2" class="text-right"><?= $total_findings ?></td>
            </tr>
            <?php

            $istmt = "select ip.id, ip.model_id, category_id, section_id, UCASE(im.name) from inspection_item_inputs ip JOIN inspection_item_models im ON ip.model_id = im.id AND im.shopid = ip.shopid
JOIN dvi ON dvi.shopid = ip.shopid AND dvi.id = ip.inspection_id where dvi.shopid = ($shopid) AND dvi.roid IN ($rolist) AND im.category_id = ? GROUP BY im.category_id, ip.model_id";
            if ($iquery = $conn->prepare($istmt)) {
                $iquery->bind_param("i", $cat_id);
                $iquery->execute();
                $iquery->bind_result($ipid, $model_id, $category_id, $section_id, $name);
                $iquery->store_result();
                while ($iquery->fetch()) {
                    ?>
                    <tr>
                    <td title="<?= $model_id ?>"><?= $name ?></td>
                    <?php
                    $allcount = 0;
                    foreach ($techArr as $tid => $tech) {
                        ?>
                            <?php
                        $count = 0;
                        $techSQL = "";
                        if (empty($tid)) {
                            $techSQL = "OR dvi.technician IS NULL OR dvi.technician = ''";
                        }
                        $stmt = "select COALESCE(COUNT(*),0) from inspection_item_inputs ip JOIN inspection_item_models im ON ip.model_id = im.id AND im.shopid = ip.shopid
JOIN dvi ON dvi.shopid = ip.shopid AND dvi.id = ip.inspection_id where dvi.shopid = ($shopid) AND im.id = ? AND dvi.roid IN (".$tech['rolist'].") AND (dvi.technician = $tid $techSQL)";
                        if ($cquery = $conn->prepare($stmt)) {
                            $cquery->bind_param("i", $model_id);
                            $cquery->execute();
                            $cquery->bind_result($count);
                            $cquery->store_result();
                            if ($cquery->num_rows > 0) {
                                while ($cquery->fetch()) {
                                    echo "<td>".$count."</td>";
                                    echo "<td title='$tid' class='text-right'>";
                                    if ($total_findings > 0) {
                                        echo number_format(($count / $total_findings) * 100, 2) . "%";
                                    } else {
                                        echo "0%";
                                    }
                                    echo "</td>";
                                    $allcount += $count;
                                }
                                $cquery->close();
                            } else {
                                echo "<td title='$tid'>0</td>";
                                echo "<td title='$tid' class='text-right'>0%</td>";
                            }
                        } else {
                            echo "PF " . $conn->error;
                        }
                    }
                    echo "</td>";
                    $allperc = 0;
                    if ($total_findings > 0) {
                        $allperc = ($allcount / $total_findings) * 100;
                    }
                    ?>
                    <td><?= $allcount ?></td>
                    <td class="text-right">(<?= number_format($allperc, 2) ?>%)</td>
                    <?php
                }
                ?>
                </tr>
                <?php
                $iquery->close();
            }
        }
        $query->close();
    }

    ?>
</table>
<script>
    /*
        <?= $rolist ?>
     */
</script>
<?php
require "includes/footer_reports.php";
require "includes/report_form.php";
?>
</body>
</html>