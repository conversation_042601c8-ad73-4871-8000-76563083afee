<?php
$title = 'Service Reminders Report';  // Report Title Goes Here
// Use this for Gen Pop Reports
require "includes/header_reports.php";
require CONN;
//require("../php/functions.php");

// Use this for Custom Reports
//require("../../../php/includes/reports/header_reports.php");
//require("../../../php/conn.php");
//require("../../functions.php");

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = date('m/d/Y');
//$sd = $_GET['sd'];
//$ed = $_GET['ed'];

// Page Variables
//$subtitle = 'my subtitle';  // Report SubTitle Goes Here - Hide if not needed
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport_autosize.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>

<title><?= $title;?></title>  <!-- Meta title for page. Change in variable above-->

<!DOCTYPE html>
<html>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>

<?php

// Use this for Gen Pop Reports
require "includes/report_buttons.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/report_buttons.php");
?>

<!-- Column Headers Insert Report Variables here -->

	<table class=report_table>
		<thead>
	<tr class="table_header table_head">
		<td>Customer</td>
		<td>Vehicle</td>
		<td>Reminder</td>
		<td>Date</td>
		<td>Recurring</td>
		<td>Interval</td>
	</tr>
</thead>

    <?php
    $tablefields=array('Customer','Vehicle','Reminder','Date','Recurring','Interval');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel

// Insert DB Query Here

// Template Query Begins - Replace entire section
	$stmt = "select reminderdate,reminderservice,recurring,`interval`,lastname,firstname,year,make,model from reminders r left join customer c on r.shopid = c.shopid and r.customerid = c.customerid left join vehicles v on r.shopid = v.shopid and r.vehid = v.vehid where r.shopid = ? order by reminderdate";
		if($query = $conn->prepare($stmt)){
			$query->bind_param("s",$shopid);
			$query->execute();
			$roresult = $query->get_result();
		}else{
			echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
        while ($ro = $roresult->fetch_array()) {
        	$remdate = date('m/d/Y',strtotime($ro["reminderdate"]));

// Template Query Ends

    ?>

<!-- Table Results Begin -->
    <tr class="table_data">
		<td><?= strtoupper($ro["firstname"] . " " . $ro["lastname"]);?></td>
		<td><?= strtoupper($ro["year"] . " " . $ro["make"] . " " . $ro["model"]);?></td>
		<td><?= strtoupper($ro["reminderservice"]);?></td>
		<td><?= $remdate;?></td>
		<td><?= strtoupper($ro["recurring"]);?></td>
		<td><?= $ro["interval"];?></td>
    </tr>

    <?php
		$alldata[]=array(strtoupper($ro["firstname"] . " " . $ro["lastname"]),strtoupper($ro["year"] . " " . $ro["make"] . " " . $ro["model"]),strtoupper($ro["reminderservice"]),$remdate,strtoupper($ro["recurring"]),$ro["interval"]); //fill up the alldata array with the arrays of data to be shown in excel export
        } // end of while for loop
        $alldata[]=array('','','','','','');
    ?>
    </table>

<?php

// Use this for Gen Pop Reports
require "includes/footer_reports.php";
require "includes/report_form_todaydate.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/footer_reports.php");
//include("../../../php/includes/reports/report_form.php");
?>

</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
