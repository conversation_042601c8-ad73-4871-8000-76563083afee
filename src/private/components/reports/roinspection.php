<!DOCTYPE html>
<html>
<?php

// Use this for Gen Pop Reports
//require("../php/functions.php");

// Use this for Custom Reports
//require("../../../php/includes/reports/header_reports.php");
//require("../../../php/conn.php");
//require("../../functions.php");


// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1=date('Y-m-d',strtotime($sd));
$ed1=date('Y-m-d',strtotime($ed));

$ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
$roTypeSQL = "AND rotype != 'No Approval'";
if ($ro_type != "all") {
    $roTypeSQL = "AND rotype = '" . $ro_type . "'";
}

// Page Variables
$title = 'Inspections Performed - ' . ucwords(strtolower($ro_type));  // Report Title Goes Here

require "includes/header_reports.php";
require CONN;

//$subtitle = '';  // Report SubTitle Goes Here - Hide if not needed
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE.'/reports/templates/excelexport_autosize.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>



<?php

// Use this for Gen Pop Reports
require "includes/report_buttons.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/report_buttons.php");
?>

<!-- Column Headers Insert Report Variables here -->

	<table class=report_table>
	<tr class="table_header table_head">
		<td>RO #</td>
		<td>Customer Name</td>
		<td>Vehicle</td>
		<td>Inspections</td>
		<td>Technician</td>
	</tr>

    <?php
    $tablefields=array('RO #','Customer Name','Vehicle','Inspections', 'Technician');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel


// Insert DB Query Here

// Template Query Begins - Replace entire section
        $inscount=0;
		$stmt = "select roid,customer,vehinfo from repairorders where shopid = ? and status = 'closed' $roTypeSQL and statusdate >= ? and statusdate <= ? order by StatusDate desc";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("sss",$shopid,$sd1,$ed1);
			$query->execute();
			$r = $query->get_result();
			while ($rs = $r->fetch_assoc()){
				// get the inspections for this ro
				$insp = "";
				$istmt = "select distinct inspectionname from roinspection where shopid = ? and roid = ? ";
				if ($iquery = $conn->prepare($istmt)){
					$iquery->bind_param("si",$shopid,$rs['roid']);
					$iquery->execute();
					$ir = $iquery->get_result();
				//	if($ir->num_rows==0)continue;
					while ($irs = $ir->fetch_assoc()){
						$inscount++;
						$insp .= $irs['inspectionname'].", ";
					}
				}

                $dvistmt = "SELECT m.name, dvi.technician as technicianId, (select concat(EmployeeLast, ' ', EmployeeFirst) as name from employees where shopid = ? and id = technicianId) as technician from dvi, inspection_models m where dvi.shopid = m.shopid and dvi.model_id = m.id AND dvi.shopid = ? and roid = ? and estimate_complete = 1";
           //     echo sprintf(str_replace("?", "'%s'", $dvistmt), $shopid, $rs['roid']);
                if($dquery = $conn->prepare($dvistmt)){
                    $dquery->bind_param("ssi", $shopid, $shopid, $rs['roid']);
                    $dquery->execute();
                    $dquery->bind_result($dvi_inspection, $technicianId, $technician);
                    while($dquery->fetch()){
                        $inscount++;
                        $insp .= $dvi_inspection.", ";
                    }
                    $dquery->close();
                } else {
                    echo "Prepare failed ".$conn->error;
                }
                if(empty($insp)){
                    continue;
                }
                $insp=rtrim($insp,', ');
		?>
		<tr>
			<td><?= strtoupper($rs["roid"]); ?></td>
			<td><?= strtoupper($rs["customer"]); ?></td>
			<td><?= strtoupper($rs["vehinfo"]); ?></td>
			<td><?= strtoupper($insp); ?></td>
            <td><?= $technician; ?></td>
		</tr>
		    <?php
		          $alldata[]=array(strtoupper($rs["roid"]), strtoupper(html_entity_decode($rs["customer"], ENT_QUOTES | ENT_XML1, 'UTF-8')), strtoupper($rs["vehinfo"]), strtoupper($insp), strtoupper($technician));//fill up the alldata array with the arrays of data to be shown in excel export
			}    // end of while for loop
		}   // end if for end of file

			$alldata[]=array("Total Count: " . $inscount, '','','','');
		    ?>
		    <tr>
			<td><b>Total Count: <?= $inscount ?></b></td>
			<td></td>
			<td></td>
			<td></td>
		</tr>
		</table>

<?php
// Use this for Gen Pop Reports
require "includes/footer_reports.php";
require "includes/report_form.php";

// Use this for Custom Reports
//include("../../../php/includes/reports/footer_reports.php");
//include("../../../php/includes/reports/report_form.php");
?>


</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
