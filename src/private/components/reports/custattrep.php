<!DOCTYPE html>
<html>
<?php

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd'])?$_GET['sd']:'';
$ed = isset($_GET['ed'])?$_GET['ed']:'';
$interval=(isset($_GET['interval'])?(int)($_GET['interval']):6);
$todaydate = 'todaydate';
$sd = date('m/d/Y');

// Page Variables
$title = 'Customer Attrition Report';  // Report Title Goes Here

include('includes/report_config.php');

$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)){
    $query->bind_param("s",$shopid);
    $query->execute();
    $query->bind_result($companyname,$conamereports);
    $query->fetch();
    $query->close();
}

if($conamereports == 'yes'){
    $title .= " - $companyname";
}
$subtitle = "This Report Shows Customers Who Have Not Returned In The Last ".$interval." Months";  // Report SubTitle Goes Here - Comment out if not needed
$template = COMPONENTS_PRIVATE."/reports/templates/excelexport.php"; //Only change if a custom PHPExcel is created in the template folder
?>


<link rel="stylesheet" type="text/css" href="<?= CSS ?>/reports.css">


<style>
/* tbody{
	display:inline ;
} */
@media print{
	tbody{
		display:inline;
}
	tr{
	font-size:8px !important;
}

}
</style>

<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>

<!-- Report Buttons	 -->
<?php
	include('includes/report_buttons.php');
?>

<!-- Column Headers Insert Report Variables here -->

	<table class="report_table">
		<tr class="table_header table_head">
			<td>Customer</td>
			<td>Vehicle</td>
			<td>Address</td>
			<td>City, State, ZIP</td>
			<td>Home #</td>
			<td>Work #</td>
			<td>Cell #</td>
			<td>Email</td>
			<td style="text-align:right">Date Last In</td>
		</tr>

    <?php
    $tablefields=array('Customer','Vehicle','Address','City','State','Zip','Home #','Work #','Cell #','Email','Date Last In');//set table headings array for excel export
    $alldata=array();//this will hold all the data arrays to be exported to excel

// Insert DB Query Here

// Template Query Begins - Replace entire section
	$stmt="SELECT `customer`,vehinfo,customeraddress,customercity,customerstate,customerzip,customerphone,`email`,cellphone,customerwork,max(datein) as datein from repairorders where shopid = ? and status = 'closed' and rotype != 'No Approval' group BY `customerid` having max(datein) <= DATE_SUB(curdate(), INTERVAL '$interval' MONTH) ORDER BY datein asc";
		if($query = $conn->prepare($stmt)){
			$query->bind_param("s",$shopid);
			$query->execute();
			$roresult = $query->get_result();
		}else{
			echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
		}
		$numrows = $roresult->num_rows;

			while($ro = $roresult->fetch_array()) {
				$statdate = date('m/d/Y',strtotime($ro["datein"]));
// Template Query Ends

    ?>

<!-- Table Results Begin -->
		<tr class="table_data">
			<td><?= strtoupper($ro["customer"]);?></td>
			<td><?= strtoupper($ro["vehinfo"]);?></td>
			<td><?= strtoupper($ro["customeraddress"]);?></td>
			<td><?= strtoupper($ro["customercity"] . ", " . $ro["customerstate"] . " " . $ro["customerzip"]); ?></td>
			<td><?= formatPhone($ro["customerphone"]);?></td>
			<td><?= formatPhone($ro["customerwork"]);?></td>
			<td><?= formatPhone($ro["cellphone"]);?></td>
			<td><?= strtoupper($ro["email"]);?></td>
			<td style="text-align:right"><?= $statdate;?></td>
		</tr>

    <?php
          $alldata[]=array(strtoupper($ro['customer']),strtoupper($ro['vehinfo']),strtoupper($ro['customeraddress']),strtoupper($ro['customercity']),strtoupper($ro['customerstate']),strtoupper($ro['customerzip']),formatPhone($ro['customerphone']),formatPhone($ro['customerwork']),formatPhone($ro['cellphone']),strtoupper($ro["email"]),$statdate); //fill up the alldata array with the arrays of data to be shown in excel export
        } // end of while for loop
     // end if for end of file
		$alldata[]=array('','','','','','','','','');
		$alldata[]=array('TOTALS',$numrows,'','','','','','','');

    ?>

			<!-- Table totals go here, if needed-->

		<tr class="table_total">
		 	<td><b>Totals</b></td>
		 	<td><b><?= $numrows; ?></b></td>
		 	<td></td>
		 	<td></td>
		 	<td></td>
		 	<td></td>
		 	<td></td>
		 	<td></td>
		 	<td></td>
	 	</tr>

    </table>

<!-- Footer Form Includes -->
<?php
	include('includes/footer_reports.php');
	require "includes/report_form_todaydate.php";
?>

</body>
</html>
<?php
if(isset($conn)){
	mysqli_close($conn);
}
?>
