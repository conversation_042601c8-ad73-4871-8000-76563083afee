<div id="editmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="editmodalLabel">Edit Event</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            <input type="hidden" id="editsd2" value="">
			<input type="hidden" id="historycid" value="">
			<input type="hidden" id="historyvid" value="">
			<input type="hidden" id="onhold2" value="">
			<input type="hidden" id="editid">
			<input type="hidden" id="editcustomerid">
			<input type="hidden" id="editvehid">
			<input type="hidden" id="editquoteid">

				<div class="row">

					<div class="col-md-6">

						<div class="form-outline mb-4">
							<input type="text" id="editfirstname" class="form-control">
							<label class="form-label" for="editfirstname">First Name</label>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="editlastname" class="form-control">
							<label class="form-label" for="editlastname">Last Name</label>
						</div>

                        <div class="form-outline mb-4 d-none">
                            <input type="text" id="editaddress" class="form-control">
                            <label class="form-label" for="editaddress">Address</label>
                        </div>

                        <div class="form-outline mb-4 d-none">
                            <input type="text" id="editcity" class="form-control">
                            <label class="form-label" for="editcity">City</label>
                        </div>

						<div class="form-group mb-4 row d-none">
							<div class="col-md-6">
                                <div class="form-outline">
                                    <input type="text" id="editstate" class="form-control">
                                    <label class="form-label" for="editstate">State</label>
                                </div>
                            </div>
                            <div class="col-md-6 d-none">
                                <div class="form-outline">
                                    <input type="text" id="editzip" class="form-control">
                                    <label class="form-label" for="editzip">Zip</label>
                                </div>
                            </div>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="edityear" class="form-control">
							<label class="form-label" for="edityear">Year</label>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="editmake" class="form-control">
							<label class="form-label" for="editmake">Make</label>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="editmodel" class="form-control">
							<label class="form-label" for="editmodel">Model</label>
						</div>

						<?php if($_COOKIE['mode']=='full'){?>

						<div class="form-outline mb-4">
							<input type="text" id="editschroid" readonly class="form-control">
							<label class="form-label" for="editschroid">RO#</label>
						</div>

						<?php if(strtolower($showwaiting) == 'yes'){?>

						<div class="form-row mb-4">
							<select class="select" id="editwaiter">
								<option value="">Select</option>
								<option value="waiting">Customer Is Waiting</option>
								<option value="dropping">Customer Is Dropping Off</option>
							</select>
							<label class="form-label select-label" for="editwaiter">Waiting / Dropping off</label>
						</div>

						<?php }?>

						<span id="salesperson"></span>
     
     					<?php }?>

					</div>

					<div class="col-md-6">

						<div class="form-outline mb-4">
							<textarea style="height:100px;" id="editservice" class="form-control"></textarea>
							<label class="form-label" for="editservice">Service</label>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="edithours" class="form-control" <?= $_COOKIE['mode']!='full'?'readonly':''?>>
							<label class="form-label" for="edithours">Hours</label>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="editemail" class="form-control">
							<label class="form-label" for="editemail">Email</label>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="editcellphone" class="form-control">
							<label class="form-label" for="editcellphone">Cell Phone</label>
						</div>

						<div class="form-check mb-4" id="editrollspan" style="display: none;">
							<input type="checkbox" class="form-check-input form-check-input-sm" id="editrollcheck" value="1">
							<label class="form-check-label" for="editrollcheck">Auto Roll Over</label>
						</div>

						<div class="form-outline mb-4 editdatetimepicker">
							<input type="text" id="editsd" class="form-control dateTimePickerBS">
							<label class="form-label" for="editsd">Date / Time</label>
						</div>

						<div id="linkedori" style="display: none; font-style: italic; font-size: smaller;"></div>

					</div>
				</div>

			</div>
								
			<div class="modal-footer d-flex justify-content-center">

				<?php if($_COOKIE['mode'] == 'full'){?>

				<?php if ($_COOKIE['createro']=='yes'){
				?>
				<button type="button" class="btn btn-secondary" id="converttorobutton" onclick="convertToRO()">Convert to RO</button>
				<?php
				}
				?>
				<button type="button" class="btn btn-secondary" style="display:none" id="gotorobutton"  onclick="goToRO()">Go to RO</button>
				<button type="button" class="btn btn-secondary" style="display:none" id="historybutton"  onclick="viewHistory()">History</button>
				<button type="button" class="btn btn-secondary" onclick="showReminder('<?php echo str_replace("'","\'",$shopname) ; ?>','<?php echo $shopphone ; ?>')">Send Reminder</button>
				<button type="button" class="btn btn-secondary" onclick="noReminders()">No Reminders</button>
				<button type="button" class="btn btn-secondary btn-mark" id="btn-done" style="display:none" onclick="markDone('complete')">Mark Done</button>
				<button type="button" class="btn btn-secondary btn-mark" id="btn-undone" style="display:none" onclick="markDone('incomplete')">Mark Not Done</button>
				<button type="button" class="btn btn-secondary" onclick="delEvent()">Delete</button>
			    <?php }?>
				<button type="button" class="btn btn-primary" onclick="saveEvent()">Save</button>
			</div>
		</div>
	</div>
</div>

<div id="sendremindermodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="sendremindermodalLabel">Send Reminder</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
				<div class="row">
					<div class="col-md-12">
						<h6 class="mb-4">This will send a reminder to your customer regarding their upcoming appointment</h6>
						
						<div class="form-outline mb-4">
							<input class="form-control" tabindex="1" id="updateemailto" name="updateemailto" value="" type="text">
							<label class="form-label" for="updateemailto">Email Address for Email</label>
						</div>
					
						<div class="form-outline mb-4">
							<input class="form-control" type="text" tabindex="1" value="" id="updatecellphone" name="updatecellphone" >
							<label class="form-label" for="updatecellphone">Cell Phone</label>
						</div>

						<div class="form-outline mb-4">
							<input class="form-control" type="text" tabindex="1" value="A friendly appointment reminder from <?php echo ucwords($shopname); ?>" id="msgsubject" name="msgsubject" >
							<label class="form-label" for="msgsubject">Email Subject</label>
						</div>
					
						<div class="form-outline mb-4">
							<textarea class="form-control" type="text" style="height:250px" tabindex="1" value ="" id="updateremindermsg" name="updateremindermsg" ></textarea>
							<label class="form-label" for="updateremindermsg">Message</label>
						</div>
					
						<div class="form-check mb-4">
							<input type="checkbox" class="form-check-input" tabindex="1" value ="on" id="updateemail" name="updateemail" >
							<label class="form-check-label" for="updateemail">Update Email in Appointment</label>
						</div>
					
						<div class="form-check">
							<input type="checkbox" class="form-check-input" tabindex="1" value ="on" id="updatecell" name="updatecell" >
							<label class="form-check-label" for="updatecell">Update Cell in Appointment</label>
						</div>
					</div>
				</div>
			</div>
			<div class="modal-footer d-flex justify-content-center">
				<button class="btn btn-md btn-secondary" type="button" onclick="sendReminder('email')">Send Email Update</button>
				<button class="btn btn-md btn-secondary" type="button" onclick="sendReminder('text')">Send Text Message</button>
				<button class="btn btn-md btn-secondary" type="button" onclick="sendReminder('both')">Send Both</button>
			</div>
		</div>
	</div>
</div>

<div id="searchmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="searchmodalLabel">Search Results</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">

        		<center><h5>Click an appointment to go to that day</h5></center>
				<table id="srchresults" class="sbdatatable w-100">
					<thead>
						<tr>
							<th>Customer</th>
							<th>Vehicle</th>
							<th>Date/Time</th>
							<th>Reason</th>
						</tr>
					</thead>
					<tbody>

					</tbody>
				</table>
	        </div>
	    </div>
	</div>
</div>


<div id="addmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="addmodalLabel">Add Event</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">

            <input type="hidden" id="customerid" value="0">
			<input type="hidden" id="vehid" value="0">
			<input type="hidden" id="onhold" value="0">
			<input type="hidden" id="quoteid" value="0">

				<div class="row">	
					<div class="col-md-6">

						<div class="form-row mb-4">
							<select id="addcolumn" class="select">
								<?php
								$rstr = "";
								$stmt = "select colorhex,title from colorcoding where shopid = '$shopid'";
								if ($query = $conn->prepare($stmt)){
								    $query->execute();
							    	$query->bind_result($colorhex,$title);
							    	while ($query->fetch()){
							    		echo "<option value='$colorhex'>$title</option>";
							    	}
							    }
								?>
							</select>
							<label class="form-label select-label" for="addcolumn">Column</label>
						</div>

						<div id="async" class="form-outline mb-4 autocomplete">
							<input type="text" id="addlastname" class="form-control" autocomplete="off" placeholder="Search by last name,first name or phone">
							<label class="form-label" for="addlastname">Last Name</label>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="addfirstname" class="form-control">
							<label class="form-label" for="addfirstname">First Name</label>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="addemail" class="form-control">
							<label class="form-label" for="addemail">Email</label>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="addcellphone" class="form-control">
							<label class="form-label" for="addcellphone">Cell Phone</label>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="addyear" class="form-control">
							<label class="form-label" for="addyear">Year</label>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="addmake" class="form-control">
							<label class="form-label" for="addmake">Make</label>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="addmodel" class="form-control">
							<label class="form-label" for="addmodel">Model</label>
						</div>


						 <a href="javascript:void(null)" style="display:none;" id="btn-addveh" onclick="openAddVeh()" class="text-primary"><i class="fas fa-plus"></i> Add new Vehicle</a>


					</div>

					<div class="col-md-6">

						<div class="form-outline mb-4">
							<textarea style="height:100px;" id="addservice" class="form-control"></textarea>
							<label class="form-label" for="addservice">Service</label>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="addhours" class="form-control">
							<label class="form-label" for="addhours"># Hours</label>
						</div>

						<div class="form-check mb-4">
							<input type="checkbox" class="form-check-input form-check-input-sm" id="rollcheck" value="1">
							<label class="form-check-label" for="rollcheck">Auto Roll Over</label>
						</div>

						<div class="form-outline mb-4 adddatetimepicker">
							<input type="text" id="addsd" class="form-control dateTimePickerBS">
							<label class="form-label" for="addsd">Start Date / Time</label>
						</div>

						<div class="form-row mb-4">
							<select onchange="reminderSet(this.value)" class="select" id="sendreminder">
								<option <?php if (strtolower($schedulesendreminderdefault) == "no"){ echo "selected='selected'"; } ?> value="no">No</option>
								<option <?php if (strtolower($schedulesendreminderdefault) == "text"){ echo "selected='selected'"; } ?> value="sms">Text Message</option>
								<option <?php if (strtolower($schedulesendreminderdefault) == "email"){ echo "selected='selected'"; } ?> value="email">Email</option>
								<option <?php if (strtolower($schedulesendreminderdefault) == "both"){ echo "selected='selected'"; } ?> value="both">Both</option>
							</select>
							<label class="form-label select-label" for="sendreminder">Send Reminder</label>
						</div>

						<div class="form-outline mb-4">
							<input type="text" id="schroid" class="form-control">
							<label class="form-label" for="schroid">RO #</label>
						</div>

						<?php
						$stmt = "select id from employees where shopid = '$shopid' and active = 'yes' and color!='' and color is not null";
                        if ($query = $conn->prepare($stmt))
                        {
                         $query->execute();
                         $query->store_result();
                         $num_empcolor = $query->num_rows;
                         $query->close();
                        }

                        if($num_empcolor>0)
                        {
                        ?>
                        <div class="form-row mb-4">
							<select class="select" id="sales">
								<option value="">Select</option>
								<?php
								$stmt = "select id, employeelast, employeefirst from employees where shopid = ? and active = 'yes'";
								if ($query = $conn->prepare($stmt))
								{
									$query->bind_param("s",$shopid);
								    $query ->execute();
									$query ->bind_result($empid,$emplast,$empfirst);
								    while ($query ->fetch())
									echo "<option value='".$empid."' ".(isset($_COOKIE['empid']) && $_COOKIE['empid']==$empid ? 'selected':'').">".$empfirst.' '.$emplast."</option>";
					            }
					            ?>
							</select>
							<label class="form-label select-label" for="sales">Sales Person</label>
						</div>
						<?php }else{?>
							<input type="hidden" id="sales" value="">
						<?php }?>

						<?php if(strtolower($showwaiting) == 'yes'){?>

						<div class="form-row mb-4">
							<select class="select" id="waiter">
								<option value="">Select</option>
								<option value="waiting">Customer Is Waiting</option>
								<option value="dropping">Customer Is Dropping Off</option>
							</select>
							<label class="form-label select-label" for="waiter">Waiting / Dropping off</label>
						</div>

						<?php }?>


					</div>
				</div>
			</div>

			<div class="modal-footer d-flex justify-content-center">
                <button class="btn btn-primary btn-md" type="button" onclick="addEvent()">Save</button>
            </div>

        </div>
    </div>
</div>


<div id="addvehmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="addvehmodalLabel">Add Vehicle</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
				<div class="row">
					<div class="col-md-12">
						<b>VIN: <a href="javascript:void(null)" onclick="decodeVIN()"><small>(Decode VIN)</small></a></b>
						<input type="text" id="vehvin" class="form-control"><br>
                        <b>Year:</b>
						<input type="text" id="vehyear" class="form-control"><br>
						<b>Make:</b>
						<input type="text" id="vehmake" class="form-control"><br>
						<b>Model:</b>
						<input type="text" id="vehmodel" class="form-control">
					</div>
				</div>
				<div class="modal-footer d-flex justify-content-center">
					<button type="button" class="btn btn-primary btn-md" onclick="addVehicle()">Save</button>
				</div>
			</div>
		</div>
	</div>
</div>


<div id="quotecheckmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog draggable-element modal-lg" data-mdb-drag-handle=".move_icon">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="calendarmodalLabel">Convert to RO</h5>
                <div class="text-right">
                    <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12 text-center">
                    	This appointment was created from a Quote. Do you want to convert the Quote to an RO?<br><br>
                        <button class="btn btn-lg btn-secondary me-2" type="button" onclick="createRO()">
                            Yes
                        </button>
                        <button class="btn btn-lg btn-primary" type="button" onclick="convertToRO(true)">
                            No
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>