<?php
require CONN;
$shopid = $_COOKIE['shopid'];

if ($_POST['t'] == "cidvid") {
    $cid = $_POST['cid'];
    $vid = $_POST['vid'];

    $stmt = "select firstname,lastname,year,make,model,email,cellphone,onhold from customer c inner join vehicles v on c.shopid = v.shopid and c.customerid = v.customerid where v.customerid = $cid and v.vehid = $vid"
        . " and c.shopid = '$shopid'";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $query->bind_result($fn, $ln, $year, $make, $model, $email, $cell, $onhold);
        $query->fetch();
        echo $fn . "|" . $ln . "|" . $year . "|" . $make . "|" . $model . "|" . $email . "|" . $cell . "|" . $onhold;
        $query->close();
    }

} elseif ($_POST['t'] == "noreminder") {

    $id = $_POST['id'];
    $stmt = "update schedule set sendreminder = 'no' where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        $query->execute();
        $conn->commit();
        $query->close();

        echo "success";
    }

} elseif ($_POST['t'] == "getcidvid") {

    $id = $_POST['id'];
    $stmt = "select s.customerid,s.vehid,c.onhold from schedule s join customer c on s.shopid = c.shopid and s.customerid = c.customerid  where s.shopid = ? and s.id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        $query->execute();
        $query->bind_result($cid, $vid, $onhold);
        $query->fetch();
        $query->close();

        echo $cid . "|" . $vid . "|" . $onhold;
    }

} elseif ($_POST['t'] == "search") {

    $st = $_POST['st'];
    $date = date("Y-m-d");
    $stmt = "select lastname,firstname,s.year,s.make,s.model,schdate,schtime,reason from schedule s LEFT join vehicles v on s.shopid = v.shopid AND v.VehID = s.VehID where (lastname like ? or firstname like ? or s.year = ? or s.make = ? or s.model like ? or v.Vin like ? or v.LicNumber like ? or v.fleetno like ?) and deleted = 'no' and s.shopid = '$shopid' order by schdate desc";
    if ($query = $conn->prepare($stmt)) {
        $stlike = $st . "%";
        $query->bind_param("ssssssss", $stlike, $stlike, $st, $st, $stlike, $stlike, $stlike, $stlike);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_array()) {
            echo '
				<tr onclick=\'goto("' . date("m/d/Y", strtotime($rs['schdate'])) . '")\'>
					<td>' . strtoupper($rs['firstname'] . ' ' . $rs['lastname']) . '</td>
					<td>' . strtoupper($rs['year'] . ' ' . $rs['make'] . ' ' . $rs['model']) . '</td>
					<td>' . date("m/d/Y", strtotime($rs['schdate'])) . ' ' . $rs['schtime'] . '</td>
					<td>' . strtoupper($rs['reason']) . '</td>
				</tr>
			';
        }

    }

} elseif ($_POST['t'] == "rsearch") {

    $st = $_POST['st'];
    $date = date("Y-m-d");
    $stmt = "select lastname,firstname,year,make,model,schdate,schtime,reason from schedule where (lastname like ? or firstname like ? or year = ? or make = ? or model like ?) and schdate >= '$date' and deleted = 'no' and shopid = '$shopid'";
    if ($query = $conn->prepare($stmt)) {
        $stlike = $st . "%";
        $query->bind_param("sssss", $stlike, $stlike, $st, $st, $stlike);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_array()) {
            echo '
				<tr onclick=\'goto("' . date("m/d/Y", strtotime($rs['schdate'])) . '")\'>
					<td>' . strtoupper($rs['firstname'] . ' ' . $rs['lastname']) . '</td>
					<td>' . strtoupper($rs['year'] . ' ' . $rs['make'] . ' ' . $rs['model']) . '</td>
					<td>' . date("m/d/Y", strtotime($rs['schdate'])) . ' ' . $rs['schtime'] . '</td>
					<td>' . strtoupper($rs['reason']) . '</td>
				</tr>
			';
        }

    }

} elseif ($_POST['t'] == "getlist") {
    $startdate = $_POST['start'];
    $enddate = $_POST['end'];
    $startdatetime = $_POST['start'] . "T00:00:00";
    $enddatetime = $_POST['end'] . "T23:59:59";

    // get the order of colors
    $stmt = "select id from colorcoding where shopid = '$shopid' order by id asc";
    $colorlist = array();
    $colorcnt = 0;
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $result = $query->get_result();
        while ($row = $result->fetch_array()) {
            $colorlist[$colorcnt] = $row['id'];
            $colorcnt = $colorcnt + 1;
        }
    } else {
        echo $conn->error;
    }
$key = "shop:$shopid:company"; // Custom key for this data block

    if ($redis && $redis->exists($key)) {
        $showaddress = $redis->hGet($key, 'showaddressonschedule');
    } else {
        $stmt = "select showaddressonschedule from company where shopid = ?";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $query->bind_result($showaddress);
            $query->fetch();
            $query->close();
        } else {
            echo $conn->error;
        }
    }

    $resourcearray = array('a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z');
    $colorarray = array('0D6E9E', 'B7730D', '0D6E0D', '860D0D', '6699cc', '560D56', 'B78C0D', '6E6E3D', '646490', '649090', '868686', '660033', 'C6538C');

    $stmt = "select s.roid,s.SchDate,s.SchTime,s.hours,s.ID,s.LastName,s.FirstName,s.Year,s.Make,s.Model,s.Reason,s.empid,colorcode,colorhex,c.id colorid,s.cellphone,s.homephone,s.workphone,s.email,done,s.customerid,s.vehid,scheduleflag,address,city,state,zip,s.linkedid,s.source,s.quoteid,s.waiter,s.customer_address, s.customer_city, s.customer_state, s.customer_zip from schedule s left JOIN customer ON s.shopid = customer.shopid AND s.CustomerID = customer.customerid left join colorcoding c on s.shopid = c.shopid and s.colorcode = c.colorhex where s.shopid = '$shopid' and deleted = 'no' and schdate >= '$startdate'"
        . "and schtime >= '00:00:00' and schdate <= '$enddate' and schtime <= '23:59:59'";
    //echo $stmt;

    $events = array();
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $result = $query->get_result();

        while ($row = $result->fetch_array()) {

            foreach ($row as &$value) {
                $value = iconv('UTF-8', 'ISO-8859-9', $value);
            }

            $row = array_map(utf8_encode, $row);

            $enddatetime = strtotime($row['SchDate'] . " " . $row['SchTime']) + (3600 * $row['hours']);

            $e['enddatetimetest'] = $enddatetime;
            $e = array();
            $e['id'] = $row['ID'];
            $e['firstname'] = $row['FirstName'];
            $e['lastname'] = $row['LastName'];
            $e['service'] = $row['Reason'];
            $e['year'] = $row['Year'];
            $e['make'] = $row['Make'];
            $e['model'] = $row['Model'];
            $e['email'] = $row['email'];
            $e['cellphone'] = str_replace("x", "", str_replace(" ", "", str_replace("-", "", str_replace("(", "", str_replace(")", "", $row['cellphone'])))));
            $e['colorcode'] = $row['colorcode'];
            $e['customerid'] = $row['customerid'];
            $e['vehid'] = $row['vehid'];
            $e['quoteid'] = $row['quoteid'];
            $e['schflag'] = $row['scheduleflag'];
            $e['done'] = $row['done'];
            $e['schroid'] = $row['roid'];
            $e['schsource'] = $row['source'];
            $e['waiter'] = $row['waiter'];

            $e['customer_address'] = $row['customer_address'];
            $e['customer_city'] = $row['customer_city'];
            $e['customer_state'] = $row['customer_state'];
            $e['customer_zip'] = $row['customer_zip'];

            if ($showaddress == "yes") {
                $address = strtoupper($row['address'] . "," . $row['city'] . "," . $row['state'] . "," . $row['zip']);
                if (strlen($row['address']) > 0) {
                    $address = "<a style='color:white !important;text-decoration:underline;font-weight:bold' target='_blank' onclick='preventParent()' href='https://google.com/maps?q=" . urlencode($address) . "'>$address</a>";
                } else
                    $address = '';

                $e['address'] = $address;
            } else {
                $e['address'] = "";
            }

            if (strlen($row['cellphone']) >= 7) {
                $cell = " CELL: " . formatPhone($e['cellphone']);
            } else {
                $cell = "";
            }
            if (strlen($row['homephone']) >= 7) {
                $cell .= " HOME: " . formatPhone($row['homephone']);
            }
            if (strlen($row['workphone']) >= 7) {
                $cell .= " WORK: " . formatPhone($row['workphone']);
            }
            $e['title'] = $row['LastName'] . "," . $row['FirstName'] . " " . $cell . "<hr class='m-1'>" . $row['Year'] . " " . $row['Make'] . " " . $row['Model'] . "<hr class='m-1'>";
            if (!empty($address)) $e['title'] .= $e['address'] . "<hr class='m-1'>";
            if (!empty($row['Reason'])) $e['title'] .= $row['Reason'] . " - ";
            $hrs = $row['hours'] . " hour" . ($row['hours'] > 1 ? 's' : '');
            $e['title'] .= $hrs;

            $e['monthtitle'] = $row['LastName'].",".$row['FirstName']." ".$cell." - ".$row['Year']." ".$row['Make']." ".$row['Model'].", ".(!empty($address)?$e['address']:'')."<br>(".(!empty($row['Reason'])?$row['Reason'].' - ':'').$hrs.")";


            $e['start'] = $row['SchDate'] . "T" . $row['SchTime'];
            $e['end'] = date('Y-m-d H:i:s', $enddatetime);

            $schcolor = '';
            $ccnt = 0;

            if (!empty($row['empid'])) {
                $stmt = "select color,employeefirst,employeelast from employees where shopid = ? and id=?";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("ss", $shopid, $row['empid']);
                    $query->execute();
                    $query->bind_result($schcolor, $empfname, $emplname);
                    $query->fetch();
                    $query->close();
                }
                $e['empname'] = ucwords(strtolower($empfname . ' ' . $emplname));
                $e['schcolor'] = $schcolor;
            }


            foreach ($colorlist as $k => $v) {
                if ($v == $row['colorid']) {
                    $e['resourceId'] = $resourcearray[$ccnt];
                    $e['color'] = "#" . $colorarray[$ccnt];
                }
                $ccnt = $ccnt + 1;
            }

            $e['tech'] = '';
            $e['hrs'] = $row['hours'];
            $e['roid'] = 0;
            $e['comid'] = 0;
            $e['laborid'] = 0;
            $e['allDay'] = 0;
            $e['linkedori'] = '';

            if (!empty($row['linkedid'])) {
                $stmt = "select schdate,schtime,SUM(hours) from schedule where shopid = ? and linkedid = ? and deleted = 'no' order by id asc";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("ss", $shopid, $row['linkedid']);
                    $query->execute();
                    $query->bind_result($linkdt, $linktime, $linkhrs);
                    $query->fetch();
                    $query->close();
                }
                $e['linkedori'] = "Start Date / Time: " . date('m/d/Y H:i', strtotime($linkdt . ' ' . $linktime)) . "&nbsp;&nbsp;Total Appt. Hours: " . $linkhrs;
            }

            // Merge the event array into the return array
            array_push($events, $e);


        }
    } else {
        echo "MTD Sales failed: (" . $conn->errno . ") " . $conn->error;
    }
    echo json_encode($events);
} elseif ($_POST['t'] == "addevent") {

    $laborid = $_POST['laborid'];
    $sd = str_replace("T", " ", $_POST['start']);
    $sd = strtotime($sd);
    //echo date("Y-m-d H:i:s",$sd)."\r\n";
    $strsdtest = date("H:i:s", $sd);
    //echo $strsdtest."\r\n";
    if ($strsdtest == "00:00:00") {
        $ed = $sd + 86400;
        $allday = true;
    } else {
        $ed = $sd + 3600;
        $allday = false;
    }
    $sd = date("Y-m-d H:i:s", $sd);
    $ed = date("Y-m-d H:i:s", $ed);
    //echo $sd."|".$ed."\r\n";
    $rid = $_POST['rid'];

    $stmt = "select roid,labor,laborhours,tech,complaintid from labor where shopid = '$shopid' and laborid = $laborid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $query->bind_result($roid, $labor, $laborhours, $tech, $complaintid);
        $query->fetch();
        $query->close();
    }

    // now use this to add a new item to the dispatch table
    $labor = substr($labor, 0, 30);

    if ($allday) {
        $stmt = "insert into dispatch (laborid,resourceid,startdatetime,enddatetime,roid,comid,shopid,tech,description,hrs,allday) values ($laborid,'$rid','$sd','$ed',$roid,$complaintid,'$shopid','$tech','$labor',$laborhours,1)";
    } else {
        $stmt = "insert into dispatch (laborid,resourceid,startdatetime,enddatetime,roid,comid,shopid,tech,description,hrs) values ($laborid,'$rid','$sd','$ed',$roid,$complaintid,'$shopid','$tech','$labor',$laborhours)";
    }
    //echo $stmt;
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
    }

    $stmt = "update labor set schedulecat = 'scheduled' where shopid = '$shopid' and laborid = $laborid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
    }


    echo "success";

} elseif ($_POST['t'] == "jobs") {

    echo "<button class=\"btn btn-danger\" type=\"button\" onclick=\"location.href='" . COMPONENTS_PRIVATE . "/v2/wip/wip.php'\">To WIP</button><span class=\"trash\" id=\"trash\" ><i class=\"fa fa-trash-o\"></i></span><p class=\"h3\">Drag Jobs to Trash <i class=\"fa fa-arrow-up\"></i> or Schedule <i class=\"fa fa-arrow-right\"></i></p>";
    echo "<div id='unscheduled'><h4>Jobs to Schedule <i class='fa fa-arrow-down'></i></h4>";
    $stmt = "select labor,laborid,laborhours,tech,roid from labor where ts >= '2017-03-22 00:00:00' and shopid = '$shopid' and schedulecat != 'scheduled'";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $result = $query->get_result();
        while ($row = $result->fetch_array()) {
            $laborid = $row['laborid'];
            $labord = substr($row['labor'], 0, 30);
            $hrs = $row['laborhours'];
            $roid = $row['roid'];
            $tech = strtoupper($row['tech']);
            echo "<div id='unsch-$laborid' class='fc-event'><b>#$roid - $tech</b> - $labord ($hrs Hours)<br>|$laborid</span></div>";
        }
    } else {
        echo "MTD Sales failed: (" . $conn->errno . ") " . $conn->error;
    }
    echo "</div>";

} elseif ($_POST['t'] == "deletejob") {

    $laborid = $_POST['laborid'];
    $stmt = "update labor set schedulecat = 'scheduled' where laborid = $laborid and shopid = '$shopid'";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
        echo "success";
    }


} elseif ($_POST['t'] == "getroid") {

    $roid = $_POST['roid'];

    $stmt = "select customerlast,customerfirst,cellphone,vehyear,vehmake,vehmodel,email,customerid,vehid,writer from repairorders where shopid = '$shopid' and roid = $roid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($customerlast, $customerfirst, $cellphone, $vehyear, $vehmake, $vehmodel, $email, $customerid, $vehid, $writer);
        $query->fetch();
        $query->close();
        //echo "success";
    }

    // now get a short list of complaints
    $stmt = "select complaint from complaints where cstatus = 'no' and shopid = '$shopid' and roid = $roid";
    $compl = "";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $result = $query->get_result();
        while ($row = $result->fetch_array()) {
            $compl .= $row['complaint'] . " *** ";
        }
        $query->close();
    }

    // now get total labor hours
    $stmt = "select sum(laborhours) hours from labor where shopid = '$shopid' and roid = $roid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($hrs);
        $query->fetch();
        $query->close();
    }

    // echo the results

    echo $customerlast . "|" . $customerfirst . "|" . $cellphone . "|" . $vehyear . "|" . $vehmake . "|" . $vehmodel . "|" . $email . "|" . $customerid . "|" . $vehid . "|" . $compl . "|" . $hrs . "|" . $writer;


} elseif ($_POST['t'] == "getquoteid") {

    $quoteid = $_POST['quoteid'];

    $stmt = "select customer,phone,year,make,model,email,cid,vid,writer from quotes where shopid = '$shopid' and id = $quoteid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($customer, $cellphone, $vehyear, $vehmake, $vehmodel, $email, $customerid, $vehid, $writer);
        $query->fetch();
        $query->close();
    }

    $cusarr = explode(', ', $customer);
    $customerlast = $cusarr[0];
    $customerfirst = $cusarr[1] ?? '';

    // now get a short list of complaints
    $stmt = "select complaint from quotecomplaints where shopid = '$shopid' and quoteid = $quoteid";
    $compl = "";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $result = $query->get_result();
        while ($row = $result->fetch_array()) {
            $compl .= $row['complaint'] . " *** ";
        }
        $query->close();
    }

    // now get total labor hours
    $stmt = "select sum(hours) hours from quotelabor where shopid = '$shopid' and quoteid = $quoteid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($hrs);
        $query->fetch();
        $query->close();
    }

    echo $customerlast . "|" . $customerfirst . "|" . $cellphone . "|" . $vehyear . "|" . $vehmake . "|" . $vehmodel . "|" . $email . "|" . $customerid . "|" . $vehid . "|" . $compl . "|" . $hrs . "|" . $writer;


} elseif ($_POST['t'] == "getcvdetails") {

    $cid = $_POST['cid'];
    $vid = $_POST['vid'];

    $stmt = "select lastname,firstname,cellphone,email from customer where shopid = '$shopid' and customerid = $cid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($customerlast, $customerfirst, $cellphone, $email);
        $query->fetch();
        $query->close();
    }

    $stmt = "select year,make,model from vehicles where shopid = '$shopid' and customerid = $cid and vehid=$vid";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($vehyear, $vehmake, $vehmodel);
        $query->fetch();
        $query->close();
    }


    echo $customerlast . "|" . $customerfirst . "|" . $cellphone . "|" . $vehyear . "|" . $vehmake . "|" . $vehmodel . "|" . $email;


} elseif ($_POST['t'] == "getdaytotal") {

    $d = date("Y-m-d", strtotime($_POST['d']));

    $stmt = "select coalesce(sum(hours),0) hrs from schedule where schdate = '$d' and shopid = '$shopid' and deleted = 'no'";
    if ($query = $conn->prepare($stmt)) {

        $query->execute();
        $query->bind_result($hrs);
        $query->fetch();
        $query->close();
        echo $hrs;

    }

} elseif ($_POST['t'] == "get3daytotal") {

    $sd = strtotime($_POST['sd']);
    $ed = strtotime($_POST['ed']);

    $thrs = "";

    for ($i = $sd; $i <= $ed; $i = $i + 86400) {
        $currdate = date("Y-m-d", $i);
        $stmt = "select coalesce(sum(hours),0) hrs from schedule where schdate = '$currdate' and shopid = '$shopid' and deleted = 'no'";
        if ($query = $conn->prepare($stmt)) {
            $query->execute();
            $query->bind_result($hrs);
            $query->fetch();
            $query->close();
            $thrs .= $currdate . "~" . number_format($hrs, 2) . "|";
        }

    }

    echo $thrs;

} elseif ($_POST['t'] == "deletecalitem") {

    $id = $_POST['id'];
    $stmt = "delete from dispatch where id = $id and shopid = '$shopid'";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
        echo "success";
    }

} elseif ($_POST['t'] == "reschedulecalitem") {

    $laborid = $_POST['laborid'];

    // get the laborid from the dispatch
    $stmt = "delete from dispatch where laborid = $laborid and shopid = '$shopid'";
    //echo $stmt;
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
    }

    $stmt = "update labor set schedulecat = '' where laborid = $laborid and shopid = '$shopid'";
    //echo $stmt;
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
        echo "success";
    }


} elseif ($_POST['t'] == "deletelaboritem") {

    $laborid = $_POST['laborid'];

    $stmt = "update labor set schedulecat = 'scheduled' where laborid = $laborid and shopid = '$shopid'";
    //echo $stmt;
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
        echo "success";
    }


} elseif ($_POST['t'] == "changeevent") {

    $id = $_POST['id'];
    $sd = $_POST['start'];
    $ed = str_replace("T", " ", $_POST['end']);
    $hdiff = (strtotime($ed) - strtotime($sd)) / 3600;

    $edarr = explode('T', $_POST['end']);
    $ed = strtotime($edarr[1]);

    $daynum = date('w', strtotime($sd));
    $shophours = array();

    $stmt = "select * from shophours where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_array())
            $shophours[$rs['day']] = array('start' => $rs['start'], 'end' => $rs['end']);
    }

    if (!empty($shophours)) {
        if (isset($shophours[$daynum])) {

            if ($ed < strtotime($shophours[$daynum]['start']) || $ed > strtotime($shophours[$daynum]['end']))
                die("Appointment Start or End Time is outside the shop hours for the day.");
        } else
            die("Appointment Start or End Time is outside the shop hours for the day.");
    }

    $stmt = "select schdate,hours from schedule where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        $query->execute();
        $query->bind_result($sd, $oldhours);
        $query->fetch();
        $query->close();
    }

    if ($hdiff - $oldhours > 0) {
        $checkhrs = checkMaxHours($sd, ($hdiff - $oldhours));
        if ($checkhrs < ($hdiff - $oldhours))
            die("Booking Failed. Max booking limit reached for the day");
    }

    $stmt = "update schedule set hours = '$hdiff' where id = $id and shopid = '$shopid'";
    //echo $stmt;
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
        echo "success";
    }


} elseif ($_POST['t'] == "moveevent") {

    //t=moveevent&id="+id+"&ed="+ed+"&sd="+sd+"&rid="+rid
    //echo $_POST['ed'];
    $resourcearray = array('a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z');
    $id = $_POST['id'];
    $rid = $_POST['rid'];
    $i = array_search($rid, $resourcearray);
    $colorcode = $_POST['colorcode'];
    $sd = strtotime(str_replace("T", " ", $_POST['sd']));
    $sd = date("Y-m-d H:i:s", $sd);
    $hrs = $_POST['hrs'];

    $sdarr = explode('T', $_POST['sd']);
    $ed = strtotime($sdarr[1]) + ($hrs * 3600);
    $schdate = date("Y-m-d", strtotime(str_replace("T", " ", $_POST['sd'])));
    $schtime = date("H:i:s", strtotime(str_replace("T", " ", $_POST['sd'])));

    $daynum = date('w', strtotime($sd));
    $shophours = array();

    $stmt = "select * from shophours where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_array())
            $shophours[$rs['day']] = array('start' => $rs['start'], 'end' => $rs['end']);
    }

    if (!empty($shophours)) {
        if (isset($shophours[$daynum])) {

            if (strtotime($schtime) < strtotime($shophours[$daynum]['start']) || strtotime($schtime) >= strtotime($shophours[$daynum]['end']) || $ed < strtotime($shophours[$daynum]['start']) || $ed > strtotime($shophours[$daynum]['end']))
                die("Appointment Start or End Time is outside the shop hours for the day.");
        } else
            die("Appointment Start or End Time is outside the shop hours for the day.");
    }


    // find the rid that matches the color code
    // get the order of colors
    $stmt = "select id,colorhex from colorcoding where shopid = '$shopid' order by id asc";
    $colorlist = array();
    $colorcnt = 0;
    $newridcolorcode = "";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $result = $query->get_result();
        while ($row = $result->fetch_array()) {
            if ($colorcnt === $i) {
                $newridcolorcode = $row['colorhex'];
            }
            $colorcnt = $colorcnt + 1;
        }
    } else {
        echo $conn->error;
    }

    $stmt = "select concat(firstname,' ',lastname),roid,linkedid,colorcode,lastname,firstname,year,make,model,reason,customerid,vehid,sendreminder,cellphone,email,empid,hours,deleted from schedule where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        $query->execute();
        $query->bind_result($customer, $roid, $linkedid, $oldcolorcode, $ln, $fn, $ay, $am, $amm, $as, $customerid, $vehid, $reminder, $cl, $em, $empid, $oldhours, $deleted);
        $query->fetch();
        $query->close();
    }

    if($deleted == 'yes')die();


    $checkhrs = checkMaxHours($schdate, $oldhours);
    if ($checkhrs < $oldhours)
        die("Booking Failed. Max booking limit reached for the day");


    $stmt = "update schedule set schdate = '$schdate', schtime = '$schtime', colorcode = '$newridcolorcode' where shopid = '$shopid' and id = $id";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();

        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='101'";
        $query = $conn->prepare($stmt);
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows();
        if ($numrows > 0) {
            $query->bind_result($textcontent, $emailcontent, $popupcontent);
            $query->fetch();
            $dt = date('m/d/Y g:i A', strtotime($schdate . ' ' . $schtime));
            if (!empty($roid)) {
                $rolink = "for <a href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?roid=$roid'>RO# $roid</a>";
                $textrolink = "for RO# $roid";
            } else {
                $rolink = $textrolink = '';
            }
            $emailcontent = str_replace("*|TIMESTAMP|*", $dt, str_replace("*|CUSTOMER|*", $customer, str_replace("*|ROLINK|*", $rolink, $emailcontent)));
            $popupcontent = str_replace("*|TIMESTAMP|*", $dt, str_replace("*|CUSTOMER|*", $customer, str_replace("*|ROLINK|*", $rolink, $popupcontent)));
            $textcontent = str_replace("*|TIMESTAMP|*", $dt, str_replace("*|CUSTOMER|*", $customer, str_replace("*|TEXTROLINK|*", $textrolink, $textcontent)));
            $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'101',?,?,?)";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param('ssss', $shopid, $popupcontent, $textcontent, $emailcontent);
                $query->execute();
                $conn->commit();
                $query->close();
            }
        }

        if ($oldcolorcode != $newridcolorcode) {
            $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='104'";
            $query = $conn->prepare($stmt);
            $query->execute();
            $query->store_result();
            $numrows = $query->num_rows();
            if ($numrows > 0) {
                $query->bind_result($textcontent, $emailcontent, $popupcontent);
                $query->fetch();
                if (!empty($roid)) {
                    $rolink = "for <a href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?roid=$roid'>RO# $roid</a>";
                    $textrolink = "for RO# $roid";
                } else {
                    $rolink = $textrolink = '';
                }
                $emailcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|ROLINK|*", $rolink, $emailcontent));
                $popupcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|ROLINK|*", $rolink, $popupcontent));
                $textcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|TEXTROLINK|*", $textrolink, $textcontent));
                $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'104',?,?,?)";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param('ssss', $shopid, $popupcontent, $textcontent, $emailcontent);
                    $query->execute();
                    $conn->commit();
                    $query->close();
                }
            }
        }

        if (!empty($linkedid)) {
            $stmt = "select coalesce(sum(hours),0) from schedule where shopid = ? and id >= ? and linkedid = ? and deleted = 'no'";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sis", $shopid, $id, $linkedid);
                $query->execute();
                $query->bind_result($hours);
                $query->fetch();
                $query->close();
            }

            $ts = array();

            $stmt = "update schedule set deleted = 'yes' where shopid = ? and id>=? and linkedid = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sss", $shopid, $id, $linkedid);
                $query->execute();
                $conn->commit();
                $query->close();
            }

            $ts = getslots($shopid, $schdate, $schtime, $hours);

            foreach ($ts as $t) {

                $stmt = "insert into schedule (roid,shopid,lastname,firstname,year,make,model,reason,schdate,schtime,hours,colorcode,customerid,vehid,sendreminder,cellphone,email,empid,linkedid) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("isssssssssdsiisssss", $roid, $shopid, $ln, $fn, $ay, $am, $amm, $as, $t['date'], $t['start'], $t['hours'], $newridcolorcode, $customerid, $vehid, $reminder, $cl, $em, $empid, $linkedid);

                    $query->execute();
                    $conn->commit();
                    $query->close();
                }
            }
        }

        echo "success";
    }


} elseif ($_POST['t'] == "changedate") {

    // "t=changedate&sd="+sd+"&ed="+ed+"&id="+id,
    //echo $_POST['sd'],"|",$_POST['ed']."\r\n";
    $sd = strtotime($_POST['sd']);
    $ed = strtotime($_POST['ed']);
    $sd = date("Y-m-d H:i:s", $sd);
    $ed = date("Y-m-d H:i:s", $ed);
    $id = $_POST['id'];
    $stmt = "update dispatch set startdatetime = '$sd', enddatetime = '$ed' where id = $id and shopid = '$shopid'";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $conn->commit();
        $query->close();
        echo "success";
    }


} elseif ($_POST['t'] == "addnewitem") {

    $hours = $_POST['ah'];
    $sd = date("Y-m-d", strtotime($_POST['sd']));
    $st = date("H:i:s", strtotime($_POST['sd']));
    $fn = $_POST['afn'];
    $ln = $_POST['aln'];
    $ay = $_POST['ay'];
    $am = $_POST['am'];
    $amm = $_POST['amm'];
    $as = $_POST['as'];
    $ac = $_POST['ac'];
    $vid = $_POST['vid'];
    $cid = $_POST['cid'];
    $em = $_POST['em'];
    $cl = $_POST['cl'];
    $rm = $_POST['rm'];
    $empid = $_POST['empid'];
    $schroid = $_POST['schroid'];
    $roll = $_POST['roll'];
    $quoteid = $_POST['quoteid'];
    $waiter = $_POST['waiter'];

    if (strlen($schroid) == 0) {
        $schroid = 0;
    }
    else
    $quoteid = 0;

    // now figure out the rid
    $resourcearray = array('a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z');
    $i = array_search($ac, $resourcearray);

    $daynum = date('w', strtotime($sd));

    $stmt = "select start,end from shophours where shopid = ? and day = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $daynum);
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows();
        if ($numrows > 0) {
            $query->bind_result($starthrs, $endhrs);
            $query->fetch();
            if (strtotime($st) < strtotime($starthrs) || strtotime($st) > strtotime($endhrs))
                die("Appointment start time is outside the shop hours for the day.");
        }
    }


    $ts = array();

    $linkedid = '';

    if ($roll == '1') {
        $ts = getslots($shopid, $sd, $st, $hours);
        if (count($ts) > 1) $linkedid = uniqid();
    } else {
        $checkhrs = checkMaxHours($sd, $hours);
        if ($checkhrs < $hours)
            die("Booking Failed. Max booking limit reached for the day");
        else
            $ts[] = array('date' => $sd, 'start' => $st, 'hours' => $hours);
    }


    foreach ($ts as $t) {
        $stmt = "insert into schedule (roid,shopid,lastname,firstname,year,make,model,reason,schdate,schtime,hours,colorcode,customerid,vehid,sendreminder,cellphone,email,empid,linkedid,quoteid,waiter) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        //echo $stmt;
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("isssssssssdsiisssssds", $schroid, $shopid, $ln, $fn, $ay, $am, $amm, $as, $t['date'], $t['start'], $t['hours'], $ac, $cid, $vid, $rm, $cl, $em, $empid, $linkedid, $quoteid, $waiter);
            $query->execute();
            $conn->commit();
            $query->close();
        }
    }

    $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='95'";
    $query = $conn->prepare($stmt);
    $query->execute();
    $query->store_result();
    $numrows = $query->num_rows();
    if ($numrows > 0) {
        $query->bind_result($textcontent, $emailcontent, $popupcontent);
        $query->fetch();
        if (!empty($roid)) {
            $rolink = "for <a href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?roid=$roid'>RO# $roid</a>";
            $textrolink = "for RO# $roid";
        } else {
            $rolink = $textrolink = '';
        }
        $customer = $fn . ' ' . $ln;
        $emailcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|ROLINK|*", $rolink, $emailcontent));
        $popupcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|ROLINK|*", $rolink, $popupcontent));
        $textcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|TEXTROLINK|*", $textrolink, $textcontent));
        $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'95',?,?,?)";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param('ssss', $shopid, $popupcontent, $textcontent, $emailcontent);
            $query->execute();
            $conn->commit();
            $query->close();
        }
    }

    echo "success";

} elseif ($_POST['t'] == "saveall") {

    $fn = $_POST['efn'];
    $ln = $_POST['eln'];
    $ay = $_POST['ey'];
    $am = $_POST['em'];
    $amm = $_POST['emm'];
    $as = $_POST['es'];
    $em = $_POST['eml'];
    $hrs = $_POST['hrs'];
    $roll = $_POST['roll'];
    $waiter = $_POST['waiter'];

    if (!is_numeric($hrs)) {
        $hrs = 1;
    }
    $cl = $_POST['cl'];
    $id = $_POST['id'];
    $stmt = "select concat(schdate,' ',schtime),schdate,roid,linkedid,colorcode,customerid,vehid,sendreminder,empid,display,source,hours,deleted from schedule where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        $query->execute();
        $query->bind_result($oldtime, $oldsd, $roid, $linkedid, $colorcode, $customerid, $vehid, $reminder, $empid, $display, $source, $oldhours, $deleted);
        $query->fetch();
        $query->close();
    }

    if($deleted == 'yes')die();

    if (isset($_POST['sd'])) {
        $sdt = $_POST['sd'];
        $sd = date("Y-m-d", strtotime($sdt));
        $st = date("H:i:s", strtotime($sdt));
        $customer = $fn . ' ' . $ln;

        $daynum = date('w', strtotime($sd));

        $stmt = "select start,end from shophours where shopid = ? and day = ?";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("si", $shopid, $daynum);
            $query->execute();
            $query->store_result();
            $numrows = $query->num_rows();
            if ($numrows > 0) {
                $query->bind_result($starthrs, $endhrs);
                $query->fetch();
                if (strtotime($st) < strtotime($starthrs) || strtotime($st) > strtotime($endhrs))
                    die("Appointment start time is outside the shop hours for the day.");
            }
        }

        if ($hrs - $oldhours > 0) {
            $checkhrs = checkMaxHours($sd, ($hrs - $oldhours));
            if ($checkhrs < ($hrs - $oldhours))
                die("Booking Failed. Max booking limit reached for the day");
        }


        if ($oldtime != ($sd . ' ' . $st)) {
            $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='101'";
            $query = $conn->prepare($stmt);
            $query->execute();
            $query->store_result();
            $numrows = $query->num_rows();
            if ($numrows > 0) {
                $query->bind_result($textcontent, $emailcontent, $popupcontent);
                $query->fetch();
                $dt = date('m/d/Y g:i A', strtotime($sd . ' ' . $st));
                if (!empty($roid)) {
                    $rolink = "for <a href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?roid=$roid'>RO# $roid</a>";
                    $textrolink = "for RO# $roid";
                } else {
                    $rolink = $textrolink = '';
                }
                $emailcontent = str_replace("*|TIMESTAMP|*", $dt, str_replace("*|CUSTOMER|*", $customer, str_replace("*|ROLINK|*", $rolink, $emailcontent)));
                $popupcontent = str_replace("*|TIMESTAMP|*", $dt, str_replace("*|CUSTOMER|*", $customer, str_replace("*|ROLINK|*", $rolink, $popupcontent)));
                $textcontent = str_replace("*|TIMESTAMP|*", $dt, str_replace("*|CUSTOMER|*", $customer, str_replace("*|TEXTROLINK|*", $textrolink, $textcontent)));
                $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'101',?,?,?)";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param('ssss', $shopid, $popupcontent, $textcontent, $emailcontent);
                    $query->execute();
                    $conn->commit();
                    $query->close();
                }
            }
        }

        if (empty($linkedid) && $roll == '0') {
            $stmt = "update schedule set lastname = ?,firstname = ?,year = ?,make = ?,model = ?,reason = ?,cellphone = ?,email = ?, schdate = ?, schtime = ?,hours = ?,waiter = ? where shopid = ? and id = ?";

            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("ssssssssssdssi", $ln, $fn, $ay, $am, $amm, $as, $cl, $em, $sd, $st, $hrs, $waiter, $shopid, $id);
                $query->execute();
                $conn->commit();
                $query->close();
                echo "success";
            }
        }

    } elseif (empty($linkedid) && $roll == '0') {

        if ($hrs - $oldhours > 0) {
            $checkhrs = checkMaxHours($oldsd, ($hrs - $oldhours));
            if ($checkhrs < ($hrs - $oldhours))
                die("Booking Failed. Max booking limit reached for the day");
        }

        $stmt = "update schedule set lastname = ?,firstname = ?,year = ?,make = ?,model = ?,reason = ?,cellphone = ?,email = ?,hours = ?,waiter = ?  where shopid = ? and id = ?";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("ssssssssdssi", $ln, $fn, $ay, $am, $amm, $as, $cl, $em, $hrs,$waiter, $shopid, $id);
            $query->execute();
            $conn->commit();
            $query->close();
            echo "success";
        } else {
            echo $conn->error;
        }

    }


    if (!empty($linkedid) || $roll == '1') {
        $ts = array();

        if (!empty($linkedid)) {
            $stmt = "select coalesce(sum(hours),0) from schedule where shopid = ? and id > ? and linkedid = ? and deleted = 'no'";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sis", $shopid, $id, $linkedid);
                $query->execute();
                $query->bind_result($hours);
                $query->fetch();
                $query->close();
            }

            $hours = $hours + $hrs;

            $stmt = "update schedule set deleted = 'yes' where shopid = ? and id>=? and linkedid = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sss", $shopid, $id, $linkedid);
                $query->execute();
                $conn->commit();
                $query->close();
            }
        } elseif ($roll == '1') {

            $linkedid = uniqid();

            $stmt = "update schedule set deleted = 'yes' where shopid = ? and id=?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("ss", $shopid, $id);
                $query->execute();
                $conn->commit();
                $query->close();
            }

            $hours = $hrs;

        }

        $ts = getslots($shopid, $sd, $st, $hours);

        $count = 0;

        foreach ($ts as $t) {

            if ($count == '0') {
                $sdisplay = $display;
            } else {
                $sdisplay = 'no';
            }

            $count++;

            $stmt = "insert into schedule (roid,shopid,lastname,firstname,year,make,model,reason,schdate,schtime,hours,colorcode,customerid,vehid,sendreminder,cellphone,email,empid,linkedid,display,source,waiter) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("isssssssssdsiissssssss", $roid, $shopid, $ln, $fn, $ay, $am, $amm, $as, $t['date'], $t['start'], $t['hours'], $colorcode, $customerid, $vehid, $reminder, $cl, $em, $empid, $linkedid, $sdisplay, $source, $waiter);

                $query->execute();
                $conn->commit();
                $query->close();
            }
        }
        echo "success";
    }


} elseif ($_POST['t'] == "delete") {

    $id = $_POST['id'];

    $stmt = "select concat(firstname,' ',lastname),roid,linkedid from schedule where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        $query->execute();
        $query->bind_result($customer, $roid, $linkedid);
        $query->fetch();
        $query->close();
    }

    $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='98'";
    $query = $conn->prepare($stmt);
    $query->execute();
    $query->store_result();
    $numrows = $query->num_rows();
    if ($numrows > 0) {
        $query->bind_result($textcontent, $emailcontent, $popupcontent);
        $query->fetch();

        if (!empty($roid)) {
            $rolink = "for <a href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?roid=$roid'>RO# $roid</a>";
            $textrolink = "for RO# $roid";
        } else {
            $rolink = $textrolink = '';
        }
        $emailcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|ROLINK|*", $rolink, $emailcontent));
        $popupcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|ROLINK|*", $rolink, $popupcontent));
        $textcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|TEXTROLINK|*", $textrolink, $textcontent));
        $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'98',?,?,?)";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param('ssss', $shopid, $popupcontent, $textcontent, $emailcontent);
            $query->execute();
            $conn->commit();
            $query->close();
        }
    }


    if (!empty($linkedid)) {
        $stmt = "update schedule set deleted = 'yes' where shopid = ? and linkedid = ?";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("ss", $shopid, $linkedid);
            $query->execute();
            $conn->commit();
            $query->close();
            echo "success";
        } else {
            echo $conn->error;
        }

    } else {
        $stmt = "update schedule set deleted = 'yes' where shopid = ? and id = ?";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("si", $shopid, $id);
            $query->execute();
            $conn->commit();
            $query->close();
            echo "success";
        } else {
            echo $conn->error;
        }
    }


} elseif ($_POST['t'] == "updatedone") {

    $id = $_POST['id'];
    $s = $_POST['s'];

    $stmt = "select deleted from schedule where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        $query->execute();
        $query->bind_result($deleted);
        $query->fetch();
        $query->close();
    }

    if($deleted == 'yes')die();

    $stmt = "update schedule set done = '$s' where shopid = ? and id = ?";
    //echo "update schedule set done = '$s' where shopid = '$shopid' and id = $id";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        $query->execute();
        $conn->commit();
        $query->close();

        if ($s == 'yes') {
            $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='92'";
            $query = $conn->prepare($stmt);
            $query->execute();
            $query->store_result();
            $numrows = $query->num_rows();
            if ($numrows > 0) {
                $query->bind_result($textcontent, $emailcontent, $popupcontent);
                $query->fetch();
                $stmt = "select concat(firstname,' ',lastname),roid from schedule where shopid = ? and id = ?";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("si", $shopid, $id);
                    $query->execute();
                    $query->bind_result($customer, $roid);
                    $query->fetch();
                    $query->close();
                }
                if (!empty($roid)) {
                    $rolink = "for <a href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?roid=$roid'>RO# $roid</a>";
                    $textrolink = "for RO#$roid";
                } else {
                    $rolink = $textrolink = '';
                }
                $emailcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|ROLINK|*", $rolink, $emailcontent));
                $popupcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|ROLINK|*", $rolink, $popupcontent));
                $textcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|TEXTROLINK|*", $textrolink, $textcontent));
                $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'92',?,?,?)";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param('ssss', $shopid, $popupcontent, $textcontent, $emailcontent);
                    $query->execute();
                    $conn->commit();
                    $query->close();
                }
            }
        }
        echo "success";
    } else {
        echo $conn->error;
    }
} elseif ($_POST['t'] == "updatefinished") {

    $id = $_POST['id'];
    $s = $_POST['s'];

    $stmt = "select deleted from schedule where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        $query->execute();
        $query->bind_result($deleted);
        $query->fetch();
        $query->close();
    }

    if($deleted == 'yes')die();

    $stmt = "update schedule set done = 'finished' where shopid = ? and id = ?";
    echo $stmt;
    //echo "update schedule set done = '$s' where shopid = '$shopid' and id = $id";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        $query->execute();
        $conn->commit();
        $query->close();


        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='89'";
        $query = $conn->prepare($stmt);
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows();
        if ($numrows > 0) {
            $query->bind_result($textcontent, $emailcontent, $popupcontent);
            $query->fetch();
            $stmt = "select concat(firstname,' ',lastname),roid from schedule where shopid = ? and id = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si", $shopid, $id);
                $query->execute();
                $query->bind_result($customer, $roid);
                $query->fetch();
                $query->close();
            }

            if (!empty($roid)) {
                $rolink = "for <a href='" . COMPONENTS_PRIVATE . "/v2/ro/ro.php?roid=$roid'>RO# $roid</a>";
                $textrolink = "for RO#$roid";
            } else {
                $rolink = $textrolink = '';
            }
            $emailcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|ROLINK|*", $rolink, $emailcontent));
            $popupcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|ROLINK|*", $rolink, $popupcontent));
            $textcontent = str_replace("*|CUSTOMER|*", $customer, str_replace("*|TEXTROLINK|*", $textrolink, $textcontent));
            $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'89',?,?,?)";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param('ssss', $shopid, $popupcontent, $textcontent, $emailcontent);
                $query->execute();
                $conn->commit();
                $query->close();
            }
        }
        echo "success";
    } else {
        echo $conn->error;
    }


} elseif ($_POST['t'] == "markdone") {

    $id = $_POST['id'];
    $flag = $_POST['flag'];

    $stmt = "select deleted from schedule where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        $query->execute();
        $query->bind_result($deleted);
        $query->fetch();
        $query->close();
    }

    if($deleted == 'yes')die();

    $stmt = "update schedule set scheduleflag = ? where shopid = ? and id = ?";
    if ($query = $conn->prepare($stmt)){
        $query->bind_param("ssi",$flag,$shopid,$id);
        $query->execute();
        $conn->commit();
        $query->close();
        echo "success";
    }else{
        echo $conn->error;
    }

} elseif ($_POST['t'] == "email" || $_POST['t'] == "text" || $_POST['t'] == "both") {
    // if I am going to update reminder...might as well update e-mail cell if they changed on the reminder screen
    $id = $_POST['id'];
    $s = $_POST['t'];
    //$email = $_POST['email'];
    //$cellphone = $_POST['cell'];

    $stmt = "update schedule set sendreminder = '$s' where shopid = ? and id = ? LIMIT 1";
    //echo "update schedule set sendreminder = '$s' where shopid = ? and id = ? LIMIT 1 ";

    //$stmt = "update schedule set cellphone = '$cellphone', sendreminder = '$s', email = '$email' where shopid = ? and id = ? LIMIT 1";
    //echo "update schedule set cellphone = '$cellphone', sendreminder = '$s', email = '$email' where = '$shopid' and id = $id LIMIT 1 ";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $id);
        $query->execute();
        $conn->commit();
        $query->close();
        echo "success";
    } else {
        echo $conn->error;
    }

} elseif ($_POST['t'] == 'checkdt') {
    $sd = date('Y-m-d', strtotime($_POST['dt']));
    $st = date('H:i', strtotime($_POST['dt']));
    $daynum = date('w', strtotime($sd));

    $shophours = array();

    $stmt = "select * from shophours where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_array())
            $shophours[$rs['day']] = array('start' => $rs['start'], 'end' => $rs['end']);
    }

    if (!empty($shophours)) {
        if (isset($shophours[$daynum])) {
            if (strtotime($st) < strtotime($shophours[$daynum]['start']) || strtotime($st) >= strtotime($shophours[$daynum]['end']))
                echo("no");
            else
                echo("yes");
        } else
            echo("no");
    } else
        echo("yes");
}


function checkMaxHours($date, $hours)
{
    global $conn, $shopid;
    $daynum = date('w', strtotime($date));
    $maxhours = '';
    $stmt = "select hours from shopmaxhours where shopid = ? and day = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $daynum);
        $query->execute();
        $query->bind_result($maxhours);
        $query->fetch();
        $query->close();
    }
    if ($maxhours != '') {
        $bookedhours = 0;

        $stmt = "select sum(hours) from schedule where shopid = ? and schdate = ? and deleted = 'no'";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("ss", $shopid, $date);
            $query->execute();
            $query->bind_result($bookedhours);
            $query->fetch();
            $query->close();
        }

        $diff = $maxhours - $bookedhours;

        if ($diff < $hours)
            return $diff;
        else
            return $hours;
    } else
        return $hours;
}

function getslots($shopid, $sd, $st, $hours)
{
    global $conn;
    $shophours = $ts = array();

    $stmt = "select * from shophours where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_array())
            $shophours[$rs['day']] = array('start' => $rs['start'], 'end' => $rs['end']);
    }

    $daynum = date('w', strtotime($sd));
    $hrsdiff = round((strtotime($shophours[$daynum]['end']) - strtotime($st)) / 3600, 2);

    if ($hrsdiff < 0) die("Auto Roll Over feature will not work unless Shop Hours are set in Settings -> Company");

    $hrsdiff = checkMaxHours($sd, $hrsdiff);

    if ($hours >= $hrsdiff) {
        $ts[] = array('date' => $sd, 'start' => $st, 'hours' => $hrsdiff);
        $remhours = $hours - $hrsdiff;
    } else {
        $ts[] = array('date' => $sd, 'start' => $st, 'hours' => $hours);
        $remhours = 0;
    }


    if (!empty($shophours)) {
        while ($remhours > 0) {
            $daynum = $daynum + 1;
            if ($daynum == 7) $daynum = 0;
            $sd = date('Y-m-d', strtotime($sd . ' +1 days'));

            if (!isset($shophours[$daynum]))
                continue;

            $workinghrs = round((strtotime($shophours[$daynum]['end']) - strtotime($shophours[$daynum]['start'])) / 3600, 2);

            $workinghrs = checkMaxHours($sd, $workinghrs);

            if ($remhours < $workinghrs) {
                $ts[] = array('date' => $sd, 'start' => $shophours[$daynum]['start'], 'hours' => $remhours);
                $remhours = 0;
            } else {
                $ts[] = array('date' => $sd, 'start' => $shophours[$daynum]['start'], 'hours' => $workinghrs);
                $remhours = $remhours - $workinghrs;
            }

        }
    } else
        $ts[] = array('date' => $sd, 'start' => $st, 'hours' => $hours);

    return $ts;
}


?>
<?php if (isset($conn)) {
    mysqli_close($conn);
} ?>
