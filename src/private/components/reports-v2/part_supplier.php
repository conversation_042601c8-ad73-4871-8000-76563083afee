<?php
$component = "reports-v2";
//To view this report with Finalized and Closed RO's, click here
//Click an RO number to view the RO
include getHeadGlobal($component);
include getRulesGlobal($component);

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));

// Page Variables
$title = 'Part Supplier Report';  // Report Title Goes Here
//$subtitle = 'my subtitle';  // Report SubTitle Goes Here - Hide if not needed
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_part_supplier.php'; //Only change if a custom PHPExcel is created in the template folder
// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);
?>
    <main id="reports">
        <div class="report">
            <div class="col-12">
                <div class="title col breadcrumb d-flex align-items-center">
                    <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                        class="text-secondary ps-3 pe-3">/</span>
                    <h2><?= $title; ?></h2>
                </div>
                <hr/>
                <p class="card-title">
                    <?php
                    if ((!empty($sd))) {
                        echo($sd);
                    }
                    if (!empty($sd) && !empty($ed)) {
                        echo ' to ';
                    }
                    if (!empty($ed)) {
                        echo $ed;
                    }
                    if (empty($sd) && empty($ed)) {
                        echo date('m/d/Y');
                    }
                    ?>
                </p>
            </div>
        </div>
        <div class="report">
            <?php
                include_once "inline_prompt.php";
            ?>
            <table class="tech_report w-100" id="tech_report" style="width: 100%">
                <thead>
                <tr class="table_header table_head">
                    <th>Supplier</th>
                    <th>RO#</th>
                    <th>Date</th>
                    <th>Part Number</th>
                    <th>Description</th>
                    <th>Qty</th>
                    <th style="text-align:right">Cost</th>
                    <th class="calc" style="text-align:right">Total</th>
                    <th style="text-align:right">Part Invoice #</th>
                </tr>
                </thead>
                <tbody>
                <?php
                $tablefields = array('Supplier', 'RO #', 'Date', 'Part Number', 'Description', 'Qty', 'Cost', 'Total', 'Part Invoice #');//set table headings array for excel export
                $alldata = array();//this will hold all the data arrays to be exported to excel
                // Insert DB Query Here

                // Template Query Begins - Replace entire section
                $srunningcost = 0;
                $trunningcost = 0;
                $stmt = "SELECT distinct supplier FROM parts WHERE shopid = ? AND `date` >= ? AND `date` <= ? ";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("sss", $shopid, $sd1, $ed1);
                    $query->execute();
                    $partresult = $query->get_result();
                } else {
                    "Parts Prepare failed (" . $conn->errorno . ")" . $conn->error;
                }
                if ($partresult->num_rows > 0) {
                    while ($part = $partresult->fetch_array()) {
                        //echo "Supplier is " . $part["supplier"] . "</br>";
                        $supplier = str_replace($part["supplier"], "'", "''");
                        $supplier = $part["supplier"];
                        //echo "Supplier after replace is " . $supplier . "</br>";
                        $stmt = "SELECT ro.shopid, ro.roid ,p.`supplier`, p.roid, p.`date`,p.partnumber,p.partdesc,p.quantity, p.cost,p.linettlcost,p.partinvoicenumber FROM repairorders ro JOIN parts p on ro.shopid = p.shopid and ro.roid = p.roid WHERE p.shopid = ? AND p.deleted = 'no' AND p.`date` >= ? AND p.`date` <= ? AND p.supplier = ? AND ro.rotype != 'NO APPROVAL' ORDER BY p.roid ";
                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("ssss", $shopid, $sd1, $ed1, $supplier);
                            $query->execute();
                            $part2result = $query->get_result();
                        } else {
                            "Parts 2 Prepare failed (" . $conn->errorno . ")" . $conn->error;
                        }
                        if ($part2result->num_rows > 0) {
                            while ($part2 = $part2result->fetch_array()) {
                                $partdate = date_format(new DateTime($part2["date"]), 'm/d/Y');
                                ?>
                                <tr>
                                    <td><?= $part2["supplier"]; ?></td>
                                    <td><?= $part2["roid"]; ?></td>
                                    <td><?= $partdate; ?></td>
                                    <td><?= $part2["partnumber"]; ?></td>
                                    <td><?= $part2["partdesc"]; ?></td>
                                    <td><?= $part2["quantity"]; ?></td>
                                    <td style="text-align:right"><?= asDollars($part2["cost"], 2); ?></td>
                                    <td style="text-align:right"><?= asDollars($part2["linettlcost"], 2); ?></td>
                                    <td style="text-align:right"><?= $part2["partinvoicenumber"]; ?></td>
                                </tr>
                                <?php
                                $srunningcost = $srunningcost + $part2["linettlcost"];
                                $trunningcost = $trunningcost + $part2["linettlcost"];
                                $alldata[] = array(strtoupper($part2["supplier"]), $part2["roid"], $partdate, strtoupper($part2["partnumber"]), strtoupper($part2["partdesc"]), $part2["quantity"], asDollars($part2["cost"], 2), asDollars($part2["linettlcost"]), $part2["partinvoicenumber"]);
                            } // end of part 2 while
                        } // end of part 2
                        ?>
                        <!--
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td><strong>Total For <?= ucwords(strtolower($part["supplier"])); ?></strong></td>
                            <td></td>
                            <td></td>
                            <td style="text-align:right"><strong><?= asDollars($srunningcost, 2); ?></strong></td>
                            <td></td>
                        </tr>
                        -->
                        <?php
                        $alldata[] = array('TOTALS', '', '', '', '', '', '', asDollars($srunningcost, 2), '');
                        $alldata[] = array('', '', '', '', '', '', '', '', '');
                        $srunningcost = 0;
                    } // end of part while
                } // end of part iff
                ?>
                </tbody>
                <tfoot>
                <tr class="table_total">
                    <td>TOTAL ALL</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td style="text-align:right"><strong></strong></td>
                    <td style="text-align:right"><strong><?= asDollars($trunningcost, 2); ?></strong></td>
                    <td></td>
                </tr>
                </tfoot>
                <?php
                $alldata[] = array('TOTALS', '', '', '', '', '', '', asDollars($trunningcost, 2), '');
                ?>
            </table>
        </div>
    </main>
<?php
include(COMPONENTS_PRIVATE_PATH . '/reports-v2/includes/report_form.php');

include getScriptsGlobal($component);


$dateRangeDisplay = ($_REQUEST['sdate'] ?? "") . " " . (!empty($_REQUEST['edate']) ? " to " . $_REQUEST['edate'] : "");
$subtitle = !empty($subtitle) ? $subtitle : "";

?>
    <script>
        $(document).ready(function () {
            //7, 9, 11
            let CalcT = Array();
            //console.log($("#tech_report thead"));
            $("#tech_report thead").find("th").each(function (idx, v) {
                if ($(v).hasClass("calc")) {
                    CalcT.push(idx);
                }
            });

            //console.log(CalcT);

            let floatVal = function (i) {
                return typeof i === 'string' ? Number(i.replace(/[$,]/g, '')) : typeof i === 'number' ? i : 0;
            };

            groupCol_writer = 0;
            let table = $("#tech_report").DataTable({
                responsive: true,
                fixedHeader: {
                    headerOffset: 68
                },
                colReorder: true,
                select: true,
                scrollY: false,
                scrollX: false,
                scroller: false,
                paging: false,
                language: {
                    searchPanes: {
                        emptyPanes: 'No records found in this date range'
                    },
                    search: "_INPUT_",
                    searchPlaceholder: "Search..."
                },
                buttons: [
                    {
                        extend: 'csv',
                        text: '<i class="fas fa-file-csv fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                        messageTop : "<?= $dateRangeDisplay ?>",
                    },
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint." (".$dateRangeDisplay.")" ?>",
                        messageTop : "<?= $subtitle ?>",
                        exportOptions: {
                            // Any other settings used
                            columns: [':visible'],
                            grouped_array_index: 0
                        },
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                        exportOptions: {
                            // Any other settings used
                            stripHtml: false,
                            columns: [':visible'],
                            grouped_array_index: 0
                        },
                        messageTop : "<?= $dateRangeDisplay."<br/>".$subtitle ?>",
                        footer: true,
                    }
                ],
                dom: "<'row'<'col-sm-12 col-md-6 dt_Search text-secondary'f><'col-sm-12 col-md-6 text-right dt_btns text-secondary'B>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
                //    columnDefs: [{visible: false, targets: groupCol_writer}],
                order: [[groupCol_writer, 'asc']],
                orderFixed: [0, "asc"],
                rowGroup: {
                    endRender: function (rows, group) {

                        let totsArr = Array();

                        CalcT.forEach(function (val, idx) {
                            var col = rows.data().pluck(val);
                            totsArr[val] = col.reduce(function (a, b) {
                                var fl_a = floatVal(a);
                                var fl_b = floatVal(b);
                                if (isNaN(fl_a)) {
                                    fl_a = 0;
                                }
                                if (isNaN(fl_b)) {
                                    fl_b = 0;
                                }
                                sum = fl_a + fl_b;
                                return (typeof sum != "undefined" || !isNaN(sum)) ? sum : 0;
                            }, 0);
                        });
                        var tr = rows.data();
                        var numcol = tr[0].length;

                        let formatting_options = {
                            style: 'currency',
                            currency: 'USD',
                            minimumFractionDigits: 2,
                        };
                        let USDollar = new Intl.NumberFormat('en-US', formatting_options);
                        var gname = group.replace(/[^A-Z0-9]/ig, "_");
                        // console.log('numcol', numcol);
                        if (typeof numcol !== "undefined" && numcol > 0) {
                            //   console.log();
                            var total_row = $('<tr id="' + gname + '_totals">');
                            total_row.append('<td colspan="2" class="text-left">' + group + ' TOTALS</td>')
                            for (var i = 2; i < numcol; i++) {

                                if (typeof totsArr[i] !== "undefined" || totsArr[i] != null) {
                                    var totVal = totsArr[i].toFixed(2);
                                    // console.log(totsArr[i], !isNaN(totsArr[i]));
                                    if (!isNaN(totsArr[i]) && rows.data().pluck(i)[0].indexOf('$') >= 0) {
                                        totVal = USDollar.format(totsArr[i])
                                    }
                                    total_row.append('<td class="text-right">' + totVal + '</td>')
                                    // console.log("Adding total")
                                } else {
                                    total_row.append('<td class=""></td>')
                                }
                            }
                            total_row.append("</tr>");
                            return total_row;
                        }

                    },
                    dataSrc: 0,
                    endClassName: 'table_total text-right',
                    startClassName: 'group_heading'
                },

            });
        });
    </script>
<?php
include getFooterComponent($component);
?>