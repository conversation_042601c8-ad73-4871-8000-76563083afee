<?php
$component = "reports-v2";
// Use this for Gen Pop Reports

include getHeadGlobal($component);
include getRulesGlobal($component);

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));

$ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
$roTypeSQL = "AND rotype != 'No Approval'";
if ($ro_type != "all") {
    $roTypeSQL = "AND rotype = '" . $ro_type . "'";
}

// Page Variables
$title = 'Parts Vs. Labor Percentage - ' . ucwords(strtolower($ro_type));  // Report Title Goes Here
$subtitle = 'This Report Includes Final Or Closed Repair Orders';  // Report SubTitle Goes Here - Hide if not needed
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($companyname, $conamereports);
    $query->fetch();
    $query->close();
}

if ($conamereports == 'yes') {
    $title .= " - $companyname";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_autosize.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
<body>
<?php

include getHeaderGlobal($component);
include getMenuGlobal($component);

$pcostpercentro = (isset($_GET['pcostpercentro']) ? $_GET['pcostpercentro'] : '');
?>

    <main id="reports">
        <div class="report">
            <div class="col-12">
                <div class="row">
                    <div class="col-md-10 col-sm-10">
                        <div class="title col breadcrumb d-flex align-items-center mb-0">
                            <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary d-print-none">Reports</a>
                            <span
                                    class="text-secondary d-print-none ps-3 pe-3">/</span>
                            <h2 class="d-print-none"><?= $title; ?></span></h2>
                            <h4 class="d-none d-print-inline"><?= $title . $companyNamePrint; ?></h4>
                        </div>
                    </div>
                    <div class="col-md-2 col-sm-4">
                        <button type="button" class="btn btn-secondary btn-md d-print-none float-end"
                                onclick="printreport()">Print
                        </button>
                        <button type="button" class="btn btn-secondary btn-md d-print-none float-end me-2"
                                onclick="$('#excelform').submit();">Excel
                        </button>
                    </div>
                </div>
                <hr/>
                <p class="card-title">
                    <?php
                    if ((!empty($sd))) {
                        echo($sd);
                    }
                    if (!empty($sd) && !empty($ed)) {
                        echo ' to ';
                    }
                    if (!empty($ed)) {
                        echo $ed;
                    }
                    if (empty($sd) && empty($ed)) {
                        echo date('m/d/Y');
                    }
                    ?>
                </p>
                <p>
                    <?= $subtitle ?? "" ?>
                </p>
            </div>
        </div>
        <div class="report d-print-none">
            <?php
            include_once "inline_prompt.php";
            ?>
        </div>
        <div class="d-flex">
            <section class="container-fluid" id="">
                <div class="row">
                    <div class="col-md-12">
                        <table class="table table-condensed table-striped table-hover reports_table">
                            <thead>
                            <tr class="">
                                <th style="text-align:right">LABOR</th>
                                <th style="text-align:center">PARTS</th>
                                <th style="text-align:left">PARTS COST</th>
                            </tr>
                            </thead>
                            <tbody>
                            <?php
                            $tablefields = array('LABOR', 'PARTS', 'PARTS COST');//set table headings array for excel export
                            $alldata = array();//this will hold all the data arrays to be exported to excel

                            // Insert DB Query Here
                            // Template Query Begins - Replace entire section

                            $stmt = "select sum(totalro+totalfees) as ttlro, sum(partscost) as prts, sum(totallbr) as ttllbr, sum(totalprts)as ttlprts from repairorders where shopid = ? and (status = 'closed' or status = 'final') $roTypeSQL and statusdate >= ? and statusdate <= ?";
                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("sss", $shopid, $sd1, $ed1);
                                $query->execute();
                                $roresult = $query->get_result();
                            } else {
                                echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
                            }
                            while ($ro = $roresult->fetch_array()) {
                                ?>
                                <tr>
                                    <td style="text-align:right"><?= "<b>Total Labor</b> " . asDollars($ro["ttllbr"]); ?></td>
                                    <td style="text-align:center"><?= "<b>Total Parts</b> " . asDollars($ro["ttlprts"]); ?></td>
                                    <td style="text-align:left"><?= "<b>Parts Cost</b> " . asDollars($ro["prts"]); ?></td>
                                </tr>
                                <?php
                                if (is_numeric($ro["ttllbr"])) {
                                    $ttllbr = $ro["ttllbr"];
                                }
                                if (is_numeric($ro["ttlprts"])) {
                                    $ttlprts = $ro["ttlprts"];
                                }
                                if (is_numeric($ro["prts"])) {
                                    $pcost = $ro["prts"];
                                }
                                if (is_numeric($ro["ttlro"])) {
                                    $ttlro = $ro["ttlro"];
                                }
                                if ($ttllbr > 0 && $ttlro > 0) {
                                    $laborpercentro = round(($ttllbr / $ttlro * 100), 2);
                                }
                                if ($ttlprts > 0 && $ttlro > 0) {
                                    $partspercentro = round(($ttlprts / $ttlro * 100), 2);
                                }
                                if ($pcost > 0 && $ttlro > 0) {
                                    $pcostpercentro = round(($pcost / $ttlro * 100), 2);
                                }
                                ?>
                                <tr>
                                    <td style="text-align:right"><?= "<b>Percent of RO</b> " . $laborpercentro . "%"; ?></td>
                                    <td style="text-align:center"><?= "<b>Percent of RO</b> " . $partspercentro . "%"; ?></td>
                                    <td style="text-align:left"><?= "<b>Parts Cost Percent</b> " . $pcostpercentro . "%"; ?></td>
                                </tr>
                                <?php
                                $alldata[] = array("Total Labor " . asDollars($ro["ttllbr"]), "Total Parts " . asDollars($ro["ttlprts"]), "Parts Cost " . asDollars($ro["prts"]));//fill up the alldata array with the arrays of data to be shown in excel export
                            } // end of while for loop
                            // end if for end of file
                            $alldata[] = array("Percent of RO " . $laborpercentro . "%", "Percent of RO " . $partspercentro . "%", "Parts Cost Percent " . $pcostpercentro . "%");
                            $alldata[] = array('', '', '');

                            ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
        </div>
    </main>

<?php
include getScriptsGlobal($component);
include(COMPONENTS_PRIVATE_PATH . '/reports-v2/includes/report_form.php');
include getFooterComponent($component);
?>