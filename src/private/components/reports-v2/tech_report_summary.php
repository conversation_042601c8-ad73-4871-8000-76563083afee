<?php

$component = "reports-v2";

//require COMPONENTS_PRIVATE_PATH."/reports-v2/includes/header_reports.php";
include getHeadGlobal($component);
include getRulesGlobal($component);

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));
$job_completed = isset($_REQUEST['job_completed']) ? filter_var($_REQUEST['job_completed'], FILTER_SANITIZE_STRING) : "";
$ro_status = isset($_REQUEST['ro_status']) ? filter_var($_REQUEST['ro_status'], FILTER_SANITIZE_STRING) : "Closed";
// Page Variables
$title = 'Technician Production Summary';  // Report Title Goes Here
$subtitle = 'This Report Includes ' . $ro_status . ' Status Repair Orders';  // Report SubTitle Goes Here - Hide if not needed
if ($job_completed == "yes") {
    $subtitle .= " and job status marked complete";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_tech_sum.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
<body>
<?php

include getHeaderGlobal($component);
include getMenuGlobal($component);


$roStatusSQL = "";
if ($ro_status != "all") {
    if ($ro_status != "open") {
        $roStatusSQL = "AND status = '" . $ro_status . "'";
    } else {
        $roStatusSQL = "AND status != 'Closed'";
    }
}


$jobSQL = "";
if (!empty($job_completed) && $job_completed == "yes") {
    $jobSQL = "AND c.acceptdecline = 'Job Complete'";
}


?>
    <main id="reports">
        <div class="report">
            <div class="col-12">
                <div class="title col breadcrumb d-flex align-items-center">
                    <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                        class="text-secondary ps-3 pe-3">/</span>
                    <h2><?= $title; ?></h2>
                </div>
                <hr/>
                <p class="card-title">
                    <?php
                    if ((!empty($sd))) {
                        echo($sd);
                    }
                    if (!empty($sd) && !empty($ed)) {
                        echo ' to ';
                    }
                    if (!empty($ed)) {
                        echo $ed;
                    }
                    if (empty($sd) && empty($ed)) {
                        echo date('m/d/Y');
                    }
                    ?>
                </p>
                <p>
                    <?= $subtitle ?>
                </p>
            </div>
        </div>
        <div class="report">
            <?php
            include_once "inline_prompt.php";
            ?>
            <table class="dtable w-100" style="width: 100%">
                <thead>
                <tr class="table_header table_head">
                    <th>Technician Name</th>
                    <th>Hours Sold</th>
                </tr>
                </thead>
                <tbody>
                <?php
                $tablefields = array('Technician Name', 'Hours Sold');//set table headings array for excel export
                $alldata = array();//this will hold all the data arrays to be exported to excel

                $tar = "";
                // Insert DB Query Here
                $tstmt = "select roid from repairorders where rotype != 'no approval' and shopid = ? and statusdate >= ? and statusdate <= ? $roStatusSQL";
                if ($query = $conn->prepare($tstmt)) {
                    $query->bind_param("sss", $shopid, $sd1, $ed1);
                    $query->execute();
                    $r = $query->get_result();
                    while ($rs = $r->fetch_assoc()) {
                        $tar .= $rs['roid'] . ",";
                    }

                } else {
                    echo "103-RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }


                if (substr($tar, -1) == ",") {
                    $tar = substr($tar, 0, strlen($tar) - 1);
                }

                // Template Query Begins - Replace entire section
                $runttlhrs = 0;
                if (!empty($tar)) {
                    $stmt = "select labor.tech, sum(labor.laborhours) as hours from labor JOIN complaints c ON c.shopid = labor.shopid AND c.roid = labor.ROID AND labor.complaintid = c.complaintid where labor.deleted = 'no' and labor.Tech != 'discount, discount' and labor.roid in ($tar) and labor.shopid = ? $jobSQL group by labor.tech ORDER BY labor.tech";

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("s", $shopid);
                        $query->execute();
                        $roresult = $query->get_result();

                        while ($ro = $roresult->fetch_array()) {
                            $hours = $ro["hours"];
                            $runttlhrs += $hours;
                            ?>
                            <tr>
                                <td><?= ucwords(strtolower($ro["tech"])); ?></td>
                                <td><?= $hours; ?></td>
                            </tr>
                            <?php
                            $alldata[] = array(strtoupper($ro["tech"]), strtoupper($ro["hours"]));//fill up the alldata array with the arrays of data to be shown in excel export
                        } // end of while for loop
                        $query->close();
                    } else {
                        echo "133-RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }
                    // end if for end of file
                }
                $alldata[] = array('', '');
                $alldata[] = array('TOTALS', $runttlhrs)
                ?>
                </tbody>
                <tfoot>
                <tr class="table_total">
                    <td><b>Total Hours</b></td>
                    <td><b><?= $runttlhrs; ?></b></td>
                </tr>
                </tfoot>
            </table>
        </div>
    </main>
<?php
include COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form_todaydate.php"; //only for reports with today date req
?>

<?php
include getScriptsGlobal($component);
include getFooterComponent($component);
?>
