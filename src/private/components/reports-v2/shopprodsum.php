<?php
$component = "reports-v2";
// Use this for Gen Pop Reports

include getHeadGlobal($component);
include getRulesGlobal($component);
$sd = $_GET['sdate'];
$ed = $_GET['edate'];
$sdate = date("Y-m-d", strtotime($_GET['sdate']));
$edate = date("Y-m-d", strtotime($_GET['edate']));
$shopid = $_COOKIE['shopid'];

if (isset($_GET['referrer'])) {
    $referrer = $_GET['referrer'];
} else {
    $referrer = '';
}

$ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
$roTypeSQL = "AND rotype != 'No Approval'";
if ($ro_type != "all") {
    $roTypeSQL = "AND rotype = '" . $ro_type . "'";
}

$title = 'Shop Production Summary With GP - ' . ucwords(strtolower($ro_type));

?>
<body>
<?php

include getHeaderGlobal($component);
include getMenuGlobal($component);
?>

<style>
    .dtable tbody td{
        padding: 10px 20px;
    }
</style>

<?php
$profitboost = "";
$stmt = "SELECT profitboost FROM company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($profitboost);
    $query->fetch();
    $query->close();
}
// get a list of the ro numbers
$rolist = "";
$totalro = 0;
$labor = 0;
$parts = 0;
$sublet = 0;
$tax = 0;
$partscost = 0;
$numros = 0;
$laborcost = 0;
$subletcost = 0;
$totalfees = 0;
$totallaborhours = 0;
$PPH = 0;
$rogpp = 0;
$stmt = "select 1 as c,roid,totalro,totallbr,totalprts,totalsublet,salestax,partscost,totalfees,PPH, gp, subtotal from repairorders where shopid = '$shopid' and status = 'closed' $roTypeSQL and statusdate >= '$sdate' and statusdate <= '$edate'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        $rolist .= $rs['roid'] . ",";
        $labor += $rs['totallbr'];
        $parts += $rs['totalprts'];
        $sublet += $rs['totalsublet'];
        $tax += $rs['salestax'];
        $partscost += $rs['partscost'];
        $totalro += $rs['totalro'];
        $numros += $rs['c'];
        $totalfees += $rs['totalfees'];
        $PPH += $rs['PPH'];
        $rogpp += $rs['gp'];
    }
}

$rolist = substr($rolist, 0, strlen($rolist) - 1);

// calculate the labor cost using the rolist

$stmt = "select distinct laborid,l.tech,l.roid,l.laborhours from labor l inner join complaints c on l.shopid = c.shopid and l.complaintid = c.complaintid where l.shopid = '$shopid' and l.roid in ($rolist) and c.cstatus = 'no' and l.deleted = 'no'";
//echo $stmt."<BR>";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        // get the hourly rate for the tech
        $totallaborhours += $rs['laborhours'];
        $tech = $rs['tech'];
        //echo $tech."<BR>";
        $currlaborcost = 0;
        if (strpos($tech, ",") > 0) {
            $tar = explode(",", $tech);
            $techlast = trim($tar[0], " ");
            $techfirst = trim($tar[1], " ");
            $techrate = 0;

            // get the hourly rate for this tech
            $estmt = "select COALESCE(hourlyrate, 0), paytype from employees where shopid = '$shopid' and employeelast = '$techlast' and employeefirst = '$techfirst' order by active desc limit 1";
            //echo $estmt."<BR>";
            if ($equery = $conn->prepare($estmt)) {
                //$equery->bind_param("ss",$techlast,$techfirst);
                $equery->execute();
                $equery->bind_result($techrate, $paytype);
                $equery->fetch();
                $equery->close();
            }

            $currlaborcost = $techrate * $rs['laborhours'];

            if (strtoupper($paytype) == "HOURLY") {

                $tcstmt = "select tech,sum(TIMESTAMPDIFF(MINUTE, startdatetime, enddatetime)) mindiff FROM labortimeclock where shopid = '$shopid' and not isnull(enddatetime) and roid = " . $rs['roid'] . " and laborid = " . $rs['laborid'] . " group by tech";

                $exres = $conn->query($tcstmt);
                $intcost = 0;
                while ($exrow = $exres->fetch_array()) {
                    $mindiff = $exrow['mindiff'];

                    if ($mindiff > 0) {
                        $hourdiff = $mindiff / 60;
                        $intcost += ($techrate * $hourdiff);
                    }

                }

                if (!empty($intcost)) {
                    $currlaborcost = $intcost;
                }

            } else if (strtoupper($paytype) == "FLATRATE") {
                $currlaborcost = $techrate * $rs['laborhours'];
            }
        }

        $laborcost += $currlaborcost;
    }
}

// get sublet cost
$stmt = "select s.complaintid,subletcost scost from sublet s inner join complaints c on s.shopid = c.shopid and s.complaintid = c.complaintid where s.shopid = '$shopid' and s.roid in ($rolist) and c.cstatus = 'no'";
//echo $stmt;
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        $subletcost += $rs['scost'];
    }
    $query->close();
}

$subtotal = $totalro - $tax;
if ($numros > 0) {
    $avghoursperro = number_format($totallaborhours / $numros, 2);
    $aro = number_format($subtotal / $numros, 2);
} else {
    $avghoursperro = 0;
    $aro = 0;
}
if ($totallaborhours > 0) {
    $effectivelaborrate = number_format($labor / $totallaborhours, 2);
} else {
    $effectivelaborrate = 0;
}

$gp = $subtotal - $partscost - $laborcost - $subletcost;

$subletprofit = $sublet - $subletcost;
$laborprofit = $labor - $laborcost;

if ($labor != 0) {
    $laborgppercent = $laborprofit / $labor;
} else {
    $laborgppercent = 0;
}

$partsprofit = $parts - $partscost;
if ($parts != 0) {
    $partsgppercent = $partsprofit / $parts;
} else {
    $partsgppercent = 0;
}

if ($gp > 0) {
    $partsgpp = number_format(100 * ($partsprofit / $gp), 0) . "%";
    $laborgpp = number_format(100 * ($laborprofit / $gp), 0) . "%";
    $subletgpp = number_format(100 * ($subletprofit / $gp), 0) . "%";
    $feesgpp = number_format(100 * ($totalfees / $gp), 0) . "%";
} else {
    $partsgpp = 0;
    $laborgpp = 0;
    $subletgpp = 0;
    $feesgpp = 0;
}

if ($subtotal > 0) {
    $gpp = number_format(100 * ($gp / $subtotal), 0);
    $partsgppoftotal = number_format(100 * ($partscost / $subtotal), 2) . "%";
} else {
    $gpp = 0;
    $partsgppoftotal = 0;
}
// labor
if ($subtotal > 0) {
    $gpl = number_format(100 * ($gp / $subtotal), 0);
    $laborgppoftotal = number_format(100 * ($laborcost / $subtotal), 2) . "%";
} else {
    $gpl = 0;
    $laborgppoftotal = 0;
}

// sublet
if ($subtotal > 0) {
    $gps = number_format(100 * ($gp / $subtotal), 0);
    $subletgppoftotal = number_format(100 * ($subletcost / $subtotal), 2) . "%";
} else {
    $gps = 0;
    $subletgppoftotal = 0;
}

$totalprofit = $partsprofit + $laborprofit + $subletprofit + $totalfees;
$gpperlh = 0;
if ($totallaborhours > 0) {
    $gpperlh = $totalprofit / $totallaborhours;
} else {
    $gpperlh = 0;
}

$stmt = "SELECT roid FROM repairorders WHERE shopid = ? $roTypeSQL AND status = 'closed' AND statusdate >= ? AND statusdate <= ? ";
//echo $stmt;

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("sss", $shopid, $sdate, $edate);
    $query->execute();
    $roresult = $query->get_result();
} else {
    echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$numofrows = $roresult->num_rows;

$shopidlist = "";

if ($roresult->num_rows > 0) {
    while ($ro = $roresult->fetch_array()) {
        $shopidlist = $shopidlist . $ro["roid"] . ",";
    } // end of while loop
}

if (substr($shopidlist, -1) == ",") {
    $shopidlist = substr($shopidlist, 0, strlen($shopidlist) - 1);

}

$tlabordiscount = 0;
$tpartsdiscount = 0;
$stmt = "SELECT coalesce(SUM(if(tech = 'DISCOUNT, DISCOUNT',abs(linetotal),discount)),0) as tlabordiscount FROM labor WHERE shopid = ? AND roid in (" . $shopidlist . ")";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($tlabordiscount);
    $query->fetch();
    $query->close();
}
$stmt = "SELECT coalesce(SUM(if(partnumber = 'DISCOUNT',abs(linettlprice),((partprice*quantity)-linettlprice))),0) as tpartsdiscount FROM parts WHERE shopid = ? AND roid in (" . $shopidlist . ") and (partnumber='Discount' OR discount!=0)";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($tpartsdiscount);
    $query->fetch();
    $query->close();
}
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-10 col-sm-10">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary d-print-none">Reports</a>
                        <span
                                class="text-secondary d-print-none ps-3 pe-3">/</span>
                        <h2 class="d-print-none"><?= $title; ?></span></h2>
                        <h4 class="d-none d-print-inline"><?= $title . $companyNamePrint; ?></h4>
                    </div>
                </div>
                <div class="col-md-2 col-sm-4">
                    <button type="button" class="btn btn-secondary btn-md d-print-none float-end"
                            onclick="printreport()">Print
                    </button>
                </div>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report d-print-none">
        <?php
        include_once "inline_prompt.php";
        ?>
    </div>

    <div class="d-flex">
        <section class="container-fluid">
            <table class="dtable" style="width: 100%">
                <thead>
                    <tr class="report_head" id="dt-header">
                        <th class="text-center" style="width: 50%;">Data</th>
                        <th class="text-center" style="width: 50%;">Analysis</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="color: #a01c1c">Repair Orders</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>
                            # Repair Orders
                            <span style="float: right"><?= $numros; ?></span>
                        </td>
                        <td>
                            Current ARO (Subtotal / # RO's)
                            <span style="float: right">$<?= $aro; ?></span>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            Parts Cost | % Of Subtotal
                            <span style="float: right"><?= asDollars($partscost) . " <b>|</b> " . $partsgppoftotal; ?></span>
                        </td>
                        <td>
                            Gross Profit | %
                            <span style="float: right"><?= asDollars($gp) . " | " . $gpp . "%"; ?></span>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            Labor Cost | % Of Subtotal
                            <span style="float: right"><?= asDollars($laborcost) . " <b>|</b> " . $laborgppoftotal; ?></span>
                        </td>
                        <td>
                            <?= 
                                $profitboost == "yes" ? 'PPH' : '';
                            ?> 
                            <span style="float: right">
                                <?php 
                                    echo $profitboost == "yes" 
                                        ? asDollars(($numros) ? ($PPH / $numros) : 0) 
                                        : ""; 
                                ?>
                            </span>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            Sublet Cost | % Of Subtotal
                            <span style="float: right"><?= asDollars($subletcost) . " <b>|</b> " . $subletgppoftotal; ?></span>
                        </td>
                        <td></td>
                    </tr>

                    <tr>
                        <td>
                            Labor Sales
                            <span style="float: right"><?= asDollars($labor); ?></span>
                        </td>
                        <td>
                            Labor Profit | % of GP
                            <span style="float: right"><?= number_format($laborgppercent * 100, 0) . "% / $" . number_format($laborprofit, 2) . " | " . $laborgpp; ?></span>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            Parts Sales
                            <span style="float: right"><?= asDollars($parts); ?></span>
                        </td>
                        <td>
                            Parts Profit | % of GP
                            <span style="float: right"><?= number_format($partsgppercent * 100, 0) . "% / " . asDollars($partsprofit) . " | " . $partsgpp; ?></span>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            Sublet Sales
                            <span style="float: right"><?= asDollars($sublet); ?></span>
                        </td>
                        <td>
                            Sublet Profit | % of GP
                            <span style="float: right"><?= asDollars($subletprofit) . " | " . $subletgpp; ?></span>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            Fees
                            <span style="float: right"><?= asDollars($totalfees); ?></span>
                        </td>
                        <td>
                            Fees Profit | % of GP	
                            <span style="float: right"><?= asDollars($totalfees) . " | " . $feesgpp; ?></span>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            Discount (Parts/Labor/Total)
                            <span style="float: right"><?= asDollars($tpartsdiscount) . " / " . asDollars($tlabordiscount) . " / " . asDollars($tlabordiscount + $tpartsdiscount); ?></span>
                        </td>
                        <td>
                            Total Labor Hours
                            <span style="float: right"><?= round($totallaborhours, 2); ?></span>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            Subtotal
                            <span style="float: right"><?= asDollars($subtotal); ?></span>
                        </td>
                        <td>
                            Gross Profit Per Labor Hour
                            <span style="float: right"><?= asDollars($gpperlh); ?></span>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            Tax
                            <span style="float: right"><?= asDollars($tax); ?></span>
                        </td>
                        <td>
                            Avg Hours per RO
                            <span style="float: right"><?= number_format($avghoursperro, 2); ?></span>
                        </td>
                        
                    </tr>

                    <tr>
                        <td>
                            <b>
                                Total RO Sales
                                <span style="float: right"><?= asDollars($totalro); ?></span>
                            </b>
                        </td>
                        <td>
                            Effective Labor Rate
                            <span style="float: right"><?= asDollars($effectivelaborrate); ?></span>
                        </td>
                    </tr>
            <?php

            $pstotal = 0;
            $pscost = 0;
            $psdisc = 0;
            $pssubtotal = 0;
            $pstax = 0;
            $pscount = 0;

            $stmt = "select count(*) cnt, sum(pcost) as mcost, sum(discount) as pd, sum(subtotal) as subt, sum(tax) as st, sum(total) as ttl from ps where shopid = '$shopid' and status = 'Closed' and statusdate >= '$sdate' and statusdate <= '$edate'";

            if ($query = $conn->prepare($stmt)) {

                $query->execute();
                $query->bind_result($pscount, $pscost, $psdisc, $pssubtotal, $pstax, $pstotal);
                $query->fetch();
                $query->close();

            }

            // we need to get the psdetail parts cost
            // now get total parts cost
            $pspartcost = 0;

            $pcstmt = "select psid from ps where shopid = ? and status = 'Closed' and statusdate >= ? and statusdate <= ?";
            //set pcrs = con.execute(pcstmt)

            if ($query = $conn->prepare($pcstmt)) {
                $query->bind_param("sss", $shopid, $sdate, $edate);
                $query->execute();
                $pcrsresult = $query->get_result();
            } else {
                echo "pcrs Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            if ($pcrsresult->num_rows > 0) {
                while ($pcrs = $pcrsresult->fetch_array()) {
                    $psid = $pcrs["psid"];
                    $psdstmt = "select (cost*qty) as extcost from psdetail where shopid = ? and psid = ? ";

                    //set psdrs = con.execute(psdstmt)
                    if ($query = $conn->prepare($psdstmt)) {
                        $query->bind_param("si", $shopid, $psid);
                        $query->execute();
                        $psdsresult = $query->get_result();
                    } else {
                        echo "psds Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    if ($psdsresult->num_rows > 0) {
                        while ($psdrs = $psdsresult->fetch_array()) {
                            $pspartcost = $pspartcost + $psdrs["extcost"];
                        } // end psdrs while loop
                    } // end if psdrs
                } // end pcrs while loop
            } //end if pcrs


            if ($pscount > 0) {
                $avgps = $pstotal / $pscount;
            } else {
                $avgps = 0;
            }

            // cost is now coming off of psdetail
            //$psgp = number_format($pssubtotal - $pscost,2);
            $psgp = number_format($pssubtotal - $pspartcost, 2);


            //$psgpcalc = $pssubtotal - $pscost;
            $psgpcalc = $pssubtotal - $pspartcost;


            if ($pssubtotal > 0) {
                $psgpp = number_format(100 * ($psgpcalc / $pssubtotal), 0);
            } else {
                $psgpp = 0;
            }
            //$psgpp = number_format(100 * ($psgpcalc / $pssubtotal),0);
            if ($pssubtotal != 0) {
                ?>
                        <tr>
                            <td style="color: #a01c1c">Part Sales</td>
                            <td></td>
                        </tr>

                        <tr>
                            <td>
                                # Parts Sales
                                <span style="float: right"><?= $pscount; ?></span>
                            </td>
                            <td>
                                Gross Profit | %
                                <span style="float: right"><?= asDollars($psgp) . " | " . $psgpp . "%"; ?></span>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                Parts Cost
                                <span style="float: right"><?= asDollars($pspartcost); ?></span>
                            </td>
                            <td>
                                Avg per Part Sale
                                <span style="float: right"><?= asDollars($avgps); ?></span>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                Parts Sales
                                <span style="float: right"><?= asDollars($pstotal - $pstax); ?></span>
                            </td>
                            <td></td>
                        </tr>

                        <tr>
                            <td>
                                Discount
                                <span style="float: right"><?= asDollars($psdisc); ?></span>
                            </td>
                            <td></td>
                        </tr>

                        <tr>
                            <td>
                            Subtotal
                                <span style="float: right"><?= asDollars($pssubtotal); ?></span>
                            </td>
                            <td></td>
                        </tr>

                        <tr>
                            <td>
                                Tax
                                <span style="float: right"><?= asDollars($pstax); ?></span>
                            </td>
                            <td></td>
                        </tr>

                        <tr>
                            <td>
                                <b>
                                    Total Parts Sales
                                    <span style="float: right"><?= asDollars($pstotal); ?></span>
                                </b>
                            </td>
                            <td></td>
                        </tr>
                    <?php
                }
                ?>
                </tbody>
            </table>
        </section>
    </div>
</main>
<?php
include getScriptsGlobal($component);
//include getScriptsComponent($component);
include getFooterComponent($component);
?>
