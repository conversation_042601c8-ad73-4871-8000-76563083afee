<?php
$component = "reports-v2";

include getRulesGlobal($component);
include getHeadGlobal($component);
// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$intervalIs = (isset($_GET['interval']) ? (int)($_GET['interval']) : 6);


// Page Variables
$title = 'Customer Attrition Report';  // Report Title Goes Here

$subtitle = "This Report Shows Customers Who Have Not Returned In The Last " . $intervalIs . " Months";  // Report SubTitle Goes Here - Comment out if not needed
$template = COMPONENTS_PRIVATE . "/reports/templates/excelexport.php"; //Only change if a custom PHPExcel is created in the template folder
?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="title col breadcrumb d-flex align-items-center">
                <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                    class="text-secondary ps-3 pe-3">/</span>
                <h2><?= $title; ?></h2>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report">
        <?php
            include_once "inline_prompt.php";
        ?>
        <table class="dtable" style="width: 100%">
            <thead>
            <tr class="table_header table_head">
                <th>Customer</th>
                <th>Vehicle</th>
                <th>Address</th>
                <th>City, State, ZIP</th>
                <th>Home #</th>
                <th>Work #</th>
                <th>Cell #</th>
                <th>Email</th>
                <th style="text-align:right">Date Last In</th>
            </tr>
            </thead>
            <tbody>
            <?php
            $tablefields = array('Customer', 'Vehicle', 'Address', 'City', 'State', 'Zip', 'Home #', 'Work #', 'Cell #', 'Email', 'Date Last In');//set table headings array for excel export
            $alldata = array();//this will hold all the data arrays to be exported to excel

            // Insert DB Query Here

            // Template Query Begins - Replace entire section
            $stmt = "SELECT `customer`,vehinfo,customeraddress,customercity,customerstate,customerzip,customerphone,`email`,cellphone,customerwork,max(datein) as datein from repairorders where shopid = ? and status = 'closed' and rotype != 'No Approval' group BY `customerid` having max(datein) <= DATE_SUB(curdate(), INTERVAL '$intervalIs' MONTH) ORDER BY datein asc";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("s", $shopid);
                $query->execute();
                $roresult = $query->get_result();
            } else {
                echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }
            $numrows = $roresult->num_rows;

            while ($ro = $roresult->fetch_array()) {
                $statdate = date('m/d/Y', strtotime($ro["datein"]));
// Template Query Ends

                ?>

                <!-- Table Results Begin -->
                <tr class="table_data">
                    <td><?= $ro["customer"]; ?></td>
                    <td><?= $ro["vehinfo"]; ?></td>
                    <td><?= $ro["customeraddress"]; ?></td>
                    <td><?= $ro["customercity"] . ", " . $ro["customerstate"] . " " . $ro["customerzip"]; ?></td>
                    <td><?= formatPhone($ro["customerphone"]); ?></td>
                    <td><?= formatPhone($ro["customerwork"]); ?></td>
                    <td><?= formatPhone($ro["cellphone"]); ?></td>
                    <td><?= $ro["email"]; ?></td>
                    <td style="text-align:right"><?= $statdate; ?></td>
                </tr>

                <?php
                $alldata[] = array(strtoupper($ro['customer']), strtoupper($ro['vehinfo']), strtoupper($ro['customeraddress']), strtoupper($ro['customercity']), strtoupper($ro['customerstate']), strtoupper($ro['customerzip']), formatPhone($ro['customerphone']), formatPhone($ro['customerwork']), formatPhone($ro['cellphone']), strtoupper($ro["email"]), $statdate); //fill up the alldata array with the arrays of data to be shown in excel export
            } // end of while for loop
            // end if for end of file
            $alldata[] = array('', '', '', '', '', '', '', '', '');
            $alldata[] = array('TOTALS', $numrows, '', '', '', '', '', '', '');

            ?>
            </tbody>
            <tfoot>
            <tr class="table_total">
                <td><b>Totals</b></td>
                <td><b><?= $numrows; ?></b></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            </tfoot>
        </table>
    </div>
</main>
<?php

require COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form.php";

include getScriptsGlobal($component);
include getFooterComponent($component);
?>
