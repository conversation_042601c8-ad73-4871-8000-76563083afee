<?php
$component = "reports-v2";
//To view this report with Finalized and Closed RO's, click here
//Click an RO number to view the RO
include getHeadGlobal($component);
include getRulesGlobal($component);

$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));
$path = $_SERVER['HTTP_HOST'];

// Page Variables
$title = 'Tech Paid Log Report';  // Report Title Goes Here

$subtitle = '';  // Report SubTitle Goes Here - Hide if not needed
// Use this for Gen Pop Reports
// $template = 'excelexport.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
$template = COMPONENTS_PRIVATE . "/reports/templates/excelexport_tech.php"; //Only change if a custom PHPExcel is created in the template folder
?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);
?>
    <main id="reports">
        <div class="report">
            <div class="col-12">
                <div class="title col breadcrumb d-flex align-items-center">
                    <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                        class="text-secondary ps-3 pe-3">/</span>
                    <h2><?= $title; ?></h2>
                </div>
                <hr/>
                <p class="card-title">
                    <?php
                    if ((!empty($sd))) {
                        echo($sd);
                    }
                    if (!empty($sd) && !empty($ed)) {
                        echo ' to ';
                    }
                    if (!empty($ed)) {
                        echo $ed;
                    }
                    if (empty($sd) && empty($ed)) {
                        echo date('m/d/Y');
                    }
                    ?>
                </p>
            </div>
        </div>
        <div class="report">
            <?php
            include "inline_prompt.php";
            ?>
            <table class="tech_report w-100" id="tech_report" style="width: 100%">
                <thead>
                <tr class="table_header table_head">
                    <th></th>
                    <th>RO #</th>
                    <th>Labor Description</th>
                    <th>Time Paid</th>
                    <th>Tech</th>
                    <th class="text-right">Pay Rate</th>
                    <th class="text-right">Labor Hours</th>
                    <th class="calc text-tight">Paid Hours</th>
                    <th class="calc text-right">Unpaid Hours</th>
                </tr>
                </thead>
                <tbody>

                <?php
                $tablefields = array('RO#', 'Labor Description', 'Time Paid', 'Tech', 'Pay Rate', 'Labor Hours', 'Paid Hours', 'Unpaid Hours');//set table headings array for excel export
                $alldata = array();//this will hold all the data arrays to be exported to excel

                $stmt = "select distinct tech from techpaidlog where shopid = ? AND date(timestamp) >= ? AND date(timestamp) <= ?";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("sss", $shopid, $sd1, $ed1);
                    $query->execute();
                    $lbrresult = $query->get_result();
                } else {
                    "Labor Prepare failed (" . $conn->errorno . ")" . $conn->error;
                }

                if ($lbrresult->num_rows > 0) {
                    while ($lbr = $lbrresult->fetch_array()) {

                        $totalpaidhrs = 0;
                        $totalunpaidhrs = 0;
                        $tech = $lbr["tech"];

                        $tar = explode(",", $tech);
                        $techlast = trim($tar[0], " ");
                        if (!empty($tar[1])) {
                            $techfirst = trim($tar[1], " ");
                        } else {
                            $techfirst = "";
                        }

                        $tech2 = $techlast . ',' . $techfirst;

                        $stmt = "SELECT t.laborid,sum(t.hours) as hrs,t.roid,l.laborhours,l.labor,t.timestamp from labor l,techpaidlog t where l.shopid=t.shopid and l.laborid=t.laborid and t.shopid = ? and t.tech=? AND date(t.timestamp) >= ? AND date(t.timestamp) <= ? group by t.laborid";

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("ssss", $shopid, $tech, $sd1, $ed1);
                            $query->execute();
                            $roresult = $query->get_result();
                        } else {
                            "RO Prepare failed (" . $conn->errorno . ")" . $conn->error;
                        }

                        if ($roresult->num_rows > 0) {
                            while ($ro = $roresult->fetch_array()) {

                                $laborid = $ro['laborid'];
                                $roid = $ro['roid'];

                                $stmt = "SELECT hourlyrate ";
                                $stmt .= " FROM employees ";
                                $stmt .= " WHERE shopid = ?";
                                $stmt .= "   AND (concat(employeelast,', ',employeefirst) = ? || concat(employeelast,employeefirst) = ?) and active='yes'";
                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("sss", $shopid, $tech, $tech2);
                                    $query->execute();
                                    $empresult = $query->get_result();

                                } else {
                                    echo "Employee prepare failed  (" . $conn->errno . ")" . $conn->error;
                                }

                                $emp = mysqli_fetch_assoc($empresult);

                                $payrate = $emp["hourlyrate"];

                                $paidhours = round($ro['hrs'], 2);

                                $unpaidhours = round($ro['laborhours'] - $paidhours, 2);

                                $totalpaidhrs += $paidhours;

                                $totalunpaidhrs += $unpaidhours;

                                ?>

                                <!-- Table Results Begin -->
                                <tr class="table_data">
                                    <td><?= ucwords(strtolower($tech)); ?></td>
                                    <td><?= $roid; ?></td>
                                    <td><?= ucwords(strtolower($ro["labor"])); ?></td>
                                    <td><?= date("m/d/Y H:i", strtotime($ro['timestamp'])) ?></td>
                                    <td><?= ucwords(strtolower($tech)); ?></td>
                                    <td class="text-right"><?= asDollars($payrate); ?></td>
                                    <td class="text-right"><?= $ro['laborhours'] ?></td>
                                    <td class="text-right"><?= $paidhours ?></td>
                                    <td class="text-right"><?= $unpaidhours ?></td>
                                </tr>

                                <?php
                                $alldata[] = array($roid, strtoupper($ro["labor"]), date("m/d/Y H:i", strtotime($ro['timestamp'])), strtoupper($tech), asDollars($payrate), $ro['laborhours'], $paidhours, $unpaidhours); //fill up the alldata array with the arrays of data to be shown in excel export
                            } // end of emp if
                        } // end of ro while loop
                        ?>
                        <!--
                        <tr class="table_total">
                            <td colspan="2"><b>TOTAL for <?= $tech ?></b></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td><b><?= round($totalpaidhrs, 2) ?></b></td>
                            <td><b><?= round($totalunpaidhrs, 2) ?></b></td>
                        </tr>
-->
                        <?php
                        $alldata[] = array('TOTAL', '', '', '', '', '', round($totalpaidhrs, 2), round($totalunpaidhrs, 2));
                        $alldata[] = array('', '', '', '', '', '', '', '', '', '', '', '', '');
                    } // end if for ro

                } // end of labor while for loop


                ?>
                </tbody>
            </table>
        </div>
    </main>
<?php
include(COMPONENTS_PRIVATE_PATH . '/reports-v2/includes/report_form.php');

include getScriptsGlobal($component);


$dateRangeDisplay = ($_REQUEST['sdate'] ?? "") . " " . (!empty($_REQUEST['edate']) ? " to " . $_REQUEST['edate'] : "");
$subtitle = !empty($subtitle) ? $subtitle : "";

?>
    <script>
        $(document).ready(function () {
            //7, 9, 11
            let CalcT = Array();
            //console.log($("#tech_report thead"));
            $("#tech_report thead").find("th").each(function (idx, v) {
                if ($(v).hasClass("calc")) {
                    CalcT.push(idx);
                }
            });

            //console.log(CalcT);

            let floatVal = function (i) {
                return typeof i === 'string' ? Number(i.replace(/[$,]/g, '')) : typeof i === 'number' ? i : 0;
            };

            groupCol_writer = 0;
            let table = $("#tech_report").DataTable({
                responsive: true,
                fixedHeader: {
                    headerOffset: 68
                },
                colReorder: true,
                select: true,
                scrollY: false,
                scrollX: false,
                scroller: false,
                paging: false,
                language: {
                    searchPanes: {
                        emptyPanes: 'No records found in this date range'
                    },
                    search: "_INPUT_",
                    searchPlaceholder: "Search..."
                },
                buttons: [
                    {
                        extend: 'csv',
                        text: '<i class="fas fa-file-csv fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                        messageTop : "<?= $dateRangeDisplay ?>",
                    },
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint." (".$dateRangeDisplay.")" ?>",
                        messageTop : "<?= $subtitle ?>",
                        exportOptions: {
                            // Any other settings used
                            columns: [':visible'],
                            grouped_array_index: 0
                        },
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                        exportOptions: {
                            // Any other settings used
                            stripHtml: false,
                            columns: [':visible'],
                            grouped_array_index: 0
                        },
                        messageTop : "<?= $dateRangeDisplay."<br/>".$subtitle ?>",
                        footer: true,
                    }
                ],
                dom: "<'row'<'col-sm-12 col-md-6 dt_Search text-secondary'f><'col-sm-12 col-md-6 text-right dt_btns text-secondary'B>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
                columnDefs: [{visible: false, targets: groupCol_writer}],
                order: [[groupCol_writer, 'asc']],
                orderFixed: [0, "asc"],
                rowGroup: {
                    endRender: function (rows, group) {

                        let totsArr = Array();

                        CalcT.forEach(function (val, idx) {
                            var col = rows.data().pluck(val);
                            totsArr[val] = col.reduce(function (a, b) {
                                var fl_a = floatVal(a);
                                var fl_b = floatVal(b);
                                if (isNaN(fl_a)) {
                                    fl_a = 0;
                                }
                                if (isNaN(fl_b)) {
                                    fl_b = 0;
                                }
                                sum = fl_a + fl_b;
                                return (typeof sum != "undefined" || !isNaN(sum)) ? sum : 0;
                            }, 0);
                        });
                        var tr = rows.data();
                        var numcol = tr[0].length;

                        let formatting_options = {
                            style: 'currency',
                            currency: 'USD',
                            minimumFractionDigits: 2,
                        };
                        let USDollar = new Intl.NumberFormat('en-US', formatting_options);
                        var gname = group.replace(/[^A-Z0-9]/ig, "_");
                        // console.log('numcol', numcol);
                        if (typeof numcol !== "undefined" && numcol > 0) {
                            //   console.log();
                            var total_row = $('<tr id="' + gname + '_totals">');
                            total_row.append('<td colspan="2" class="text-left">' + group + ' TOTALS</td>')
                            for (var i = 3; i < numcol; i++) {

                                if (typeof totsArr[i] !== "undefined" || totsArr[i] != null) {
                                    var totVal = totsArr[i].toFixed(2);
                                    // console.log(totsArr[i], !isNaN(totsArr[i]));
                                    if (!isNaN(totsArr[i]) && rows.data().pluck(i)[0].indexOf('$') >= 0) {
                                        totVal = USDollar.format(totsArr[i])
                                    }
                                    total_row.append('<td class="text-right">' + totVal + '</td>')
                                    // console.log("Adding total")
                                } else {
                                    total_row.append('<td class=""></td>')
                                }
                            }
                            total_row.append("</tr>");
                            return total_row;
                        }

                    },
                    dataSrc: 0,
                    endClassName: 'table_total text-right',
                    startClassName: 'group_heading'
                },

            });
        });
    </script>
<?php
include getFooterComponent($component);
?>