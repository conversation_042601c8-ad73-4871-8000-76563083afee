<?php

$component = "reports-v2";
// Use this for Gen Pop Reports

include getRulesGlobal($component);
include getHeadGlobal($component);

// Global Variables
$shopid = $_COOKIE['shopid'];
$category = $_GET['category'];
$sortby = $_GET['sortby'];
$salesdata = $_GET['salesdata'];
// Page Variables
$title = 'Inventory Report/Analysis';  // Report Title Goes Here

// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_multiple.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
<body>
<?php

include getHeaderGlobal($component);
include getMenuGlobal('');


   $tablefields=array(
        '',
        'Part #',
        'Bin',
        'Description',
        'Unit Cost',
        'Total Cost',
        'Alloc',
        'OnHand',
        'Reorder',
        'Max',
        '90_Days',
        '12_Mos',
        'Over'
    );
    $alldata=array();
    $partCategory = "";
    $secondRow = false;
    $totalCost = 0;

    $statement="SELECT * FROM partsinventory 
                WHERE shopid = ? 
                ORDER BY PartCategory, $sortby";

    if ($query = $conn->prepare($statement)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $result = $query->get_result();

        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $astmt = "SELECT COALESCE(SUM(p.quantity),0) FROM parts p, repairorders r WHERE p.shopid=r.shopid AND p.roid=r.roid AND r.shopid=? AND r.status != 'CLOSED' and r.ROType != 'No Approval' AND p.partnumber=?";
                if ($aquery = $conn->prepare($astmt))
                {
                    $aquery->bind_param("ss",$shopid,$prs['PartNumber']);
                    $aquery->execute();
                    $aquery->bind_result($allocated);
                    $aquery->fetch();
                    $aquery->close();
                }

                $ninetydays = $twelvemos = '';
                if($salesdata=='yes'){
                    $date = date('Y-m-d');
                    $day90 = date('Y-m-d',strtotime('-90 days'));
                    $mth12 = date('Y-m-d',strtotime('-12 months'));

                    $astmt = "select count(*) from parts where deleted = 'no' and shopid = ? and `date` <= '$date' and `date` >= '$day90' and PartNumber = ?";
                    if ($aquery = $conn->prepare($astmt))
                    {
                        $aquery->bind_param("ss",$shopid,$prs['PartNumber']);
                        $aquery->execute();
                        $aquery->bind_result($ninetydays);
                        $aquery->fetch();
                        $aquery->close();
                    }

                    $astmt = "select count(*) from parts where deleted = 'no' and shopid = ? and `date` <= '$date' and `date` >= '$mth12' and PartNumber = ?";
                    if ($aquery = $conn->prepare($astmt))
                    {
                        $aquery->bind_param("ss",$shopid,$prs['PartNumber']);
                        $aquery->execute();
                        $aquery->bind_result($twelvemos);
                        $aquery->fetch();
                        $aquery->close();
                    }
                }

                if($row['NetOnHand'] > $row['MaxOnHand']){
                    $over = $row['NetOnHand'] - $row['MaxOnHand'];
                }
                else $over = '';

               
                $partCategory = $row['PartCategory'];
                $secondRow = true;
                $totalCost += $row['PartCost'] * $row['NetOnHand'];

                $alldata[]=array(
                    strtoupper($row['PartCategory']),
                    strtoupper($row['PartNumber']),
                    strtoupper($row['Bin']),
                    strtoupper($row['PartDesc']),
                    asDollars($row['PartCost']),
                    asDollars($row['PartCost'] * $row['NetOnHand']),
                    $allocated,
                    $row['NetOnHand'],
                    $row['ReOrderLevel'],
                    $row['MaxOnHand'],
                    $ninetydays,
                    $twelvemos,
                    $over
                );
            }

            // $alldata[]=array(
            //     "",
            //     "Grand Totals",
            //     "",
            //     "",
            //     asDollars($totalCost),
            //     "",
            //     "",
            //     "",
            //     "",
            //     "",
            //     "",
            //     "",
            //     ""
            // );
        }
        $query->close();
    } else {
        echo "Data Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="row">
                    <div class="col-sm-12 col-md-10">
            <div class="title col breadcrumb d-flex align-items-center">
                <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                    class="text-secondary ps-3 pe-3">/</span>
                <h2><?= $title; ?></h2>
                    </div>
                </div>

                    <div class="col-md-2">
                        <?php
                        if ($_COOKIE['empid'] == 'Admin') {
                            ?>
                            <button type="button" class="btn btn-secondary btn-md d-print-none float-end"
                                    onclick="location.href='<?= COMPONENTS_PRIVATE ?>/v2/report_builder/builder.php?preset=parts_inv&op=create_new'">Edit This Report
                            </button>
                            <?php
                        }
                        ?>
                    </div>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report">
        <?php
            include_once "inline_prompt.php";
        ?>
    </div>
    <div class="d-flex">
        <section class="container-fluid" id="">
            <table class="inv_report" id="inv_report" style="width: 100%">
                <thead>
                    <tr class="table_header table_head">
                        <td></td>
                        <th>Part #</th>
                        <th>Bin</th>
                        <th>Description</th>
                        <th>Unit Cost</th>
                        <th class="calc">Total Cost</th>
                        <th>Alloc</th>
                        <th>OnHand</th>
                        <th>Reorder</th>
                        <th>Max</th>
                        <th>90_Days</th>
                        <th>12_Mos</th>
                        <th>Over</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($alldata as $row): ?>
                        <?php $isBold = strpos($row[0], 'TOTAL') !== false; ?>
                        <?php $isGrandTotals = strpos($row[1], 'Grand Totals') !== false; ?>
                        <tr<?= $isBold ? ' style="font-weight:bold;"' : ($isGrandTotals ? ' class="table_totals"' : '') ?>>
                            <?php foreach ($row as $cell): ?>
                                <td><?= $cell; ?></td>
                            <?php endforeach; ?>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr class="table_totals">
                        <td colspan="4"><strong>Grand Totals</strong></td>
                        <td><strong><?= asDollars($totalCost) ?></strong></td>
                        <td colspan="8"></td>
                    </tr>
                </tfoot>
            </table>
        </section>
    </div>
</main>

<?php
include getScriptsGlobal($component);
//include getScriptsComponent($component);


$dateRangeDisplay = ($_REQUEST['sdate'] ?? "") . " " . (!empty($_REQUEST['edate']) ? " to " . $_REQUEST['edate'] : "");
$subtitle = !empty($subtitle) ? $subtitle : "";

?>
<script>
    $(document).ready(function () {
        //let CalcT = Array(4, 5, 6, 7, 8, 9, 11, 13, 14);

        var CalcT = Array();

        $("#inv_report thead").find("th").each(function (idx, v) {
            if ($(v).hasClass("calc")) {
                CalcT.push(idx);
            }
        });

        let floatVal = function (i) {
            return typeof i === 'string' ? Number(i.replace(/[$,]/g, '')) : typeof i === 'number' ? i : 0;
        };

        groupCol_writer = 0;
        let table = $("#inv_report").DataTable({
            responsive: true,
            fixedHeader: {
                headerOffset: 68
            },
            colReorder: true,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            language: {
                searchPanes: {
                    emptyPanes: 'No records found in this date range'
                },
                search: "_INPUT_",
                searchPlaceholder: "Search..."
            },
            buttons: [
                {
                    extend: 'csv',
                    text: '<i class="fas fa-file-csv fa-xl"></i>',
                    title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                    messageTop : "<?= $dateRangeDisplay ?>",
                },
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel fa-xl"></i>',
                    title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint." (".$dateRangeDisplay.")" ?>",
                    messageTop : "<?= $subtitle ?>",
                    exportOptions: {
                        // Any other settings used
                        columns: [':visible'],
                        grouped_array_index: 0
                    },
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print fa-xl"></i>',
                    title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                    exportOptions: {
                        // Any other settings used
                        stripHtml: false,
                        columns: [':visible'],
                        grouped_array_index: 0
                    },
                    messageTop : "<?= $dateRangeDisplay."<br/>".$subtitle ?>",
                    footer: true,
                }
            ],
            dom: "<'row'<'col-sm-12 col-md-6 dt_Search text-secondary'f><'col-sm-12 col-md-6 dt_btns text-secondary'B>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
            columnDefs: [{visible: false, targets: groupCol_writer}],
            order: [[groupCol_writer, 'asc']],
            orderFixed: [0, 'desc'],
            rowGroup: {
                endRender: function (rows, group) {
                    if (group == "Grand Totals") {
                        return;
                    }
                    let totsArr = Array();

                    CalcT.forEach(function (val, idx) {
                        var col = rows.data().pluck(val);
                        totsArr[val] = col.reduce(function (a, b) {
                            var fl_a = floatVal(a);
                            var fl_b = floatVal(b);
                            if (isNaN(fl_a)) {
                                fl_a = 0;
                            }
                            if (isNaN(fl_b)) {
                                fl_b = 0;
                            }
                            sum = fl_a + fl_b;
                            return (typeof sum != "undefined" || !isNaN(sum)) ? sum : 0;
                        }, 0);
                    });
                    var tr = rows.data();
                    var numcol = tr[0].length;

                    let formatting_options = {
                        style: 'currency',
                        currency: 'USD',
                        minimumFractionDigits: 2,
                    };
                    let USDollar = new Intl.NumberFormat('en-US', formatting_options);
                    var gname = group.replace(" ", "_").replace(",", "").replace(/^a-zA-Z0-9]/g, '').replace('%', '');
                    // console.log('numcol', numcol);
                    if (typeof numcol !== "undefined" && numcol > 0) {
                        //   console.log();
                        var total_row = $('<tr id="' + gname + '_totals">');
                        total_row.append('<td colspan="2" class="text-left">' + group + ' Total</td>')
                        for (var i = 3; i < numcol; i++) {

                            if (typeof totsArr[i] !== "undefined" || totsArr[i] != null) {
                                var totVal = totsArr[i].toFixed(0);
                                // console.log(totsArr[i], !isNaN(totsArr[i]));
                                //     if (!isNaN(totsArr[i]) && rows.data().pluck(i)[0].indexOf('$') >= 0) {
                                totVal = USDollar.format(totsArr[i])
                                //     }
                                total_row.append('<td>' + totVal + '</td>')
                                // console.log("Adding total")
                            } else {
                                total_row.append('<td class=""></td>')
                            }
                        }
                        total_row.append("</tr>");
                        return total_row;
                    }

                },
                dataSrc: 0,
                endClassName: 'table_total',
                startClassName: 'group_heading'
            },

        });

    });
</script>
<?php
include getFooterComponent($component);
?>

?>
