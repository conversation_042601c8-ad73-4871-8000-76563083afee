<?php
$component = "reports-v2";
//To view this report with Finalized and Closed RO's, click here
//Click an RO number to view the RO
include getHeadGlobal($component);
include getRulesGlobal($component);
$title = 'Fee Report';  // Report Title Goes Here

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sdate']) ? filter_var($_GET['sdate'], FILTER_SANITIZE_STRING) : '';
$ed = isset($_GET['sdate']) ? filter_var($_GET['edate'], FILTER_SANITIZE_STRING) : '';
$feename = isset($_GET['feename']) ? filter_var($_GET['feename'], FILTER_SANITIZE_STRING) : 'all';

$sdate = date('Y-m-d', strtotime($sd));
$edate = date('Y-m-d', strtotime($ed));

$path = $_SERVER['HTTP_HOST'];

$subtitle = "Fee - " . strtoupper($feename);
// Page Variable
//$subtitle = 'my subtitle';  // Report SubTitle Goes Here - Comment out if not needed
$template = COMPONENTS_PRIVATE . "/reports/templates/excelexport_multiple.php"; //Only change if a custom PHPExcel is created in the template folder
?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);
?>
<main id="reports" class="min-vh-100">
    <div class="report">
        <div class="col-12">
            <div class="title col breadcrumb d-flex align-items-center">
                <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                        class="text-secondary ps-3 pe-3">/</span>
                <h2><?= $title; ?></h2>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?>
            </p>
        </div>
    </div>
    <div class="report">
        <?php
        include_once "inline_prompt.php";
        ?>
        <table class="w-100 datatable-custom" id="partfee_report" style="width: 100%">
            <thead>
            <tr class="">
                <th>RO #</th>
                <th>Fee</th>
                <th class="text-end">Fee Amount</th>
                <th class="text-end">Qty Of Fee Sold</th>
                <th class="text-end">Total Of Fee On RO</th>
                <th class="text-end">Total RO</th>
            </tr>
            </thead>
            <tbody>
            <?php
            $alldata = array();
            $alldata[] = array('TABLEHEAD', "RO #", "Customer", "Fee Label", "Fee Amount", "Total RO");
            if ($feename == "all") {
                $row2colArr = array('userfee1', 'userfee2', 'userfee3', 'storagefee', 'hazardouswaste');
            } else {
                $row2colArr = array($feename);
            }
            $ttlArr = array();
            $ttlro = 0;

            $stmt = "select roid, totalro, totalfees FROM repairorders WHERE shopid = ? AND statusdate >= ? AND statusdate <= ? AND Status = 'Closed' AND ROType != 'No Approval'";

            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sss", $shopid, $sdate, $edate);
                $query->execute();
                $results = $query->get_result();
                $query->close();
            }
            if ($results->num_rows > 0) {
            while ($rs = $results->fetch_assoc()) {
                $feeamt = 0;
                $roid = $rs['roid'];
                $skipflag = false;
                if ($feename == "all") {
                    $pstmt = "SELECT UCASE(PartDesc) as fee_label, PartPrice as fee_amount, quantity FROM parts WHERE shopid = ? AND ROID = ? and deleted = 'no' AND (PartNumber = 'PARTFEE' OR PartCategory = 'PARTFEE') UNION SELECT UCASE(feename) as fee_label, SUM(feeamount) as fee_amount, '1' as quantity FROM rofees WHERE shopid = ? AND ROID = ? group by feename";
                } else {
                    $pstmt = "SELECT UCASE(PartDesc) as fee_label, PartPrice as fee_amount, quantity FROM parts WHERE shopid = ? AND ROID = ? and deleted = 'no' AND PartDesc = '$feename' UNION SELECT UCASE(feename) as fee_label, SUM(feeamount) as fee_amount, '1' as quantity FROM rofees WHERE shopid = ? AND ROID = ? and feename = '$feename' group by feename";
                }
                if ($pquery = $conn->prepare($pstmt)) {
                    $pquery->bind_param("sisi", $shopid, $roid, $shopid, $roid);
                    $pquery->execute();
                    $presults = $pquery->get_result();
                    if ($presults->num_rows > 0) {
                        $skipflag = false;
                        while ($prs = $presults->fetch_assoc()) {
                            $totalfee = $prs['fee_amount'] * $prs['quantity'];
                            ?>
                            <tr class="">
                                <td><?= $roid ?></td>
                                <td><?= $prs['fee_label'] ?></td>
                                <td style="text-align:right"><?= asDollars($prs['fee_amount']) ?></td>
                                <td style="text-align:right"><?= $prs['quantity'] ?></td>
                                <td style="text-align:right"><?= asDollars($totalfee) ?></td>
                                <td style="text-align:right"><?= asDollars($rs['totalro']) ?></td>
                            </tr>
                            <?php

                            if (isset($ttlArr[$prs['fee_label']])) {
                                $ttlArr[$prs['fee_label']] = array(
                                    'amount' => $ttlArr[$prs['fee_label']]['amount'] + $prs['fee_amount'],
                                    'quantity' => $ttlArr[$prs['fee_label']]['quantity'] + $prs['quantity'],
                                    'totalfee' => $ttlArr[$prs['fee_label']]['totalfee'] + $totalfee
                                );
                            } else {
                                $ttlArr[$prs['fee_label']] = array(
                                    'amount' => $prs['fee_amount'],
                                    'quantity' => $prs['quantity'],
                                    'totalfee' => $totalfee
                                );
                            }

                            $alldata[] = array($rs['roid'], $rs['customer'], $prs['fee_label'], asDollars($prs['fee_amount']), asDollars($rs['totalro']));
                        }
                        $ttlro += $rs['totalro'];
                    }
                }
            }
            ?>
            <!--
            <tr>
                <td colspan="6" class="border-bottom"></td>
                <td class="d-none"></td>
                <td class="d-none"></td>
                <td class="d-none"></td>
                <td class="d-none"></td>
                <td class="d-none"></td>
            </tr>
            -->

            <!--
            <tr>
                <td colspan="5"></td>
            </tr>
            <tr class="table_header">
                <td colspan="2">Totals</td>
                <td class="text-end">Total Fee Amt</td>
                <td class="text-end">Qty Total Sold</td>
                <td class="text-end">Total Amount Of Fee</td>
                <td class="text-end">Total RO Amount</td>
            </tr>
            -->
            <?php
            $alldata[] = array("", "", "", "", "");
            $alldata[] = array("", "", "", "", "");
            $alldata[] = array('TABLEHEAD', "Totals", "Total Fee Amt", "Qty Total Sold", "Total Amount of Fee", "Total RO Amount");
            $gttlamt = $gttlfee = 0;
            foreach ($ttlArr as $key => $val) {
                ?>
                <tr class="">
                    <td colspan="2"><?= $key ?></td>
                    <td class="d-none"></td>
                    <td class="d-none"></td>
                    <td class="text-end"><?= asDollars($val['amount']) ?></td>
                    <td class="text-end"><?= $val['quantity'] ?></td>
                    <td class="text-end" style=""><?= asDollars($val['totalfee']) ?></td>
                    <td class="text-end" style=""></td>
                </tr>
                <?php
                $alldata[] = array($key, asDollars($val['amount']), $val['quantity'], asDollars($val['totalfee']), '');
                $gttlamt += $val['amount'];
                $gttlfee += $val['totalfee'];
            }
            ?>

            </tbody>
            <tfoot>
            <tr class="table_total">
                <td colspan="">TOTALS</td>
                <td class=""></td>
                <td class="text-end"><?= asDollars($gttlamt) ?></td>
                <td class="text-end"></td>
                <td class="text-end" style=""><?= asDollars($gttlfee) ?></td>
                <td class="text-end" style=""><?= asDollars($ttlro) ?></td>
            </tr>
            </tfoot>
            <?php
            $alldata[] = array('TOTALS', asDollars($gttlamt), '', asDollars($gttlfee), $ttlro);
            }
            ?>
        </table>
    </div>
</main>

<?php
include(COMPONENTS_PRIVATE_PATH . '/reports/includes/report_form.php');
include getScriptsGlobal($component);


$dateRangeDisplay = ($_REQUEST['sdate'] ?? "") . " " . (!empty($_REQUEST['edate']) ? " to " . $_REQUEST['edate'] : "");
$subtitle = !empty($subtitle) ? $subtitle : "";

?>
<script>

    $(document).ready(function () {


        let table = $('#partfee_report').DataTable({
            responsive: true,
            fixedHeader: {
                headerOffset: 68
            },
            colReorder: true,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            order: [],
            language: {
                searchPanes: {
                    emptyPanes: 'No records found in this date range'
                },
                search: "_INPUT_",
                searchPlaceholder: "Search..."
            },
            buttons: [
                {
                    extend: 'csv',
                    text: '<i class="fas fa-file-csv fa-xl"></i>',
                    title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                    messageTop : "<?= $dateRangeDisplay ?>",
                    footer: true,
                },
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel fa-xl"></i>',
                    title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint." (".$dateRangeDisplay.")" ?>",
                    messageTop : "<?= $subtitle ?>",
                    exportOptions: {
                        // Any other settings used
                        columns: [':visible'],
                    },
                    footer: true,
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print fa-xl"></i>',
                    title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                    exportOptions: {
                        // Any other settings used
                        stripHtml: false,
                        columns: [':visible'],
                    },
                    messageTop : "<?= $dateRangeDisplay."<br/>".$subtitle ?>",
                    footer: true,
                }
            ],
            dom: "<'row'<'col-sm-12 col-md-6 dt_Search text-secondary'f><'col-sm-12 col-md-6 text-right dt_btns text-secondary'B>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
            "drawCallback": function () {

                let api = this.api();

                let floatVal = function (i) {
                    return typeof i === 'string' ? Number(i.replace(/[$,]/g, '')) : typeof i === 'number' ? i : 0;
                };
                // Calculate totals for columns with .calc_total class
                $('.calc_total', api.table().header()).each(function () {
                    let columnIdx = api.column($(this)).index();
                    let total = api
                        .column(columnIdx, {search: 'applied'})
                        .data()
                        .reduce(function (a, b) {
                            return floatVal(a) + floatVal(b);
                        }, 0);

                    let formatting_options = {
                        style: 'currency',
                        currency: 'USD',
                        minimumFractionDigits: 2,
                    };
                    let USDollar = new Intl.NumberFormat('en-US', formatting_options);

                    $(api.column(columnIdx).footer()).html('<b>' + USDollar.format(total) + '<b>');

                });
                // Calculate totals for columns with .calc_total class
                $('.calc_qty', api.table().header()).each(function () {
                    let columnIdx = api.column($(this)).index();
                    let total = api
                        .column(columnIdx, {search: 'applied'})
                        .data()
                        .reduce(function (a, b) {
                            return floatVal(a) + floatVal(b);
                        }, 0);
                    if ($(this).hasClass('dt_int')) {
                        total = total.toFixed(0);
                    } else {
                        total = total.toFixed(2);
                    }
                    $(api.column(columnIdx).footer()).html('<b>' + (total) + '<b>');

                });

            }
        });

    });
</script>
</body>
<?php
if (isset($conn)) {
    mysqli_close($conn);
}
?>
</html>
