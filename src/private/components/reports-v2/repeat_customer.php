<?php
$component = "reports-v2";

include getHeadGlobal($component);
include getRulesGlobal($component);
include getHeaderGlobal($component);
include getMenuGlobal($component);

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));

$ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
$roTypeSQL = "AND rotype != 'No Approval'";
if ($ro_type != "all") {
    $roTypeSQL = "AND rotype = '" . $ro_type . "'";
}

// Page Variables
$title = 'Repeat Customer Report - ' . ucwords(strtolower($ro_type)); // Report Title Goes Here
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_autosize.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="title col breadcrumb d-flex align-items-center">
                <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                    class="text-secondary ps-3 pe-3">/</span>
                <h2><?= $title; ?></h2>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report">
        <?php
            include_once "inline_prompt.php";
        ?>
        <table class="dtable" style="width: 100%">
            <thead>
            <tr class="table_header table_head">
                <th>Customer</th>
                <th>Phones</th>
                <th>Email</th>
                <th class="calc_qty">Count</th>
                <th class="calc_total" style="text-align:right">Total RO</th>
                <th style="text-align:right">Last RO Date</th>
            </tr>
            </thead>
            <tbody>

            <?php
            $tablefields = array('Customer', 'Home Phone', 'Work Phone', 'Cell Phone', 'Email', 'Count', 'Total RO', 'Last RO Date');//set table headings array for excel export
            $alldata = array();//this will hold all the data arrays to be exported to excel

            // Insert DB Query Here

            // Template Query Begins - Replace entire section
            $tc = 0;
            $gtro = 0;
            $stmt = "select count(*) as c, sum(totalro) as tro, `customer`, customerid,max(statusdate) as statusdate,customerphone,customerwork,cellphone,email from repairorders where shopid = ? and `status` = 'closed' $roTypeSQL and datein >= ? and datein <= ? group by `customer` order by count(*) desc,customer";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sss", $shopid, $sd1, $ed1);
                $query->execute();
                $roresult = $query->get_result();
            } else {
                echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            while ($ro = $roresult->fetch_array()) {
                $tro = $ro["tro"];
                $c = $ro["c"];
                $cid = $ro["customerid"];
                $stdate = date('m/d/Y', strtotime($ro['statusdate']));
                $tc += $c;
                $gtro += $tro;

                ?>
                <tr>
                    <td><?= $ro["customer"]; ?></td>
                    <td><?php
                        if (strlen($ro["customerphone"]) > 5) {
                            echo " <b>H:</b>" . formatPhone($ro["customerphone"]);
                        }
                        if (strlen($ro["customerwork"]) > 5) {
                            echo " <b>W:</b>" . formatPhone($ro["customerwork"]);
                        }
                        if (strlen($ro["cellphone"]) > 5) {
                            echo " <b>C:</b>" . formatPhone($ro["cellphone"]);
                        } ?>
                    </td>
                    <td><?= $ro["email"]; ?></td>
                    <td><?= $c; ?></td>
                    <td style="text-align:right"><?= asDollars($tro); ?></td>
                    <td style="text-align:right"><?= $stdate ?></td>
                </tr>
                <?php
                $alldata[] = array(strtoupper($ro["customer"]), formatPhone($ro["customerphone"]), formatPhone($ro["customerwork"]), formatPhone($ro["cellphone"]), strtoupper($ro['email']), $c, asDollars($tro), $stdate);//fill up the alldata array with the arrays of data to be shown in excel export
            } // end of while for loop
            // end if for end of file

            $alldata[] = array('', '', '', '', '', '', '', '');
            $alldata[] = array('TOTALS', '', '', '', '', $tc, asDollars($gtro), '');
            ?>
            </tbody>
            <tfoot>
            <tr class="table_total">
                <td><b>TOTALS</b></td>
                <td><b></b></td>
                <td><b></b></td>
                <td><b><?= $tc; ?></b></td>
                <td style="text-align:right"><b><?= asDollars($gtro); ?></b></td>
                <td style="text-align:right"><b></b></td>
            </tr>
            </tfoot>
        </table>
    </div>
</main>
<?php

require COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form.php";

include getScriptsGlobal($component);
include getFooterComponent($component);
?>
