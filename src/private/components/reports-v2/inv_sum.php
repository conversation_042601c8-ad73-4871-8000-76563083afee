<?php
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;

$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));
$component = "reports-v2";

$title = 'Inventory Summary';
$subtitle = "";
include getHeadGlobal($component);
include getRulesGlobal($component);
include getHeaderGlobal($component);
include getMenuGlobal($component);
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_autosize.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="title col breadcrumb d-flex align-items-center">
                <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                    class="text-secondary ps-3 pe-3">/</span>
                <h2><?= $title; ?></h2>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?>
            </p>
        </div>
    </div>
    <div class="report">
        <table class="dtable">
            <thead>
            <tr class="table_header table_head">
                <th>Part Category</th>
                <th class="calc_total">Inventory Cost</th>
                <th class="calc_qty">Quantity</th>
            </tr>
            </thead>
            <tbody>
            <?php
            $tablefields = array('Part Category', 'Inventory Cost', 'Quantity');//set table headings array for excel export
            $alldata = array();//this will hold all the data arrays to be exported to excel
            // Insert DB Query Here

            // Template Query Begins - Replace entire section
            $gtcost = 0;
            $gtq = 0;
            $stmt = "select distinct category from category where shopid = ? order by category";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("s", $shopid);
                $query->execute();
                $roresult = $query->get_result();
            } else {
                echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }
            while ($ro = $roresult->fetch_array()) {
                $istmt = "select sum(PartCost) as ttlcost, count(*) as pcntr from partsinventory where shopid = ? and PartCategory = ?";
                if ($query = $conn->prepare($istmt)) {
                    $query->bind_param("ss", $shopid, $ro["category"]);
                    $query->execute();
                    $iresult = $query->get_result();
                } else {
                    echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }
                while ($iro = $iresult->fetch_array()) {
                    $gtcost += $iro["ttlcost"];
                    $gtq += $iro["pcntr"];
                    ?>
                    <tr>
                        <td><?= $ro["category"]; ?></td>
                        <td><?= asDollars($iro["ttlcost"]); ?></td>
                        <td><?= $iro["pcntr"]; ?></td>
                    </tr>
                    <?php
                    $alldata[] = array($ro["category"], asDollars($iro["ttlcost"]), $iro["pcntr"]);//fill up the alldata array with the arrays of data to be shown in excel export
                } // end of while for loop
            }// end if for end of file
            $alldata[] = array('', '', '');
            $alldata[] = array('TOTALS', asDollars($gtcost), $gtq);
            ?>
            </tbody>
            <tfoot>
            <tr class="table_total">
                <td><b>TOTAL</b></td>
                <td><b><?= asDollars($gtcost); ?></b></td>
                <td><b><?= $gtq; ?></b></td>
            </tr>
            </tfoot>
        </table>
    </div>
</main>
<?php

require COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form.php";

include getScriptsComponent($component);
include getFooterComponent($component);
?>