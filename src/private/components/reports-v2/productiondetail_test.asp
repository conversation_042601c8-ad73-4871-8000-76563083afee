<%
'1/8/18 corrected RO's Parts Cost..it should be qty * cost as in SPD w/payments'
'11/21/17 added logic to add PartNumber(Discount) to overall discounts. Corrected Parts Cost calc. Should be qty * cost as in SPD w/payments
if request("e") = "y" then	
	Response.ContentType = "application/vnd.ms-excel"
	Response.AddHeader "Content-Disposition", "attachment; filename=productiondetail.xls"
end if
%>

<!-- #include file=adovbs.asp -->
<!-- #include file=conn.asp -->
<%
server.scripttimeout = 900
%>
<html>
<!-- Copyright 2011 - Boss Software Inc. --><head><meta name="robots" content="noindex,nofollow">
<meta http-equiv="Content-Language" content="en-us">
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="ProgId" content="FrontPage.Editor.Document">
<meta name="swp" content="The report gives a detailed look at shop production.">
<meta name="datereq" content="yes">
<title> Shop Production Detail Report: Date Range</title>
<script language="javascript">
	s = screen.width
	h = screen.height
	cw = (s / 2)-150
	ch = (h / 2)-50
	//mywin = window.open("pmess.htm", "what", "left="+cw+", top="+ch+", width=300, height=100, toolbar=no, menubar=no, scrollbar=no")
	//mywin.focus()
	//window.print()
	timerID = setTimeout("closeBack()", 500)
	function closeBack(){
		mywin.close()
		//history.go(-1)
	}
	function printSPR(){
		document.getElementById('buttons').style.display = 'none';
		window.print();
		setTimeout("showButtons()",1000)
	}
	function showButtons(){
		document.getElementById('buttons').style.display = 'block';


	}
</script>
<style type="text/css">
.style5 {
	font-size: x-small;
	font-weight: bold;
}
.style6 {
	font-size: x-small;
}
.style7 {
	border: 1px solid #000000;
	font-size: x-small;
	font-weight: bold;
}
input{
	width:100px;
	border-radius:3px;
	border:1px silver solid;
	background-color:#2C5783;
	color:white;
	padding:10px;
	cursor:pointer;
	font-size:14px;
}

</style>
</head>

<body  style="font-family:Verdana, Geneva, Tahoma, sans-serif" link="#800000" vlink="#800000" alink="#800000" >
<%
sdate = dconv(request("sdate"))
edate = dconv(request("edate"))
%>
<p align="center">
<b>
BETA Shop Production Detail Report<br>
from&nbsp;<%=oconv(sdate)%> to <%=oconv(edate)%>
&nbsp;</b><br>
This report has been modified to include only Closed Repair Orders. To view this 
report with Finalized and Closed RO's,
<a onclick="document.body.style.cursor='wait'" href="productiondetailwithfinalizedros.asp?sd=<%=request("sdate")%>&ed=<%=request("edate")%>">click here</a>


<p id="buttons"><input onclick="printSPR()" name="Button1" type="button" value="Print">
<input onclick="location.href='../reports.php'" name="Button2" type="button" value="Done">
<input onclick="location.href='productiondetail.asp?e=y&sdate=<%=request("sdate")%>&edate=<%=request("edate")%>'" name="Button3" type="button" value="Export to Excel" style="width: 146px"></p><p align="center">
<%
sd = timer()
shopid = request.cookies("shopid")
stmt = "select replacerowithtag from company where shopid = '" & shopid & "'"
set rs = con.execute(stmt)
rrwt = lcase(rs("replacerowithtag"))
set rs = nothing

stmt = "select roid,tagnumber,statusdate,totalprts,totallbr,totalro,totalsublet,totalfees,discountamt,salestax,customer,fleetno,partscost,taxrate from repairorders where shopid = '" & shopid & "' and "
stmt = stmt & "Status = 'CLOSED' and statusdate >= '"
stmt = stmt & sdate & "' and statusdate <= '" & edate & "' order by statusdate"
'response.write stmt
set rs = con.execute(stmt)
%>
<table width=100% cellpadding="3" cellspacing="0">
	<tr>
		<td width="5%" bgcolor="#C0C0C0" class="style5"><strong>RO#</strong></td>
		<td width="5%" bgcolor="#C0C0C0" class="style5"><strong>Final_Date</strong></td>
		<td width="20%" bgcolor="#C0C0C0" class="style5"><strong>Customer</strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Labor</strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Parts</strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5">Non_Tax 
		Parts</td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5">Tax_Parts</td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Sublet</strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0">
		<span class="style6"><strong>Fees</strong></span></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5">Taxable 
		Subtotal</td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5">Non-Tax 
		Subtotal</td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Tax</strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style6"><strong>
		Discount</strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Total_RO</strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Parts_Cost</strong></td>
	</tr>
<%
tntparts = 0
ttparts = 0
ttdiscparts = 0

runsubtotal = 0
runntsubtotal = 0
if not rs.eof then
	rs.movefirst
	while not rs.eof
	tparts = 0
	if len(rs("fleetno")) > 0 then
		fleetno = " (#" & rs("fleetno") & ")"
	else
		fleetno = ""
	end if
	
	'per Scott 1/8/18 want mypcost to reflect GP. Therefore, pulling the same code from shop prod det/w pymts
	
	'if isnull(rs("partscost")) then
	'	mypcost = 0
	'else
	'	mypcost = rs("partscost")
	'end if
	
	' get total part cost
	
	pstmt = "select coalesce(sum(cost*quantity),0) as pcost from parts where deleted = 'no' and shopid = '" & request.cookies("shopid") & "' and ROID = " & rs("ROID")
	set prs = con.execute(pstmt)
	if not prs.eof then
		prs.movefirst
		mypcost = prs("pcost")
		if isnull(prs("pcost")) then mypcost = 0
	else
		mypcost = 0
	end if
	
	labor = labor + rs("TotalLbr")
	parts = parts + rs("TotalPrts")
	sublet = sublet + rs("TotalSublet")
	slstax = slstax + rs("SalesTax")
	
	pcost = pcost + mypcost
	'ttldisc = ttldisc + rs("discountamt")
	totalfees = totalfees + rs("totalfees")
	mytotalro = rs("TotalLbr")+rs("TotalPrts")+rs("TotalSublet")+rs("SalesTax")+rs("totalfees")-rs("discountamt")
	tro = tro + mytotalro
	if rrwt = "yes" then
		displayroid = rs("tagnumber")
	else
		displayroid = rs("roid")
	end if

	ntparts = 0
	tparts = 0
	partsdiscount = 0
	discountamt = 0

	
	stmt = "select linettlprice as ntprice from parts p left join complaints c on p.shopid = c.shopid and p.complaintid = c.complaintid and p.ROID = c.roid where cstatus = 'no' and deleted = 'no' and tax = 'NO' and p.shopid = '" & shopid & "' and p.roid = " & rs("roid")
	'response.write stmt
	set prs = con.execute(stmt)
	if not prs.eof then
		do until prs.eof
			ntparts = ntparts + formatnumber(prs("ntprice"),2)
			prs.movenext
		loop
	end if
	stmt = "select linettlprice as tprice from parts p left join complaints c on p.shopid = c.shopid and p.complaintid = c.complaintid and p.ROID = c.roid where cstatus = 'no' and deleted = 'no' and tax = 'YES' and p.shopid = '" & shopid & "' and p.roid = " & rs("roid")
	'response.write stmt & "<BR>"
	set prs = con.execute(stmt)
	if not prs.eof then
		do until prs.eof
			tparts = tparts + formatnumber(prs("tprice"),2)
			prs.movenext
		loop
	end if	
	' add the discount amount on parts amount
	stmt = "SELECT coalesce(sum(linettlprice),0) partsdiscount from parts where partnumber = 'DISCOUNT' and shopid = '" & request.cookies("shopid") & "' and roid = " & rs("roid")
	set prs = con.execute(stmt)
	'response.write stmt & "<BR>"

	if not prs.eof then
		do until prs.eof
			partsdiscount = partsdiscount + formatnumber(prs("partsdiscount"),2)
			discountamt =  discountamt + partsdiscount + rs("discountamt")
			prs.movenext
		loop
	end if

	'ttldisc = ttldisc + rs("discountamt")
	ttldisc = ttldisc + discountamt
	tntparts = tntparts + ntparts
	ttparts = ttparts + tparts
	ttdiscparts = ttdiscparts + partsdiscount


	ntsubtotal = 0
	subtotal = 0
	if rs("taxrate") = 0 then
		'ntsubtotal = rs("TotalLbr") + rs("TotalPrts") + rs("TotalSublet") + rs("totalfees")
		ntsubtotal = rs("TotalPrts") + rs("TotalSublet") 
		runntsubtotal = runntsubtotal + ntsubtotal
		ntflag = " <b>(TaxExempt)</b>"
	else
		'subtotal = rs("TotalLbr") + rs("TotalPrts") + rs("TotalSublet") + rs("totalfees")
		subtotal =  rs("TotalPrts") + rs("TotalSublet") + rs("totalfees")
		runsubtotal = runsubtotal + subtotal
		ntflag = ""
	end if

	' Labor should always go in to non tax subtotal
	ntsubtotal = ntsubtotal + rs("TotalLbr") 
	runntsubtotal = runntsubtotal + ntsubtotal
	
	' Fees needed to be added to Taxable Subtotal
	if rs("taxrate") = 0 then
		subtotal = subtotal + rs("totalfees") 
		runsubtotal = runsubtotal + subtotal
	end if
		
	
	
	calctax = round(tparts * (rs("taxrate") / 100),2)
	runtax = runtax + round(rs("salestax")-calctax,2)
	

%>
	<tr>
		<td width="5%" class="style6"><%=displayroid%></td>
		<td width="5%" class="style6"><%=rs("statusdate")%></td>
		<td width="20%" class="style6"><%=rs("Customer") & fleetno & ntflag%></td>
		<td align="right" width="5%" class="style6"><%=formatnumber(rs("TotalLbr"),2)%></td>
		<td align="right" width="5%" class="style6"><%=formatnumber(rs("TotalPrts"),2)%></td>
		<td align="right" width="5%" class="style6"><%=ntparts%></td>
		<td align="right" width="5%" class="style6"><%=tparts%></td>
		<td align="right" width="5%" class="style6"><%=formatnumber(rs("TotalSublet"),2)%></td>
		<td align="right" width="5%" class="style6"><%=formatnumber(rs("totalfees"),2)%></td>
		<td align="right" width="5%" class="style6"><%=formatnumber(subtotal,2)%></td>
		<td align="right" width="5%" class="style6"><%=formatnumber(ntsubtotal,2)%></td>
		<%
		if request.cookies("shopid") = "3240" then
		%>
		<td align="right" width="5%" class="style6"><%=calctax & "/" & formatnumber(rs("SalesTax"),2) & "/" & round(rs("salestax")-calctax,2)%></td>
		<%
		else
		%>
		<td align="right" width="5%" class="style6"><%=formatnumber(rs("SalesTax"),2)%></td>
		<%
		end if
		%>
		<td align="right" width="5%" class="style6"><%=formatnumber(discountamt,2)%></td>
		<td align="right" width="5%" class="style6"><%=formatnumber(mytotalro,2)%></td>
		<td align="right" width="5%" class="style6"><%=formatnumber(mypcost,2)%></td>
	</tr>
<%
		set prs = nothing
		rs.movenext
	wend
end if

'now get the part sales
stmt = "select * from ps where statusdate >= '" & sdate & "' and statusdate <= '" & edate
stmt = stmt & "' and shopid = '" & request.cookies("shopid") & "' and `status` = 'Closed'"
set rs = con.execute(stmt)
if not rs.eof then
%>
	<tr>
		<td width="5%" bgcolor="#C0C0C0" class="style5"><strong>PS#</strong></td>
		<td width="5%" bgcolor="#C0C0C0" class="style5"><strong>Sale_Date</strong></td>
		<td width="20%" bgcolor="#C0C0C0" class="style5"><strong>Customer</strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong></strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Parts</strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5">Non_Tax 
		Parts</td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5">Tax_Parts</td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong></strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Fees</strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5">&nbsp;</td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5">&nbsp;</td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Tax</strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong></strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Total_Sale</strong></td>
		<td align="right" width="5%" bgcolor="#C0C0C0" class="style5"><strong>Parts_Cost</strong></td>
	</tr>

<%
	do until rs.eof
		stmt = "select concat(lastname,',',firstname) as lf from customer where customerid = "
		stmt = stmt & rs("cid") & " and shopid = '" & request.cookies("shopid") & "'"
		set trs = con.execute(stmt)
		lf = trs("lf")
		parts = parts + (rs("subtotal") - rs("fees"))
		slstax = slstax + rs("tax")
		tro = tro + rs("total")
		
		' get total part cost
		stmt = "select sum(qty*cost) as extcost from psdetail where psid = " & rs("psid")
		stmt = stmt & " and shopid = '" & request.cookies("shopid") & "'"
		set trs = con.execute(stmt)
		if not trs.eof then
			if len(trs("extcost")) > 0 then
				mypcost = trs("extcost")
			else
				mypcost = 0
			end if
		else
			mypcost = 0
		end if
		pcost = pcost + mypcost

		stmt = "select sum(ext) as ntprice from psdetail where lcase(tax) = 'no' and shopid = '" & request.cookies("shopid") & "' and psid = " & rs("psid")
		set prs = con.execute(stmt)
		if not isnull(prs("ntprice")) then
			ntparts = formatnumber(prs("ntprice"),2)
		else
			ntparts = "0.00"
		end if
		stmt = "select sum(ext) as ntprice from psdetail where lcase(tax) = 'yes' and shopid = '" & request.cookies("shopid") & "' and psid = " & rs("psid")
		set prs = con.execute(stmt)
		if not isnull(prs("ntprice")) then
			tparts = formatnumber(prs("ntprice"),2)
		else
			tparts = "0.00"
		end if
		tntparts = tntparts + ntparts
		ttparts = ttparts + tparts

%>
	<tr>
		<td width="5%" class="style6"><%=rs("psid")%></td>
		<td width="5%" class="style6"><%=rs("statusdate")%></td>
		<td width="20%" class="style6"><%=lf%></td>
		<td align="right" width="5%" class="style6"></td>
		<td align="right" width="5%" class="style6"><%=formatcurrency(rs("subtotal") - rs("fees"))%></td>
		<td align="right" width="5%" class="style6">&nbsp;</td>
		<td align="right" width="5%" class="style6">&nbsp;</td>
		<td align="right" width="5%" class="style6"></td>
		<td align="right" width="5%" class="style6"><%=formatcurrency(rs("fees"))%>&nbsp;</td>
		<td align="right" width="5%" class="style6">&nbsp;</td>
		<td align="right" width="5%" class="style6">&nbsp;</td>
		<td align="right" width="5%" class="style6"><%=formatcurrency(rs("Tax"))%></td>
		<td align="right" width="5%" class="style6"></td>
		<td align="right" width="5%" class="style6"><%=formatcurrency(rs("Total"))%></td>
		<td align="right" width="5%" class="style6"><%=formatcurrency(mypcost)%></td>
	</tr>
<%
		rs.movenext
	loop
end if
%>
	<tr>
		<td width="5%" class="style5">Totals</td>
		<td width="5%"></td>
		<td width="20%"></td>
		<td align="right" width="5%" class="style7"><%=formatnumber(labor,2)%></td>
		<td align="right" width="5%" class="style7"><%=formatnumber(parts,2)%></td>
		<td align="right" width="5%" class="style7"><%=formatnumber(tntparts,2)%></td>
		<td align="right" width="5%" class="style7"><%=formatnumber(ttparts,2)%></td>
		<td align="right" width="5%" class="style7"><%=formatnumber(sublet,2)%></td>
		<td align="right" width="5%" class="style7"><%=formatnumber(totalfees,2)%></td>
		<td align="right" width="5%" class="style7"><%=formatnumber(runsubtotal,2)%></td>
		<td align="right" width="5%" class="style7"><%=formatnumber(runntsubtotal,2)%></td>
		<%
		if request.cookies("shopid") = "3240" then
		%>
		<td align="right" width="5%" class="style7"><%=formatnumber(slstax,2) & "/" & runtax%></td>
		<%
		else
		%>
		<td align="right" width="5%" class="style7"><%=formatnumber(slstax,2)%></td>
		<%
		end if
		%>
		<td align="right" width="5%" class="style7"><%=formatnumber(ttldisc,2)%></td>
		<td align="right" width="5%" class="style7"><%=formatnumber(tro,2)%></td>
		<td align="right" width="5%" class="style7"><%=formatnumber(pcost,2)%></td>
	</tr>
</table>
<br><br><br>
</body>

</html>
<%
ed = timer

ed = ""
sd = ""
'Copyright 2011 - Boss Software Inc.
%>