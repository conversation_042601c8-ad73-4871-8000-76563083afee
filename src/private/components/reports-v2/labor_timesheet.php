<?php
$component = "reports-v2";
//To view this report with Finalized and Closed RO's, click here
//Click an RO number to view the RO
include getHeadGlobal($component);
include getRulesGlobal($component);


$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));
$job_completed = isset($_REQUEST['job_completed']) ? filter_var($_REQUEST['job_completed'], FILTER_SANITIZE_STRING) : "";
// Page Variables
$title = 'Labor Timesheet';  // Report Title Goes Here

$subtitle = 'This Report Includes All Labor Timeclock Punches';  // Report SubTitle Goes Here - Hide if not needed

if ($job_completed == "yes") {
    $subtitle .= " for job status marked complete";
}
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_freeze_header_lab_times.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';


$jobSQL = "";
if (!empty($job_completed) && $job_completed == "yes") {
    $jobSQL = "AND c.acceptdecline = 'Job Complete'";
}
?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);

// Use this for Custom Reports
//include("../../../php/includes/reports/report_buttons.php");
$tablefields = array('Technician', 'RO #', 'Issue #', 'Clock In', 'Clock Out', 'Time Hours:Min', 'Time - decimal', 'Sold Hours', 'Labor Sold');//set table headings array for excel export
$alldata = array();//this will hold all the data arrays to be exported to excel
?>

    <main id="reports">
        <div class="report">
            <div class="col-12">
                <div class="title col breadcrumb d-flex align-items-center">
                    <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                        class="text-secondary ps-3 pe-3">/</span>
                    <h2><?= $title; ?></h2>
                </div>
                <hr/>
                <p class="card-title">
                    <?php
                    if ((!empty($sd))) {
                        echo($sd);
                    }
                    if (!empty($sd) && !empty($ed)) {
                        echo ' to ';
                    }
                    if (!empty($ed)) {
                        echo $ed;
                    }
                    if (empty($sd) && empty($ed)) {
                        echo date('m/d/Y');
                    }
                    ?>
                </p>
                <p class="">
                    This Report Includes All Labor Timeclock Punches
                </p>
            </div>
        </div>
        <div class="report">
            <?php
            include_once "inline_prompt.php";
            ?>
            <table class="tech_report w-100" id="tech_report" style="width: 100%">
                <thead>
                <tr class="table_header table_head">
                    <th>Technician</th>
                    <th>RO #</th>
                    <th>Issue #</th>
                    <th>Clock In</th>
                    <th>Clock Out</th>
                    <th style="text-align:right">Time - Hours:Min</th>
                    <th style="text-align:right">Time - Decimal Format</th>
                    <th style="text-align:right">Sold Hours</th>
                    <th style="text-align:right">Labor Sold</th>
                </tr>
                </thead>
                <tbody>
                <?php
                $stmt = "SELECT distinct tech FROM labortimeclock WHERE shopid = ? AND date(startdatetime) >= ? AND date(enddatetime) <= ? ";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("sss", $shopid, $sd1, $ed1);
                    $query->execute();
                    $lbrcresult = $query->get_result();
                } else {
                    echo "Labortime clock prepare failed:  . (" . $conn->errno . ")" . $conn->error;
                }
                if ($lbrcresult->num_rows > 0) {

                    while ($lbrc = $lbrcresult->fetch_array()) {

                        $tech = $lbrc["tech"];
                        $hr = 0;
                        $mins = 0;
                        $runningdiff = 0;
                        $runtime = 0;
                        $runro = 0;

                        $laborids = array();
// Template Query Begins - Replace entire section
                        $stmt = "SELECT l.id,l.tech,l.startdatetime,l.enddatetime,l.roid,l.complaintid,c.displayorder, lb.LineTotal as labor_sold, lb.LaborHours as sold_hours, lb.LaborID as labor_id FROM labortimeclock l JOIN complaints c ON l.shopid = c.shopid AND l.roid = c.roid AND l.complaintid = c.complaintid JOIN labor lb ON lb.LaborID = l.laborid AND lb.shopid = l.shopid AND l.roid = lb.ROID WHERE l.shopid = ? AND l.tech = ? AND date(l.startdatetime) >= '$sd1' AND date(l.enddatetime) <= '$ed1' AND lb.deleted = 'no' $jobSQL ORDER BY l.startdatetime";
                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("ss", $shopid, $tech);
                            $query->execute();
                            $rsresult = $query->get_result();
                        } else {
                            echo "Timeclock Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }
                        if ($rsresult->num_rows > 0) {
                            $tech_ttlsold = 0;
                            $tech_ttlhrs = 0;
                            while ($rs = $rsresult->fetch_array()) {
                                $sdt = date('m/d/Y H:i:s', strtotime($rs["startdatetime"]));
                                $edt = date('m/d/Y H:i:s', strtotime($rs["enddatetime"]));
                                $st = date('Y-m-d H:i:00', strtotime($rs["startdatetime"]));
                                $et = date('Y-m-d H:i:00', strtotime($rs["enddatetime"]));
                                $dateDiff = strtotime($et) - strtotime($st);
                                $runningdiff += $dateDiff;
                                $hours = round($dateDiff / 3600, 2);
                                $hrsarr = explode('.', $hours);
                                $hrs = $hrsarr[0];
                                $minutes = round(($dateDiff / 60) % 60, 2);
                                if (strlen($minutes) == 1) $minutes = "0" . $minutes;

                                $nHours = (int)($dateDiff / 3600);
                                $nTotalSeconds = $dateDiff - (3600 * $nHours);
                                $nMinutes = (int)($nTotalSeconds / 60);
                                $nMinutes = round($nMinutes * 1.67);
                                if (strlen($nMinutes) == 1) $nMinutes = "0" . $nMinutes;

                                $sold_hours = 'N/A';
                                $labor_sold = 'N/A';

                                if (!isset($laborids[$rs['labor_id']])) {
                                    $laborids[$rs['labor_id']] = array(
                                        'sold_hours' => $rs['sold_hours'],
                                        'labor_sold' => $rs['labor_sold']
                                    );
                                    $sold_hours = number_format($rs['sold_hours'], 2);
                                    $labor_sold = asDollars($rs['labor_sold']);
                                    $tech_ttlsold += $rs['labor_sold'];
                                    $tech_ttlhrs += $rs['sold_hours'];
                                }
                                ?>
                                <tr>
                                    <td><?= $tech; ?></td>
                                    <td><?= $rs["roid"]; ?></td>
                                    <td><?= $rs["displayorder"]; ?></td>
                                    <td><?= $sdt; ?></td>
                                    <td><?= $edt; ?></td>
                                    <td style="text-align:right"><?= $hrs . ":" . $minutes ?></td>
                                    <td style="text-align:right"><?= $nHours . "." . $nMinutes ?></td>
                                    <td style="text-align:right"><?= $sold_hours ?></td>
                                    <td title="" style="text-align:right"><?= $labor_sold ?></td>
                                </tr>
                                <?php
                                $alldata[] = array(strtoupper($tech), $rs["roid"], $rs["displayorder"], $sdt, $edt, $hrs . ":" . $minutes, $nHours . "." . $nMinutes, $sold_hours, $labor_sold);//fill up the alldata array with the arrays of data to be shown in excel export
                            } // end of while for loop
                        }// end if for end of file

                        $hours = round($runningdiff / 3600, 2);
                        $hrsarr = explode('.', $hours);
                        $hrs = $hrsarr[0];
                        $minutes = round(($runningdiff / 60) % 60, 2);
                        if (strlen($minutes) == 1) $minutes = "0" . $minutes;

                        $nHours = (int)($runningdiff / 3600);
                        $nTotalSeconds = $runningdiff - (3600 * $nHours);
                        $nMinutes = (int)($nTotalSeconds / 60);
                        $nMinutes = round($nMinutes * 1.67);
                        if (strlen($nMinutes) == 1) $nMinutes = "0" . $nMinutes;
                        ?>
                        <!--
            <tr class="table_total">
                <td><b>TOTALS for <?= $tech; ?></b></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td style="text-align:right"><b><?= $hrs . "hrs " . $minutes . "min" ?></b></td>
                <td style="text-align:right"><b><?= $nHours . "." . $nMinutes . " hours" ?></b></td>
                <td style="text-align:right"><b><?= number_format($tech_ttlhrs, 2) ?></b></td>
                <td style="text-align:right"><b><?= asDollars($tech_ttlsold) ?></b></td>
            </tr>
            -->
                        <?php
                        $alldata[] = array("TOTALS for $tech", '', '', '', '', "$hrs hrs $minutes min", "$nHours.$nMinutes hours", number_format($tech_ttlhrs, 2), asDollars($tech_ttlsold));
                        $alldata[] = array('', '', '', '', '', '', '', '', '');
                    }
                }
                ?>
                </tbody>
            </table>
        </div>
    </main>
<?php
include(COMPONENTS_PRIVATE_PATH . '/reports-v2/includes/report_form.php');

include getScriptsGlobal($component);


$dateRangeDisplay = ($_REQUEST['sdate'] ?? "") . " " . (!empty($_REQUEST['edate']) ? " to " . $_REQUEST['edate'] : "");
$subtitle = !empty($subtitle) ? $subtitle : "";

?>
    <script>
        $(document).ready(function () {

            let CalcT = Array(6, 7, 8);

            let floatVal = function (i) {
                return typeof i === 'string' ? Number(i.replace(/[$,]/g, '')) : typeof i === 'number' ? i : 0;
            };

            groupCol_writer = 0;
            let table = $("#tech_report").DataTable({
                responsive: true,
                fixedHeader: {
                    headerOffset: 68
                },
                colReorder: true,
                select: true,
                scrollY: false,
                scrollX: false,
                scroller: false,
                paging: false,
                language: {
                    searchPanes: {
                        emptyPanes: 'No records found in this date range'
                    },
                    search: "_INPUT_",
                    searchPlaceholder: "Search..."
                },
                buttons: [
                    {
                        extend: 'csv',
                        text: '<i class="fas fa-file-csv fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                        messageTop : "<?= $dateRangeDisplay ?>",
                    },
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint." (".$dateRangeDisplay.")" ?>",
                        messageTop : "<?= $subtitle ?>",
                        exportOptions: {
                            // Any other settings used
                            columns: [':visible'],
                            grouped_array_index: 0
                        },
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                        exportOptions: {
                            // Any other settings used
                            stripHtml: false,
                            columns: [':visible'],
                            grouped_array_index: 0
                        },
                        messageTop : "<?= $dateRangeDisplay."<br/>".$subtitle ?>",
                        footer: true,
                    }
                ],
                dom: "<'row'<'col-sm-12 col-md-6 dt_Search text-secondary'f><'col-sm-12 col-md-6 text-right dt_btns text-secondary'B>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
                //    columnDefs: [{visible: false, targets: groupCol_writer}],
                //order: [[groupCol_writer, 'asc']],
                order: [],
                orderFixed: [0, "asc"],
                rowGroup: {
                    endRender: function (rows, group) {

                        let totsArr = Array();

                        CalcT.forEach(function (val, idx) {
                            var col = rows.data().pluck(val);
                            totsArr[val] = col.reduce(function (a, b) {
                                var fl_a = floatVal(a);
                                var fl_b = floatVal(b);
                                if (isNaN(fl_a)) {
                                    fl_a = 0;
                                }
                                if (isNaN(fl_b)){
                                    fl_b = 0;
                                }
                                sum = fl_a + fl_b;
                                return (typeof sum != "undefined" || !isNaN(sum)) ? sum : 0;
                            }, 0);
                        });
                        var tr = rows.data();
                        var numcol = tr[0].length;

                        let formatting_options = {
                            style: 'currency',
                            currency: 'USD',
                            minimumFractionDigits: 2,
                        };
                        let USDollar = new Intl.NumberFormat('en-US', formatting_options);
                        var gname = group.replace(/[^A-Z0-9]/ig, "_");
                       // console.log('numcol', numcol);
                        if (typeof numcol !== "undefined" && numcol > 0) {
                         //   console.log();
                            var total_row = $('<tr id="' + gname + '_totals">');
                            total_row.append('<td colspan="2" class="text-left">' + group + ' Totals</td>')
                            for (var i = 2; i < numcol; i++) {

                                if (typeof totsArr[i] !== "undefined" || totsArr[i] != null) {
                                    var totVal = totsArr[i];
                                   // console.log(totsArr[i], !isNaN(totsArr[i]));
                                    if (!isNaN(totsArr[i]) && rows.data().pluck(i)[0].indexOf('$') >= 0) {
                                        totVal = USDollar.format(totsArr[i])
                                    }
                                    total_row.append('<td class="text-right">' + totVal + '</td>')
                                   // console.log("Adding total")
                                } else {
                                    total_row.append('<td class=""></td>')
                                }
                            }
                            total_row.append("</tr>");
                            return total_row;
                        }

                    },
                    dataSrc: 0,
                    endClassName: 'table_total',
                    startClassName: 'group_heading'
                },

            });
        });
    </script>
<?php
include getFooterComponent($component);
?>