<?php
$title = 'TimeClock vs. Labor TimeClock';
$component = "reports-v2";

include getHeadGlobal($component);
include getRulesGlobal($component);
include getHeaderGlobal($component);
include getMenuGlobal($component);

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd)). " 00:00:00";
$ed1 = date('Y-m-d', strtotime($ed)) . " 23:59:59";


// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_autosize.php';

function SEC_TO_TIME($seconds)
{
    $t = round($seconds);
    return sprintf('%02d:%02d:%02d', ($t / 3600), ($t / 60 % 60), $t % 60);
}

?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="title col breadcrumb d-flex align-items-center">
                <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                    class="text-secondary ps-3 pe-3">/</span>
                <h2><?= $title; ?></h2>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report">
        <?php
            include_once "inline_prompt.php";
        ?>
        <table class="dtable" style="width: 100%">
            <thead>
            <tr class="table_header table_head">
                <th>Technician</th>
                <th>TimeClock Hours</th>
                <th>Labor TimeClock</th>
                <th>Productivity</th>
            </tr>
            </thead>
            <tbody>
            <?php
            $tablefields = array('Technician', 'TimeClock Hours', 'Labor TimeClock', 'Productivity');//set table headings array for excel export
            $alldata = array();//this will hold all the data arrays to be exported to excel

            // Insert DB Query Here

            // Template Query Begins - Replace entire section
            $stmt = "select employeefirst ef,employeelast el, SUM(TIMESTAMPDIFF(SECOND, startdatetime, enddatetime)) td, startdatetime, enddatetime FROM timeclock tc LEFT JOIN employees e ON tc.shopid = e.shopid and tc.emp = e.id WHERE tc.shopid = ? AND tc.startdatetime >= ? AND tc.enddatetime <= ? and not isnull(enddatetime) and enddatetime != '0000-00-00 00:00:00' GROUP BY tc.emp ORDER BY employeelast, employeefirst";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sss", $shopid, $sd1, $ed1);
                $query->execute();
                $rsresult = $query->get_result();
            } else {
                echo "Timeclock Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }
            if ($rsresult->num_rows > 0) {
                while ($rs = $rsresult->fetch_array()) {
                    $el = $rs["el"];
                    $ef = $rs["ef"];
                    $tech = $rs["el"] . ", " . $rs["ef"];
                    $rs['td'] = SEC_TO_TIME($rs['td']);

                    $stmt = "SELECT tech, SUM(TIME_TO_SEC(enddatetime) - TIME_TO_SEC(startdatetime)) td from labortimeclock WHERE shopid = ? AND tech = ? AND startdatetime >= ? AND enddatetime <= ? ";
                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("ssss", $shopid, $tech, $sd1, $ed1);
                        $query->execute();
                        $xrsresult = $query->get_result();
                    } else {
                        echo "Labor Time Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }
                    $xrs = $xrsresult->fetch_array();
                    $xrs['td'] = SEC_TO_TIME($xrs['td']);
                    $tcnumsectcar = 0;
                    $tchourtosecs = 0;
                    $tcmintosecs = 0;
                    $tcsecstosecs = 0;
                    $tctsecs = 0;
                    $ltcnumsectcar = 0;
                    $ltchourtosecs = 0;
                    $ltcmintosecs = 0;
                    $ltcsecstosecs = 0;
                    $ltctsecs = 0;
                    if (strpos($rs["td"], ":")) {
                        $tcnumsectcar = explode(":", $rs["td"]);
                        $tchourtosecs = $tcnumsectcar[0] * 60 * 60;
                        $tcmintosecs = $tcnumsectcar[1] * 60;
                        $tcsecstosecs = $tcnumsectcar[2];
                        $tctsecs = $tchourtosecs + $tcmintosecs + $tcsecstosecs;
                        //echo " time clock " . $tctsecs . "<br />";
                    } else {
                        $tcsecs = 1;
                    }
                    if (strpos($xrs["td"], ":")) {
                        //if instr(xrs("td"),":") then
                        // now same for ltc times
                        $ltcnumsectcar = explode(":", $xrs["td"]);
                        $ltchourtosecs = $ltcnumsectcar[0] * 60 * 60;
                        $ltcmintosecs = $ltcnumsectcar[1] * 60;
                        $ltcsecstosecs = $ltcnumsectcar[2];
                        $ltctsecs = $ltchourtosecs + $ltcmintosecs + $ltcsecstosecs;
                        //echo " labor " . $ltctsecs . "<br />";
                    } else {
                        $ltctsecs = 1;
                    }
                    ?>
                    <tr>
                        <td><?= $rs["el"] . ", " . $rs["ef"]; ?></td>
                        <td><?= $rs["td"]; ?></td>
                        <td><?= $xrs["td"]; ?></td>
                        <td><?php
                            // should be 0.somehting
                            if ($tctsecs > 0) {
                                //echo ($ltctsecs / $tctsecs) . "<br />";
                                $productivity = sbpround(($ltctsecs / $tctsecs), 2) * 100;
                                if ($productivity < 101) {
                                    echo $productivity . "%";
                                } else {
                                    echo $productivity . "%";
                                }
                            } else {
                                echo $productivity . "%";
                            }
                            ?>
                        </td>
                    </tr>
                    <?php
                    $alldata[] = array(strtoupper($rs["el"] . ", " . $rs["ef"]), $rs["td"], $xrs["td"], $productivity . "%");//fill up the alldata array with the arrays of data to be shown in excel export
                } // end of while for loop
            }// end if for end of file

            $alldata[] = array('', '', '', '');
            ?>
            </tbody>
        </table>
    </div>
</main>
<?php

require COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form.php";

include getScriptsGlobal($component);
include getFooterComponent($component);
?>
