<style>
    #reports{
        height: 100% !important;
    }
    .dt_Search .dataTables_filter{
        text-align: left !important;
    }
    .dt_btns .dt-buttons {
        text-align:right !important;
    }
    .dt_btns .dt-buttons .dt-button {
        background: transparent;
        border: none;
        outline: none;
        --mdb-text-opacity: 1;
        color: rgba(var(--mdb-secondary-rgb),var(--mdb-text-opacity)) !important;
        padding: 5px 10px;
    }
    .dataTable th{
        border-bottom: 1px solid var(--secondary) !important;
        font-weight: normal;
    }
    .text-right{
        text-align: right !important;
    }
    .text-left{
        text-align: left !important;
    }
    .text-center{
        text-align: center !important;
    }

    .compressed{
        width : 48px;
    }
    .table_total td, .table_total th, .table_totals td{
        font-weight : bold;
    }
    .group_heading th, .group_heading td, .group_heading{
        color: var(--primary) !important;
        font-weight: 100;
        font-size: 1.25rem;
    }
    .dtable{
        width: 100%;
    }
    table.dataTable tbody tr td {
        word-wrap: break-word;
        /* word-break: break-all; */
    }
    /*
    .reports_table thead>tr th, .report_head td, .report_head th, .report_head{
        background-color : var(--primary) !important;
        color: white !important;
    }
     */
    th {
       /* color: var(--secondary) !important; */
    }
    th {
       /* color: var(--secondary) !important; */
    }
    th:hover{
        color: var(--primary) !important;
    }
    .reports_table thead>tr {
        border : 1px solid var(--primary) !important;
        border-radius : 3px;
        color: var(--primary) !important;
    }
    .reports_table thead th{
        color: var(--primary) !important;
    }
    .form-label{
        line-height : inherit !important;
    }
    .vh-65{
        height : 65vh;
    }

    #main-container, .main-container{
        margin: 0px !important;
    }

    .dataTables_scrollBody tr, .dataTables_scrollBody th{
        height: 0px !important;
    }

    @media print {
        .dt-buttons, .dataTables_filter, .dt_Search{
            display: none;
        }
        table.dataTable thead>tr>th.sorting:before, table.dataTable thead>tr>th.sorting:after {
            display : none;
        }
        table th, table tfoot td{
            color: black;
            font-weight: bold;
        }
        .group_heading{
        color: var(--primary) !important;
        font-weight: 100;
        font-size: 1.25rem;
        }
        tr{
            font-size:9px !important;
        }
        .dtable{
            width : 100%
        }
        .dtable tr, .dtable th,
        .datatable-custom tr, .datatable-custom th {
            height: 0px !important;
        }
    }
</style>
<link rel="stylesheet" media="print" href="<?= CSS ?>/report_print.css">
