

<?php
$title = "Inventory Order Report";
$component = "reports-v2";

include getHeadGlobal($component);
include getRulesGlobal($component);


$shopid = $_COOKIE['shopid'];
$supplier = isset($_REQUEST['supplier']) ? filter_var($_REQUEST['supplier'], FILTER_SANITIZE_STRING) : "all";
$supplier = str_replace("'", "''", $supplier);

$template = COMPONENTS_PRIVATE . "/reports/templates/excelexport_multiple.php";

?>


<body>
<?php

include getHeaderGlobal($component);
include getMenuGlobal($component);
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-10 col-sm-10">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary d-print-none">Reports</a>
                        <span
                                class="text-secondary d-print-none ps-3 pe-3">/</span>
                        <h2><?= $title; ?> <span class="d-none d-print-inline"> <?= $companyNamePrint ?></span></h2>
                    </div>
                </div>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report">
        <?php
            include_once "inline_prompt.php";
        ?>
        <table class="w-100" id="inv_order_table" style="page-break-after: avoid; width: 100%">
            <thead>
            <tr class="">
                <th></th>
                <th style="width: 10%">Part #</th>
                <th style="width: 15%">Description</th>
                <th style="width: 10%">Supplier</th>
                <th style="width: 8%" class="text-right">Cost</th>
                <th style="width: 8%" class="text-right">Alloc</th>
                <th style="width: 8%" class="text-right">Net</th>
                <th style="width: 8%" class="text-right">Reorder</th>
                <th style="width: 8%" class="text-right">Max</th>
                <th style="width: 8%" class="text-right">90_Days</th>
                <th style="width: 8%" class="text-right">12_Mos</th>
                <th style="width: 8%" class="text-right">Order</th>
            </tr>
            </thead>
            <tbody>
        <?php
        $tablefields = array();
        $alldata = array();

        $catlistArr = array();
        $catlist = "";
        //get categories
        $stmt = "select distinct UCASE(Category) from category where shopid = ? order by Category";
        if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($Category);
        $query->store_result();
        if ($query->num_rows > 0) {

        while ($query->fetch()) {
        //get the parts
        //get sum cost
        $lineorderamt = 0;
        if ($supplier == "all") {
            $cstmt = "select * from partsinventory where shopid = '$shopid' and netonhand <= reorderlevel
and reorderlevel >= 0 and maxonhand > 0 and (maxonhand - netonhand) <> 0 and PartCategory = '$Category' order by PartDesc, PartNumber";
            $showemail = "no";
        } else {
            $cstmt = "select * from partsinventory where partsupplier = '$supplier' and shopid = '$shopid' and netonhand <= reorderlevel and reorderlevel >= 0 and maxonhand > 0 and (maxonhand - netonhand) <> 0 and PartCategory = '$Category' order by PartDesc, PartNumber";
            $showemail = "yes";
            //get the supplier info
            $stmt = "select * from supplier where suppliername = '$supplier' and shopid = '$shopid'";
            if ($xquery = $conn->prepare($stmt)) {
                $xquery->execute();
                $xresults = $xquery->get_result();
                if ($xresults->num_rows > 0) {
                    $xrs = $xresults->fetch_assoc();
                    $sname = $xrs["SupplierName"] . " - " . $xrs["SupplierPhone"];
                }
                $xquery->close();
            }
        }

        if ($cquery = $conn->prepare($cstmt)) {
            $cquery->execute();
            $cresults = $cquery->get_result();
            $cquery->close();
        }

        if ($cresults->num_rows > 0) {
            //   echo "<strong>" . (empty($sname) ? "" : $sname) . "</strong>";
            if (!empty($sname)) {
                $alldata[] = array('TABLETITLE', $sname);
            }
            $alldata[] = array('', '', '', '', '', '', '', '', '', '', '');
            $alldata[] = array('TABLEHEAD', 'Category', $Category, '', '', '', '', '', '', '', '', '');
            $alldata[] = array('TABLEHEAD', 'Part #', 'Description', 'Supplier', 'Cost', 'Alloc', 'Net', 'Reorder', 'Max', '90_Days', '12_Mos', 'Order');

            $cntr = 0;
            while ($crs = $cresults->fetch_assoc()) {
                $cntr++;
                //get 90 day sales
                $startdate = date("Y-m-d", strtotime("-90 days"));
                $date = date('Y-m-d');
                $ninetystmt = "select count(*) as pcnt from parts where deleted = 'no' and shopid = ? and `date` <= ? and `date` >= ? and PartNumber = ?";
                if ($nquery = $conn->prepare($ninetystmt)) {
                    $nquery->bind_param("ssss", $shopid, $date, $startdate, $crs['PartNumber']);
                    $nquery->execute();
                    $nquery->bind_result($ninetydays);
                    $nquery->fetch();
                    $nquery->close();
                }

                if (empty($ninetydays)) {
                    $ninetydays = 0;
                }
                //'get 12 mos sales
                $startdate = date("Y-m-d", strtotime("-12 months"));
                $m12stmt = "select count(*) as pcnt from parts where deleted = 'no' and shopid = ? and `date` <= ? and `date` >= ? and PartNumber = ?";
                if ($mquery = $conn->prepare($m12stmt)) {
                    $mquery->bind_param("ssss", $shopid, $date, $startdate, $crs['PartNumber']);
                    $mquery->execute();
                    $mquery->bind_result($twelvemos);
                    $mquery->fetch();
                    $mquery->close();
                }
                if (empty($twelvemos)) {
                    $twelvemos = 0;
                }
                if ($cntr >= 25) {
                    $cntr = 0;
                    ?>
                    <!--
                    <tr class="table_header">
                        <th>Part #</th>
                        <th style="width: 36px">Description</th>
                        <th style="width: 18%">Supplier</th>
                        <th class="text-right">Cost</th>
                        <th class="text-right">Alloc</th>
                        <th class="text-right">Net</th>
                        <th class="text-right">Reorder</th>
                        <th class="text-right">Max</th>
                        <th class="text-right">90_Days</th>
                        <th class="text-right">12_Mos</th>
                        <th class="text-right">Order</th>
                    </tr> -->
                    <?php
                    $alldata[] = array('TABLEHEAD', 'Part #', 'Description', 'Supplier', 'Cost', 'Alloc', 'Net', 'Reorder', 'Max', '90_Days', '12_Mos', 'Order');
                }

                $noh = $crs["NetOnHand"];
                $rol = $crs["ReOrderLevel"];
                $moh = $crs["MaxOnHand"];

                $allocated = 0;

                $aStmt = "SELECT COALESCE(SUM(p.quantity), 0) AS q
                           FROM repairorders ro
                           LEFT JOIN parts p ON ro.roid = p.roid
                           WHERE ro.shopid = ? 
                           AND ro.status != 'CLOSED' 
                           AND ro.ROType != 'No Approval'
                           AND p.partnumber = ?
                           AND ro.roid IN (SELECT roid FROM repairorders WHERE shopid = ? AND status != 'CLOSED' AND ROType != 'No Approval')";
            
                if ($aquery = $conn->prepare($aStmt)) {
                    $aquery->bind_param("sss", $shopid, $crs['PartNumber'], $shopid);
                    $aquery->execute();
                    $aquery->bind_result($allocated);
                    $aquery->fetch();
                    $aquery->close();
                }

                if ($moh > $noh) {
                    ?>
                    <tr>
                        <td><?= ucwords(strtolower($Category)) ?></td>
                        <td style="width: 10%">
                            <a target="_blank" class="text-secondary"
                               href='<?= COMPONENTS_PRIVATE ?>/v2/inventory/editinventory.php?pn=<?= urlencode($crs["partid"]) ?>'><?= $crs["PartNumber"] ?></a>
                        </td>
                        <td style="width: 15%"><?= $crs["PartDesc"] ?></td>
                        <td style="width: 10%"><?= $crs["PartSupplier"] ?></td>
                        <td style="width: 8%" class="text-right"><?= asDollars($crs["PartCost"]) ?></td>
                        <td style="width: 8%" class="text-right"><?= $allocated ?></td>
                        <td style="width: 8%" class="text-right"><?= $crs["NetOnHand"] ?></td>
                        <td style="width: 8%" class="text-right"><?= $crs["ReOrderLevel"] ?></td>
                        <td style="width: 8%" class="text-right"><?= $crs["MaxOnHand"] ?></td>
                        <td style="width: 8%" class="text-right"><?= $ninetydays ?></td>
                        <td style="width: 8%" class="text-right"><?= $twelvemos ?></td>
                        <td style="width: 8%" class="text-right">
                            <?php
                            $order = "";
                            if ($moh > $noh) {
                                $order = $moh - $noh;
                                $lineorderamt = $lineorderamt + (($moh - $noh) * $crs["PartCost"]);
                            }
                            echo $order;
                            ?>
                        </td>
                    </tr>
                    <?php
                    $alldata[] = array(strtoupper($crs["PartNumber"]), strtoupper($crs["PartDesc"]), strtoupper($crs["PartSupplier"]), asDollars($crs["PartCost"]), $allocated, $crs["NetOnHand"], $crs["ReOrderLevel"], $crs["MaxOnHand"], $ninetydays, $twelvemos, $order);
                }
            }
            } //crs enf if

            $alldata[] = array('', '', '', '', '', '', '', '', '', '', '');
            $catlist = $catlist . $Category . "~" . $lineorderamt . ",";

            $catlistArr[] = $Category . "~" . $lineorderamt;

            }
            } else {
                echo "No Parts Matrix Category found";
            }
            $query->close();
            }
            ?>

            </tbody>
        </table>
        <p></p>
        <div id="summary_table">
        <table class="table table-bordered w-50 float-end" style="">
            <?php
            $alldata[] = array('TABLEHEAD', '', '', '', '', '', '', '', 'Category', '', 'Amount', '');

            //$car = explode(",", $catlist);
            $runcost = 0;
            foreach ($catlistArr as $car) {
                $tar = explode("~", $car);
                ?>
                <tr>
                    <td width="50%"><?= $tar[0] ?>
                    </td>
                    <td align=right width="50%"><?= asDollars($tar[1]) ?></td>
                </tr>
                <?php
                $runcost = $runcost + $tar[1];
                $alldata[] = array('', '', '', '', '', '', '', $tar[0], '', asDollars($tar[1]), '');
            }

            if ($runcost > 0) {
                ?>
                <tr>
                    <td width="50%">Total Order Cost:</td>
                    <td align=right width="50%"><?= asDollars($runcost) ?>
                    </td>
                </tr>
                <?php
                $alldata[] = array('', '', '', '', '', '', '', 'Total Order', '', asDollars($runcost), '');
            }
            ?>
        </table>
        </div>
        <div class="clearfix"></div>
    </div>
</main>
<?php
//get supplier email
if ($showemail == "yes") {
    $supplieremail = $suppliername = $companyname = "";
    $stmt = "select supplieremail,suppliername from supplier where shopid = ? and suppliername = ?";
    if ($tquery = $conn->prepare($stmt)) {
        $tquery->bind_param("ss", $shopid, $supplier);
        $tquery->execute();
        $tquery->bind_result($supplieremail, $suppliername);
        $tquery->fetch();
        $tquery->close();
    }
    if (!empty($supplieremail)) {
        $stmt = "select companyemail from company where shopid = ?";
        if ($xquery = $conn->prepare($stmt)) {
            $xquery->bind_param("s", $shopid);
            $xquery->execute();
            $xquery->bind_result($companyemail);
            $xquery->fetch();
            $xquery->close();
        }

        ?>
        <script type="text/javascript">
            document.getElementById("email").style.visibility = "visible"
            document.getElementById("emailaddress").value = '<?= $supplieremail?>'
            document.getElementById("shopemailaddress").value = '<?= $companyemail ?>'
            document.getElementById("suppliername").value = '<?= $suppliername ?>'
        </script>
        <?php
    }
}
?>
<script type="text/javascript">


    x = setTimeout("closeSupplier()", 3000)


    function closeSupplier() {

        //document.getElementById("popup").style.display = "none"
        //document.getElementById("popuphider").style.display = "none"
        //document.getElementById("popupdropshadow").style.display = "none"
        //window.print();
        //location.href='../reportsnew.asp'
    }

    function printit() {
        document.getElementById("print").style.display = "none"
        window.print()
        setTimeout("showemaildiv()", 2000)
    }

    function showemaildiv() {
        document.getElementById("print").style.display = "block"
    }

    function GetXmlHttpObject() {
        var objXMLHttp = null
        if (window.XMLHttpRequest) {
            objXMLHttp = new XMLHttpRequest()
        } else if (window.ActiveXObject) {
            objXMLHttp = new ActiveXObject("Microsoft.XMLHTTP")
        }
        return objXMLHttp
    }

    function sendEmail(t) {

        xmlHttp = GetXmlHttpObject()
        if (xmlHttp == null) {
            alert("Browser does not support HTTP Request")
            return
        }

        // first get the next po number
        url = "pogetnextponumber.asp?shopid=<?= $shopid ?>"

        xmlHttp.onreadystatechange = stateChanged
        xmlHttp.open("GET", url, true)
        xmlHttp.send(null)
    }

    function stateChanged() {
        //
        if (xmlHttp.readyState == 4 || xmlHttp.readyState == "complete") {
            ponumber = xmlHttp.responseText
            c = confirm("A new PO Number " + ponumber + " will be created and sent to " + document.getElementById("suppliername").value + ". Click OK to continue or Cancel to stop")

            if (c) {
                xmlHttp = GetXmlHttpObject()
                if (xmlHttp == null) {
                    alert("Browser does not support HTTP Request")
                    return
                }

                path = 'sendinventoryorder.asp?shopid=<?= $shopid ?>&ponumber=' + ponumber + '&shopemail=' + document.getElementById("shopemailaddress").value + '&sendemail=' + document.getElementById("emailaddress").value + '&suppliername=<?= urlencode($supplier) ?>'
                xmlHttp.onreadystatechange = sendEmailStateChanged
                xmlHttp.open("GET", path, true)
                xmlHttp.send(null)

            }
        }
    }

    function sendEmailStateChanged() {
        //
        if (xmlHttp.readyState == 4 || xmlHttp.readyState == "complete") {
            rt = xmlHttp.responseText
            alert(rt)
        }
    }

</script>

<?php

require COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form.php";

include getScriptsGlobal($component);

$dateRangeDisplay =  date('m/d/Y');
?>

<script>
    $(document).ready(function () {
        groupCol_writer = 0;
        let table = $("#inv_order_table").DataTable({
            responsive: true,
            fixedHeader: {
                headerOffset: 68
            },
            colReorder: true,
            select: true,
            scrollY: false,
            scrollX: false,
            scroller: false,
            paging: false,
            language: {
                searchPanes: {
                    emptyPanes: 'No records found in this date range'
                },
                search: "_INPUT_",
                searchPlaceholder: "Search..."
            },
            buttons: [
                {
                    extend: 'csv',
                    text: '<i class="fas fa-file-csv fa-xl"></i>',
                    title: "<?= (!empty($title) ? $title : 'Shop Boss Report').$companyNamePrint ?>",
                },
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel fa-xl"></i>',
                    title: "<?= (!empty($title) ? $title : 'Shop Boss Report').$companyNamePrint ." (".$dateRangeDisplay.")" ?>",
                    exportOptions: {
                        // Any other settings used
                        columns: [ ':visible' ],
                        grouped_array_index: 0
                    },
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print fa-xl"></i>',
                    title: "<?= (!empty($title) ? $title : 'Shop Boss Report').$companyNamePrint ?>",
                    messageTop : "<?= $dateRangeDisplay; ?>",
                    exportOptions: {
                        // Any other settings used
                        columns: [ ':visible' ],
                        grouped_array_index: 0
                    },
                    customize: function ( win ) {
                        $(win.document.body)
                            .append(
                               $("#summary_table").html()
                            );
                    },
                    footer: true,
                }
            ],
            dom: "<'row'<'col-sm-12 col-md-6 dt_Search text-secondary'f><'col-sm-12 col-md-6 text-right dt_btns text-secondary'B>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
            columnDefs: [{visible: false, targets: groupCol_writer}],
            order: [[groupCol_writer, 'asc']],
            orderFixed: [0, 'asc'],
            rowGroup: {
                dataSrc: 0,
                endClassName: 'table_total text-right',
                startClassName: 'group_heading'
            },
        });
    });
</script>

<?php
include getFooterComponent($component);
?>
