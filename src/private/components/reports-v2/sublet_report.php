<?php
$component = "reports-v2";

include getRulesGlobal($component);
include getHeadGlobal($component);
// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));


// Page Variables
$title = 'Sublet Report';  // Report Title Goes Here
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="title col breadcrumb d-flex align-items-center">
                <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                    class="text-secondary ps-3 pe-3">/</span>
                <h2><?= $title; ?></h2>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report">
        <?php
        include_once "inline_prompt.php";
        ?>
        <table class="dtable" style="width: 100%">
            <thead>
            <tr class="table_header table_head">
                <th>RO #</th>
                <th>RO Close Date</th>
                <th>Customer</th>
                <th>Source</th>
                <th>VIN</th>
                <th>Description</th>
                <th class="calc_total">Price</th>
                <th>Supplier</th>
                <th class="calc_total">Shop Cost</th>
            </tr>
            </thead>
            <tbody>

            <?php
            $tablefields = array('RO #', 'RO Close Date', 'Customer', 'Source', 'VIN', 'Description', 'Price', 'Supplier', 'Shop Cost');//set table headings array for excel export
            $alldata = array();//this will hold all the data arrays to be exported to excel


            // Insert DB Query Here

            // Template Query Begins - Replace entire section


            $stmt = "SELECT s.complaintid, s.SubletDesc, s.SubletPrice, s.SubletCost, s.SubletSupplier, r.roid, r.statusdate,r.customer,r.source,r.vin";
            $stmt .= " FROM repairorders r ";
            $stmt .= " LEFT JOIN sublet s ";
            $stmt .= "   ON r.shopid = s.shopid and r.roid = s.roid ";
            $stmt .= "WHERE r.shopid = '$shopid' ";
            $stmt .= "  AND r.status = 'CLOSED' and s.deleted = 'no' ";
            $stmt .= "  AND r.statusdate >= ? And r.statusdate <= ? ";
            $stmt .= " ORDER BY subletsupplier, r.statusdate ";

            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("ss", $sd1, $ed1);
                $query->execute();
                $roresult = $query->get_result();
            } else {
                echo "Repair Orders Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            //printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$sdate,$edate);
            $tcost = $tprice = 0;
            if ($roresult->num_rows > 0) {
            while ($ro = $roresult->fetch_array()) {
                // test for any ros prior to startdate

                $statusdate = new DateTime ($ro["statusdate"]);
                $complaintid = $ro["complaintid"];

                $stmt = "SELECT complaintid,cstatus";
                $stmt .= " FROM complaints ";
                $stmt .= "WHERE shopid = '$shopid' ";
                $stmt .= "  AND complaintid = ? ";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $complaintid);
                    $query->execute();
                    $compresult = $query->get_result();
                } else {
                    echo "Complaint Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $comp = $compresult->fetch_array();

                if (strtolower($comp["cstatus"]) == 'no') {
                    ?>
                    <tr>
                        <td><?= $ro["roid"]; ?></td>
                        <td><?= date_format($statusdate, 'm/d/Y'); ?></td>
                        <td><?= $ro["customer"]; ?></td>
                        <td><?= $ro["source"]; ?></td>
                        <td><?= $ro["vin"]; ?></td>
                        <td><?= $ro["SubletDesc"]; ?></td>
                        <td><?= asDollars($ro["SubletPrice"], 2); ?></td>
                        <td><?= $ro["SubletSupplier"]; ?></td>
                        <td><?= asDollars($ro["SubletCost"], 2); ?></td>
                    </tr>
                    <?php
                    $alldata[] = array($ro["roid"], date_format($statusdate, 'm/d/Y'), strtoupper($ro["customer"]), strtoupper($ro["source"]), strtoupper($ro["vin"]), strtoupper($ro["SubletDesc"]), asDollars($ro["SubletPrice"], 2), strtoupper($ro["SubletSupplier"]), asDollars($ro["SubletCost"], 2));//fill up the alldata array with the arrays of data to be shown in excel export
                    $tcost += $ro['SubletCost'];
                    $tprice += $ro['SubletPrice'];

                } // end if
            } // end while
            ?>
            </tbody>
            <tfoot>
            <tr class="table_total">
                <td>TOTALS</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td><?= asDollars($tprice) ?></td>
                <td></td>
                <td><?= asDollars($tcost) ?></td>
            </tr>
            </tfoot>
            <?php
            $alldata[] = array('TOTALS', '', '', '', '', '', asDollars($tprice), '', asDollars($tcost));
            } // end if
            $alldata[] = array('', '', '', '', '', '', '', '', '');


            ?>
        </table>
    </div>
</main>
<?php

require COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form.php";

include getScriptsGlobal($component);
include getFooterComponent($component);
?>

