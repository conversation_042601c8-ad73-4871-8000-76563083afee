<?php
$component = "reports-v2";
//To view this report with Finalized and Closed RO's, click here
//Click an RO number to view the RO
include getHeadGlobal($component);
include getRulesGlobal($component);

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));
$job_completed = isset($_REQUEST['job_completed']) ? filter_var($_REQUEST['job_completed'], FILTER_SANITIZE_STRING) : "";
$ro_status = isset($_REQUEST['ro_status']) ? filter_var($_REQUEST['ro_status'], FILTER_SANITIZE_STRING) : "Closed";
$path = $_SERVER['HTTP_HOST'];

// Page Variables
$title = 'Technician Detail Report';  // Report Title Goes Here

$subtitle = 'This report includes ' . $ro_status . ' status Repair Orders';  // Report SubTitle Goes Here - Hide if not needed

if ($job_completed == "yes") {
    $subtitle .= " and job status marked complete";
}
// Use this for Gen Pop Reports
// $template = 'excelexport.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
$template = COMPONENTS_PRIVATE . "/reports/templates/gg_excellexport.php"; //Only change if a custom PHPExcel is created in the template folder
?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);

$sdate = date_format(new DateTime($sd), 'Y-m-d');
$edate = date_format(new DateTime($ed), 'Y-m-d')

?>
    <main id="reports" class="min-vh-100">
        <div class="report">
            <div class="col-12">
                <div class="row">
                    <div class="col--sm-12 col-md-10">
                        <div class="title col breadcrumb d-flex align-items-center">
                            <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                                    class="text-secondary ps-3 pe-3">/</span>
                            <h2><?= $title; ?></h2>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <?php
                        if ($_COOKIE['empid'] == 'Admin') {
                            ?>
                            <button type="button" class="btn btn-secondary btn-md d-print-none float-end"
                                    onclick="location.href='<?= COMPONENTS_PRIVATE ?>/v2/report_builder/builder.php?preset=tech_det&op=create_new'">Edit This Report
                            </button>
                            <?php
                        }
                        ?>
                    </div>
                </div>
                <hr/>
                <p class="card-title">
                    <?php
                    $dateRangeDisplay = "";
                    if ((!empty($sd))) {
                        $dateRangeDisplay = $sd;
                    }
                    if (!empty($sd) && !empty($ed)) {
                        $dateRangeDisplay .= ' to ';
                    }
                    if (!empty($ed)) {
                        $dateRangeDisplay .= $ed;
                    }
                    if (empty($sd) && empty($ed)) {
                        $dateRangeDisplay = date('m/d/Y');
                    }

                    echo $dateRangeDisplay;
                    ?>
                </p>
                <p class="">
                    <?= $subtitle ?>
                </p>
            </div>
        </div>
        <div class="report">
            <?php
            include_once "inline_prompt.php";
            ?>
            <table class="tech_report w-100" id="tech_report" style="width: 100%">
                <thead>
                <tr class="table_header table_head">
                    <th>Technician</th>
                    <th>RO #</th>
                    <th>Status</th>
                    <th>Status Date</th>
                    <?php if ($job_completed == "yes") { ?>
                        <th>Job Complete Date</th><?php } ?>
                    <th>Customer</th>
                    <th>Vehicle</th>
                    <th>Labor Description</th>
                    <th class="text-right calc">Labor Hours</th>
                    <th class="text-right">Sold Rate</th>
                    <th class="text-right calc">Total Labor</th>
                    <th class="text-right">Tech Rate</th>
                    <th class="text-right calc">Tech Pay</th>
                </tr>
                </thead>
                <tbody>
                <?php

                $roStatusSQL = "";
                if ($ro_status != "all") {
                    if ($ro_status != "open") {
                        $roStatusSQL = "AND ro.status = '" . $ro_status . "'";
                    } else {
                        $roStatusSQL = "AND ro.status != 'Closed'";
                    }
                }

                $jobSQL = "";
                if (!empty($job_completed) && $job_completed == "yes") {
                    $jobSQL = "AND c.acceptdecline = 'Job Complete'";
                }


                $tablefields = array('Tech', 'RO #', 'Status', 'Status Date', 'Customer', 'Vehicle', 'Labor Description', 'Labor Hours', 'Sold Rate', 'Total Labor', 'Tech Rate', 'Tech Pay');//set table headings array for excel export
                $alldata = array();//this will hold all the data arrays to be exported to excel

                $stmt = "SELECT replacerowithtag FROM company WHERE shopid = '$shopid' ";
                if ($query = $conn->prepare($stmt)) {
                    $query->execute();
                    $query->bind_result($replacerowithtag);
                    $query->fetch();
                    $query->close();
                }

                if ($_GET['empid'] == "all") {

                    $stmt = "select distinct tech from labor l,repairorders ro where ro.shopid = l.shopid and ro.roid = l.roid AND l.Tech != 'discount, discount' AND ro.rotype != 'No Approval' AND l.deleted = 'no' $roStatusSQL and l.shopid = ? AND ro.statusdate >= ? AND ro.statusdate <= ? ORDER BY tech";

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("sss", $shopid, $sdate, $edate);
                        $query->execute();
                        $lbrresult = $query->get_result();
                    } else {
                        "Labor Prepare failed (" . $conn->errorno . ")" . $conn->error;
                    }
                    //echo $stmt;
                    //printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$tech,$sdate,$edate);

                    if ($lbrresult->num_rows > 0) {
                        while ($lbr = $lbrresult->fetch_array()) {

                            $totallbrhrs = 0;
                            $totallabor = 0;
                            $totalcurrentpay = 0;
                            $tech = $lbr["tech"];

                            $tar = explode(",", $tech);
                            $techlast = trim($tar[0], " ");
                            if (!empty($tar[1])) {
                                $techfirst = trim($tar[1], " ");
                            } else {
                                $techfirst = "";
                            }

                            $tech2 = $techlast . ',' . $techfirst;

                            if (in_array($tech, $techarr)) continue;

                            $techarr[] = $tech;

                            $stmt = "SELECT ro.roid,ro.tagnumber,ro.rotype,ro.customer,ro.vehinfo,ro.status,ro.statusdate,ro.datetimepromised,l.laborhours,l.Tech, l.techrate,l.hourlyrate,l.linetotal,l.labor,c.datedone ";
                            $stmt .= " FROM repairorders ro ";
                            $stmt .= " JOIN labor l ";
                            $stmt .= "   ON ro.shopid = l.shopid and ro.roid = l.roid ";
                            $stmt .= " JOIN complaints c ON c.complaintid = l.complaintid AND l.roid = c.roid AND l.shopid = c.shopid ";
                            $stmt .= " WHERE ro.shopid = ?";
                            $stmt .= "  AND l.tech = ? ";
                            $stmt .= "  AND l.Tech != 'discount, discount' AND ro.rotype != 'No Approval' AND l.deleted = 'no' ";
                            $stmt .= "  AND ro.statusdate >= ? AND ro.statusdate <= ? ";
                            $stmt .= $roStatusSQL . " " . $jobSQL;
                            $stmt .= "  ORDER BY ro.roid ";

                            //echo sprintf(str_replace("?","'%s'", $stmt), $shopid,$tech,$sdate,$edate);

                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("ssss", $shopid, $tech, $sdate, $edate);
                                $query->execute();
                                $roresult = $query->get_result();
                            } else {
                                "RO Prepare failed (" . $conn->errorno . ")" . $conn->error;
                            }

                            if ($roresult->num_rows > 0) {
                                while ($ro = $roresult->fetch_array()) {

                                    $statusdate = new Datetime($ro["statusdate"]);

                                    $totallbrhrs = $totallbrhrs + $ro["laborhours"];
                                    $totallabor = $totallabor + $ro["linetotal"];

                                    $paytype = "";
                                    $payrate = 0;
                                    $rtechpay = 0;

                                    $stmt = "SELECT concat(employeelast,', ',employeefirst) emp, hourlyrate,paytype ";
                                    $stmt .= " FROM employees ";
                                    $stmt .= " WHERE shopid = ?";
                                    $stmt .= "   AND (concat(employeelast,', ',employeefirst) = ? || concat(employeelast,employeefirst) = ?) AND active = 'yes'";
                                    if ($query = $conn->prepare($stmt)) {
                                        $query->bind_param("sss", $shopid, $tech, $tech2);
                                        $query->execute();
                                        $empresult = $query->get_result();

                                    } else {
                                        echo "Employee prepare failed  (" . $conn->errno . ")" . $conn->error;
                                    }
                                    //printf(str_replace("?","'" . "%s" . "'",$stmt),$shopid,$tech)  ;

                                    $emp = mysqli_fetch_assoc($empresult);

                                    if ($empresult->num_rows > 0) {

                                        $paytype = $emp["paytype"];
                                        $payrate = $emp["hourlyrate"];

                                        $laborchg = $ro["linetotal"];
                                        if (strtolower($paytype) == "percentage") {
                                            if (is_numeric($payrate) and $payrate > 0) {
                                                //echo $ro["tech"] . ":" . $paytype . ":" . $payrate . ":" . $linetotal .  "<BR>";

                                                if ($linetotal == 0) {
                                                    //echo $ro["tech"] . ":" . $paytype . ":" . $payrate . "<BR>";
                                                    // calculate the charged labor and
                                                    $linetotal = $ro["laborhours"] * $ro["memorate"];
                                                    $techpay = asDollars(round($linetotal * ($payrate / 100), 2), 2);
                                                    //echo $ro["laborhours"] . ":" . $ro["hourlyrate"] . ":" . $linetotal . ":" . $techpay . "<BR>";
                                                } else {
                                                    //$techpaydisplay = asDollars(round($laborchg * ($payrate/100),2),2);
                                                    $techpay = round($laborchg * ($payrate / 100), 2);
                                                }
                                            } else {
                                                $techpay = 0.00;
                                            }

                                        } elseif ($paytype == "flatrate") {
                                            $techpay = $ro["laborhours"] * $payrate;
                                            $rtechpay = $rtechpay + $techpay;

                                        } elseif (strtoupper($paytype) == "HOURLY") {
                                            $techpay = $ro["laborhours"] * $payrate;
                                            $rtechpay = $rtechpay + $techpay;
                                        }
                                        if (floatval($ro["hourlyrate"]) > 0) {
                                            $currentpay = ($emp["hourlyrate"] * $ro["laborhours"]);
                                        } else {
                                            $currentpay = 0;
                                        } // end of current pay

                                        $totalcurrentpay = $totalcurrentpay + $currentpay;

                                        // rlk pulling l.laborhours vs lbrhrs * hrhly rate 1/13/20
                                        $rostatus = $ro["status"];
                                        if(is_numeric(substr($rostatus,0,1))){
                                            $rostatus = substr($rostatus,1);
                                        }
                                        ?>

                                        <!-- Table Results Begin -->
                                        <tr class="">
                                            <td><?= ucwords(strtolower($ro["Tech"])); ?></td>
                                            <td><?= $ro["roid"]; ?></td>
                                            <td><?= ucwords(strtolower($rostatus)); ?></td>
                                            <td><?= date_format($statusdate, 'm/d/Y'); ?></td>
                                            <?php if ($job_completed == "yes") { ?>
                                                <td><?= (!empty($ro['datedone']) && $ro['datedone'] != '0000-00-00') ? date('m/d/Y', strtotime($ro['datedone'])) : '' ?></td><?php } ?>
                                            <td><?= ucwords(strtolower($ro["customer"])); ?></td>
                                            <td><?= substr(ucwords(strtolower($ro["vehinfo"])), 0, 30); ?></td>
                                            <td><?= substr(ucwords(strtolower($ro["labor"])), 0, 50); ?></td>
                                            <td style="text-align:right"><?php echo $ro["laborhours"]; ?></td>
                                            <td style="text-align:right"><?php echo asDollars($ro["hourlyrate"]); ?></td>
                                            <td style="text-align:right"><?php echo asDollars($ro["linetotal"]); ?></td>
                                            <td style="text-align:right"><?php echo asDollars($payrate); ?></td>
                                            <td style="text-align:right"><?php echo asDollars($currentpay); ?></td>
                                        </tr>

                                        <?php
                                        $alldata[] = array($ro["Tech"], $ro["roid"], strtoupper($rostatus), date_format($statusdate, 'm/d/Y'), strtoupper($ro["customer"]), strtoupper($ro["vehinfo"]), strtoupper($ro["labor"]), floatval($ro["laborhours"]), asDollars($ro["hourlyrate"]), asDollars($ro["linetotal"]), asDollars($payrate), asDollars($currentpay)); //fill up the alldata array with the arrays of data to be shown in excel export
                                    } // end of emp if
                                } // end of ro while loop
                                ?>
                                <!--
		<tr class="table_total">
		 	<td><b>TOTAL</b></td>
		 	<td></td>
		 	<td></td>
		 	<td></td>
		 	<?php if ($job_completed == "yes") { ?><td></td><?php } ?>
		 	<td></td>
		 	<td></td>
		 	<td><b><?= $techfirst . " " . $techlast; ?></b></td>
		 	<td style="text-align:right"><b><?php echo $totallbrhrs; ?></b></td>
		 	<td></td>
		 	<td style="text-align:right"><b><?php echo asDollars($totallabor, 2); ?></b></td>
		 	<td></td>
		 	<td style="text-align:right"><b><?php echo asDollars($totalcurrentpay); ?></b></td>
		 </tr>
		 -->
                                <?php
                                $alldata[] = array('', '', '', '', '', '', '', '', '', '', '', '');
                                $alldata[] = array('TOTAL', '', '', '', '', 'Hours for', $techfirst . " " . $techlast, floatval($totallbrhrs), '', asDollars($totallabor, 2), '', asDollars($totalcurrentpay));
                            } // end if for ro

                        } // end of labor while for loop
                    } // end if for labor end of file

// individual technician
                } else {
                    $tech = $_GET['empid'];

                    $totallbrhrs = 0;
                    $totallabor = 0;
                    $totalcurrentpay = 0;
                    $tar = explode(",", $tech);
                    $techlast = trim($tar[0], " ");
                    if (!empty($tar[1])) {
                        $techfirst = trim($tar[1], " ");
                    } else {
                        $techfirst = "";
                    }

                    $stmt = "SELECT ro.roid,ro.tagnumber,ro.rotype,ro.customer,ro.vehinfo,ro.status,ro.statusdate,ro.datetimepromised,l.laborhours,l.Tech, l.techrate,l.hourlyrate,l.linetotal,l.labor,c.datedone ";
                    $stmt .= " FROM repairorders ro ";
                    $stmt .= " JOIN labor l ";
                    $stmt .= "   ON ro.shopid = l.shopid and ro.roid = l.roid ";
                    $stmt .= " JOIN complaints c ON c.complaintid = l.complaintid AND l.roid = c.roid AND l.shopid = c.shopid ";
                    $stmt .= " WHERE ro.shopid = ?";
                    $stmt .= "  AND l.Tech = ? ";
                    $stmt .= "  AND l.Tech != 'discount, discount' AND ro.rotype != 'No Approval' AND l.deleted = 'no' ";
                    $stmt .= $jobSQL . " " . $roStatusSQL;
                    $stmt .= "  AND ro.statusdate >= ? AND ro.statusdate <= ? ";
                    $stmt .= "  ORDER BY ro.roid ";

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("ssss", $shopid, $tech, $sdate, $edate);
                        $query->execute();
                        $roresult = $query->get_result();
                    } else {
                        "RO Prepare failed (" . $conn->errorno . ")" . $conn->error;
                    }
                    if ($roresult->num_rows > 0) {
                        while ($ro = $roresult->fetch_array()) {

                            $statusdate = new Datetime($ro["statusdate"]);

                            $totallbrhrs = $totallbrhrs + $ro["laborhours"];
                            $totallabor = $totallabor + $ro["linetotal"];


                            $paytype = "";
                            $payrate = 0;
                            $rtechpay = 0;

                            $stmt = "SELECT concat(employeelast,', ',employeefirst) emp, hourlyrate,paytype ";
                            $stmt .= " FROM employees ";
                            $stmt .= " WHERE shopid = ?";
                            $stmt .= "   AND concat(employeelast,', ',employeefirst) = ? ";
                            if ($query = $conn->prepare($stmt)) {
                                $query->bind_param("ss", $shopid, $tech);
                                $query->execute();
                                $empresult = $query->get_result();

                            } else {
                                echo "Employee prepare failed  (" . $conn->errno . ")" . $conn->error;
                            }
                            //printf(str_replace("?","'" . "%s" . "'",$stmt),$shopid,$tech)  ;

                            $emp = mysqli_fetch_assoc($empresult);

                            if ($empresult->num_rows > 0) {

                                $paytype = $emp["paytype"];
                                $payrate = $emp["hourlyrate"];

                                $laborchg = $ro["linetotal"];
                                if (strtolower($paytype) == "percentage") {
                                    if (is_numeric($payrate) and $payrate > 0) {

                                        if ($linetotal == 0) {
                                            $linetotal = $ro["laborhours"] * $ro["memorate"];
                                            $techpay = asDollars(round($linetotal * ($payrate / 100), 2), 2);
                                        } else {
                                            $techpay = round($laborchg * ($payrate / 100), 2);
                                        }
                                    } else {
                                        $techpay = 0.00;
                                    }

                                } elseif ($paytype == "flatrate") {
                                    $techpay = $ro["laborhours"] * $payrate;
                                    $rtechpay = $rtechpay + $techpay;

                                } elseif (strtoupper($paytype) == "HOURLY") {
                                    $techpay = $ro["laborhours"] * $payrate;
                                    $rtechpay = $rtechpay + $techpay;
                                }
                                if (floatval($ro["hourlyrate"]) > 0) {
                                    $currentpay = ($emp["hourlyrate"] * $ro["laborhours"]);
                                } else {
                                    $currentpay = 0;
                                } // end of current pay

                                $totalcurrentpay = $totalcurrentpay + $currentpay;
                                // rlk pulling l.laborhours vs lbrhrs * hrhly rate 1/13/20
                                $rostatus = $ro["status"];
                                if(is_numeric(substr($rostatus,0,1))){
                                    $rostatus = substr($rostatus,1);
                                }
                                ?>

                                <!-- Table Results Begin -->
                                <tr class="">
                                    <td><?= ucwords(strtolower($ro["Tech"])); ?></td>
                                    <td><?= $ro["roid"]; ?></td>
                                    <td><?= strtoupper($rostatus); ?></td>
                                    <td><?= date_format($statusdate, 'm/d/Y'); ?></td>
                                    <?php if ($job_completed == "yes") { ?>
                                        <td><?= (!empty($ro['datedone']) && $ro['datedone'] != '0000-00-00') ? date('m/d/Y', strtotime($ro['datedone'])) : '' ?></td><?php } ?>
                                    <td><?= strtoupper($ro["customer"]); ?></td>
                                    <td><?= substr(strtoupper($ro["vehinfo"]), 0, 30); ?></td>
                                    <td><?= substr(strtoupper($ro["labor"]), 0, 50); ?></td>
                                    <td style="text-align:right"><?php echo $ro["laborhours"]; ?></td>
                                    <td style="text-align:right"><?php echo asDollars($ro["hourlyrate"]); ?></td>
                                    <td style="text-align:right"><?php echo asDollars($ro["linetotal"]); ?></td>
                                    <td style="text-align:right"><?php echo asDollars($payrate); ?></td>
                                    <td style="text-align:right"><?php echo asDollars($currentpay); ?></td>
                                </tr>

                                <?php
                                $alldata[] = array($ro["Tech"], $ro["roid"], strtoupper($rostatus), date_format($statusdate, 'm/d/Y'), strtoupper($ro["customer"]), strtoupper($ro["vehinfo"]), strtoupper($ro["labor"]), floatval($ro["laborhours"]), asDollars($ro["hourlyrate"]), asDollars($ro["linetotal"]), asDollars($payrate), asDollars($currentpay)); //fill up the alldata array with the arrays of data to be shown in excel export
                            } // end of emp if
                        } // end of ro while loop
                        ?>
                        <!--
		<tr class="table_total">
		 	<td><b>TOTAL</b></td>
		 	<td></td>
		 	<td></td>
		 	<td></td>
		 	<td></td>
		 	<td></td>
		 	<td><b><?= $techfirst . " " . $techlast; ?></b></td>
		 	<td style="text-align:right"><b><?php echo $totallbrhrs; ?></b></td>
		 	<td></td>
		 	<td style="text-align:right"><b><?php echo asDollars($totallabor, 2); ?></b></td>
		 	<td></td>
		 	<td style="text-align:right"><b><?php echo asDollars($totalcurrentpay); ?></b></td>
		 </tr>
		 -->
                        <?php
                        $alldata[] = array('', '', '', '', '', '', '', '', '', '', '', '');
                        $alldata[] = array('TOTAL', '', '', '', '', 'Hours for', $techfirst . " " . $techlast, floatval($totallbrhrs), '', asDollars($totallabor, 2), '', asDollars($totalcurrentpay));
                    } // end if for ro

                } // end if of employee all

                ?>
                </tbody>
            </table>
        </div>
    </main>
<?php
include(COMPONENTS_PRIVATE_PATH . '/reports-v2/includes/report_form.php');

include getScriptsGlobal($component);


$dateRangeDisplay = ($_REQUEST['sdate'] ?? "") . " " . (!empty($_REQUEST['edate']) ? " to " . $_REQUEST['edate'] : "");
$subtitle = !empty($subtitle) ? $subtitle : "";

?>
    <script>
        $(document).ready(function () {
            //7, 9, 11
            let CalcT = Array();
            //console.log($("#tech_report thead"));
            $("#tech_report thead").find("th").each(function (idx, v) {
                if ($(v).hasClass("calc")) {
                    CalcT.push(idx);
                }
            });

            //console.log(CalcT);

            let floatVal = function (i) {
                return typeof i === 'string' ? Number(i.replace(/[$,]/g, '')) : typeof i === 'number' ? i : 0;
            };

            groupCol_writer = 0;
            let table = $("#tech_report").DataTable({
                responsive: true,
                fixedHeader: {
                    headerOffset: 60
                },
                colReorder: true,
                select: true,
                scrollY: false,
                scrollX: false,
                scroller: false,
                paging: false,
                language: {
                    searchPanes: {
                        emptyPanes: 'No records found in this date range'
                    },
                    search: "_INPUT_",
                    searchPlaceholder: "Search..."
                },
                buttons: [
                    {
                        extend: 'csv',
                        text: '<i class="fas fa-file-csv fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                        messageTop: "<?= $dateRangeDisplay ?>",
                    },
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint . " (" . $dateRangeDisplay . ")" ?>",
                        messageTop: "<?= $subtitle ?>",
                        exportOptions: {
                            // Any other settings used
                            columns: [':visible'],
                            grouped_array_index: 0
                        },
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                        exportOptions: {
                            // Any other settings used
                            stripHtml: false,
                            columns: [':visible'],
                            grouped_array_index: 0
                        },
                        messageTop: "<?= $dateRangeDisplay . "<br/>" . $subtitle ?>",
                        footer: true,
                    }
                ],
                dom: "<'row'<'col-sm-12 col-md-6 dt_Search text-secondary'f><'col-sm-12 col-md-6 text-right dt_btns text-secondary'B>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
                columnDefs: [{visible: false, targets: groupCol_writer}],
                order: [[groupCol_writer, 'asc']],
                orderFixed: [0, "asc"],
                rowGroup: {
                    endRender: function (rows, group) {

                        let totsArr = Array();

                        CalcT.forEach(function (val, idx) {
                            var col = rows.data().pluck(val);
                            totsArr[val] = col.reduce(function (a, b) {
                                var fl_a = floatVal(a);
                                var fl_b = floatVal(b);
                                if (isNaN(fl_a)) {
                                    fl_a = 0;
                                }
                                if (isNaN(fl_b)) {
                                    fl_b = 0;
                                }
                                sum = fl_a + fl_b;
                                return (typeof sum != "undefined" || !isNaN(sum)) ? sum : 0;
                            }, 0);
                        });
                        var tr = rows.data();
                        var numcol = tr[0].length;

                        let formatting_options = {
                            style: 'currency',
                            currency: 'USD',
                            minimumFractionDigits: 2,
                        };
                        let USDollar = new Intl.NumberFormat('en-US', formatting_options);
                        var gname = group.replace(/[^A-Z0-9]/ig, "_");
                        // console.log('numcol', numcol);
                        if (typeof numcol !== "undefined" && numcol > 0) {
                            //   console.log();
                            var total_row = $('<tr id="' + gname + '_totals">');
                            total_row.append('<td colspan="2" class="text-left">' + group + ' TOTALS</td>')
                            for (var i = 3; i < numcol; i++) {

                                if (typeof totsArr[i] !== "undefined" || totsArr[i] != null) {
                                    var totVal = totsArr[i].toFixed(2);
                                    // console.log(totsArr[i], !isNaN(totsArr[i]));
                                    if (!isNaN(totsArr[i]) && rows.data().pluck(i)[0].indexOf('$') >= 0) {
                                        totVal = USDollar.format(totsArr[i])
                                    }
                                    total_row.append('<td class="text-right">' + totVal + '</td>')
                                    // console.log("Adding total")
                                } else {
                                    total_row.append('<td class=""></td>')
                                }
                            }
                            total_row.append("</tr>");
                            return total_row;
                        }

                    },
                    dataSrc: 0,
                    endClassName: 'table_total text-right',
                    startClassName: 'group_heading'
                },

            });
        });
    </script>
<?php
include getFooterComponent($component);
?>