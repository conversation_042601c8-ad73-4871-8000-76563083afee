<?php
// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$component = "reports-v2";
$sd = '';
$ed = '';
$title = "Work In Process with Advisor";
$subtitle = 'This Report Is Sorted By Advisor';  // Report SubTitle Goes Here - Hide if not needed
$todaydate = "todaydate";

include getHeadGlobal($component);
include getRulesGlobal($component);
include getHeaderGlobal($component);
include getMenuGlobal($component);

$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_multiple.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
    <main id="reports">
        <div class="report">
            <div class="col-12">
                <div class="title col breadcrumb d-flex align-items-center">
                    <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                        class="text-secondary ps-3 pe-3">/</span>
                    <h2><?= $title; ?></h2>
                </div>
                <hr/>
                <p class="card-title">
                    <?php
                    if ((!empty($sd))) {
                        echo($sd);
                    }
                    if (!empty($sd) && !empty($ed)) {
                        echo ' to ';
                    }
                    if (!empty($ed)) {
                        echo $ed;
                    }
                    if (empty($sd) && empty($ed)) {
                        echo date('m/d/Y');
                    }
                    ?>
                </p>
                <p>
                    <?= $subtitle ?>
                </p>
            </div>
        </div>
        <div class="report">
            <table class="wip_adv_table" style="width: 100%">
                <thead>
                <tr>
                    <th>Date In</th>
                    <th>Status</th>
                    <th>Advisor</th>
                    <th>Issue</th>
                    <th>RO #</th>
                    <th>Customer</th>
                    <th>Vehicle</th>
                    <th style="text-align:right;">Sold Hours</th>
                    <th style="text-align:right;">Labor Clock</th>
                    <th style="text-align:right;">%Complete</th>
                    <th class="calc_total" style="text-align:right;">Total RO</th>
                </tr>
                </thead>
                <tbody>
                <?php
                $alldata = array();//this will hold all the data arrays to be exported to excel

                $gttl = 0;
                $stmt = "select distinct writer from repairorders where shopid = ? and Status <> 'Closed' and ROType <> 'No Approval'";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $row = $query->get_result();
                } else {
                    echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }
                while ($ro = $row->fetch_array()) {
                    $alldata[] = array('TABLETITLE', $ro['writer']);
                    $alldata[] = array('TABLEHEAD', 'Date In', 'Status', 'RO #', 'Customer', 'Vehicle', 'Sold Hours', 'Labor Clock', '%Complete', 'Total RO');

                    $runttl = 0;
                    $query = "select Writer, MajorComplaint, ROID, ucase(Status) as stat, DateIn, Customer, VehInfo, TotalRO, totalfees, VehLicNum, CustomerID, ROType from repairorders where shopid ='$shopid' and Writer = '" . $ro['writer'] . "' and Status <> 'Closed' and ROType <> 'No Approval' ORDER BY Status ASC, ROID DESC";
                    $roresult = mysqli_query($conn, $query);
                    if (!$roresult) {
                        die("No Data To Display!");
                    }
                    if ($roresult->num_rows > 0) {
                        while ($rs = $roresult->fetch_array()) {

                            $stmt = "select sum(laborhours) as lh from labor where deleted = 'no' and shopid = '$shopid' and roid = '" . $rs['ROID'] . "'";
                            $query = mysqli_query($conn, $stmt);
                            $trs = $query->fetch_array();
                            if (!empty($trs['lh'])) {
                                $soldhours = round($trs["lh"], 2);
                            } else {
                                $soldhours = 0;
                            }
                            $stmt = "select sum(round((timestampdiff(second,startdatetime,enddatetime)/60)/60,2)) as thours, labortimeclock.* from labortimeclock where shopid = '$shopid' and roid = '" . $rs['ROID'] . "'";
                            $query = mysqli_query($conn, $stmt);
                            $trs = $query->fetch_array();
                            if (!empty($trs['thours'])) {
                                $laborclock = $trs["thours"];
                            } else {
                                $laborclock = 0;
                            }
                            if ($laborclock > 0 && $soldhours > 0) {
                                $percentcomplete = round(($laborclock / $soldhours) * 100, 2);
                            } else {
                                $percentcomplete = 0;
                            }
                            if (is_numeric(substr($rs['stat'], 0, 1))) {
                                $thestat = substr($rs['stat'], 1);
                            } else {
                                $thestat = $rs["stat"];
                            }
                            $runttl += $rs["TotalRO"];
                            $gttl += $rs["TotalRO"];
                            $comp = "";
                            $cstmt = "select * from complaints where cstatus = 'no' and shopid = ? and acceptdecline != 'declined' and roid = '" . $rs['ROID'] . "'";
                            if ($cquery = $conn->prepare($cstmt)) {
                                $cquery->bind_param("s", $shopid);
                                $cquery->execute();
                                $cr = $cquery->get_result();
                                if ($cr->num_rows == 0) continue;
                                while ($crs = $cr->fetch_assoc()) {
                                    $comp .= $crs['complaint'] . ", ";
                                }
                                $comp = rtrim($comp, ', ');
                            }

                            ?>

                            <tr class="">
                                <td><?= date('m/d/Y', strtotime($rs["DateIn"])) ?></td>
                                <td><?= $thestat ?></td>
                                <td><?= ucwords(strtolower($ro['writer'])) ?></td>
                                <td><b>Issue: </b><i><?= ucwords(strtolower($comp)); ?></i></td>
                                <td><?= $rs["ROID"] ?></td>
                                <td><?= ucwords(strtolower($rs["Customer"])) ?></td>
                                <td><?= ucwords(strtolower($rs["VehInfo"])) ?></td>
                                <td style="text-align:right;"><?= $soldhours ?></td>
                                <td style="text-align:right;"><?= $laborclock ?></td>
                                <td style="text-align:right;"><?= number_format($percentcomplete, 2) . '%' ?></td>
                                <td style="text-align:right;">$<?= number_format($rs['TotalRO'], 2) ?></td>
                            </tr>
                            <?php
                            $alldata[] = array(date('m/d/Y', strtotime($rs["DateIn"])), $thestat, $rs['ROID'], strtoupper($rs["Customer"]), strtoupper($rs["VehInfo"]), $soldhours, $laborclock, number_format($percentcomplete, 2) . '%', '$' . number_format($rs['TotalRO'], 2));
                            $alldata[] = array('Vehicle Issues', strtoupper($comp));
                        } // end of while for loop
                    } // end if for end of file
                    $alldata[] = array('');
                    $alldata[] = array("TOTAL IN PROCESS FOR " . strtoupper($ro["writer"]),'', '', '', '', '', '', '', '$' . number_format($runttl, 2));
                    $alldata[] = array('');
                    ?>
                    <!--
                    <tr class="table_total">
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td><b>Total In Process for <?= $ro["writer"]; ?></b></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td style="text-align:right;"><b>$<?= number_format($runttl, 2) ?></b></td>
                    </tr>
                    -->
                    <?php
                }
                ?>
                </tbody>
                <tfoot>
                <tr class="table_total">
                    <td>GRAND TOTAL</td>
                    <td></td>
                    <td>GRAND TOTAL</td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td style="text-align:right;"><b>$<?= number_format($gttl, 2) ?></b></td>
                </tr>
                </tfoot>
            </table>
        </div>
    </main>

<?php
$alldata[] = array('GRAND TOTAL', '', '', '', '', '', '', '', '$' . number_format($gttl, 2));

require COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form.php";

include getScriptsComponent($component);


$dateRangeDisplay =  date('m/d/Y');
$subtitle = !empty($subtitle) ? $subtitle : "";

?>
    <script>
        $(document).ready(function () {
            let floatVal = function (i) {
                return typeof i === 'string' ? Number(i.replace(/[$,]/g, '')) : typeof i === 'number' ? i : 0;
            };

            groupCol_writer = 2;
            groupCol_issue = 3;
            let table = $(".wip_adv_table").DataTable({
                responsive: true,
                fixedHeader: {
                    headerOffset: 68
                },
                colReorder: true,
                select: true,
                scrollY: false,
                scrollX: false,
                scroller: false,
                paging: false,
                language: {
                    searchPanes: {
                        emptyPanes: 'No records found in this date range'
                    },
                    search: "_INPUT_",
                    searchPlaceholder: "Search..."
                },
                buttons: [
                    {
                        extend: 'csv',
                        text: '<i class="fas fa-file-csv fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                        messageTop : "<?= $dateRangeDisplay ?>",
                    },
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint." (".$dateRangeDisplay.")" ?>",
                        messageTop : "<?= $subtitle ?>",
                        action: function ( e, dt, node, config ) {
                            $("#excelform").submit();
                        }
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                        exportOptions: {
                            // Any other settings used
                            stripHtml: false,
                            columns: [':visible'],
                            grouped_array_index: 0
                        },
                        messageTop : "<?= $dateRangeDisplay."<br/>".$subtitle ?>",
                        footer: true,
                    }
                ],
                dom: "<'row'<'col-sm-12 col-md-6 dt_Search text-secondary'f><'col-sm-12 col-md-6 text-right dt_btns text-secondary'B>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
                columnDefs: [{visible: false, targets: groupCol_writer}, {visible: false, targets: groupCol_issue}],
                order: [[groupCol_writer, 'asc']],
                orderFixed: [2, 'asc'],
                rowGroup: {
                    endRender: function (rows, group) {
                        //console.log(group);

                        let sum = rows
                            .data()
                            .pluck(10)
                            .reduce(function (a, b) {
                                return floatVal(a) + floatVal(b);
                            }, 0);

                        let formatting_options = {
                            style: 'currency',
                            currency: 'USD',
                            minimumFractionDigits: 2,
                        };
                        let USDollar = new Intl.NumberFormat('en-US', formatting_options);

                        return '<strong>Total In Process for ' + group + ' ' +
                            $.fn.dataTable.render.number(',', '.', 0, '$').display(USDollar.format(sum)) + '</strong>';
                    },
                    dataSrc: [2],
                    endClassName : 'table_total text-right',
                    startClassName : 'group_heading'
                },
                drawCallback: function (settings) {
                    var api = this.api();
                    var rows = api.rows({page: 'current'}).nodes();
                    var last = null;
                    api
                        .column(groupCol_issue, {page: 'all'})
                        .data()
                        .each(function (group, i) {
                            if (last !== group) {
                                $(rows)
                                    .eq(i)
                                    .after('<tr class="group"><td colspan="11">' + group + '</td></tr>');

                                last = group;
                            }
                        });

                },
            });

            $('#wip_adv_table').on('click', 'tr.group', function () {
                var currentOrder = table.order()[0];
                if (currentOrder[0] === groupColumn && currentOrder[1] === 'asc') {
                    table.order([groupColumn, 'desc']).draw();
                } else {
                    table.order([groupColumn, 'asc']).draw();
                }
            });
        });
    </script>
<?php
include getFooterComponent($component);
?>