<script src="https://cdn.datatables.net/fixedheader/3.3.2/js/dataTables.fixedHeader.min.js"></script>
<script src="https://cdn.datatables.net/rowgroup/1.3.1/js/dataTables.rowGroup.min.js"></script>
<script src="<?= SCRIPT ?>/dataTables.buttons.js"></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.html5.min.js"></script>
<script src="<?= SCRIPT ?>/printThis.js"></script>
<script>

    $(document).ready(function () {

        //load_params();

        const options = {
            confirmDateOnSelect: true,
            format: "mm/dd/yyyy",
            inline: true
        }

        sdPicker = document.getElementById('sdpicker');
        edPicker = document.getElementById('edpicker');


        if (typeof sdPicker !== "undefined" && sdPicker != null) {
            const SDpicker = new mdb.Datepicker(sdPicker, options);
        }
        if (typeof edPicker !== "undefined" && edPicker != null) {
            const EDpicker = new mdb.Datepicker(edPicker, options);
            sdPicker.addEventListener('dateChange.mdb.datepicker', (e) => {
                //console.log($("#sd").val());
                EDpicker.open();
            });
        }

        $("#dateRangeDropDown li a").click(function (e) {
            var range = $(this).data("range");
            //console.log(range);
            setdates(range, "#sd", "#ed");
            e.preventDefault();
        });

        $("#RTdateRangeDropDown li a").click(function (e) {
            var range = $(this).data("range");
            //console.log(range);
            setdates(range, "#ref_sd", "#ref_ed");
            e.preventDefault();
        });
        /*
        $("#dateRangeDropDown").change(function (e) {
            var range = $(this).val();
            console.log(range);
            setdates(range);
            e.preventDefault();
        });

        */

        let resizeTimer;
        $(window).on('resize', function() {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(function() {
                $('table.dataTable').each(function() {
                    if ($.fn.dataTable.isDataTable(this)) {
                        $(this).DataTable().columns.adjust().draw();
                    }
                });
            }, 300); // A delay to prevent excessive resizing recalculations
        });
    });

    function load_params() {
        if (window.localStorage.getItem("report_params")) {
            repObj = JSON.parse(window.localStorage.getItem("report_params"));
            //console.log(repObj);
            for (const property in repObj) {
                //console.log(property + ":" + repObj[property].tag);
                if (repObj[property].tag === "SELECT") {
                    $("#" + property + " option[value='" + repObj[property].val + "']").prop("selected", true);
                } else if (repObj[property].tag === "INPUT") {
                    $("#" + property).val(repObj[property].val)
                }
            }
        }
    }

    function showShopStats() {

        if ($('#shopstats').html() == "") {
            $('#spinner').show().css("left", "50px");
        } else {
            $('#spinner').hide().css("left", "49%");
        }
        $('#shopstats').toggle()

        ssclock = setInterval(function () {
            if ($('#shopstats').html() == "") {
                $('#spinner').show().css("left", "50px");
            } else {
                $('#spinner').hide().css("left", "49%");
                clearInterval(ssclock)
            }
        }, 500)

    }

    function kpiReport() {

        $('#kpidateselect').modal('show')


    }

    function runKPIReport() {

        $('#kpidateselect').modal('hide')
        m = $('#monthselect').val()
        location.href = "goals.php?m=" + m
    }

    function setdates(t, sdid, edid) {
        <?php
        echo "\r\nvar lw = '" . date("m/d/Y", strtotime("last week monday")) . "|" . date("m/d/Y", strtotime("last week sunday")) . "';\r\n";
        echo "var lm = '" . date("m/d/Y", strtotime("first day of previous month")) . "|" . date("m/d/Y", strtotime("last day of previous month")) . "';\r\n";
        $ly = date("Y") - 1;
        echo "var ly = '" . date("m/d/Y", strtotime("01/01/" . $ly)) . "|" . date("m/d/Y", strtotime("12/31/" . $ly)) . "';\r\n";
        echo "var tw = '" . date("m/d/Y", strtotime("this week monday")) . "|" . date("m/d/Y", strtotime("this week sunday")) . "';\r\n";
        echo "var tm = '" . date("m/d/Y", strtotime("first day of this month")) . "|" . date("m/d/Y", strtotime("last day of this month")) . "';\r\n";
        $ty = date("Y");
        echo "var ty = '" . date("m/d/Y", strtotime("01/01/" . $ty)) . "|" . date("m/d/Y", strtotime("12/31/" . $ty)) . "';\r\n";
        ?>

        if (t == "thisWeek") {
            tar = tw.split("|")
        }
        if (t == "thisMonth") {
            tar = tm.split("|")
        }
        if (t == "thisYear") {
            tar = ty.split("|")
        }
        if (t == "lastWeek") {
            tar = lw.split("|")
        }
        if (t == "lastMonth") {
            tar = lm.split("|")
        }
        if (t == "lastYear") {
            tar = ly.split("|")
        }
        $(sdid).val(tar[0]).addClass('active');
        $(edid).val(tar[1]).addClass('active');
    }

    function runReport(report, techreq = "no", enddate = "yes", cat = "no", job_status = "no", ro_status = "no", ro_type = "no", complaint_category = "no") {

        $('#datemodal').modal('show')
        $('#spdrow').hide();
        $("#jsrow").hide();
        $("#rsrow").hide();
        $('#emprow').hide()
        $("#complaint_category").hide();
        $('#techreq').val(techreq)
        $('#report').val(report)
        if (techreq == "yes") {
            $('#emprow').show()
        }
        if (cat == "yes") {
            $('#catrow').show()
        } else {
            $('#catrow').hide()
        }
        if (report == "custaddressreport.php") {
            $('#active_row').show()
        } else {
            $('#active_row').hide();
        }
        if (report == "partfeereport.php") {
            $('#feename_row').show()
        } else {
            $('#feename_row').hide();
        }
        if (report == "cannedjobdetail.php") {
            $('#cannjob_row').show()
        } else {
            $('#cannjob_row').hide();
        }
        if (ro_type == "yes") {
            $('#rotype_row').show()
        } else {
            $('#rotype_row').hide();
        }
        if (enddate == "no") {
            $('#endrow').hide()
            $('#datenav').hide()
        } else {
            $('#datenav').show()
        }
        if (report == "salestax.php") {
            $('#spdrow').show();
        }

        if (job_status == "yes") {
            $("#jsrow").show();
        }
        if (ro_status == "yes") {
            $("#rsrow").show();
        }
        if (complaint_category == "yes"){
            $("#complaint_category").show();
        }

        //setTimeout(function(){$('#sd').focus()},500)
    }

    function startIntReport() {
        it = $('#interval').val();
        path = 'custattrep.php?interval=' + it;
        location.href = path;
    }

    function startReport() {

        rp = $('#report').val()
        tr = $('#techreq').val()
        sd = $('#sd').val()
        ed = $('#ed').val()
        js = $("#job_completed").val();
        rs = $("#ro_status").val();
        complaint_category = $("#complaint_cat").val();
        path = rp + "?techreq=" + tr + "&sdate=" + sd + "&edate=" + ed
        //console.log($('#emprow').css("display"))
        if ($('#emprow').css("display") != "none") {
            path += "&empid=" + $('#emp').val()
        }
        if ($('#rotype_row').css("display") != "none") {
            path += "&rotype=" + $('#rotype').val()
        }
        if ($('#feename_row').css("display") != "none") {
            path += "&feename=" + $('#feename').val()
        }
        if ($('#cannjob_row').css("display") != "none") {
            path += "&cannedjob=" + $('#cannjob').val()
        }
        if ($('#catrow').css("display") != "none") {
            path += "&cat=" + $('#cat').val()
        }
        if ($('#active_row').css("display") != "none") {
            path += "&active=" + $('#active').val()
        }
        if ($("#jsrow").css("display") != "none") {
            path += "&job_completed=" + js;
        }
        if ($("#rsrow").css('display') != "none") {
            path += "&ro_status=" + rs;
        }
        if (rp == "salestax.php") {
            show_spd = $("#show_spd").prop('checked')
            path += "&spd=" + show_spd
        }
        if ($("#complaint_category").css('display') != "none") {
            path += "&complaint_category=" + complaint_category;
        }
        
        location.href = path
    }

    function suppReport() {

        location.href = 'inventoryorderreport.php?supplier=' + $('#supp').val()

    }

    function serviceReminderReport() {

        location.href = 'service_reminders.php'

    }


    function cancel() {

        $('#datemodal').modal('hide')
        $('#techreq').val('')
        $('#report').val('')
        $('#sd').val('')
        $('#ed').val('')
        $('#emprow').hide()
        $('#rotype_row').hide()
        $('#feename_row').hide()
        $('#cannjob_row').hide()
        $('#catrow').hide()
        $('#active_row').hide()
        $('#endrow').show()


    }

    function cancelsupp() {

        $('#suppmodal').modal('hide')

    }

    $(document).ready(function () {

        // get shop stats
        setTimeout(function () {
            $.ajax({
                data: "shopid<?php echo $shopid; ?>",
                url: "<?= COMPONENTS_PRIVATE ?>/wip/wipstats.php",
                success: function (r) {
                    //console.log(r)
                    $('#shopstats').append(r)
                }
            });
        }, 5000);

    });

    /*
    (function() {
        var url = 'https://debug.datatables.net/bookmarklet/DT_Debug.js';
        if (typeof DT_Debug != 'undefined') {
            if (DT_Debug.instance !== null) {
                DT_Debug.close();
            } else {
                new DT_Debug();
            }
        } else {
            var n = document.createElement('script');
            n.setAttribute('language', 'JavaScript');
            n.setAttribute('src', url + '?rand=' + new Date().getTime());
            document.body.appendChild(n);
        }
    })();

     */

    <?php
    $dateRangeDisplay = (!empty($_REQUEST['sdate']) || !empty($_REQUEST['edate'])) 
        ? ($_REQUEST['sdate'] ?? "") . (!empty($_REQUEST['edate']) ? " to " . $_REQUEST['edate'] : "") 
        : date("m/d/Y");
    $subtitle = !empty($subtitle)? $subtitle : "";
    ?>
    $(document).ready(function () {

        if ( $('#jsrow').length > 0) {
            $('#jsrow').tooltip({
                trigger: 'hover',
                container: 'body'
            })
        }

        if ($(".dtable").length > 0) {
            $(".dtable").each(function () {
                //console.log($(this).find('tr').length);
                if ($(this).find('tr').length > 0) {
                    let table = $(this).DataTable({
                        responsive: true,
                        fixedHeader: {
                            headerOffset: 68
                        },
                        colReorder: true,
                        select: true,
                        scrollX: false,
                        paging: false,
                        order: [],
                        language: {
                            searchPanes: {
                                emptyPanes: 'No records found in this date range'
                            },
                            search: "_INPUT_",
                            searchPlaceholder: "Search..."
                        },
                        buttons: [
                            {
                                extend: 'csv',
                                text: '<i class="fas fa-file-csv fa-xl"></i>',
                                title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                                messageTop : "<?= $dateRangeDisplay ?>",
                            },
                            {
                                extend: 'excel',
                                text: '<i class="fas fa-file-excel fa-xl"></i>',
                                title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint." (".$dateRangeDisplay.")" ?>",
                                messageTop : "<?= $subtitle ?>",
                                exportOptions: {
                                    // Any other settings used
                                    columns: [':visible'],
                                    // grouped_array_index: 0
                                },
                                footer: true,
                                customize: function (xlsx) {
                                    var sheet = xlsx.xl.worksheets['sheet1.xml'];
                                    var styleSheet = xlsx.xl['styles.xml'];

                                    var numFmts = $('numFmts', styleSheet);
                                    var numFmtId = 164;

                                    if (numFmts.length === 0) {
                                        $('styleSheet', styleSheet).prepend('<numFmts count="1"><numFmt numFmtId="164" formatCode="$#,##0.00" /></numFmts>');
                                    } else if ($('numFmt[formatCode="$#,##0.00"]', numFmts).length === 0) {
                                        numFmts.append('<numFmt numFmtId="' + numFmtId + '" formatCode="$#,##0.00" />');
                                        numFmts.attr('count', parseInt(numFmts.attr('count')) + 1);
                                    } else {
                                        numFmtId = $('numFmt[formatCode="$#,##0.00"]', numFmts).attr('numFmtId');
                                    }

                                    var cellXfs = $('cellXfs', styleSheet);
                                    var currencyStyleIndex = parseInt(cellXfs.attr('count'));
                                    cellXfs.append('<xf numFmtId="' + numFmtId + '" fontId="0" fillId="0" borderId="0" xfId="0" applyNumberFormat="1"/>');
                                    cellXfs.attr('count', currencyStyleIndex + 1);

                                    var fonts = $('fonts', styleSheet);
                                    var fontCount = parseInt(fonts.attr('count'));
                                    fonts.append('<font><b/></font>');
                                    fonts.attr('count', fontCount + 1);

                                    var boldCurrencyStyleIndex = parseInt(cellXfs.attr('count'));
                                    cellXfs.append('<xf numFmtId="' + numFmtId + '" fontId="' + fontCount + '" fillId="0" borderId="0" xfId="0" applyNumberFormat="1"/>');
                                    cellXfs.attr('count', boldCurrencyStyleIndex + 1);

                                    var rows = $('row', sheet);
                                    var lastRowIndex = rows.length;

                                    $('c', sheet).each(function() {
                                        var cell = $(this);
                                        var r = cell.attr('r');
                                        var rowIndex = parseInt(r.replace(/[A-Z]/g, ''));
                                        var valNode = cell.find('v');

                                        if (!valNode.length) return;

                                        var text = valNode.text();
                                        if (text.indexOf('$') !== -1) {
                                            valNode.text(text.replace(/[\$,]/g, ''));
                                            cell.attr('s', currencyStyleIndex);
                                        }

                                        var colIndex = cell[0].getAttribute('r').match(/[A-Z]+/)[0];
                                        var thIndex = colIndex.charCodeAt(0) - 65;
                                        var th = $('thead th').eq(thIndex);

                                        if (rowIndex === lastRowIndex && th.hasClass('calc_total')) {
                                            cell.attr('s', boldCurrencyStyleIndex);
                                        }
                                    });
                                }
                            },
                            {
                                extend: 'print',
                                text: '<i class="fas fa-print fa-xl"></i>',
                                title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                                exportOptions: {
                                    // Any other settings used
                                    stripHtml: false,
                                    columns: [':visible'],
                                    // grouped_array_index: 0
                                },
                                messageTop : "<?= $dateRangeDisplay."<br/>".$subtitle ?>",
                                footer: true,
                            }
                        ],
                        dom: "<'row'<'col-sm-12 col-md-6 dt_Search text-secondary'f><'col-sm-12 col-md-6 text-right dt_btns text-secondary'B>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
                        "drawCallback": function () {
                            /*
                            // Move the footer row to the bottom of the table
                            let footer = $(this.api().table().footer());
                            footer.detach();
                            $(this.api().table().footer()).remove();
                            $(this.api().table().node()).append(footer);

                            // Fix the footer at the bottom
                            new $.fn.dataTable.FixedFooter(table);
                             */

                            let api = this.api();

                            let floatVal = function (i) {
                                return typeof i === 'string' ? Number(i.replace(/[$,]/g, '')) : typeof i === 'number' ? i : 0;
                            };
                            // Calculate totals for columns with .calc_total class
                            $('.calc_total', api.table().header()).each(function () {
                                let columnIdx = api.column($(this)).index();
                                let total = api
                                    .column(columnIdx, {search: 'applied'})
                                    .data()
                                    .reduce(function (a, b) {
                                        return floatVal(a) + floatVal(b);
                                    }, 0);

                                let formatting_options = {
                                    style: 'currency',
                                    currency: 'USD',
                                    minimumFractionDigits: 2,
                                };
                                let USDollar = new Intl.NumberFormat('en-US', formatting_options);

                                $(api.column(columnIdx).footer()).html('<b>' + USDollar.format(total) + '<b>');

                            });
                            // Calculate totals for columns with .calc_total class
                            $('.calc_qty', api.table().header()).each(function () {
                                let columnIdx = api.column($(this)).index();
                                let total = api
                                    .column(columnIdx, {search: 'applied'})
                                    .data()
                                    .reduce(function (a, b) {
                                        return floatVal(a) + floatVal(b);
                                    }, 0);
                                if ($(this).hasClass('dt_int')) {
                                    total = total.toFixed(0);
                                } else {
                                    total = total.toFixed(2);
                                }
                                $(api.column(columnIdx).footer()).html('<b>' + (total) + '<b>');

                            });

                            /*
                            $('.calc_avg', api.table().header()).each(function () {
                                let columnIdx = api.column($(this)).index();
                                let count = table.data().count();
                                let total = api
                                    .column(columnIdx, {search: 'applied'})
                                    .data()
                                    .reduce(function (a, b) {
                                        return floatVal(a) + floatVal(b);
                                    }, 0);

                                total = total / count;

                                let formatting_options = {
                                    style: 'currency',
                                    currency: 'USD',
                                    minimumFractionDigits: 2,
                                };
                                let USDollar = new Intl.NumberFormat('en-US', formatting_options);

                                $(api.column(columnIdx).footer()).html('<b>' + USDollar.format(total) +'<b>');

                            }); */

                        }
                    });
                }
            })
        }

    });

    function checkRefForm() {
        $("#err_msg").fadeOut();
        sd = $('#ref_sd').val()
        ed = $('#ref_ed').val()
        source = $('#source').val()

        if (sd.length > 0 && ed.length > 0) {
            //location.href = 'report.php?sd=' + sd + '&ed=' + ed + "&tech1=" + tech1 + "&tech2=" + tech2
            output = $("#output").val()
            $("#report_form").submit()
        } else {
            $("#err_msg").fadeIn();
        }

    }

    function printreport() {
        //  alert("If you would like to print this report, we suggest 'Landscape' mode as it fits better on the page");
        $('#reports').printThis({
            printContainer: false,
            importStyle : true,
        });
    }

    function runInventoryReport(){
        if(document.getElementById("partscategory").value != 'none'){
            location.href='inventoryreport.php?salesdata='+document.getElementById("salesdata").value+'&sortby='+document.getElementById("sortby").value+'&category='+document.getElementById("partscategory").value
        }else{
            sbalert("You have no parts inventory with assigned categories")
        }

    }

</script>