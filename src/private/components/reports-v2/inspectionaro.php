<?php
$component = "reports-v2";
// Use this for Gen Pop Reports

include getRulesGlobal($component);
include getHeadGlobal($component);

$shopid = filter_var($_COOKIE['shopid'], FILTER_SANITIZE_STRING);
$sd = isset($_GET['sd']) ? filter_var($_GET['sd'], FILTER_SANITIZE_STRING) : '';
$ed = isset($_GET['ed']) ? filter_var($_GET['ed'], FILTER_SANITIZE_STRING) : '';
$sdate = isset($_GET['sdate']) ? filter_var($_GET['sdate'], FILTER_SANITIZE_STRING) : $sd;
$edate = isset($_GET['edate']) ? filter_var($_GET['edate'], FILTER_SANITIZE_STRING) : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));
$path = $_SERVER['HTTP_HOST'];

$ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
$roTypeSQL = "AND rotype != 'No Approval'";
if ($ro_type != "all") {
    $roTypeSQL = "AND r.rotype = '" . $ro_type . "'";
}

// Page Variables
$title = 'Inspection ARO Report - ' . ucwords(strtolower($ro_type));  // Report Title Goes Here

$template = COMPONENTS_PRIVATE . "/reports//templates/excelexport.php"; //Only change if a custom PHPExcel is created in the template folder
?>
<body>
<?php

include getHeaderGlobal($component);
include getMenuGlobal('');
?>


<!-- Column Headers Insert Report Variables here -->


<?php
$tablefields = array('RO Count W/ Inspection', 'Subtotal RO Amount', 'ARO W/ Inspection', 'RO Count W/O Inspection', 'Subtotal RO Amount', 'ARO W/O Inspection');//set table headings array for excel export
$alldata = array();//this will hold all the data arrays to be exported to excel

// Insert DB Query Here

// Template Query Begins - Replace entire section
$asid = "";
$stmt = "select asid from autoserveshop where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($asid);
    $query->fetch();
    $query->close();
}

if (!empty($asid)) {
    $stmt = "select distinct r.roid,(r.totalro-r.salestax) as tro from repairorders r,autoserveinspections i WHERE r.shopid = i.shopid and r.roid = i.roid and r.shopid =  ? AND r.`status` = 'closed' $roTypeSQL AND r.statusdate >= ? AND r.statusdate <= ?";
} else {
    $stmt = "select distinct r.roid,(r.totalro-r.salestax) as tro from repairorders r,roinspection i WHERE r.shopid = i.shopid and r.roid = i.roid and r.shopid =  ? AND r.`status` = 'closed' $roTypeSQL AND r.statusdate >= ? AND r.statusdate <= ?";
}

$dvistmt = "select distinct r.roid,(r.totalro-r.salestax) as tro from repairorders r,dvi i 
WHERE r.shopid = i.shopid and r.roid = i.roid AND r.`status` = 'closed' $roTypeSQL
and r.shopid = ? AND r.statusdate >= ? AND r.statusdate <= ?";
if ($dviquery = $conn->prepare($dvistmt)) {
    $dviquery->bind_param("sss", $shopid, $sd1, $ed1);
    $dviquery->execute();
    $dviresults = $dviquery->get_result();
    $dviquery->close();
}

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("sss", $shopid, $sd1, $ed1);
    $query->execute();
    $roresult = $query->get_result();
} else {
    echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
}
$tro = $cnt = $aro = $wtro = $wcnt = $waro = 0;
$ros = array();
while ($ro = $roresult->fetch_array()) {
    $tro += $ro['tro'];
    $cnt++;
    $ros[] = $ro['roid'];
}

if ($dviresults->num_rows > 0) {
    while ($dvi = $dviresults->fetch_assoc()) {
        if (!in_array($dvi['roid'], $ros)) {
            $tro += $dvi['tro'];
            $cnt++;
            $ros[] = $dvi['roid'];
        }
    }
}

if (!empty($cnt)) {
    $aro = $tro / $cnt;
}

if (!empty($ros)) {
    $exstr = "and r.roid not in (" . implode(',', $ros) . ")";
} else $exstr = "";

$stmt = "select r.roid,(r.totalro-r.salestax) as tro from repairorders r WHERE r.shopid = ? AND r.`status` = 'closed' $roTypeSQL AND r.statusdate >= ? AND r.statusdate <= ? $exstr";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("sss", $shopid, $sd1, $ed1);
    $query->execute();
    $roresult = $query->get_result();
} else {
    echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

while ($ro = $roresult->fetch_array()) {
    $wtro += $ro['tro'];
    $wcnt++;
}

if (!empty($wcnt)) {
    $waro = $wtro / $wcnt;
}
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md- col-sm-10">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary d-print-none">Reports</a>
                        <span
                                class="text-secondary d-print-none ps-3 pe-3">/</span>
                        <h2><?= $title; ?> <span class="d-none d-print-inline"> <?= $companyNamePrint ?></span></h2>
                    </div>
                </div>
                <div class="col-md-2 col-sm-4">
                    <button type="button" class="btn btn-secondary btn-md d-print-none float-end"
                            onclick="printreport()">Print
                    </button>
                    <button type="button" class="btn btn-secondary btn-md d-print-none float-end me-2"
                            onclick="$('#excelform').submit();">Excel
                    </button>
                </div>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report d-print-none">
        <?php
        include_once "inline_prompt.php";
        ?>
    </div>
    <div class="d-flex">
        <section class="container-fluid" id="">
            <table class="table table-striped table-hover">
                <tr>
                    <td style="text-align:right;width:51%"><b>RO Count W/ Inspection</b></td>
                    <td style="text-align:left;width:49%"><?= $cnt; ?></td>
                </tr>
                <tr>
                    <td style="text-align:right;width:50%"><b>Subtotal RO Amount</b></td>
                    <td style="text-align:left;width:50%"><?= asDollars($tro); ?></td>
                </tr>
                <tr>
                    <td style="text-align:right;width:50%"><b>ARO W/ Inspection</b></td>
                    <td style="text-align:left;width:50%"><?= asDollars($aro); ?></td>
                </tr>
                <tr>
                    <td style="text-align:right;width:50%"><b>RO Count W/O Inspection</b></td>
                    <td style="text-align:left;width:50%"><?= $wcnt; ?></td>
                </tr>
                <tr>
                    <td style="text-align:right;width:50%"><b>Subtotal RO Amount</b></td>
                    <td style="text-align:left;width:50%"><?= asDollars($wtro); ?></td>
                </tr>
                <tr>
                    <td style="text-align:right;width:50%"><b>ARO W/O Inspection</b></td>
                    <td style="text-align:left;width:50%"><?= asDollars($waro); ?></td>
                </tr>
                <?php
                $alldata[] = array($cnt, asDollars($tro), asDollars($aro), $wcnt, asDollars($wtro), asDollars($waro));//fill up the alldata array with the arrays of data to be shown in excel export

                // end if for end of file
                $alldata[] = array('', '', '', '', '', '');

                ?>
            </table>
        </section>
    </div>
</main>
<?php
include(COMPONENTS_PRIVATE_PATH . '/reports-v2/includes/report_form.php');
include getScriptsGlobal($component);
//include getScriptsComponent($component);
include getFooterComponent($component);
?>
