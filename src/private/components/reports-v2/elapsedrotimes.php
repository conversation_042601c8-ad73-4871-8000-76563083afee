<?php

$component = "reports-v2";
include getHeadGlobal($component);
include getRulesGlobal($component);
include getHeaderGlobal($component);
include getMenuGlobal($component);

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));

$ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
$roTypeSQL = "";
if ($ro_type != "all") {
    $roTypeSQL = "AND rotype = '" . $ro_type . "'";
}

$title = 'RO Elapsed Time - ' . ucwords(strtolower($ro_type));
$component = "reports-v2";

// Use this for Custom Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_elapsedrotimes.php';
?>

<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="title col breadcrumb d-flex align-items-center">
                <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                    class="text-secondary ps-3 pe-3">/</span>
                <h2><?= $title; ?></h2>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report">

        <?php
        include_once "inline_prompt.php";
        ?>
        <table class="dtable w-100" style="width: 100%">
            <thead>
            <tr class="table_header table_head">
                <td>RO #</td>
                <th>Customer</th>
                <th>Vehicle</th>
                <th>Status</th>
                <th>Date/Time IN</th>
                <th>Date/Time CLOSED</th>
                <th>Elapsed Time</th>
                <th class="text-right calc_total">Total RO</th>
            </tr>
            </thead>
            <tbody>
            <?php
            $tablefields = array('RO#', 'Customer', 'Vehicle', 'Status', 'Date IN', 'Date CLOSED', 'Elapsed Time', 'Total RO');//set table headings array for excel export
            $alldata = array();//this will hold all the data arrays to be exported to excel


            // Insert DB Query Here

            // Template Query Begins - Replace entire section
            $tro = "";
            $stmt = "select roid,customer,vehinfo,datein,timein,updatesent,totalro,status from repairorders where status = 'closed' $roTypeSQL and statusdate >= ? and statusdate <= ? and shopid = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sss", $sd1, $ed1, $shopid);
                $query->execute();
                $r = $query->get_result();
                $totalrows = $r->num_rows;
                $elapsedsec = 0;

                while ($rs = $r->fetch_array()) {
                    if (is_numeric(substr($rs['status'], 0, 1))) {
                        $stat = substr($rs['status'], 1, strlen($rs['status']) - 1);
                    } else {
                        $stat = $rs['status'];
                    }
                    $sd1 = date_create($rs['datein'] . " " . $rs['timein']);
                    $ed1 = date_create($rs['updatesent']);
                    $elapsed = date_diff($sd1, $ed1);
                    $elapsedsec += (strtotime($rs['updatesent']) - strtotime($rs['datein'] . " " . $rs['timein']));
                    ?>

                    <tr>
                        <td><?= $rs['roid']; ?></td>
                        <td><?= $rs['customer']; ?></td>
                        <td><?= $rs['vehinfo']; ?></td>
                        <td><?= $stat; ?></td>
                        <td><?= date("m/d/Y h:i A", strtotime($rs['datein'] . " " . $rs['timein'])); ?></td>
                        <td><?= date("m/d/Y h:i A", strtotime($rs['updatesent'])); ?></td>
                        <td><?= $elapsed->format("%y years, %m months, %d days, %h hrs %i min"); ?></td>
                        <td class="text-right"><?= asDollars($rs['totalro'], 2); ?></td>
                    </tr>
                    <?php
                    $alldata[]=array($rs['roid'], strtoupper($rs['customer']), strtoupper($rs['vehinfo']), strtoupper($stat), date("m/d/Y h:i A",strtotime($rs['datein']." ".$rs['timein'])), date("m/d/Y h:i A",strtotime($rs['updatesent'])), $elapsed->format("%y years, %m months, %d days, %h hrs %i min"), asDollars($rs['totalro'],2));//fill up the alldata array with the arrays of data to be shown in excel export
                } // end of while for loop
            } // end if for end of file


            $alldata[] = array('', '', '', '', '', '', '');
            if ($totalrows > 0) {
                $sec = round($elapsedsec / $totalrows);
                $dtF = new \DateTime('@0');
                $dtT = new \DateTime("@" . $sec);
                $avgTime = $dtF->diff($dtT)->format('%y years, %m months, %d days, %h hrs %i min');

                $alldata[] = array('', 'Average Elapsed Time', '', '', '', '', $avgTime, '');
            } else {
                $avgTime = "No data available";
                $sec = 0;
            }
            
            ?>
            </tbody>

            <?php if ($totalrows > 0): ?>
                <tfoot>
                    <tr class="table_total">
                        <td></td>
                        <td><b>Average Elapsed Time</b></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td><b><?= $avgTime ?></b></td>
                        <td class="text-right"></td>
                    </tr>
                </tfoot>
            <?php endif; ?>
        </table>
    </div>
</main>
<?php

require COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form.php";

include getScriptsGlobal($component);
include getFooterComponent($component);
?>
