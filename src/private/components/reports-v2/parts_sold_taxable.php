<?php
$component = "reports-v2";

include getHeadGlobal($component);
include getRulesGlobal($component);
include getHeaderGlobal($component);
include getMenuGlobal($component);


$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));

$ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
$roTypeSQL = "AND rotype != 'No Approval'";
if ($ro_type != "all") {
    $roTypeSQL = "AND rotype = '" . $ro_type . "'";
}

// Page Variables
$title = 'Part Sales by Taxable - ' . ucwords(strtolower($ro_type));  // Report Title Goes Here
$subtitle = 'Non-Taxable vs Taxable Parts';  // Report SubTitle Goes Here - Hide if not needed
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_multiple_parts_sold_taxable.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="title col breadcrumb d-flex align-items-center">
                <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                    class="text-secondary ps-3 pe-3">/</span>
                <h2><?= $title; ?></h2>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report">
        <?php
        include_once "inline_prompt.php";
        ?>
        <div id="non_taxable_parts" style="clear: both;">
            <h3>Non-Taxable Parts</h3>
            <table class="dtable" style="width: 100%">
                <thead>
                <tr class="table_header table_head">
                    <th>Date Sold</th>
                    <th>RO #</th>
                    <th>Part #</th>
                    <th>Description</th>
                    <th class="calc_qty" style="text-align:center">Qty</th>
                    <th class="calc_total" style="text-align:right">Cost</th>
                    <th class="calc_total" style="text-align:right">Price</th>
                    <th class="calc_total" style="text-align:right">Tax</th>
                </tr>
                </thead>
                <tbody>
                <?php
                //$tablefields=array('Date Sold','RO #','Part #','Description','Qty','Cost','Price','Tax');//set table headings array for excel export
                //$alldata=array();//this will hold all the data arrays to be exported to excel
                $alldata[] = array('TABLETITLE', 'Non-Taxable Parts');
                $alldata[] = array('TABLEHEAD', 'Date Sold', 'RO #', 'Part #', 'Description', 'Qty', 'Cost', 'Price', 'Tax');
                // Insert DB Query Here
                // Template Query Begins - Replace entire section
                $runttlcost = 0;
                $runttlprice = 0;
                $stmt = "SELECT p.`date`,p.roid,p.partnumber,p.partdesc,p.quantity,p.linettlcost,p.linettlprice from parts p join repairorders r ON p.shopid = r.shopid AND p.roid = r.roid where p.shopid = ? AND p.`date` >= ? AND p.`date` <= ? AND p.tax = 'no' $roTypeSQL order BY p.`date`,p.roid";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("sss", $shopid, $sd1, $ed1);
                    $query->execute();
                    $roresult = $query->get_result();
                } else {
                    echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                if (mysqli_num_rows($roresult) > 0) {
                    while ($ro = $roresult->fetch_array()) {
                        $runttlcost += $ro["linettlcost"];
                        $runttlprice += $ro["linettlprice"];
                        ?>
                        <tr>
                            <td><?= date("m/d/Y", strtotime($ro["date"])); ?></td>
                            <td><?= $ro["roid"]; ?></td>
                            <td><?= $ro["partnumber"]; ?></td>
                            <td><?= $ro["partdesc"]; ?></td>
                            <td style="text-align:center"><?= $ro["quantity"]; ?></td>
                            <td style="text-align:right"><?= asDollars($ro["linettlcost"]); ?></td>
                            <td style="text-align:right"><?= asDollars($ro["linettlprice"]); ?></td>
                            <td style="text-align:right"><?= asDollars('0.00'); ?></td>
                        </tr>
                        <?php
                        $alldata[] = array(date("m/d/Y", strtotime($ro["date"])), strtoupper($ro["roid"]), strtoupper($ro["partnumber"]), strtoupper($ro["partdesc"]), $ro["quantity"], asDollars($ro["linettlcost"]), asDollars($ro["linettlprice"]), asDollars('0.00'));//fill up the alldata array with the arrays of data to be shown in excel export
                    } // end of while for loop
                }
                ?>
                </tbody>
                <tfoot>
                <tr>
                    <td><b>TOTALS</b></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td style="text-align:right"><b><?= asDollars($runttlcost); ?></b></td>
                    <td style="text-align:right"><b><?= asDollars($runttlprice); ?></b></td>
                    <td style="text-align:right"><b><?= asDollars('0.00'); ?></b></td>
                </tr>
                </tfoot>
            </table>
            <?php
            $alldata[] = array('TOTALS', '', '', '', '', asDollars($runttlcost), asDollars($runttlprice), asDollars('0.00'));
            ?>
        </div>
        <div id="taxable_parts" title="taxable_parts" style="clear: both;">
            <h3>Taxable Parts</h3>
            <table class="dtable"  style="width: 100%">
                <thead>
                <tr class="table_header table_head">
                    <th>Date Sold</th>
                    <th>RO #</th>
                    <th>Part #</th>
                    <th>Description</th>
                    <th class="calc_qty" style="text-align:center">Qty</th>
                    <th class="calc_total" style="text-align:right">Cost</th>
                    <th class="calc_total" style="text-align:right">Price</th>
                    <th class="calc_total" style="text-align:right">Tax</th>
                </tr>
                </thead>
                <tbody>
                <?php
                $alldata[] = array('', '', '', '', '', '', '', '');
                $alldata[] = array('TABLETITLE', 'Taxable Parts');
                $alldata[] = array('TABLEHEAD', 'Date Sold', 'RO #', 'Part #', 'Description', 'Qty', 'Cost', 'Price', 'Tax');
                // Insert DB Query Here
                // Template Query Begins - Replace entire section
                $runttlcost2 = 0;
                $runttlprice2 = 0;
                $tax = 0;
                $runttltax2 = 0;
                $stmt2 = "select p.`date`,p.shopid,p.roid,p.partnumber,p.partdesc,p.quantity,p.linettlcost,p.linettlprice,p.tax,c.defaulttaxrate,c.shopid from parts p join company c on p.shopid = c.shopid join repairorders r ON p.shopid = r.shopid AND p.roid = r.roid where p.shopid = ? and p.`date` >= ? and p.`date` <= ? and p.tax = 'yes' $roTypeSQL order by p.`date`,p.roid";
                if ($query = $conn->prepare($stmt2)) {
                    $query->bind_param("sss", $shopid, $sd1, $ed1);
                    $query->execute();
                    $ro2result = $query->get_result();
                } else {
                    echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }
                while ($ro2 = $ro2result->fetch_array()) {
                    $defaulttax = $ro2["defaulttaxrate"] / 100;
                    $pricetax = $defaulttax * $ro2["linettlprice"];
                    $runttlcost2 += $ro2["linettlcost"];
                    $runttlprice2 += $ro2["linettlprice"];
                    $runttltax2 += $pricetax;
                    ?>
                    <tr>
                        <td><?= date("m/d/Y", strtotime($ro2["date"])); ?></td>
                        <td><?= $ro2["roid"]; ?></td>
                        <td><?= $ro2["partnumber"]; ?></td>
                        <td><?= $ro2["partdesc"]; ?></td>
                        <td style="text-align:center"><?= $ro2["quantity"]; ?></td>
                        <td style="text-align:right"><?= asDollars($ro2["linettlcost"]); ?></td>
                        <td style="text-align:right"><?= asDollars($ro2["linettlprice"]); ?></td>
                        <td style="text-align:right"><?= asDollars($pricetax); ?></td>
                    </tr>
                    <?php
                    $alldata[] = array(date("m/d/Y", strtotime($ro2["date"])), strtoupper($ro2["roid"]), strtoupper($ro2["partnumber"]), strtoupper($ro2["partdesc"]), $ro2["quantity"], asDollars($ro2["linettlcost"]), asDollars($ro2["linettlprice"]), asDollars('0.00'));//fill up the alldata array with the arrays of data to be shown in excel export
                } // end of while for loop
                // end if for end of file
                ?>
                </tbody>
                <tfoot>
                <tr>
                    <td><b>TOTALS</b></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td style="text-align:right"><b><?= asDollars($runttlcost2); ?></b></td>
                    <td style="text-align:right"><b><?= asDollars($runttlprice2); ?></b></td>
                    <td style="text-align:right"><b><?= asDollars($runttltax2); ?></b></td>
                </tr>
                </tfoot>
                <?php
                $alldata[] = array('TOTALS', '', '', '', '', asDollars($runttlcost2), asDollars($runttlprice2), asDollars($runttltax2));
                ?>
            </table>
        </div>
</main>
<?php

require COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form.php";

include getScriptsGlobal($component);
include getFooterComponent($component);
?>
