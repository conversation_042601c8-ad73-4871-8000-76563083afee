<?php
$template = $_GET['template'] ?? 'yes';
$component = "reports-v2";

include getRulesGlobal($component);
include getHeadGlobal($component);

$shopid = $_COOKIE['shopid'];

function convertToHoursMins($time, $format = '%02d:%02d')
{
    if ($time < 1) {
        return;
    }
    $hours = floor($time / 60);
    $minutes = ($time % 60);
    return sprintf($format, $hours, $minutes);
}

$title = "Sales KPI Report";
?>
<style media="print">
    @media print{@page {size: landscape}}
</style>
<body>
<?php
if ($template != "no") {
    include getHeaderGlobal($component);
    include getMenuGlobal($component);
} else {
    ?>
    <style>
        main#reports {
            margin-left: 0px !important;
            border: none !important;
            margin-top: 0px !important;
        }

        .report {
            display: none !important;
        }
    </style>
    <?php
}
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-10 col-sm-10">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary d-print-none">Reports</a>
                        <span
                            class="text-secondary ps-3 pe-3 d-print-none">/</span>
                        <h2><?= $title; ?><span class="d-none d-print-inline"> - <?= $companyNamePrint ?></span></h2>
                    </div>
                </div>
                <div class="col-md-2 col-sm-4 d-print-none">
                    <button type="button" class="btn btn-secondary btn-md float-end" onclick="printreport()">Print</button>
                </div>
            </div>
            <hr/>
            <p class="card-title">

                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report d-print-none">
        <?php
        if ($template != "no") {
            include_once "inline_prompt.php";
        }
        ?>
    </div>
    <div class="d-flex">
        <section class="container-fluid" id="">

            <?php
            $m = $_GET['m'];
            $sd = $m;
            $ed = date("Y-m-t", strtotime($sd));

            $stmt = "select mogoal,workingdays,laborsalesgoal,partssalesgoal,laborhoursgoal from goals where shopid = '$shopid' and mostartdate = '$m'";
            //echo $stmt;
            if ($query = $conn->prepare($stmt)) {
                $query->execute();
                $query->store_result();
                $numrows = $query->num_rows();
                $query->free_result();
                $query->execute();
                $query->bind_result($mogoal, $workingdays, $laborsalesgoal, $partssalesgoal, $laborhoursgoal);
                $query->fetch();
                $query->close();
            }

            if ($numrows == 0) {
                ?>
                <div class="text-center text-primary border border-primary p-3 m-5 d-print-none">
                    <h4>No KPI's (Key Performance Indicators) Showing? Go to <a href="<?= COMPONENTS_PRIVATE ?>/v2/settings/goals.php" target="_top">Settings -> Custom Settings -> Monthly Sales Goals </a></h4>
                </div>
                <?php
            } else {

                // total closed for current month
                if ($shopid == '7260') {
                    $stmt = "select coalesce(sum(totallbrhrs),0) totallbrhrs, coalesce(sum(totallbr+totalprts+totalsublet+totalfees-discountamt),0) tro, coalesce(sum(totallbr),0) totallbr, coalesce(sum(totalprts),0) totalprts from repairorders where shopid = '$shopid' and rotype != 'no approval' and status = 'closed'"
                        . " and statusdate >= '$sd' and statusdate <= '$ed'";
                } else {
                    $stmt = "select coalesce(sum(totallbrhrs),0) totallbrhrs, coalesce(sum(totallbr+totalprts+totalsublet+totalfees+salestax-discountamt),0) tro, coalesce(sum(totallbr),0) totallbr, coalesce(sum(totalprts),0) totalprts from repairorders where shopid = '$shopid' and rotype != 'no approval' and status = 'closed'"
                        . " and statusdate >= '$sd' and statusdate <= '$ed'";
                }
                if ($query = $conn->prepare($stmt)) {
                    $query->execute();
                    $query->bind_result($totallbrhrs, $totalclosed, $totallbr, $totalprts);
                    $query->fetch();
                    $query->close();
                } else {
                    echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $now = time(); // or your date as well
                $your_date = strtotime($m);
                $datediff = $now - $your_date;

                //$ndays = cal_days_in_month(CAL_GREGORIAN,date("m",time()),date("Y",time()));

                $ndays = $workingdays;
                if ($ndays == 0) {
                    $ndays = 30;
                }
                $edays = round($datediff / (60 * 60 * 24));
                $eperc = round($edays / $ndays, 2);
                $percgoal = $mogoal * $eperc;

                $dailygoal = round($mogoal / $ndays, 0);
                if ($mogoal > 0) {
                    $percentachieved = round(($totalclosed / $mogoal) * 100, 2);
                } else {
                    $percentachieved = 0;
                }
                if ($laborsalesgoal > 0) {
                    $percentlaborachieved = round(($totallbr / $laborsalesgoal) * 100, 2);
                } else {
                    $percentlaborachieved = 0;
                }
                if ($partssalesgoal > 0) {
                    $percentpartsachieved = round(($totalprts / $partssalesgoal) * 100, 2);
                } else {
                    $percentpartsachieved = 0;
                }
                if ($laborhoursgoal > 0) {
                    $percenthoursachieved = round(($totallbrhrs / $laborhoursgoal) * 100, 2);
                } else {
                    $percenthoursachieved = 0;
                }

                $end = new DateTime($sd);
                $end->modify('last day of this month');
                $end = $end->format('Y-m-d');

                $current = $m;

                $curr = date('Y-m-d', strtotime($current));
                if ($curr == date('Y-m-01'))
                    $percentofmonth = round((date('d') / date('t')) * 100, 0);
                else
                    $percentofmonth = 100;

                if ($percentachieved < $percentofmonth) {
                    $ptsborder = "border:3px red solid";
                    $bgcolor = '#ff4d4d';
                } else {
                    $ptsborder = "border:3px green solid";
                    $bgcolor = 'green';
                }

                if ($percentlaborachieved < $percentofmonth) {
                    $ptsborder = "border:3px red solid";
                    $plabgcolor = '#ff4d4d';
                } else {
                    $ptsborder = "border:3px green solid";
                    $plabgcolor = 'green';
                }

                if ($percentpartsachieved < $percentofmonth) {
                    $ptsborder = "border:3px red solid";
                    $ppabgcolor = '#ff4d4d';
                } else {
                    $ptsborder = "border:3px green solid";
                    $ppabgcolor = 'green';
                }

                if ($percenthoursachieved < $percentofmonth) {
                    $ptsborder = "border:3px red solid";
                    $phabgcolor = '#ff4d4d';
                } else {
                    $ptsborder = "border:3px green solid";
                    $phabgcolor = 'green';
                }

                ?>
                <div class="row mb-4">
                    <div class="col text-center">
                        <h3><?= $percentofmonth ?>% of Month Complete</h3>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-lg-2 col-md-6 col-sm-12 text-center border-end">
                        <div class="mb-2">
                            Total Sales Goal
                        </div>
                        <div class="mb-3">
                            <h3>$<?= number_format($mogoal, 0); ?></h3>
                        </div>

                    </div>
                    <div class="col-lg-2 col-md-6 col-sm-12 text-center border-end">
                        <div class="mb-2">
                            Daily Goal
                        </div>
                        <div class="mb-3">
                            <h3><?= $dailygoal ?></h3>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6 col-sm-12 text-center border-end">
                        <div class="mb-2">
                            Total Sold
                        </div>
                        <div class="mb-3">
                            <h3><?= asDollars($totalclosed) ?></h3>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6 col-sm-12 text-center border-end">
                        <div class="mb-2">
                            Labor Sales Goal | Achieved
                        </div>
                        <div class="mb-3">
                            <h3><span
                                    class="text-secondary">$<?= number_format($laborsalesgoal, 0) ?> | </span><?= asDollars($totallbr) ?>
                            </h3>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6 col-sm-12 text-center border-end">
                        <div class="mb-2">
                            Parts Sales Goal | Achieved
                        </div>
                        <div class="mb-3">
                            <h3><span
                                    class="text-secondary">$<?= number_format($partssalesgoal, 0) ?> | </span><?= asDollars($totalprts) ?>
                            </h3>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6 col-sm-12 text-center">
                        <div class="mb-2">
                            Labor Hours Goal | Achieved
                        </div>
                        <div class="mb-3">
                            <h3><span
                                    class="text-secondary">$<?= number_format($laborhoursgoal, 0) ?> | </span><?= number_format($totallbrhrs, 2) ?>
                            </h3>
                        </div>
                    </div>
                </div>

                <!-- Progress -->
                <div class="row mb-4">
                    <div class="col">
                        Sales Achieved
                        <div class="progress h-30">
                            <div class="progress-bar" role="progressbar" style="width: <?= $percentachieved ?>%;"
                                 aria-valuenow="<?= $percentachieved ?>"
                                 aria-valuemin="0" aria-valuemax="100"><?= $percentachieved ?>%
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col">
                        Labor Achieved
                        <div class="progress h-30">
                            <div class="progress-bar" role="progressbar" style="width: <?= $percentlaborachieved ?>%;"
                                 aria-valuenow="<?= $percentlaborachieved ?>"
                                 aria-valuemin="0" aria-valuemax="100"><?= $percentlaborachieved ?>%
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col">
                        Parts Achieved
                        <div class="progress h-30">
                            <div class="progress-bar" role="progressbar" style="width: <?= $percentpartsachieved ?>%;"
                                 aria-valuenow="<?= $percentpartsachieved ?>"
                                 aria-valuemin="0" aria-valuemax="100"><?= $percentpartsachieved ?>%
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mb-5">
                    <div class="col">
                        Labor Hours Achieved
                        <div class="progress h-30">
                            <div class="progress-bar" role="progressbar" style="width: <?= $percenthoursachieved ?>%;"
                                 aria-valuenow="<?= $percenthoursachieved ?>" aria-valuemin="0"
                                 aria-valuemax="100"><?= $percenthoursachieved ?>%
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advisors -->
                <div class="accordion" id="accordion-advisors">
                    <div class="accordion-item">
                        <div class="accordion-header" id="flush-advisors">
                            <button class="accordion-button collapsed mb-0 pb-0" type="button"
                                    data-mdb-toggle="collapse"
                                    data-mdb-target="#advisors" aria-expanded="false" aria-controls="advisors">
                                <h3>Advisors</h3>
                            </button>
                        </div>
                        <div id="advisors" class="accordion-collapse collapse show" aria-labelledby="flush-headingOne"
                             data-mdb-parent="#advisors">
                            <div class="accordion-body">

                                <?php
                                $saarray = array();
                                //$sd = date("Y-m-d",strtotime("first day of this month"))." 00:00:00";
                                $stmt = "select * from goals_sa where shopid = '$shopid' and mostartdate = '$sd'";
                                $cdate = new DateTime($sd);
                                $cdate->modify('last day of this month');
                                $cdate = $cdate->format('Y-m-d');
                                if ($query = $conn->prepare($stmt)) {
                                    $query->execute();
                                    $r = $query->get_result();
                                    while ($rs = $r->fetch_assoc()) {
                                        $totalsalesgoal = $rs['partssalesgoal'] + $rs['laborsalesgoal'];

                                        // get the actual sold for this advisor
                                        $stmt = "select sum(totallbr+totalprts) tro,sum(totallbr) tlabor, sum(totalprts) tparts, sum(totallbrhrs) thours, count(*) c"
                                            . " from repairorders where shopid = '$shopid' and writer = '" . $rs['writername'] . "' and status = 'closed' and rotype != '"
                                            . "no approval' and statusdate >= '$sd' and statusdate <= '$cdate'";
                                        //echo $stmt;
                                        if ($cquery = $conn->prepare($stmt)) {
                                            $cquery->execute();
                                            $cquery->bind_result($tro, $tlabor, $tparts, $thours, $carcount);
                                            $cquery->fetch();
                                            $cquery->close();
                                        } else {
                                            echo $conn->error;
                                            $tro = 0;
                                            $tlabor = 0;
                                            $tparts = 0;
                                            $thours = 0;
                                            $carcount = 0;
                                        }

                                        if ($rs['carcountgoal'] > 0) {
                                            $percentcarcount = round(($carcount / $rs['carcountgoal']) * 100, 0);
                                        } else {
                                            $percentcarcount = 0;
                                        }
                                        if ($rs['laborsalesgoal'] > 0) {
                                            $percentlaborsales = round(($tlabor / $rs['laborsalesgoal']) * 100, 0);
                                        } else {
                                            $percentlaborsales = 0;
                                        }
                                        if ($rs['laborhoursgoal'] > 0) {
                                            $percentlaborhours = round(($thours / $rs['laborhoursgoal']) * 100, 0);
                                        } else {
                                            $percentlaborhours = 0;
                                        }
                                        if ($rs['partssalesgoal'] > 0) {
                                            $percentpartssales = round(($tparts / $rs['partssalesgoal']) * 100, 0);
                                        } else {
                                            $percentpartssales = 0;
                                        }
                                        if ($totalsalesgoal > 0) {
                                            $percenttotalsales = round(($tro / $totalsalesgoal) * 100, 0);
                                        } else {
                                            $percenttotalsales = 0;
                                        }


                                        if ($percenttotalsales < $percentofmonth) {
                                            $ptsborder = "border:3px red solid";
                                            $ptsbgcolor = "#ff4d4d";
                                        } else {
                                            $ptsborder = "border:3px green solid";
                                            $ptsbgcolor = "green";
                                        }
                                        if ($percentcarcount < $percentofmonth) {
                                            $pccborder = "border:3px red solid";
                                            $pccbgcolor = "#ff4d4d";
                                        } else {
                                            $pccborder = "border:3px green solid";
                                            $pccbgcolor = "green";
                                        }
                                        if ($percentlaborsales < $percentofmonth) {
                                            $plsborder = "border:3px red solid";
                                            $plsbgcolor = "#ff4d4d";
                                        } else {
                                            $plsborder = "border:3px green solid";
                                            $plsbgcolor = "green";
                                        }
                                        if ($percentlaborhours < $percentofmonth) {
                                            $plhborder = "border:3px red solid";
                                            $plhbgcolor = "#ff4d4d";
                                        } else {
                                            $plhborder = "border:3px green solid";
                                            $plhbgcolor = "green";
                                        }
                                        if ($percentpartssales < $percentofmonth) {
                                            $ppsborder = "border:3px red solid";
                                            $ppsbgcolor = "#ff4d4d";
                                        } else {
                                            $ppsborder = "border:3px green solid";
                                            $ppsbgcolor = "green";
                                        }

                                        $saarray[] = array('id' => $rs['id'], 'ppsbgcolor' => $ppsbgcolor, 'plhbgcolor' => $plhbgcolor, 'plsbgcolor' => $plsbgcolor, 'pccbgcolor' => $pccbgcolor, 'ptsbgcolor' => $ptsbgcolor);
                                        ?>
                                        <!-- Advisor 1 -->
                                        <div class="row m-2">
                                            <table id="advisors-01" class="display responsive nowrap"
                                                   style="width:100%">
                                                <thead>
                                                <tr>
                                                    <th width="16%">Name</th>
                                                    <th>Car Count</th>
                                                    <th>Labor Sales</th>
                                                    <th>Labor Hours</th>
                                                    <th>Parts Sales</th>
                                                    <th>Total Sales</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr>
                                                    <td class="align-top">
                                                        <?= ucwords(strtolower($rs['writername'])) ?>
                                                    </td>
                                                    <td>
                                                        <div class="card-square p-3 me-3">
                                                            <table class="w-100">
                                                                <tbody>
                                                                <tr>
                                                                    <td class="text-secondary">Target</td>
                                                                    <td class="text-end"><?= asDollars($rs['carcountgoal']) ?></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-secondary">Current Count</td>
                                                                    <td class="text-end"><?= $carcount ?></td>
                                                                </tr>
                                                                <tr>
                                                                    <td colspan="2">
                                                                        <div class="text-primary mb-2">Car Count
                                                                            Achieved <?= $percentcarcount ?>%
                                                                        </div>
                                                                        <div class="progress h-10 mb-3">
                                                                            <div class="progress-bar" role="progressbar"
                                                                                 style="width: <?= $percentcarcount ?>%;"
                                                                                 aria-valuenow="<?= $percentcarcount ?>"
                                                                                 aria-valuemin="0"
                                                                                 aria-valuemax="100"></div>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="card-square p-3 me-3">
                                                            <table class="w-100">
                                                                <tbody>
                                                                <tr>
                                                                    <td class="text-secondary">Target</td>
                                                                    <td class="text-end"><?= asDollars($rs['laborsalesgoal']) ?></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-secondary">Current Sales</td>
                                                                    <td class="text-end"><?= asDollars($tlabor) ?></td>
                                                                </tr>
                                                                <tr>
                                                                    <td colspan="2">
                                                                        <div class="text-primary mb-2">Labor Sales
                                                                            Achieved <?= $percentlaborsales ?>%
                                                                        </div>
                                                                        <div class="progress h-10  mb-3">
                                                                            <div class="progress-bar" role="progressbar"
                                                                                 style="width: <?= $percentlaborsales ?>%;"
                                                                                 aria-valuenow="<?= $percentlaborsales ?>"
                                                                                 aria-valuemin="0"
                                                                                 aria-valuemax="100"></div>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="card-square p-3 me-3">
                                                            <table class="w-100">
                                                                <tbody>
                                                                <tr>
                                                                    <td class="text-secondary">Target</td>
                                                                    <td class="text-end"><?= asDollars($rs['laborhoursgoal']) ?></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-secondary">Current Hours</td>
                                                                    <td class="text-end"><?= number_format($thours, 2) ?></td>
                                                                </tr>
                                                                <tr>
                                                                    <td colspan="2">
                                                                        <div class="text-primary mb-2">Labor Hours
                                                                            Achieved <?= $percentlaborhours ?>%
                                                                        </div>
                                                                        <div class="progress mb-3"
                                                                             style="height: 10px;">
                                                                            <div class="progress-bar" role="progressbar"
                                                                                 style="width: <?= $percentlaborhours ?>%;"
                                                                                 aria-valuenow="75"
                                                                                 aria-valuemin="0"
                                                                                 aria-valuemax="100"></div>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="card-square p-3 me-3">
                                                            <table class="w-100">
                                                                <tbody>
                                                                <tr>
                                                                    <td class="text-secondary">Target</td>
                                                                    <td class="text-end"><?= asDollars($rs['partssalesgoal']) ?></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-secondary">Current Parts</td>
                                                                    <td class="text-end"><?= number_format($tparts, 2) ?></td>
                                                                </tr>
                                                                <tr>
                                                                    <td colspan="2">
                                                                        <div class="text-primary mb-2">Parts Sales
                                                                            Achieved <?= $percentpartssales ?>%
                                                                        </div>
                                                                        <div class="progress h-10 mb-3">
                                                                            <div class="progress-bar" role="progressbar"
                                                                                 style="width: <?= $percentpartssales ?>%;"
                                                                                 aria-valuenow="<?= $percentpartssales ?>"
                                                                                 aria-valuemin="0"
                                                                                 aria-valuemax="100"></div>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="card-square p-3">
                                                            <table class="w-100">
                                                                <tbody>
                                                                <tr>
                                                                    <td class="text-secondary">Target</td>
                                                                    <td class="text-end"><?= asDollars($totalsalesgoal) ?></td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-secondary">Current Total</td>
                                                                    <td class="text-end">
                                                                        $<?= number_format($tlabor + $tparts, 2) ?></td>
                                                                </tr>
                                                                <tr>
                                                                    <td colspan="2">
                                                                        <div class="text-primary mb-2">Total Sales
                                                                            Achieved <?= $percenttotalsales ?>%
                                                                        </div>
                                                                        <div class="progress h-10 mb-3">
                                                                            <div class="progress-bar" role="progressbar"
                                                                                 style="width: <?= $percenttotalsales ?>%;"
                                                                                 aria-valuenow="<?= $percenttotalsales ?>"
                                                                                 aria-valuemin="0"
                                                                                 aria-valuemax="100"></div>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                        <hr class="mt-4 mb-4">
                                        <?php
                                    }
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Techs -->
                <div class="accordion mt-4" id="accordion-techs">
                    <div class="accordion-item">
                        <div class="accordion-header" id="flush-techs">
                            <button class="accordion-button collapsed mb-0 pb-0" type="button"
                                    data-mdb-toggle="collapse"
                                    data-mdb-target="#techs" aria-expanded="false" aria-controls="techs">
                                <h3>Techs</h3>
                            </button>
                        </div>
                        <div id="techs" class="accordion-collapse collapse show" aria-labelledby="flush-headingOne"
                             data-mdb-parent="#techs">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col">
                                        <div class="text-center text-secondary">
                                            Sold Labor Hours
                                        </div>
                                    </div>
                                </div>
                                <hr class="pt-2 pb-3">

                                <?php
                                //$sd = date("Y-m-d",strtotime("first day of this month"))." 00:00:00";
                                $sd2 = date("Y-m-d", strtotime($sd));
                                //$cdate = date("Y-m-d",strtotime($sd));
                                $cdate = new DateTime($sd);
                                $cdate->modify('last day of this month');
                                $cdate = $cdate->format('Y-m-d');
                                $stmt = "select * from goals_tk where shopid = '$shopid' and mostartdate = '$sd'";
                                //echo $stmt;
                                $techar = array();
                                $roidlist = "";
                                if ($query = $conn->prepare($stmt)) {
                                    $query->execute();
                                    $r = $query->get_result();
                                    while ($rs = $r->fetch_assoc()) {
                                        $lhrs = 0;
                                        $lstmt = "SELECT distinct repairorders.ROID, labor.Tech, repairorders.Status, repairorders.FinalDate, repairorders.Customer, repairorders.VehInfo, labor.ROID"
                                            . " FROM repairorders INNER JOIN labor ON repairorders.ROID = labor.ROID and repairorders.shopid = labor.shopid"
                                            . " where labor.deleted = 'no' and labor.tech = '" . $rs['techname'] . "' and repairorders.rotype != 'No Approval' and repairorders.shopid = '$shopid' and (repairorders.Status = 'Closed' OR repairorders.Status = 'final') AND repairorders.statusdate Between '$sd2' And '$cdate'";
                                        //echo $lstmt.";<BR>";
                                        if ($lquery = $conn->prepare($lstmt)) {
                                            $lquery->execute();
                                            $lquery->store_result();
                                            $numrows = $lquery->num_rows();
                                            $lquery->free_result();
                                            if ($numrows > 0) {
                                                $lquery->execute();
                                                $l = $lquery->get_result();
                                                $lhrs = 0;
                                                $roidlist = "";
                                                while ($ls = $l->fetch_array()) {
                                                    $tech = $ls['Tech'];
                                                    $roid = $ls['ROID'];
                                                    $tstmt = "select sum(l.LaborHours) as lh from labor l "
                                                        . " INNER JOIN complaints c ON l.shopid = c.shopid and l.roid = c.roid and l.complaintid = c.complaintid "
                                                        . " where deleted = 'no' and c.cstatus = 'no' and l.shopid = '$shopid' and l.ROID = $roid and l.Tech = ?";
                                                    if ($tquery = $conn->prepare($tstmt)) {
                                                        $tquery->bind_param("s", $tech);
                                                        $tquery->execute();
                                                        $t = $tquery->get_result();
                                                        while ($ts = $t->fetch_assoc()) {
                                                            $lhrs += $ts['lh'];
                                                            $roidlist .= $ls['ROID'] . ",";
                                                        }
                                                    }
                                                }
                                            } else {
                                                $lhrs = 0;
                                            }
                                        } else {
                                            echo $conn->error;
                                            $lhrs = 0;
                                            $roidlist = "";
                                        }


                                        if ($lhrs > 0) {
                                            if ($rs['laborhoursgoal'] > 0) {
                                                $percentachievedlaborhours = ($lhrs / $rs['laborhoursgoal']) * 100;
                                            } else {
                                                $percentachievedlaborhours = 0;
                                            }
                                        } else {
                                            $percentachievedlaborhours = 0;
                                        }

                                        // get the labor timeclock hours
                                        if (substr($roidlist, -1) == ",") {
                                            $roidlist = substr($roidlist, 0, strlen($roidlist) - 1);
                                        }
                                        $runmins = 0;
                                        $lstmt = "select startdatetime sdt, enddatetime edt, roid from labortimeclock where"
                                            . " shopid = '$shopid' and roid in ($roidlist) and not isnull(enddatetime) and tech = '" . $rs['techname'] . "'";
                                        if ($lquery = $conn->prepare($lstmt)) {
                                            $lquery->execute();
                                            $l = $lquery->get_result();
                                            $runmins = 0;
                                            while ($ls = $l->fetch_assoc()) {
                                                $sdt = strtotime($ls['sdt']);
                                                $edt = strtotime($ls['edt']);
                                                $elapsedmins = round(abs($edt - $sdt) / 60, 2);
                                                $runmins += $elapsedmins;
                                            }
                                        }
                                        $decmins = round($runmins / 60, 2);

                                        if ($percentachievedlaborhours < $percentofmonth) {
                                            $ptsborder = "border:3px red solid";
                                            $ptsbgcolor = "red";
                                        } else {
                                            $ptsborder = "border:3px green solid";
                                            $ptsbgcolor = "green";
                                        }

                                        $techar[] = array('id' => $rs['id'], 'ptsbgcolor' => $ptsbgcolor);

                                        ?>
                                        <!-- tech 1 -->
                                        <div class="row m-1">
                                            <div class="col-md-2 col-sm-12 mb-3">
                                                <div>
                                                    <?= ucwords(strtolower($rs['techname'])); ?>
                                                </div>
                                            </div>
                                            <div class="col-md-10 col-sm-12">
                                                <div class="card-square p-3">
                                                    <table class="w-100">
                                                        <tbody>
                                                        <tr>
                                                            <td class="text-secondary">Target</td>
                                                            <td class="text-end"><?= number_format($rs['laborhoursgoal'],2) ?></td>
                                                        </tr>
                                                        <tr>
                                                            <td class="text-secondary">Current Total</td>
                                                            <td class="text-end"><?= number_format($lhrs, 2) ?></td>
                                                        </tr>
                                                        <tr>
                                                            <td class="text-secondary">Percent Achieved</td>
                                                            <td class="text-end"><?= number_format($percentachievedlaborhours,0); ?>%</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="text-secondary">Labor Timeclock (hours and
                                                                minutes)
                                                            </td>
                                                            <td class="text-end"><?= convertToHoursMins($runmins, '%02d:%02d') ?></td>
                                                        </tr>
                                                        <tr>
                                                            <td class="text-secondary">Labor Timeclock (decimal)</td>
                                                            <td class="text-end"><?= $decmins ?> hours</td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="2" class="pt-2">
                                                                <div class="text-primary mb-2">Labor Hours Achieved
                                                                    <?= $percentachievedlaborhours ?>%
                                                                </div>
                                                                <div class="progress h-10 mb-3">
                                                                    <div class="progress-bar" role="progressbar"
                                                                         style="width: <?= $percentachievedlaborhours ?>%;"
                                                                         aria-valuenow="<?= $percentachievedlaborhours ?>" aria-valuemin="0"
                                                                         aria-valuemax="100"></div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>

                                        <hr class="mt-4 mb-4">
                                        <?php
                                    }
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
        </section>
    </div>
</main>
<?php include getScriptsGlobal($component); ?>
<script>


    $(document).ready(function () {
        /*
        $('#shopsales').barIndicator({
            foreColor: '<?php echo $bgcolor; ?>',
                    horTitle: 'Percent of Sales Achieved'
                });
                $('#shoplabor').barIndicator({
                    foreColor: '<?php echo $plabgcolor; ?>',
                    horTitle: 'Percent of Labor Achieved'
                });
                $('#shopparts').barIndicator({
                    foreColor: '<?php echo $ppabgcolor; ?>',
                    horTitle: 'Percent of Parts Achieved'
                });
                $('#shophours').barIndicator({
                    foreColor: '<?php echo $phabgcolor; ?>',
                    horTitle: 'Percent of Labor Hours Achieved'
                });

*/
        <?php
        /*
        foreach ($saarray as $k => $v) {
            $id = $v['id'];
            $pccbgcolor = $v['pccbgcolor'];
            $plsbgcolor = $v['plsbgcolor'];
            $plhbgcolor = $v['plhbgcolor'];
            $ppsbgcolor = $v['ppsbgcolor'];
            $ptsbgcolor = $v['ptsbgcolor'];


            echo "$('#writercarcount$id').barIndicator({foreColor: '$pccbgcolor',horTitle: 'Car Count Achieved'})\r\n";
            echo "$('#writerlaborsales$id').barIndicator({foreColor: '$plsbgcolor',horTitle: 'Labor Sales Achieved'})\r\n";
            echo "$('#writerlaborhours$id').barIndicator({foreColor: '$plhbgcolor',horTitle: 'Labor Hours Achieved'})\r\n";
            echo "$('#writerpartssales$id').barIndicator({foreColor: '$ppsbgcolor',horTitle: 'Parts Sales Achieved'})\r\n";
            echo "$('#writertotalsales$id').barIndicator({foreColor: '$ptsbgcolor',horTitle: 'Total Sales Achieved'})\r\n";

        }

        foreach ($techar as $v) {

            $id = $v['id'];
            $ptsbgcolor = $v['ptsbgcolor'];
            echo "$('#tech$id').barIndicator({foreColor: '$ptsbgcolor',horTitle: 'Labor Hours Achieved'})\r\n";

        }
        */

        ?>

    })

</script>
<?php

//include getScriptsComponent($component);
include getFooterComponent($component);
?>

