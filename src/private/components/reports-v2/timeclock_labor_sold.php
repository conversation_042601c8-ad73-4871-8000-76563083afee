<?php
$title = 'TimeClock vs. Labor Sold';  // Report Title Goes Here

$component = "reports-v2";

include getHeadGlobal($component);
include getRulesGlobal($component);
include getHeaderGlobal($component);
include getMenuGlobal($component);

////require("../php/functions.php");

// Use this for Custom Reports
//require("../../../php/includes/reports/header_reports.php");
//require("../../../php/conn.php");
//require("../../functions.php");

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));
$job_completed = isset($_REQUEST['job_completed']) ? filter_var($_REQUEST['job_completed'], FILTER_SANITIZE_STRING) : "";
$ro_status = isset($_REQUEST['ro_status']) ? filter_var($_REQUEST['ro_status'], FILTER_SANITIZE_STRING) : "Closed";
// Page Variables

$subtitle = 'This report includes ' . $ro_status . ' status Repair Orders';  // Report SubTitle Goes Here - Hide if not needed

$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_autosize.php'; //Only change if a custom PHPExcel is created in the template folder

if ($job_completed == "yes") {
    $subtitle .= " and job status marked complete";
}

$roStatusSQL = "";
if ($ro_status != "all") {
    if ($ro_status != "open") {
        $roStatusSQL = "AND r.status = '" . $ro_status . "'";
    } else {
        $roStatusSQL = "AND r.status != 'Closed'";
    }
}


$jobSQL = "";
if (!empty($job_completed) && $job_completed == "yes") {
    $jobSQL = "AND c.acceptdecline = 'Job Complete'";
}

function convertToHoursAndMinutes($decimal) {
    $hours = floor($decimal);
    
    $minutes = round(($decimal - $hours) * 60);
    
    return $hours . 'h ' . $minutes . 'm';
}
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="title col breadcrumb d-flex align-items-center">
                <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                    class="text-secondary ps-3 pe-3">/</span>
                <h2><?= $title; ?></h2>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report">
        <?php
            include_once "inline_prompt.php";
        ?>
        <table class="dtable" style="width: 100%">
            <thead>
            <tr class="">
                <th>Technician</th>
                <th class="calc_qty">TimeClock Hours</th>
                <th class="calc_qty">Sold Hours</th>
                <th>Productivity</th>
            </tr>
            </thead>
            <tbody>
            <?php
            $tablefields = array('Technician', 'TimeClock Hours', 'Sold Hours', 'Productivity');//set table headings array for excel export
            $alldata = array();//this will hold all the data arrays to be exported to excel

            // Insert DB Query Here
            // Template Query Begins - Replace entire section
            $runtime = 0;
            $runlhrs = 0;
            $runprod = 0;
            $stmt = "select employeefirst ef,employeelast el, sum(TIMESTAMPDIFF(SECOND, startdatetime, enddatetime)) td, startdatetime sd,enddatetime ed from timeclock tc left join employees e on tc.shopid = e.shopid and tc.emp = e.id where tc.shopid = ? and date(startdatetime) >= ? and date(enddatetime) <= ? and (e.jobdesc like '%technician%' or e.jobdesc like '%general labor%') group by tc.emp order by employeelast, employeefirst";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sss", $shopid, $sd1, $ed1);
                $query->execute();
                $roresult = $query->get_result();
            } else {
                echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }
            while ($ro = $roresult->fetch_array()) {
                $tchours = round(($ro["td"] / 60) / 60, 2);
                $tech = $ro["el"] . ", " . $ro["ef"];

                $lstmt = "select coalesce(sum(laborhours),0) as lbrhours from labor l left join complaints c on l.shopid = c.shopid and l.roid = c.roid and l.complaintid = c.complaintid left join repairorders r on c.shopid = r.shopid and c.roid = r.roid where l.shopid = ? and l.tech = '$tech' and l.deleted = 'no' and c.cstatus = 'no' and r.statusdate >= ? and r.statusdate <= ? $roStatusSQL $jobSQL and r.rotype != 'No Approval'";
                if ($query = $conn->prepare($lstmt)) {
                    $query->bind_param("sss", $shopid, $sd1, $ed1);
                    $query->execute();
                    $lroresult = $query->get_result();
                } else {
                    echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }
                while ($lro = $lroresult->fetch_array()) {
                    $runtime += $tchours;
                    $runlhrs += $lro["lbrhours"];
                    ?>
                    <tr>
                    <td><?= ucwords(strtolower($tech)); ?></td>
                    <td><?= convertToHoursAndMinutes($tchours) . " : " . $tchours; ?></td>
                    <td><?= convertToHoursAndMinutes(round($lro["lbrhours"], 2)) . " : " . round($lro["lbrhours"], 2); ?></td>
                    <td><?php
                    if ($tchours > 0) {
                        $productivity = number_format(($lro["lbrhours"] / $tchours) * 100, 1);
                        echo $productivity . "%";
                        ?>
                        </td>
                        </tr>
                        <?php
                    }
                    $alldata[] = array(strtoupper($tech), convertToHoursAndMinutes($tchours) . " : " . $tchours, convertToHoursAndMinutes(round($lro["lbrhours"], 2)) . " : " . round($lro["lbrhours"], 2), $productivity . "%");//fill up the alldata array with the arrays of data to be shown in excel export
                } // end of while for loop
            }// end if for end of file

            $runprod = number_format(($runlhrs / $runtime) * 100, 1);
            $alldata[] = array('', '', '', '');
            $alldata[]=array('TOTALS',convertToHoursAndMinutes($runtime) . " : " . $runtime,convertToHoursAndMinutes($runlhrs) . " : " . $runlhrs,$runprod."%");
            ?>
            <tr class="table_total">
                <td><b>TOTALS</b></td>
                <td><b><?= convertToHoursAndMinutes($runtime) . " : " . $runtime; ?></b></td>
			 	<td><b><?= convertToHoursAndMinutes($runlhrs) . " : " . $runlhrs; ?></b></td>
                <td><b><?= $runprod . "%"; ?></b></td>
            </tr>
            </tbody>
        </table>
    </div>
</main>
<?php

require COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form.php";

include getScriptsGlobal($component);
include getFooterComponent($component);
?>
