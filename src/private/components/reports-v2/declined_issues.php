<?php

$component = "reports-v2";

include getRulesGlobal($component);
include getHeadGlobal($component);


// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));

$ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
$roTypeSQL = "AND rotype != 'No Approval'";
if ($ro_type != "all") {
    $roTypeSQL = "AND ro.rotype = '" . $ro_type . "'";
}

// Page Variables
$title = 'Declined Vehicle Issues - ' . ucwords(strtolower($ro_type));;  // Report Title Goes Here
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_autosize.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="title col breadcrumb d-flex align-items-center">
                <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                    class="text-secondary ps-3 pe-3">/</span>
                <h2><?= $title; ?></h2>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report">
        <?php
            include_once "inline_prompt.php";
        ?>
        <table class="dtable" style="width: 100%">
            <thead>
            <tr class="table_header table_head">
                <th>RO #</th>
                <th>Date In</th>
                <th>Customer</th>
                <th>Cell Phone</th>
                <th>Vehicle</th>
                <th>Veh Issue</th>
                <th class="calc_total" style="text-align:right">Total</th>
            </tr>
            </thead>
            <tbody>

            <?php
            $tablefields = array('RO #', 'Date In', 'Customer', 'Cell Phone', 'Vehicle', 'Veh Issue', 'Total');//set table headings array for excel export
            $alldata = array();//this will hold all the data arrays to be exported to excel


            // Insert DB Query Here

            // Template Query Begins - Replace entire section
            $runttlrec = 0;
            $stmt = "select ro.roid, datein, `customer`, cellphone, vehinfo,c.complaint,c.complaintid from repairorders ro,complaints c where c.shopid = ro.shopid and c.roid = ro.roid and ro.shopid = ? and datein >= ? and datein <= ? $roTypeSQL and c.acceptdecline = 'declined'";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sss", $shopid, $sd1, $ed1);
                $query->execute();
                $roresult = $query->get_result();
            } else {
                echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }
            while ($ro = $roresult->fetch_array()) {
                $roid = $ro["roid"];
                $statdate = date('m/d/Y', strtotime($ro["datein"]));
                ?>
                <tr>
                    <td><?= $ro["roid"]; ?></td>
                    <td><?= $statdate; ?></td>
                    <td><?= ($ro["customer"]); ?></td>
                    <td><?= formatPhone($ro["cellphone"]); ?></td>
                    <td><?= ($ro["vehinfo"]); ?></td>
                    <td><?= ($ro["complaint"]); ?></td>
                    <td style="text-align:right">
                        <?php
                        $cstmt = "select totalrec from recommend where comid = '" . $ro['complaintid'] . "' and shopid = '$shopid' and roid = '$roid'";
                        if ($query = $conn->prepare($cstmt)) {
                            $query->execute();
                            $query->bind_result($ttlrec);
                            $query->fetch();
                            $query->close();
                        } else {
                            echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }

                        echo asDollars($ttlrec, 2);
                        $runttlrec += $ttlrec;
                        ?>
                    </td>
                </tr>
                <?php
                $alldata[] = array(strtoupper($ro["roid"]), $statdate, strtoupper($ro["customer"]), formatPhone($ro["cellphone"]), strtoupper($ro["vehinfo"]), strtoupper($ro["complaint"]), asDollars($ttlrec));//fill up the alldata array with the arrays of data to be shown in excel export
            } // end of while for loop
            // end if for end of file

            $alldata[] = array('', '', '', '', '', '', '');
            $alldata[] = array('TOTALS', '', '', '', '', '', asDollars($runttlrec))
            ?>
            </tbody>
            <tfoot>
            <tr>
                <td><b>Total Declined Issue Amount</b></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td style="text-align:right"><b><?= asDollars($runttlrec); ?></b></td>
            </tr>
            </tfoot>
        </table>
    </div>
</main>
<?php

require COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form.php";

include getScriptsGlobal($component);
include getFooterComponent($component);
?>
