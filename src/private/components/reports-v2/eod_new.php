
<?php
$component = "reports-v2";
// Use this for Gen Pop Reports

include getHeadGlobal($component);
include getRulesGlobal($component);
$sd = $_GET['sdate'];
$ed = $_GET['edate'];

$sdate = date("Y-m-d", strtotime($_GET['sdate']));
$edate = date("Y-m-d", strtotime($_GET['edate']));
$shopid = $_COOKIE['shopid'];

$ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
$roTypeSQL = "AND rotype != 'No Approval'";
if ($ro_type != "all") {
    $roTypeSQL = "AND rotype = '" . $ro_type . "'";
}

$title = 'End Of Day Report - ' . ucwords(strtolower($ro_type));
$stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($companyname, $conamereports);
    $query->fetch();
    $query->close();
}

if ($conamereports == 'yes') {
    $title .= " - $companyname";
}

// get a list of the ro numbers
$rolist = "";
$totalro = 0;
$labor = 0;
$parts = 0;
$sublet = 0;
$tax = 0;
$partscost = 0;
$numros = 0;
$laborcost = 0;
$subletcost = 0;
$totalfees = 0;
$totallaborhours = 0;

$stmt = "select 1 as c,roid,totalro,totallbr,totalprts,totalsublet,salestax,partscost,totalfees from repairorders where shopid = '$shopid' and status = 'closed' $roTypeSQL and statusdate >= '$sdate' and statusdate <= '$edate'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        $rolist .= $rs['roid'] . ",";
        $labor += $rs['totallbr'];
        $parts += $rs['totalprts'];
        $sublet += $rs['totalsublet'];
        $tax += $rs['salestax'];
        $partscost += $rs['partscost'];
        $totalro += $rs['totalro'];
        $numros += $rs['c'];
        $totalfees += $rs['totalfees'];
    }
}

$rolist = substr($rolist, 0, strlen($rolist) - 1);

// calculate the labor cost using the rolist

$stmt = "select distinct laborid,l.tech,l.roid,l.laborhours from labor l inner join complaints c on l.shopid = c.shopid and l.complaintid = c.complaintid where l.shopid = '$shopid' and l.roid in ($rolist) and c.cstatus = 'no' and l.deleted = 'no'";
//echo $stmt."<BR>";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        // get the hourly rate for the tech
        $totallaborhours += $rs['laborhours'];
        $tech = $rs['tech'];
        //echo $tech."<BR>";
        $currlaborcost = 0;
        if (strpos($tech, ",") > 0) {
            $tar = explode(",", $tech);
            $techlast = trim($tar[0], " ");
            $techfirst = trim($tar[1], " ");
            $techrate = 0;

            // get the hourly rate for this tech
            $estmt = "select COALESCE(hourlyrate, 0), paytype from employees where shopid = '$shopid' and employeelast = '$techlast' and employeefirst = '$techfirst' order by active desc limit 1";
            //echo $estmt."<BR>";
            if ($equery = $conn->prepare($estmt)) {
                //$equery->bind_param("ss",$techlast,$techfirst);
                $equery->execute();
                $equery->bind_result($techrate, $paytype);
                $equery->fetch();
                $equery->close();
            }

            $currlaborcost = $techrate * $rs['laborhours'];

            if (strtoupper($paytype) == "HOURLY") {

                $tcstmt = "select tech,sum(TIMESTAMPDIFF(MINUTE, startdatetime, enddatetime)) mindiff FROM labortimeclock where shopid = '$shopid' and not isnull(enddatetime) and roid = " . $rs['roid'] . " and laborid = " . $rs['laborid'] . " group by tech";

                $exres = $conn->query($tcstmt);
                $intcost = 0;
                while ($exrow = $exres->fetch_array()) {
                    $mindiff = $exrow['mindiff'];

                    if ($mindiff > 0) {
                        $hourdiff = $mindiff / 60;
                        $intcost += ($techrate * $hourdiff);
                    }

                }

                if (!empty($intcost)) {
                    $currlaborcost = $intcost;
                }

            } else if (strtoupper($paytype) == "FLATRATE") {
                $currlaborcost = $techrate * $rs['laborhours'];
            }
        }

        $laborcost += $currlaborcost;
    }
}

// get sublet cost
$stmt = "select distinct s.complaintid,subletcost scost from sublet s inner join complaints c on s.shopid = c.shopid and s.complaintid = c.complaintid where s.shopid = '$shopid' and s.roid in ($rolist) and c.cstatus = 'no'";
//echo $stmt;
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        $subletcost += $rs['scost'];
    }
    $query->close();
}

$subtotal = $totalro - $tax;
if ($numros > 0) {
    $avghoursperro = number_format($totallaborhours / $numros, 2);
    $aro = number_format($subtotal / $numros, 2);
} else {
    $avghoursperro = 0;
    $aro = 0;
}
if ($totallaborhours > 0) {
    $effectivelaborrate = number_format($labor / $totallaborhours, 2);
} else {
    $effectivelaborrate = 0;
}

$gp = $subtotal - $partscost - $laborcost - $subletcost;

$subletprofit = $sublet - $subletcost;
$laborprofit = $labor - $laborcost;

if ($labor != 0) {
    $laborgppercent = $laborprofit / $labor;
} else {
    $laborgppercent = 0;
}

$partsprofit = $parts - $partscost;
if ($parts != 0) {
    $partsgppercent = $partsprofit / $parts;
} else {
    $partsgppercent = 0;
}

if ($gp > 0) {
    $partsgpp = number_format(100 * ($partsprofit / $gp), 0) . "%";
    $laborgpp = number_format(100 * ($laborprofit / $gp), 0) . "%";
    $subletgpp = number_format(100 * ($subletprofit / $gp), 0) . "%";
    $feesgpp = number_format(100 * ($totalfees / $gp), 0) . "%";
} else {
    $partsgpp = 0;
    $laborgpp = 0;
    $subletgpp = 0;
    $feesgpp = 0;
}

if ($subtotal > 0) {
    $gpp = 100 * ($gp / $subtotal);
    $partsgppoftotal = number_format(100 * ($partscost / $subtotal), 2) . "%";
} else {
    $gpp = 0;
    $partsgppoftotal = 0;
}
// labor
if ($subtotal > 0) {
    $gpl = number_format(100 * ($gp / $subtotal), 0);
    $laborgppoftotal = number_format(100 * ($laborcost / $subtotal), 2) . "%";
} else {
    $gpl = 0;
    $laborgppoftotal = 0;
}

// sublet
if ($subtotal > 0) {
    $gps = number_format(100 * ($gp / $subtotal), 0);
    $subletgppoftotal = number_format(100 * ($subletcost / $subtotal), 2) . "%";
} else {
    $gps = 0;
    $subletgppoftotal = 0;
}

$totalprofit = $partsprofit + $laborprofit + $subletprofit + $totalfees;
$gpperlh = 0;
if ($totallaborhours > 0) {
    $gpperlh = $totalprofit / $totallaborhours;
} else {
    $gpperlh = 0;
}

$stmt = "SELECT roid FROM repairorders WHERE shopid = ? $roTypeSQL AND status = 'closed' AND statusdate >= ? AND statusdate <= ? ";
//echo $stmt;

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("sss", $shopid, $sdate, $edate);
    $query->execute();
    $roresult = $query->get_result();
} else {
    echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$numofrows = $roresult->num_rows;

$shopidlist = "";

if ($roresult->num_rows > 0) {
    while ($ro = $roresult->fetch_array()) {
        $shopidlist = $shopidlist . $ro["roid"] . ",";
    } // end of while loop
}

if (substr($shopidlist, -1) == ",") {
    $shopidlist = substr($shopidlist, 0, strlen($shopidlist) - 1);

}

$tlabordiscount = 0;
$tpartsdiscount = 0;

$stmt = "SELECT coalesce(SUM(if(tech = 'DISCOUNT, DISCOUNT',abs(linetotal),discount)),0) as tlabordiscount FROM labor WHERE shopid = ? AND roid in (" . $shopidlist . ")";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($tlabordiscount);
    $query->fetch();
    $query->close();
}

$stmt = "SELECT coalesce(SUM(if(partnumber = 'DISCOUNT',abs(linettlprice),((partprice*quantity)-linettlprice))),0) as tpartsdiscount FROM parts WHERE shopid = ? AND roid in (" . $shopidlist . ") and (partnumber='Discount' OR discount!=0)";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($tpartsdiscount);
    $query->fetch();
    $query->close();
}
?>
<body>
<?php

include getHeaderGlobal($component);
include getMenuGlobal($component);
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-10 col-sm-10">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary d-print-none">Reports</a>
                        <span
                            class="text-secondary d-print-none ps-3 pe-3">/</span>
                        <h2 class="d-print-none"><?= $title; ?></span></h2>
                        <h4 class="d-none d-print-inline"><?= $title . $companyNamePrint; ?></h4>
                    </div>
                </div>
                <div class="col-md-2 col-sm-4">
                    <button type="button" class="btn btn-secondary btn-md d-print-none float-end"
                            onclick="printreport()">Print
                    </button>
                </div>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report d-print-none">
        <?php
            include_once "inline_prompt.php";
        ?>
    </div>
    <div class="d-flex">
        <section class="container-fluid">
            <h5>Repair Orders</h5>
            <table class="table table-condensed table-striped table-hover reports_table table_eod">
                <thead>
                <tr>
                    <th class="auto-style1" style="width: 25%"><strong>Data</strong></th>
                    <th class="auto-style1" style="width: 25%">&nbsp;</th>
                    <th class="auto-style1" style="width: 25%"><strong>Analysis</strong></th>
                    <th class="auto-style1" style="width: 25%" colspan="2">&nbsp;</th>
                </tr>
                </thead>
                <tr>
                    <td style="width: 25%; height: 29px;"># Repair Orders</td>
                    <td class="text-right border-right" style="width: 25%; height: 29px;"><?= $numros; ?></td>
                    <td style="width: 25%; height: 29px;">Current ARO (Subtotal / # RO's):</td>
                    <td class="text-right" style="width: 25%; height: 29px;" colspan="2">$<?= $aro; ?></td>
                </tr>
                <tr>
                    <td style="width: 25%">Parts Cost / % Of Subtotal</td>
                    <td class="text-right border-right" style="width: 25%">
                        $<?= number_format($partscost, 2) . " / " . $partsgppoftotal; ?></td>
                    <td style="width: 25%">Gross Profit&nbsp; | %</td>
                    <td class="text-right border-right" style="width: 16%">$<?= number_format($gp, 2); ?></td>
                    <td class="text-right" style="width: 25%"><?= number_format($gpp, 2) . "%"; ?></td>
                </tr>
                <tr>
                    <td style="width: 25%">Labor Cost / % Of Subtotal</td>
                    <td class="text-right border-right" style="width: 25%">
                        $<?= number_format($laborcost, 2) . " / " . $laborgppoftotal; ?></td>
                    <td></td>
                    <td></td>
                    <td class="text-right"></td>
                </tr>
                <tr>
                    <td style="width: 25%">Sublet Cost / % Of Subtotal</td>
                    <td class="text-right border-right" style="width: 25%">
                        $<?= number_format($subletcost, 2) . " / " . $subletgppoftotal; ?></td>
                    <td></td>
                    <td></td>
                    <td class="text-right"></td>
                </tr>
                <tr>
                    <td>Labor Sales</td>
                    <td class="text-right border-right">$<?= number_format($labor, 2); ?></td>
                    <td>Labor Profit&nbsp; | % of GP</td>
                    <td class="text-right border-right"
                        style="width: 16%"><?= number_format($laborgppercent * 100, 0) . "% / $" . number_format($laborprofit, 2); ?></td>
                    <td class="text-right"><?= $laborgpp; ?></td>
                </tr>
                <tr>
                    <td>Parts Sales</td>
                    <td class="text-right border-right">$<?= number_format($parts, 2); ?></td>
                    <td>Parts Profit&nbsp;&nbsp; | % of GP</td>
                    <td class="text-right border-right"
                        style="width: 16%"><?= number_format($partsgppercent * 100, 0) . "% / $" . number_format($partsprofit, 2); ?></td>
                    <td class="text-right"><?= $partsgpp; ?></td>
                </tr>
                <tr>
                    <td>Sublet Sales</td>
                    <td class="text-right border-right">$<?= number_format($sublet, 2); ?></td>
                    <td>Sublet Profit | % of GP</td>
                    <td class="text-right border-right" style="width: 16%">$<?= number_format($subletprofit, 2); ?></td>
                    <td class="text-right"><?= $subletgpp; ?></td>
                </tr>
                <tr>
                    <td>Fees</td>
                    <td class="text-right border-right">$<?= number_format($totalfees, 2); ?></td>
                    <td>Fees Profit&nbsp;&nbsp; | % of GP</td>
                    <td class="text-right border-right" style="width: 16%">$<?= number_format($totalfees, 2); ?></td>
                    <td class="text-right"><?= $feesgpp; ?></td>
                </tr>
                <tr>
                    <td>Discount (Parts/Labor/Total)</td>
                    <td class="text-right border-right">
                        (<?= asDollars($tpartsdiscount) . " / " . asDollars($tlabordiscount) . " / " . asDollars($tlabordiscount + $tpartsdiscount); ?>
                        )
                    </td>
                    <td>Total Labor Hours | Gross Profit Per Labor Hour</td>
                    <td class="text-right"
                        colspan="2"><?= round($totallaborhours, 2) . " | $" . round($gpperlh, 2); ?></td>
                </tr>
                <tr>
                    <td>Subtotal</td>
                    <td class="text-right border-right">$<?= number_format($subtotal, 2); ?></td>
                    <td>Total Labor Hours</td>
                    <td class="text-right" colspan="2"><?= round($totallaborhours, 2); ?></td>
                </tr>
                <tr>
                    <td>Tax</td>
                    <td class="text-right border-right">$<?= number_format($tax, 2); ?></td>
                    <td>Avg Hours per RO</td>
                    <td class="text-right" colspan="2"><?= number_format($avghoursperro, 2); ?></td>
                </tr>
                <tr>
                    <td class="autostyle2"><strong>Total RO Sales</strong></td>
                    <td class="text-right border-right auto-style2"><strong>$<?= number_format($totalro, 2); ?></strong>
                    </td>
                    <td class="autostyle2">Effective Labor Rate</td>
                    <td class="text-right auto-style2" colspan="2">$<?= number_format($effectivelaborrate, 2); ?></td>
                </tr>
            </table>
            <?php

            $pstotal = 0;
            $pscost = 0;
            $psdisc = 0;
            $pssubtotal = 0;
            $pstax = 0;
            $pscount = 0;

            $stmt = "select count(*) cnt, sum(pcost) as mcost, sum(discount) as pd, sum(subtotal) as subt, sum(tax) as st, sum(total) as ttl from ps where shopid = '$shopid' and status = 'Closed' and statusdate >= '$sdate' and statusdate <= '$edate'";

            if ($query = $conn->prepare($stmt)) {

                $query->execute();
                $query->bind_result($pscount, $pscost, $psdisc, $pssubtotal, $pstax, $pstotal);
                $query->fetch();
                $query->close();

            }

            // we need to get the psdetail parts cost
            // now get total parts cost
            $pspartcost = 0;

            $pcstmt = "select psid from ps where shopid = ? and status = 'Closed' and statusdate >= ? and statusdate <= ?";
            //set pcrs = con.execute(pcstmt)

            if ($query = $conn->prepare($pcstmt)) {
                $query->bind_param("sss", $shopid, $sdate, $edate);
                $query->execute();
                $pcrsresult = $query->get_result();
            } else {
                echo "pcrs Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            if ($pcrsresult->num_rows > 0) {
                while ($pcrs = $pcrsresult->fetch_array()) {
                    $psid = $pcrs["psid"];
                    $psdstmt = "select (cost*qty) as extcost from psdetail where shopid = ? and psid = ? ";

                    //set psdrs = con.execute(psdstmt)
                    if ($query = $conn->prepare($psdstmt)) {
                        $query->bind_param("si", $shopid, $psid);
                        $query->execute();
                        $psdsresult = $query->get_result();
                    } else {
                        echo "psds Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    if ($psdsresult->num_rows > 0) {
                        while ($psdrs = $psdsresult->fetch_array()) {
                            $pspartcost = $pspartcost + $psdrs["extcost"];
                        } // end psdrs while loop
                    } // end if psdrs
                } // end pcrs while loop
            } //end if pcrs


            if ($pscount > 0) {
                $avgps = $pstotal / $pscount;
            } else {
                $avgps = 0;
            }

            // cost is now coming off of psdetail
            //$psgp = number_format($pssubtotal - $pscost,2);
            $psgp = number_format($pssubtotal - $pspartcost, 2);


            //$psgpcalc = $pssubtotal - $pscost;
            $psgpcalc = $pssubtotal - $pspartcost;


            if ($pssubtotal > 0) {
                $psgpp = number_format(100 * ($psgpcalc / $pssubtotal), 0);
            } else {
                $psgpp = 0;
            }
            //$psgpp = number_format(100 * ($psgpcalc / $pssubtotal),0);
            if ($pssubtotal > 0) {
                ?>
                <h5>Parts Sales</h5>
                <table class="table table-condensed table-striped table-hover reports_table table_eod">
                    <thead>
                    <tr>
                        <th class="auto-style1" style="width: 25%"><strong>Data</strong></th>
                        <th class="auto-style1" style="width: 25%">&nbsp;</th>
                        <th class="auto-style1" style="width: 25%"><strong>Analysis</strong></th>
                        <th class="auto-style1" style="width: 25%" colspan="2">&nbsp;</th>
                    </tr>
                    </thead>
                    <tr>
                        <td># Parts Sales</td>
                        <td class="auto-style3 text-right border-right"><?= $pscount; ?></td>
                        <td>Gross Profit | %</td>
                        <td class="auto-style3 text-right">$<?= $psgp . " | " . $psgpp . "%"; ?></td>
                    </tr>
                    <tr>
                        <td>Parts Cost</td>
                        <td class="auto-style3 text-right border-right">$<?= number_format($pspartcost, 2); ?></td>
                        <td>Avg per Part Sale</td>
                        <td class="auto-style3 text-right">$<?= number_format($avgps, 2); ?></td>
                    </tr>
                    <tr>
                        <td>Parts Sales</td>
                        <td class="auto-style3 text-right border-right">
                            $<?= number_format($pstotal - $pstax, 2); ?></td>
                        <td>&nbsp;</td>
                        <td class="auto-style3" colspan="2">&nbsp;</td>
                    </tr>
                    <tr>
                        <td>Discount</td>
                        <td class="auto-style3 text-right border-right">$<?= number_format($psdisc, 2); ?></td>
                        <td>&nbsp;</td>
                        <td class="auto-style3" colspan="2">&nbsp;</td>
                    </tr>
                    <tr>
                        <td>Subtotal</td>
                        <td class="auto-style3 text-right border-right">$<?= number_format($pssubtotal, 2); ?></td>
                        <td>&nbsp;</td>
                        <td class="auto-style3" colspan="2">&nbsp;</td>
                    </tr>
                    <tr>
                        <td>Tax</td>
                        <td class="auto-style3 text-right border-right">$<?= number_format($pstax, 2); ?></td>
                        <td>&nbsp;</td>
                        <td class="auto-style3" colspan="2">&nbsp;</td>
                    </tr>
                    <tr>
                        <td style="text-align:left" class="auto-style2"><b>Total Parts Sales</b></td>
                        <td class="auto-style3 auto-style2 text-right border-right">
                            <b>$<?= number_format($pstotal, 2); ?></b>
                        </td>
                        <td class="auto-style2">&nbsp;</td>
                        <td class="auto-style3 auto-style2" colspan="2">&nbsp;</td>
                    </tr>
                </table>

                <?php
            }
            ?>

            <table class="table table-condensed table-striped table-hover reports_table table_eod"
                   style="page-break-before: avoid; page-break-after: avoid">
                <thead>
                    <tr>
                        <th>Daily Accounting Report</th>
                        <?php
                        $stmt = "select coalesce(sum(amt),0) from accountpayments where shopid = '" . $_COOKIE['shopid'] . "' and roid in (" . $shopidlist . ")";
                        if ($query = $conn->prepare($stmt)) {
                            $query->execute();
                            $query->bind_result($totalpaid);
                            $query->fetch();
                            $query->close();
                        }

                        ?>
                        <th colspan="2" style="text-primary"><b>Amounts for Closed RO's that have a
                                balance: <?= asDollars($totalro - $totalpaid); ?></b></th>
                    </tr>
                </thead>
                <tr class="table_header table_head">
                    <td class="auto-style1" style="text-align:left"><b>Payment Method</b></td>
                    <td class="auto-style1" style="text-align:center"><b>Count</b></td>
                    <td class="auto-style1" style="text-align:right"><b>Total Amount</b></td>
                </tr>
                <?php
                $tapcount = 0;
                $tapamount = 0;
                $stmt = "select distinct ptype as method from accountpayments where shopid = ? and pdate >= ? and pdate <= ? UNION select distinct ptype as method from `accountpayments-ps` where shopid = ? and pdate >= ? and pdate <= ? ";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("ssssss", $shopid, $sdate, $edate, $shopid, $sdate, $edate);
                    $query->execute();
                    $pmresult = $query->get_result();
                } else {
                    echo "Payment Methods Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                if ($pmresult->num_rows > 0) {
                    while ($pm = $pmresult->fetch_array()) {
                        $ptype = $pm["method"];
                        $stmt = "SELECT sum(amt) as a, count(*) as c FROM accountpayments WHERE shopid = ? AND ptype = ? AND pdate >= ? And pdate <= ? ";
                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("ssss", $shopid, $ptype, $sdate, $edate);
                            $query->execute();
                            $apresult = $query->get_result();
                        } else {
                            echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }
                        $ap = $apresult->fetch_array();
                        if ($apresult->num_rows > 0) {
                            if (!is_null($ap["a"])) {
                                $apamount = $ap["a"];
                            } else {
                                $apamount = 0;
                            }
                            if (!is_null($ap["c"])) {
                                $apcount = (int)$ap["c"];
                            } else {
                                $apcount = 0;
                            }
                        }
                        $stmt = "SELECT sum(amt) as a, count(*) as c FROM `accountpayments-ps` WHERE shopid = ? AND ptype = ? AND pdate >= ? And pdate <= ? ";
                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("ssss", $shopid, $ptype, $sdate, $edate);
                            $query->execute();
                            $apsresult = $query->get_result();
                        } else {
                            echo "Account Payments PS Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }
                        $aps = $apsresult->fetch_array();
                        if ($apsresult->num_rows > 0) {
                            if (!is_null($aps["a"])) {
                                $apamount = $apamount + $aps["a"];
                            } else {
                                $apamount = $apamount + (int)0;
                            }
                            if (!is_null($aps["c"])) {
                                $apcount = $apcount + (int)$aps["c"];
                            } else {
                                $apcount = $apcount + (int)0;
                            }
                        }

                        if ($apcount == 0) continue;

                        $tapcount = $tapcount + $apcount;
                        $tapamount = $tapamount + $apamount;
                        ?>
                        <tr>
                            <td style="padding-top:1px; text-align:left"><?= ucwords(strtolower($pm["method"])); ?></td>
                            <td style="padding-top:1px; text-align:center"><?= $apcount; ?></td>
                            <td style="padding-top:1px; text-align:right;"><?= asDollars($apamount, 2); ?></td>
                        </tr>
                        <?php
                        //$alldata[]=array(strtoupper($pm["method"]), $apcount, asDollars($apamount),'');

                    } // while loop
                } // end if pm
                ?>
                <tr>
                    <td class="auto-style2" style="text-align:left"><strong>Total: </strong></td>
                    <td class="auto-style2" style="text-align:center"><strong><?= $tapcount; ?></strong></td>
                    <td class="auto-style2" style="text-align:right;"><strong><?= asDollars($tapamount); ?></strong>
                    </td>
                </tr>
            </table>
</main>
<?php
include getScriptsGlobal($component);
//include getScriptsComponent($component);
include getFooterComponent($component);
?>
