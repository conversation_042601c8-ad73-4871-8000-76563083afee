<?php
// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$component = "reports-v2";
// Page Variables
$title = 'Parts On WIP (Allocated Parts)';  // Report Title Goes Here
//$subtitle = 'my subtitle';  // Report SubTitle Goes Here - Hide if not needed
$subtitle = "";

include getHeadGlobal($component);
include getRulesGlobal($component);
include getHeaderGlobal($component);
include getMenuGlobal($component);

$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_autosize.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>

    <main id="reports">
        <div class="report">
            <div class="col-12">
                <div class="title col breadcrumb d-flex align-items-center">
                    <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                        class="text-secondary ps-3 pe-3">/</span>
                    <h2><?= $title; ?></h2>
                </div>
                <hr/>
                <p class="card-title">
                    <?php
                    if ((!empty($sd))) {
                        echo($sd);
                    }
                    if (!empty($sd) && !empty($ed)) {
                        echo ' to ';
                    }
                    if (!empty($ed)) {
                        echo $ed;
                    }
                    if (empty($sd) && empty($ed)) {
                        echo date('m/d/Y');
                    }
                    ?>
                </p>
                <p>
                    <?= $subtitle ?>
                </p>
            </div>
        </div>
        <div class="report">
            <table class="dtable" style="width: 100%">
                <thead>
                <tr class="">
                    <th class="w-10">RO #</th>
                    <th>Part #</th>
                    <th>Description</th>
                    <th class="calc_qty">Qty</th>
                    <th class="calc_total" style="text-align:right">Cost Each</th>
                    <th class="calc_total" style="text-align:right">Total Cost</th>
                </tr>
                </thead>
                <?php
                $tablefields = array('RO #', 'Part #', 'Description', 'Qty', 'Cost Each', 'Total Cost');//set table headings array for excel export
                $alldata = array();//this will hold all the data arrays to be exported to excel

                // Insert DB Query Here
                $qty = 0;
                $c = 0;
                $tc = 0;
                $gtqty = 0;
                $gtc = 0;
                $gttc = 0;
                $stmt = "select r.roid, partnumber, partdesc, cost, quantity from repairorders r inner join parts p on r.shopid = p.shopid and r.roid = p.roid where r.shopid = ? and r.`status` != 'closed' and r.`rotype` != 'no approval' order by partnumber";
                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $roresult = $query->get_result();
                } else {
                    echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }
                while ($ro = $roresult->fetch_array()) {
                    $qty = $ro["quantity"];
                    $c = $ro["cost"];
                    $tc = $qty * $c;
                    $pstmt = "select partnumber from partsinventory where shopid = ? and partnumber = ?";
                    if ($query = $conn->prepare($pstmt)) {
                        $query->bind_param("ss", $shopid, $ro['partnumber']);
                        $query->execute();
                        $proresult = $query->get_result();
                    } else {
                        echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }
                    ?>
                    <!-- Table Results Begin -->
                    <tr class="table_data">
                        <td><?= $ro["roid"]; ?></td>
                        <td><?= ucwords(strtolower($ro["partnumber"])); ?></td>
                        <td><?= ucwords(strtolower($ro["partdesc"])); ?></td>
                        <td><?= $qty; ?></td>
                        <td style="text-align:right"><?= asDollars($c); ?></td>
                        <td style="text-align:right"><?= asDollars($tc); ?></td>
                    </tr>

                    <?php
                    $gtqty += $qty;
                    $gtc += $c;
                    $gttc += $tc;
                    $alldata[] = array($ro['roid'], strtoupper($ro["partnumber"]), strtoupper($ro["partdesc"]), $qty, asDollars($c), asDollars($tc)); //fill up the alldata array with the arrays of data to be shown in excel export
                } // end of while for loop
                $alldata[] = array('', '', '', '', '', '');
                $alldata[] = array('TOTALS', '', '', $gtqty, asDollars($gtc), asDollars($gttc));
                ?>
                <tfoot>
                <tr class="table_total">
                    <td><b>TOTALS</b></td>
                    <td></td>
                    <td></td>
                    <td><b><?= $gtqty; ?></b></td>
                    <td style="text-align:right"><b><?= asDollars($gtc); ?></b></td>
                    <td style="text-align:right"><b><?= asDollars($gttc); ?></b></td>
                </tr>
                </tfoot>
            </table>
        </div>
    </main>
<?php

require COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form.php";

include getScriptsComponent($component);
include getFooterComponent($component);
?>