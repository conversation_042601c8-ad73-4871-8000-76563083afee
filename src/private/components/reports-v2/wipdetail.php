<?php

$component = "reports-v2";
// Use this for Gen Pop Reports
$title = 'Work In Progress Detail';  // Report Title Goes Here
//require COMPONENTS_PRIVATE_PATH."/reports-v2/includes/header_reports.php";
include getHeadGlobal($component);
include getRulesGlobal($component);
//require("../php/functions.php");

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = "";
$ed = "";

$subtitle = "Issue Subtotal Does Not Include Fees";
//$subtitle = '';  // Report SubTitle Goes Here - Hide if not needed
$todaydate = "todaydate";
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports-v2/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder
?>
<body>
    <style media="print">
        @page {
            size: auto;
            /*size: auto;   /* auto is the initial value */
            /* this affects the margin in the printer settings
            margin: 10mm 10mm 10mm 10mm;
             */
            margin: 0.15in !important;
        }
    </style>
<?php

include getHeaderGlobal($component);
include getMenuGlobal($component);

?>

    <!-- Column Headers Insert Report Variables here -->
    <main id="reports">
        <div class="report">
            <div class="col-12">
                <div class="row">
                    <div class="col-md-10 col-sm-10">
                        <div class="title col breadcrumb d-flex align-items-center mb-0">
                            <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary d-print-none">Reports</a>
                            <span
                                    class="text-secondary d-print-none ps-3 pe-3">/</span>
                            <h2><?= $title; ?> <span class="d-none d-print-inline"> <?= $companyNamePrint ?></span></h2>
                        </div>
                    </div>
                </div>
                <hr/>
                <p class="card-title">
                    <?php
                    if ((!empty($sd))) {
                        echo($sd);
                    }
                    if (!empty($sd) && !empty($ed)) {
                        echo ' to ';
                    }
                    if (!empty($ed)) {
                        echo $ed;
                    }
                    if (empty($sd) && empty($ed)) {
                        echo date('m/d/Y');
                    }
                    ?>
                </p>
                <p>
                    <?= $subtitle ?>
                </p>
            </div>
        </div>
        <div class="report">
            <table class="wip_adv_table w-100" style="width: 100%">
                <thead>
                <tr class="">
                    <th style="width: 6%">Date In</th>
                    <th style="width: 6%">Status</th>
                    <th style="width: 5%">RO #</th>
                    <th style="width: 14%">Customer</th>
                    <th style="width: 14%">Phone</th>
                    <th style="width: 14%">Email</th>
                    <th style="width: 16%">Vehicle</th>
                    <th style="text-align:right; width: 5%">Sold Hours</th>
                    <th style="text-align:right; width: 5%">Labor Clock</th>
                    <th style="text-align:right; width: 5%">%Complete</th>
                    <th style="text-align:right; width: 5%">Total RO</th>
                    <th style="text-align:right; width: 5%">Balance Owed</th>
                    <th>Issue</th>
                </tr>
                </thead>
                <tbody>
                <?php
                $tablefields = array('Date In', 'Status', 'RO #', 'Customer', 'Phone', 'Email', 'Vehicle', 'Sold Hours', 'Labor Clock', '%Complete', 'Total RO', 'Balance Owed');//set table headings array for excel export
                $alldata = array();//this will hold all the data arrays to be exported to excel


                // Insert DB Query Here

                // Template Query Begins - Replace entire section
                $runttl = $runbal = 0;
                $query = "select Writer, MajorComplaint, ROID, ucase(r.Status) as stat, DateIn, Customer, CustomerPhone, CustomerWork, CellPhone, email, VehInfo, TotalRO, totalfees, VehLicNum, CustomerID, ROType, balance from repairorders r,rostatus s where r.shopid=s.shopid and r.status=s.status and r.shopid ='$shopid' and r.Status <> 'Closed' and ROType <> 'No Approval' ORDER BY s.displayorder, ROID DESC";
                $roresult = mysqli_query($conn, $query);
                if (!$roresult) {
                    die("No Data To Display!");
                }
                if ($roresult->num_rows > 0) {
                while ($rs = $roresult->fetch_array()) {
                $roid = $rs["ROID"];

                $stmt = "select sum(laborhours) as lh from labor where deleted = 'no' and shopid = '$shopid' and roid = '" . $rs['ROID'] . "'";
                $query = mysqli_query($conn, $stmt);
                $trs = $query->fetch_array();
                if (!empty($trs['lh'])) {
                    $soldhours = round($trs["lh"], 2);
                } else {
                    $soldhours = 0;
                }
                $stmt = "select sum(round((timestampdiff(second,startdatetime,enddatetime)/60)/60,2)) as thours, labortimeclock.* from labortimeclock where shopid = '$shopid' and roid = '" . $rs['ROID'] . "'";
                $query = mysqli_query($conn, $stmt);
                $trs = $query->fetch_array();
                if (!empty($trs['thours'])) {
                    $laborclock = $trs["thours"];
                } else {
                    $laborclock = 0;
                }
                if ($laborclock > 0 && $soldhours > 0) {
                    $percentcomplete = round(($laborclock / $soldhours) * 100, 2);
                } else {
                    $percentcomplete = 0;
                }
                if (is_numeric(substr($rs['stat'], 0, 1))) {
                    $thestat = substr($rs['stat'], 1);
                } else {
                    $thestat = $rs["stat"];
                }
                $runttl += $rs["TotalRO"];
                $runbal += $rs["balance"];
                // $stmt = "select complaintid, roid, complaint FROM complaints WHERE roid = '".$rs['ROID']."' AND shopid = '$shopid' AND acceptdecline != 'Declined'";
                // $query = mysqli_query($conn, $stmt);
                // $custcomplain = $query->fetch_array();
                // if (!$custcomplain) {
                // 	die("No Customer Complaint");
                // }

                // Template Query Ends

                ?>
                <tr class="">
                    <td><?= date('n/j/Y', strtotime($rs["DateIn"])) ?></td>
                    <td><?= $thestat ?></td>
                    <td><?= $roid ?></td>
                    <td><?= strtoupper($rs["Customer"]) ?></td>
                    <td><?php
                        if (strlen($rs["CustomerPhone"]) > 5) {
                            echo " <b>H:</b>" . formatPhone($rs["CustomerPhone"]);
                        }
                        if (strlen($rs["CustomerWork"]) > 5) {
                            echo " <b>W:</b>" . formatPhone($rs["CustomerWork"]);
                        }
                        if (strlen($rs["CellPhone"]) > 5) {
                            echo " <b>C:</b>" . formatPhone($rs["CellPhone"]);
                        } ?>
                    </td>
                    <td><?= strtoupper($rs["email"]) ?></td>
                    <td><?= strtoupper($rs["VehInfo"]) ?></td>
                    <td style="text-align:right;"><?= $soldhours ?></td>
                    <td style="text-align:right;"><?= $laborclock ?></td>
                    <td style="text-align:right;"><?= number_format($percentcomplete, 2) . '%' ?></td>
                    <td style="text-align:right;">$<?= number_format($rs['TotalRO'], 2) ?></td>
                    <td style="text-align:right;">$<?= number_format($rs['balance'], 2) ?></td>
                    <?php
                    $alldata[] = array(date('n/j/Y', strtotime($rs["DateIn"])), $thestat, $rs['ROID'], $rs["Customer"], "H:" . $rs["CustomerPhone"] . " W:" . $rs["CustomerWork"] . " C:" . $rs["CellPhone"], $rs['email'], $rs["VehInfo"], $soldhours, $laborclock, number_format($percentcomplete, 2) . '%', '$' . number_format($rs['TotalRO'], 2), '$' . number_format($rs['balance'], 2));

                    $compid = $compdesc = "";
                    $subtotal = $ttlpartsprice = $ttllaborprice = $ttlsubletprice = 0;
                    $cstmt = "select * from complaints where cstatus = 'no' and shopid = ? and acceptdecline != 'declined' and roid = ?";
                    $subtable = "";
                    if ($cquery = $conn->prepare($cstmt)) {
                        $cquery->bind_param("ss", $shopid, $roid);
                        $cquery->execute();
                        $cr = $cquery->get_result();

                        if ($cr->num_rows > 0) {

                            $subtable = "<table class='w-100'>";
                            while ($crs = $cr->fetch_assoc()) {
                                $compid = $crs["complaintid"];
                                $compdesc = $crs["complaint"];
                                $pstmt = "SELECT SUM(linettlprice) FROM parts WHERE shopid = ? AND complaintid = ?";
                                if ($query = $conn->prepare($pstmt)) {
                                    $query->bind_param("ss", $shopid, $compid);
                                    $query->execute();
                                    $query->bind_result($ttlpartsprice);
                                    $query->fetch();
                                    $query->close();
                                }
                                $lstmt = "SELECT SUM(linetotal) FROM labor WHERE shopid = ? AND complaintid = ?";
                                if ($query = $conn->prepare($lstmt)) {
                                    $query->bind_param("ss", $shopid, $compid);
                                    $query->execute();
                                    $query->bind_result($ttllaborprice);
                                    $query->fetch();
                                    $query->close();
                                }
                                $sstmt = "SELECT SUM(subletprice) FROM sublet WHERE shopid = ? AND complaintid = ?";
                                if ($query = $conn->prepare($sstmt)) {
                                    $query->bind_param("ss", $shopid, $compid);
                                    $query->execute();
                                    $query->bind_result($ttlsubletprice);
                                    $query->fetch();
                                    $query->close();
                                }
                                $subtotal = $ttlpartsprice + $ttllaborprice + $ttlsubletprice;
                                $subtable .= '<tr><td style="width: 6%"></td> <td style="width:84%" colspan="8"><b>Issue:</b><em>' . $compdesc . '</em></td><td style="text-align: right; width: 5%">Subtotal</td><td style="text-align: right; width: 5%">' . asDollars($subtotal) . '</td></tr>';
                                $alldata[] = array('Issue:' . $compdesc, '', '', '', '', '', '', '', '', '', 'Subtotal', asDollars($subtotal));
                            }
                            $subtable .= "</table>";
                        }
                        //    $comp=rtrim($comp,', ');
                    }
                    echo "<td>".$subtable."</td>";
                    echo "</tr>";
                    } // end of while for loop
                    } // end if for end of file


                    $alldata[] = array('', '', '', '', '', '', '', '', '', '');
                    $alldata[] = array('TOTAL IN PROCESS	', '', '', '', '', '', '', '', '', '', '$' . number_format($runttl, 2), '$' . number_format($runbal, 2));

                    ?>

                    <!-- Table totals go here, if needed-->
                </tbody>
                <tfoot>
                <tr class="table_total">
                    <td><b>TOTAL IN PROCESS</b></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td style="text-align:right;"><b>$<?= number_format($runttl, 2) ?></b></td>
                    <td style="text-align:right;"><b>$<?= number_format($runbal, 2) ?></b></td>
                    <td></td>
                </tr>
                </tfoot>
            </table>
        </div>
    </main>


<?php
include COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form_todaydate.php"; //only for reports with today date req
?>
    <script>
        $(document).ready(function () {
            let floatVal = function (i) {
                return typeof i === 'string' ? Number(i.replace(/[$,]/g, '')) : typeof i === 'number' ? i : 0;
            };

            groupCol_writer = 2;
            groupCol_issue = 12;
            let table = $(".wip_adv_table").DataTable({
                responsive: true,
                fixedHeader: {
                    headerOffset: 68
                },
                colReorder: true,
                select: true,
                scrollY: false,
                scrollX: false,
                scroller: false,
                paging: false,
                language: {
                    searchPanes: {
                        emptyPanes: 'No records found in this date range'
                    },
                    search: "_INPUT_",
                    searchPlaceholder: "Search..."
                },
                buttons: [
                    {
                        extend: 'csv',
                        text: '<i class="fas fa-file-csv fa-xl"></i>',
                        title: '<?= !empty($title) ? $title : 'Shop Boss Report' ?>',
                    },
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel fa-xl"></i>',
                        title: '<?= !empty($title) ? $title : 'Shop Boss Report' ?>',
                        action: function ( e, dt, node, config ) {
                            $("#excelform").submit();
                        }
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print fa-xl"></i>',
                        title: '<?= !empty($title) ? $title : 'Shop Boss Report' ?>',
                        footer: true,
                        action: function ( e, dt, node, config ) {
                            window.open("prints/wipdetail.php", "_blank");
                        }
                    }
                ],
                dom: "<'row'<'col-sm-12 col-md-6 dt_Search text-secondary'f><'col-sm-12 col-md-6 text-right dt_btns text-secondary'B>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
                columnDefs: [{visible: false, targets: groupCol_issue}],
                drawCallback: function (settings) {
                    var api = this.api();
                    var rows = api.rows({page: 'current'}).nodes();
                    var last = null;
                    api
                        .column(groupCol_issue, {page: 'all'})
                        .data()
                        .each(function (group, i) {
                            if (last !== group) {
                                $(rows)
                                    .eq(i)
                                    .after('<tr class="group"><td colspan="13">' + group + '</td></tr>');

                                last = group;
                            }
                        });

                },
            });
        });

        function printTable() {
            //  alert("If you would like to print this report, we suggest 'Landscape' mode as it fits better on the page");
            $('.wip_adv_table').printThis({
                printContainer: false,
                importStyle : true,
                importCss : true
            });
        }
    </script>
<?php
include getScriptsComponent($component);
include getFooterComponent($component);
?>