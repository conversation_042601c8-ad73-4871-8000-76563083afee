
<?php
$component = "reports-v2";
// Use this for Gen Pop Reports

include getHeadGlobal($component);
include getRulesGlobal($component);

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));

// $ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
// $roTypeSQL = "AND rotype != 'No Approval'";
// if ($ro_type != "all") {
//     $roTypeSQL = "AND rotype = '" . $ro_type . "'";
// }

// Page Variables
$title = 'Cash In Report - ' . ucwords(strtolower($ro_type));  // Report Title Goes Here
$subtitle = 'Summary and Detail';  // Report SubTitle Goes Here - Hide if not needed
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_multiple.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
<body>
<?php

include getHeaderGlobal($component);
include getMenuGlobal($component);
?>
<!-- Column Headers Insert Report Variables here -->
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-10 col-sm-10">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary d-print-none">Reports</a>
                        <span
                                class="text-secondary d-print-none ps-3 pe-3">/</span>
                        <h2 class="d-print-none"><?= $title; ?></span></h2>
                        <h4 class="d-none d-print-inline"><?= $title . $companyNamePrint; ?></h4>
                    </div>
                </div>
                <div class="col-md-2 col-sm-4">
                    <button type="button" class="btn btn-secondary btn-md d-print-none float-end"
                            onclick="printreport()">Print
                    </button>
                    <button type="button" class="btn btn-secondary btn-md d-print-none float-end me-2"
                            onclick="$('#excelform').submit();">Excel
                    </button>
                </div>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report d-print-none">
        <?php
            include_once "inline_prompt.php";
        ?>
    </div>
    <div class="d-flex">
        <section class="container-fluid" id="">
            <div class="row">
                <div class="col-md-3"></div>
                <div class="col-md-6">
                    <h5 class="text-center">Summary</h5>
                    <table class="reports_table table table-condensed table-striped table-hover w-100">
                        <?php
                        $tablefields = array('Payment Method', 'Count', 'Total Amount', '');//set table headings array for excel export
                        $alldata = array();//this will hold all the data arrays to be exported to excel

                        $tapcount = 0;
                        $tapamount = 0;
                        $totalsurcharge = 0;

                        // Insert DB Query Here

                        // Template Query Begins - Replace entire section

                        $stmt = "select distinct ptype as method from accountpayments where shopid = ? and pdate >= ? and pdate <= ? UNION select distinct ptype as method from `accountpayments-ps` where shopid = ? and pdate >= ? and pdate <= ? UNION SELECT distinct method FROM paymentmethods WHERE shopid = ? ";

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("sssssss", $shopid, $sd1, $ed1, $shopid, $sd1, $ed1, $shopid);
                            $query->execute();
                            $pmresult = $query->get_result();
                        } else {
                            echo "Payment Methods Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }

                        $pmArr = array();
                        if ($pmresult->num_rows > 0) {
                            while ($pm = $pmresult->fetch_array()) {
                                $ptype = $pm["method"];

                                $stmt = "SELECT sum(amt) as a, count(*) as c, sum(surcharge) as sa ";
                                $stmt .= " FROM accountpayments ";
                                $stmt .= "WHERE shopid = ? ";
                                $stmt .= "  AND ptype = ?";
                                $stmt .= "  AND pdate >= ? And pdate <= ? ";

                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("ssss", $shopid, $ptype, $sd1, $ed1);
                                    $query->execute();
                                    $apresult = $query->get_result();
                                } else {
                                    echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                }

                                //printf(str_replace("?","'"."%s"."'",$stmt),$shopid,$ptype,$sdate,$edate);

                                $ap = $apresult->fetch_array();

                                if ($apresult->num_rows > 0) {

                                    if (!is_null($ap["a"])) {
                                        $apamount = $ap["a"];
                                    } else {
                                        $apamount = 0;
                                    }
                                    if (!is_null($ap["c"])) {
                                        $apcount = (int)$ap["c"];
                                    } else {
                                        $apcount = 0;
                                    }
                                    $surcharge = $ap['sa'];
                                }
                                $stmt = "SELECT sum(amt) as a, count(*) as c, sum(surcharge) as sa ";
                                $stmt .= " FROM `accountpayments-ps` ";
                                $stmt .= "WHERE shopid = ? ";
                                $stmt .= "  AND ptype = ?";
                                $stmt .= "  AND pdate >= ? And pdate <= ? ";

                                if ($query = $conn->prepare($stmt)) {
                                    $query->bind_param("ssss", $shopid, $ptype, $sd1, $ed1);
                                    $query->execute();
                                    $apsresult = $query->get_result();
                                } else {
                                    echo "Account Payments PS Prepare failed: (" . $conn->errno . ") " . $conn->error;
                                }

                                $aps = $apsresult->fetch_array();

                                if ($apsresult->num_rows > 0) {

                                    if (!is_null($aps["a"])) {
                                        $apamount = $apamount + $aps["a"];
                                    } else {
                                        $apamount = $apamount + (int)0;
                                    }
                                    if (!is_null($aps["c"])) {
                                        $apcount = $apcount + (int)$aps["c"];
                                    } else {
                                        $apcount = $apcount + (int)0;
                                    }
                                    $surcharge += $aps['sa'];
                                }
                                $tapcount = $tapcount + $apcount;
                                $tapamount = $tapamount + $apamount;
                                $totalsurcharge += $surcharge;

                                $nptype = strtoupper(trim($ptype));

                                if ($nptype == "MC") {
                                    $nptype = "MASTERCARD";
                                } else if ($nptype == "AMEX") {
                                    $nptype = "AMERICAN EXPRESS";
                                } else if ($nptype == "DISC") {
                                    $nptype = "DISCOVER";
                                }

                                if (isset($pmArr[$nptype])) {
                                    $pmcount = $pmArr[$nptype]['count'] + $apcount;
                                    $pmamt = $pmArr[$nptype]['amount'] + $apamount;
                                    $pmsamt = $pmArr[$nptype]['surcharge'] + $surcharge;
                                } else {
                                    $pmcount = $apcount;
                                    $pmamt = $apamount;
                                    $pmsamt = $surcharge;
                                }

                                $pmArr[$nptype] = array(
                                    'ptype' => ucwords(strtolower($nptype)),
                                    'count' => $pmcount,
                                    'amount' => $pmamt,
                                    'surcharge' => $pmsamt
                                );

                            } 
                        }?>

                        <thead>
                        <tr class="">
                            <th>Payment Method</th>
                            <th class="text-center">Count</th>
                            <th class="text-right">Total Amount</th>
                            <?php if(!empty($totalsurcharge)){?><th class="text-right">Total Surcharge</th><?php }?>
                        </tr>
                        <?php
                        $alldata[] = array('TABLEHEAD','Payment Method', 'Count', 'Total Amount',(!empty($totalsurcharge)?'Total Surcharge':''));//set table headings array for excel export
                        ?>
                        </thead>
                        <tbody>
                        <?php
                        if(!empty($pmArr))
                        {
                            foreach ($pmArr as $pm) {
                                ?>
                                <tr>
                                    <td style="padding-top:1px; padding-left:10px;"><?= $pm['ptype'] ?></td>
                                    <td style="padding-top:1px; text-align:center"><?= $pm['count']; ?></td>
                                    <td style="padding-top:1px; text-align:right; padding-right:10px;"><?= asDollars($pm['amount']); ?></td>
                                    <?php if(!empty($totalsurcharge)){?><td style="padding-top:1px; text-align:right; padding-right:10px;"><?= asDollars($pm['surcharge'])?></td><?php }?>
                                </tr>
                                <?php
                                $alldata[] = array($pm['ptype'], $pm['count'], asDollars($pm['amount']),(!empty($totalsurcharge)?asDollars($pm['surcharge']):''));
                            }
                        } // end if pm
                        ?>
                        <tr>
                            <td><strong>Total: </strong></td>
                            <td style="text-align:center"><strong><?= $tapcount; ?></strong></td>
                            <td style="text-align:right; padding-right:10px;">
                                <strong><?= asDollars($tapamount); ?></strong>
                            </td>
                            <?php if(!empty($totalsurcharge)){?><td style="text-align:right; padding-right:10px;"><strong><?= asDollars($totalsurcharge); ?></strong></td><?php }?>
                        </tr>

                        <?php
                        $alldata[] = array('TOTALS', $tapcount, asDollars($tapamount), (!empty($totalsurcharge)?asDollars($totalsurcharge):''));//fill up the alldata array with the arrays of data to be shown in excel export
                        //  } // end of while for loop
                        // end if for end of file

                        $roTITLE = "Repair Order Details - " . $ro_type;
                        $alldata[] = array('', '', '', '');

                        $alldata[] = array('TABLETITLE', $roTITLE, '', '');

                        $alldata[] = array('TABLEHEAD', 'RO #', 'Date Received', 'Payment Type', 'Amount',(!empty($totalsurcharge)?'Surcharge':''));

                        $ttlamt = 0;
                        $ttlcount = 0;
                        ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="table_margin table_detail">
                <h5 class="text-center">Repair Order Details - <?= $ro_type; ?></h5>
                <table class="table table-condensed table-striped table-hover reports_table w-100">
                    <thead>
                    <tr class="table_header table_head">
                        <th> RO #</th>
                        <th>Date Received</th>
                        <th>Payment Type</th>
                        <th style="text-align:right">Amount</th>
                        <?php if(!empty($totalsurcharge)){?><th style="text-align:right">Surcharge</th><?php }?>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
        //  $stmtRO = "SELECT distinct roid FROM repairorders WHERE shopid = ? $roTypeSQL AND statusdate >= ? AND statusdate <= ? ";


        //  if ($query = $conn->prepare($stmtRO)) {
        //      $query->bind_param("sss", $shopid, $sd1, $ed1);
        //      $query->execute();
        //      $roresult = $query->get_result();
        //  } else {
        //      echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
        //  }
 
        //  $numofrows = $roresult->num_rows;
 
        //  $shopidlist = "";
 
        //  if ($roresult->num_rows > 0) {
        //      while ($ro = $roresult->fetch_array()) {
                 
                 $stmt = "SELECT roid,pdate,ptype,amt,surcharge";
                 $stmt .= " FROM accountpayments ";
                 $stmt .= "WHERE shopid = ? ";
                 $stmt .= "  AND pdate >= ? And pdate <= ? order by pdate, roid asc";
         
                 if ($query = $conn->prepare($stmt)) {
                     $query->bind_param("sss", $shopid, $sd1, $ed1);
                     $query->execute();
                     $apresult = $query->get_result();
                 } else {
                     echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                 }
         
                 ?>
         
                 <?php
                 if ($apresult->num_rows > 0) {
                     while ($ap = $apresult->fetch_array()) {
         
                         $pdate = new Datetime($ap["pdate"]);
         
                         $nptype = strtoupper($ap['ptype']);
                         if ($nptype == "MC") {
                             $nptype = "MASTERCARD";
                         } else if($nptype == "AMEX"){
                             $nptype = "AMERICAN EXPRESS";
                         } else if($nptype == "DISC"){
                             $nptype = "DISCOVER";
                         }
         
                         ?>
                         <tr>
                             <td><?= $ap["roid"]; ?>&nbsp;</td>
                             <td><?= date_format($pdate, 'm/d/Y'); ?></td>
                             <td><?= $nptype; ?></td>
                             <td style="text-align:right"><?= asDollars($ap["amt"]); ?></td>
                             <?php if(!empty($totalsurcharge)){?><td style="text-align:right"><?= asDollars($ap["surcharge"]); ?></td><?php }?>
                         </tr>
         
         
                         <?php
                         $ttlamt += $ap['amt'];
                         $ttlschg += $ap['surcharge'];
                         $ttlcount++;
         
                         $alldata[] = array($ap["roid"], date_format($pdate, 'm/d/Y'), strtoupper($ap["ptype"]), asDollars($ap["amt"]),(!empty($totalsurcharge)?asDollars($ap['surcharge']):''));
         
                     } // end while
         
                 } // end if
 
             
                    ?>
                    </tbody>
                </table>
            </div>

            <?php
            $alldata[] = array('', '', '', '');

            $alldata[] = array('TABLETITLE', 'Part Sales Details', '', '');

            $alldata[] = array('TABLEHEAD', 'PS #', 'Date Received', 'Payment Type', 'Amount');
            ?>
            <div class="table_margin table_detail">
                <h5 class="text-center">Part Sales Details</h5>
                <table class="table table-condensed table-striped table-hover reports_table w-100">
                    <thead>
                    <tr class="table_header table_head">
                        <th>PS #</th>
                        <th>Date Received</th>
                        <th>Payment Type</th>
                        <th style="text-align:right">Amount</th>
                        <?php if(!empty($totalsurcharge)){?><th style="text-align:right">Surcharge</th><?php }?>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                    $stmt = "SELECT *";
                    $stmt .= " FROM `accountpayments-ps` ";
                    $stmt .= "WHERE shopid = ? ";
                    $stmt .= "  AND pdate >= ? And pdate <= ? order by pdate, psid asc";

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("sss", $shopid, $sd1, $ed1);
                        $query->execute();
                        $apsresult = $query->get_result();
                    } else {
                        echo "Account Payments for Part Sales Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    if ($apsresult->num_rows > 0) {
                        while ($aps = $apsresult->fetch_array()) {

                            $pdate = new Datetime($aps["pdate"]);
                            $nptype = strtoupper($aps['ptype']);
                            if ($nptype == "MC") {
                                $nptype = "MASTERCARD";
                            } else if ($nptype == "AMEX") {
                                $nptype = "AMERICAN EXPRESS";
                            } else if ($nptype == "DISC") {
                                $nptype = "DISCOVER";
                            }
                            $nptype = ucwords(strtolower($nptype));
                            ?>
                            <tr>
                                <td><?php echo "PS " . $aps["psid"]; ?>&nbsp;</td>
                                <td><?php echo date_format($pdate, 'm/d/Y'); ?></td>
                                <td><?php echo $nptype; ?></td>
                                <td style="text-align:right"><?php echo asDollars($aps["amt"]); ?></td>
                                <?php if(!empty($totalsurcharge)){?><td style="text-align:right"><?= asDollars($aps["surcharge"]); ?></td><?php }?>
                            </tr>


                            <?php
                            $ttlamt += $aps['amt'];
                            $ttlschg += $aps['surcharge'];
                            $ttlcount++;

                            $alldata[] = array("PS " . $aps["psid"], date_format($pdate, 'm/d/Y'), strtoupper($aps["ptype"]), asDollars($aps["amt"]),(!empty($totalsurcharge)?asDollars($aps['surcharge']):''));

                        } // end while

                    } // end if

                    ?>
                    <tr class="">
                        <td colspan="4">&nbsp;</td>
                    </tr>
                    <tr class="table_total">
                        <td>Totals</td>
                        <td></td>
                        <td><strong><?= $ttlcount ?></strong></td>
                        <td style="text-align:right; font-weight: bold"><?php echo asDollars($ttlamt); ?></td>
                        <?php if(!empty($totalsurcharge)){?><td style="text-align:right; font-weight: bold"><?php echo asDollars($ttlschg); ?></td><?php }?>
                    </tr>
                    </tbody>
                </table>
            </div>
        </section>
    </div>
</main>
<?php
include(COMPONENTS_PRIVATE_PATH . '/reports-v2/includes/report_form.php');
include getScriptsGlobal($component);
//include getScriptsComponent($component);
include getFooterComponent($component);
?>
