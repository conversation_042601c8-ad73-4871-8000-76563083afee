<?php
$component = "reports-v2";

include getHeadGlobal($component);
include getRulesGlobal($component);

$shopid = $_COOKIE['shopid'];
//echo "SHop id is " . $shopid;
$ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
$roTypeSQL = "AND rotype != 'No Approval'";
if ($ro_type != "all") {
    $roTypeSQL = "AND rotype = '" . $ro_type . "'";
}

$title = "Shop Production Detail W/ Payments Report - " . ucwords(strtolower($ro_type));

$todaydate = date('n/j/Y');

$sd = isset($_REQUEST['sdate']) ? filter_var($_REQUEST['sdate'], FILTER_SANITIZE_STRING) : "";
$ed = isset($_REQUEST['edate']) ? filter_var($_REQUEST['edate'], FILTER_SANITIZE_STRING) : "";

$sdate = date('Y-m-d', strtotime($sd));
$edate = date('Y-m-d', strtotime($ed));


$subtitle = "This report has been modified to include only Closed Repair Orders.<div class='no-print'> To view this report with Finalized and Closed RO's,
<a target='_blank' href='productiondetailwithfinalizedroswithpayments.php?sdate=$sd&edate=$ed'>click here</a></div>";

?>
<body>
<?php
include getHeaderGlobal($component);
include getMenuGlobal($component);


$alldata = array();
// Need to change to prepare!!!!


$stmt = "SELECT replacerowithtag ";
$stmt .= "FROM company ";
$stmt .= "WHERE shopid = ? ";
//echo $stmt;

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $coresult = $query->get_result();
} else {
    echo "Company Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$co = $coresult->fetch_array();

$rrwt = strtolower($co["replacerowithtag"]);

$query = "SELECT roid,tagnumber,statusdate ,totalprts,totallbr,totalro,totalsublet,totalfees,discountamt,salestax,customer,fleetno ";
$query .= " FROM repairorders ";
$query .= " WHERE shopid = '{$shopid}'";
$query .= "  AND `status` = 'Closed'";
$query .= "  AND statusdate >= '{$sdate}'";
$query .= "  $roTypeSQL ";
$query .= "  AND statusdate <= '{$edate}'";
//echo $query;

$roresult = mysqli_query($conn, $query);

if (!$roresult) {
    die("Database Repair Orders query failed.");
}
$alldata[] = array('TABLETITLE', 'Repair Orders');
$alldata[] = array('TABLEHEAD', 'Ro #', 'Final Date', 'Customer', 'Labor', 'Parts', 'Sublet', 'Fees', 'Tax', 'Discount', 'Pmt Date', 'Pmts', 'Source', 'Total RO', 'Parts Cost');
?>
    <main id="reports">
        <div class="report">
            <div class="col-12">
                <div class="title col breadcrumb d-flex align-items-center">
                    <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                        class="text-secondary ps-3 pe-3">/</span>
                    <h2><?= $title; ?></h2>
                </div>
                <hr/>
                <p class="card-title">
                    <?php
                    if ((!empty($sd))) {
                        echo($sd);
                    }
                    if (!empty($sd) && !empty($ed)) {
                        echo ' to ';
                    }
                    if (!empty($ed)) {
                        echo $ed;
                    }
                    if (empty($sd) && empty($ed)) {
                        echo date('m/d/Y');
                    }
                    ?>
                </p>
                <p class="d-print-none">
                    <?= $subtitle ?><br>
                    Click an RO number to view the RO.
                </p>
                <p class="d-print-none"><span class=""><strong style="color: red">Red Date</strong></span> indicates
                    payment
                    not received on RO closing date.
                </p>
            </div>
        </div>
        <div class="report">
            <?php
            include_once "inline_prompt.php";
            ?>
            <table class="spd_report w-100" style="width: 100%" id="spd_report">
                <thead>
                <tr class="table_header">
                    <th></th>
                    <th style="width: 6%">RO#</th>
                    <th style="width: 7%;">Final Date</th>
                    <th style="width: 12%;">Customer</th>
                    <th class="text-right calc" style="width: 7%;">Labor</th>
                    <th class="text-right calc" style="width: 7%;">Parts</th>
                    <th class="text-right calc" style="width: 7%;">Sublet</th>
                    <th class="text-right calc" style="width: 7%;">Fees</th>
                    <th class="text-right calc" style="width: 7%;">Tax</th>
                    <th class="text-right calc" style="width: 7%;">Discount</th>
                    <th class="text-center" style="width:7%;">Pmt Date</th>
                    <th class="text-right calc" style="width: 7%;">PMTS</th>
                    <th class="text-right calc" style="width: 6%;">Source</th>
                    <th class="text-right calc" style="width: 7%;">Total RO</th>
                    <th class="text-right calc" style="width: 7%;">Parts Cost</th>
                </tr>
                </thead>
                <tbody>
                <?php
                $runningpmts = 0;
                $labor = 0;
                $parts = 0;
                $sublet = 0;
                $slstax = 0;
                $pcost = 0;
                $ttldisc = 0;
                $totalfees = 0;
                $rrwt = "";
                $tro = 0;
                $dfirstdate = "";

                if ($roresult->num_rows > 0) {
                    while ($ro = $roresult->fetch_array()) {
                        $roid = $ro["roid"];

                        $statusdate = new DateTime($ro["statusdate"]);
                        $statusdate = date_format($statusdate, 'm/d/Y');

                        $today = new DateTime('now');
                        $today = date_format($today, 'm/d/Y');

                        if (strlen($ro["fleetno"]) > 0) {
                            $fleetno = " (#" & $ro["fleetno"] . ")";
                        } else {
                            $fleetno = "";
                        }

                        $stmt = "SELECT coalesce(sum(cost*quantity),0) as pcost ";
                        $stmt .= "FROM parts ";
                        $stmt .= "WHERE shopid = ? ";
                        $stmt .= "  AND roid = ? ";
                        $stmt .= "  AND deleted = 'no'";
                        //echo $stmt;

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("si", $shopid, $roid);
                            $query->execute();
                            $partresult = $query->get_result();
                        } else {
                            echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }

                        if ($partresult->num_rows > 0) {

                            $part = $partresult->fetch_array();

                            if (is_null($part["pcost"])) {
                                $mypcost = 0;
                            } else {
                                $mypcost = $part["pcost"];
                            } // end of is null

                        } // end of parts end if

                        $labor = $labor + $ro["totallbr"];
                        $parts = $parts + $ro["totalprts"];
                        $sublet = $sublet + $ro["totalsublet"];
                        $slstax = $slstax + $ro["salestax"];

                        $pcost = $pcost + $mypcost;

                        $discount = $ro["discountamt"];

                        $parts_discount = 0;
                        $stmt = "SELECT coalesce(sum(linettlprice),0) partsdiscount ";
                        $stmt .= " FROM parts  ";
                        $stmt .= "WHERE shopid = ? ";
                        $stmt .= "  AND partnumber = 'DISCOUNT' ";
                        $stmt .= "  AND roid = ? ";
                        //echo $stmt;

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("si", $shopid, $roid);
                            $query->execute();
                            $query->bind_result($parts_discount);
                            $query->fetch();
                            $query->close();
                        } else {
                            echo "Discount Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }

                        $labor_discount = 0;

                        $stmt = "SELECT coalesce(sum(LineTotal),0) labordiscount ";
                        $stmt .= " FROM labor  ";
                        $stmt .= "WHERE shopid = ? ";
                        $stmt .= "  AND labor = 'DISCOUNT' ";
                        $stmt .= "  AND roid = ? ";
                        //echo $stmt;

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("si", $shopid, $roid);
                            $query->execute();
                            $query->bind_result($labor_discount);
                            $query->fetch();
                            $query->close();
                        } else {
                            echo "Lbr Discount Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }
                        $discount += $parts_discount + $labor_discount;

                        $ttldisc = $ttldisc + $discount;
                        $totalfees = $totalfees + $ro["totalfees"];

                        if ($rrwt == "yes") {
                            $displayroid = $ro["tagnumber"];
                        } else {
                            $displayroid = $ro["roid"];
                        }

                        //	echo "DB Parts Cost for ROID is : " . $displayroid . ":" . $part["pcost"] . "<br />";
                        //	echo "My Parts Cost for ROID is : " . $displayroid . ":" . $mypcost . "<br />";


                        $stmt = "SELECT amt,ptype,id,pdate ";
                        $stmt .= "FROM accountpayments ";
                        $stmt .= "WHERE shopid = ? ";
                        $stmt .= "  AND roid = ? ";
                        //echo $stmt;

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("si", $shopid, $roid);
                            $query->execute();
                            $apresult = $query->get_result();
                        } else {
                            echo "Account Payments Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }

                        if ($apresult->num_rows > 0) {
                            $ap = $apresult->fetch_array();

                            $firstamt = $ap["amt"];
                            $firsttype = $ap["ptype"];

                            $firstdate = $ap["pdate"];

                            // display first date
                            if (!empty($ap["pdate"])) {
                                $dfirstdate = new DateTime($ap["pdate"]);
                                $dfirstdate = date_format($dfirstdate, 'm/d/Y');
                            } else {
                                $dfirstdate = "";
                            }

                            $runningpmts = $runningpmts + $ap["amt"];
                        } else {
                            $firstamt = 0;
                            $firsttype = "";
                            $firstdate = 0;
                            $dfirstdate = "";
                        }

                        $totalro = $ro["totallbr"] + $ro["totalprts"] + $ro["totalsublet"] + $ro["totalfees"] + $ro["salestax"] - $ro["discountamt"];
                        $tro = $tro + $totalro;
                        ?>
                        <tr>
                            <td>Repair Orders</td>
                            <td style="width: 6%"><?php echo $displayroid; ?></td>
                            <td style="width: 7%;">
                                <?php
                                echo $statusdate; ?>
                            </td>
                            <td style="width: 12%;"><?php echo $ro["customer"] . $fleetno; ?></td>
                            <td class="text-right" style="width: 7%;"><?php echo asDollars($ro["totallbr"]); ?></td>
                            <td class="text-right" style="width: 7%;"><?php echo asDollars($ro["totalprts"]); ?></td>
                            <td class="text-right" style="width: 7%;"><?php echo asDollars($ro["totalsublet"]); ?></td>
                            <td class="text-right" style="width: 7%;"><?php echo asDollars($ro["totalfees"]); ?></td>
                            <td class="text-right" style="width: 7%;"><?php echo asDollars($ro["salestax"]); ?></td>
                            <td class="text-right" style="width: 7%;"><?php echo asDollars($discount); ?></td>
                            <td class="text-center"
                                style="width: 6%;<?= ($firstdate != $ro["statusdate"]) ? "font-weight:bold;color:red;" : "" ?>">
                                <?php
                                echo $dfirstdate;
                                ?>
                            </td>
                            <td class="text-right" style="width: 7%;"><?php echo asDollars($firstamt); ?></td>
                            <td class="text-right" style="width: 6%;"><?php echo $firsttype; ?></td>
                            <td class="text-right" style="width: 7%;"><?php echo asDollars($totalro); ?></td>
                            <td class="text-right" style="width: 7%;"><?php echo asDollars($mypcost); ?></td>
                        </tr>
                        <?php

                        $alldata[] = array($displayroid, $statusdate, strtoupper($ro["customer"] . $fleetno), asDollars($ro["totallbr"]), asDollars($ro["totalprts"]), asDollars($ro["totalsublet"]), asDollars($ro["totalfees"]), asDollars($ro["salestax"]), asDollars($discount), $dfirstdate, asDollars($firstamt), strtoupper($firsttype), asDollars($totalro), asDollars($mypcost));


                        if ($apresult->num_rows > 0) {
                            while ($ap = $apresult->fetch_array()) {
                                $runningpmts = $runningpmts + $ap["amt"];
                                ?>
                                <tr class="">
                                    <td>Repair Orders</td>
                                    <td style="width: 6%;"></td>
                                    <td style="width: 7%;"></td>
                                    <td style="width: 12%;"></td>
                                    <td style="width: 7%;"></td>
                                    <td style="width: 7%;"></td>
                                    <td style="width: 7%;"></td>
                                    <td style="width: 7%;"></td>
                                    <td style="width: 7%;"></td>
                                    <td style="width: 7%;"></td>
                                    <td style="width: 7%;"></td>
                                    <td class="text-right" style="width: 7%;"><?php echo asDollars($ap["amt"]); ?></td>
                                    <td class="text-right"
                                        style="width: 6%;"><?php echo $ap["ptype"]; ?></td>
                                    <td style="width: 7%;"></td>
                                    <td style="width: 7%;"></td>
                                </tr>
                                <?php
                                $alldata[] = array('', '', '', '', '', '', '', '', '', '', asDollars($ap["amt"]), strtoupper($ap["ptype"]), '', '');

                            } // end of ap while

                        } // end of ap result end if

                    } // end of while

                }
                ?>
                <?php
                // now get the part sales

                //stmt = "select * from ps where statusdate >= '" & sdate & "' and statusdate <= '" & edate
                $query = "SELECT * ";
                $query .= " FROM ps ";
                $query .= " WHERE shopid = '{$shopid}'";
                $query .= "  AND statusdate >= '{$sdate}'";
                $query .= "  AND statusdate <= '{$edate}'";
                $query .= "  AND `status` = 'Closed'";

                //echo $query;

                $psresult = mysqli_query($conn, $query);

                if (!$psresult) {
                    die("Database Part Sales query failed.");
                }
                if ($psresult->num_rows > 0) {
                    ?>
                    <!--
                    <tr class="table_header">
                        <td>Part Sales</td>
                        <td style="width: 6%;">PS #</td>
                        <td style="width: 6%;">Closed Date</td>
                        <td style="width: 12%;">Customer</td>
                        <td style="width: 7%;"></td>
                        <td class="text-right" style="width: 7%;">Parts</td>
                        <td style="width: 7%;"></td>
                        <td class="text-right" style="width: 7%;">Fees</td>
                        <td class="text-right" style="width: 7%;">Tax</td>
                        <td style="width: 7%;"></td>
                        <td style="width: 6%;"></td>
                        <td class="text-right" style="width: 7%;">PMTS</td>
                        <td class="text-right" style="width: 6%;">Source</td>
                        <td class="text-right" style="width: 7%;">Total Sale</td>
                        <td class="text-right" style="width: 7%;">Parts Cost</td>
                    </tr>
                    -->
                    <?php
                    $alldata[] = array('', '', '', '', '', '', '', '', '', '', '', '', '', '');
                    $alldata[] = array('TABLETITLE', 'Part Sales');
                    $alldata[] = array('TABLEHEAD', 'PS #', 'Closed Date', 'Customer', '', 'Parts', '', 'Fees', 'Tax', '', '', 'PMTS', 'Source', 'Total Sale', 'Parts Cost');


                    while ($ps = $psresult->fetch_array()) {
                        $psid = $ps["psid"];
                        $cid = $ps["cid"];
                        $statusdate = new DateTime($ps["statusdate"]);
                        $statusdate = date_format($statusdate, 'm/d/Y');


                        $stmt = "SELECT concat(lastname,',',firstname) as lf ";
                        $stmt .= "FROM customer ";
                        $stmt .= "WHERE shopid = ? ";
                        $stmt .= "  AND customerid = ? ";
                        //echo $stmt;

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("si", $shopid, $cid);
                            $query->execute();
                            $custresult = $query->get_result();
                        } else {
                            echo "Customer Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }

                        //if ($custresult->num_rows > 0) {
                        $cust = $custresult->fetch_array();

                        $lf = $cust["lf"];

                        $parts = $parts + $ps["subtotal"] - $ps["fees"];
                        $slstax = $slstax + $ps["tax"];
                        $tro = $tro + $ps["total"];

                        // get total part cost
                        $stmt = "SELECT sum(qty*cost) as extcost ";
                        $stmt .= " FROM psdetail ";
                        $stmt .= " WHERE shopid = ? ";
                        $stmt .= "  AND psid = ? ";
                        //echo $stmt;

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("si", $shopid, $psid);
                            $query->execute();
                            $psdresult = $query->get_result();
                        } else {
                            echo "Parts Sales Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }


                        if ($psdresult->num_rows > 0) {

                            $psd = $psdresult->fetch_array();

                            if (strlen($psd["extcost"]) > 0) {
                                $mypcost = $psd["extcost"];
                            } else {
                                $mypcost = 0;
                            }

                        } else {
                            $mypcost = 0;
                        }

                        $pcost = $pcost + $mypcost;

                        $stmt = "SELECT sum(amt) as a, ptype  ";
                        $stmt .= " FROM `accountpayments-ps` ";
                        $stmt .= "WHERE shopid = ? ";
                        $stmt .= "  AND psid = ? ";
                        //echo $stmt;

                        if ($query = $conn->prepare($stmt)) {
                            $query->bind_param("si", $shopid, $psid);
                            $query->execute();
                            $apsresult = $query->get_result();
                        } else {
                            echo "Account Payments PS Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }

                        $aps = $apsresult->fetch_array();

                        if (is_null($aps["a"])) {
                            $currentamt = 0;
                        } else {
                            $currentamt = $aps["a"];
                            $runningpmts = $runningpmts + $aps["a"];
                        } // end of if

                        //} // end if for cust

                        ?>
                        <tr>
                            <td>Part Sales</td>
                            <td style="width: 6%;"><?php echo $ps["psid"]; ?></td>
                            <td style="width: 7%;"><?php echo $statusdate; ?></td>
                            <td style="width: 12%;"><?php echo $lf; ?></td>
                            <td style="width: 7%;"></td>
                            <td class="text-right"
                                style="width: 7%;"><?php echo asDollars($ps["subtotal"] - $ps["fees"]); ?></td>
                            <td style="width: 7%;"></td>
                            <td class="text-right" style="width: 7%;"><?php echo asDollars($ps["fees"]); ?></td>
                            <td class="text-right" style="width: 7%;"><?php echo asDollars($ps["tax"]); ?></td>
                            <td style="width: 7%;"></td>
                            <td style="width: 7%;"></td>
                            <td class="text-right" style="width: 6%;"><?php echo asDollars($currentamt); ?></td>
                            <td class="text-right" style="width: 6%;"><?= $aps["ptype"]; ?></td>
                            <td class="text-right" style="width: 7%;"><?php echo asDollars($ps["total"]); ?></td>
                            <td class="text-right" style="width: 7%;"><?php echo asDollars($mypcost); ?></td>
                        </tr>

                        <?php
                        $alldata[] = array($ps["psid"], $statusdate, $lf, '', asDollars($ps["subtotal"] - $ps["fees"]), '', asDollars($ps["fees"]), asDollars($ps["tax"]), '', '', asDollars($currentamt), strtoupper($aps["ptype"]), asDollars($ps["total"]), asDollars($mypcost));
                    } // end of ps while
                    ?>
                    <?php
                }

                $alldata[] = array('', '', '', '', '', '', '', '', '', '', '', '', '', '');
                $alldata[] = array('TOTALS', '', '', asDollars($labor), asDollars($parts), asDollars($sublet), asDollars($totalfees), asDollars($slstax), asDollars($ttldisc), '', asDollars($runningpmts), '', asDollars($tro), asDollars($pcost));
                ?>

                <?php if ( $roresult->num_rows > 0 || $psresult->num_rows > 0): ?>
                    <tr class="table_total">
                        <td>Grand Totals</td>
                        <td style="width: 6%;">Grand Totals</td>
                        <td style="width: 7%;"> &nbsp;</td>
                        <td style="width: 12%;"> &nbsp;</td>
                        <td class="text-right" style="width: 7%;"><?php echo asDollars($labor); ?></td>
                        <td class="text-right" style="width: 7%;"><?php echo asDollars($parts); ?></td>
                        <td class="text-right" style="width: 7%;"><?php echo asDollars($sublet); ?></td>
                        <td class="text-right" style="width: 7%;"><?php echo asDollars($totalfees); ?></td>
                        <td class="text-right" style="width: 7%;"><?php echo asDollars($slstax); ?></td>
                        <td class="text-right" style="width: 7%;"><?php echo asDollars($ttldisc); ?></td>
                        <td style="width: 7%;"></td>
                        <td class="text-right" style="width: 7%;"><?php echo asDollars($runningpmts); ?></td>
                        <td style="width: 6%;"></td>
                        <td class="text-right" style="width: 7%; text-align: right"><?php echo asDollars($tro); ?></td>
                        <td class="text-right" style="width: 7%; text-align: right"><?php echo asDollars($pcost); ?></td>
                    </tr>
                <?php endif; ?>
                </tbody>
            </table>
        </div>
    </main>
<?php
include(COMPONENTS_PRIVATE_PATH . '/reports-v2/includes/report_form.php');

$subtitle = "This report has been modified to include only Closed Repair Orders.";
include getScriptsGlobal($component);


$dateRangeDisplay = ($_REQUEST['sdate'] ?? "") . " " . (!empty($_REQUEST['edate']) ? " to " . $_REQUEST['edate'] : "");
$subtitle = !empty($subtitle) ? $subtitle : "";

?>
    <script>
        $(document).ready(function () {

            //let CalcT = Array(4, 5, 6, 7, 8, 9, 11, 13, 14);

            var CalcT = Array();

            $("#spd_report thead").find("th").each(function (idx, v) {
                if ($(v).hasClass("calc")) {
                    CalcT.push(idx);
                }
            });

            let floatVal = function (i) {
                return typeof i === 'string' ? Number(i.replace(/[$,]/g, '')) : typeof i === 'number' ? i : 0;
            };

            groupCol_writer = 0;
            let table = $("#spd_report").DataTable({
                responsive: true,
                fixedHeader: {
                    headerOffset: 68
                },
                colReorder: true,
                select: true,
                scrollY: false,
                scrollX: false,
                scroller: false,
                paging: false,
                language: {
                    searchPanes: {
                        emptyPanes: 'No records found in this date range'
                    },
                    search: "_INPUT_",
                    searchPlaceholder: "Search...",
                    emptyTable: "No Repair Orders found",
                },
                buttons: [
                    {
                        extend: 'csv',
                        text: '<i class="fas fa-file-csv fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                        messageTop : "<?= $dateRangeDisplay ?>",
                    },
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint." (".$dateRangeDisplay.")" ?>",
                        messageTop : "This report has been modified to include only Closed Repair Orders",
                        exportOptions: {
                            // Any other settings used
                            columns: [':visible'],
                            grouped_array_index: 0
                        },
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                        exportOptions: {
                            // Any other settings used
                            stripHtml: false,
                            columns: [':visible'],
                            grouped_array_index: 0
                        },
                        messageTop : "<?= $dateRangeDisplay."<br/>This report has been modified to include only Closed Repair Orders" ?>",
                        footer: true,
                    }
                ],
                dom: "<'row'<'col-sm-12 col-md-6 dt_Search text-secondary'f><'col-sm-12 col-md-6 text-right dt_btns text-secondary'B>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
                columnDefs: [{visible: false, targets: groupCol_writer}],
                order: [[groupCol_writer, 'asc']],
                orderFixed: [0, 'desc'],
                rowGroup: {
                    endRender: function (rows, group) {
                        if (group == "Grand Totals"){
                            return;
                        }
                        let totsArr = Array();

                        CalcT.forEach(function (val, idx) {
                            var col = rows.data().pluck(val);
                            totsArr[val] = col.reduce(function (a, b) {
                                var fl_a = floatVal(a);
                                var fl_b = floatVal(b);
                                if (isNaN(fl_a)) {
                                    fl_a = 0;
                                }
                                if (isNaN(fl_b)) {
                                    fl_b = 0;
                                }
                                sum = fl_a + fl_b;
                                return (typeof sum != "undefined" || !isNaN(sum)) ? sum : 0;
                            }, 0);
                        });
                        var tr = rows.data();
                        var numcol = tr[0].length;

                        let formatting_options = {
                            style: 'currency',
                            currency: 'USD',
                            minimumFractionDigits: 2,
                        };
                        let USDollar = new Intl.NumberFormat('en-US', formatting_options);
                        var gname = group.replace(" ", "_").replace(",", "");
                        // console.log('numcol', numcol);
                        if (typeof numcol !== "undefined" && numcol > 0) {
                            //   console.log();
                            var total_row = $('<tr id="' + gname + '_totals">');
                            total_row.append('<td colspan="2" class="text-left">' + group + ' Total</td>')
                            for (var i = 3; i < numcol; i++) {

                                if (typeof totsArr[i] !== "undefined" || totsArr[i] != null) {
                                    var totVal = totsArr[i].toFixed(0);
                                    // console.log(totsArr[i], !isNaN(totsArr[i]));
                                    //     if (!isNaN(totsArr[i]) && rows.data().pluck(i)[0].indexOf('$') >= 0) {
                                    totVal = USDollar.format(totsArr[i])
                                    //     }
                                    total_row.append('<td class="text-right">' + totVal + '</td>')
                                    // console.log("Adding total")
                                } else {
                                    total_row.append('<td class=""></td>')
                                }
                            }
                            total_row.append("</tr>");
                            return total_row;
                        }

                    },
                    dataSrc: 0,
                    endClassName: 'table_total text-right',
                    startClassName: 'group_heading'
                },

            });

        });

        function showRO(roid) {
            t = "View RO"
            p = "<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?roid=" + roid
            eModal.iframe({
                title: t,
                url: p,
                size: eModal.size.xl,
                buttons: [
                    //   {text: 'Close', style: 'secondary', close: true}
                ]
            });

        }
    </script>
<?php
include getFooterComponent($component);
?>