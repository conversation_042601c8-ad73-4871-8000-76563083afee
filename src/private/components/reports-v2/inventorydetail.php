<?php
$component = "reports-v2";
$title = "Inventory Detail Report";
$subtitle = "";
include getHeadGlobal($component);
include getRulesGlobal($component);
$shopid = $_COOKIE['shopid'];

$template = COMPONENTS_PRIVATE . "/reports/templates/excelexport.php";

?>
<body>
<?php

include getHeaderGlobal($component);
include getMenuGlobal($component);


$tablefields = array('CNT', 'Part Number', 'Part Description', 'Price', 'Cost', 'Quantity', 'TTL Cost');
$alldata = array();
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="title col breadcrumb d-flex align-items-center">
                <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                    class="text-secondary ps-3 pe-3">/</span>
                <h2><?= $title; ?></h2>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?>
            </p>
        </div>
    </div>
    <div class="report">
        <table class="dtable" style="width: 100%">
            <thead>
            <tr class="">
                <th class="">CNT</th>
                <th class="">Part Number</th>
                <th class="">Part Description</th>
                <th class="calc_total" style="text-align: right;">Price</th>
                <th class="" style="text-align: right;">Cost</th>
                <th class="calc_qty" style="text-align: right;">Qty</th>
                <th class="calc_total" style="text-align: right;">TTL Cost</th>
            </tr>
            </thead>
            <tbody>
            <?php
            $runningcost = 0;
            $runningprice = 0;
            $runningqty = 0;

            $stmt = "select * from partsinventory where shopid = ? order by partnumber";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("s", $shopid);
                $query->execute();
                $results = $query->get_result();
                $query->close();
            }
            $c = 0;
            if ($results->num_rows > 0) {
            while ($rs = $results->fetch_assoc()) {
                $c = $c + 1;
                $runningcost = $runningcost + ($rs["PartCost"] * $rs["NetOnHand"]);
                //$runningprice = $runningprice + ($rs["PartPrice"] * $rs["NetOnHand"]);
                $runningprice += $rs["PartPrice"];
                $runningqty = $runningqty + $rs["NetOnHand"];
                ?>
                <tr>
                    <td><?= $c ?></td>
                    <td><?= $rs["PartNumber"] ?></td>
                    <td><?= $rs["PartDesc"] ?></td>
                    <td style="text-align: right;"><?= asDollars($rs["PartPrice"]) ?></td>
                    <td style="text-align: right;"><?= asDollars($rs["PartCost"]) ?></td>
                    <td style="text-align: right;"><?= $rs["NetOnHand"] ?></td>
                    <td style="text-align: right;"><?= asDollars($rs["PartCost"] * $rs["NetOnHand"]) ?></td>
                </tr>
                <?php
                $alldata[] = array($c, strtoupper($rs['PartNumber']), strtoupper($rs['PartDesc']), asDollars($rs["PartPrice"]), asDollars($rs["PartCost"]), $rs["NetOnHand"], asDollars($rs["PartCost"] * $rs["NetOnHand"]));
            }
            ?>
            </tbody>
            <tfoot>
            <tr class="table_total">
                <td style="text-align: right;"><strong>TOTALS:</strong></td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td style="text-align: right;"><strong><?= asDollars($runningprice) ?></td>
                <td style="text-align: right;">&nbsp;</td>
                <td style="text-align: right;"><strong><?= number_format($runningqty, 2) ?><strong></td>
                <td style="text-align: right;"><strong><?= asDollars($runningcost) ?><strong></td>
            </tr>
            </tfoot>
            <?php
            $alldata[] = array('', '', '', '', '', '', '');
            $alldata[] = array('TOTALS', '', '', asDollars($runningprice), '', number_format($runningqty, 2), asDollars($runningcost));
            }
            ?>
        </table>
    </div>
    <?php
    include_once COMPONENTS_PRIVATE_PATH . "/reports/includes/report_form.php";

    include getScriptsComponent($component);
    ?>
    <?php
    include getFooterComponent($component);

    ?>