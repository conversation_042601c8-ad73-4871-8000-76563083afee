<?php

$component = "reports-v2";

include getHeadGlobal($component);
include getRulesGlobal($component);
include getHeaderGlobal($component);
include getMenuGlobal($component);


// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = isset($_GET['sd']) ? $_GET['sd'] : '';
$ed = isset($_GET['ed']) ? $_GET['ed'] : '';
$sdate = isset($_GET['sdate']) ? $_GET['sdate'] : $sd;
$edate = isset($_GET['edate']) ? $_GET['edate'] : $ed;
$sd = $sdate;
$ed = $edate;
$sd1 = date('Y-m-d', strtotime($sd));
$ed1 = date('Y-m-d', strtotime($ed));

// Page Variables
$title = 'Follow Up Report';  // Report Title Goes Here
//$subtitle = "$sdate";  // Report SubTitle Goes Here - Hide if not needed
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports/templates/excelexport_autosize.php'; //Only change if a custom PHPExcel is created in the template folder

// Use this for Custom Reports
//$template = '/sbpi2/reports/templates/excelexport.php';
?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="title col breadcrumb d-flex align-items-center">
                <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                    class="text-secondary ps-3 pe-3">/</span>
                <h2><?= $title; ?></h2>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report">
        <?php
        include_once "inline_prompt.php";
        ?>
        <table class="dtable" style="width: 100%">
            <thead>
            <tr>
                <th>Customer</th>
                <th>Vehicle</th>
                <th>Vehicle Issues</th>
                <th>Phone #'s</th>
                <th style="text-align:right">Date RO Closed</th>
            </tr>
            </thead>
            <tbody>
            <?php
            $tablefields = array('Customer', 'Vehicle', 'Vehicle Issues', 'Home Phone', 'Work Phone', 'Cell Phone', 'Date RO Closed');//set table headings array for excel export
            $alldata = array();//this will hold all the data arrays to be exported to excel


            // Insert DB Query Here

            // Template Query Begins - Replace entire section
            $stmt = "select roid, `customer`, vehinfo, customerphone, customerwork, cellphone, statusdate from repairorders where shopid = ? and status = 'closed' and rotype != 'no approval' and statusdate >= ? and statusdate <= ?";

            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sss", $shopid, $sd1, $ed1);
                $query->execute();
                $roresult = $query->get_result();
            } else {
                echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            while ($ro = $roresult->fetch_array()) {
                $statdate = date_format(new DateTime($ro["statusdate"]), 'm/d/Y');
                $roid = $ro["roid"];
                $complaint_arr = array();
                ?>
                <tr>
                    <td><?= $ro["customer"]; ?></td>
                    <td><?= $ro["vehinfo"]; ?></td>
                    <td><?php
                        $cstmt = "select complaint from complaints where cstatus = 'no' and shopid = ? and roid = $roid";
                        if ($query = $conn->prepare($cstmt)) {
                            $query->bind_param("s", $shopid);
                            $query->execute();
                            $rocresult = $query->get_result();
                        } else {
                            echo "RO Prepare failed: (" . $conn->errno . ") " . $conn->error;
                        }
                        while ($cro = $rocresult->fetch_array())
                            $complaint_arr[] = $cro["complaint"];

                        echo implode(' ,', $complaint_arr);
                        ?>
                    </td>
                    <td><?php
                        if (strlen($ro["customerphone"]) > 5) {
                            echo " <b>H:</b>" . formatPhone($ro["customerphone"]);
                        }
                        if (strlen($ro["customerwork"]) > 5) {
                            echo " <b>W:</b>" . formatPhone($ro["customerwork"]);
                        }
                        if (strlen($ro["cellphone"]) > 5) {
                            echo " <b>C:</b>" . formatPhone($ro["cellphone"]);
                        }
                        ?>
                    </td>
                    <td style="text-align:right"><?= $statdate; ?></td>
                </tr>
                <?php
                $alldata[] = array(($ro["customer"]), ($ro["vehinfo"]), (implode(' ,', $complaint_arr)), formatPhone($ro["customerphone"]), formatPhone($ro["customerwork"]), formatPhone($ro["cellphone"]), $statdate);//fill up the alldata array with the arrays of data to be shown in excel export
            } // end of while for loop
            // end if for end of file


            $alldata[] = array('', '', '', '', '', '', '');
            ?>
            </tbody>
        </table>
    </div>
</main>
<?php

require COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form.php";

include getScriptsGlobal( $component);
include getFooterComponent($component);
?>
