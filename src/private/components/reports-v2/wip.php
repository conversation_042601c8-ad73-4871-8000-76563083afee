<?php

$component = "reports-v2";
// Use this for Gen Pop Reports
$title = 'Work In Progress';  // Report Title Goes Here
//require COMPONENTS_PRIVATE_PATH."/reports-v2/includes/header_reports.php";
include getHeadGlobal($component);
include getRulesGlobal($component);
//require("../php/functions.php");

// Global Variables
$shopid = $_COOKIE['shopid'];
$date = new DateTime('now');
$sd = "";
$ed = "";

$subtitle = "";
//$subtitle = '';  // Report SubTitle Goes Here - Hide if not needed
$todaydate = "todaydate";
// Use this for Gen Pop Reports
$template = COMPONENTS_PRIVATE . '/reports-v2/templates/excelexport.php'; //Only change if a custom PHPExcel is created in the template folder
?>
<body>
<?php

include getHeaderGlobal($component);
include getMenuGlobal($component);
// Use this for Gen Pop Reports
//require COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_buttons.php";
?>

<!-- Column Headers Insert Report Variables here -->
<main id="reports">
    <div class="report">
        <div class="col-12">
                <div class="row">
                    <div class="col-sm-12 col-md-10">
            <div class="title col breadcrumb d-flex align-items-center">
                <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary">Reports</a> <span
                    class="text-secondary ps-3 pe-3">/</span>
                <h2><?= $title; ?></h2>
            </div>
                    </div>

                    <div class="col-md-2">
                        <?php
                        if ($_COOKIE['empid'] == 'Admin') {
                            ?>
                            <button type="button" class="btn btn-secondary btn-md d-print-none float-end"
                                    onclick="location.href='<?= COMPONENTS_PRIVATE ?>/v2/report_builder/builder.php?preset=wip&op=create_new'">Edit This Report
                            </button>
                            <?php
                        }
                        ?>
                    </div>
                </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?>
            </p>
        </div>
    </div>
    <div class="report">
        <table class="dtable" style="width: 100%">
            <thead>
            <tr class="">
                <th>Date In</th>
                <th>Status</th>
                <th>RO #</th>
                <th>Customer</th>
                <th>Vehicle</th>
                <th style="text-align:right;">Sold Hours</th>
                <th style="text-align:right;">Labor Clock</th>
                <th style="text-align:right;">%Complete</th>
                <th style="text-align:right;" class="calc_total">Total RO</th>
                <th style="text-align:right;" class="calc_total">Balance Owed</th>
            </tr>
            </thead>

            <?php
            $tablefields = array('Date In', 'Status', 'RO #', 'Customer', 'Vehicle', 'Sold Hours', 'Labor Clock', '%Complete', 'Total RO', 'Balance Owed');//set table headings array for excel export
            $alldata = array();//this will hold all the data arrays to be exported to excel


            // Insert DB Query Here

            // Template Query Begins - Replace entire section
            $runttl = $runbal = 0;
            $query = "select Writer, MajorComplaint, ROID, ucase(r.Status) as stat, DateIn, Customer, VehInfo, TotalRO, totalfees, VehLicNum, CustomerID, ROType, balance from repairorders r,rostatus s where r.shopid=s.shopid and r.status=s.status and r.shopid ='$shopid' and r.Status <> 'Closed' and r.ROType <> 'No Approval' ORDER BY s.displayorder, r.ROID DESC";
            $roresult = mysqli_query($conn, $query);
            if (!$roresult) {
                die("No Data To Display!");
            }
            if ($roresult->num_rows > 0) {
                while ($rs = $roresult->fetch_array()) {

                    $stmt = "select sum(laborhours) as lh from labor where deleted = 'no' and shopid = '$shopid' and roid = '" . $rs['ROID'] . "'";
                    $query = mysqli_query($conn, $stmt);
                    $trs = $query->fetch_array();
                    if (!empty($trs['lh'])) {
                        $soldhours = round($trs["lh"], 2);
                    } else {
                        $soldhours = 0;
                    }
                    $stmt = "select sum(round((timestampdiff(second,startdatetime,enddatetime)/60)/60,2)) as thours, labortimeclock.* from labortimeclock where shopid = '$shopid' and roid = '" . $rs['ROID'] . "'";
                    $query = mysqli_query($conn, $stmt);
                    $trs = $query->fetch_array();
                    if (!empty($trs['thours'])) {
                        $laborclock = $trs["thours"];
                    } else {
                        $laborclock = 0;
                    }
                    if ($laborclock > 0 && $soldhours > 0) {
                        $percentcomplete = round(($laborclock / $soldhours) * 100, 2);
                    } else {
                        $percentcomplete = 0;
                    }
                    if (is_numeric(substr($rs['stat'], 0, 1))) {
                        $thestat = substr($rs['stat'], 1);
                    } else {
                        $thestat = $rs["stat"];
                    }
                    $runttl += $rs["TotalRO"];
                    $runbal += $rs["balance"];
                    // $stmt = "select complaintid, roid, complaint FROM complaints WHERE roid = '".$rs['ROID']."' AND shopid = '$shopid' AND acceptdecline != 'Declined'";
                    // $query = mysqli_query($conn, $stmt);
                    // $custcomplain = $query->fetch_array();
                    // if (!$custcomplain) {
                    // 	die("No Customer Complaint");
                    // }

                    // Template Query Ends

                    ?>

                    <tr class="table_data">
                        <td><?= date('n/j/Y', strtotime($rs["DateIn"])) ?></td>
                        <td><?= $thestat ?></td>
                        <td><?= $rs["ROID"] ?></td>
                        <td><?= $rs["Customer"] ?></td>
                        <td><?= $rs["VehInfo"] ?></td>
                        <td style="text-align:right;"><?= $soldhours ?></td>
                        <td style="text-align:right;"><?= $laborclock ?></td>
                        <td style="text-align:right;"><?= number_format($percentcomplete, 2) . '%' ?></td>
                        <td style="text-align:right;">$<?= number_format($rs['TotalRO'], 2) ?></td>
                        <td style="text-align:right;">$<?= number_format($rs['balance'], 2) ?></td>
                    </tr>

                    <?php
                    $alldata[] = array(date('n/j/Y', strtotime($rs["DateIn"])), $thestat, $rs['ROID'], $rs["Customer"], $rs["VehInfo"], $soldhours, $laborclock, number_format($percentcomplete, 2) . '%', '$' . number_format($rs['TotalRO'], 2), '$' . number_format($rs['balance'], 2));
                    //$alldata[]=array("Customer Concern",$rs["MajorComplaint"],'','','','','','',''); //fill up the alldata array with the arrays of data to be shown in excel export
                } // end of while for loop
            } // end if for end of file

            $statement="SELECT ps.shopid,psid,`status`,psdate,concat(customer.firstname,' ',customer.LastName) AS customer,`total`,cid,statusdate
                        FROM ps
                        LEFT JOIN customer ON ps.shopid = customer.shopid AND ps.cid = customer.CustomerID
                        WHERE ps.`status` != 'CLOSED' AND ps.`status` != 'DEAD' AND ps.shopid = ?";

            if ($query = $conn->prepare($statement)) {
                $query->bind_param("s", $shopid);
                $query->execute();
                $result = $query->get_result();

                $psTotal = 0;
                if ($result->num_rows > 0) {
                    while ($ps = $result->fetch_assoc()) {
                        $psTotal += $ps['total'];
                        ?>
                            <tr class="table_data">
                                <td><?= date('n/j/Y',strtotime($ps["statusdate"])) ?></td>
                                <td><?= strtoupper($ps['status'])?></td>
                                <td>PS - <?= $ps["psid"]?></td>
                                <td><?= strtoupper($ps["customer"])?></td>
                                <td></td>
                                <td></td>
                                <td style="text-align:right;"></td>
                                <td style="text-align:right;"></td>
                                <td style="text-align:right;"><?= asDollars($ps['total'],2)?></td>
                                <td style="text-align:right;"></td>
                            </tr>
                        <?php
                        $alldata[]=array(
                            date('n/j/Y',strtotime($ps["statusdate"])),
                            strtoupper($ps['status']),
                            "PS - " . $ps["psid"],
                            strtoupper($ps["customer"]), '', '', '', '',
                            asDollars($ps['total'],2), ''
                        );
                    }
                    $runttl += $psTotal;
                }
                $query->close();
            } else {
                echo "PS Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            $alldata[] = array('', '', '', '', '', '', '', '', '', '');
            $alldata[] = array('TOTAL IN PROCESS', '', '', '', '', '', '', '', asDollars($runttl, 2), asDollars($runbal, 2));

            ?>

            <!-- Table totals go here, if needed-->
            <tfoot>
            <tr class="table_total">
                <td>TOTAL IN PROCESS</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td style="text-align:right;"><?= asDollars($runttl, 2) ?></td>
                <td style="text-align:right;"><?= asDollars($runbal, 2) ?></td>
            </tr>
            </tfoot>
        </table>
    </div>
</main>
<?php
include COMPONENTS_PRIVATE_PATH . "/reports-v2/includes/report_form_todaydate.php"; //only for reports with today date req
?>

<?php
include getScriptsComponent($component);
include getFooterComponent($component);
?>