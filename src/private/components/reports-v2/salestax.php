
<?php
$component = "reports-v2";
// Use this for Gen Pop Reports

include getHeadGlobal($component);
include getRulesGlobal($component);


$shopid = $_COOKIE['shopid'];

$sd = $_REQUEST['sdate'];
$ed = $_REQUEST['edate'];

$sdate = date("Y-m-d", strtotime($sd));
$edate = date("Y-m-d", strtotime($ed));

$ro_type = isset($_REQUEST['rotype']) ? filter_var($_REQUEST['rotype'], FILTER_SANITIZE_STRING) : "ALL";
$roTypeSQL = "AND rotype != 'No Approval'";
if ($ro_type != "all") {
    $roTypeSQL = "AND r.rotype = '" . $ro_type . "'";
}

$title = 'Sales Tax Report - ' . ucwords(strtolower($ro_type));  // Report Title Goes Here

$subtitle = '';  // Report SubTitle Goes Here - Hide if not needed
// $subtitle = 'my subtitle';

$template = COMPONENTS_PRIVATE . "/reports/template/excelexport.php";

?>
<body>
<?php

include getHeaderGlobal($component);
include getMenuGlobal($component);



$tablefields = array();
$alldata = array();
/*
userfee1taxable,userfee2taxable,userfee3taxable,hazwastetaxable
*/

// added hazwaste taxable


$stmt = "select userfee1taxable,userfee2taxable,userfee3taxable,hazwastetaxable,defaulttaxrate,defaultlabortaxrate,defaultsublettaxrate,hazwastetaxable,DefaultLaborTaxRate, storagetaxable from company where shopid = '$shopid'";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $query->bind_result($userfee1taxable, $userfee2taxable, $userfee3taxable, $hazwastetaxable, $defaulttaxrate, $defaultlabortaxrate, $defaultsublettaxrate, $hazwastetaxable, $DefaultLaborTaxRate, $storagetaxable);
    $query->fetch();
    $query->close();
} else {
    echo $conn->error;
}

$tsales = 0;
$tparts = 0;
$tntparts = 0;
$tlabor = 0;
$totallabor = 0;
$tntlabor = 0;
$tsublet = 0;
$tntsublet = 0;
$tuserfee1 = 0;
$tuserfee2 = 0;
$tuserfee3 = 0;
$thazwaste = 0;

$tntuserfee1 = 0;
$tntuserfee2 = 0;
$tntuserfee3 = 0;
$tnthazwaste = 0;

$tstorage = 0;
$tntstorage = 0;
$tsalestax = 0;
$taxrolist = "";
$taxcomlist = "";
$nontaxrolist = "";
$nontaxcomlist = "";


$roArr = array();
$rolist = "";

$partdiscounts = 0;
$labordiscounts = 0;
$partsdiscountsnontaxable = 0;
$partsdiscountstaxable = 0;
$labordiscountsnontaxable = 0;
$labordiscountstaxable = 0;

// ro taxrate 2/20/20
$rotaxrate = 0;
$partlinettlprice = 0;

$stmt = "select totallbr,userfee1,userfee2,userfee3,hazardouswaste,storagefee,salestax,roid,taxexempt,taxrate,labortaxrate,sublettaxrate from repairorders r left join customer c on r.shopid = c.shopid and r.customerid = c.customerid where status = 'CLOSED' $roTypeSQL and statusdate >= '$sdate' and statusdate <= '$edate' and r.shopid = '$shopid'";
//echo $stmt."<BR>";
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        $totallabor += $rs['totallbr'];
        $roArr[] = $rs['roid'];
        if ($rs["taxrate"] == 0 && $rs['labortaxrate'] == 0 && $rs['sublettaxrate'] == 0) //tax exempt
        {
            $tntuserfee1 += sbpround($rs['userfee1'], 2);
            $tntuserfee2 += sbpround($rs['userfee2'], 2);
            $tntuserfee3 += sbpround($rs['userfee3'], 2);
            $tnthazwaste += $rs['hazardouswaste'];
            $tntstorage += $rs['storagefee'];
            $nontaxrolist .= $rs['roid'] . ",";
        } else {
            $tuserfee1 += sbpround($rs['userfee1'], 2);
            $tuserfee2 += sbpround($rs['userfee2'], 2);
            $tuserfee3 += sbpround($rs['userfee3'], 2);
            //echo "ROID/Hazwaste fee " . $rs["roid"] . ":" . $rs["hazardouswaste"] . "</br>"	;


            // allowing for override for hazardouse waste
            if ($hazwastetaxable == "yes") {
                $thazwaste += $rs['hazardouswaste'];
            } else {
                $tnthazwaste += $rs['hazardouswaste'];
            }

            if ($storagetaxable == "yes") {
                $tstorage += $rs['storagefee'];
            } else {
                $tntstorage += $rs['storagefee'];
            }

            $tsalestax += $rs['salestax'];
            $taxrolist .= $rs['roid'] . ",";

            // add ro taxrate
            $rotaxrate .= $rs['roid'] . ":" . $rs['taxrate'] . ",";

            //echo "Start of Tax Exempt RO list";
            //	print_r ($rotaxrate);
            //	echo "</br>";

        }
    }
    $query->close();
}

if (substr($taxrolist, -1) == ",") {
    $taxrolist = substr($taxrolist, 0, strlen($taxrolist) - 1);
}

if (substr($nontaxrolist, -1) == ",") {
    $nontaxrolist = substr($nontaxrolist, 0, strlen($nontaxrolist) - 1);
    //echo "Non Tax RO List is " .
}

$rolist = implode(",", $roArr);


$stmt = "select complaintid from complaints where shopid = '$shopid' and roid in ($taxrolist) and cstatus = 'no'";
//echo $stmt;
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        $taxcomlist .= $rs['complaintid'] . ",";
    }
    $query->close();
}


$stmt = "select complaintid from complaints where shopid = '$shopid' and roid in ($nontaxrolist) and cstatus = 'no'";
//echo $stmt;
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        $nontaxcomlist .= $rs['complaintid'] . ",";
    }
    $query->close();
}
if (substr($taxcomlist, -1) == ",") {
    $taxcomlist = substr($taxcomlist, 0, strlen($taxcomlist) - 1);
}

if (substr($nontaxcomlist, -1) == ",") {
    $nontaxcomlist = substr($nontaxcomlist, 0, strlen($nontaxcomlist) - 1);
}


// Build a roidlist for parts for a rotaxrate

$rotlist = "";

if (substr($rotaxrate, -1) == ",") {
    $rotaxrate = substr($rotaxrate, 0, strlen($rotaxrate) - 1);
}

// Explode the rotaxrate

$rotaxr = explode(",", $rotaxrate);
foreach ($rotaxr as $rot) {

    $pos = strpos($rot, ':');
    $part1 = substr($rot, 0, $pos);
    $part2 = substr($rot, $pos + 1);
    if ($part2 == 0) {
        //echo "</br>" . "ROID with zero RO Taxrate " . $part1 . "</br>";
        //echo "Tax Rate" . $part2 . "</br>";
        $rotlist .= $part1 . ",";
    }
} // end of foreach

if (substr($rotlist, -1) == ",") {
    $rotlist = substr($rotlist, 0, strlen($rotlist) - 1);
}

if (strlen($taxrolist) > 0) {
    // now get the parts sold and add the taxable and non taxable up skipping parts that have a ro tax rate
    if (strlen($rotlist) > 0) {
        $stmt = "select roid,linettlprice,tax from parts where shopid = '$shopid' and roid in ($taxrolist) and complaintid in ($taxcomlist) and roid not in ($rotlist) and partnumber!='discount'";
    } else {
        $stmt = "select roid,linettlprice,tax from parts where shopid = '$shopid' and roid in ($taxrolist) and complaintid in ($taxcomlist) and partnumber!='discount'";
    }
    //echo $stmt."<BR>";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_array()) {
            if (strtolower($rs['tax']) == "yes") {
                $tparts += $rs['linettlprice'];

            } else {
                $tntparts += $rs['linettlprice'];

            }
        }
        $query->close();
    } else {
        echo "parts query 1 error:" . $conn->error;
    }

    // now get the parts sold and add the taxable and non taxable up only picking parts who have an ro taxrate = 0
    if (strlen($rotlist) > 0) {
        $stmt2 = "select sum(linettlprice) as 'TtlLineprice' from parts where shopid = '$shopid' and roid in ($taxrolist) and roid in ($rotlist) and partnumber!='discount'";

        if ($query = $conn->prepare($stmt2)) {
            $query->execute();
            $r2 = $query->get_result();
            while ($rs2 = $r2->fetch_array()) {
                $tntparts += $rs2['TtlLineprice'];
                //echo "Total Parts in second query" . $tntparts . "</br>";
            }
            $query->close();

        } else {
            echo "parts query 2 error:" . $conn->error;
        }
    } // end of search for
    // now get the discounts

    $stmt = "select coalesce(sum(linettlprice),0) from parts where shopid = '$shopid' and roid in ($taxrolist) and partnumber = 'discount' and partdesc = 'discount'";

    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($tpartdiscounts);
        $query->fetch();
        $query->close();
    }

    $partsdiscountstaxable += $tpartdiscounts;

} else {
    $tparts += 0;
    $tntparts += 0;
}

if (strlen($nontaxrolist) > 0) {
    // now get the parts sold and add the taxable and non taxable up
    $stmt = "select linettlprice,tax from parts where shopid = '$shopid' and roid in ($nontaxrolist) and complaintid in ($nontaxcomlist)";
    //echo $stmt."<BR>";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_array()) {

            $tntparts += $rs['linettlprice'];
        }
        $query->close();
    } else {
        echo "parts query 3 error:" . $conn->error . "<BR>";
    }
} else {
    $tntparts += 0;
}


    $stmt = "select l.schedulelength,l.linetotal,l.tech,r.labortaxrate from labor l,repairorders r where r.shopid=l.shopid and r.roid=l.roid and l.shopid = '$shopid' and l.roid in ($rolist) $roTypeSQL";

    //echo $stmt."<BR>";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_array()) {

            if(strtolower($rs['tech'])=='discount, discount')
            {
             if(strtolower($rs['schedulelength'])=='no' || $rs['labortaxrate']=='0')
             $labordiscountsnontaxable += $rs['linetotal'];
             else
             $labordiscountstaxable += $rs['linetotal'];
            }
            elseif(strtolower($rs['schedulelength'])=='no' || $rs['labortaxrate']=='0')
            $tntlabor += $rs['linetotal'];
        }
        $query->close();
    } else {
        echo "labor query 1 error:" . $conn->error . "<BR>";
    }

    //echo "Tax RO List totallabor " . $totallabor . "</br>";
    //echo "Non Tax RO List tntlabor " . $tntlabor . "</br>";


    $tlabor = $totallabor - $labordiscountsnontaxable - $tntlabor;

    //echo "tlabor After sub of tntlabor " . $tlabor . "<br>";

if (strlen($nontaxrolist) > 0) {
    // now get the labor sold and add the taxable and non taxable up


    $stmt = "select coalesce(sum(linettlprice),0) from parts where shopid = '$shopid' and roid in ($nontaxrolist) and partnumber = 'discount' and partdesc = 'discount'";

    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->bind_result($tpartdiscounts);
        $query->fetch();
        $query->close();
    }

    $partsdiscountsnontaxable += $tpartdiscounts;
    

} else {
    $tntlabor += 0;

}

if (!empty($rolist)) {
    // now get the labor sold and add the taxable and non taxable up
    $stmt = "select subletprice, s.taxable, r.sublettaxrate from sublet s JOIN repairorders r ON r.shopid = s.shopid AND r.ROID = s.roid where s.shopid = '$shopid' and s.roid in ($rolist)";
    //echo $stmt."<BR>";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $r = $query->get_result();
        while ($rs = $r->fetch_array()) {
            if ($rs['taxable'] == 'yes' && $rs['sublettaxrate'] > 0) {
                $tsublet += $rs['subletprice'];
            } else {
                $tntsublet += $rs['subletprice'];
            }
        }
        $query->close();
    } else {
        echo "sublet query 1 error:" . $conn->error . "<BR>";
    }
}

// calculate the totals
$userfee1taxsale = 0;
$userfee2taxsale = 0;
$userfee3taxsale = 0;
$hazwastetaxsale = 0;
$storagetaxsale = 0;
$userfee1nontaxsale = 0;
$userfee2nontaxsale = 0;
$userfee3nontaxsale = 0;
$hazwastenontaxsale = 0;
$storagenontaxsale = 0;

if ($userfee1taxable == "Taxable") {
    $userfee1taxsale = $tuserfee1;

    // if userfees are taxable display them as non tax when customer is taxexempt (nontaxrolist)
    // rlk 2/23/20
    $userfee1nontaxsale = $tntuserfee1;

} else {
    $userfee1nontaxsale = $tntuserfee1 + $tuserfee1;
}
if ($userfee2taxable == "Taxable") {
    $userfee2taxsale = $tuserfee2;
    // if userfees are taxable display them as non tax when customer is taxexempt (nontaxrolist)
    // rlk 2/23/20

    $userfee2nontaxsale = $tntuserfee2;

} else {
    $userfee2nontaxsale = $tntuserfee2 + $tuserfee2;
}
if ($userfee3taxable == "Taxable") {
    $userfee3taxsale = $tuserfee3;
    // if userfees are taxable display them as non tax when customer is taxexempt (nontaxrolist)
    // rlk 2/23/20

    $userfee3nontaxsale = $tntuserfee3;

} else {
    $userfee3nontaxsale = $tntuserfee3 + $tuserfee3;
}
if ($hazwastetaxable == "yes") {
    $hazwastetaxsale = $thazwaste;
    $hazwastenontaxsale = $tnthazwaste;
} else {
    $hazwastenontaxsale = $tnthazwaste + $thazwaste;
}

if ($storagetaxable == "yes") {
    $storagetaxsale = $tstorage;
    $storagenontaxsale = $tntstorage;
} else {
    $storagenontaxsale = $tntstorage + $tstorage;
}

$ttaxfees = $userfee1taxsale + $userfee2taxsale + $userfee3taxsale + $hazwastetaxsale + $storagetaxsale;
$tntaxfees = $userfee1nontaxsale + $userfee2nontaxsale + $userfee3nontaxsale + $hazwastenontaxsale + $storagenontaxsale;

$ttaxps = 0;
$tntaxps = 0;
$stmt = "select subtotal, tax from ps where status = 'closed' and statusdate >= '$sdate' and statusdate <= '$edate' and shopid = '$shopid'";
//echo $stmt;
if ($query = $conn->prepare($stmt)) {
    $query->execute();
    $r = $query->get_result();
    while ($rs = $r->fetch_array()) {
        if ($rs['tax'] <= 0.001) {
            $tntaxps += $rs['subtotal'];
        } else {
            $ttaxps += $rs['subtotal'];
            $tsalestax += $rs['tax'];
        }
    }
    $query->close();
} else {
    echo $conn->error;
}


$stmt = "SELECT ps.psid, ps.subtotal, ps.tax, ps.status, c.taxexempt FROM ps as ps
            JOIN customer c ON c.shopid = ps.shopid AND c.CustomerID = ps.cid
            WHERE ps.shopid = ? AND ps.statusdate >= ? AND ps.statusdate <= ? AND ps.`status` = 'Closed'";

if($query = $conn->prepare($stmt)){
    $query->bind_param("sss",$shopid,$sdate,$edate);
    $query->execute();
    $ps_result = $query->get_result();
}else{
    echo "Part Sale Prepare failed: (" . $conn->errno . ") " . $conn->error;
}

$taxable_parts_price = 0;
$non_taxable_parts_price = 0;
if ($ps_result->num_rows > 0) {
    while($ps = $ps_result->fetch_array()) {
        $nt_parts = 0; $t_parts = 0;

        $taxexempt = $ps['taxexempt'];

        $stmt = "select sum(ext) as ntprice from psdetail where lcase(tax) = 'no' and shopid = ? and psid = ? ";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("si", $shopid, $ps['psid']);
            $query->execute();
            $ntpresult = $query->get_result();
        } else {
            echo "Net Price Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }

        $ntp = mysqli_fetch_assoc($ntpresult);

        if (!empty($ntpresult)) {

            if (!is_null($ntp["ntprice"])) {
                // number format out here
                $nt_parts = $ntp["ntprice"];
            } else {
                $nt_parts = "0.00";
            }

        } else {
            $nt_parts = 0;
        }

        // get taxable parts
        $stmt = "select sum(ext) as ntprice from psdetail where lcase(tax) = 'yes' and shopid = ? and psid = ? ";

        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("si", $shopid, $ps['psid']);
            $query->execute();
            $prsresult = $query->get_result();
        } else {
            echo "Net Price Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }

        $prs = mysqli_fetch_assoc($prsresult);

        if (!empty($prsresult)) {

            if (!is_null($prs["ntprice"])) {
                $t_parts = $prs["ntprice"];
            } else {
                $t_parts = "0.00";
            }

        } else {
            $t_parts = 0;
        }
        //tax exempt this trumps everything
        if ($taxexempt == 'yes') {
            $nt_parts = $ps["subtotal"];
            $t_parts = 0;
        } // end

        $taxable_parts_price += $t_parts;
        $non_taxable_parts_price += $nt_parts;
    }
}


/*$partsdiscountsnontaxable = 0;
$partsdiscountstaxable = 0;
$labordiscountsnontaxable = 0;
$labordiscountstaxable = 0;

if ($defaulttaxrate > 0){
    $partsdiscountstaxable = $partdiscounts;
}else{
    $partsdiscountsnontaxable = $partdiscounts;
}

if ($defaultlabortaxrate > 0){
    $labordiscountstaxable = $labordiscounts;
}else{
    $labordiscountsnontaxable = $labordiscounts;
}

if ($DefaultLaborTaxRate == 0) {
    $tntlabor += $tlabor;
    $tlabor = 0;
}
*/

$partdiscounts = $partsdiscountstaxable + $partsdiscountsnontaxable;
$labordiscounts = $labordiscountstaxable + $labordiscountsnontaxable;


?>
<main id="reports">
    <div class="report">
        <div class="col-12">
            <div class="row">
                <div class="col-md-10 col-sm-10">
                    <div class="title col breadcrumb d-flex align-items-center mb-0">
                        <a href="<?= COMPONENTS_PRIVATE ?>/v2/reports/reports.php" class="text-secondary d-print-none">Reports</a>
                        <span
                            class="text-secondary d-print-none ps-3 pe-3">/</span>
                        <h2 class="d-print-none"><?= $title; ?></span></h2>
                        <h4 class="d-none d-print-inline"><?= $title . $companyNamePrint; ?></h4>
                    </div>
                </div>
                <div class="col-md-2 col-sm-4">
                    <button type="button" class="btn btn-secondary btn-md d-print-none float-end"
                            onclick="printreport()">Print
                    </button>
                </div>
            </div>
            <hr/>
            <p class="card-title">
                <?php
                if ((!empty($sd))) {
                    echo($sd);
                }
                if (!empty($sd) && !empty($ed)) {
                    echo ' to ';
                }
                if (!empty($ed)) {
                    echo $ed;
                }
                if (empty($sd) && empty($ed)) {
                    echo date('m/d/Y');
                }
                ?>
            </p>
            <p>
                <?= $subtitle ?? "" ?>
            </p>
        </div>
    </div>
    <div class="report d-print-none">
        <?php
            include_once "inline_prompt.php";
        ?>
    </div>
    <div class="d-flex">
        <section class="container-fluid" id="">
    <h5>Repair Orders</h5>
    <table class="table table-condensed table-hover reports_table">
        <thead>
            <tr class="">
                <th colspan="2" class="auto-style1">Discounts</th>
            </tr>
        </thead>
        <tr>
            <td>Parts Discounts</td>
            <td class="text-right"><?php echo asDollars($partdiscounts); ?></td>
        </tr>
        <tr>
            <td>Labor Discounts</td>
            <td class="text-right"><?php echo asDollars($labordiscounts); ?></td>
        </tr>
    </table>
    <table class="table table-condensed table-hover reports_table">
        <thead>
            <tr class="">
                <th colspan="2" class="auto-style1" style="width: 50%">Non-Taxable</th>
                <th colspan="2" class="auto-style1" style="width: 50%">Taxable</th>
            </tr>
        </thead>
        <tr>
            <td style="width: 25%">Non-Taxable Parts in RO's</td>
            <td style="width: 25%"
                class="text-right"><?php echo asDollars($tntparts + $partsdiscountsnontaxable); ?></td>
            <td style="width: 25%">Taxable Parts in RO's</td>
            <td style="width: 25%" class="text-right"><?php echo asDollars($tparts + $partsdiscountstaxable); ?></td>
        <tr>
            <td>Non-Taxable Labor in RO's</td>
            <td class="text-right"><?php echo asDollars($tntlabor + $labordiscountsnontaxable); ?></td>
            <td>Taxable Labor in RO's</td>
            <td class="text-right"><?php echo asDollars($tlabor); ?></td>
        </tr>
        <tr>
            <td>Non-Taxable Sublet in RO's</td>
            <td class="text-right"><?php echo asDollars($tntsublet); ?></td>
            <td>Taxable Sublet in RO's</td>
            <td class="text-right"><?php echo asDollars($tsublet); ?></td>
        </tr>
        <tr>
            <td>Non-Taxable Fees in RO's</td>
            <td class="text-right"><?php echo asDollars($tntaxfees); ?></td>
            <td>Taxable Fees in RO's</td>
            <td class="text-right"><?php echo asDollars($ttaxfees); ?></td>
        </tr>
    </table>
    <h5>Part Sales</h5>
    <table class="table table-condensed table-hover reports_table">
        <thead>
            <tr class="">
                <th colspan="2" class="auto-style1" style="width: 50%">Non-Taxable</th>
                <th colspan="2" class="auto-style1" style="width: 50%">Taxable</th>
            </tr>
        </thead>
        <tr>
            <td style="width: 25%">Non-Taxable PartSales</td>
            <td style="width: 25%" class="text-right"><?php echo asDollars($non_taxable_parts_price); ?></td>
            <td style="width: 25%">Taxable PartSales</td>
            <td style="width: 25%" class="text-right"><?php echo asDollars($taxable_parts_price); ?></td>
        </tr>
    </table>

    <h5>Totals</h5>
    <table class="table table-condensed reports_table table_total font-weight-bold">
        <tr>
            <td>Total Taxable</td>
            <td class="text-right"><?php echo asDollars($tparts + $tlabor + $tsublet + $ttaxfees + $partsdiscountstaxable + $taxable_parts_price, 2); ?></td>
        </tr>
        <tr>
            <td>Total Non-Taxable</td>
            <td class="text-right"><?php echo asDollars($tntparts + $tntlabor + $tntsublet + $tntaxfees + $labordiscountsnontaxable + $partsdiscountsnontaxable + $non_taxable_parts_price, 2); ?></td>
        </tr>
        <tr>
            <td>Total Sales w/o Tax</td>
            <td class="text-right"><?php echo asDollars($tparts + $tlabor + $tsublet + $ttaxfees + $tntparts + $tntlabor + $tntsublet + $tntaxfees + $ttaxps + $tntaxps + $partsdiscountstaxable + $labordiscountsnontaxable + $partsdiscountsnontaxable, 2); ?></td>
        <tr>
            <td>Tax Rate</td>
            <td class="text-right"><?php echo $defaulttaxrate; ?>%</td>
        </tr>
        <tr>
            <td>Total Tax Collected</td>
            <td class="text-right"><?php echo asDollars($tsalestax, 2); ?></td>
        </tr>
    </table>
        </section>
</div>

<?php
/*****************************
 *
 *
 *  SHOP PRODUCTION REPORT
 *
 *
 ***************************/
if ($_REQUEST['spd'] == "true") {

    $title = 'Shop Production Detail Report';  // Report Title Goes Here
    $subtitle = 'This report has been modified to include only Closed Repair Orders. ';  // Report SubTitle Goes Here - Hide if not needed
// $subtitle = 'my subtitle';
    $stmt = "select trim(upper(companyname)), conamereports from company where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($companyname, $conamereports);
        $query->fetch();
        $query->close();
    }

    if ($conamereports == 'yes') {
        $title .= " - $companyname";
    }

    $sdate = date_format(new DateTime($sd), 'Y-m-d');
    $edate = date_format(new DateTime($ed), 'Y-m-d');

// Bringing in default tax rates
    $stmt = "SELECT replacerowithtag,defaulttaxrate,defaultlabortaxrate,defaultSubletTaxRate,userfee1taxable,userfee2taxable,userfee3taxable,hazwastetaxable,profitboost ";
    $stmt .= "FROM company ";
    $stmt .= "WHERE shopid = ? ";
//echo $stmt;

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $coresult = $query->get_result();
    } else {
        echo "Company Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }

    $co = $coresult->fetch_array();

    $rrwt = strtolower($co["replacerowithtag"]);
    $defaulttaxrate = $co["defaulttaxrate"];
    $defaultlabortaxrate = $co["defaultlabortaxrate"];
    $defaultSubletTaxRate = $co["defaultSubletTaxRate"];
    $userfee1taxable = $co["userfee1taxable"];
    $userfee2taxable = $co["userfee2taxable"];
    $userfee3taxable = $co["userfee3taxable"];
    $hazwastetaxable = $co["hazwastetaxable"];
    $profitboost = $co['profitboost'];

// if there are any labor items with count(*) where schedulelength = 'no' then show both columns. Coming from canned jobs
// if shop labor ltr = 0 exclude
    $nlcount = 0;

    if ($defaultlabortaxrate != 0) {
        $nlstmt = "SELECT count(l.schedulelength) as 'NonLaborCount' ";
        $nlstmt .= " FROM repairorders r";
        $nlstmt .= " JOIN labor l";
        $nlstmt .= "   ON r.shopid = l.shopid and r.roid = l.roid";
        $nlstmt .= " WHERE r.shopid = ? ";
        $nlstmt .= "  AND r.statusdate >= ? ";
        $nlstmt .= "  AND r.status = 'Closed' $roTypeSQL ";
        $nlstmt .= "  AND r.statusdate <= ? ";
        $nlstmt .= "  AND l.schedulelength = 'no'";
        $nlstmt .= " ORDER BY r.statusdate ";

//echo $stmt;

        if ($query = $conn->prepare($nlstmt)) {
            $query->bind_param("sss", $shopid, $sdate, $edate);
            $query->execute();
            $nlresult = $query->get_result();
        } else {
            echo "Non Labor Count Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }

        $nl = $nlresult->fetch_array();

        if ($nlresult->num_rows > 0) {
            $nlcount = $nl["NonLaborCount"];
//echo "Non Labor Count is : " . $nlcount;
        }
    }

    // Use this for Gen Pop Reports
    // require "includes/report_buttons.php";

    // Use this for Custom Reports

    ?>
    <div class="d-print-none">
        <hr style="page-break-after: always" />
        <div class="mt-5 mb-5">
            <h2 class=""><?= $title ?></h2>
            <p class=""><?= $subtitle ?></p>
        </div>
    </div>


    <div class="report d-print-none">
        <table class="spd_report" id="spd_report">
            <thead>
            <tr>
                <th></th>
                <th>RO#</th>
                <th>Status Date</th>
                <th>Customer</th>
                <?php
                if ($defaultlabortaxrate > 0 || $nlcount > 0) {
                    ?>
                    <th class="calc_total">Taxable Labor</th>
                    <th class="calc_total">Non-Tax Labor</th>
                    <?php
                } else {
                    ?>
                    <th class="calc_total">Labor</th>
                    <?php
                }
                ?>
                <th class="calc_total">Taxable Parts</th>
                <th class="calc_total">Non-Tax Parts</th>
                <th class="calc_total">Sublet</th>
                <th class="calc_total">Fees</th>
                <th class="calc_total">Taxable Subtotal</th>
                <th class="calc_total">Non-Tax Subtotal</th>
                <th class="calc_total">Tax</th>
                <th class="calc_total">Discount</th>
                <th class="calc_total">Total RO</th>
                <th class="calc_total">Parts Cost</th>
                <?php
                if ($profitboost == "yes") {
                    ?>
                    <th class="">PPH</th>
                    <?php
                }
                ?>
            </tr>
            </thead>
            <tbody>
            <?php

            //$tablefields=array('RO#','Status Date','Customer','Labor','Parts','Non Tax Parts','Tax Parts','Sublet','Fees','Taxable Subtotal','Non-Tax Subtotal','Tax','Discount','Total RO','Parts Cost');//set table headings array for excel export
            //$alldata=array();//this will hold all the data arrays to be exported to excel
            if ($defaultlabortaxrate > 0 || $nlcount > 0) {
                $alldata[] = array('TABLETITLE1', 'Repair Order Details');
                $alldataArr = array('TABLEHEAD', 'RO#', 'Status Date', 'Customer', 'Taxable Labor', 'Non-Tax Labor', 'Taxable Parts', 'Non-Tax Parts', 'Sublet', 'Fees', 'Taxable Subtotal', 'Non-Tax Subtotal', 'Tax', 'Discount', 'Total RO', 'Parts Cost');
            } else {
                $alldata[] = array('TABLETITLE2', 'Repair Order Details');
                $alldataArr = array('TABLEHEAD', 'RO#', 'Status Date', 'Customer', 'Labor', 'Taxable Parts', 'Non-Tax Parts', 'Sublet', 'Fees', 'Taxable Subtotal', 'Non-Tax Subtotal', 'Tax', 'Discount', 'Total RO', 'Parts Cost');
            }
            if ($profitboost == "yes") {
                $alldataArr[] = "PPH";
            }
            $alldata[] = $alldataArr;

            $stmt = "SELECT roid,tagnumber,statusdate,totalprts,totallbr,totalro,totalsublet,totalfees,discountamt,salestax,customer,fleetno,partscost,taxrate,labortaxrate,sublettaxrate,userfee1,userfee2,userfee3,HazardousWaste,storagefee,PPH ";
            $stmt .= "FROM repairorders ";
            $stmt .= "WHERE shopid = ? ";
            $stmt .= "  AND statusdate >= ? ";
            $stmt .= "  AND repairorders.status = 'Closed' $roTypeSQL";
            $stmt .= "  AND statusdate <= ? ";
            $stmt .= " ORDER BY statusdate ";

            //echo $stmt;

            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sss", $shopid, $sdate, $edate);
                $query->execute();
                $roresult = $query->get_result();
            } else {
                echo "Repair Order Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }

            $ro = mysqli_fetch_assoc($roresult);

            // resetting the result set pointer to beginning
            mysqli_data_seek($roresult, 0);
            ?>

            <?php
            $runningpmts = 0;
            $labor = 0;
            $parts = 0;
            $sublet = 0;
            $fees = 0;
            $slstax = 0;
            $pcost = 0;
            $ttldisc = 0;
            $ttlntparts = 0;
            $ttltparts = 0;
            $runntsubtotal = 0;
            $runsubtotal = 0;
            $ttldisc = 0;
            $ttldiscparts = 0;

            $tro = 0;
            $dfirstdate = "";

            $runtaxlabor = 0;
            $runnontaxlabor = 0;

            $gtruntaxlabor = 0;
            $gtrunnontaxlabor = 0;
            $gtttltparts = 0;
            $gtttlntparts = 0;
            $gtsublet = 0;
            $gtfees = 0;
            $gtrunsubtotal = 0;
            $gtrunntsubtotal = 0;
            $gtslstax = 0;
            $gtttldisc = 0;
            $gttro = 0;
            $gtpcost = 0;
            $rocount = $roresult->num_rows;
            $ttlPPH = 0;
            if ($rocount > 0) {
                while ($ro = $roresult->fetch_array()) {
                    $roid = $ro["roid"];

                    $statusdate = new DateTime($ro["statusdate"]);
                    $statusdate = date_format($statusdate, 'm/d/Y');

                    $today = new DateTime('now');
                    $today = date_format($today, 'm/d/Y');

                    if (strlen($ro["fleetno"]) > 0) {
                        $fleetno = " (#" . $ro["fleetno"] . ")";
                    } else {
                        $fleetno = "";
                    }

                    // Get sum of valid parts cost for roid

                    $stmt = "SELECT coalesce(sum(cost*quantity),0) as pcost ";
                    $stmt .= "FROM parts ";
                    $stmt .= "WHERE shopid = ? ";
                    $stmt .= "  AND roid = ? ";
                    $stmt .= "  AND deleted = 'no'";
                    //echo $stmt;

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("si", $shopid, $roid);
                        $query->execute();
                        $partresult = $query->get_result();
                    } else {
                        echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    if ($partresult->num_rows > 0) {

                        $part = $partresult->fetch_array();

                        if (is_null($part["pcost"])) {
                            $mypcost = 0;
                        } else {
                            $mypcost = $part["pcost"];
                        } // end of is null

                    } // end of parts end if

                    // all instances except adding into RO going to after labor read
                    //$labor = $labor + $ro["totallbr"];

                    $parts = $parts + $ro["totalprts"];
                    $sublet = $sublet + $ro["totalsublet"];
                    $fees = $fees + $ro["totalfees"];
                    $slstax = $slstax + $ro["salestax"];
                    $pcost = $pcost + $mypcost;
                    $ttldisc = $ttldisc + $ro["discountamt"];

                    if ($rrwt == "yes" && !empty($ro["tagnumber"])) {
                        $displayroid = $ro["tagnumber"];
                    } else {
                        $displayroid = $ro["roid"];
                    }

                    $ntparts = 0;
                    $tparts = 0;
                    $ntsubtotal = 0;
                    $partsdiscount = 0;
                    $discountamt = 0;

                    // add back in the non taxable parts
                    $stmt = "SELECT linettlprice as ntprice ";
                    $stmt .= " FROM parts p ";
                    $stmt .= " LEFT JOIN complaints c ";
                    $stmt .= "   on p.shopid = c.shopid and p.complaintid = c.complaintid and p.ROID = c.roid ";
                    $stmt .= "WHERE p.shopid = ? ";
                    $stmt .= "  AND cstatus = 'no' ";
                    $stmt .= "  AND deleted = 'no' AND tax = 'NO'";
                    $stmt .= "  AND p.roid = ? ";
                    //echo $stmt;

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("si", $shopid, $roid);
                        $query->execute();
                        $prsresult = $query->get_result();
                    } else {
                        echo "Parts Complaints Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    if ($prsresult->num_rows > 0) {

                        while ($prs = $prsresult->fetch_array()) {

                            $ntparts = $ntparts + $prs["ntprice"];

                        } // end of while for parts complaint

                    } // end if of parts complaint


                    // moving this down below taxexempt
                    //$ttlntparts = $ttlntparts + $ntparts;

                    // add back in the taxable parts
                    $stmt = "SELECT linettlprice as tprice ";
                    $stmt .= " FROM parts p ";
                    $stmt .= " LEFT JOIN complaints c ";
                    $stmt .= "   on p.shopid = c.shopid and p.complaintid = c.complaintid and p.ROID = c.roid ";
                    $stmt .= "WHERE p.shopid = ? ";
                    $stmt .= "  AND cstatus = 'no' ";
                    $stmt .= "  AND deleted = 'no' AND tax = 'YES'";
                    $stmt .= "  AND p.roid = ? ";
                    //echo $stmt;

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("si", $shopid, $roid);
                        $query->execute();
                        $prsresult = $query->get_result();
                    } else {
                        echo "Parts Complaints Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    if ($prsresult->num_rows > 0) {

                        while ($prs = $prsresult->fetch_array()) {

                            $tparts = $tparts + $prs["tprice"];
                            //echo "Taxable Parts Line 347 " . $tparts . "</br>";

                        } // end of while for parts complaint

                    } // end if of parts complaint

                    //tax exempt this trumps everything
                    // keep from doubling up by seeing if non taxable part was already accounted for
//				if ($ro["taxrate"] == 0 && $ntparts == 0 ) {
                    if ($ro["taxrate"] == 0) {
                        if ($tparts > 0) {
                            $ntparts = $ntparts + $tparts;
                            $tparts = 0;
                        } else {
                            // because all parts regardless are non taxable when ro taxrate = 0 ..just pick up totalprts 3/12/20
                            $ntparts = $ro["totalprts"];
                            $tparts = 0;
                        }
                    } // end

                    $ttlntparts = $ttlntparts + $ntparts;

                    /*
                    Move the discount read to here so tparts can be updated with parts discounts
                    */
                    // add the discount amount on parts amount
                    $stmt = "SELECT coalesce(sum(linettlprice),0) partsdiscount ";
                    $stmt .= " FROM parts  ";
                    $stmt .= "WHERE shopid = ? ";
                    $stmt .= "  AND partnumber = 'DISCOUNT' ";
                    $stmt .= "  AND roid = ? ";
                    //echo $stmt;

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("si", $shopid, $roid);
                        $query->execute();
                        $prsresult = $query->get_result();
                    } else {
                        echo "Discount Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    // had numeric_format originally
                    if ($prsresult->num_rows > 0) {
                        while ($prs = $prsresult->fetch_array()) {
                            $partsdiscount = $partsdiscount + $prs["partsdiscount"];
                            $discountamt = $discountamt + $partsdiscount + $ro["discountamt"];
                        } // end while
                    }

                    // adding the discount to tparts rlk 1/31/20
                    if ($ro["taxrate"] == 0 && $ro['labortaxrate'] == 0 && $ro['sublettaxrate'] == 0) {
                        $ntparts = $ntparts + $partsdiscount;
                        $ttlntparts = $ttlntparts + $partsdiscount;
                    } else {
                        $tparts = $tparts + $partsdiscount;
                        $ttltparts = $ttltparts + $tparts;
                    }

                    //echo "Taxable Parts Line 397 " . $tparts . "</br>";


                    $gtttltparts = $gtttltparts + $tparts;

                    // moving below labor
                    //$ttldisc = $ttldisc + $discountamt;
                    $ttldiscparts = $ttldiscparts + $partsdiscount;


                    // check for labor discounts
                    $discountlabortotal = 0;
                    $labordiscount = 0;
                    $totallabor = 0;

                    $stmt = "SELECT coalesce(sum(LineTotal),0) labordiscount ";
                    $stmt .= " FROM labor  ";
                    $stmt .= "WHERE shopid = ? ";
                    $stmt .= "  AND labor = 'DISCOUNT' ";
                    $stmt .= "  AND roid = ? ";
                    //echo $stmt;

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("si", $shopid, $roid);
                        $query->execute();
                        $lrsresult = $query->get_result();
                    } else {
                        echo "Lbr Discount Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    $lrs = $lrsresult->fetch_array();

                    $labordiscount = $labordiscount + $lrs["labordiscount"];
                    $discountamt = $discountamt + $labordiscount + $ro["discountamt"];


                    //need to sub out the labor discount for $totallabor
                    //$totallabor = $ro["totallbr"] + ($labordiscount * -1);

                    // need to check this ro totallbr is accurate here... for 10223
                    //$totallabor = $ro["totallbr"] + $lrs["labordiscount"];

                    // rlk 2/14/20 ... this needs a read out to labor
                    //check for non taxable labor

                    $taxlabor = 0;
                    $nontaxlabor = 0;
                    $regularlabor = 0;
                    $ntrlabor = 0;

                    $stmt = "select coalesce(sum(linetotal),0) linetotal from labor where shopid = ? and roid = ? and schedulelength = 'no'";

                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("si", $shopid, $roid);
                        $query->execute();
                        $lbrresult = $query->get_result();
                    } else {
                        echo "Labor Prepare failed: (" . $conn->errno . ") " . $conn->error;
                    }

                    if ($lbrresult->num_rows > 0) {

                        $lbr = $lbrresult->fetch_array();
                        // if labor tax rate = 0 roll into labor
                        if ($defaultlabortaxrate == 0) {
                            //$ntrlabor =	$lbr["linetotal"];
                        } else {
                            $nontaxlabor = $lbr["linetotal"];
                        }
                    } // end of nontax labor


                    // if defaultlabor tax rate = 0 everything falls into the labor column (no tax)
                    if ($defaultlabortaxrate == 0) {

                        $regularlabor = $ro["totallbr"];
                        $taxlabor = $regularlabor - $nontaxlabor;

                        $runtaxlabor = $runtaxlabor + $taxlabor;
                        $gtruntaxlabor = $gtruntaxlabor + $taxlabor;

                        $runnontaxlabor = $runnontaxlabor + $nontaxlabor;
                        $gtrunnontaxlabor = $gtrunnontaxlabor + $nontaxlabor;

                    } else {
                        //tax exempt this trumps everything change rlk 2/26/20 and defaulttaxrate != 0

                        if ($ro["labortaxrate"] == 0) {
                            $nontaxlabor = $ro["totallbr"];
                            $runnontaxlabor = $runnontaxlabor + $ro["totallbr"];
                        } else {

                            $regularlabor = $ro["totallbr"];
                            $taxlabor = $regularlabor - $nontaxlabor;

                            $runtaxlabor = $runtaxlabor + $taxlabor;
                            $gtruntaxlabor = $gtruntaxlabor + $taxlabor;

                            $runnontaxlabor = $runnontaxlabor + $nontaxlabor;
                            $gtrunnontaxlabor = $gtrunnontaxlabor + $nontaxlabor;
                        }
                    }
                    // adding the labordiscount to total discounts
                    $ttldisc = $ttldisc + $discountamt;
                    // totalling labor after read of labor

                    // start of subtotal logic
                    $ntsubtotal = 0;
                    $subtotal = 0;
                    $ntflag = '';

                    // need to qualify which taxrates to use rlk 1/27/20
                    // assuming that ro tax rate = 0 is the same as defaultaxrate = 0

                    //tax exempt this trumps everything
                    if ($ro["taxrate"] == 0 && $ro['labortaxrate'] == 0 && $ro['sublettaxrate'] == 0) {
                        $ntsubtotal = $ro["totallbr"] + $ro["totalprts"] + $ro["totalsublet"] + $ro["totalfees"];
                        $runntsubtotal = $runntsubtotal + $ntsubtotal;
                        $ntflag = " <b>(TaxExempt)</b>";
                    } else {
                        // does not need the $nlt because it is added above wiht shchedule length
                        if ($defaultlabortaxrate == 0) {
                            // change to correct ntsubtotal
                            $ntsubtotal = $ntsubtotal + $ro["totallbr"];
                            //$ntsubtotal = $ntsubtotal + $nontaxlabor;
                        }
                        if ($defaultSubletTaxRate == 0) {
                            $ntsubtotal = $ntsubtotal + $ro["totalsublet"];
                        }
                        if ($defaulttaxrate == 0) {
                            $ntsubtotal = $ntsubtotal + $ro["totalprts"];
                        }
                        if ($defaultlabortaxrate > 0) {
                            $subtotal = $subtotal + $ro["totallbr"];
                        }
                        if ($defaultSubletTaxRate > 0) {
                            $subtotal = $subtotal + $ro["totalsublet"];
                        }
                        // adjust based on non tax parts
                        if ($defaulttaxrate > 0) {
                            $subtotal = $subtotal + ($ro["totalprts"] - $ntparts);
                            $ntsubtotal = $ntsubtotal + $ntparts;
                        }

                        //check for taxable userfees
                        if ($userfee1taxable == 'Non-Taxable') {
                            $ntsubtotal = $ntsubtotal + sbpround($ro["userfee1"]);
                        } else {
                            $subtotal = $subtotal + sbpround($ro["userfee1"]);
                        }
                        if ($userfee2taxable == 'Non-Taxable') {
                            $ntsubtotal = $ntsubtotal + sbpround($ro["userfee2"]);
                        } else {
                            $subtotal = $subtotal + sbpround($ro["userfee2"]);
                        }
                        if ($userfee3taxable == 'Non-Taxable') {
                            $ntsubtotal = $ntsubtotal + sbpround($ro["userfee3"]);
                        } else {
                            $subtotal = $subtotal + sbpround($ro["userfee3"]);
                        }
                        //check for hazwaste taxable

                        if ($hazwastetaxable == 'yes') {
                            $subtotal = $subtotal + $ro["HazardousWaste"];
                        } else {
                            $ntsubtotal = $ntsubtotal + $ro["HazardousWaste"];
                        }

                        //adding in storage rlk 3/6/20
                        if ($ro["taxrate"] != 0) {
                            $subtotal = $subtotal + $ro["storagefee"];
                        }

                        // ok adjusting subtotals based on tax labor and nontax labor

                        $ntsubtotal = $ntsubtotal + $nontaxlabor;
                        $subtotal = $subtotal - $nontaxlabor;

                        $runntsubtotal = $runntsubtotal + $ntsubtotal;
                        $runsubtotal = $runsubtotal + $subtotal;
                    }

                    // dont change this rlk
                    $totalro = $ro["totallbr"] + $ro["totalprts"] + $ro["totalsublet"] + $ro["totalfees"] + $ro["salestax"] - $ro["discountamt"];
                    $tro = $tro + $totalro;

                    ?>
                    <tr>
                        <td>Repair Orders</td>
                        <td><a onclick="showRO('<?= $displayroid ?>')" href="#"><?= $displayroid; ?></a></td>
                        <td><?= $statusdate; ?></td>
                        <td><?= left($ro["customer"], 35) . $fleetno . $ntflag; ?></td>
                        <?php
                        if ($defaultlabortaxrate > 0 || $nlcount > 0) {
                            ?>
                            <td><?php echo asDollars($taxlabor, 2, ".", $thousands_sep = ","); ?></td>
                            <td><?php echo asDollars($nontaxlabor, 2, ".", $thousands_sep = ","); ?></td>
                            <?php
                        } else {
                            ?>
                            <td><?php echo asDollars($taxlabor); ?></td>
                            <?php
                        }
                        ?>
                        <td><?= asDollars($tparts, 2, ".", $thousands_sep = ","); ?></td>
                        <td><?= asDollars($ntparts, 2, ".", $thousands_sep = ","); ?></td>
                        <td><?= asDollars($ro["totalsublet"], 2, ".", $thousands_sep = ","); ?></td>
                        <td><?= asDollars($ro["totalfees"], 2, ".", $thousands_sep = ","); ?></td>
                        <td><?= asDollars($subtotal, 2, ".", $thousands_sep = ","); ?></td>
                        <td><?= asDollars($ntsubtotal, 2, ".", $thousands_sep = ","); ?></td>
                        <td><?= asDollars($ro["salestax"], 2, ".", $thousands_sep = ","); ?></td>
                        <td><?= asDollars($discountamt, 2, ".", $thousands_sep = ","); ?></td>
                        <td><?= asDollars($totalro, 2, ".", $thousands_sep = ","); ?></td>
                        <td><?= asDollars($mypcost, 2, ".", $thousands_sep = ","); ?></td>
                        <?php
                        if ($profitboost == "yes") {
                            $ttlPPH += $ro['PPH'];
                            ?>
                            <td><?= asDollars($ro["PPH"]) ?></td>
                            <?php
                        }
                        ?>
                    </tr>

                    <?php

                    if ($defaultlabortaxrate > 0 || $nlcount > 0) {
                        $alldataArr = array($displayroid, $statusdate, strtoupper(left($ro["customer"], 35)), asDollars($taxlabor, 2, ".", $thousands_sep = ","), asDollars($nontaxlabor, 2, ".", $thousands_sep = ","), asDollars($tparts, 2, ".", $thousands_sep = ","), asDollars($ntparts, 2, ".", $thousands_sep = ","), asDollars($ro["totalsublet"], 2, ".", $thousands_sep = ","), asDollars($ro["totalfees"], 2, ".", $thousands_sep = ","), asDollars($subtotal, 2, ".", $thousands_sep = ","), asDollars($ntsubtotal, 2, ".", $thousands_sep = ","), asDollars($ro["salestax"], 2, ".", $thousands_sep = ","), asDollars($discountamt, 2, ".", $thousands_sep = ","), asDollars($totalro, 2, ".", $thousands_sep = ","), asDollars($mypcost, 2, ".", $thousands_sep = ",")); //fill up the alldata array with the arrays of data to be shown in excel export
                    } else {
                        $alldataArr = array($displayroid, $statusdate, strtoupper(left($ro["customer"], 35)), asDollars($taxlabor, 2, ".", $thousands_sep = ","), asDollars($tparts, 2, ".", $thousands_sep = ","), asDollars($ntparts, 2, ".", $thousands_sep = ","), asDollars($ro["totalsublet"], 2, ".", $thousands_sep = ","), asDollars($ro["totalfees"], 2, ".", $thousands_sep = ","), asDollars($subtotal, 2, ".", $thousands_sep = ","), asDollars($ntsubtotal, 2, ".", $thousands_sep = ","), asDollars($ro["salestax"], 2, ".", $thousands_sep = ","), asDollars($discountamt, 2, ".", $thousands_sep = ","), asDollars($totalro, 2, ".", $thousands_sep = ","), asDollars($mypcost, 2, ".", $thousands_sep = ",")); //fill up the alldata array with the arrays of data to be shown in excel export
                    }
                    if ($profitboost == "yes") {
                        $alldataArr[] = asDollars($ro['PPH']);
                    }
                    $alldata[] = $alldataArr;

                } // end of while
                ?>
                <?php

                if ($defaultlabortaxrate > 0 || $nlcount > 0) {
                    $alldata[] = array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
                    $alldataArr = array('RO Totals', '(' . $rocount . ')', '', asDollars($runtaxlabor, 2, ".", $thousands_sep = ","), asDollars($runnontaxlabor, 2, ".", $thousands_sep = ","), asDollars($ttltparts, 2, ".", $thousands_sep = ",")
                    , asDollars($ttlntparts, 2, ".", $thousands_sep = ","), asDollars($sublet, 2, ".", $thousands_sep = ",")
                    , asDollars($fees, 2, ".", $thousands_sep = ","), asDollars($runsubtotal, 2, ".", $thousands_sep = ","), asDollars($runntsubtotal, 2, ".", $thousands_sep = ",")
                    , asDollars($slstax, 2, ".", $thousands_sep = ","), asDollars($ttldisc, 2, ".", $thousands_sep = ","), asDollars($tro, 2, ".", $thousands_sep = ","), asDollars($pcost, 2, ".", $thousands_sep = ",")
                    );

                } else {
                    $alldata[] = array('', '', '', '', '', '', '', '', '', '', '', '', '', '');
                    $alldataArr = array('RO Totals', '(' . $rocount . ')', '', asDollars($runtaxlabor, 2, ".", $thousands_sep = ","), asDollars($ttltparts, 2, ".", $thousands_sep = ",")
                    , asDollars($ttlntparts, 2, ".", $thousands_sep = ","), asDollars($sublet, 2, ".", $thousands_sep = ",")
                    , asDollars($fees, 2, ".", $thousands_sep = ","), asDollars($runsubtotal, 2, ".", $thousands_sep = ","), asDollars($runntsubtotal, 2, ".", $thousands_sep = ",")
                    , asDollars($slstax, 2, ".", $thousands_sep = ","), asDollars($ttldisc, 2, ".", $thousands_sep = ","), asDollars($tro, 2, ".", $thousands_sep = ","), asDollars($pcost, 2, ".", $thousands_sep = ",")
                    );
                }
                if ($profitboost == "yes") {
                    $alldataArr[] = asDollars(($rocount > 0) ? ($ttlPPH / $rocount) : 0);
                }
                $alldata[] = $alldataArr;

            } else {
                //  echo "No Repair Orders found ";
            } //end if
            ?>
            <!--
            <tr class="table_total" style="display: none">
                <td>Repair Orders</td>
                <td colspan=""><b>RO Totals</b></td>
                <td class="">(<?= $rocount ?>)</td>
                <td></td>
                <td class=""></td>
                <?php
            if ($defaultlabortaxrate > 0 || $nlcount > 0) {
                ?>
                    <td style="text-align:right">
                        <b><?= asDollars($runtaxlabor, 2, ".", $thousands_sep = ","); ?></b></td>
                    <td style="text-align:right">
                        <b><?= asDollars($runnontaxlabor, 2, ".", $thousands_sep = ","); ?></b>
                    </td>
                    <?php
            } else {
                ?>
                    <td style="text-align:right">
                        <b><?= asDollars($runtaxlabor, 2, ".", $thousands_sep = ","); ?></b></td>
                    <?php
            }
            ?>
                <td style="text-align:right"><b> <?= asDollars($ttltparts, 2, ".", $thousands_sep = ","); ?></b>
                </td>
                <td style="text-align:right"><b><?= asDollars($ttlntparts, 2, ".", $thousands_sep = ","); ?></b>
                </td>
                <td style="text-align:right"><b><?= asDollars($sublet, 2, ".", $thousands_sep = ","); ?></b></td>
                <td style="text-align:right"><b><?= asDollars($fees, 2, ".", $thousands_sep = ","); ?></b></td>
                <td style="text-align:right"><b><?= asDollars($runsubtotal, 2, ".", $thousands_sep = ","); ?></b>
                </td>
                <td style="text-align:right"><b><?= asDollars($runntsubtotal, 2, ".", $thousands_sep = ","); ?></b>
                </td>
                <td style="text-align:right"><b><?= asDollars($slstax, 2, ".", $thousands_sep = ","); ?></b></td>
                <td style="text-align:right"><b><?= asDollars($ttldisc, 2, ".", $thousands_sep = ","); ?></b></td>
                <td style="text-align:right"><b><?= asDollars($tro, 2, ".", $thousands_sep = ","); ?></b></td>
                <td style="text-align:right"><b><?= asDollars($pcost, 2, ".", $thousands_sep = ","); ?></b></td>
                <?php
            if ($profitboost == "yes") {
                ?>
                    <td style="text-align:right"><b><?= asDollars(($rocount > 0) ? ($ttlPPH / $rocount) : 0) ?></b>
                    </td>
                    <?php
            }
            ?>
            </tr>
            -->
            <?php
            $tntparts = 0;
            $ttparts = 0;
            $ntflag = "";

            $pttltparts = 0;
            $pttlntparts = 0;
            $psublet = 0;
            $pfees = 0;
            $prunsubtotal = 0;
            $prunntsubtotal = 0;
            $pslstax = 0;
            $pttldisc = 0;
            $ptro = 0;
            $ppcost = 0;

            $stmt = "SELECT * ";
            $stmt .= "FROM ps ";
            $stmt .= "WHERE shopid = ? ";
            $stmt .= "  AND statusdate >= ? ";
            $stmt .= "  AND statusdate <= ? ";
            $stmt .= "  AND `status` = 'Closed'";

            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("sss", $shopid, $sdate, $edate);
                $query->execute();
                $psresult = $query->get_result();
            } else {
                echo "Parts Prepare failed: (" . $conn->errno . ") " . $conn->error;
            }
            $pscount = $psresult->num_rows;
            if ($psresult->num_rows > 0) {

            ?>
            <!--
            <tr class="">
                <th></th>
                <th style="width: 5%" class="">PS#</th>
                <th style="width: 8%" class="">Status Date</th>
                <th class="" style="width: 12%">Customer</th>
                <?php
            if ($defaultlabortaxrate > 0 || $nlcount > 0) {
                ?>
                    <th class="" style="text-align:right; width: 5%"></th>
                    <th class="" style="text-align:right; width: 5%"></th>
                    <?php
            } else {
                ?>
                    <th class="" style="text-align:right; width: 5%"></th>
                    <?php
            }
            ?>
                <th class="calc_total" style="text-align:right; width: 5%">Taxable Parts</th>
                <th class="calc_total" style="text-align:right; width: 5%">Non-Tax Parts</th>
                <th class="" style="text-align:right; width: 5%"></th>
                <th class="" style="text-align:right; width: 5%"></th>
                <th class="calc_total" style="text-align:right; width: 5%">Taxable Subtotal</th>
                <th class="calc_total" style="text-align:right; width: 5%">Non-Tax Subtotal</th>
                <th class="calc_total" style="text-align:right; width: 5%">Tax</th>
                <th class="calc_total" style="text-align:right; width: 5%">Discount</th>
                <th class="calc_total" style="text-align:right; width: 5%">Total Sales</th>
                <th class="calc_total" style="text-align:right; width: 5%">Parts Cost</th>
                <?php
            if ($profitboost == "yes") {
                ?>
                    <th class="" style="text-align:right; width: 5%"></th>
                    <?php
            }
            ?>
            </tr>
            -->
            <?php

            //$tablefields=array('PS#','Sale Date','Customer','','Parts','','','','','','','Tax','','Total Sale','Parts Cost');//set table headings array for excel export
            //$alldata=array();//this will hold all the data arrays to be exported to excel
            $alldata[] = array('');

            if ($defaultlabortaxrate > 0 || $nlcount > 0) {

                $alldata[] = array('TABLETITLE3', 'Part Sale Details');
                $alldataArr = array('TABLEHEAD', 'PS#', 'Status Date', 'Customer', '', '', 'Taxable Parts', 'Non-Tax Parts', '', '', 'Taxable Subtotal', 'Non-Tax Subtotal', 'Tax', 'Discount', 'Total Sale', 'Parts Cost');
            } else {
                $alldata[] = array('TABLETITLE4', 'Part Sale Details');
                $alldataArr = array('TABLEHEAD', 'PS#', 'Status Date', 'Customer', '', 'Taxable Parts', 'Non-Tax Parts', '', '', 'Taxable Subtotal', 'Non-Tax Subtotal', 'Tax', 'Discount', 'Total Sale', 'Parts Cost');
            }
            if ($profitboost == "yes") {
                $alldataArr[] = "";
            }
            $alldata[] = $alldataArr;


            while ($ps = $psresult->fetch_array()) {
                $customerid = $ps["cid"];
                $psid = $ps["psid"];
                // changed from psdate to status date
                $statusdate = new DateTime($ps["statusdate"]);
                $statusdate = date_format($statusdate, 'm/d/Y');

                $stmt = "select concat(lastname,',',firstname) as lf,taxexempt from customer where customerid = ? ";
                $stmt = $stmt . " and shopid = ?";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("ss", $customerid, $shopid);
                    $query->execute();
                    $trsresult = $query->get_result();
                } else {
                    echo "customer Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $trs = mysqli_fetch_assoc($trsresult);

                $lf = $trs["lf"];

                // check for tax exempt
                $taxexempt = $trs["taxexempt"];

                if ($taxexempt == "yes") {
                    $ntflag = " <b>(TaxExempt)</b>";
                } else {
                    $ntflag = "";
                }

                $parts = $parts + ($ps["subtotal"] - $ps["fees"]);
                $pslstax = $pslstax + $ps["tax"];

                // adding discount
                $pttldisc = $pttldisc + $ps["discount"];

                $ptro = $ptro + $ps["total"];

                //get total part cost
                $stmt = "select sum(qty*cost) as extcost from psdetail where psid = ? ";
                $stmt = $stmt . " and shopid = ? ";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("ss", $psid, $shopid);
                    $query->execute();
                    $trsresult = $query->get_result();
                } else {
                    echo "customer Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $trs = mysqli_fetch_assoc($trsresult);

                if (!empty($trsresult)) {
                    if (strlen($trs["extcost"]) > 0) {
                        $mypcost = $trs["extcost"];
                    } else {
                        $mypcost = 0;
                    }
                } else {
                    $mypcost = 0;
                }

                $ppcost = $ppcost + $mypcost;
                // get non taxable parts
                $stmt = "select sum(ext) as ntprice from psdetail where lcase(tax) = 'no' and shopid = ? and psid = ? ";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("si", $shopid, $psid);
                    $query->execute();
                    $ntpresult = $query->get_result();
                } else {
                    echo "Net Price Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $ntp = mysqli_fetch_assoc($ntpresult);

                if (!empty($ntpresult)) {

                    if (!is_null($ntp["ntprice"])) {
                        // number format out here
                        $ntparts = $ntp["ntprice"];
                    } else {
                        $ntparts = "0.00";
                    }

                } else {
                    $ntparts = 0;
                }

                // get taxable parts
                $stmt = "select sum(ext) as ntprice from psdetail where lcase(tax) = 'yes' and shopid = ? and psid = ? ";

                if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("si", $shopid, $psid);
                    $query->execute();
                    $prsresult = $query->get_result();
                } else {
                    echo "Net Price Prepare failed: (" . $conn->errno . ") " . $conn->error;
                }

                $prs = mysqli_fetch_assoc($prsresult);

                if (!empty($prsresult)) {

                    if (!is_null($prs["ntprice"])) {
                        $tparts = $prs["ntprice"];
                    } else {
                        $tparts = "0.00";
                    }

                } else {
                    $tparts = 0;
                }
                //tax exempt this trumps everything
                if ($taxexempt == 'yes') {
                    $ntparts = $ps["subtotal"];
                    $tparts = 0;
                } // end

                $pttlntparts = $pttlntparts + $ntparts;
                $pttltparts = $pttltparts + $tparts;

                $prunntsubtotal = $prunntsubtotal + $ntparts;
                $prunsubtotal = $prunsubtotal + $tparts;

                //Note ntpart and tparts is also non taxaxable subtotal and taxable subtotal
                ?>
                <tr class="">
                    <td>Part Sales</td>
                    <td><?= $ps["psid"]; ?></td>
                    <td><?= $statusdate; ?></td>
                    <td style="text-align:left"><?= left($lf, 35) . $ntflag; ?></td>
                    <?php
                    if ($defaultlabortaxrate > 0 || $nlcount > 0) {
                        ?>
                        <td></td>
                        <td></td>
                        <?php
                    } else {
                        ?>
                        <td></td>
                        <?php
                    }
                    ?>
                    <td><?php echo asDollars($tparts, 2); ?></td>
                    <td><?php echo asDollars($ntparts, 2); ?></td>
                    <td></td>
                    <td></td>
                    <td><?php echo asDollars($tparts); ?></td>
                    <td><?php echo asDollars($ntparts); ?></td>
                    <td><?php echo asDollars($ps["tax"], 2); ?></td>
                    <td><?php echo asDollars($ps["discount"], 2); ?></td>
                    <td><?php echo asDollars($ps["total"], 2); ?></td>
                    <td><?php echo asDollars($mypcost, 2); ?></td>
                    <?php
                    if ($profitboost == "yes") {
                        ?>
                        <td></td>
                        <?php
                    }
                    ?>
                </tr>

                <?php

                if ($defaultlabortaxrate > 0 || $nlcount > 0) {
                    $alldataArr = array($ps["psid"], $statusdate, strtoupper(left($lf, 25)), '', '', asDollars($tparts, 2), asDollars($ntparts, 2), '', '', asDollars($tparts, 2), asDollars($ntparts, 2), asDollars($ps["tax"], 2), '', asDollars($ps["total"], 2), asDollars($mypcost, 2)); //fill up the alldata array with the arrays of data to be shown in excel export
                } else {
                    $alldataArr = array($ps["psid"], $statusdate, strtoupper(left($lf, 25)), '', asDollars($tparts, 2), asDollars($ntparts, 2), '', '', asDollars($tparts, 2), asDollars($ntparts, 2), asDollars($ps["tax"], 2), '', asDollars($ps["total"], 2), asDollars($mypcost, 2)); //fill up the alldata array with the arrays of data to be shown in excel export
                }
                if ($profitboost == "yes") {
                    $alldataArr[] = "";
                }
                $alldata[] = $alldataArr;
            } // end of while
            ?>
            <!--
            <tfoot>
            <tr class="table_total">
                <td>Part Sales</td>
                <td><b>PS Totals</b></td>
                <td style="text-align:left">(<?= $pscount ?>)</td>
                <td class="text-right"></td>
                <?php
            if ($defaultlabortaxrate > 0 || $nlcount > 0) {
                ?>
                    <td style="text-align:right"><b><?= ''; ?></b></td>
                    <td style="text-align:right"><b><?= ''; ?></b></td>
                    <?php
            } else {
                ?>
                    <td style="text-align:right"><b><?= ''; ?></b></td>
                    <?php
            }
            ?>
                <td style="text-align:right"><b><?= asDollars($pttltparts, 2, ".", $thousands_sep = ","); ?></b>
                </td>
                <td style="text-align:right"><b><?= asDollars($pttlntparts, 2, ".", $thousands_sep = ","); ?></b>
                </td>
                <td style="text-align:right"><b><?= asDollars($psublet, 2, ".", $thousands_sep = ","); ?></b></td>
                <td style="text-align:right"><b><?= asDollars($pfees, 2, ".", $thousands_sep = ","); ?></b></td>
                <td style="text-align:right"><b><?= asDollars($prunsubtotal, 2, ".", $thousands_sep = ","); ?></b>
                </td>
                <td style="text-align:right"><b><?= asDollars($prunntsubtotal, 2, ".", $thousands_sep = ","); ?></b>
                </td>
                <td style="text-align:right"><b><?= asDollars($pslstax, 2, ".", $thousands_sep = ","); ?></b></td>
                <td style="text-align:right"><b><?= asDollars($pttldisc, 2, ".", $thousands_sep = ","); ?></b></td>
                <td style="text-align:right"><b><?= asDollars($ptro, 2, ".", $thousands_sep = ","); ?></b></td>
                <td style="text-align:right"><b><?= asDollars($ppcost, 2, ".", $thousands_sep = ","); ?></b></td>
                <?php
            if ($profitboost == "yes") {
                ?>
                    <td style="text-align:right"></td>
                    <?php
            }
            ?>
            </tr> -->
            <?php
            if ($defaultlabortaxrate > 0 || $nlcount > 0) {
                $alldata[] = array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
                $alldataArr = array('PS Totals', '(' . $pscount . ')', '', '', '', asDollars($pttltparts, 2, ".", $thousands_sep = ",")
                , asDollars($pttlntparts, 2, ".", $thousands_sep = ","), asDollars($psublet, 2, ".", $thousands_sep = ",")
                , asDollars($pfees, 2, ".", $thousands_sep = ","), asDollars($prunsubtotal, 2, ".", $thousands_sep = ","), asDollars($prunntsubtotal, 2, ".", $thousands_sep = ",")
                , asDollars($pslstax, 2, ".", $thousands_sep = ","), asDollars($pttldisc, 2, ".", $thousands_sep = ","), asDollars($ptro, 2, ".", $thousands_sep = ","), asDollars($ppcost, 2, ".", $thousands_sep = ",")
                );

            } else {
                $alldata[] = array('', '', '', '', '', '', '', '', '', '', '', '', '', '');
                $alldataArr = array('PS Totals', '(' . $pscount . ')', '', '', asDollars($pttltparts, 2, ".", $thousands_sep = ",")
                , asDollars($pttlntparts, 2, ".", $thousands_sep = ","), asDollars($psublet, 2, ".", $thousands_sep = ",")
                , asDollars($pfees, 2, ".", $thousands_sep = ","), asDollars($prunsubtotal, 2, ".", $thousands_sep = ","), asDollars($prunntsubtotal, 2, ".", $thousands_sep = ",")
                , asDollars($pslstax, 2, ".", $thousands_sep = ","), asDollars($pttldisc, 2, ".", $thousands_sep = ","), asDollars($ptro, 2, ".", $thousands_sep = ","), asDollars($ppcost, 2, ".", $thousands_sep = ",")
                );
            }
            if ($profitboost == "yes") {
                $alldataArr[] = "";
            }
            $alldata[] = $alldataArr;

            $gtttltparts = $pttltparts + $ttltparts;
            $gtttlntparts = $pttlntparts + $ttlntparts;
            $gtsublet = $psublet + $sublet;
            $gtfees = $pfees + $fees;
            $gtrunsubtotal = $prunsubtotal + $runsubtotal;
            $gtrunntsubtotal = $prunntsubtotal + $runntsubtotal;
            $gtslstax = $pslstax + $slstax;
            $gtttldisc = $pttldisc + $ttldisc;
            $gttro = $ptro + $tro;
            $gtpcost = $ppcost + $pcost;

            // this should be fine
            $gtrunnontaxlabor = $runnontaxlabor;

            $totalCount = $rocount + $pscount;

            ?>
            <tr class="table_total">
                <td>Grand Totals</td>
                <td><b>Grand Totals</b></td>
                <td><?= $totalCount ?></td>
                <td></td>
                <?php
                if ($defaultlabortaxrate > 0 || $nlcount > 0) {
                    ?>
                    <td>
                        <b><?= asDollars($gtruntaxlabor, 2, ".", $thousands_sep = ","); ?></b>
                    </td>
                    <td>
                        <b><?= asDollars($gtrunnontaxlabor, 2, ".", $thousands_sep = ","); ?></b>
                    </td>
                    <?php
                } else {
                    ?>
                    <td>
                        <b><?= asDollars($gtruntaxlabor, 2, ".", $thousands_sep = ","); ?></b>
                    </td>
                    <?php
                }
                ?>
                <td>
                    <b><?= asDollars($gtttltparts, 2, ".", $thousands_sep = ","); ?></b>
                </td>
                <td>
                    <b><?= asDollars($gtttlntparts, 2, ".", $thousands_sep = ","); ?></b>
                </td>
                <td>
                    <b><?= asDollars($gtsublet, 2, ".", $thousands_sep = ","); ?></b></td>
                <td>
                    <b><?= asDollars($gtfees, 2, ".", $thousands_sep = ","); ?></b></td>
                <td>
                    <b><?= asDollars($gtrunsubtotal, 2, ".", $thousands_sep = ","); ?></b>
                </td>
                <td>
                    <b><?= asDollars($gtrunntsubtotal, 2, ".", $thousands_sep = ","); ?></b></td>
                <td>
                    <b><?= asDollars($gtslstax, 2, ".", $thousands_sep = ","); ?></b></td>
                <td>
                    <b><?= asDollars($gtttldisc, 2, ".", $thousands_sep = ","); ?></b></td>
                <td>
                    <b><?= asDollars($gttro, 2, ".", $thousands_sep = ","); ?></b></td>
                <td>
                    <b><?= asDollars($gtpcost, 2, ".", $thousands_sep = ","); ?></b></td>
                <?php
                if ($profitboost == "yes") {
                    ?>
                    <td>
                        <b><?= asDollars(($rocount > 0) ? ($ttlPPH / $rocount) : 0) ?></b>
                    </td>
                    <?php
                }
                ?>
            </tr>

            <?php
            if ($defaultlabortaxrate > 0 || $nlcount > 0) {
                $alldata[] = array('', '', '', '', '', '', '', '', '', '', '', '', '', '', '');
                $alldataArr = array('Grand Totals', '(' . $totalCount . ')', '', asDollars($gtruntaxlabor, 2, ".", $thousands_sep = ","), asDollars($gtrunnontaxlabor, 2, ".", $thousands_sep = ","), asDollars($gtttltparts, 2, ".", $thousands_sep = ",")
                , asDollars($gtttlntparts, 2, ".", $thousands_sep = ","), asDollars($gtsublet, 2, ".", $thousands_sep = ",")
                , asDollars($gtfees, 2, ".", $thousands_sep = ","), asDollars($gtrunsubtotal, 2, ".", $thousands_sep = ","), asDollars($gtrunntsubtotal, 2, ".", $thousands_sep = ",")
                , asDollars($gtslstax, 2, ".", $thousands_sep = ","), asDollars($gtttldisc, 2, ".", $thousands_sep = ","), asDollars($gttro, 2, ".", $thousands_sep = ","), asDollars($gtpcost, 2, ".", $thousands_sep = ",")
                );

            } else {
                $alldata[] = array('', '', '', '', '', '', '', '', '', '', '', '', '', '');
                $alldataArr = array('Grand Totals', '(' . $totalCount . ')', '', asDollars($runtaxlabor, 2, ".", $thousands_sep = ","), asDollars($ttltparts, 2, ".", $thousands_sep = ",")
                , asDollars($gtttlntparts, 2, ".", $thousands_sep = ","), asDollars($gtsublet, 2, ".", $thousands_sep = ",")
                , asDollars($gtfees, 2, ".", $thousands_sep = ","), asDollars($gtrunsubtotal, 2, ".", $thousands_sep = ","), asDollars($gtrunntsubtotal, 2, ".", $thousands_sep = ",")
                , asDollars($gtslstax, 2, ".", $thousands_sep = ","), asDollars($gtttldisc, 2, ".", $thousands_sep = ","), asDollars($gttro, 2, ".", $thousands_sep = ","), asDollars($gtpcost, 2, ".", $thousands_sep = ",")
                );
            }
            if ($profitboost == "yes") {
                $alldataArr[] = asDollars(($rocount > 0) ? ($ttlPPH / $rocount) : 0);
            }
            $alldata[] = $alldataArr;
            ?>
            </tbody>
        </table>
        <?php
        }
        ?>
    </div>
    <!--now get the part sales  -->
</main>

    <?php
        $dateRangeDisplay = ($_REQUEST['sdate'] ?? "") . " " . (!empty($_REQUEST['edate']) ? " to " . $_REQUEST['edate'] : "");
        $subtitle = !empty($subtitle) ? $subtitle : "";
    ?>
    <script>
        $(document).ready(function () {

            let CalcT = Array(4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15);

            let floatVal = function (i) {
                return typeof i === 'string' ? Number(i.replace(/[$,]/g, '')) : typeof i === 'number' ? i : 0;
            };

            groupCol_writer = 0;
            let table = $(".spd_report").DataTable({
                responsive: true,
                fixedHeader: {
                    headerOffset: 68
                },
                colReorder: true,
                select: true,
                scrollY: false,
                scrollX: false,
                scroller: false,
                paging: false,
                language: {
                    searchPanes: {
                        emptyPanes: 'No records found in this date range'
                    },
                    search: "_INPUT_",
                    searchPlaceholder: "Search..."
                },
                buttons: [
                    {
                        extend: 'csv',
                        text: '<i class="fas fa-file-csv fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                        messageTop : "<?= $dateRangeDisplay ?>",
                    },
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint." (".$dateRangeDisplay.")" ?>",
                        messageTop : "<?= $subtitle ?>",
                        exportOptions: {
                            // Any other settings used
                            columns: [':visible'],
                            grouped_array_index: 0
                        },
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print fa-xl"></i>',
                        title: "<?= (!empty($title) ? $title : 'Shop Boss Report') . $companyNamePrint ?>",
                        exportOptions: {
                            // Any other settings used
                            stripHtml: false,
                            columns: [':visible'],
                            grouped_array_index: 0
                        },
                        messageTop : "<?= $dateRangeDisplay."<br/>".$subtitle ?>",
                        footer: true,
                    }
                ],
                dom: "<'row'<'col-sm-12 col-md-6 dt_Search text-secondary'f><'col-sm-12 col-md-6 text-right dt_btns text-secondary'B>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
                columnDefs: [{visible: false, targets: groupCol_writer}],
                order: [[groupCol_writer, 'asc']],
                orderFixed: [0, 'desc'],
                rowGroup: {
                    endRender: function (rows, group) {

                        if (group == "Grand Totals"){
                            return;
                        }
                        let totsArr = Array();

                        CalcT.forEach(function (val, idx) {
                            totsArr[val] = rows
                                .data()
                                .pluck(val)
                                .reduce(function (a, b) {
                                    return floatVal(a) + floatVal(b);
                                }, 0);
                        });


                        let formatting_options = {
                            style: 'currency',
                            currency: 'USD',
                            minimumFractionDigits: 2,
                        };
                        let USDollar = new Intl.NumberFormat('en-US', formatting_options);
                        var gname = group.replace(" ", "_");
                        var totals = $('<tr id="'+gname+'_totals" />')
                            .append('<td colspan="3" class="text-left">' + group + ' Totals</td>')
                            .append('<td class="text-left">' + USDollar.format(totsArr[4]) + '</td>')
                            .append('<td class="text-left">' + USDollar.format(totsArr[5]) + '</td>')
                            .append('<td class="text-left">' + USDollar.format(totsArr[6]) + '</td>')
                            .append('<td class="text-left">' + USDollar.format(totsArr[7]) + '</td>')
                            .append('<td class="text-left">' + USDollar.format(totsArr[8]) + '</td>')
                            .append('<td class="text-left">' + USDollar.format(totsArr[9]) + '</td>')
                            .append('<td class="text-left">' + USDollar.format(totsArr[10]) + '</td>')
                            .append('<td class="text-left">' + USDollar.format(totsArr[11]) + '</td>')
                            .append('<td class="text-left">' + USDollar.format(totsArr[12]) + '</td>')
                            .append('<td class="text-left">' + USDollar.format(totsArr[13]) + '</td>')
                            .append('<td class="text-left">' + USDollar.format(totsArr[14]) + '</td>');

                        if ( "<?= $profitboost ?>" == "yes" ){
                            totals.append('<td class="text-left">' + USDollar.format(totsArr[15]) + '</td>');
                            totals.append('<td class="text-left">' + "<?= asDollars(($rocount > 0) ? ($ttlPPH / $rocount) : 0) ?>" + '</td>');
                        }

                        return totals;
                    },
                    dataSrc: 0,
                    endClassName: 'table_total text-right',
                    startClassName: 'group_heading'
                },

            });



            $('#wip_adv_table').on('click', 'tr.group', function () {
                var currentOrder = table.order()[0];
                if (currentOrder[0] === groupColumn && currentOrder[1] === 'asc') {
                    table.order([groupColumn, 'desc']).draw();
                } else {
                    table.order([groupColumn, 'asc']).draw();
                }
            });
        });

        function showRO(roid) {
            t = "View RO"
            p = "<?= COMPONENTS_PRIVATE ?>/v2/ro/ro.php?roid=" + roid
            eModal.iframe({
                title: t,
                url: p,
                size: eModal.size.xl,
                buttons: [
                    //   {text: 'Close', style: 'secondary', close: true}
                ]
            });

        }
    </script>
    <script language="javascript">
        function showRO(roid) {
            t = "View RO"
            p = "<?= COMPONENTS_PRIVATE ?>/ro/roclosed.php?roid=" + roid
            eModal.iframe({
                title: t,
                url: p,
                size: eModal.size.xl,
                buttons: [
                    {text: 'Close', style: 'warning', close: true}
                ]
            });

        }
    </script>
    <?php

}

include getScriptsGlobal($component);
include getFooterComponent($component);
?>
