<?php
$redirect = $_REQUEST['red'] ?? "";
echo "GET ACCESS TOKEN<br />";


$oec_base_uri = "https://api-qa.oeconnection.com";
$rl_base_uri = "https://oecqa.repairlinkshop.com";

$client_id = "XARjl4M0GH3CG90x4ATxxUSCcCJmPBqC";
$client_secret = "oJ0psnqJhxKurX8j";

$headers = array(
    'Authorization: Basic ' . base64_encode($client_id . ':' . $client_secret),
    'Content-Type: application/x-www-form-urlencoded'
);

$data = http_build_query(array(
    'grant_type' => 'client_credentials',
    'uid' => 'AhmedAmmar'
));

echo $oec_base_uri . " /oauth2/accesstoken<br>";

$ch = curl_init($oec_base_uri . "/oauth2/accesstoken");
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_ENCODING, '');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
//curl_setopt($ch, CURLOPT_NOPROGRESS, false);
$http_status = curl_getinfo($ch);

if (curl_errno($ch)) {
    echo "ERROR : " . curl_error($ch);
    throw new Exception(curl_error($ch), curl_errno($ch));
}

$result = curl_exec($ch);
echo "<br>Result <br>";
echo $result;
$jsonr = json_decode($result);

$access_token = $jsonr->access_token;

echo "<br><br> Access Token : ".$access_token."<br />";


$headers = array(
    'Authorization: Bearer ' . $access_token,
    'Accept: application/json'
);

$data = json_encode(array("vin" => "1FMZU32P9XUB91617"));

echo $rl_base_uri . "/punchout/login<br>";

$ch = curl_init($oec_base_uri . "/punchout/login");
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_ENCODING, '');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
//curl_setopt($ch, CURLOPT_NOPROGRESS, false);
$http_status = curl_getinfo($ch);

if (curl_errno($ch)) {
    echo "ERROR : " . curl_error($ch);
    throw new Exception(curl_error($ch), curl_errno($ch));
}

$result = curl_exec($ch);
echo "<br>Result <br>";
echo $result;
//print_r($http_status);

/*
 include_once "repair_link.php";
$rl = new repair_link();
$res = $rl->get_access_token('AhmedAmmar');

if (!empty($res)) {
    if (!empty($res->access_token)) {
        $accessToken = $res->access_token;
        echo "Access Token : " . $accessToken;
        echo "<br />";
        //sleep(3);

        if(!empty($redirect)){
            header("Location:".$redirect."?at=".$accessToken);
        }
    }
}
*/
