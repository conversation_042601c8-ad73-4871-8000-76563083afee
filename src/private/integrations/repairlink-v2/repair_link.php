<?php
include_once CONN;
include_once "CURL_API.php";

class repair_link extends CURL_API
{

    public $oec_base_uri;
    public $rl_base_uri;
    public $login_url;
    public $punchOut_url;
    public $logout_url;

    public $shopid;

    private $logger = true;

    public $livemode = false;

    private $conn;
    /*
     * LIVE API KEYS
     */
    //  public $oec_base_uri = "https://api.oeconnection.com";
    //  public $rl_base_uri = "https://www.repairlinkshop.com";

    private $client_id = "XARjl4M0GH3CG90x4ATxxUSCcCJmPBqC";
    private $client_secret = "oJ0psnqJhxKurX8j";

    private string $BearerToken;

    private $redirect_uri = INTEGRATIONS . "/repairlink/return.php";


    function __construct()
    {
        global $conn;
        $this->conn = $conn;
        if (stripos($_SERVER['SERVER_NAME'], 'staging') === false) {
            $this->livemode();
        } else {
            $this->sandboxmode();
        }
        parent::__construct($this->oec_base_uri);
        if (!empty($_COOKIE['shopid'])){
            $this->shopid = $_COOKIE['shopid'];
        } else {
            $this->shopid = "";
        }
    }

    function livemode()
    {
        $this->oec_base_uri = "https://api.oeconnection.com";
        $this->rl_base_uri = "https://www.repairlinkshop.com";
        $this->login_url = "https://www.repairlinkshop.com/punchout/browserlogin";
        $this->punchOut_url = "https://www.repairlinkshop.com/punchout";
        $this->logout_url = "https://www.repairlinkshop.com/punchout/logout";
        $this->livemode = true;
    }

    function sandboxmode()
    {
        $this->oec_base_uri = "https://api-qa.oeconnection.com";
        $this->rl_base_uri = "https://oecqa.repairlinkshop.com";
        $this->login_url = "https://oecqa.repairlinkshop.com/punchout/browserlogin";
        $this->punchOut_url = "https://oecqa.repairlinkshop.com/punchout";
        $this->logout_url = "https://oecqa.repairlinkshop.com/punchout/logout";
        $this->livemode = false;
    }

    /**
     * @return string
     */
    public function getBearerToken(): string
    {
        return $this->BearerToken;
    }

    /**
     * @param string $BearerToken
     */
    public function setBearerToken(string $BearerToken): void
    {
        $this->BearerToken = $BearerToken;
    }

    public function get_oauth2_url()
    {
        $qs = http_build_query(array(
            'reponse_type' => 'code',
            'scope' => 'rls_api',
            'client_id' => $this->client_id,
            'redirect_uri' => urlencode($this->redirect_uri)
        ));
        return $this->oec_base_uri . "/oauth2/authorize?" . $qs;
    }

    public function get_oauth2_token($code)
    {
        $this->setHeaders(array(
            'Content-Type: application/x-www-form-urlencoded',
            'Authorization: Basic ' . base64_encode($this->client_id . ':' . $this->client_secret),
        ));
        $postData = http_build_query(array(
            'grant_type' => 'authorization_code',
            'code' => $code,
            'redirect_uri' => urlencode($this->redirect_uri)
        ));
        return $this->post('/oauth2/accesstoken', $postData);
    }

    public function get_access_token($uid, $code = "")
    {
        $this->setHeaders(array(
            'Authorization: Basic ' . base64_encode($this->client_id . ':' . $this->client_secret),
            'Content-Type: application/x-www-form-urlencoded',
        ));
        $postData = http_build_query(array(
            'grant_type' => 'client_credentials',
            'uid' => $uid
        ));
        return $this->post('/oauth2/accesstoken', $postData);
    }

    function punch_out_login_red($vehicle_info = array())
    {
        $headers = array(
            'Authorization: Bearer ' . $this->BearerToken,
            'Accept: application/json',
            "Content-Type: application/json"
            //       'Content-Length: '.strlen(json_encode($vehicle_info))
        );
        $this->setHeaders($headers);
        $this->setBaseUri($this->rl_base_uri);

        //  return $this->post('/punchout/login', json_encode($vehicle_info));
        return $this->get_redirect('/punchout/login', json_encode($vehicle_info), $headers, 'POST');
    }

    function punch_out()
    {
        $headers = array(
            'Authorization: Bearer ' . $this->BearerToken,
            'Accept: application/json'
        );
        $this->setHeaders($headers);
        $this->setBaseUri($this->rl_base_uri);
        return $this->get('/punchout/');
    }

    function get_uid($shopid)
    {
        $stmt = "SELECT uid FROM repairlinkshops WHERE shopid = ?";

        if ($query = $this->conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $query->bind_result($uid);
            $query->fetch();
            $query->close();
        }
        if (!empty($uid)) {
            return $uid;
        }
        return false;
    }

    function get_user($shopid)
    {
        $stmt = "SELECT id, uid, access_token FROM repairlinkshops WHERE shopid = ?";

        if ($query = $this->conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $results = $query->get_result();
            $query->close();
        }
        if ($results->num_rows > 0) {
            return $results->fetch_assoc();
        }
        return false;
    }

    function add_creds($uid, $pwd, $shopid)
    {
        if (!$this->get_uid($shopid)) {
            $stmt = "INSERT INTO repairlinkshops (shopid, uid, password) VALUES (?, ?, ?)";
            if ($query = $this->conn->prepare($stmt)) {
                $query->bind_param("sss", $shopid, $uid, $pwd);
                if ($query->execute()) {
                    $this->conn->commit();
                } else {
                    return $this->conn->error;
                }
                $query->close();
            }
        } else {
            $stmt = "UPDATE repairlinkshops SET uid = ?, password = ? WHERE shopid = ?";
            if ($query = $this->conn->prepare($stmt)) {
                $query->bind_param("sss", $uid, $pwd, $shopid);
                if ($query->execute()) {
                    $this->conn->commit();
                } else {
                    return $this->conn->error;
                }
                $query->close();
            }
        }
        return true;
    }

    function del_creds($id)
    {

        $stmt = "DELETE FROM repairlinkshops WHERE id = ?";
        if ($query = $this->conn->prepare($stmt)) {
            $query->bind_param("i", $id);
            if ($query->execute()) {
                $this->conn->commit();
            } else {
                return false;
            }
            $query->close();
        }

        return true;
    }
    function save_token($shopid, $token)
    {
        $stmt = "UPDATE repairlinkshops SET access_token = ? WHERE shopid = ?";
        if ($query = $this->conn->prepare($stmt)) {
            $query->bind_param("ss", $token, $shopid);
            if ($query->execute()) {
                $this->conn->commit();
            } else {
                return $this->conn->error;
            }
            $query->close();
        }
        return true;
    }

    function retrieve_token($shopid)
    {
        $token = false;
        $stmt = "SELECT uid, access_token FROM repairlinkshops WHERE shopid = ?";
        if ($query = $this->conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $query->bind_result($uid, $token);
            $query->fetch();
            $query->close();
        }
        if (empty($token)) {
            $token = $this->get_access_token($uid);
        }
        return $token;
    }

    function add_part($shopid, $roid, $quoteid, $partnumber, $qty, $desc, $cost, $price, $core, $dealer, $cartid, $manufacturer, $vendorid, $oemid, $taxable = 'yes', $transferstatus = 'not', $orderstatus = 'not')
    {
        $stmt = "INSERT INTO repairlinkreturn (shopid, roid, quoteid, qty, `desc`, cost, price, core, partnumber, dealer, cartid, transferstatus, orderstatus, manufacturer, taxable, vendorid, oemid) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        if ($query = $this->conn->prepare($stmt)) {
            $query->bind_param("siidsdddsssssssss", $shopid, $roid, $quoteid, $qty, $desc, $cost, $price, $core, $partnumber, $dealer, $cartid, $transferstatus, $orderstatus, $manufacturer, $taxable, $vendorid, $oemid);
            if ($query->execute()) {
                $this->conn->commit();
            } else {
                return false;
            }
            $query->close();
        }
        return true;
    }

    function update_part($shopid, $roid, $quoteid, $partnumber, $qty, $cost, $price, $core)
    {
        $stmt = "UPDATE repairlinkreturn SET qty = ?, cost = ?, price = ?, core = ?, transferstatus = 'not', orderstatus = 'not' WHERE shopid = ? AND roid = ? AND quoteid = ? AND partnumber = ?";
        if ($query = $this->conn->prepare($stmt)) {
            $query->bind_param("ddddsiis", $qty, $cost, $price, $core, $shopid, $roid, $quoteid, $partnumber);
            if ($query->execute()) {
                $this->conn->commit();
            } else {
                return false;
            }
            $query->close();
        }
    }

    function mark_transfered($shopid, $partnumber, $roid, $quoteid, $cartid = '')
    {
        $stmt = "UPDATE repairlinkreturn SET transferstatus = 'transferred', cartid = ? WHERE shopid = ? AND partnumber = ? AND roid = ? AND quoteid = ?";
        if ($query = $this->conn->prepare($stmt)) {
            $query->bind_param("sssii", $cartid, $shopid, $partnumber, $roid, $quoteid);
            if ($query->execute()) {
                $this->conn->commit();
                return $this->conn->insert_id;
            }
            $query->close();
        }
        return false;
    }

    function mark_ordered($shopid, $partnumber, $roid, $quoteid, $transaction_id)
    {

        $stmt = "UPDATE repairlinkreturn SET orderstatus = 'ordered', transferstatus = 'transferred', transaction_id = ? WHERE shopid = ? AND partnumber = ? AND roid = ? AND quoteid = ?";
        if ($query = $this->conn->prepare($stmt)) {
            $query->bind_param("sssii", $transaction_id, $shopid, $partnumber, $roid, $quoteid);
            if ($query->execute()) {
                $this->conn->commit();
                return $this->conn->affected_rows;
            }
            $query->close();
        }
        return false;
    }

    function get_parts_by_entity($shopid, $roid, $quoteid)
    {

        $stmt = "SELECT id, partnumber FROM repairlinkreturn WHERE shopid = ? AND quoteid = ? AND roid = ?";
        $parts = array();
        if ($query = $this->conn->prepare($stmt)) {
            $query->bind_param("sii", $shopid, $quoteid, $roid);
            $query->execute();
            $query->bind_result($id, $partnumber);
            while ($query->fetch()) {
                $parts[] = $partnumber;
            }
            $query->close();
        }

        return $parts;
    }

    function get_cart_id($shopid, $roid, $quoteid)
    {
        $stmt = "SELECT cartid as cart_id FROM repairlinkreturn WHERE shopid = ? AND quoteid = ? AND roid = ? AND transferstatus = 'transferred' AND orderstatus = 'not' AND cartid != '' order by ts desc limit 1";
        $carts = array();
        if ($query = $this->conn->prepare($stmt)) {
            $query->bind_param("sii", $shopid, $quoteid, $roid);
            $query->execute();
            $query->bind_result($cartid);
            $query->fetch();
            $query->close();
        }

        if (!empty($cartid)) {
            //check if the cart exists in repairlink
            $ret = $this->update_cart($cartid, " ", array());
            if (!isset($ret->LineCount)) {
                return false;
            }
        }

        return $cartid;
    }

    function create_cart($vendorId, $poNUmber, $Note, $oemId = "", $vehicle = array(), $parts = array())
    {

        $this->setBaseUri($this->oec_base_uri);

        $json = array(
            "parts" => $parts,
            "vehicle" => $vehicle,
            "OEMId" => "$oemId",
            "vendorid" => $vendorId,
            "PurchaseOrderNumber" => $poNUmber,
            "Note" => $Note
        );
        $json = json_encode($json);
        $headers = array(
            'Authorization: Bearer ' . $this->BearerToken,
            'Accept: application/json',
            "Content-Type: application/json"
            //       'Content-Length: '.strlen(json_encode($vehicle_info))
        );
        $this->setHeaders($headers);
        $ret = $this->post("/rls/cart", $json);
        if (!empty($ret->CartId)) {
            $cart_id = $ret->CartId;
        }

        $this->log($this->shopid, "POST /rls/cart", $json, json_encode($headers), json_encode($ret));

        return $cart_id ?? false;
    }

    function get_cart($cartid)
    {
        $this->setBaseUri($this->oec_base_uri);

        $headers = array(
            'Authorization: Bearer ' . $this->BearerToken,
            'Accept: application/json',
            "Content-Type: application/json"
            //       'Content-Length: '.strlen(json_encode($vehicle_info))
        );
        $this->setHeaders($headers);

        return $this->get("/rls/cart/count?cartid=" . $cartid);
    }

    function update_cart($cartId, $Note = "", $parts = array())
    {

        $this->setBaseUri($this->oec_base_uri);

        $json = array(
            "CartId" => $cartId,
            "Parts" => $parts,
            "Note" => $Note
        );
        $json = json_encode($json);

        $headers = array(
            'Authorization: Bearer ' . $this->BearerToken,
            'Accept: application/json',
            "Content-Type: application/json"
            //       'Content-Length: '.strlen(json_encode($vehicle_info))
        );
        $this->setHeaders($headers);

        $ret = $this->patch("/rls/cart", $json);

        $this->log($this->shopid, "PATCH /rls/cart", $json, json_encode($headers), json_encode($ret));

        return $ret;
    }

    function create_order($vendorId, $poNUmber, $Note, $oemId = "", $vehicle = array(), $parts = array())
    {

        $this->setBaseUri($this->oec_base_uri);

        $json = array(
            "Parts" => $parts,
            "vehicle" => $vehicle,
            "OEMId" => "$oemId",
            "vendorid" => $vendorId,
            "PurchaseOrderNumber" => $poNUmber,
            "Note" => $Note
        );
        $json = json_encode($json);

        $headers = array(
            'Authorization: Bearer ' . $this->BearerToken,
            'Accept: application/json',
            "Content-Type: application/json"
            //       'Content-Length: '.strlen(json_encode($vehicle_info))
        );
        $this->setHeaders($headers);

        $ret = $this->post("/rls/order", $json);

        if (!empty($ret->TransactionNumber)) {
            $transaction_id = $ret->TransactionNumber;
        }

        $this->log($this->shopid, "POST /rls/order", $json, json_encode($headers), json_encode($ret));

        return $transaction_id ?? false;
    }

    function log($shopid, $endpoint, $payload = "", $header = '', $output = ''){

        if(!$this->logger){
            return false;
        }

        $stmt = "INSERT INTO repairlinklogs (shopid, endpoint, payload, headers, output) VALUES (?, ?, ?, ?, ?)";
        if ($query = $this->conn->prepare($stmt)){
            $query->bind_param("sssss", $shopid, $endpoint, $payload, $header, $output);
            if ($query->execute()) {
                $this->conn->commit();
            } else {
                return $this->conn->error;
            }
            $query->close();
        }
        return true;
    }

}

/*
 *
CREATE TABLE `repairlinkshops` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shopid` varchar(45) NOT NULL,
  `uid` varchar(45) NOT NULL,
  `access_token` varchar(45) DEFAULT NULL,
  `ts` datetime DEFAULT NULL COMMENT 'Table to save repair link information and access token. ',
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `shopid` (`shopid`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;


 */

/*
CREATE TABLE `repairlinkreturn` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `shopid` varchar(10) NOT NULL DEFAULT '',
  `roid` int(11) NOT NULL DEFAULT '0',
  `quoteid` int(11) NOT NULL DEFAULT '0',
  `qty` double NOT NULL DEFAULT '0',
  `desc` varchar(255) NOT NULL DEFAULT '',
  `cost` double NOT NULL DEFAULT '0',
  `price` double NOT NULL DEFAULT '0',
  `core` double NOT NULL DEFAULT '0',
  `partnumber` varchar(255) NOT NULL DEFAULT '',
  `dealer` varchar(20) NOT NULL DEFAULT '',
  `cartid` varchar(50) NOT NULL DEFAULT '',
  `transferstatus` varchar(20) NOT NULL DEFAULT 'not',
  `orderstatus` varchar(20) NOT NULL DEFAULT 'not',
  `manufacturer` varchar(50) NOT NULL DEFAULT '',
  `taxable` varchar(10) DEFAULT 'yes',
  `ts` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `vendorid` varchar(45) DEFAULT '',
  `oemid` int(11) DEFAULT '0',
  `transaction_id` varchar(45) DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `shopid` (`shopid`),
  KEY `roid` (`roid`),
  KEY `ts` (`ts`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8;


*/