<?php
include INTEGRATIONS_PATH . "/repairlink/repair_link.php";

if (!empty($_POST['data'])) {

    $shopid = $_COOKIE['shopid'];
    $entity_id = $_REQUEST['entity_id'];
    $ptype = $_REQUEST['type'] ?? 'part';
    $version = $_REQUEST['version'] ?? '';
    $data = $_REQUEST['data'];
    $entity = isset($_REQUEST['entity']) ? filter_var($_REQUEST['entity'], FILTER_SANITIZE_STRING) : "";

    if (!empty($data)) {
        $data = json_decode($data, true);
    } else {
        echo "No repairlink data";
    }

    $rl = new repair_link();
    $accessToken = $rl->retrieve_token($shopid);
    $rl->setBearerToken($accessToken);
    $ev = "";
    $roid = $quoteid = 0;
    if ($entity == "quotes") {
        $quoteid = $entity_id;
        $ev = "QUOTE";
    } else {
        $roid = $entity_id;
        $ev = "RO";
    }

    $partsArr = array();
    $vehArr = array();

    if (!empty($data['Vehicle']['VIN'])) {
        $vehArr = array("VIN" => $data['Vehicle']['VIN']);
    } else {
        $vehArr = array(
            "Make" => $data['Vehicle']['Make'],
            "Model" => $data['Vehicle']['Model'],
            "Year" => $data['Vehicle']['Year']
        );
    }
    $cart_id = '';
    /*
    $ret = $rl->create_cart($data['Dealer']['VendorId'], $ev . " # " . $entity_id, $ev . " # " . $entity_id, $oemID, $vehArr, $partsArr);
    //$ret = $rl->create_cart("107990", "PO:850", "TST QUOTES # 15 11:00 PM", '', $vehicle, $parts);
    if (!empty($ret->CartId)) {
        $cart_id = $ret->CartId;
    }
    */

    $parts = $rl->get_parts_by_entity($shopid, $roid, $quoteid);

    $supplier = strtoupper($data['Dealer']['Name']);
    $vendorid = $data['Dealer']['VendorId'];
    $oemID = (empty($data['Dealer']['OEMId']) ? "" : $data['Dealer']['OEMId']);

    foreach ($data['CartItems'] as $idx => $cartItem) {
        //  $sessionid = $row['ptsessionid'];
        $pq = $cartItem['QuantityRequested'];
        $pd = $cartItem['Description'];
        /*
       $pc = $cartItem['ListPrice'];
       $pp = $cartItem['SalePrice'];
       */
        $pc = $cartItem['SalePrice'];
        $pp = $cartItem['ListPrice'];

        $pn = $cartItem['PartNumber'];
        $manufacturer = $cartItem['ManufacturerName'];
        $pcore = $cartItem['CorePrice'];
        $cartItem['taxable'] = 'no';
        if ($pc <= 0 && $pp <= 0){
            continue;
        }
        if (!empty($parts) && in_array($pn, $parts)) {
            $rl->update_part($shopid, $roid, $quoteid, $pn, $pq, $pc, $pp, $pcore);
        } else {
           $rl->add_part($shopid, $roid, $quoteid, $pn, $pq, $pd, $pc, $pp, $pcore, $supplier, $cart_id, $manufacturer, $vendorid, $oemID);
        }

        $partsArr[] = array("PartNumber" => $pn, "QuantityRequested" => $pq);

    }
    /*
    $json = array(
        "parts" => $partsArr,
        "vehicle" => $vehArr,
        "OEMId" => (empty($data['Dealer']['OEMId']) ? "" : $data['Dealer']['OEMId']),
        "vendorid" => $data['Dealer']['VendorId'],
        "PurchaseOrderNumber" => $ev . " # " . $entity_id,
        "Note" => $ev . " # " . $entity_id
    );
    */

} else {
    echo "No data posted";
}