<?php
/*
 * @var sbp_bucket $sbp_bucket
 */
//ini_set("display_errors", "1");
//error_reporting(E_ALL);

require "aws-sdk/vendor/autoload.php";

use Aws\S3\S3Client;
use Aws\Exception\AwsException;

class sbp_bucket
{
    private $s3Client;

    private $bucket = 'customers-ss';
    private $region = 'us-east-1';

    const public_url = "https://customers-ss.s3.amazonaws.com/";

    public function __construct()
    {
        $this->s3Client = new S3Client([
            'region' => $this->region,
            'version' => 'latest',
            'credentials' => [
                'key' => '********************',
                'secret' => 'drORMYs2mxr3FG0rWM3xJLtSJ2TONEkt8pg549U7',
            ]
        ]);
    }


    public function add_file_from_path($file_path, $file_name = "", $remove_src_file = true, $ACL = 'public-read')
    {
      //  $file_name .= "/" . basename($file_path);
        try {
            $result = $this->s3Client->putObject([
                'Bucket' => $this->bucket,
                'Key' => $file_name,
                'SourceFile' => $file_path,
                'ACL'    => $ACL,
            ]);
            if ($remove_src_file) {
                unlink($file_path);
            }
            $url = $result['ObjectURL'];
            return $url;
        } catch (Exception $exception) {
            echo "Failed to upload $file_name with error: " . $exception->getMessage();
            return false;
        }
    }

    public function copy_file($source, $target, $ACL = 'public-read')
    {
        try {
            $result = $this->s3Client->copyObject([
                'Bucket' => $this->bucket,
                'CopySource' => '/' . $this->bucket . '/' . $source,
                //'/' . $sourceBucket . '/' . $sourceKey,
                'Key' => $target,
                'ACL'    => $ACL,
            ]);

            $url = $result['ObjectURL'];
            return $url;
        } catch (Exception $e) {
            echo 'Error copying image: ' . $e->getMessage()."<br />";
            echo "From source : $source , $target";
        }
    }

    public function add_file($body, $file_name, $content_type = 'application/pdf', $ACL = 'public-read')
    {
        try {
            $result = $this->s3Client->putObject(array(
                'Bucket' => $this->bucket,
                'Key' => $file_name,
                'Body' => $body,
                'ContentType' => $content_type,
                'ACL' => $ACL

            ));
            $url = $result['ObjectURL'];
            return $url;
        } catch (Exception $exception) {
            echo "Failed to upload $file_name with error: " . $exception->getMessage();
            return false;
        }
    }

    public function remove_file($file_name)
    {
        try {
            $result = $this->s3Client->deleteObject(array(
                'Bucket' => $this->bucket,
                'Key' => $file_name,

            ));
            return $result;
        } catch (Exception $exception) {
            echo "Failed to upload $file_name with error: " . $exception->getMessage();
            return false;
        }
    }

    function list_files()
    {
        try {
            $contents = $this->s3Client->listObjects([
                'Bucket' => $this->bucket,
            ]);
            echo "The contents of your bucket are: \n";
            foreach ($contents['Contents'] as $content) {
                echo $content['Key'] . "\n";
            }
        } catch (Exception $exception) {
            echo "Failed with error: " . $exception->getMessage();
            exit("Please fix error with listing objects before continuing.");
        }
    }

    function get_files_by_timestamp($path, $shopid)
    {
        $shop_timezone = $this->get_shop_timezone($shopid);
        date_default_timezone_set($shop_timezone);
        try {
            $objects = $this->s3Client->getIterator('ListObjects', array(
                "Bucket" => $this->bucket,
                "Prefix" => $path,
                'region' => $this->region,
            ));
        } catch (Exception $exception) {
            echo $exception->getMessage();
            return false;
        }

        $obj_arr = array();
        foreach ($objects as $object) {
            $timestamp = $object['LastModified']->setTimezone(new DateTimeZone($shop_timezone))->getTimestamp();
           // $obj_arr[$timestamp] = INTEGRATIONS."/sbp_bucket/presign.php?key=". $object['Key'];
            $obj_arr[$timestamp] = INTEGRATIONS."/sbp_bucket/file.php?pdf=true&key=". $object['Key'];
        }
        return $obj_arr;
    }

    function get_files($path)
    {
        try {
            $objects = $this->s3Client->getIterator('ListObjects', array(
                "Bucket" => $this->bucket,
                "Prefix" => $path,
                'region' => $this->region,
            ));
        } catch (Exception $exception) {
            echo $exception->getMessage();
            return false;
        }

        $obj_arr = array();
        foreach ($objects as $object) {
            $obj_arr[] = $object;
        }
        return $obj_arr;
    }

    function get_file_body($filename)
    {
        try {
            $file = $this->s3Client->getObject([
                'Bucket' => $this->bucket,
                'Key' => $filename,
            ]);
            $body = $file->get('Body');
            return $body;
        } catch (Exception $exception) {
            echo "Failed to download $filename with error: " . $exception->getMessage();
            exit("Please fix error with file downloading before continuing.");
        }

    }

    function presigned_url($filename, $expiresin = 20)
    {
        $cmd = $this->s3Client->getCommand('GetObject', [
            'Bucket' => $this->bucket,
            'Key' => $filename
        ]);

        $request = $this->s3Client->createPresignedRequest($cmd, '+' . $expiresin . ' minutes');

// Get the actual presigned-url
        $presignedUrl = (string)$request->getUri();

        return $presignedUrl;
    }

    function get_shop_timezone($shopid)
    {
        global $conn;

        $timestmt = "select timezone from company where shopid = ?";
        if ($timequery = $conn->prepare($timestmt)) {

            $timequery->bind_param("s", $shopid);
            $timequery->execute();
            $timequery->store_result();
            $timenum_roid_rows = $timequery->num_rows;
            if ($timenum_roid_rows > 0) {
                $timequery->bind_result($ttz);
                $timequery->fetch();
            } else {
                $tz = "Unknown";
            }
            $timequery->close();
        } else {
            echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
        }

        switch ($ttz) {
            case "est":
                $phptz = 'America/New_York';
                break;
            case "mst":
                $phptz = 'America/Denver';
                break;
            case "pst":
                $phptz = 'America/Los_Angeles';
                break;
            case "cst":
                $phptz = 'America/Chicago';
                break;
            case "azst":
                $phptz = 'America/Phoenix';
                break;
            case "hst":
                $phptz = 'Pacific/Honolulu';
                break;
            case "akst":
                $phptz = 'America/Anchorage';
                break;
            case "gst":
                $phptz = 'Pacific/Guam';
                break;
            case "ast":
                $phptz = 'America/Halifax';
                break;
            case "astnd":
                $phptz = 'America/Blanc-Sablon';
                break;
            case "eat":
                $phptz = 'Africa/Addis_Ababa';
                break;
            case "chst":
                $phptz = 'Pacific/Guam';
                break;
            default:
                $phptz = "unknown";

        }
        return $phptz;
    }


}

$sbp_bucket = new sbp_bucket();


/*
$path = "\\\\fs.shopboss.aws\\share\\savedinvoices\\6062\\1551\\6062_1551_2020-05-19_10-54-46.pdf";
$file_name = basename($path);
$sbp_bucket->add_file('6062/invoices/1551/' . $file_name, $path);
*/