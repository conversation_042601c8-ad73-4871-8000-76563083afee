<?php


// Global Rules
function getRulesGlobal($component)
{
    if (file_exists(GLOBAL_PAGES . DS . "rules.global.php")) {
        return GLOBAL_PAGES . DS . "rules.global.php";
    }
    return "";
}

// Global Head
function getHeadGlobal($component)
{
    if (file_exists(GLOBAL_PAGES . DS . "head.global.php")) {
        return GLOBAL_PAGES . DS . "head.global.php";
    }
    return "";
}

// Global Menu
function getMenuGlobal($component)
{
    if (file_exists(GLOBAL_PAGES . DS . "menu.global.php")) {
        return GLOBAL_PAGES . DS . "menu.global.php";
    }
    return "";
}

// Global Preheader
function getPreheaderGlobal($component)
{
    if (file_exists(GLOBAL_PAGES . DS . "preheader.global.php")) {
        return GLOBAL_PAGES . DS . "preheader.global.php";
    }
    return "";
}

// Global Body
function getBodyGlobal($component)
{
    if (file_exists(GLOBAL_PAGES . DS . "body.global.php")) {
        return GLOBAL_PAGES . DS . "body.global.php";
    }
    return "";
}

// Global Header
function getHeaderGlobal($component)
{
    if (file_exists(GLOBAL_PAGES . DS . "header.global.php")) {
        return GLOBAL_PAGES . DS . "header.global.php";
    }
    return "";
}

// Global Postheader
function getPostheaderGlobal($component)
{
    if (file_exists(GLOBAL_PAGES . DS . "postheader.global.php")) {
        return GLOBAL_PAGES . DS . "postheader.global.php";
    }
    return "";
}

// Global Maincontent
function getMaincontentGlobal($component)
{
    if (file_exists(GLOBAL_PAGES . DS . "maincontent.global.php")) {
        return GLOBAL_PAGES . DS . "maincontent.global.php";
    }
    return "";
}

// Global Scripts
function getScriptsGlobal($component)
{
    if (file_exists(GLOBAL_PAGES . DS . "scripts.global.php")) {
        return GLOBAL_PAGES . DS . "scripts.global.php";
    }
    return "";
}

// Global Modals
function getModalsGlobal($component)
{
    if (file_exists(GLOBAL_PAGES . DS . "modals.global.php")) {
        return GLOBAL_PAGES . DS . "modals.global.php";
    }
    return "";
}

// Global Functions
function getFunctionsGlobal($component)
{
    if (file_exists(GLOBAL_PAGES . DS . "functions.global.php")) {
        return GLOBAL_PAGES . DS . "functions.global.php";
    }
    return "";
}

// Global Footer
function getFooterGlobal($component)
{
    if (file_exists(GLOBAL_PAGES . DS . "footer.global.php")) {
        return GLOBAL_PAGES . DS . "footer.global.php";
    }
    return "";
}

// Global Style
function getStyleGlobal($component)
{
    if (file_exists(GLOBAL_PAGES . DS . "style.global.php")) {
        return GLOBAL_PAGES . DS . "style.global.php";
    }
    return "";
}


// Components Rules

function getRulesComponent($component)
{
    $component_file = str_replace("-v2", "", $component);
    if (file_exists(SBP_PAGES . DS . $component . DS . "rules." . $component_file . ".php")) {
        return SBP_PAGES . DS . $component . DS . "rules." . $component_file . ".php";
    }
    return "";
}


// Components Head
function getHeadComponent($component)
{
    $component_file = str_replace("-v2", "", $component);
    if (file_exists(SBP_PAGES . DS . $component . DS . "head." . $component_file . ".php")) {
        return SBP_PAGES . DS . $component . DS . "head." . $component_file . ".php";
    }
    return "";
}

// Components Menu

function getMenuComponent($component)
{
    $component_file = str_replace("-v2", "", $component);
    if (file_exists(SBP_PAGES . DS . $component . DS . "menu." . $component_file . ".php")) {
        return SBP_PAGES . DS . $component . DS . "menu." . $component_file . ".php";
    }
    return "";
}

// Components Preheader

function getPreheaderComponent($component)
{
    $component_file = str_replace("-v2", "", $component);
    if (file_exists(SBP_PAGES . DS . $component . DS . "preheader." . $component_file . ".php")) {
        return SBP_PAGES . DS . $component . DS . "preheader." . $component_file . ".php";
    }
    return "";
}

// Components Body

function getBodyComponent($component)
{
    $component_file = str_replace("-v2", "", $component);
    if (file_exists(SBP_PAGES . DS . $component . DS . "body." . $component_file . ".php")) {
        return SBP_PAGES . DS . $component . DS . "body." . $component_file . ".php";
    }
    return "";
}

// Components Header

function getHeaderComponent($component)
{
    $component_file = str_replace("-v2", "", $component);
    if (file_exists(SBP_PAGES . DS . $component . DS . "header." . $component_file . ".php")) {
        return SBP_PAGES . DS . $component . DS . "header." . $component_file . ".php";
    }
    return "";
}

// Components Postheader

function getPostheaderComponent($component)
{
    $component_file = str_replace("-v2", "", $component);
    if (file_exists(SBP_PAGES . DS . $component . DS . "postheader." . $component_file . ".php")) {
        return SBP_PAGES . DS . $component . DS . "postheader." . $component_file . ".php";
    }
    return "";
}

// Components Maincontent

function getMaincontentComponent($component)
{
    $component_file = str_replace("-v2", "", $component);
    if (file_exists(SBP_PAGES . DS . $component . DS . $component_file . ".php")) {
        return SBP_PAGES . DS . $component . DS . $component_file . ".php";
    }
    return "";
}

// Components Scripts

function getScriptsComponent($component)
{
    $component_file = str_replace("-v2", "", $component);
    if (file_exists(SBP_PAGES . DS . $component . DS . "scripts." . $component_file . ".php")) {
        return SBP_PAGES . DS . $component . DS . "scripts." . $component_file . ".php";
    }
    return "";
}

// Components Modals

function getModalsComponent($component)
{
    $component_file = str_replace("-v2", "", $component);
    if (file_exists(SBP_PAGES . DS . $component . DS . "modals." . $component_file . ".php")) {
        return SBP_PAGES . DS . $component . DS . "modals." . $component_file . ".php";
    }
    return "";
}

// Components Functions

function getFunctionsComponent($component)
{
    $component_file = str_replace("-v2", "", $component);
    if (file_exists(SBP_PAGES . DS . $component . DS . "functions." . $component_file . ".php")) {
        return SBP_PAGES . DS . $component . DS . "functions." . $component_file . ".php";
    }
    return "";
}

// Components Footer

function getFooterComponent($component)
{
    $component_file = str_replace("-v2", "", $component);
    if (file_exists(SBP_PAGES . DS . $component . DS . "footer." . $component_file . ".php")) {
        return SBP_PAGES . DS . $component . DS . "footer." . $component_file . ".php";
    }
    return "";
}

// Components Style

function getStyleComponent($component)
{
    $component_file = str_replace("-v2", "", $component);
    if (file_exists(SBP_PAGES . DS . $component . DS . "style." . $component_file . ".php")) {
        return SBP_PAGES . DS . $component . DS . "style." . $component_file . ".php";
    }
    return "";
}


// function get_css() {
//     if (file_exists(GLOBAL_PAGES . DS . "style.global.css")) {
//         $server_name = $_SERVER['SERVER_NAME'];
//         $css_link = "https://{$server_name}/src/private/components/global/style.global.css";
//         echo '<link rel="stylesheet" href="' . $css_link . '" />' . "\n";
//     }
// }

function get_css_bs5()
{
    $server_name = $_SERVER['SERVER_NAME'];
   // $css_link = "https://{$server_name}/src/public/MDB5-UI/css/mdb.min.css";
    $css_link = "https://{$server_name}/src/public/MDB5v6.4/css/mdb.min.css";
    echo '<link rel="preload" href="' . $css_link . '" />' . "\n";
    echo '<link rel="stylesheet" href="' . $css_link . '" />' . "\n";
}

function get_css_plugins_bs5()
{
    $server_name = $_SERVER['SERVER_NAME'];
    //$css_link = "https://{$server_name}/src/public/MDB5-UI/plugins/css/all.min.css";
    $css_link = "https://{$server_name}/src/public/MDB5v6.4/plugins/css/all.min.css";
    echo '<link rel="preload" href="' . $css_link . '" />' . "\n";
    echo '<link rel="stylesheet" href="' . $css_link . '" />' . "\n";
}

function get_js_bs5()
{
    $server_name = $_SERVER['SERVER_NAME'];
    //$js_link = "https://{$server_name}/src/public/MDB5-UI/js/mdb.min.js";
    $js_link = "https://{$server_name}/src/public/MDB5v6.4/js/mdb.min.js";
    echo '<script type="text/javascript" src="' . $js_link . '"></script>' . "\n";

}


function get_js_plugins_bs5()
{

    $server_name = $_SERVER['SERVER_NAME'];
    //$js_link = "https://{$server_name}/src/public/MDB5-UI/plugins/js/all.min.js";
    $js_link = "https://{$server_name}/src/public/MDB5v6.4/plugins/js/all.min.js";
    echo '<script type="text/javascript" src="' . $js_link . '"></script>' . "\n";

}

