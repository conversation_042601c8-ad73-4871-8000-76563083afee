<?php

function getNAME($component = 'wip')
{
    if ($component == 'wip') {
        $component_name = 'Home';
    }
}

function getComponent()
{
    $URI = $_SERVER['REQUEST_URI'];
    $pathinfo = pathinfo($URI);
    $dirname = $pathinfo['dirname'];
    $cpos = strpos($dirname, 'components');
    if ($cpos !== false) {
        // component is called directly, use url part after /component/
        // echo "cpos : ".$cpos;
        $reduced_url = substr($URI, $cpos);
        $dirparts = $exploded = preg_split('@/@', $reduced_url, null, PREG_SPLIT_NO_EMPTY);

        return $dirparts[1];
    } else {
        // component is called indirectly or the function is called incorrectly outside the component. Give out first URL part.
        $dirparts = $exploded = preg_split('@/@', $dirname, null, PREG_SPLIT_NO_EMPTY);
        if ($dirparts[0] == 'v2') {
            $component = $dirparts[1].'-v2';
        } else {
            $component = $dirparts[0];
        }

        return $component;
    }
}

function getPageTitleLogin()
{
    if (stripos($_SERVER['SERVER_NAME'], 'matco') !== false) {
        $brand = 'matco';
        echo 'Matco SMS - Login';
    } elseif (stripos($_SERVER['SERVER_NAME'], 'protractorgo') !== false) {
        $brand = 'protractor';
        echo 'Protractor SMS - Login';
    } else {
        $brand = 'shopboss';
        echo 'Shop Boss SMS - Login';
    }
}

function getPageTitle()
{
    global $sub_component;
    // Check if $component is set
    if (!isset($component)) {
        $component = 'Default Title'; // You can replace "Default Title" with any default value you want
    }
    $title = getComponentTitle($component);

    if (!empty($sub_component)){
        $title .= " : $sub_component";
    }

    echo $title;
}

function getLogoLogin($color = null)
{
    if (stripos($_SERVER['SERVER_NAME'], 'matco') !== false) {
        $brand = 'matco';
    } elseif (stripos($_SERVER['SERVER_NAME'], 'protractorgo') !== false) {
        $brand = 'protractor';
    } else {
        $brand = 'shopboss';
    }

    if (($brand == 'matco') && ($color == null)) {
        echo "<img src='".IMAGE."/matcosms-white.png' width='225'>";
    } elseif (($brand == 'matco') && ($color != null)) {
        echo "<img src='".IMAGE."/matcosms-color.png' width='225'>";
    } elseif (($brand == 'shopboss') && ($color == null)) {
        echo "<img src='".IMAGE."/sblogo-white.png' width='225'>";
    } elseif ($brand == 'protractor') {
        echo "<img src='".IMAGE."/protractor.png'>";
    } else {
        echo "<img src='".IMAGE."/shopboss-logo.png' width='225'>";
    }
}

function getFaviconLogin()
{
    if (stripos($_SERVER['SERVER_NAME'], 'matco') !== false) {
        $brand = 'matco';
    } elseif (stripos($_SERVER['SERVER_NAME'], 'protractorgo') !== false) {
        $brand = 'protractor';
    } else {
        $brand = 'shopboss';
    }

    if ($brand == 'matco') {
        echo 'favicon_matco.ico';
    } elseif ($brand == 'protractor') {
        echo 'favicon_protractor.png';
    } else {
        echo 'favicon_shopboss.ico';
    }
}

function getLogo()
{
    $matco = $_COOKIE['matco'] ?? 'no';
    $protractor = $_COOKIE['protractor'] ?? ' no';

    if ($matco == 'yes') {
        echo "<img src='".IMAGE."/matcosms-white.png' width='150'>";
    } elseif ($protractor == 'yes') {
        echo "<img src='".IMAGE."/protractor.png'>";
    } else {
        echo "<img src='".IMAGE."/sblogo-white.svg' width='190'>";
    }
}

function getLogoUI()
{
    $matco = $_COOKIE['matco'] ?? 'no';
    $protractor = $_COOKIE['protractor'] ?? ' no';

    if ($matco == 'yes') {
        echo "<img src='".IMAGE."/matcosms.png' class='logo img-fluid' alt='Logo' loading='lazy'>";
    } elseif ($protractor == 'yes') {
        echo "<img src='".IMAGE."/protractor.png' class='logo img-fluid' alt='Logo' loading='lazy'>";
    } else {
        echo "<img src='" . IMAGE . "/logos/svgs/HorizontalLeft_Dark.svg' class='logo img-fluid' alt='Logo' loading='lazy' width=''>";
    }
}


function getLogoUIwhite()
{
  $matco = $_COOKIE['matco'] ?? 'no';
  $protractor = $_COOKIE['protractor'] ??' no';

  if ($matco == 'yes')
  echo "<img src='" . IMAGE . "/matcosms-white.png' class='logo img-fluid' alt='Logo' loading='lazy'>";
  elseif($protractor == 'yes')
  echo "<img src='" . IMAGE . "/protractor.png' class='logo img-fluid' alt='Logo' loading='lazy'>";
  else
  echo "<img src='" . IMAGE . "/logos/svgs/HorizontalLeft_Light.svg' class='logo img-fluid' alt='Logo' loading='lazy' width=''>";

}

function getFavicon()
{
    if (stripos($_SERVER['SERVER_NAME'], 'matco') !== false) {
        $brand = 'matco';
    } elseif (stripos($_SERVER['SERVER_NAME'], 'protractorgo') !== false) {
        $brand = 'protractor';
    } else {
        $brand = 'shopboss';
    }

    if ($brand == 'matco') {
        echo 'favicon_matco.ico';
    } elseif ($brand == 'protractor') {
        echo 'favicon_protractor.png';
    } else {
        echo 'favicon_shopboss.ico';
    }
}

function getTechURL()
{
    /*
     * Function to get a relative tech URL. Example:
     *   staging.shopbosspro.com    =>  staging.tech.shopbosspro.com
     *   staging.matcosms.com       =>  staging.tech.matcosms.com
     *   shopbosspro.com            =>  tech.shopbosspro.com     *
     */
    $sn_parts = explode('.', $_SERVER['SERVER_NAME']);
    $tech_sn = array_splice($sn_parts, -2, 0, 'tech');

    return implode('.', $sn_parts);
}

function getMotorModal($shopid)
{
    // this function just returns the HTML of the modal
    $output = '
    <div class="modal fade" id="motorModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">Trying to Access MOTOR Labor Times?</h4>
                </div>
                <div class="modal-body">
                    It looks like you’re trying to access a new labor guide for your shop’s account. While you’re not currently connected to this service, we’d be more than happy to configure your account and ensure you’re set up for success. <center><br> To get started, please send a message to <br> <b> <a target="_blank" href="mailto:<EMAIL>?subject=Motor Optin Request for '.$shopid.'&body=I would like to schedule my Motor setup."><EMAIL></a> </b>. </center> <br>Our customer success team will reach out to schedule your setup. Thank you!
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <a target="_blank" href="mailto:<EMAIL>?subject=Motor Optin Request for '.$shopid.'&body=I would like to schedule my Motor setup." class="btn btn-primary">Contact Customer Success</a>
                </div>
            </div>
        </div>
    </div>';

    return $output;
}
