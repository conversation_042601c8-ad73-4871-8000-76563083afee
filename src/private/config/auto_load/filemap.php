<?php
/*
 * Helper file to map URL's with their path.
 */

class FileMap
{
    static $PUBLIC_COMPONENTS_FULL_URL = "https://".ROOT."/src/public/components";
    static $PRIVATE_COMPONENTS_FULL_URL = "https://".ROOT."/src/private/components";

    static $errorPages = array(
        '404' => PUBLIC_PATH . DS . "error_pages" . DS . "404.php",
        '500' => PUBLIC_PATH . DS . "error_pages" . DS . "500.php"
    );
    static $fileMap = array(
        '/status.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "status.php",
        '/quotestatus.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "quotestatus.php",
        '/getstatusdvionly.asp' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "getstatusdvionly.php",
        '/getstatuswithdvi.asp' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "getstatuswithdvi.php",
        '/getstatusdvionly.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "getstatusdvionly.php",
        '/getstatuswithdvi.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "getstatuswithdvi.php",
        '/getstatus.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "getstatus.php",
        '/remotestatus.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "remotestatus.php",
        '/autherror.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "autherror.php",
        '/status-auth-fail.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "status-auth-fail.php",
        '/status-esig.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "status-esig.php",
        '/status-postpmt.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "status-postpmt.php",
        '/status_payment.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "status_payment.php",
        '/statusupdates.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "statusupdates.php",
        '/totalupdate.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "totalupdate.php",
        '/ps-status.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "ps-status.php",
        '/ps_status_payment.php' => PUBLIC_PATH . DS . "components" . DS . "status" . DS . "ps_status_payment.php",

        '/appointment.php' => PUBLIC_PATH . DS . "components" . DS . "appointments" . DS . "appointment.php",
        '/appointment.asp' => PUBLIC_PATH . DS . "components" . DS . "appointments" . DS . "appointment.php",
        '/appointment2.php' => PUBLIC_PATH . DS . "components" . DS . "appointments" . DS . "appointment2.php",
        '/apptset.php' => PUBLIC_PATH . DS . "components" . DS . "appointments" . DS . "apptset.php",
        '/gettime.php' => PUBLIC_PATH . DS . "components" . DS . "appointments" . DS . "gettime.php",
        '/sbpi2/createtrial.php' => PUBLIC_PATH . DS . "endpoints" . DS . "trial" . DS . "createtrial.php",
        '/dvi_report.php' => PUBLIC_PATH . DS . "components" . DS . "inspections" . DS . "report.php",
        '/dashboard.php' => PRIVATE_PATH . DS . "components" . DS . "dashboard" . DS . "dashboard.php",
        '/admin_dashboard.php' => PRIVATE_PATH . DS . "components" . DS . "admin_dashboard" . DS . "dashboard.php",
        '/retention_dashboard.php' => PRIVATE_PATH . DS . "components" . DS . "retention_dashboard" . DS . "dashboard.php",
        '/sales_dashboard.php' => PRIVATE_PATH . DS . "components" . DS . "sales_dashboard" . DS . "dashboard.php",
        '/dashboard_optin.php' => PRIVATE_PATH . DS . "components" . DS . "dashboard" . DS . "dashboard_optin.php",
        '/dashboard_expired.php' => PRIVATE_PATH . DS . "components" . DS . "dashboard" . DS . "dashboard_expired.php",
        '/optinaction.php' => PRIVATE_PATH . DS . "components" . DS . "dashboard" . DS . "optinaction.php",
        '/sbpi2/history.php' => PRIVATE_PATH . DS . "components" . DS . "history" . DS . "history.php",
        '/sbpi2/history_action.php' => PRIVATE_PATH . DS . "components" . DS . "history" . DS . "history_action.php",
        '/lyftwebhook.php' => PRIVATE_PATH . DS . "integrations" . DS . "lyft" . DS . "webhook.php",
        '/sbpi2/api/lyft/webhook.php' => PRIVATE_PATH . DS . "integrations" . DS . "lyft" . DS . "webhook.php",
        '/remove.asp' => PUBLIC_PATH . DS . "components" . DS . "root" . DS . "remove.php",
        '/wipdemo/legacy.php' => PRIVATE_PATH . DS ."components" . DS . "wipdemo" . DS . "legacy_index.php",
        '/vin' => PUBLIC_PATH . DS . "components" . DS . "vin" . DS . "index.php",
        '/help' => PUBLIC_PATH . DS . "components" . DS . "helpdesk" . DS . "index.php",
        '/ip' => PUBLIC_PATH . DS . "ip.php",
        //Login component root links
        '/login.php' => COMPONENTS_PUBLIC_PATH.DS."login".DS."login.php",
        '/signin.php' => COMPONENTS_PUBLIC_PATH.DS."login".DS."signin.php",
        'sbpi2/php/login.php' => COMPONENTS_PUBLIC_PATH.DS."login".DS."login_action.php",
        '/login_sb.php' => COMPONENTS_PUBLIC_PATH.DS."login".DS."login_sb.php",
        '/fleetlogin.php' => COMPONENTS_PUBLIC_PATH.DS."login".DS."fleetlogin.php",
        '/sbp/login.php' => COMPONENTS_PUBLIC_PATH.DS."login".DS."login.php",
        '/sbp/login_sb.php' => COMPONENTS_PUBLIC_PATH.DS."login".DS."login_sb.php",
        '/logoff.asp' => COMPONENTS_PUBLIC_PATH.DS."login".DS."logoff.php",
        '/logoff.php' => COMPONENTS_PUBLIC_PATH.DS."login".DS."logoff.php",
        '/cardknoxemail.php' => COMPONENTS_PUBLIC_PATH.DS."login".DS."cardknoxemail.php",
        '/trial.php' => COMPONENTS_PUBLIC_PATH.DS."signup".DS."trial.php",
        '/sbp/api/kukui.asp' => PUBLIC_PATH. DS ."endpoints" . DS . "kukui".DS."kukui.php",
        "/sbp/api/ftt/default.asp" => PUBLIC_PATH. DS ."endpoints" . DS . "ftt". DS . "default.php",
        "/sbp/api/demandforce/appointment/default.asp" => PUBLIC_PATH. DS ."endpoints" . DS . "demandforce".DS. "appointment".DS."default.php",
        "/sbp/api/demandforce/customer/default.asp" => PUBLIC_PATH. DS ."endpoints" . DS . "demandforce".DS. "customer".DS."default.php",
        "/sbp/api/demandforce/customer/single/default.asp" => PUBLIC_PATH. DS ."endpoints" . DS . "demandforce".DS. "customer".DS. "single". DS."default.php",

        "/sbp/api/demandforce/recommendations/default.asp" => PUBLIC_PATH. DS ."endpoints" . DS . "demandforce".DS. "recommendations".DS."default.php",
        "/sbp/api/demandforce/reminders/default.asp" => PUBLIC_PATH. DS ."endpoints" . DS . "demandforce".DS. "reminders".DS."default.php",
        "/sbp/api/demandforce/servicehistoryheader/default.asp" => PUBLIC_PATH. DS ."endpoints" . DS . "demandforce".DS. "servicehistoryheader".DS."default.php",
        "/sbp/api/demandforce/servicehistorylabor/default.asp" => PUBLIC_PATH. DS ."endpoints" . DS . "demandforce".DS. "servicehistorylabor".DS."default.php",
        "/sbp/api/demandforce/servicehistoryparts/default.asp" => PUBLIC_PATH. DS ."endpoints" . DS . "demandforce".DS. "servicehistoryparts".DS."default.php",
        "/sbp/api/demandforce/servicehistorysublet/default.asp" => PUBLIC_PATH. DS ."endpoints" . DS . "demandforce".DS. "servicehistorysublet".DS."default.php",
        "/sbp/api/demandforce/vehicle/default.asp" => PUBLIC_PATH. DS ."endpoints" . DS . "demandforce".DS. "vehicle".DS."default.php",
        "/sbpi2/api/msmcustomerandvehicle/index.php" => PRIVATE_PATH . DS . "integrations" . DS . "myshopmanager" . DS. "index.php",
        "/sbpi2/api/msmcustomerandvehicle/" => PRIVATE_PATH . DS . "integrations" . DS . "myshopmanager" . DS. "index.php",
   );

    static $folderMap = array(
        "/invoicing" => PRIVATE_PATH . DS . "components" . DS . "invoicing" . DS,
        "/fleetforce" => PRIVATE_PATH . DS . "components" . DS . "fleetforce" . DS,
        "/sbpi2/api/tpms" => PRIVATE_PATH . DS . "integrations" . DS . "tpms" . DS,
        "/sbpi2/api/msmcustomerandvehicle" => PRIVATE_PATH . DS . "integrations" . DS . "myshopmanager" . DS,
        "/sbpi2/api/qb" => PRIVATE_PATH . DS . "integrations" . DS . "qb" . DS,
        "/inspections" => PRIVATE_PATH . DS . "components" . DS . "inspections" . DS,
        "/sandbox" => PUBLIC_PATH . DS . "components" . DS . "sandbox" . DS,
        "/sbpi2/servus" => PRIVATE_PATH . DS . "integrations" . DS . "servus" . DS,
        "/API/" => PUBLIC_PATH. DS ."API" . DS . "v1" . DS . "ss" . DS . "components" . DS,
        DS => PUBLIC_PATH . DS . "components" . DS . "root" . DS,
    );

    static $assetsMap = array(
        'js' => PUBLIC_PATH . DS . "js" . DS,
        'jpg' => PUBLIC_PATH . DS . "assets" . DS . "img" . DS,
        'png' => PUBLIC_PATH . DS . "assets" . DS . "img" . DS,
        'jpeg' => PUBLIC_PATH . DS . "assets" . DS . "img" . DS,
        'gif' => PUBLIC_PATH . DS . "assets" . DS . "img" . DS,
        'svg' => PUBLIC_PATH . DS . "assets" . DS . "img" . DS,
        'css' => PUBLIC_PATH . DS . "css" . DS,
        'scss' => PUBLIC_PATH . DS . "scss" . DS
    );

    static function map_path($url_path)
    {
        $pathinfo = pathinfo($url_path);
        if (isset(self::$fileMap[$url_path])) {
            return self::$fileMap[$url_path];
        }
        if ($url_path == "/fleetforce" || $url_path == "/fleetforce/") {
            return self::$folderMap['/fleetforce']."index.php";
        }
        //Not found in file map arr
        $pathDir = $pathinfo['dirname'];
        $baseName = $pathinfo['basename']; //filename with extension
        $filename = $pathinfo['filename']; //filename without extension.

        $pathDirPrts = explode("/", $pathDir);
        $pathDirParent = $pathDirPrts[1] ?? "";
        if(isset(self::$folderMap["/$pathDirParent/"])){
            unset($pathDirPrts[1]);
            $subFolders = implode("/",$pathDirPrts);
            $rootPath = self::$folderMap["/$pathDirParent/"].$subFolders.DS;
        } else {
            $rootPath = (self::$folderMap[$pathDir]) ?? "";
        }
        $ext = $pathinfo['extension'] ?? "";
        if ((empty($ext) || $ext == "php") && !empty($rootPath)) {
            $request_component = $rootPath . $baseName;
            if (empty($ext)) {
                $request_component .= DS . "index.php";
            }

            if (file_exists($request_component)) {
                //        echo "file exists";
                //file exists in folder with same name, include.
                return $request_component;
            } else {
                //check if we can find the component path otherwise throw error
                //TODO check if we can find the component by string compare otherwise throw error

                self::error_page(404);
            }
        } else {
            //can not find proper path, throw Error not found.
            //  self::error_page(404);
            $request_component = self::mapComponent($pathDir, $baseName, $ext);
            if ($request_component) {
                return $request_component;
            } else {
                self::assetFinder($pathinfo);
            }
        }

        return false;
    }

    static function error_page($error)
    {
        //TODO instead of an error => error page, make a search path to search the error file.
        //TODO change above mentioned array to error => error header information to send proper header.
        $errstr = strval($error);
        $defaultErr = PUBLIC_PATH . DS . "error_pages" . DS . $errstr.".php";
        $errpage = empty(self::$errorPages[$errstr]) ? $defaultErr : self::$errorPages[$errstr];
        if (ob_get_length()) {
            ob_clean();
        }
        //header('HTTP/1.0 404 Not Found', true, 404);
        http_response_code(404);
        include $errpage;
        exit();
    }

    static function mapComponent($dir, $baseName, $ext = "")
    {
        //Search components in both public and private paths.
        //example https://shopbosspro.com/ro/ro.php
        //  $baseName = str_replace("/", DS, $baseName);
        $component_parts = preg_split('@/@', $dir, NULL, PREG_SPLIT_NO_EMPTY);
        define('COMPONENT_NAME', $component_parts[0]);
        $dir = str_replace("/", DS, $dir);
        if(empty($ext)){
            $baseName = "index.php";
        }
        //search private components
        //  die($dir);
        if (file_exists(COMPONENTS_PRIVATE_PATH . $dir . DS . $baseName)) {
            if ($ext == "asp" || $ext == "pdf"){
                //Band-AID
                header("Location:".self::$PRIVATE_COMPONENTS_FULL_URL.$dir."/".$baseName."?".$_SERVER['QUERY_STRING']);
                exit();
            }
            return COMPONENTS_PRIVATE_PATH . $dir . DS . $baseName;
        }
        elseif (file_exists(COMPONENTS_PUBLIC_PATH . DS . $dir . DS . $baseName)) {
            if ($ext == "asp" || $ext == "pdf"){
                //PUBLIC BAND-AID lol :p
                header("Location:".self::$PUBLIC_COMPONENTS_FULL_URL.$dir."/".$baseName."?".$_SERVER['QUERY_STRING']);
                exit();
            }
            return COMPONENTS_PUBLIC_PATH . DS . $dir . DS . $baseName;
        }
        elseif (file_exists(PUBLIC_PATH . DS . $dir . DS . $baseName)) {
            return PUBLIC_PATH . DS . $dir . DS . $baseName;
        }
        if ($component_parts[0] == "v2") {
           // $v2Dir = $component_parts[1]."-v2";
            $component_parts[1] = $component_parts[1]."-v2";
            unset($component_parts[0]);
            $v2_dir = implode(DS, $component_parts);
          //  var_dump(file_exists(COMPONENTS_PRIVATE_PATH.DS.$v2_dir.DS.$baseName));
            //echo "<br />v2 dir : ".$v2Dir." base : ".$v2base."<br >";
            //echo COMPONENTS_PRIVATE_PATH.$v2Dir.DS.$v2base;
            if (file_exists(COMPONENTS_PRIVATE_PATH.DS.$v2_dir.DS.$baseName)) {
                return COMPONENTS_PRIVATE_PATH .DS. $v2_dir . DS . $baseName;
            } else {
                return COMPONENTS_PRIVATE_PATH . $dir . DS . $baseName;
            }
        }
        return false;
    }

    /*
     * A smart method to map components from private/components as well as private/includes/components
     *
     */

    static function assetFinder($pathinfo)
    {
        $pathDir = $pathinfo['dirname'];
        $pathParts = explode("/", $pathDir);
        $subFolder = "";
        if (sizeof($pathParts) > 2) {
            $subFolder = $pathParts[sizeof($pathParts) - 1];
            $subFolder .= DS;
        }
        $baseName = $pathinfo['basename']; //filename with extension
        $filename = $pathinfo['filename']; //filename without extension.
        $ext = $pathinfo['extension'];
        $img_types = array('jpg', 'jpeg', 'png', 'gif', 'svg');
        if (in_array($ext, $img_types)) {
            $baseURL = IMAGE;
        } else {
            if ($ext == "js") {
                $baseURL = SCRIPT;
            } else {
                if ($ext == "css") {
                    $baseURL = CSS;
                }
            }
        }
        if (isset(self::$assetsMap[$ext])) {
            $path = self::$assetsMap[$ext];
            $path .= $baseName;
            if (file_exists($path)) {
                header("Location:" . $baseURL . "/" . $baseName, false, 302);
                exit();
            } else {
                echo "Not found : ".$path;
                exit();
            }
        } else {
            //well you are on your own now. it could be a pdf
           return false;
        }
    }

    function path_search($haystack, $needle) {
        return(strpos($haystack, $needle)); // or stripos() if you want case-insensitive searching.
    }

}
