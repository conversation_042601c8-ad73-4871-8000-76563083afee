<script>
    // Event bus to use
    window.bus = new Vue();

    <?php $shopid = $_COOKIE['shopid']; ?>

    Window.imagePath = "<?= UPLOAD_URL . '/' . $shopid . '/' ?>";
    var vm = new Vue({
        el: '#dvi-inspection-prepare',
        components: sbpUI.components,
        data() {
            return {
                showDviTaxesFees: false,
                showEmployeePhoto: false,
                quoteKey: 1,
                report: null,
                goodFindings: [],
                warningFindings: [],
                criticalFindings: [],
                itemEducationChecked: {},
                inspection: {},
                educationAssets: [],
                iframeUrl: '',
                iframeUrlPD: '',
                motorData: [],
                activeData: {},
                savePL: false,
                unsavePL: false,
                plData: {
                    id: null,
                    partnumber: '',
                    desc: '',
                    price: '',
                    cost: '',
                    qty: '',
                    tech: '',
                    matrix: '',
                    overridematrix: 'no',
                    rate: {},
                    finding: {}
                },
                techs: {},
                rates: {},
                cats: {},
                catsdis: {},
                partcats: {},
                partcatsdis: {},
                activeInput: {},
                cannedJobs: {},
                usepartsmatrix: 'no',
                porder: {},
                partkey: '',
                search_data: {}
            };
        },
        computed: {
            imageBaseUrl() {
                let url = ''

                <?php if (!defined('DEV')) { ?>
                url = 'https://shopbosspro.com';
                <?php } ?>

                return url;
            },
        },
        mounted() {
            Vue.prototype.$rootUrl = '<?= ROOT ?>';
            Vue.prototype.$isDev = "<?= DEV ?>";
            Vue.prototype.$imageBaseUrl = this.imageBaseUrl;

            this.getPageData();

            this.loadDropdownInfo();
            this.loadCannedJobs();
            this.loadPartsOrdering();
            window.addEventListener('message', this.handlePDReturn);
            $('#canned-modal').on("shown.bs.modal", this.handleCannedShown);
            $('#canned-modal').on("hidden.bs.modal", this.handleCannedHidden);

            window.bus.$on('addPartsToAll', this.handleAddPartOpen);
        },
        methods: {
            getPageData(){
                const qs = this.parseQueryString();

                this.post({
                    id: qs.id
                }, '/inspections/get.php').then((inspection) => {
                    this.inspection = inspection;
                    Vue.prototype.$current_inspection = this.inspection;
                    this.quoteKey += 1;
                    this.educationAssets = this.inspection.educationAssets;
                    Vue.prototype.$educationAssets = this.educationAssets;
                    let itemEducationChecked = {};
                    this.inspection.model.categories.forEach(category => {
                        category.sections.forEach(section => {
                            section.items.forEach(item => {
                                item.educations.forEach(education => {
                                    itemEducationChecked[item.id] = itemEducationChecked[item.id] || {};
                                    if (education.education_asset_id)
                                        itemEducationChecked[item.id][education.education_asset_id] = true;
                                })
                            })
                        })
                    })

                    this.itemEducationChecked = itemEducationChecked;

                    Vue.prototype.$educationChecked = itemEducationChecked
                    Vue.prototype.$itemEducationChecked = itemEducationChecked;
                });

                this.post({
                    id: qs.id
                }, '/inspections/get-report.php').then((report) => {
                    this.report = report;
                    const goodFindings = [];
                    const warningFindings = [];
                    const criticalFindings = [];
                    report.inputs.forEach((itemInput) => {

                        const measurements = [];
                        itemInput.measurements.forEach((measurement) => {
                            const measurementModelInput = report.inputs.find((ii) => ii.modelId === measurement.measurementModelId);

                            if (measurementModelInput) {
                                measurements.push({
                                    name: measurement[`measurementName${measurement.whichMeasurement}`],
                                    description: measurementModelInput[`measurement${measurement.whichMeasurement}Input`]
                                });
                            }
                        });

                        itemInput.findingInputs.forEach((findingInput) => {
                            if (findingInput.status === 'warning') {
                                warningFindings.push({
                                    itemModelName: itemInput.modelName,
                                    ...findingInput,
                                    model_id: itemInput.modelId,
                                    measurements
                                });
                            } else if (findingInput.status === 'critical') {
                                criticalFindings.push({
                                    itemModelName: itemInput.modelName,
                                    ...findingInput,
                                    model_id: itemInput.modelId,
                                    measurements
                                });
                            } else if (findingInput.status === 'good') {
                                goodFindings.push({
                                    itemModelName: itemInput.modelName,
                                    model_id: itemInput.modelId,
                                    ...findingInput,
                                    measurements
                                });
                            }
                        });
                    });

                    this.goodFindings = goodFindings;
                    this.warningFindings = warningFindings;
                    this.criticalFindings = criticalFindings;
                    setTimeout(() => {
                        document.querySelectorAll('.form-outline').forEach((el) => {
                            new mdb.Input(el).init();
                        });
                    }, 0);
                });

                this.post(null, '/custom-settings/getall.php').then((res) => {
                    this.showDviTaxesFees = !res || (res && res.showDviTaxesFees === 'yes');
                    this.showEmployeePhoto = !res || (res && res.showEmployeePhoto === 'yes');
                });
            },

            async loadDropdownInfo() {
                const response = await this.post(null, '/inspections/get-info.php')
                this.techs = response.techs;
                this.rates = response.rates;
                this.cats = response.cats;
                this.catsdis = response.catsdis;
                this.partcats = response.partcats;
                this.partcatsdis = response.partcatsdis;
                this.usepartsmatrix = response.usepartsmatrix;
            },
            async loadCannedJobs() {
                const response = await this.post(null, '/inspections/get-canned-jobs.php')
                this.cannedJobs = response.cannedjobs;
            },
            async loadPartsOrdering() {
                const response = await this.post(null, '/inspections/get-parts-ordering.php')
                this.porder = response;
            },
            handleChangeDVIEstimate(event) {
                const qs = this.parseQueryString();
                this.post({
                    id: qs.id,
                    estimate_complete: event.target.checked ? 1 : 0
                }, '/inspections/update.php')
            },
            handleCustomerRequestChange(key, ...args) {
                if (this.timeout1) {
                    clearTimeout(this.timeout1);
                    this.timeout1 = null;
                }

                this.timeout1 = setTimeout(() => {
                    if (key !== 'image') {
                        const qs = this.parseQueryString();
                        this.post({
                            id: qs.id,
                            [key]: args[0]
                        }, '/inspections/update.php')
                    } else {
                        this.post({
                            id: args[0],
                            [args[1]]: args[2]
                        }, '/inspection-customer-request-images/update.php')
                    }
                }, 300);
            },
            handleEducationCheck(finding_id, asset_id) {

            },
            handleChange(finding, key, value1, value2) {
                if (key !== 'photo') {
                    if (this.timeout3) {
                        clearTimeout(this.timeout3);
                        this.timeout3 = null;
                    }

                    this.timeout3 = setTimeout(() => {
                        const qs = this.parseQueryString();
                        this.post({
                            id: finding.id,
                            [key]: value1
                        }, '/inspection-items/update.php');
                    }, 300);
                } else {
                    this.post({
                        id: value1,
                        available: value2
                    }, '/inspection-items/set-finding-image-available.php');
                }
            },
            handleChangeGeneralRecommendation(event) {
                if (this.timeout2) {
                    clearTimeout(this.timeout2);
                    this.timeout2 = null;
                }

                this.timeout2 = setTimeout(() => {
                    const qs = this.parseQueryString();
                    this.post({
                        id: qs.id,
                        general_recommendation: event.target.value
                    }, '/inspections/update.php')
                }, 300);
            },
            handleAddtoRO(input) {
                document.getElementById("addtoro-" + input.id).disabled = true;

                this.post({
                    type: "addtoro",
                    roid: this.inspection.roid,
                    complaint: input.itemModelName,
                    inspection_item_id: input.id
                }, "/inspections/process-motor.php").then((res) => {

                    sbpUI.methods.alertPositive('Added');
                    document.getElementById("addtoro-" + input.id).style.display = "none";

                });
            },
            handleMotorOpen(motor, vin, input) {
                if (motor == 'no')
                    this.$refs['nomotor-modal'].open();
                else {
                    this.activeData = {};
                    this.activeData.inspection_item_id = input.id;
                    this.activeData.comid = input.complaint.comid;
                    this.activeData.modelName = input.itemModelName;
                    this.activeData.status = input.status;
                    var iOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
                    this.iframeUrl = `<?= COMPONENTS_PRIVATE ?>/ro/loadmotor.php?ios=` + iOS + `&pid=39&vin=` + vin + `&comid=&shopid=<?php echo $_COOKIE['shopid']; ?>&roid=${this.inspection.roid}`;
                    this.$refs['motor-modal'].open();
                }
            },
            handlePDOpen(vin, input) {
                this.activeData = {};
                this.activeData.inspection_item_id = input.id;
                this.activeData.status = input.status;
                this.iframeUrlPD = `<?= COMPONENTS_PRIVATE ?>/ro/prodemand.php?vin=${this.inspection.vh_vin}&complaint=` + input.itemModelName + `&comid=` + input.complaint.comid + `&inspection_item_id=` + input.id + `&shopid=<?php echo $_COOKIE['shopid']; ?>&roid=${this.inspection.roid}`;
                this.$refs['pd-modal'].open();
            },
            handlePDReturn: function (e) {
                sp = e.data.parts;
                sl = e.data.labors;
                sc = e.data.complaint;
                if (sp == undefined && sl == undefined) return;

                if (sp != "") {
                    if (this.activeData.status == 'critical')
                        this.criticalFindings = this.criticalFindings.map((is) => {
                            if (is.id !== this.activeData.inspection_item_id) return is;
                            return {
                                ...is,
                                parts: (is.parts || []).concat(sp),
                                complaint: sc
                            };
                        });
                    else if (this.activeData.status == 'warning')
                        this.warningFindings = this.warningFindings.map((is) => {
                            if (is.id !== this.activeData.inspection_item_id) return is;
                            return {
                                ...is,
                                parts: (is.parts || []).concat(sp),
                                complaint: sc
                            };
                        });

                }

                if (sl != "") {
                    if (this.activeData.status == 'critical')
                        this.criticalFindings = this.criticalFindings.map((is) => {
                            if (is.id !== this.activeData.inspection_item_id) return is;
                            return {
                                ...is,
                                labor: (is.labor || []).concat(sl),
                                complaint: sc
                            };
                        });
                    else if (this.activeData.status == 'warning')
                        this.warningFindings = this.warningFindings.map((is) => {
                            if (is.id !== this.activeData.inspection_item_id) return is;
                            return {
                                ...is,
                                labor: (is.labor || []).concat(sl),
                                complaint: sc
                            };
                        });

                }
                $('.modal').modal('hide');
                setTimeout(() => {
                    this.iframeUrl = '';
                    this.iframeUrlPD = '';
                }, 0);
                sbpUI.methods.alertPositive('Added');
            },
            handleCloseMotor() {
                this.post({type: "check", roid: this.inspection.roid}, "/inspections/process-motor.php").then((res) => {

                    if (res.status == "found") {
                        this.motorData = res.data;
                        this.$refs['bump-modal'].open();
                    }
                });

                setTimeout(() => {
                    this.iframeUrl = '';
                    this.iframeUrlPD = '';
                }, 0);

            },
            handleBump(per) {
                const lbhrs = document.querySelectorAll(".laborhours");
                for (const lb of lbhrs) {
                    const cval = lb.value;
                    const mk = parseFloat("1." + per);
                    lb.value = (cval * mk).toFixed(2);
                }
            },
            handlecloseWOSaving() {
                this.unsavePL = true;
                this.post({
                    type: "delete",
                    roid: this.inspection.roid
                }, "/inspections/process-motor.php").then((res) => {
                    $('.modal').modal('hide');
                    this.unsavePL = false;
                    setTimeout(() => {
                        this.iframeUrl = '';
                        this.iframeUrlPD = '';
                    }, 0);

                })
            },
            handleSave() {
                this.savePL = true;
                const pldata = [];

                $('input[class=motorcheck]:checked').each(function () {
                    const id = $(this).attr('id');
                    const $this = $(this);
                    const a = {};

                    if ($this.val() == 'PART') {
                        a['partnumber'] = $('#partnumber' + id).val();
                        a['partprice'] = $('#pprice' + id).val();
                        a['partqty'] = $('#pqty' + id).val();
                        a['partdesc'] = $('#partdesc' + id).val();
                        a['type'] = 'Part';
                    } else {
                        a['tech'] = $('#tech' + id).val();
                        a['desc'] = $('#labordesc' + id).val();
                        a['hours'] = $('#laborhours' + id).val();
                        a['rate'] = $('#laborrate' + id).val();
                        a['category'] = $('#lmcat' + id).val();
                        a['type'] = 'Labor';
                    }
                    pldata.push(a);
                });

                this.post({
                    type: "save",
                    pldata: JSON.stringify(pldata),
                    roid: this.inspection.roid,
                    comid: this.activeData.comid,
                    inspection_item_id: this.activeData.inspection_item_id,
                    complaint: this.activeData.modelName
                }, "/inspections/process-motor.php").then((res) => {

                    if (res.parts != "") {
                        if (this.activeData.status == 'critical')
                            this.criticalFindings = this.criticalFindings.map((is) => {
                                if (is.id !== this.activeData.inspection_item_id) return is;
                                return {
                                    ...is,
                                    parts: (is.parts || []).concat(res.parts),
                                    complaint: res.complaint
                                };
                            });
                        else if (this.activeData.status == 'warning')
                            this.warningFindings = this.warningFindings.map((is) => {
                                if (is.id !== this.activeData.inspection_item_id) return is;
                                return {
                                    ...is,
                                    parts: (is.parts || []).concat(res.parts),
                                    complaint: res.complaint
                                };
                            });

                    }

                    if (res.labors != "") {
                        if (this.activeData.status == 'critical')
                            this.criticalFindings = this.criticalFindings.map((is) => {
                                if (is.id !== this.activeData.inspection_item_id) return is;
                                return {
                                    ...is,
                                    labor: (is.labor || []).concat(res.labors),
                                    complaint: res.complaint
                                };
                            });
                        else if (this.activeData.status == 'warning')
                            this.warningFindings = this.warningFindings.map((is) => {
                                if (is.id !== this.activeData.inspection_item_id) return is;
                                return {
                                    ...is,
                                    labor: (is.labor || []).concat(res.labors),
                                    complaint: res.complaint
                                };
                            });

                    }

                    this.getPageData();

                    $('.modal').modal('hide');
                    this.savePL = false;
                    setTimeout(() => {
                        this.iframeUrl = '';
                        this.iframeUrlPD = '';
                    }, 0);
                    sbpUI.methods.alertPositive('Added');
                });

            },
            handleEditPl(finding, type, input) {
                if (type == 'Part') {
                    this.plData = {
                        id: input.partid,
                        partnumber: input.partnumber,
                        desc: input.partdesc,
                        price: parseFloat(input.partprice).toFixed(2),
                        cost: parseFloat(input.cost).toFixed(2),
                        qty: input.quantity,
                        tech: '',
                        rate: {},
                        finding: finding,
                        matrix: input.partcategory,
                        overridematrix: input.overridematrix
                    };

                    this.$refs['part-edit-modal'].open();
                } else if (type == 'Labor') {
                    this.plData = {
                        id: input.laborid,
                        partnumber: '',
                        desc: input.labor,
                        price: '',
                        cost: '',
                        qty: input.laborhours,
                        tech: input.tech,
                        rate: {label: input.ratelabel, rate: input.hourlyrate},
                        finding: finding,
                        matrix: input.matrix
                    };
                    this.$refs['labor-edit-modal'].open();
                } else if (type == 'Sublet') {
                    this.plData = {
                        id: input.subletid,
                        partnumber: input.subletinvoiceno,
                        desc: input.subletdesc,
                        price: input.subletprice,
                        cost: input.subletcost,
                        qty: '',
                        tech: input.subletsupplier,
                        rate: {},
                        finding: finding,
                        matrix: ''
                    };
                    this.$refs['sublet-edit-modal'].open();
                }
            },
            handleSavePart() {
                this.savePL = true;
                this.post({
                    type: "savepart",
                    part: this.plData,
                    roid: this.inspection.roid
                }, "/inspections/update-part-labor.php").then((res) => {

                    if (this.plData.finding.status == 'critical')
                        this.criticalFindings = this.criticalFindings.map((is) => {
                            if (is.id !== this.plData.finding.id) return is;
                            return {
                                ...is,
                                parts: is.parts.map((p) => {
                                    if (p.partid != res.partid) return p;
                                    return res;
                                })
                            };
                        });

                    else if (this.plData.finding.status == 'warning')
                        this.warningFindings = this.warningFindings.map((is) => {
                            if (is.id !== this.plData.finding.id) return is;
                            return {
                                ...is,
                                parts: is.parts.map((p) => {
                                    if (p.partid != res.partid) return p;
                                    return res;
                                })
                            };
                        });

                    this.savePL = false;
                    this.$refs['part-edit-modal'].close();
                    sbpUI.methods.alertPositive('Saved');

                });
            },
            handleSaveLabor() {
                this.savePL = true;
                this.plData.rate = document.getElementById('laborrate').value;
                var ratesplit = this.plData.rate.split(':;');
                var hourlyrate = parseFloat(ratesplit[1]);
                var calclabor = parseFloat((hourlyrate * parseFloat(this.plData.qty)).toFixed(2));
                var matrix = this.plData.matrix;

                if (this.plData.matrix != 'none') {
                    $.each(this.cats, function (i, v) {
                        if (v.category.toUpperCase() === matrix.toUpperCase() && v.start <= calclabor && v.end >= calclabor) {
                            calclabor = Math.round((calclabor * v.factor) * 100) / 100
                            return false
                        }
                    })
                }
                this.post({
                    type: "savelabor",
                    labor: this.plData,
                    linetotal: calclabor,
                    roid: this.inspection.roid
                }, "/inspections/update-part-labor.php").then((res) => {
                    if (this.plData.finding.status == 'critical')
                        this.criticalFindings = this.criticalFindings.map((is) => {
                            if (is.id !== this.plData.finding.id) return is;
                            return {
                                ...is,
                                labor: is.labor.map((l) => {
                                    if (l.laborid != res.laborid) return l;
                                    return res;
                                })
                            };
                        });

                    else if (this.plData.finding.status == 'warning')
                        this.warningFindings = this.warningFindings.map((is) => {
                            if (is.id !== this.plData.finding.id) return is;
                            return {
                                ...is,
                                labor: is.labor.map((l) => {
                                    if (l.laborid != res.laborid) return l;
                                    return res;
                                })
                            };
                        });

                    this.savePL = false;
                    this.$refs['labor-edit-modal'].close();
                    sbpUI.methods.alertPositive('Saved');
                });
            },
            handleSaveSublet() {
                this.savePL = true;
                this.post({
                    type: "savesublet",
                    sublet: this.plData,
                    roid: this.inspection.roid
                }, "/inspections/update-part-labor.php").then((res) => {
                    if (this.plData.finding.status == 'critical')
                        this.criticalFindings = this.criticalFindings.map((is) => {
                            if (is.id !== this.plData.finding.id) return is;
                            return {
                                ...is,
                                sublet: is.sublet.map((l) => {
                                    if (l.subletid != res.subletid) return l;
                                    return res;
                                })
                            };
                        });

                    else if (this.plData.finding.status == 'warning')
                        this.warningFindings = this.warningFindings.map((is) => {
                            if (is.id !== this.plData.finding.id) return is;
                            return {
                                ...is,
                                sublet: is.sublet.map((l) => {
                                    if (l.subletid != res.subletid) return l;
                                    return res;
                                })
                            };
                        });

                    this.savePL = false;
                    this.$refs['sublet-edit-modal'].close();
                    sbpUI.methods.alertPositive('Saved');
                });
            },
            handleAddPartOpen(input) {
                this.activeInput = input;
                document.querySelectorAll('.form-control').forEach((el) => {
                    el.value = '';
                });
                if (this.usepartsmatrix == 'yes') {
                    document.getElementById('addmatrixprice').value = "no"
                    document.getElementById('addmatrixcategory').value = ""
                }
                this.$refs['part-add-modal'].open();
            },
            handlePartSearch() {
                if (this.partkey.length > 1) {
                    this.post({term: this.partkey}, "/inspections/getparts.php").then((res) => {
                        this.search_data = res;
                    })
                }
            },
            handlePartSelect(data) {
                this.search_data = {}
                this.partkey = data.partnumber
                document.getElementById('addpartdesc').value = data.partdesc
                document.getElementById('addpartprice').value = data.partprice.replace('$', '')
                document.getElementById('addpartcost').value = data.partcost.replace('$', '')
                document.getElementById('addmatrixprice').value = data.overridematrix
                document.getElementById('addmatrixcategory').value = data.partcategory
                document.getElementById('addpartqty').focus()
            },
            handleSavePartCall() {
                if (this.activeInput === null) {
                    this.handleAddPartsAll();
                } else {
                    this.handleAddPart();
                }
            },
            async handleAddPartsAll() {
                const partnumber = document.getElementById('addpartnum').value;
                const partdesc = document.getElementById('addpartdesc').value;
                const partqty = document.getElementById('addpartqty').value;
                let partprice = document.getElementById('addpartprice').value;
                const partcost = document.getElementById('addpartcost').value;
                let partcategory = '';
                let overridematrix = "no";

                if (partnumber == '' || partdesc == '' || partqty == '' || partprice == '') {
                    sbpUI.methods.alertNegative('Fields cannot be empty');
                    return;
                }

                if (this.usepartsmatrix == 'yes') {
                    overridematrix = $('#addmatrixprice').val();
                    partcategory = $('#addmatrixcategory').val();

                    if ($('#addmatrixprice').val() == 'no' && $('#addmatrixcategory').val() != '') {
                        $.each(this.partcats, function (i, v) {
                            if (v.category.toUpperCase() === partcategory.toUpperCase() && v.start <= partcost && v.end >= partcost) {
                                partprice = Math.round((partcost * v.factor) * 100) / 100;
                                return false
                            }
                        })
                    }
                }

                this.savePL = true;

                // Update warning items
                const warningLength = this.warningFindings.length;
                for (let i = 0; i < warningLength; i++) {
                    const finding = this.warningFindings[i];
                    let comid = finding.complaint.comid;
                    let itemid = finding.id;

                    await this.savePart(finding, itemid, {
                        comid: comid,
                        partnumber: partnumber,
                        partdesc: partdesc,
                        partqty: partqty,
                        partcost: partcost,
                        partprice: partprice,
                        partcategory: partcategory,
                        overridematrix: overridematrix,
                    });
                }

                // Update critical items
                const criticalLength = this.criticalFindings.length;
                for (let i = 0; i < criticalLength; i++) {
                    const finding = this.criticalFindings[i];
                    let comid = finding.complaint.comid;
                    let itemid = finding.id;

                    await this.savePart(finding, itemid, {
                        comid: comid,
                        partnumber: partnumber,
                        partdesc: partdesc,
                        partqty: partqty,
                        partcost: partcost,
                        partprice: partprice,
                        partcategory: partcategory,
                        overridematrix: overridematrix,
                    });
                }

                this.savePL = false;
                this.$refs['part-add-modal'].close();
                sbpUI.methods.alertPositive('Added');
                document.querySelectorAll('.form-control').forEach((el) => {
                    el.value = '';
                });
            },
            async savePart(finding, itemId, data) {
                let response = await this.post({
                    roid: this.inspection.roid,
                    inspection_item_id: itemId,
                    complaint: finding.itemModelName,
                    ...data
                }, "/inspections/add-part.php");

                if (finding.status === 'critical')
                    this.criticalFindings = this.criticalFindings.map((is) => {
                        if (is.id !== itemId) return is;
                        return {
                            ...is,
                            parts: (is.parts || []).concat(response.parts),
                            complaint: response.complaint
                        };
                    });
                else if (finding.status === 'warning')
                    this.warningFindings = this.warningFindings.map((is) => {
                        if (is.id !== itemId) return is;
                        return {
                            ...is,
                            parts: (is.parts || []).concat(response.parts),
                            complaint: response.complaint
                        };
                    });
            },
            handleAddPart() {
                var partnumber = document.getElementById('addpartnum').value;
                var partdesc = document.getElementById('addpartdesc').value;
                var partqty = document.getElementById('addpartqty').value;
                var partprice = document.getElementById('addpartprice').value;
                var partcost = document.getElementById('addpartcost').value;
                var comid = this.activeInput.complaint.comid;
                var itemid = this.activeInput.id;
                var partcategory = '';
                var overridematrix = "no";

                if (partnumber == '' || partdesc == '' || partqty == '' || partprice == '') {
                    sbpUI.methods.alertNegative('Fields cannot be empty');
                    return;
                }

                if (this.usepartsmatrix == 'yes') {
                    overridematrix = $('#addmatrixprice').val();
                    partcategory = $('#addmatrixcategory').val();

                    if ($('#addmatrixprice').val() == 'no' && $('#addmatrixcategory').val() != '') {
                        $.each(this.partcats, function (i, v) {
                            if (v.category.toUpperCase() === partcategory.toUpperCase() && v.start <= partcost && v.end >= partcost) {
                                partprice = Math.round((partcost * v.factor) * 100) / 100;
                                return false
                            }
                        })
                    }
                }
                this.savePL = true;
                this.post({
                    comid: comid,
                    partnumber: partnumber,
                    partdesc: partdesc,
                    partqty: partqty,
                    partcost: partcost,
                    partprice: partprice,
                    partcategory: partcategory,
                    overridematrix: overridematrix,
                    roid: this.inspection.roid,
                    inspection_item_id: itemid,
                    complaint: this.activeInput.itemModelName
                }, "/inspections/add-part.php").then((res) => {

                    if (this.activeInput.status == 'critical')
                        this.criticalFindings = this.criticalFindings.map((is) => {
                            if (is.id !== itemid) return is;
                            return {
                                ...is,
                                parts: (is.parts || []).concat(res.parts),
                                complaint: res.complaint
                            };
                        });
                    else if (this.activeInput.status == 'warning')
                        this.warningFindings = this.warningFindings.map((is) => {
                            if (is.id !== itemid) return is;
                            return {
                                ...is,
                                parts: (is.parts || []).concat(res.parts),
                                complaint: res.complaint
                            };
                        });
                    this.savePL = false;
                    this.$refs['part-add-modal'].close();
                    sbpUI.methods.alertPositive('Added');
                    document.querySelectorAll('.form-control').forEach((el) => {
                        el.value = '';
                    });
                });
            },
            handleAddPartCalc() {
                var partcost = document.getElementById('addpartcost').value;
                if ($('#addmatrixcategory').length > 0 && $('#addmatrixprice').val() == 'no' && $('#addmatrixcategory').val() != '' && partcost != '') {
                    partcategory = $('#addmatrixcategory').val();
                    $.each(this.partcats, function (i, v) {
                        if (v.category.toUpperCase() === partcategory.toUpperCase() && v.start <= partcost && v.end >= partcost) {
                            $('#addpartprice').val((Math.round((partcost * v.factor) * 100) / 100).toFixed(2));
                            return false
                        }
                    })
                }
            },
            handleEditPartCalc() {
                if ($('#matrixcategory').length > 0 && this.plData.overridematrix == 'no' && this.plData.matrix != '' && this.plData.cost != '') {
                    this.partcats.forEach((v, i) => {

                        if (v.category.toUpperCase() === this.plData.matrix.toUpperCase() && v.start <= this.plData.cost && v.end >= this.plData.cost) {
                            this.plData.price = (Math.round((this.plData.cost * v.factor) * 100) / 100).toFixed(2);
                            return false
                        }

                    });
                }
            },
            handleAddLaborOpen(input) {
                this.activeInput = input;
                $("#addlabormatrix").val($("#addlabormatrix option:first").val());
                this.$refs['labor-add-modal'].open();
            },
            handleAddLabor() {
                var tech = document.getElementById('addlabortech').value;
                var labor = document.getElementById('addlabordesc').value;
                var hours = document.getElementById('addhours').value;
                var ratelabel = document.getElementById('addlaborrate').value;
                var matrix = document.getElementById('addlabormatrix').value;
                var comid = this.activeInput.complaint.comid;
                var itemid = this.activeInput.id;

                if (tech == '' || labor == '' || hours == '' || ratelabel == '') {
                    sbpUI.methods.alertNegative('Fields cannot be empty');
                    return;
                }

                var ratesplit = ratelabel.split(':;');
                var hourlyrate = parseFloat(ratesplit[1]);

                var calclabor = parseFloat((hourlyrate * parseFloat(hours)).toFixed(2));

                if (matrix != 'none') {
                    $.each(this.cats, function (i, v) {
                        if (v.category.toUpperCase() === matrix.toUpperCase() && v.start <= calclabor && v.end >= calclabor) {
                            calclabor = Math.round((calclabor * v.factor) * 100) / 100
                            return false
                        }
                    })
                }

                this.savePL = true;
                this.post({
                    comid: comid,
                    tech: tech,
                    labor: labor,
                    hours: hours,
                    ratelabel: ratelabel,
                    linetotal: calclabor,
                    matrix: matrix,
                    roid: this.inspection.roid,
                    inspection_item_id: itemid,
                    complaint: this.activeInput.itemModelName
                }, "/inspections/add-labor.php").then((res) => {
                    if (this.activeInput.status == 'critical')
                        this.criticalFindings = this.criticalFindings.map((is) => {
                            if (is.id !== itemid) return is;
                            return {
                                ...is,
                                labor: (is.labor || []).concat(res.labor),
                                complaint: res.complaint
                            };
                        });
                    else if (this.activeInput.status == 'warning')
                        this.warningFindings = this.warningFindings.map((is) => {
                            if (is.id !== itemid) return is;
                            return {
                                ...is,
                                labor: (is.labor || []).concat(res.labor),
                                complaint: res.complaint
                            };
                        });
                    this.savePL = false;
                    this.$refs['labor-add-modal'].close();
                    sbpUI.methods.alertPositive('Added');
                    document.querySelectorAll('.form-control').forEach((el) => {
                        el.value = '';
                    });
                });
            },
            handleAddSubletOpen(input) {
                this.activeInput = input;
                this.$refs['sublet-add-modal'].open();
            },
            handleAddSublet() {
                var description = document.getElementById('addsubletdesc').value;
                var cost = document.getElementById('addsubletcost').value;
                var price = document.getElementById('addsubletprice').value;
                var invoice = document.getElementById('addsubletinv').value;
                var supplier = document.getElementById('addsubletsup').value;
                var comid = this.activeInput.complaint.comid;
                var itemid = this.activeInput.id;

                if (description == '' || cost == '' || price == '') {
                    sbpUI.methods.alertNegative('Description, Cost, Price fields cannot be empty');
                    return;
                }

                this.savePL = true;

                this.post({
                    comid: comid,
                    desc: description,
                    cost: cost,
                    price: price,
                    invoice: invoice,
                    supplier: supplier,
                    roid: this.inspection.roid,
                    inspection_item_id: itemid,
                    complaint: this.activeInput.itemModelName
                }, "/inspections/add-sublet.php").then((res) => {
                    console.log(res)
                    if (this.activeInput.status == 'critical')
                        this.criticalFindings = this.criticalFindings.map((is) => {
                            if (is.id !== itemid) return is;
                            return {
                                ...is,
                                sublet: (is.sublet || []).concat(res.sublet),
                                complaint: res.complaint
                            };
                        });
                    else if (this.activeInput.status == 'warning')
                        this.warningFindings = this.warningFindings.map((is) => {
                            if (is.id !== itemid) return is;
                            return {
                                ...is,
                                sublet: (is.sublet || []).concat(res.sublet),
                                complaint: res.complaint
                            };
                        });
                    this.savePL = false;
                    this.$refs['sublet-add-modal'].close();
                    sbpUI.methods.alertPositive('Added');
                    document.querySelectorAll('.form-control').forEach((el) => {
                        el.value = '';
                    });
                });
            },
            handleCannedOpen(input) {
                this.activeInput = input;
                this.$refs['canned-modal'].open();
            },
            handleCannedMouseover(e) {
                var element = document.getElementById(e.currentTarget.id);
                element.classList.remove("alert-info");
                element.classList.add("alert-warning");
            },
            handleCannedMouseleave(e) {
                var element = document.getElementById(e.currentTarget.id);
                element.classList.remove("alert-warning");
                element.classList.add("alert-info");
            },
            handleJobSearch() {
                const key = document.getElementById('srch').value.toLowerCase();
                $(".row.cjrow").filter(function () {
                    $(this).parent().toggle($(this).text().toLowerCase().indexOf(key) > -1);
                });
            },
            handleCannedShown() {
                const h = $("#canned-modal .modal-body").height();
                $("#canned-modal .modal-body").height(h);
            },
            handleCannedHidden() {
                document.getElementById('srch').value = '';
                $(".row.cjrow").parent().show();
            },
            handleCannedClick(cannedjob) {
                this.activeData = {id: cannedjob.id};
                document.getElementById('canned-hr-name').innerHTML = cannedjob.jobname.toUpperCase();
                this.$refs['canned-hr-modal'].open();
            },
            handleSaveCanned() {
                this.savePL = true;
                const tech = document.getElementById('cannedtech').value;
                const rate = document.getElementById('cannedrate').value;

                this.post({
                    roid: this.inspection.roid,
                    id: this.activeData.id,
                    comid: this.activeInput.complaint.comid,
                    inspection_item_id: this.activeInput.id,
                    complaint: this.activeInput.itemModelName,
                    tech: tech,
                    rate: rate
                }, "/inspections/add-canned-job.php").then((res) => {

                    const mergeComplaint = () => {
                        return { ...this.activeInput.complaint, ...res.complaint };
                    };

                    if (res.parts != "") {
                        if (this.activeInput.status == 'critical')
                            this.criticalFindings = this.criticalFindings.map((is) => {
                                if (is.id !== this.activeInput.id) return is;
                                return {
                                    ...is,
                                    parts: (is.parts || []).concat(res.parts),
                                    complaint: mergeComplaint()
                                };
                            });
                        else if (this.activeInput.status == 'warning')
                            this.warningFindings = this.warningFindings.map((is) => {
                                if (is.id !== this.activeInput.id) return is;
                                return {
                                    ...is,
                                    parts: (is.parts || []).concat(res.parts),
                                    complaint: mergeComplaint()
                                };
                            });
                    }

                    if (res.labors != "") {
                        if (this.activeInput.status == 'critical')
                            this.criticalFindings = this.criticalFindings.map((is) => {
                                if (is.id !== this.activeInput.id) return is;
                                return {
                                    ...is,
                                    labor: (is.labor || []).concat(res.labors),
                                    complaint: mergeComplaint()
                                };
                            });
                        else if (this.activeInput.status == 'warning')
                            this.warningFindings = this.warningFindings.map((is) => {
                                if (is.id !== this.activeInput.id) return is;
                                return {
                                    ...is,
                                    labor: (is.labor || []).concat(res.labors),
                                    complaint: mergeComplaint()
                                };
                            });
                    }

                    if (res.sublets != "") {
                        if (this.activeInput.status == 'critical')
                            this.criticalFindings = this.criticalFindings.map((is) => {
                                if (is.id !== this.activeInput.id) return is;
                                return {
                                    ...is,
                                    sublet: (is.sublet || []).concat(res.sublets),
                                    complaint: mergeComplaint()
                                };
                            });
                        else if (this.activeInput.status == 'warning')
                            this.warningFindings = this.warningFindings.map((is) => {
                                if (is.id !== this.activeInput.id) return is;
                                return {
                                    ...is,
                                    sublet: (is.sublet || []).concat(res.sublets),
                                    complaint: mergeComplaint()
                                };
                            });
                    }

                    $('.modal').modal('hide');
                    this.savePL = false;
                    sbpUI.methods.alertPositive('Added');
                });
            },

            handleDeleteConfirm(finding, type, id) {
                this.activeData = {finding: finding, type: type, id: id};
                this.$refs['pl-delete-modal'].open();
            },
            handleDeletePl() {

                this.post({
                    type: "delete",
                    roid: this.inspection.roid,
                    id: this.activeData.id,
                    pl: this.activeData.type
                }, "/inspections/update-part-labor.php").then((res) => {

                    if (this.activeData.finding.status == 'critical')
                        this.criticalFindings = this.criticalFindings.map((is) => {
                            if (is.id !== this.activeData.finding.id) return is;
                            return {
                                ...is,
                                parts: this.activeData.type == 'Part' ? (is.parts || []).filter((p) => p.partid !== this.activeData.id) : is.parts,
                                labor: this.activeData.type == 'Labor' ? (is.labor || []).filter((l) => l.laborid !== this.activeData.id) : is.labor,
                                sublet: this.activeData.type == 'Sublet' ? (is.sublet || []).filter((l) => l.subletid !== this.activeData.id) : is.sublet
                            };
                        });
                    else if (this.activeData.finding.status == 'warning')
                        this.warningFindings = this.warningFindings.map((is) => {
                            if (is.id !== this.activeData.finding.id) return is;
                            return {
                                ...is,
                                parts: this.activeData.type == 'Part' ? (is.parts || []).filter((p) => p.partid !== this.activeData.id) : is.parts,
                                labor: this.activeData.type == 'Labor' ? (is.labor || []).filter((l) => l.laborid !== this.activeData.id) : is.labor,
                                sublet: this.activeData.type == 'Sublet' ? (is.sublet || []).filter((l) => l.subletid !== this.activeData.id) : is.sublet
                            };
                        });

                    sbpUI.methods.alertPositive('Deleted');

                });
            },
            handleCategoryChange(input){
                this.post({
                    roid: this.inspection.roid,
                    id: input.id,
                    comid: input.complaint.comid,
                    category: input.complaint.category,
                }, "/inspections/update-complaint-category.php").then((res) => {
                    console.log(res);
                });
            },
            handlePartsOrderingOpen(input) {
                this.activeInput = input;
                this.$refs['parts-ordering-modal'].open();
            },
            launchPartstech() {
                path = `<?= INTEGRATIONS ?>/partstech/main.php?existing=no&shopid=<?= $_COOKIE['shopid']?>&roid=${this.inspection.roid}&vin=${this.inspection.vh_vin}&inspid=${this.activeInput.id}`
                this.winPopUp(path)
            },
            launchRepairLink(rluid) {
                path = `<?= INTEGRATIONS ?>/repairlink/index.php?uid=`+ rluid +`&vin=${this.inspection.vh_vin}&entity=ro&entity_id=${this.inspection.roid}&inspid=${this.activeInput.id}`
                this.winPopUp(path)
            },
            showEpicor() {
                this.$refs['parts-ordering-modal'].close();
                this.$refs['epicor-modal'].open();
            },
            launchEpicor() {
                const supp = document.getElementById('epicorsupplier').value;
                const mode = document.getElementById('epicormode').value;

                path = `<?= INTEGRATIONS ?>/epicor/main.php?shopid=<?= $_COOKIE['shopid']?>&om=${mode}&vin=${this.report.VIN}&supp=${supp}&roid=${this.inspection.roid}&inspid=${this.activeInput.id}`
                this.winPopUp(path)
            },
            showNexpart() {
                this.$refs['parts-ordering-modal'].close();
                this.$refs['nexpart-modal'].open();
            },
            launchNexpart() {
                const supp = document.getElementById('nexpartsupplier').value;
                const rar = supp.split("|")
                un = rar[0]
                pw = rar[1]
                path = `http://www.nexpart.com/extpart.php?rev=4.0&clientversion=2.2&requesttype=launch&sms_form=X&provider=NEXLINKCSBTECH&pwd=${pw}&nexpartuname=${un}&vin=${this.report.VIN}&identifier=<?= $_COOKIE['shopid']?>-${this.inspection.roid}-${this.activeInput.id}&webpost=<?= SBP; ?>nexpart/return.php`
                console.log(path);
                this.winPopUp(path)
            },
            launchWorldpac() {
                path = `<?= INTEGRATIONS ?>/worldpac/main.php?shopid=<?= $_COOKIE['shopid']?>&roid=${this.inspection.roid}&inspid=${this.activeInput.id}`
                this.winPopUp(path)
            },
            winPopUp(URL) {
                h = screen.availHeight - 80
                w = screen.availWidth - 20
                str = 'addressbar=0,toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=yes,screenX=0,screenY=0,top=0,left=0,maximize=1,'
                str = str + 'height=' + h + ', width=' + w
                var mywin = window.open(URL, "ewin", str)
                wintimer = setInterval(() => {
                    if (mywin.closed) {
                        clearInterval(wintimer);
                        this.getPageData();
                        this.$refs['parts-ordering-modal'].close();
                    }
                }, 1000)
            },
            parseQueryString() {
                return window.location.search.substr(1).split('&').reduce((m, e) => {
                    const key = e.split('=')[0];
                    const value = e.split('=')[1];
                    m[key] = value;
                    return m;
                }, {});
            },
            post(data, endpoint) {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: 'POST',
                        url: `https://<?= ROOT ?>/src/private/components/inspections/apis${endpoint}`,
                        dataType: 'json',
                        data,
                        success: function (obj, textstatus) {
                            resolve(obj);
                        }
                    });
                });
            },
            get(data, endpoint) {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: 'GET',
                        url: `${endpoint}`,
                        dataType: 'json',
                        data,
                        success: function (obj, textstatus) {
                            resolve(obj);
                        }
                    });
                });
            }
        }
    });


    function closeProDemand() {
        $('.modal').modal('hide')
    }

    /*window.onload = function(){

      setInterval(function () {


     $('#addpartnum').autocomplete({source:'https://<?= ROOT ?>/src/private/components/inspections/apis/inspections/getparts.php', minLength:2,
     select: function( event, ui ) {
     }
   })

 },2000)
 }
*/
</script>