<script>
    Window.bus = new Vue();

    new Vue({
        el: '#dvi-inspection',
        components: sbpUI.components,
        data() {
            return {
                breakpoints: [],
                inspection: null,
                settingMultipleFindings: false,
                imageEditModal: false,
                quote: '"',
                activeFileCount: 0,
                customItem: {
                    sectionId: null,
                    categoryId: null,
                    loading: false
                },
                coverImageLoading: false,
            };
        },
        computed: {
            uploadUrl() {
                let url

                if (!this.inspection) return null;
                <?php if (defined('DEV')) { ?>
                url = `https://<?= ROOT ?>/src/private/components/inspections/apis/localupload.php?roid=${this.inspection.roid}`
                <?php } else { ?>
                url = `https://<?= ROOT ?>/src/private/components/inspections/apis/upload.php?roid=${this.inspection.roid}`;
                <?php } ?>

                return url;
            },
            imageBaseUrl() {
                let url = ''

                <?php if (!defined('DEV')) { ?>
                url = 'https://<?= $_SERVER['SERVER_NAME'] ?>';
                <?php } ?>

                return url;
            }
        },
        mounted() {

            // upon this component mounts, /inspections/get.php is called. the response (inspection) includes roid
            // so you can call get-ro.php again to get the vehicle information.
            /*
            this.post({ id: qs.id }, '/inspections/get-report.php').then((report) => {
              this.report = report;
              Vue.prototype.$current_report = report;
              const goodFindings = [];
            });*/
            this.getInspection();

            Window.bus.$on('change-finding-input', (itemId, ...params) => {

            });
        },
        methods: {
            getInspection(){
                Vue.prototype.$rootUrl = '<?= ROOT ?>';
                Vue.prototype.$isDev = "<?= DEV ?>";
                Vue.prototype.$imageBaseUrl = this.imageBaseUrl;
                //Vue.prototype.$addFindingPhoto = this.addFindingPhoto;
                const qs = this.parseQueryString();

                this.post({
                    id: qs.id
                }, '/inspections/get.php').then((inspection) => {
                    this.inspection = inspection;
                    Vue.prototype.$current_inspection = this.inspection;
                    setTimeout(() => {
                        document.querySelectorAll('.breakpoint-listener').forEach((el) => {
                            for (let index = el.children.length - 1; index >= 0; index -= 1) {
                                const child = el.children[index];
                                if (window.getComputedStyle(child).display === 'block') {
                                    this.breakpoints.push(child.className);
                                }
                            }
                        });

                        if (this.breakpoints.indexOf('lg') !== -1) {
                            document.getElementsByClassName('dvi-inspection-input-panel')[0].style = "margin-left: 240px;";
                        }

                        const el = document.getElementById('dvi-inspection-sidenav');
                        const sidenavInst = new mdb.Sidenav(el, {
                            mode: this.breakpoints.indexOf('lg') !== -1 ? 'push' : 'over',
                            position: 'absolute'
                        });
                        sidenavInst.show();

                        document.querySelectorAll('.vehicle-detail-input').forEach((formOutline) => {
                            new mdb.Input(formOutline).init();
                        });

                        document.querySelectorAll('.vehicle-detail-select').forEach((selectEl) => {
                            new mdb.Select(selectEl);
                            selectEl.addEventListener('valueChange.mdb.select', () => {
                                this.handleChangeVehicleDetails(selectEl.name, selectEl.value);
                            });
                        });
                    }, 0);
                });
            },
            range(start, end, length = end - start + 1) {
                return length > 0 ? Array.from({
                    length
                }, (_, i) => start + i) : []
            },
            handleToggleSidebar() {
                const el = document.getElementById('dvi-inspection-sidenav');
                const sidebarInst = mdb.Sidenav.getInstance(el);
                sidebarInst.show();
            },
            handleClickSidebarItem(anchor) {
                if (this.breakpoints.indexOf('lg') !== -1) {
                    document.getElementById(anchor).scrollIntoView({
                        behavior: 'smooth'
                    });
                } else {
                    const el = document.getElementById('dvi-inspection-sidenav');
                    const sidebarInst = mdb.Sidenav.getInstance(el);
                    sidebarInst.hide();

                    setTimeout(() => {
                        document.getElementById(anchor).scrollIntoView({
                            behavior: 'smooth'
                        });
                    }, 400);
                }
            },
            parseQueryString() {
                return window.location.search.substr(1).split('&').reduce((m, e) => {
                    const key = e.split('=')[0];
                    const value = e.split('=')[1];
                    m[key] = value;
                    return m;
                }, {});
            },
            post(data, endpoint) {
                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: 'POST',
                        url: `https://<?= ROOT ?>/src/private/components/inspections/apis${endpoint}`,
                        dataType: 'json',
                        data,
                        success: function (obj, textstatus) {
                            resolve(obj);
                        }
                    });
                });
            },
            handleAddPhoto() {
                this.$refs.fileInput.click();
            },

            handleFileChange() {
                const files = this.$refs.fileInput.files;
                const file = files[0];
                if (!file) return;

                const url = (window.URL || window.webkitURL).createObjectURL(files[0]);
                //alert(url);
                if (url == undefined)
                    return;

                this.$refs.provider.open({
                    url: url
                });
                this.$refs.provider.updator = (img) => {
                    this.coverImageLoading = true;

                    this.post({
                        id: this.inspection.id,
                        vh_photo: img
                    }, '/inspections/update.php', function () {

                    });
                    this.inspection.vehicle_photo_url = img;
                    this.$refs.provider.updator = undefined;
                }

                this.$refs.fileInput.value = null;
            },

            onImageLoaded() {
                this.coverImageLoading = false;
            },

            handleChangeVehicleDetails(field, value) {
                if (this.timeoutHandler2) {
                    clearTimeout(this.timeoutHandler2);
                    this.timeoutHandler2 = null;
                }
                this.timeoutHandler2 = setTimeout(() => {
                    this.post({
                        id: this.inspection.id,
                        [field]: value
                    }, '/inspections/update.php');
                }, 300);
            },
            handleSetCustomerRequest(note) {
                this.post({
                    id: this.inspection.id,
                    customer_request: note
                }, '/inspections/update.php');
            },
            handleSetVisitReason(reason) {
                this.post({
                    id: this.inspection.id,
                    visit_reason: reason
                }, '/inspections/update.php');
            },
            handleAddCustomerRequestImage(file) {

                if (!file) return;

                const url = (window.URL || window.webkitURL).createObjectURL(file);
                //alert(url);
                if (url == undefined)
                    return;

                this.$refs.provider.open({
                    url: url,
                    editable: true
                });
                let provider = this.$refs.provider;
                let that = this;
                this.$refs.provider.updator = (img) => {

                    if (provider.imgRef == undefined)
                        this.post({
                            id: this.inspection.id,
                            url: img
                        }, '/inspection-customer-request-images/create.php').then(images => this.$set(this.inspection, 'customer_request_images', images))
                    else if (provider.imgRef != undefined) {
                        this.post({
                            id: this.inspection.id,
                            url: img
                        }, '/inspection-customer-request-images/update.php').then(() => {
                        })
                    }
                    this.$refs.provider.updator = undefined;
                }

                this.$refs.fileInput.value = null;
            },
            handleDeleteCustomerRequestImage(image) {
                this.post({
                    id: image.id
                }, '/inspection-customer-request-images/delete.php').then(() => {
                    this.$set(this.inspection, 'customer_request_images', this.inspection.customer_request_images.filter((i) => i.id !== image.id));
                });
            },
            handleItemFindingInputChange(item, findingInputId, status, findingId, recommId, findingOther, recommOther) {
                const currentRequest = { status, findingId, recommId };
                this._lastStatusRequest = currentRequest;
                
                this.post({
                    inspectionId: this.inspection.id,
                    itemModelId: item.id,
                    itemInputId: item.item_input_id,
                    findingInputId,
                    status,
                    findingId,
                    recommId,
                    findingOther,
                    recommOther
                }, '/inspection-items/set-finding-input.php').then((res) => {
                    if (this._lastStatusRequest !== currentRequest) return;
                    
                    var currentItem = item.finding_inputs ? item.finding_inputs[0] : {};
                    var newItem = res.finding_inputs ? res.finding_inputs[0] : {};

                    if (status != 'none') {                                       
                        const updatedFindingInputs = item.finding_inputs ? 
                            item.finding_inputs.map(fi => 
                                fi.id === (newItem.id || currentItem.id) ? {...fi, ...newItem} : fi
                            ) : 
                            [{...currentItem, ...newItem}];
                        
                        this.$set(item, 'item_input_id', res.item_input_id);
                        this.$set(item, 'finding_inputs', updatedFindingInputs);
                    } else {
                        this.$set(item, 'item_input_id', res.item_input_id);
                        this.$set(item, 'finding_inputs', []);
                        
                        this.getInspection();
                    }
                });
            },
            handleItemSetMultipleFindings(item, findingIds, baseStatus) {
                this.settingMultipleFindings = true;
                this.post({
                    inspectionId: this.inspection.id,
                    itemModelId: item.id,
                    itemInputId: item.item_input_id,
                    findingIds,
                    baseStatus
                }, '/inspection-items/set-multiple-findings.php').then((res) => {
                    this.$set(item, 'finding_inputs', res);
                    this.settingMultipleFindings = false;
                });
            },
            handleItemMeasurementInputChange(item, value, which) {
                this.$set(item, `measurement${which}_input`, value);
                if (this.timeoutHandler1) {
                    clearTimeout(this.timeoutHandler1);
                    this.timeoutHandler1 = null;
                }
                this.timeoutHandler1 = setTimeout(() => {
                    this.post({
                        inspectionId: this.inspection.id,
                        itemModelId: item.id,
                        itemInputId: item.item_input_id,
                        input: value,
                        which
                    }, '/inspection-items/set-measurement-input.php').then((res) => {
                        this.$set(item, 'item_input_id', res);
                    });
                }, 300);
            },
            handleAddFindingPhoto(item, findingInputId, files, reset = 'true') {
                if (reset == 'true') {
                    files = Array.from(files)
                    this.activeFileCount = 0;
                }

                const url = (window.URL || window.webkitURL).createObjectURL(files[this.activeFileCount]);
                this.$refs.provider.open({
                    url: url,
                    editable: true
                });
                let that = this;
                this.$refs.provider.updator = (img) => {
                    // Clear the updator reference first to prevent any race conditions
                    const currentUpdator = this.$refs.provider.updator;
                    this.$refs.provider.updator = undefined;

                    if (that.imgRef == undefined) {
                        // Show loading indicator
                        sbpUI.methods.alertPositive('Saving image...');

                        this.post({
                            inspectionId: this.inspection.id,
                            findingInputId,
                            imageUrl: img
                        }, '/inspection-items/add-finding-image.php').then((res) => {
                            if (res == null || res == undefined) {
                                sbpUI.methods.alertNegative('Failed to save image');
                                return;
                            }

                            // Create a copy of the finding inputs to avoid reference issues
                            const updatedFindingInputs = item.finding_inputs.map((fi) => {
                                if (fi.id === findingInputId) {
                                    // Create a new array of images to ensure Vue detects the change
                                    const updatedImages = fi.images ? [...fi.images, res] : [res];
                                    return { ...fi, images: updatedImages };
                                }
                                return fi;
                            });

                            // Update the item with the new finding inputs
                            this.$set(item, 'finding_inputs', updatedFindingInputs);

                            // Process the next image if available
                            this.activeFileCount++;
                            if (files[this.activeFileCount] !== undefined) {
                                setTimeout(() => {
                                    this.handleAddFindingPhoto(item, findingInputId, files, 'false');
                                }, 500); // Add a small delay between processing images
                            } else {
                                sbpUI.methods.alertPositive('All images uploaded successfully');
                            }
                        }).catch(error => {
                            console.error('Error uploading image:', error);
                            sbpUI.methods.alertNegative('Error uploading image');
                        });
                    } else {
                        this.post({
                            id: findingInputId,
                            imageUrl: img
                        }, '/inspection-items/update-finding-image.php').then(() => {
                            sbpUI.methods.alertPositive('Image updated successfully');
                        }).catch(error => {
                            console.error('Error updating image:', error);
                            sbpUI.methods.alertNegative('Error updating image');
                        });
                    }
                }

                this.$refs.fileInput.value = null;

            },
            handleAddFindingVideo(item, findingInputId, files) {

                const fileSize = files[0].size
                const maxFileSize = 1024 * 1024 * 1000;

                if (fileSize > maxFileSize) {
                    sbpUI.methods.alertNegative('File is too large. Maximum file size is 1GB.');
                    return;
                }

                var formData = new FormData();
                formData.append('file', files[0]);
                formData.append('inspectionId', this.inspection.id);
                formData.append('findingInputId', findingInputId);
                sbpUI.methods.alertPositive('Video Is Processing. Please wait it will be ready soon.');

                new Promise((resolve, reject) => {
                    $.ajax({
                        type: 'POST',
                        url: `https://<?= ROOT ?>/src/private/components/inspections/apis/inspection-items/add-finding-video.php`,
                        data: formData,
                        contentType: false,
                        processData: false,
                        cache: false,
                        dataType: 'json',
                        success: function (obj, textstatus) {
                            resolve(obj);
                        },
                        error: function (xhr, textStatus, errorThrown) {
                            console.error(xhr.responseText); // Display the error response
                        }
                    });
                }).then((res) => {
                    if (res == null || res == undefined )
                        return;

                    if ( !res.status && typeof res.status !== 'undefined' ){
                        sbpUI.methods.alertNegative(res.message);
                        return;
                    }
                    
                    this.$set(item, 'finding_inputs', item.finding_inputs.map((fi) => ({
                        ...fi,
                        images: (fi.id === findingInputId ? (fi.images ? fi.images.concat(res) : [res]) : fi.images)
                    }))); 
                });
            },
            handleAddRoImages(item, findingInputId, imgs) {
                if (!imgs || imgs.length === 0) {
                    sbpUI.methods.alertNegative('No images selected');
                    return;
                }

                // Show loading indicator
                sbpUI.methods.alertPositive('Importing images...');

                this.post({
                    findingInputId,
                    images: JSON.stringify(imgs)
                }, '/inspection-items/add-ro-images.php').then((res) => {
                    if (res == null || res == undefined || res == "") {
                        sbpUI.methods.alertNegative('Failed to import images');
                        return;
                    }

                    // Create a copy of the finding inputs to avoid reference issues
                    const updatedFindingInputs = item.finding_inputs.map((fi) => {
                        if (fi.id === findingInputId) {
                            // Create a new array of images to ensure Vue detects the change
                            const updatedImages = fi.images ? [...fi.images, ...res] : [...res];
                            return { ...fi, images: updatedImages };
                        }
                        return fi;
                    });

                    // Update the item with the new finding inputs
                    this.$set(item, 'finding_inputs', updatedFindingInputs);
                    sbpUI.methods.alertPositive('Images imported successfully');
                }).catch(error => {
                    console.error('Error importing images:', error);
                    sbpUI.methods.alertNegative('Error importing images');
                });

            },
            handleDeleteFindingPhoto(item, findingInputId, image) {
                let inputType = image.type == 'vid' 
                    ? "video" 
                    : "image"
                // Show loading indicator
                sbpUI.methods.alertPositive(`Deleting ${inputType}...`);

                this.post({
                    id: image.id
                }, '/inspection-items/delete-finding-image.php').then((res) => {
                    if (res.status) {
                        // Create a copy of the finding inputs to avoid reference issues
                        const updatedFindingInputs = item.finding_inputs.map((fi) => {
                            if (fi.id === findingInputId) {
                                // Filter out the deleted image
                                const updatedImages = fi.images ? fi.images.filter((i) => i.id !== image.id) : [];
                                return { ...fi, images: updatedImages };
                            }
                            return fi;
                        });

                        // Update the item with the new finding inputs
                        this.$set(item, 'finding_inputs', updatedFindingInputs);
                        sbpUI.methods.alertPositive(`${this.capitalizeFirstLetter(inputType)} deleted successfully`);
                    } else {
                        sbpUI.methods.alertNegative(`Failed to delete ${inputType}`);
                    }
                }).catch(error => {
                    console.error(`Error deleting ${inputType}:`, error);
                    sbpUI.methods.alertNegative(`Error deleting ${inputType}`);
                });

            },
            capitalizeFirstLetter(val) {
                return String(val).charAt(0).toUpperCase() + String(val).slice(1);
            },
            handleAddCustom() {
                const itemname = document.getElementById('customitem').value;
                if (itemname == '') {
                    sbpUI.methods.alertNegative('Item name cannot be empty');
                    return;
                }

                this.customItem.loading = true;

                this.post({
                    inspectionId: this.inspection.id,
                    secId: this.customItem.sectionId,
                    catId: this.customItem.categoryId,
                    name: itemname,
                }, '/inspection-items/add-custom-item.php').then((res) => {

                    const categories = this.inspection.model.categories.map((c) => ({
                        ...c,
                        sections: (c.sections || []).map((sec) => ({
                            ...sec,
                            items: sec.id === this.customItem.sectionId ? (sec.items || []).concat(res) : sec.items
                        }))
                    }))

                    this.inspection.model.categories = categories;
                    this.customItem.loading = false;
                    document.getElementById('customitem').value = '';
                    this.$refs['custom-item-modal'].close();

                });
            },
            addCustomOpen(catId, secId) {
                this.customItem.categoryId = catId;
                this.customItem.sectionId = secId;
                this.$refs['custom-item-modal'].open();
            },
            handleAddTechNotes(item, findingInputId, text) {
                this.post({
                    id: findingInputId,
                    technician_notes: text
                }, '/inspection-items/update.php').then(() => {
                    this.$set(item, 'finding_inputs', item.finding_inputs.map((fi) => ({
                        ...fi,
                        technician_notes: fi.id === findingInputId ? text : fi.technician_notes
                    })));
                })
            }
        }
    });
</script>

