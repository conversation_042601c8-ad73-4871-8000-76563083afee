<script>
  const MONTH_NAMES = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  function getFormattedDate(date, prefomattedDate = false, hideYear = false) {
    const day = date.getDate();
    const month = MONTH_NAMES[date.getMonth()];
    const year = date.getFullYear();
    const hours = date.getHours();
    let minutes = date.getMinutes();

    if (minutes < 10) {
      // Adding leading zero to minutes
      minutes = `0${ minutes }`;
    }

    if (prefomattedDate) {
      // Today at 10:20
      // Yesterday at 10:20
      return `${ prefomattedDate } at ${ hours }:${ minutes }`;
    }

    if (hideYear) {
      // 10. January at 10:20
      return `${ day }. ${ month } at ${ hours }:${ minutes }`;
    }

    // 10. January 2017. at 10:20
    return `${ day }. ${ month } ${ year }. at ${ hours }:${ minutes }`;
  }

  function timeAgo(dateParam) {
    if (!dateParam) {
      return null;
    }

    const date = typeof dateParam === 'object' ? dateParam : new Date(dateParam);
    const DAY_IN_MS = 86400000; // 24 * 60 * 60 * 1000
    const today = new Date();
    const yesterday = new Date(today - DAY_IN_MS);
    const seconds = Math.round((today - date) / 1000);
    const minutes = Math.round(seconds / 60);
    const isToday = today.toDateString() === date.toDateString();
    const isYesterday = yesterday.toDateString() === date.toDateString();
    const isThisYear = today.getFullYear() === date.getFullYear();


    if (seconds < 5) {
      return 'now';
    } else if (seconds < 60) {
      return `${ seconds } seconds ago`;
    } else if (seconds < 90) {
      return 'about a minute ago';
    } else if (minutes < 60) {
      return `${ minutes } minutes ago`;
    } else if (isToday) {
      return getFormattedDate(date, 'Today'); // Today at 10:20
    } else if (isYesterday) {
      return getFormattedDate(date, 'Yesterday'); // Yesterday at 10:20
    } else if (isThisYear) {
      return getFormattedDate(date, false, true); // 10. January at 10:20
    }

    return getFormattedDate(date); // 10. January 2017. at 10:20
  }

  new Vue({
    el: '#dvi-dashboard',
    components: sbpUI.components,
    data() {
      return {
        techs: [],
        selectedTechs: [],
        inspections: [],
        messageChannels: [],
        selectedChannel: null,
        selectedChannelMessages: [],
        cannedMessages:[],
        unreadMessages:[],
        roIds:[],
        messageInput:'',
        msgsending: false,
        cannedShow:false,
        inspectionsLoaded: false,
        activeInspectionId : '',
        history: false
      };
    },
    filters: {
      timeSince: function (value) {
        return timeAgo(value);
      },
      date: function (value) {
        const date = new Date(value);
        const month = date.getMonth() + 1;
        const day = date.getDate();

        return `${month < 10 ? `0${month}` : month}/${day < 10 ? `0${day}` : day}/${date.getFullYear()}`;
      }
    },
    computed:{
        imageBaseUrl() {
        let url = ''

        <?php
          if (!defined('DEV')) {
        ?>
          url = 'https://shopbosspro.com';
        <?php
          }
        ?>

        return url;
      },
    },
    mounted() {
      const qs = this.parseQueryString();
      if (Object.keys(qs).includes('roid')) {
        console.log(qs.roid)
        this.getInspections(qs.roid)
      } else {
        this.post(null, '/inspections/getall.php').then((inspections) => {
          this.inspections = inspections;
          this.inspectionsLoaded = true;
        });
      }

      this.post(null, '/messages/get-channels.php').then((channels) => {
        this.messageChannels = channels;
        for(const channel of channels)
        this.roIds.push(channel.roid);
      });

      this.post(null, '/get-techs.php').then((res) => {
        this.techs = res.techs;
      });

      this.loadCannedMessages();

      document.getElementById('techsFilterSelect').addEventListener('valueChange.mdb.select', () => {
        setTimeout(() => {
          this.getInspections();
        }, 0);
      });

      document.getElementById('messageModal').addEventListener('hidden.bs.modal', () => {
        this.selectedChannel = null;
        this.selectedChannelMessages = null;
      });


      setInterval(this.checkMessages,2000);
    },

    methods: {
      post(data, endpoint) {
        return new Promise((resolve, reject) => {
          $.ajax({
            type: 'POST',
            url: `https://<?= ROOT ?>/src/private/components/inspections/apis${endpoint}`,
            dataType: 'json',
            data,
            success: function (obj, textstatus) {
              resolve(obj);
            }
          });
        });
      },
      async loadCannedMessages(){
        try {
           const response = await this.post(null, '/canned-messages/getall.php')
           this.cannedMessages = response;
        } catch (error) {
          console.log(error)
        }
      },
      handleUnreadCount(roid){
        const msgs = this.unreadMessages.filter((m) => m.roid==roid);
        if(msgs.length>0)
        return msgs.length;
        else return "";
      },
      checkMessages()
      {
       this.post({roids:JSON.stringify(this.roIds)}, '/messages/check-incoming-messages.php').then((messages) => {
       this.unreadMessages = [];

        if(messages.length>0)
        {
          let selroid = '';
          for(const msg of messages)
          {
            if(this.selectedChannel && this.selectedChannel.roid==msg.roid && !this.selectedChannelMessages.some(data => data.id == msg.id))
            {
            this.selectedChannelMessages.unshift(msg);
            selroid = msg.roid;
            }
            this.unreadMessages.push(msg);
          }
          if(selroid!='')
          this.markAsRead(selroid);
        }

        });
      },
      getInspections(roid) {
        let filters = {
          search: null,
          techs: this.selectedTechs.join(","),
          history: (this.history?'yes':null)
        };
        if (!roid) {
          const el = document.getElementById('searchInspection');
          filters = {
            ...filters,
            search: el.value
          };
        } else {
          filters = {
            ...filters,
            search: roid,
          };
        }
        this.post(filters, '/inspections/getall.php').then((inspections) => {
          this.inspections = inspections;
          this.inspectionsLoaded = true;
        });
      },
      loadHistory(roid) {
        this.inspectionsLoaded = false;
        this.history = true
        let filters = {
          search: null,
          techs: null,
          history: 'yes'
        };
          this.post(filters, '/inspections/getall.php').then((inspections) => {
          this.inspections = inspections;
          this.inspectionsLoaded = true;
        });
      },
      handleSearch() {
        if (this.searchTimeout) {
          clearTimeout(this.searchTimeout);
        }
        this.searchTimeout = setTimeout(() => {
          this.getInspections();
        }, 300);
      },
      handleOpenMessageModal() {
        this.selectedChannel = this.messageChannels[0];
        this.openMessageModal();
      },
      handleOpenInspectionMessageModal(roid) {
        this.selectedChannel = this.messageChannels.find((mc) => mc.roid === roid);
        /*if (selectedChannel) {
          this.selectedChannel = selectedChannel;
        } else {
          const { id, ..._inspection } = inspection;
          this.selectedChannel = {
            inspectionId: inspection.id,
            ..._inspection
          };
          this.$set(this, 'messageChannels', this.messageChannels.concat(this.selectedChannel))
        }*/
        this.openMessageModal();
      },
      openMessageModal() {
        const el = document.getElementById('messageModal');
        const modal = new mdb.Modal(el);
        modal.show();
        this.loadMessages();

        setTimeout(() => {
          const messageInputEl = document.getElementById('messageInput');
          new mdb.Input(messageInputEl).update();
        }, 0);
      },
      loadMessages() {
        if (!this.selectedChannel) return;

        this.post({ roid: this.selectedChannel.roid }, '/messages/get-channel-messages.php').then((messages) => {
          this.selectedChannelMessages = messages;
          this.markAsRead(this.selectedChannel.roid);
        });
      },
      handleLoadRoMessages(roid) {
       this.cannedShow = false;
       this.messageInput = '';
       this.selectedChannel = this.messageChannels.find((m) => m.roid == roid);
       this.loadMessages();
       setTimeout(() => {
          const messageInputEl = document.getElementById('messageInput');
          new mdb.Input(messageInputEl).update();
        }, 0);
      },
      handleSendMessage(){
      if(this.messageInput=='')
      {
        sbpUI.methods.alertNegative('Text cannot be empty');
        return;
      }
      this.msgsending = true;
      this.post({ roid: this.selectedChannel.roid,message:this.messageInput,cellphone:this.selectedChannel.customerCellPhone }, '/messages/send-message.php').then((r) => {
          this.messageInput = '';
          this.selectedChannelMessages.unshift(r);
          this.msgsending = false;
        });
      },
      markAsRead(roid){
        //console.log(roid);
       this.post({ roid: roid }, '/messages/mark-read.php');
      },
      handleShowCanned(){
       this.cannedShow = true;
       this.$refs.cannedmsg = '';
      },
      handleSelectCanned(event){
      const cid = event.target.value;
       if(cid!='')
       {
        const canned = this.cannedMessages.find((c) => c.id==cid);
        this.messageInput = canned.message;
        setTimeout(() => {
          const messageInputEl = document.getElementById('messageInput');
          new mdb.Input(messageInputEl).update();
        }, 0);
       }
       else
       this.messageInput = '';

       this.cannedShow = false;
      },
      handleDeleteInspection(inspid)
      {
        this.activeInspectionId = inspid;
        this.$refs['confirm-delete-inspection'].open();
      },
      handleConfirmDelete()
      {
        this.post({ id: this.activeInspectionId }, '/inspections/delete.php').then((r) => {
        this.inspections = this.inspections.filter((i) => i.id !== this.activeInspectionId);
        this.activeInspectionId = '';
        });
      },
       parseQueryString() {
        return window.location.search.substr(1).split('&').reduce((m, e) => {
          const key = e.split('=')[0];
          const value = e.split('=')[1];
          m[key] = value;
          return m;
        }, {});
      },
    }
  });

// const opt = {
//     'tourTitle': 'BOSS Inspect Dashboard',
//     'steps': [
//         {
//             'title': 'Search Inspection',
//             'content': 'You can search by VIN, RO, Technician, Service Advisor, Vehicle and much more!',
//             'target': '#searchInspection'
//         },
//         {
//             'title': 'Filter by Technician',
//             'content': 'You can filter inspections by Technician',
//             'target': '#techfilter'
//         },
//         {
//             'title': 'Create a new inspection',
//             'content': 'You can create a new inspection from here',
//             'target': '#newinspection'
//         },
//
//         {
//             'title': 'Messages',
//             'content': 'View all messages (email and text) for your active inspections here',
//             'target': '#messages'
//         },
//
//         {
//             'title': 'Inspection Manager',
//             'content': 'Manage all of your inspections here',
//             'target': '#inspectionmanager'
//         },
//
//         {
//             'title': 'Settings',
//             'content': 'Manage inspection settings here',
//             'target': '#settings'
//         },
//         {
//             'title': 'Inspection Details',
//             'content': 'Information regarding the insepction',
//             'target': '#inspectiondetails'
//         },
//
//         {
//             'title': 'View Inspection',
//             'content': 'View Inspection for this vehicle',
//             'target': '#viewinspection'
//         },
//
//         {
//             'title': 'View Repair Order',
//             'content': 'View Repair Order in Shop Boss',
//             'target': '#viewro'
//         },
//
//         {
//             'title': 'Inspection Messages',
//             'content': 'View messages for this specific inspection',
//             'target': '#inspectionmessages'
//         }
//
//     ]
// };

// window.onload = function(){
//     iGuider('button',opt);
// };
</script>

<style scoped>
  .dvi-items {
    font-size: 14px;
    padding: 1px;
  }
  .dvi-icon-button {
    font-size: 18px;
    font-weight: 600;
    padding: 2px;
  }
  .items {
    padding: 20px;
  }
  .no-border {
    border: none !important;
  }
  .img-thumbnail, .list-group-item {
    border: 0px;
  }
  .align-vertically {
    margin-top: 35px;
  }
  .dvi-center-vert {
    position: relative;
    margin: auto;
    min-height: 150px;
  }
  .dvi-center {
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
  }
  .car {
    min-width: 150px;
    max-width: 150px;
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
  }
  @media (min-width: 0px) {
    .navbar {
      -webkit-box-shadow: none !important;
      background-color: #eeeeee;
    }
  }
  .col-8 {
    width: 100%;
  }
  .dvi-body {
    padding-top: 125px !important;
  }
  .dvi-row {
    margin-left: 1px;
    margin-right: 1px;
  }
  .dvi-buttons {
    padding-bottom: 20px;
  }
  .input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }
  @media (min-width: 768px) {
    .col-8 {
      width: 100%;
    }
    .navbar {
      -webkit-box-shadow: none !important;
      background-color: #eeeeee;
    }
    .dvi-row {
      margin-left: 20px;
      margin-right: 20px;
    }
    .dvi-body {
      padding-top: 100px !important;
    }
  }
  /* // Large devices (desktops, 992px and up) */
  @media (min-width: 1092px) {
    .col-8 {
      width: 90%;
    }
    .navbar {
      -webkit-box-shadow: none !important;
      background-color: #eeeeee;
    }
    .dvi-body {
      padding-top: 100px;
    }
    .dvi-row {
      margin-left: 20px;
      margin-right: 20px;
    }
  }
  @media (min-width: 1276px) {
    .col-8 {
      width: 90%;
    }
    .navbar {
      -webkit-box-shadow: none !important;
      background-color: #eeeeee;
    }
    .dvi-container {
      margin-top: 20px;
      margin-bottom: 20px;
      margin-left: 50px;
      margin-right: 50px;
    }
    .dvi-row {
      margin-left: 80px;
      margin-right: 80px;
    }
  }
  /* // Extra large devices (large desktops, 1200px and up) */
  @media (min-width: 1736px) {
    .col-8 {
      width: 66.6%;
    }
    .dvi-container {
      margin-top: 20px;
      margin-bottom: 20px;
      margin-left: 250px;
      margin-right: 250px;
    }
    .dvi-icon {
      color: gray;
    }
    .navbar {
      -webkit-box-shadow: none !important;
      background-color: #eeeeee;
    }
    .dvi-row {
      margin-left: 100px;
      margin-right: 100px;
    }
  }
  input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }
  #del-icon {
    position: absolute;
    left: 5px;
    cursor: pointer;
    font-size: 19px;
    font-weight: bold;
  }
</style>
