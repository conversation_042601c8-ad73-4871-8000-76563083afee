<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap"/>

<style>
    :root{
        --base-color: var(--textColor);
        --shadow: 0 4px 8px -2px rgba(9, 30, 66, 0.31), 0 0 1px rgba(9, 30, 66, 0.31);
    }
    .ai-container{
        position: absolute;
        width: 100%;
    }

    .ai-tool{
        position: relative;
        margin: 0 auto;
        background-color: var(--bodybackground);
        box-shadow: var(--shadow);
        border-radius: 12px;
        z-index: 110;
        margin-top: 10px;
    }

    .ai-tool .header {
        display: flex;
        justify-content: space-between;
        color: var(--base-color);
        padding: 6px 12px;
        border-bottom: 1px solid var(--base-color);
    }

    .ai-tool .ai-result{
        padding: 6px 12px;
    }

    .ai-tool .menu-item,
    .ai-tool .menu-item-wo {
        display: grid;
        grid-template-columns: 30px 1fr;
        align-items: center;
        position: relative;
        padding-right: 20px;
        padding: 6px 12px;
        color: var(--base-color);
        cursor: pointer;
    }

    .ai-tool p,
    .ai-tool .ai-footer,
    .ai-tool .typing-animation{
        color: var(--base-color);
    }

    .ai-tool .menu-item::after {
        content: "";
        position: absolute;
        right: 10px;
        width: 0;
        height: 0;
        border-top: 5px solid transparent;
        border-left: 8px solid var(--base-color);
        border-bottom: 5px solid transparent;
        transition: transform 0.3s ease;
    }

    .ai-tool .menu-item:hover::after {
        transform: translateX(5px);
    }

    .ai-tool .menu-item:hover,
    .ai-tool .menu-item-wo:hover {
        background-color:rgba(223, 223, 223, 0.05);
    }

    .ai-tool .fas,
    .ai-tool .fa-solid {
        color: var(--base-color);
    }

    .ai-tool .ai-footer{
        margin-top: 10px;
        border-top: 1px solid var(--base-color);
    }

    .ai-tool .ai-footer span{
        font-size: 12px;
        padding: 6px 12px;
    }

    .fa-microchip-ai{
        font-size: 22px;
    }

    .menu__tone, .menu__translate {
        display: none;
    }

    .textarea-wrapper {
        position: relative;
        border-radius: 12px;
        border: 2px solid #e0e0e0;
        transition: border-color 0.3s ease-in-out;
    }

    .textarea-wrapper.loading {
        border: 2px solid transparent;
        background: linear-gradient(90deg, red, orange, yellow, green, cyan, blue, violet);
        background-size: 200% 200%;
        animation: rainbow-border 2s linear infinite;
    }

    @keyframes rainbow-border {
        0% { background-position: 0% 50%; }
        100% { background-position: 100% 50%; }
    }

    .loader-overlay {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        justify-content: center;
        align-items: center;
        font-size: 16px;
        font-weight: bold;
        color: #333;
        border-radius: 8px;
    }

    .dropdown-menu {
        width: auto;
        position: absolute !important;
    }

    .ai-button{
      font-size: .9rem;
      border: 1px solid var(--primary);
      color: var(--primary);
      transition: .3s;
    }

    .ai-button i{
        transform: translateY(2px);
    }
    
    .ai-button:hover{
        background-color: var(--primary) !important;
        color: #fff !important;
    }

    .ai-button:hover i{
        color: #fff !important;
    }

    .ai-tool .buttons{
        user-select: none;
        -ms-user-select: none;
        -webkit-user-select: none;
    }

    .ai-tool .buttons span{
        cursor: pointer;
    }

    .ai-tool.loading {
        position: relative;
        border: 1px solid transparent;
    }

    .ai-tool.loading::before {
        content: "";
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        border-radius: 12px;
        border: 1px solid transparent;
        pointer-events: none;
        z-index: 1;
        background: linear-gradient(90deg, red, orange, yellow, green, cyan, blue, violet);
        background-size: 200% 200%;
        animation: rainbow-border 2s linear infinite;
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
        -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
    }

    @keyframes rainbow-border {
        0% { background-position: 0% 50%; }
        100% { background-position: 100% 50%; }
    }


    @keyframes rainbow-border {
        0% { background-position: 0% 50%; }
        100% { background-position: 100% 50%; }
    }

    .typing-animation {
        display: inline-flex;
        align-items:center;
        justify-content: center;
        gap: 4px;
    }

    .typing-animation span {
        width: 8px;
        height: 8px;
        background-color: #007BFF;
        border-radius: 50%;
        display: inline-block;
        animation: bounce 1.5s infinite ease-in-out;
        box-shadow: 0 0 10px rgba(0, 123, 255, 0.6);
    }

    .typing-animation span:nth-child(1) {
        animation-delay: 0s;
    }
    .typing-animation span:nth-child(2) {
        animation-delay: 0.2s;
    }
    .typing-animation span:nth-child(3) {
        animation-delay: 0.4s;
    }

    @keyframes bounce {
        0%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-6px);
        }
    }

    #partform{
        margin-bottom: 360px;
    }

    .ai__fade-in {
        animation: aiFadeIn 0.1s ease-out;
    }

    @keyframes aiFadeIn {
        0% {
            opacity: 0.2;
            transform: scale(0.6);
        }
        100% {
            opacity: 1;
            transform: scale(1);
        }
    }

    .ai__fade-out {
        animation: aiFadeOut 0.1s ease-in forwards;
    }

    @keyframes aiFadeOut {
        0% {
            opacity: 1;
            transform: scale(1);
        }
        100% {
            opacity: 0;
            transform: scale(0);
            display: none;
        }
    }
</style>

<script>
    document.addEventListener("DOMContentLoaded", () => {
        // Define Global Variables
        const aiBaseUrl = window.location.origin + '/src/public/js/plugins/ai-writing-tool/index.php';

        // AI Permission Checker
        const shopIsHavingAIAccess = <?php echo ($aiSettingsActive && $aiBetaFeatureActive) ? 'true' : 'false'; ?>;
        
        const aiToolSelector = '.ai-tool';
        const aiResultSelector = '#aiSuggestion';
        const aiButtonSelector = '.ai-button';
        const aiActionSelector = '[data-action]';
        const changeToneBtnSelector = '#changeToneBtn';
        const translateToBtnSelector = '#translateToBtn';
        const backBtnSelector = '#backBtn';
        const backBtn2Selector = '#backBtn2';
        const menuBasicSelector = '.menu__basic';
        const menuToneSelector = '.menu__tone';
        const menuTranslateSelector = '.menu__translate';
        const typingAnimationSelector = '.typing-animation span';

        document.querySelectorAll("textarea[ai-writing-tool]").forEach(textarea => {
            // Create loader and add AI button
            const loader = createLoader();
            textarea.parentNode.insertBefore(loader, textarea);

            // Buttons
            const buttonsFather = createButtonsFatherElement();
            const aiButton = createAiButton();
            const speechRecognitionButton = createSpeechRecognitionButton();
            const textareaParent = textarea.parentNode;

            // Insert Buttons
            shopIsHavingAIAccess && buttonsFather.appendChild(aiButton);
            buttonsFather.appendChild(speechRecognitionButton);
            textareaParent.parentNode.insertBefore(buttonsFather, textareaParent);

            // Create suggestion box
            const suggestionBox = createSuggestionBox();
            textarea.parentNode.insertBefore(suggestionBox, textarea.nextSibling);

            let AIAction = 'summarize';

            // Handle x header action
            suggestionBox.querySelector('.closeBTN').addEventListener('click', function(e){
                const aiTool = suggestionBox.querySelector(aiToolSelector);

                aiTool.classList.remove('ai__fade-in');
                aiTool.classList.add('ai__fade-out');
                
                setTimeout(() => {
                    hideAIMenu(suggestionBox)
                }, 100);
            });

            // Handle Previus header action
            suggestionBox.querySelector('.firstStepBTN').addEventListener('click', function(e){
                hideAIMenu(suggestionBox)
                suggestionBox.querySelector('.ai-tool').style.display = 'block';
            });
            

            // Handle menu actions
            suggestionBox.querySelector('#menu').addEventListener('click', function(e){
                const action = e.target.closest('[data-action]')?.getAttribute('data-action');
                const changeToneBtn = suggestionBox.querySelector('#changeToneBtn');
                const translateToBtn = suggestionBox.querySelector('#translateToBtn');
                const backBtn = suggestionBox.querySelector('#backBtn');
                const backBtn2 = suggestionBox.querySelector('#backBtn2');
                const menuBasic = suggestionBox.querySelector('.menu__basic');
                const menuTone = suggestionBox.querySelector('.menu__tone');
                const menuTranslate = suggestionBox.querySelector('.menu__translate');

                if (action) {
                    AIAction = action;

                    suggestionBox.querySelector('.ai-tool').classList.add('loading');
                    suggestionBox.querySelector('#aiSuggestion').innerHTML="<p class='typing-animation'><span></span><span></span><span></span>Generating</p>";

                    aiGenerate(action).then(() => {
                        suggestionBox.querySelector('.ai-tool').classList.remove('loading');
                    });

                    // Hide the basic menu and show the result
                    menuBasic.style.display = 'none';
                    menuTone.style.display = 'none';
                    menuTranslate.style.display = 'none';
                    suggestionBox.querySelector('.ai-result').style.display = 'block';
                }

                // Show tone options
                if (e.target.closest('#changeToneBtn')) {
                    menuBasic.style.display = 'none';
                    menuTone.style.display = 'block';
                    menuTranslate.style.display = 'none';
                }

                // Show translation options
                if (e.target.closest('#translateToBtn')) {
                    menuBasic.style.display = 'none';
                    menuTranslate.style.display = 'block';
                    menuTone.style.display = 'none';
                }

                // Back to basic menu from tone submenu
                if (e.target.closest('#backBtn')) {
                    menuTone.style.display = 'none';
                    menuBasic.style.display = 'block';
                }

                // Back to basic menu from translate submenu
                if (e.target.closest('#backBtn2')) {
                    menuTranslate.style.display = 'none';
                    menuBasic.style.display = 'block';
                }
            });


            // AI generation function
            async function aiGenerate(action) {
                try {
                    const response = await fetch(aiBaseUrl, {
                        method: "POST",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify({ user_text: textarea.value, user_action: action }),
                    });
                    const data = await response.json();
                    suggestionBox.querySelector(aiResultSelector).textContent = data.content || "Please insert text.";
                } catch (error) {
                    suggestionBox.querySelector(aiResultSelector).textContent = "Error: Unable to fetch AI suggestion.";
                    console.log(error)
                }
            }

            // Handle Replace action
            suggestionBox.querySelector(".replace").addEventListener("click", (event) => {
                event.preventDefault();

                hideAIMenu(suggestionBox)
                handleReplaceAction(suggestionBox, textarea);
            });

            // Handle Insert Below action
            suggestionBox.querySelector(".insert").addEventListener("click", (event) => {
                event.preventDefault();

                hideAIMenu(suggestionBox)
                handleInsertAction(suggestionBox, textarea);
            });


            // Show or hide AI tool
            aiButton.addEventListener('click', function () {
                hideAIMenuAll()
                toggleAiToolVisibility(suggestionBox);
            });

            // Helper Functions

            // Show loading effect
            function showLoading(suggestionBox) {
                suggestionBox.querySelector(aiToolSelector).classList.add('loading');
                suggestionBox.querySelector(aiResultSelector).innerHTML = "<p class='typing-animation'><span></span><span></span><span></span>Generating</p>";
            }

            // Hide loading effect
            function hideLoading(suggestionBox) {
                suggestionBox.querySelector(aiToolSelector).classList.remove('loading');
            }

            // Handle Replace action logic
            function handleReplaceAction(suggestionBox, textarea) {
                const aiText = suggestionBox.querySelector(aiResultSelector).textContent.trim();
                if (aiText !== 'AI suggestion will appear here.') {
                    textarea.value = aiText;
                } else {
                    sbalert('Firstly You need to click Process!');
                }
            }

            // Handle Insert Below action logic
            function handleInsertAction(suggestionBox, textarea) {
                const aiText = suggestionBox.querySelector(aiResultSelector).textContent.trim();
                if (aiText !== 'AI suggestion will appear here.') {
                    textarea.value += `\n\n${aiText}`;
                } else {
                    sbalert('Firstly You need to click Process!');
                }
            }

            // Toggle AI tool visibility
            function toggleAiToolVisibility(suggestionBox) {
                const aiTool = suggestionBox.querySelector(aiToolSelector);
                
                aiTool.classList.add('ai__fade-in');
                aiTool.classList.remove('ai__fade-out');

                aiTool.style.display = 'block';
            }


            function hideAIMenu(suggestionBox){
                const aiTool = suggestionBox.querySelector('.ai-tool');
                const menuBasic = suggestionBox.querySelector('.menu__basic');
                const menuTone = suggestionBox.querySelector('.menu__tone');
                const menuTranslate = suggestionBox.querySelector('.menu__translate');
                const aiResult = suggestionBox.querySelector('.ai-result');
            
                aiTool.style.display = 'none'
                menuBasic.style.display = 'block';
                menuTranslate.style.display = 'none';
                menuTone.style.display = 'none';
                aiResult.style.display = 'none';
            }

            function hideAIMenuAll() {
                const aiTools = document.querySelectorAll('.ai-tool');
                const menuBasics = document.querySelectorAll('.menu__basic');
                const menuTones = document.querySelectorAll('.menu__tone');
                const menuTranslates = document.querySelectorAll('.menu__translate');
                const aiResults = document.querySelectorAll('.ai-result');

                aiTools.forEach(aiTool => aiTool.style.display = 'none');
                menuBasics.forEach(menuBasic => menuBasic.style.display = 'block');
                menuTranslates.forEach(menuTranslate => menuTranslate.style.display = 'none');
                menuTones.forEach(menuTone => menuTone.style.display = 'none');
                aiResults.forEach(aiResult => aiResult.style.display = 'none');
            }

            // Create AI loader
            function createLoader() {
                const loader = document.createElement("div");
                loader.classList.add("loader-overlay");
                loader.innerHTML = "Loading...";
                return loader;
            }

            // Create AI button
            function createAiButton() {
                const aiButton = document.createElement("button");
                aiButton.classList.add("btn", "ai-button", "btn-secondary");
                aiButton.innerHTML = "<i class='fa-solid fa-microchip-ai'></i>";
                aiButton.type = "button";

                return aiButton;
            }

            // Create Speech Recognition Button
            function createSpeechRecognitionButton() {
                const recordButton = document.createElement("button");
                recordButton.classList.add("btn", "ai-button", "btn-secondary");
                recordButton.innerHTML = "<i class='fas fa-microphone'></i>";
                recordButton.type = "button";
                recordButton.title = "Start Voice Recording";
                recordButton.onclick = () => toggleRecording(textarea, recordButton);
                return recordButton;
            }

            // Create Buttons Father Element
            function createButtonsFatherElement(){
                const buttonsFather = document.createElement('div');
                buttonsFather.classList.add("d-flex", "justify-content-end", "mb-2", "gap-1");
                return buttonsFather;
            }

            // Create Suggestion Box
            function createSuggestionBox() {
                const suggestionBox = document.createElement("div");
                suggestionBox.classList.add('ai-container')
                suggestionBox.innerHTML = `
                    <div class="ai-tool" style="display: none">
                        <div class="header">
                           <span>Boss AI <span class="badge rounded-pill badge-danger">Beta</span></span>
                           
                            <div class="buttons">
                                <span class="firstStepBTN">
                                    <i class="fa-light fa-arrow-left-from-arc"></i>
                                </span>
                                <span class="closeBTN ms-2">
                                    <i class="fa-regular fa-x"></i>
                                </span>
                            </div>
                        </div>
                        <div class="menu" id="menu">
                            <div class="menu__basic">
                                <div class="menu-item" data-action="improve"><i class="fas fa-align-left"></i> Summarize Writing</div>
                                <div class="menu-item" data-action="summarize"><i class="fas fa-magic"></i> Improve Writing</div>
                                <div class="menu-item" data-action="fix_spelling"><i class="fas fa-spell-check"></i> Fix Spelling & Grammar</div>
                                <div class="menu-item" data-action="shorten"><i class="fas fa-compress-alt"></i> Make Shorter</div>
                                <div class="menu-item" data-action="technical"><i class="fas fa-cogs"></i> Make More Technical</div>
                                <div class="menu-item" data-action="adult"><i class="fas fa-user-tie"></i> Simplify For Adult</div>
                                <div class="menu-item" id="changeToneBtn"><i class="fa-solid fa-volume-high"></i> Change Tone to</div>
                                <div class="menu-item" id="translateToBtn"><i class="fas fa-language"></i> Translate to</div>
                            </div>

                            <!-- Tone Menu -->
                            <div class="menu__tone" style="display: none;">
                                <div class="menu-item-wo" id="backBtn"><i class="fas fa-arrow-left"></i> Back</div>
                                <div class="menu-item" data-action="tone_professional"><i class="fa-regular fa-circle"></i> Professional</div>
                                <div class="menu-item" data-action="tone_empathetic"><i class="fa-regular fa-circle"></i> Empathetic</div>
                                <div class="menu-item" data-action="tone_casual"><i class="fa-regular fa-circle"></i> Casual</div>
                                <div class="menu-item" data-action="tone_neutral"><i class="fa-regular fa-circle"></i> Neutral</div>
                                <div class="menu-item" data-action="tone_educational"><i class="fa-regular fa-circle"></i> Educational</div>
                            </div>

                            <!-- Translate Menu -->
                            <div class="menu__translate" style="display: none;">
                                <div class="menu-item-wo" id="backBtn2"><i class="fas fa-arrow-left"></i> Back</div>
                                <div class="menu-item" data-action="translate_spanish"><i class="fa-solid fa-globe"></i> Spanish</div>
                                <div class="menu-item" data-action="translate_english"><i class="fa-solid fa-globe"></i> English</div>
                                <div class="menu-item" data-action="translate_french"><i class="fa-solid fa-globe"></i> French</div>
                                <div class="menu-item" data-action="translate_german"><i class="fa-solid fa-globe"></i> German</div>
                                <div class="menu-item" data-action="translate_chinese"><i class="fa-solid fa-globe"></i> Chinese</div>
                                <div class="menu-item" data-action="translate_japanese"><i class="fa-solid fa-globe"></i> Japanese</div>
                            </div>
                        </div>

                        <div class="ai-result" style="display: none">
                            <p id="aiSuggestion">AI suggestion will appear here.</p>
                            <button class="btn btn-primary replace">Replace</button>
                            <button class="btn btn-secondary insert">Insert Below</button>
                            <div class="ai-footer">
                                <span>AI can make mistakes, check your work before submitting. | Powered by Boss AI <span class="badge rounded-pill badge-danger">Beta</span></span>
                                 
                            </div>
                        </div>
                    </div>
                `;
                return suggestionBox;
            }
        });

        // Voice Recording
        let recognition;
        let isRecording = false;
        let isStopping = false;
        let activeTextarea = null;
        let activeButton = null;
        let initialTextValue = '';
        let finalTranscript = '';
        let interimTranscript = '';

        function toggleRecording(textarea, button) {
            // If already recording for this textarea, stop it
            if (isRecording && activeTextarea === textarea) {
                stopVoiceRecording();
                return;
            }
            
            // If recording for a different textarea, stop current and start new
            if (isRecording) {
                stopVoiceRecording(() => {
                    startVoiceRecording(textarea, button);
                });
                return;
            }
            
            // Start new recording
            startVoiceRecording(textarea, button);
        }

        function startVoiceRecording(textarea, button) {
            // Don't start if we're already recording or stopping
            if (isRecording || isStopping) return;
            
            // Initialize recognition if needed
            if (!recognition) {
                initializeRecognition();
                if (!recognition) return; // If initialization failed
            }

            // Reset transcript tracking
            initialTextValue = textarea.value;
            finalTranscript = '';
            interimTranscript = '';
            
            // Set active elements
            activeTextarea = textarea;
            activeButton = button;
            isRecording = true;
            
            // Update button UI
            updateButtonState(button, 'recording');
            
            try {
                recognition.start();
                console.log('Recording started for', textarea.id || 'unnamed textarea');
            } catch (e) {
                console.error("Failed to start recording:", e);
                resetRecordingState();
            }
        }

        function stopVoiceRecording(callback) {
            if (!isRecording || isStopping) {
                if (callback) callback();
                return;
            }
            
            isStopping = true;
            updateButtonState(activeButton, 'stopping');
            
            // First try to stop immediately
            try {
                recognition.stop();
                console.log('Recording stopped immediately');
                if (callback) callback();
            } catch (e) {
                console.error("Error stopping recognition:", e);
            }
            
            // Fallback: Wait up to 3 seconds if needed
            const maxWaitTime = 3000;
            const startTime = Date.now();
            
            const checkStopStatus = () => {
                if (!isRecording || Date.now() - startTime > maxWaitTime) {
                    if (callback) callback();
                    return;
                }
                setTimeout(checkStopStatus, 100);
            };
            
            checkStopStatus();
        }

        function initializeRecognition() {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            if (!SpeechRecognition) {
                console.error("Speech recognition not supported in this browser");
                return;
            }
            
            recognition = new SpeechRecognition();
            recognition.lang = 'en-US';
            recognition.continuous = true;
            recognition.interimResults = true;
            
            recognition.onstart = () => {
                console.log('Recognition engine started');
            };
            
            recognition.onend = () => {
                console.log('Recognition engine ended');
                if (isRecording && !isStopping) {
                    console.log('Attempting to restart recognition...');
                    setTimeout(() => {
                        try {
                            if (isRecording && !isStopping) {
                                recognition.start();
                            }
                        } catch (e) {
                            console.error('Failed to restart recognition:', e);
                            resetRecordingState();
                        }
                    }, 500);
                } else {
                    resetRecordingState();
                }
            };
            
            recognition.onerror = (event) => {
                console.error('Recognition error:', event.error);
                if (event.error === 'no-speech' && isRecording && !isStopping) {
                    setTimeout(() => {
                        try {
                            if (isRecording && !isStopping) {
                                recognition.start();
                            }
                        } catch (e) {
                            console.error('Failed to restart after error:', e);
                            resetRecordingState();
                        }
                    }, 1000);
                } else {
                    resetRecordingState();
                }
            };
            
            recognition.onresult = (event) => {
                // Clear interim transcript
                interimTranscript = '';
                
                // Process all new results
                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;
                    if (event.results[i].isFinal) {
                        finalTranscript += transcript + ' ';
                    } else {
                        interimTranscript += transcript;
                    }
                }
                
                // Update textarea with both final and interim results
                if (isRecording && activeTextarea) {
                    activeTextarea.value = initialTextValue + finalTranscript + interimTranscript;
                }

                reinitializeMDBForm();
            };
        }

        function resetRecordingState() {
            isRecording = false;
            isStopping = false;
            
            if (activeButton) {
                updateButtonState(activeButton, 'ready');
            }
            
            activeTextarea = null;
            activeButton = null;
            initialTextValue = '';
            finalTranscript = '';
            interimTranscript = '';
        }

        function updateButtonState(button, state) {
            if (!button) return;
            
            switch(state) {
                case 'recording':
                    button.innerHTML = "<i class='fas fa-microphone-slash'></i>";
                    button.title = "Stop Voice Recording";
                    button.classList.add('recording-active');
                    break;
                case 'stopping':
                    button.innerHTML = "<i class='fas fa-clock'></i>";
                    button.title = "Finishing recording...";
                    break;
                case 'ready':
                    button.innerHTML = "<i class='fas fa-microphone'></i>";
                    button.title = "Start Voice Recording";
                    button.classList.remove('recording-active');
                    break;
            }
        }

        // Initialize on DOM ready
        document.addEventListener("DOMContentLoaded", () => {
            // Check microphone permission
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then(stream => {
                    console.log('Microphone permission granted');
                    stream.getTracks().forEach(track => track.stop());
                })
                .catch(err => {
                    console.log('Microphone permission denied:', err);
                    document.querySelectorAll('.btn-secondary[title*="Voice Recording"]')
                        .forEach(btn => btn.style.display = 'none');
                });
        });
    });


    function reinitializeMDBForm() {
        var formOutlines = document.querySelectorAll('.form-outline');

        formOutlines.forEach(function(formOutline) {
            new mdb.Input(formOutline).update();
        });
    }
</script>