{"version": 3, "sources": ["webpack://./src/public/wc/boss_inspect/src/vuecomponents/sbp-modal-base.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/sbp-portal-modal-base.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/sbp-pricing-plan-card.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/sbp-resizable.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/report/sbp-drilldown-section.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/report/sbp-drilldown-item.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/report/sbp-drilldown-diff-item.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/report/sbp-summary-item.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/report/sbp-metric-summary-card.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/report/labor-hours-per-tech/drilldown-table.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/vue-imageupload-editor/components/vue-image-markup/Editor.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/vue-imageupload-editor/components/Tool/Tool.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/vue-imageupload-editor/components/ColorPicker/ColorPicker.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/vue-imageupload-editor/ImageEditor.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/dvi/sbp-dvi-image-wrapper.vue", "webpack://./src/public/wc/src/vuecomponents/ai-writing-tool/sbp-ai-writing-tool.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/dvi/sbp-dvi-finding-input.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/dvi/sbp-dvi-customer-request.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/dvi/sbp-dvi-report-customer-request.vue", "webpack://./src/public/wc/boss_inspect/src/vuecomponents/vue-badger-accordion/components/BadgerAccordionItem.vue"], "names": [], "mappings": "AACA,kCACE,wBAAA,CAGF,wBACE,kCACE,wBAAA,CAAA,CCNJ,kCACE,wBAAA,CAEF,kCACE,wBAAA,CCFE,iGACE,qBAAA,CACA,eAAA,CAKF,6FACE,wBAAA,CACA,UAAA,CACA,eAAA,CAKF,qGACE,wBAAA,CACA,UAAA,CACA,eAAA,CCkbN,sCACA,iBACA,CAEA,mDAQA,eAAA,CAPA,aAAA,CAWA,WAAA,CAVA,iBAAA,CAQA,OAAA,CACA,KAAA,CAPA,iBAAA,CACA,gBAAA,CACA,qBAAA,CACA,wBAAA,CAEA,SAAA,CANA,UAUA,CAEA,oDAOA,gBAAA,CAGA,UAAA,CAFA,UAAA,CAIA,UACA,CAEA,uGAJA,WAAA,CAVA,aAAA,CAQA,WAAA,CAPA,iBAAA,CACA,iBAAA,CACA,gBAAA,CACA,qBAAA,CACA,wBAsBA,CAbA,mDAQA,eAAA,CAIA,MAAA,CADA,UAAA,CARA,UAUA,CAEA,oDAWA,WAAA,CAJA,gBAAA,CAEA,WAAA,CAGA,UACA,CAEA,uGAdA,aAAA,CASA,SAAA,CARA,iBAAA,CACA,iBAAA,CACA,gBAAA,CACA,qBAAA,CACA,wBAAA,CAEA,UAoBA,CAbA,mDAQA,eAAA,CAGA,WAAA,CACA,KAAA,CATA,UAUA,CAEA,oDAOA,gBAAA,CAGA,SAAA,CAFA,UAAA,CAIA,UACA,CAEA,uGAdA,aAAA,CAQA,WAAA,CAPA,iBAAA,CASA,QAAA,CARA,iBAAA,CACA,gBAAA,CACA,qBAAA,CACA,wBAsBA,CAbA,mDAQA,eAAA,CAIA,MAAA,CADA,UAAA,CARA,UAUA,CAEA,oDAOA,gBAAA,CANA,aAAA,CAQA,WAAA,CAPA,iBAAA,CAQA,UAAA,CACA,QAAA,CARA,iBAAA,CACA,gBAAA,CACA,qBAAA,CACA,wBAAA,CAEA,UAAA,CAIA,UACA,CC/jBE,2CACE,sBAAA,CAGF,2CACE,aAAA,CAGF,uCACE,uBAAA,CAIJ,6BACE,sBAAA,CCdA,uCAOE,6BAAA,CAHA,QAAA,CAHA,UAAA,CAEA,MAAA,CADA,iBAAA,CAIA,uBAAA,CADA,OAEA,CAIA,6CACE,UAAA,CCZJ,4CAOE,6BAAA,CAHA,QAAA,CAHA,UAAA,CAEA,MAAA,CADA,iBAAA,CAIA,uBAAA,CADA,OAEA,CAIA,kDACE,UAAA,CCbN,+BACE,WAAA,CAEF,8BAEE,iBAAA,CADA,0CAAA,CAGA,eAAA,CADA,iBACA,CAEA,qCASE,2DAAA,CADA,uEAAA,CAPA,UAAA,CACA,aAAA,CAIA,WAAA,CAFA,WAAA,CADA,iBAAA,CAEA,KAAA,CAEA,WAEA,CAIJ,yBACE,GACE,WAAA,CAEF,GACE,SAAA,CAAA,CC1BF,8CACE,WAAA,CAGF,6DACE,SAAA,CACA,qCAAA,CAIA,mEACE,SAAA,CAKN,qCAEE,YAAA,CADA,UACA,CAOF,2BAEE,YAAA,CADA,UACA,CAEA,uCAmCE,wCAAA,CA7BA,2TAAA,CAoBA,uEAAA,CAQA,2BAAA,CAfA,gIAAA,CAlBA,UAAA,CACA,aAAA,CAEA,WAAA,CADA,UAgCA,CAIJ,4BACE,GACE,sEAAA,CAAA,CCtEF,65BACE,cAAA,CC8jCJ,cACA,SACA,CClkCA,uBACE,kBAAA,CAEA,YAAA,CACA,cAAA,CAFA,6BAAA,CAGA,eAAA,CAEA,6BAEE,aAAA,CADA,cACA,CAIJ,mCACE,iBAAA,CAGF,+BACE,cAAA,CACA,oBAAA,CAEA,uCACE,aAAA,CAIJ,6BAOE,kBAAA,CACA,QAAA,CALA,UAAA,CAEA,WAAA,CACA,eAAA,CAFA,SAAA,CAHA,iBAAA,CACA,SAMA,CClCF,+BACE,iBAAA,CAIA,cAAA,CAFA,WAAA,CACA,YAAA,CAFA,UAGA,CCLF,MAEE,kCAAA,CACA,iCAAA,CAEA,aAAA,CAEA,YAAA,CANA,6CAAA,CAOA,sBAAA,CAFA,eAAA,CAFA,iBAIA,CACA,wBACE,YAAA,CACA,qBAAA,CACA,YAAA,CACA,UAAA,CAEA,wBANF,wBAOI,YAAA,CAAA,CAGF,gCAGE,kBAAA,CAFA,YAAA,CAIA,cAAA,CADA,QAAA,CAFA,sBAGA,CAEA,wBAPF,gCAQI,QAAA,CACA,gBAAA,CAAA,CAGF,+CACE,iBAAA,CAEA,eAAA,CADA,cACA,CAEF,6CAEE,aAAA,CADA,cACA,CAKN,cAGE,kBAAA,CAFA,YAAA,CACA,qBAAA,CAEA,sBAAA,CACA,kBAAA,CACA,2BAAA,CAEA,wBARF,cASI,gCAAA,CAAA,CAKN,eACE,eAAA,CACA,QAAA,CACA,kCAAA,CAEA,wBALF,eAMI,QAAA,CACA,kCAAA,CAAA,CAGF,wBAVF,eAWI,QAAA,CACA,mCAAA,CAAA,CAGF,wBAfF,eAgBI,QAAA,CACA,mCAAA,CAAA,CAIJ,OACE,iCAAA,CAGF,wBACE,cAEE,YAAA,CADA,cACA,CAAA,CClEJ,wCASA,kBAAA,CAHA,mCAAA,CADA,QAAA,CAEA,YAAA,CACA,sBAAA,CALA,MAAA,CAFA,iBAAA,CAGA,OAAA,CAFA,KAAA,CAQA,UACA,CAEA,wCAGA,kBAAA,CAGA,wBAAA,CACA,wBAAA,CACA,aAAA,CAPA,YAAA,CACA,qBAAA,CAEA,sBAAA,CAKA,gBAAA,CAJA,YAAA,CAKA,iBACA,CAEA,wBACA,sBACA,CAqVA,uBAGA,kBAAA,CAEA,wBAAA,CACA,wBAAA,CACA,aAAA,CAIA,cAAA,CAVA,YAAA,CACA,qBAAA,CAEA,sBAAA,CAMA,gBAAA,CAFA,YAAA,CACA,iBAGA,CAEA,yBACA,cAAA,CACA,kBACA,CCyLA,uBACA,+BAAA,CACA,0BACA,CAEA,4CACA,iBACA,CAEA,iCAEA,kBAAA,CADA,YAAA,CAIA,UAAA,CAFA,mBAAA,CACA,mBAAA,CAGA,sBACA,gBACA,CAEA,gCAEA,+BAAA,CACA,oBAAA,CAFA,eAAA,CAGA,cAAA,CAEA,yBACA,yCAAA,CACA,oBAAA,CAEA,mBACA,oBACA,CACA,CACA,CACA,CAEA,mCAGA,wBAAA,CADA,kBAAA,CADA,iBAAA,CAGA,uCAAA,CACA,2BAIA,oDAAA,CAFA,wEAAA,CACA,yBAAA,CAFA,4BAIA,CACA,CAEA,0BAEA,WAAA,CACA,kBAAA,CAEA,YAAA,CADA,eAAA,CAHA,UAKA,CAEA,4DAGA,eAAA,CACA,SAAA,CAFA,UAAA,CAIA,yBACA,eAAA,CACA,aACA,CAEA,mBACA,cAAA,CACA,UACA,CACA,CAEA,+BACA,iBAAA,CACA,UAAA,CACA,WACA,CAEA,0BAGA,sCAAA,CAEA,kBAAA,CADA,qEAAA,CAGA,kBAAA,CANA,iBAAA,CAKA,WAAA,CAGA,2BAEA,4BAAA,CADA,iBAAA,CAGA,0BAaA,oDAAA,CAFA,wEAAA,CACA,yBAAA,CAJA,4BAAA,CADA,kBAAA,CADA,WAAA,CALA,UAAA,CAGA,SAAA,CAUA,oEAAA,CACA,sBAAA,CACA,4EAAA,CACA,0BAAA,CARA,mBAAA,CAPA,iBAAA,CAGA,UAAA,CAFA,QAAA,CAOA,SAQA,CACA,CAEA,yBAKA,wCAAA,CAFA,sBAAA,CAFA,YAAA,CACA,6BAAA,CAEA,gBAEA,CAEA,4BACA,gBACA,CAEA,2DAIA,kBAAA,CAIA,sBAAA,CACA,cAAA,CAPA,YAAA,CACA,8BAAA,CAIA,gBAAA,CAFA,iBAAA,CAMA,yBAQA,mCAAA,CADA,sCAAA,CADA,gCAAA,CALA,UAAA,CAIA,QAAA,CAHA,iBAAA,CACA,UAAA,CAMA,6BAAA,CALA,OAMA,CAEA,+BACA,yBACA,CAEA,yBACA,mCACA,CACA,CAQA,mIAEA,sBACA,CAEA,4BAEA,qCAAA,CADA,eAAA,CAEA,aAAA,CAEA,sBACA,cAAA,CACA,gBACA,CACA,CAEA,+BAEA,+BAAA,CADA,UAEA,CAEA,0DAEA,cACA,CACA,CAEA,mCAEA,kBAAA,CADA,mBAAA,CAGA,OAAA,CADA,sBAAA,CAGA,sBAMA,mDAAA,CAHA,wBAAA,CACA,iBAAA,CAGA,sCAAA,CAFA,oBAAA,CAHA,UAAA,CADA,SAAA,CAQA,+BACA,kBACA,CACA,gCACA,mBACA,CACA,gCACA,mBACA,CACA,CACA,CAEA,2BACA,UACA,uBACA,CACA,IACA,0BACA,CACA,CAEA,8BACA,wCACA,CAEA,6BACA,GACA,UAAA,CACA,mBACA,CACA,GACA,SAAA,CACA,kBACA,CACA,CAEA,+BACA,iDACA,CAEA,8BACA,GACA,SAAA,CACA,kBACA,CACA,GAGA,YAAA,CAFA,SAAA,CACA,kBAEA,CACA,CAEA,mCACA,GACA,yBACA,CACA,GACA,4BACA,CACA,CCx1BA,4BACE,UAAA,CAMA,oGACE,iBAAA,CAGF,wEAEE,eAAA,CADA,iBACA,CAIJ,4BACE,aAAA,CAMA,oGACE,oBAAA,CAGF,wEAEE,kBAAA,CADA,oBACA,CAIJ,+BACE,aAAA,CAMA,0GACE,oBAAA,CAGF,2EAEE,kBAAA,CADA,oBACA,CAIJ,2BACE,aAAA,CAMA,kGACE,oBAAA,CAGF,uEAEE,kBAAA,CADA,oBACA,CAIJ,iCAGE,kBAAA,CAGA,aAAA,CALA,YAAA,CACA,qBAAA,CAKA,eAAA,CAHA,sBAAA,CACA,iBAEA,CAGF,8BAIE,kBAAA,CAHA,kBAAA,CAIA,cAAA,CAHA,YAAA,CACA,sBAAA,CAGA,gBAAA,CAEA,oCACE,qBAAA,CAIJ,0CACE,YAAA,CAEA,QAAA,CADA,yDACA,CAEA,8CACE,iEAAA,CAEA,eAAA,CACA,SAAA,CAGF,2DAGE,kBAAA,CAFA,eAAA,CACA,YAAA,CAIA,yBAAA,CAFA,sBAAA,CACA,UACA,CAGF,6DAEE,mEAAA,CAEA,cAAA,CAHA,aAGA,CAEA,oEACE,mCAAA,CAIJ,iDACE,sBAAA,CC5HJ,8BAIE,kBAAA,CAHA,kBAAA,CAIA,cAAA,CAHA,YAAA,CACA,sBAAA,CAGA,gBAAA,CAEA,oCACE,qBAAA,CCyJJ,8BACA,YAAA,CACA,8BACA,CCpKE,qDACE,YAAA,CAGJ,yBACE,2BAAA,CACA,eAAA,CAGA,uCACE,sBAAA,CAMF,wDACE,qCAAA,CAMA,+EAME,kBAAA,CAJA,sBAAA,CACA,QAAA,CACA,eAAA,CACA,YAAA,CAJA,UAKA,CAEA,uGAEE,cAAA,CADA,YAAA,CAEA,eAAA,CAGF,wGAGE,kBAAA,CAEA,cAAA,CAHA,YAAA,CADA,YAAA,CAGA,wBACA", "file": "main.css", "sourcesContent": [".modal-custom-lg{max-width:80vw !important}@media(min-width: 576px){.modal-custom-md{max-width:50vw !important}}", ".modal-custom-lg{max-width:80vw !important}.modal-custom-md{max-width:50vw !important}", ".plan.--silver .card-header,.plan.--silver .card-footer button{background-color:#ccc;font-weight:bold}.plan.--gold .card-header,.plan.--gold .card-footer button{background-color:#cb9f0e;color:#fff;font-weight:bold}.plan.--platinum .card-header,.plan.--platinum .card-footer button{background-color:#3f4f58;color:#fff;font-weight:bold}", "\n.resizable-component {\n  position: relative;\n}\n\n.resizable-component > .resizable-r {\n  display: block;\n  position: absolute;\n  z-index: 90;\n  touch-action: none;\n  user-select: none;\n  -moz-user-select: none;\n  -webkit-user-select: none;\n  cursor: e-resize;\n  width: 4px;\n  right: 0px;\n  top: 0;\n  height: 100%;\n}\n\n.resizable-component > .resizable-rb {\n  display: block;\n  position: absolute;\n  touch-action: none;\n  user-select: none;\n  -moz-user-select: none;\n  -webkit-user-select: none;\n  cursor: se-resize;\n  width: 12px;\n  height: 12px;\n  right: -6px;\n  bottom: -6px;\n  z-index: 91;\n}\n\n.resizable-component > .resizable-b {\n  display: block;\n  position: absolute;\n  z-index: 90;\n  touch-action: none;\n  user-select: none;\n  -moz-user-select: none;\n  -webkit-user-select: none;\n  cursor: s-resize;\n  height: 12px;\n  bottom: -6px;\n  width: 100%;\n  left: 0;\n}\n\n.resizable-component > .resizable-lb {\n  display: block;\n  position: absolute;\n  touch-action: none;\n  user-select: none;\n  -moz-user-select: none;\n  -webkit-user-select: none;\n  cursor: sw-resize;\n  width: 12px;\n  height: 12px;\n  left: -6px;\n  bottom: -6px;\n  z-index: 91;\n}\n\n.resizable-component > .resizable-l {\n  display: block;\n  position: absolute;\n  z-index: 90;\n  touch-action: none;\n  user-select: none;\n  -moz-user-select: none;\n  -webkit-user-select: none;\n  cursor: w-resize;\n  width: 12px;\n  left: -6px;\n  height: 100%;\n  top: 0;\n}\n\n.resizable-component > .resizable-lt {\n  display: block;\n  position: absolute;\n  touch-action: none;\n  user-select: none;\n  -moz-user-select: none;\n  -webkit-user-select: none;\n  cursor: nw-resize;\n  width: 12px;\n  height: 12px;\n  left: -6px;\n  top: -6px;\n  z-index: 91;\n}\n\n.resizable-component > .resizable-t {\n  display: block;\n  position: absolute;\n  z-index: 90;\n  touch-action: none;\n  user-select: none;\n  -moz-user-select: none;\n  -webkit-user-select: none;\n  cursor: n-resize;\n  height: 12px;\n  top: -6px;\n  width: 100%;\n  left: 0;\n}\n\n.resizable-component > .resizable-rt {\n  display: block;\n  position: absolute;\n  touch-action: none;\n  user-select: none;\n  -moz-user-select: none;\n  -webkit-user-select: none;\n  cursor: ne-resize;\n  width: 12px;\n  height: 12px;\n  right: -6px;\n  top: -6px;\n  z-index: 91;\n}\n", ".print-document .no-print{display:none !important}.print-document .collapse{display:block}.print-document .aggs{padding-top:0px !important}.only-print{display:none !important}", ".drilldown-item::after{content:\"\";position:absolute;left:0;bottom:0;width:0;transition:all .3s ease;border-bottom:solid 1px green}.drilldown-item:hover::after{width:100%}", ".drilldown-diff-item::after{content:\"\";position:absolute;left:0;bottom:0;width:0;transition:all .3s ease;border-bottom:solid 1px green}.drilldown-diff-item:hover::after{width:100%}", ".summary-item{height:1rem}.placeholder{box-shadow:0 4px 10px 0 rgba(33,33,33,.15);border-radius:4px;position:relative;overflow:hidden}.placeholder::before{content:\"\";display:block;position:absolute;left:-150px;top:0;height:100%;width:150px;background:linear-gradient(to right, transparent 0%, #E8E8E8 50%, transparent 100%);animation:load 1s cubic-bezier(0.4, 0, 0.2, 1) infinite}@keyframes load{from{left:-150px}to{left:100%}}", ".metric-summary-card .handle{cursor:move}.metric-summary-card .metric-summary-toggle{opacity:0;transition:opacity 600ms,visibility 600ms}.metric-summary-card:hover .metric-summary-toggle{opacity:1}.skeleton-container{width:100%;height:171px}.skeleton{width:100%;height:118px}.skeleton:empty::after{content:\"\";display:block;width:100%;height:100%;background-image:linear-gradient(90deg, rgba(255, 255, 255, 0) 0, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0) 100%),linear-gradient(rgb(230, 230, 230) 32px, transparent 0),linear-gradient(rgb(230, 230, 230) 4px, transparent 0),linear-gradient(rgb(230, 230, 230) 23px, transparent 0),linear-gradient(rgb(230, 230, 230) 23px, transparent 0),linear-gradient(white 118px, transparent 0);background-size:200px 118px,calc(100% - 80px) 24px,calc(100% - 32px) 4px,calc(100% - 80px) 16px,calc(100% - 80px) 16px,100% 100%;background-position:-150% 0,40px 16px,16px 56px,40px 65px,40px 90px,0 0;background-repeat:no-repeat;animation:loading 1.5s infinite}@keyframes loading{to{background-position:350% 0,40px 16px,16px 56px,40px 65px,40px 90px,0 0}}", ".labor-hours-per-tech-drilldown-table .table>:not(caption)>*>*{border:0 solid}", "\n.upper-canvas {\n  z-index: 1;\n}\n", ".tool{align-items:center;justify-content:space-between;display:flex;font-size:16px;padding:2px 5px}.tool:hover{cursor:pointer;color:#4287f5}.upload-container{position:relative}.upload-label{cursor:pointer;display:inline-block}.upload-label:hover i{color:#4287f5}.file-input{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);border:0}", ".color-picker{border-radius:50%;width:30px;height:30px;margin:5px 0;cursor:pointer}", ".main{font-family:\"Avenir\",Helvetica,Arial,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-align:center;color:#2c3e50;margin-top:10px;display:flex;justify-content:center}.main .editor-container{display:flex;flex-direction:column;height:700px;width:100%}@media(max-width: 600px){.main .editor-container{height:500px}}.main .editor-container .editor{display:flex;justify-content:center;align-items:center;gap:20px;flex-wrap:wrap}@media(max-width: 768px){.main .editor-container .editor{gap:10px;margin-left:58px}}.main .editor-container .editor .current-color{border-radius:5px;min-width:28px;min-height:28px}.main .editor-container .editor .active-tool{cursor:pointer;color:#4287f5}.main .colors{display:flex;flex-direction:column;align-items:center;justify-content:center;margin:10px 15px 0;transform:translateX(-20px)}@media(max-width: 768px){.main .colors{transform:translateX(-20px, 25px)}}.custom-editor{margin-top:20px;scale:.5;transform:translate(-250px, -600px)}@media(max-width: 768px){.custom-editor{scale:.4;transform:translate(-300px, -760px)}}@media(max-width: 600px){.custom-editor{scale:.3;transform:translate(-400px, -1400px)}}@media(max-width: 480px){.custom-editor{scale:.2;transform:translate(-450px, -2200px)}}canvas{border:1px solid rgba(0,0,0,.1294117647)}@media(max-width: 992px){.modal-dialog{max-width:100%;margin:.5rem}}", "\n.image-error-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background-color: #f8d7da;\n  border: 1px solid #f5c6cb;\n  color: #721c24;\n  padding: 20px;\n  text-align: center;\n  min-height: 150px;\n  cursor: pointer;\n}\n\n.image-error-container i {\n  font-size: 2rem;\n  margin-bottom: 10px;\n}\n", "\n:root {\n  --bb: var(--bodybackground, white);\n  --bodybackground: var(white);\n}\n\n.ai-writing-tool-container {\n  position: relative;\n}\n\n.action-buttons{\n  display: flex;\n  align-items: center;\n  justify-content: end;\n  margin-bottom: .5rem;\n  gap: .25rem;\n\n  .btn{\n    padding: 5px 15px;\n  }\n\n  .btn-secondary{\n    font-size: .9rem;\n    border: 1px solid var(--primary);\n    color: var(--primary);\n    transition: .3s;\n\n    &:hover{\n      background-color: var(--primary) !important;\n      color: #fff !important;\n\n      i{\n        color: #fff !important;\n      }\n    }\n  }\n}\n\n.textarea-wrapper {\n  position: relative;\n  border-radius: 12px;\n  border: 2px solid #e0e0e0;\n  transition: border-color 0.3s ease-in-out;\n  &.loading {\n    border: 2px solid transparent;\n    background: linear-gradient(90deg, red, orange, yellow, green, cyan, blue, violet);\n    background-size: 200% 200%;\n    animation: rainbow-border 2s linear infinite;\n  }\n}\n\ntextarea {\n  width: 100%;\n  border: none;\n  border-radius: 10px;\n  resize: vertical;\n  outline: none;\n}\n\n.ai-button,\n.record-button {\n  z-index: 10;\n  box-shadow: none;\n  padding: 0;\n\n  &:hover {\n    box-shadow: none;\n    color: #a01c1c;\n  }\n\n  i{\n    font-size: 20px;\n    width: 25px;\n  }\n}\n\n.ai-container {\n  position: absolute;\n  width: 100%;\n  z-index: 110;\n}\n\n.ai-tool {\n  position: relative;\n  margin: 0 auto;\n  background-color: var(--bodybackground);\n  box-shadow: 0 4px 8px -2px rgba(9, 30, 66, 0.31), 0 0 1px rgba(9, 30, 66, 0.31);\n  border-radius: 12px;\n  z-index: 110;\n  margin-top: 10px;\n\n  &.loading {\n    position: relative;\n    border: 1px solid transparent;\n\n    &::before {\n      content: \"\";\n      position: absolute;\n      top: -1px;\n      left: -1px;\n      right: -1px;\n      bottom: -1px;\n      border-radius: 12px;\n      border: 1px solid transparent;\n      pointer-events: none;\n      z-index: 1;\n      background: linear-gradient(90deg, red, orange, yellow, green, cyan, blue, violet);\n      background-size: 200% 200%;\n      animation: rainbow-border 2s linear infinite;\n      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\n      mask-composite: exclude;\n      -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\n      -webkit-mask-composite: xor;\n    }\n  }\n\n  .header {\n    display: flex;\n    justify-content: space-between;\n    color: var(--textColor);\n    padding: 6px 12px;\n    border-bottom: 1px solid var(--textColor);\n  }\n\n  .ai-result {\n    padding: 6px 12px;\n  }\n\n  .menu-item,\n  .menu-item-wo {\n    display: grid;\n    grid-template-columns: 30px 1fr;\n    align-items: center;\n    position: relative;\n    padding-right: 20px;\n    padding: 6px 12px;\n    color: var(--textColor);\n    cursor: pointer;\n\n    &::after {\n      content: \"\";\n      position: absolute;\n      right: 10px;\n      width: 0;\n      height: 0;\n      border-top: 5px solid transparent;\n      border-left: 8px solid var(--textColor);\n      border-bottom: 5px solid transparent;\n      transition: transform 0.3s ease;\n    }\n\n    &:hover::after {\n      transform: translateX(5px);\n    }\n\n    &:hover {\n      background-color: rgba(223, 223, 223, 0.05);\n    }\n  }\n\n  p,\n  .ai-footer,\n  .typing-animation {\n    color: var(--textColor);\n  }\n\n  .fas,\n  .fa-solid {\n    color: var(--textColor);\n  }\n  \n  .ai-footer {\n    margin-top: 10px;\n    border-top: 1px solid var(--textColor);\n    padding: 2px 0;\n\n    span {\n      font-size: 12px;\n      padding: 6px 12px;\n    }\n  }\n\n  .badge-danger{\n    color: #fff;\n    background-color: var(--primary);\n  }\n\n  .firstStepBTN,\n  .closeBTN{\n    cursor: pointer;\n  }\n}\n\n.typing-animation {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: 4px;\n\n  span {\n    width: 8px;\n    height: 8px;\n    background-color: #007BFF;\n    border-radius: 50%;\n    display: inline-block;\n    animation: bounce 1.5s infinite ease-in-out;\n    box-shadow: 0 0 10px rgba(0, 123, 255, 0.6);\n\n    &:nth-child(1) {\n      animation-delay: 0s;\n    }\n    &:nth-child(2) {\n      animation-delay: 0.2s;\n    }\n    &:nth-child(3) {\n      animation-delay: 0.4s;\n    }\n  }\n}\n\n@keyframes bounce {\n  0%, 80%, 100% {\n    transform: translateY(0);\n  }\n  40% {\n    transform: translateY(-6px);\n  }\n}\n\n.ai__fade-in {\n  animation: aiFadeIn 0.1s ease-out;\n}\n\n@keyframes aiFadeIn {\n  0% {\n    opacity: 0.2;\n    transform: scale(0.6);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n.ai__fade-out {\n  animation: aiFadeOut 0.1s ease-in forwards;\n}\n\n@keyframes aiFadeOut {\n  0% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  100% {\n    opacity: 0;\n    transform: scale(0);\n    display: none;\n  }\n}\n\n@keyframes rainbow-border {\n  0% {\n    background-position: 0% 50%;\n  }\n  100% {\n    background-position: 100% 50%;\n  }\n}\n", ".none-item{color:gray}.none-item .form-check-input{border-color:gray}.none-item .form-check-input:checked{border-color:gray}.none-item .form-check-input[type=radio]:checked:after{border-color:gray;background:gray}.good-item{color:#00b74a}.good-item .form-check-input{border-color:#00b74a}.good-item .form-check-input:checked{border-color:#00b74a}.good-item .form-check-input[type=radio]:checked:after{border-color:#00b74a;background:#00b74a}.warning-item{color:#ffa900}.warning-item .form-check-input{border-color:#ffa900}.warning-item .form-check-input:checked{border-color:#ffa900}.warning-item .form-check-input[type=radio]:checked:after{border-color:#ffa900;background:#ffa900}.bad-item{color:#f93154}.bad-item .form-check-input{border-color:#f93154}.bad-item .form-check-input:checked{border-color:#f93154}.bad-item .form-check-input[type=radio]:checked:after{border-color:#f93154;background:#f93154}.upload-loading{display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;color:#4285f4;font-weight:bold}.upload-zone{border:gray dashed;display:flex;justify-content:center;align-items:center;cursor:pointer;min-height:200px}.upload-zone:hover{border:#d3d3d3 dashed}.custom-upload-container{display:grid;grid-template-columns:repeat(auto-fill, minmax(220px, 1fr));gap:10px}.custom-upload-container>div{box-shadow:rgba(0,0,0,.05) 0px 6px 24px 0px,rgba(0,0,0,.08) 0px 0px 0px 1px;overflow:hidden;padding:0}.custom-upload-container .without-shadows{box-shadow:none;display:grid;align-items:center;justify-content:center;width:100%;grid-template-columns:1fr}.custom-upload-container .dvi-action-button{padding:7px 0;box-shadow:rgba(0,0,0,.05) 0px -6px 24px 0px,rgba(0,0,0,.08) 0px -1px 0px 0px;margin-top:1px}.custom-upload-container .dvi-action-button button{text-transform:capitalize !important}.custom-upload-container iframe{height:250px !important}", ".upload-zone{border:gray dashed;display:flex;justify-content:center;align-items:center;cursor:pointer;min-height:200px}.upload-zone:hover{border:#d3d3d3 dashed}", "\n.custom-grid{\n  display: grid;\n  grid-template-columns: 1fr 20px;\n}\n", ".component-badger-accordion .badger-accordion__panel{max-height:0}.badger-accordion__panel{max-height:2000vh !important;overflow:hidden}.badger-accordion__panel.-ba-is-hidden{max-height:0 !important}.badger-accordion--initialized .badger-accordion__panel{transition:max-height ease-in-out .2s}.badger-accordion__header .js-badger-accordion-header .badger-accordion-toggle{width:100%;background:rgba(0,0,0,0);border:0;box-shadow:none;display:flex;align-items:center}.badger-accordion__header .js-badger-accordion-header .badger-accordion-toggle .badger-accordion-title{flex:0 0 90%;cursor:pointer;text-align:left}.badger-accordion__header .js-badger-accordion-header .badger-accordion-toggle .badger-toggle-indicator{flex:0 0 10%;display:flex;align-items:center;justify-content:flex-end;cursor:pointer}"]}