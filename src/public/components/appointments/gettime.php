<?php
require(CONNWOSHOPID);
$shopid = isset($_REQUEST['shopid']) ? filter_var($_REQUEST['shopid'], FILTER_SANITIZE_STRING) : "";
$date = isset($_REQUEST['date']) ? filter_var($_REQUEST['date'], FILTER_SANITIZE_STRING) : "";
$res = '';

if(!empty($shopid) && !empty($date))
{
    $daynum = date('w',strtotime($date));

    $stmt = "select * from shophours where shopid = ? and day = ?";
    if ($query = $conn->prepare($stmt))
    {
         $query->bind_param("si",$shopid,$daynum);
         $query->execute();
         $r = $query->get_result();
         $rs = $r->fetch_array();
         $start = strtotime($rs['start']);
         $end = strtotime($rs['end']);

         if(!empty($start) && !empty($end))
         {
          for($i=$start;$i<=$end;$i=$i+1800)
          $res.="<option>".date('g:i A',$i)."</option>";
         }
         else
         {
         	if ($shopid != "2842" && $shopid != "6614") 
         	{
                if ($shopid != "6207") 
                {
                	$res.="<option>7:00 AM</option>";
                	$res.="<option>7:30 AM</option>";
                }

                $res.="<option>8:00 AM</option>";
                $res.="<option>8:30 AM</option>";
                                                                                                                
            }
            
            if ($shopid == "6614") 
            {
            	$res.="<option>8:00 AM</option>";
                $res.="<option>8:30 AM</option>";
            }

            $res.="<option>9:00 AM</option>";
            $res.="<option>9:30 AM</option>";
            $res.="<option>10:00 AM</option>";
            $res.="<option>10:30 AM</option>";
            $res.="<option>11:00 AM</option>";
            $res.="<option>11:30 AM</option>";
            $res.="<option>12:00 PM</option>";
            $res.="<option>12:30 PM</option>";
            $res.="<option>1:00 PM</option>";
            $res.="<option>1:30 PM</option>";
            $res.="<option>2:00 PM</option>";
            $res.="<option>2:30 PM</option>";
            $res.="<option>3:00 PM</option>";
            $res.="<option>3:30 PM</option>";
            $res.="<option>4:00 PM</option>";

            if ($shopid == "2842") 
            {
            	$res.="<option>4:30 PM</option>";
                $res.="<option>5:00 PM</option>";
            }
                                                                 
         }
    }
    echo($res);
}