<!DOCTYPE html>
<?php
require(CONNWOSHOPID);
require(PRIVATE_PATH."/integrations/mandrill/src/Mandrill.php");
if(!defined("ASSETS"))define("ASSETS", 'https://' . $_SERVER['SERVER_NAME'] . '/src/public/assets');


$shopid = isset($_REQUEST['shopid']) ? filter_var($_REQUEST['shopid'], FILTER_SANITIZE_STRING) : "";
$bookings = isset($_REQUEST['bookings']) ? filter_var($_REQUEST['bookings'], FILTER_SANITIZE_STRING) : "";
$error = isset($_REQUEST['error']) ? filter_var($_REQUEST['error'], FILTER_SANITIZE_STRING) : "";

if (empty($shopid)){
    //check for decription
    $qs = $_SERVER['QUERY_STRING'];
    $decrypted_qs = decryptData($qs);

    parse_str($decrypted_qs, $parsedData);

    $shopid = isset($parsedData['shopid']) ? filter_var($parsedData['shopid'], FILTER_SANITIZE_STRING) : null;

    $bookings = isset($parsedData['bookings']) ? filter_var($parsedData['bookings'], FILTER_SANITIZE_STRING) : null;
}

if (empty($shopid)){
?>
<link rel="stylesheet" href="<?= MDB ?>/css/mdb.min.css" type="text/css">

<div class="container mt-4">
    <div class="d-flex flex-column justify-content-center align-items-center">
      <title>Shop Boss - Appointment</title>
        <img class="align-self-center" style="max-width: 250px;" src="<?= IMAGE ?>/shopboss-logo.png"/>
        <p class="note note-danger">This page is only available to Shop Boss Pro clients.</p>
    </div>
</div>


<?php
} else {

    $firstname = isset($_REQUEST['firstname']) ? filter_var($_REQUEST['firstname'], FILTER_SANITIZE_STRING) : "";
    $lastname = isset($_REQUEST['lastname']) ? filter_var($_REQUEST['lastname'], FILTER_SANITIZE_STRING) : "";
    $customer_address = isset($_REQUEST['customer_address']) ? filter_var($_REQUEST['customer_address'], FILTER_SANITIZE_STRING) : "";
    $customer_city = isset($_REQUEST['customer_city']) ? filter_var($_REQUEST['customer_city'], FILTER_SANITIZE_STRING) : "";
    $customer_state = isset($_REQUEST['customer_state']) ? filter_var($_REQUEST['customer_state'], FILTER_SANITIZE_STRING) : "";
    $customer_zip = isset($_REQUEST['customer_zip']) ? filter_var($_REQUEST['customer_zip'], FILTER_SANITIZE_STRING) : "";
    $year = isset($_REQUEST['year']) ? filter_var($_REQUEST['year'], FILTER_SANITIZE_STRING) : "";
    $make = isset($_REQUEST['make']) ? filter_var($_REQUEST['make'], FILTER_SANITIZE_STRING) : "";
    $model = isset($_REQUEST['model']) ? filter_var($_REQUEST['model'], FILTER_SANITIZE_STRING) : "";
    $issue = isset($_REQUEST['issue']) ? filter_var($_REQUEST['issue'], FILTER_SANITIZE_STRING) : "";
    $adate = isset($_REQUEST['adate']) ? filter_var($_REQUEST['adate'], FILTER_SANITIZE_STRING) : "";
    $atime = isset($_REQUEST['atime']) ? filter_var($_REQUEST['atime'], FILTER_SANITIZE_STRING) : "";
    $phone = isset($_REQUEST['phone']) ? filter_var($_REQUEST['phone'], FILTER_SANITIZE_STRING) : "";
    $email = isset($_REQUEST['email']) ? filter_var($_REQUEST['email'], FILTER_SANITIZE_STRING) : "";
    $sendto = isset($_REQUEST['sendto']) ? filter_var($_REQUEST['sendto'], FILTER_SANITIZE_STRING) : "";

    $sathours = $sunhours = 0;
    $stmt = "select sathours,sunhours,cfpid from company where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->store_result();
        $query->bind_result($sathours, $sunhours, $cfpid);
        $query->fetch();
        $query->close();
    }

    $stmt = "select showcfp from settings where shopid = ?";
    if ($query = $conn->prepare($stmt)){
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($showcfp);
        $query->fetch();
        $query->close();
    }
    
    $shophours = array();

    $stmt = "select * from shophours where shopid = ?";
    if ($query = $conn->prepare($stmt))
    {
         $query->bind_param("s",$shopid);
         $query->execute();
         $r = $query->get_result();
         while ($rs = $r->fetch_array())
         $shophours[$rs['day']]=array('start'=>$rs['start'],'end'=>$rs['end']);
    }

    if (!empty($firstname)) {

        $firstname = strtoupper(str_replace("'", "''", $firstname));
        $lastname = strtoupper(str_replace("'", "''", $lastname));
        $year = strtoupper(str_replace("'", "''", $year));
        $make = strtoupper(str_replace("'", "''", $make));
        $model = strtoupper(str_replace("'", "''", $model));
        $issue = strtoupper(str_replace("'", "''", $issue));
        $phone = strtoupper(str_replace("'", "''", $phone));
        $dateTs = strtotime($adate);
        $dbdate = date("Y-m-d", $dateTs);

        $dbtime = strtotime($atime);
        $dbtime = date('H:i', $dbtime);

         $daynum = date('w',strtotime($dbdate));
         $maxhours = '';
         $stmt = "select hours from shopmaxhours where shopid = ? and day = ?";
         if ($query = $conn->prepare($stmt))
         {
            $query->bind_param("si",$shopid,$daynum);
            $query->execute();
            $query->bind_result($maxhours);
            $query->fetch();
            $query->close();
         }
         if($maxhours != '')
         {
            $bookedhours = 0;

            $stmt = "select sum(hours) from schedule where shopid = ? and schdate = ?";
            if ($query = $conn->prepare($stmt))
            {
                 $query->bind_param("ss",$shopid,$dbdate);
                 $query->execute();
                $query->bind_result($bookedhours);
                $query->fetch();
                $query->close();
            }

            if($bookedhours+1>$maxhours)
            {
              header("Location:appointment.php?shopid=".$shopid."&bookings=".$bookings."&error=1");
              exit();
            }
         }


        $cc = "";
        $stmt = "select colorhex from colorcoding where shopid = ? order by id asc";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("s", $shopid);
            $query->execute();
            $query->store_result();
            $query->bind_result($cc);
            $query->fetch();
            $query->close();
        }
        $stmt = "insert into schedule (colorcode,FirstName, LastName, Year, Make, Model, Reason, SchDate, SchTime, cellphone, Hours,shopid,`source`,display, email,customer_address,customer_city,customer_state,customer_zip) values (?,?,?,?,?,?,?,?,?,?,1,?,'O.A.S.','yes',?,?,?,?,?)";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("ssssssssssssssss", $cc, $firstname, $lastname, $year, $make, $model, $issue, $dbdate, $dbtime, $phone, $shopid, $email,$customer_address, $customer_city, $customer_state, $customer_zip);
            if ($query->execute()) {
                $conn->commit();
            } else {
                echo $conn->error;
            }
        } else {
            echo "prepared failed ".$conn->error;
        }

        if (!empty($sendto)) {
            $e = $sendto;
        } else {
            $stmt = "select companyemail from company where shopid = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("s", $shopid);
                $query->execute();
                $query->store_result();
                $query->bind_result($e);
                $query->fetch();
                $query->close();
            }
        }

        $stmt = "SELECT t.textcontent,t.emailcontent,t.popupcontent from notification_settings s,notification_types t WHERE s.notification_type=t.id AND s.shopid='$shopid' AND s.notification_type='95'";
        $query = $conn->prepare($stmt);
        $query->execute();
        $query->store_result();
        $numrows = $query->num_rows();
        if ($numrows > 0)
        {
         $query->bind_result($textcontent,$emailcontent,$popupcontent);
         $query->fetch();
         $rolink=$textrolink='';
         $customer=$firstname.' '.$lastname;
         $emailcontent=str_replace("*|CUSTOMER|*",$customer,str_replace("*|ROLINK|*",$rolink,$emailcontent));
         $popupcontent=str_replace("*|CUSTOMER|*",$customer,str_replace("*|ROLINK|*",$rolink,$popupcontent));
         $textcontent=str_replace("*|CUSTOMER|*",$customer,str_replace("*|TEXTROLINK|*",$textrolink,$textcontent));
         $stmt = "insert into notification_queue (shopid,notification_type,popup,text,email) values (?,'95',?,?,?)";
         if ($query = $conn->prepare($stmt))
         {
         $query->bind_param('ssss',$shopid,$popupcontent,$textcontent,$emailcontent);
         $query->execute();
         $conn->commit();
         $query->close();
         }
        } 

        $message = 'You have a new appointment scheduled online for ' . $adate . ' Please verify it has been added and contact the customer to confirm';
        $date = date('Y-m-d');
        $stmt = "insert into notifications (shopid,ndate,message) values (?, ?, ?)";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("sss", $shopid, $date, $message);
            if ($query->execute()) {
                $conn->commit();
            } else {
                echo $conn->error;
            }
        }


        $subject = "APPOINTMENT SET ON YOUR WEBSITE - SHOPBOSSPRO.COM";

        $message = "<img src='https://".$_SERVER['SERVER_NAME']."/src/public/assets/img/shopboss-logo-email.png'><br />A new appointment has been created in the Shop Boss Pro system.<br><br><b>Please verify the appointment has been entered and contact the customer to verify the appointment date and time</b><br><br> \n";
        $message .= "Name: " . $firstname . " " . $lastname . "<br />\n";
        $message .= "Phone: " . $phone . "<br />\n";
        $message .= "Email: " . $email . "<br />\n";
        $message .= "Year: " . $year . "<BR>";
        $message .= "Make: " . $make . "<BR>";
        $message .= "Model: " . $model . "<BR>";
        $message .= "Date Requested: " . $adate . "<BR>";
        $message .= "Time Requested: " . $atime . "<BR>";
        $message .= "Vehicle Issue: " . $issue . "<BR>";

        $res = sendEmailMandrill($e,$subject,$message,"Shop Boss Pro - Online Appointment",$email);

        if ($shopid == "5520") {
            header("Location:https://emcnv.com/thank-you");
            exit();
        } else {
            if($bookings=='yes')
            header("Location:apptset.php?shopid=".$shopid."&bookings=yes");
            else
            header("Location:apptset.php");
            exit();
        }
    }
    ?>

<html>
<head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8"/>
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">

    <title>Shop Boss - Appointment</title>

    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.11.2/css/all.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" />

    <link rel="stylesheet" href="<?= MDB ?>/css/mdb.min.css" type="text/css">
    <link rel="stylesheet" href="<?= CSS ?>/appointment.css" type="text/css">

    <?php if($bookings=='yes'){?>
    <link href='<?= ASSETS ?>/calendar/lib/fullcalendar.min.css' rel='stylesheet' />
    <link href='<?= ASSETS ?>/calendar/lib/fullcalendar.print.min.css' rel='stylesheet' media='print' />
    <link href='<?= ASSETS ?>/calendar/scheduler.min.css' rel='stylesheet' />
    <script src="<?= SCRIPT ?>/core/jquery.min.js"></script>
    <script src='<?= SCRIPT ?>/plugins/moment/moment.js'></script>
    <script src='<?= ASSETS ?>/calendar/lib/fullcalendar.min.js'></script>
    <script src='<?= ASSETS ?>/calendar/scheduler.min.js'></script>
    <?php }?>

</head>

<body class="appointment bg-white">
    <div class="container mt-4">
        <div class="card mb-2">
            <div class="card-body">
                <h2 class="card-title font-weight-bold text-center">Request An Appointment</h2>
                <div class="pt-4">
                    <div class="container-form">
                        <?php if($error=='1'){?>
                        <p class="note note-danger">Booking Failed. Max booking limit reached for the day</p>
                        <?php }?>
                        <p class="note note-info">Complete the information below. <b>We will contact you to confirm your appointment time</b></p>
                        <form id="mainform" name="mainform" action="appointment.php" method="post" class="row g-3" novalidate>
                            <input type="hidden" name="dfield" id="dfield"/>
                            <input name="shopid" type="hidden" value="<?php echo $shopid; ?>"/>
                            <input name="bookings" type="hidden" value="<?php echo $bookings; ?>"/>

                            <div class="col-12">
                                <div class="form-outline datepicker-with-filter" data-inline="true">
                                    <input type="text" class="form-control" id="adate" name="adate" required onkeydown="return false" />
                                    <label for="adate" class="form-label">Requested Date</label>
                                    <div class="invalid-feedback">Requested Date is required</div>
                                </div>
                            </div>

                            <div class="col-12">
                                <select class="select" data-placeholder="Requested Time" name="atime" id="atime" required>
                                </select>
                                <div class="invalid-feedback">Time is required</div>
                            </div>

                            <div class="col-12">
                                <div class="form-outline">
                                    <input type="text" class="form-control" id="firstname" name="firstname" required />
                                    <label class="form-label" for="firstname">First Name</label>
                                    <div class="invalid-feedback">First Name is required</div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-outline">
                                    <input type="text" class="form-control" id="lastname" name="lastname" required />
                                    <label class="form-label" for="lastname">Last Name</label>
                                    <div class="invalid-feedback">Last Name is required</div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-outline">
                                    <input type="text" class="form-control" id="customer_address" name="customer_address" />
                                    <label class="form-label" for="lastname">Address</label>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-outline">
                                    <input type="text" class="form-control" id="customer_city" name="customer_city" />
                                    <label class="form-label" for="lastname">City</label>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-outline">
                                    <input type="text" class="form-control" id="customer_state" name="customer_state" />
                                    <label class="form-label" for="lastname">State</label>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-outline">
                                    <input type="text" class="form-control" id="customer_zip" name="customer_zip" />
                                    <label class="form-label" for="lastname">Zip</label>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-outline">
                                    <input type="tel" class="form-control" id="phone" name="phone" onblur="checkPrequal()" required />
                                    <label class="form-label" for="phone">Phone Number</label>
                                    <div class="invalid-feedback">Phone Number is required</div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-outline">
                                    <input type="email" class="form-control" id="email" name="email" required />
                                    <label class="form-label" for="email">Email</label>
                                    <div class="invalid-feedback">Email is required</div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-outline">
                                    <input type="text" class="form-control" id="year" name="year" required />
                                    <label class="form-label" for="year">Vehicle Year</label>
                                    <div class="invalid-feedback">Vehicle Year is required</div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-outline">
                                    <input type="text" class="form-control" id="make" name="make" required />
                                    <label class="form-label" for="make">Vehicle Make</label>
                                    <div class="invalid-feedback">Vehicle Make is required</div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-outline">
                                    <input type="text" class="form-control" id="model" name="model" required />
                                    <label class="form-label" for="model">Vehicle Model</label>
                                    <div class="invalid-feedback">Vehicle Model is required</div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="form-outline">
                                    <textarea class="form-control" id="issue" name="issue" rows="4" required></textarea>
                                    <label class="form-label" for="issue">What does your vehicle need?</label>
                                    <div class="invalid-feedback">This field is required</div>
                                </div>
                            </div>

                            <div class="col-12 d-flex flex-column flex-sm-row justify-content-end">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-calendar-check mr-3"></i>
                                    Request Appointment
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <span id="prequalsec"></span>
        </div>
    </div>

    <?php if($bookings=='yes'){
     $stmt = "select coalesce(MIN(START),'06:00:00'),coalesce(MAX(end),'20:00:00') FROM shophours WHERE shopid = '$shopid'";
     if ($query = $conn->prepare($stmt)){
     $query->execute();
     $query->bind_result($minstart,$maxend);
     $query->fetch();
     $query->close();
     }
        ?>
        <div class="container mt-12" style="margin-top: 125px; padding-bottom: 100px;">
         <div id='calendar' style="cursor: pointer;"></div>
        </div>

        <script type="text/javascript">

            var isMobile = window.innerWidth < 768;

            $('#calendar').fullCalendar({

            defaultView: 'agendaWeek',
            defaultDate: '<?php echo date("Y-m-d"); ?>',
            minTime: "<?= $minstart?>",
            maxTime: "<?= $maxend?>",
            slotMinutes: 15,
            slotDuration: '00:15:00',
            slotLabelFormat: 'hh:mm',
            aspectRatio: 0.80,
            droppable: false,
            selectHelper: true,
            editable: false,
            selectable: false,
            allDaySlot: false,
            columnFormat: isMobile ? 'ddd\nM/D' : 'ddd M/D/Y',
            eventLimit: true, // allow "more" link when too many events
            timeFormat: 'h:mm T',
            header: {
                left: 'wipButton,prev,next today printbtn',
                center: 'title',
                right: 'agendaDay,agendaTwoDay,agendaWeek,month'
            },

            events: [
                    <?php
                    $start = date('Y-m-d',strtotime('-7 days'));
                    $stmt = "select * from schedule where shopid = ? and SchDate>='$start'";
                    if ($query = $conn->prepare($stmt)) {
                    $query->bind_param("s", $shopid);
                    $query->execute();
                    $results = $query->get_result();
                    while ($row = $results->fetch_assoc())
                    {
                    $enddatetime = strtotime($row['SchDate']." ".$row['SchTime'])+(3600 * $row['hours']);
                    ?>
                    {
                        textColor: "#ffffff",
                        start: '<?= $row['SchDate']."T".$row['SchTime'] ?>',
                        end: '<?= date('Y-m-d\TH:i:s',$enddatetime) ?>'
                    },
                <?php }}?>
                ],
            dayClick: function(date, jsEvent, view) {

                $('#adate').val(date.format('L')).focus()
                $('#atime option').removeAttr('selected')
                $('#atime option[value="'+date.format('h:mm A')+'"]').attr('selected','selected')
                $('#firstname').focus()
            },
            schedulerLicenseKey: 'GPL-My-Project-Is-Open-Source'
        })

        </script>
    <?php }?>
</body>

<script src="<?= MDB ?>/js/mdb.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.min.js"></script>

<script type="text/javascript">
    function filterFunction(date) {
        var day = date.getDay();
        <?php
        if(!empty($shophours))
        {
            $nostr = array();
            for($i=0;$i<=6;$i++)
            {
             if(!isset($shophours[$i]))
             $nostr[] = "day!=".$i;
            }

            if(!empty($nostr))
            echo "return ".implode(' && ', $nostr);
        }
        else
        {
        if ($sathours == 0 && $sunhours == 0) {
            echo "return day != 0 && day != 6;";
        }
        elseif ($sathours > 0 && $sunhours == 0) {
            echo "return day != 0;";
        }
        elseif ($sathours > 0 && $sunhours > 0) {
            echo "return day != 7;";
        }
        }
        ?>
    }

    function checkPrequal()
    {
      <?php if(!empty($cfpid) && strtolower($showcfp)=='yes'){?>

        var firstname = $('#firstname').val()
        var lastname = $('#lastname').val()
        var phone = $('#phone').val()
        var email = $('#email').val()

        $.ajax({
            data: "shopid=<?php echo $shopid; ?>&phone="+phone+"&firstname="+firstname+"&lastname="+lastname+"&email="+email,
            url: "https://<?= $_SERVER['SERVER_NAME']?>/src/public/components/appointments/getprequal.php",
            type: "post",
            success: function(r){
                console.log(r)
                $('#prequalsec').html(r)
            }
       })
      <?php }?>
    }

    document.addEventListener('DOMContentLoaded', (event) => {
        var options = {
            format: 'mm/dd/yyyy',
            filter: filterFunction
        }
        const dt = document.querySelector('.datepicker-with-filter');
        var myDatepicker = new mdb.Datepicker(dt, options);

        dt.addEventListener('dateChange.mdb.datepicker', (e) => {

            var seldate = document.getElementById('adate').value;

            var request = new XMLHttpRequest();
            request.open("GET", "gettime.php?shopid=<?= $shopid?>&date="+seldate);

            request.onreadystatechange = function() {

           if(this.readyState === 4 && this.status === 200) {
            document.getElementById('atime').innerHTML = this.responseText;
          }
       };

          request.send();
})


        const mainform = document.getElementById('mainform');
        mainform.addEventListener('submit', function (event) {
            if (!mainform.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                mainform.classList.add('was-validated');
            }
        }, false);
    });
</script>

</html>
<?php
}
?>
