<?php
require(CONNWOSHOPID);
?>
<!DOCTYPE html>
<html lang="en">

  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <title>Shop Boss Create Account/Trial Form</title>
    <!-- MDB icon -->
    <link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFavicon()?>' type='image/x-icon'/ >
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <!-- Google Fonts Roboto -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" />
    <!-- MDB ESSENTIAL -->
    <link rel="stylesheet" href="<?= MDB ?>/css/mdb.min.css" />
    <!-- Custom styles -->
    <style>
    .logo {
      max-width: 200px;
    }

    .overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.5);
      z-index: 9999;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .spinner-border {
      width: 3rem;
      height: 3rem;
      color: #007bff;
    }

    @media (max-width: 768px) {
      .mobile-mb-4 {
        margin-bottom: 1.5rem !important;
        /* adjust the margin as needed */
      }

      .mobile-center {
        display: block;
        margin: 0 auto;
        text-align: center;
      }
    }

    @media (max-width: 576px) {
      .mobile-mb-4 {
        margin-bottom: 1.5rem !important;
        /* adjust the margin as needed */
      }

      .mobile-center {
        display: block;
        margin: 0 auto;
        text-align: center;
      }
    }
    </style>
  </head>

  <body>
    <!-- Start your project here-->
    <div class="mb-5">
      <div class="container mt-3 text-center">
        <div class="row">
          <div class="col">
            <img class="logo" src="<?= IMAGE?>/shopboss-logo.png"
              alt="Shop Boss Logo" />
          </div>
        </div>
        <div>
          <p class="h6">Shop Boss Create Account/Trial Form</p>
        </div>
      </div>
      <div class="container mt-3">
        <div id="result"></div>
        <form class="needs-validation" novalidate method="post"
          action="https://<?=$_SERVER['SERVER_NAME']?>/src/public/endpoints/trial/post.php">
          <!-- Shop Contact information  -->
          <p class="h5 mobile-center">Shop Contact Information</p>
          <hr class="hr" />
          <div class="row mb-4">
            <div class="col-12 col-md-6 mobile-mb-4 ">
              <select class="form-select" name="salesrep" id="salesrep" required>
                <option selected disabled value="">Sales Rep</option>
                <?php
                $stmt = "select id,first_name,last_name from adminlogin where salesrep='yes' order by first_name,last_name";
                if ($query = $conn->prepare($stmt)) 
                {
                    $query->execute();
                    $results = $query->get_result();
                    while ($rs = $results->fetch_assoc())
                    echo("<option value='".$rs['id']."'>".$rs['first_name'].' '.$rs['last_name']."</option>");
                }
                ?>
              </select>
              <div class="invalid-feedback">Sales Rep Required </div>
            </div>
            <!-- Text input -->
            <div class="col-12 col-md-6">
              <div class="form-outline">
                <input type="text" id="shop" class="form-control" name="shop" required />
                <div class="invalid-feedback"> Shop Name Required </div>
                <label class="form-label" for="shop">Shop Name</label>
              </div>
            </div>
          </div>
          <div class="row mb-4">
            <!-- Text input -->
            <div class="col-12 col-md-6  mobile-mb-4">
              <div class="form-outline">
                <input type="text" id="first_name" class="form-control" name="first_name" required />
                <div class="invalid-feedback"> First Name Required </div>
                <label class="form-label" for="first_name">First Name</label>
              </div>
            </div>
            <!-- Text input -->
            <div class="col-12 col-md-6 ">
              <div class="form-outline">
                <input type="text" id="last_name" class="form-control" name="last_name" required />
                <div class="invalid-feedback"> Last Name Required </div>
                <label class="form-label" for="last_name">Last Name</label>
              </div>
            </div>
          </div>
          <!-- Shop Address 1 -->
          <div class="row mb-4">
            <div class="col-12 col-md-6  mobile-mb-4 ">
              <div class="form-outline">
                <input type="email" id="email" class="form-control" name="email" required />
                <div class="invalid-feedback"> Shop Email Required </div>
                <label class="form-label" for="email">Shop Email</label>
              </div>
            </div>
            <!-- Text input -->
            <div class="col-12 col-md-6 ">
              <div class="form-outline">
                <input type="text" id="phone" class="form-control" name="phone" required />
                <div class="invalid-feedback"> Phone Required </div>
                <label class="form-label" for="phone">Phone</label>
              </div>
            </div>
          </div>
          <div class="row mb-4">
            <div class="col">
              <div class="form-outline">
                <input type="text" id="address_street" class="form-control" name="address_street" required />
                <div class="invalid-feedback"> Shop Address Required </div>
                <label class="form-label" for="address_street">Shop Address</label>
              </div>
            </div>
          </div>
          <div class="row mb-4">
            <div class="col-12 col-md-6  mobile-mb-4 ">
              <div>
                <select id="address_country" name="address_country" class="form-select"
                  onchange="toggleStateProvince()">
                  <option value="United States" selected>United States</option>
                  <option value="Canada">Canada</option>
                </select>
                <div class="invalid-feedback"> Select Country </div>
              </div>
            </div>
            <div class="col-12 col-md-6 ">
              <div>
                <select class="form-select" id="shop_timezone" name="shop_timezone" required>
                  <option selected disabled value="">Time Zone</option>
                  <option value="pst">Pacific - PST</option>
                  <option value="azst">Arizona - AZST</option>
                  <option value="mst">Mountain - MST</option>
                  <option value="cst">Central - CST</option>
                  <option value="est">Eastern - EST</option>
                  <option value="ast">Atlantic - AST</option>
                  <option value="astnd">Atlantic no DST - ASTND</option>
                  <option value="akst">Alaska - AKST</option>
                  <option value="hst">Hawaii - HST</option>
                  <option value="eat">East Africa - EAT</option>
                  <option value="chst">Chamorro - CHST</option>
                </select>
                <div class="invalid-feedback"> Select Time Zone </div>
              </div>
            </div>
          </div>
          <div class="row mb-4">
            <!-- Text input -->
            <div class="col-12 col-md-4 ">
              <div class="form-outline">
                <input type="text" id="address_zip" class="form-control" name="address_zip" required />
                <div class="invalid-feedback"> Zip/Postal Code Required </div>
                <label class="form-label" for="address_zip">Zip/Postal Code</label>
              </div>
            </div>
            <div class="col-12 col-md-4  mobile-mb-4">
              <div class="form-outline">
                <input type="text" id="address_city" class="form-control" name="address_city" required />
                <div class="invalid-feedback"> City Required </div>
                <label class="form-label" for="address_city">City</label>
              </div>
            </div>
            <div class="col-12 col-md-4  mobile-mb-4 ">
              <div>
                <select class="form-select" id="address_state" name="address_state" required>
                  <option selected disabled value="">State/Province</option>
                </select>
                <div class="invalid-feedback"> Select State/Providence </div>
              </div>
            </div>
          </div>
          <!-- Shop Settings -->
          <p class="h5">Shop Settings</p>
          <hr class="hr" />
          <div class="row mb-4">
            <div class="col-12 col-md-6  mobile-mb-4">
              <label class="visually-hidden" name="shop_type" for="shop_type">Shop Type</label>
              <select class="select">
                <option selected disabled value="">Shop Type</option>
                <option value="2">General Repair</option>
                <option value="3">Tire Shop</option>
                <option value="4">Transmission</option>
                <option value="5">Specialist</option>
                <option value="6">Fleet</option>
                <option value="7">Custom</option>
                <option value="8">Mobile</option>
                <option value="9">Truck</option>
                <option value="10">Other</option>
              </select>
            </div>
            <!-- Text input -->
            <div class="col-12 col-md-6 ">
              <label class="visually-hidden" name="current_sms" for="current_sms">Current SMS Provider</label>
              <select class="select">
                <option selected disabled value="">Current SMS Provider</option>
                <option value="AllData SMS">AllData SMS</option>
                <option value="Andreolli">Andreolli</option>
                <option value="ASA">ASA</option>
                <option value="Easy RO">Easy RO</option>
                <option value="Freedomsoft">Freedomsoft</option>
                <option value="FullBay">FullBay</option>
                <option value="Lankar">Lankar</option>
                <option value="MaddenCo">MaddenCo</option>
                <option value="MaxxTraxx">MaxxTraxx</option>
                <option value="Mitchell1">Mitchell1</option>
                <option value="NapaTracs">NapaTracs</option>
                <option value="NAPA TRACS Enterprise">NAPA TRACS Enterprise</option>
                <option value="NAPA TRACS Legacy">NAPA TRACS Legacy</option>
                <option value="Omnique">Omnique</option>
                <option value="Other">Other</option>
                <option value="ProfitBoost">ProfitBoost</option>
                <option value="Protractor">Protractor</option>
                <option value="RO Writer">RO Writer</option>
                <option value="ShopBoss">ShopBoss</option>
                <option value="ShopMonkey">ShopMonkey</option>
                <option value="Shop-Ware">Shop-Ware</option>
                <option value="Simply Genius">Simply Genius</option>
                <option value="TCS">TCS</option>
                <option value="TekMetric">TekMetric</option>
                <option value="Tire Guru">Tire Guru</option>
                <option value="VAST- MAM Software">VAST- MAM Software</option>
              </select>
              </select>
            </div>
          </div>
          <!-- shop rates row -->
          <div class="row">
            <div class="col-12 col-md-6 ">
              <div class="input-group mb-3">
                <span class="input-group-text">$</span>
                <div class="form-outline">
                  <input type="text" class="form-control" />
                  <label class="form-label" for="laborrate">Labor Rate</label>
                </div>
              </div>
            </div>
            <div class="col-12 col-md-6 ">
              <div class="input-group mb-3">
                <span class="input-group-text">%</span>
                <div class="form-outline">
                  <input type="text" class="form-control" />
                  <label class="form-label" for="taxrate">Parts Tax Rate</label>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-12 col-md-6 ">
              <div class="input-group mb-3">
                <span class="input-group-text">%</span>
                <div class="form-outline">
                  <input type="text" class="form-control" />
                  <label class="form-label" for="labortaxrate">Labor Tax Rate</label>
                </div>
              </div>
            </div>
            <div class="col-12 col-md-6 ">
              <div class="input-group mb-3">
                <span class="input-group-text">%</span>
                <div class="form-outline">
                  <input type="text" class="form-control" />
                  <label class="form-label" for="sublettaxrate">Sublet Tax Rate</label>
                </div>
              </div>
            </div>
          </div>

          <div class="row mb-4">
            <div class="col-12 col-md-6 ">
              <div class="form-check">
                <input class="form-check-input me-2" type="checkbox" value="" id="tempdata" name="tempdata" />
                <label class="form-check-label" for="tempdata"> Include Test Data </label>
              </div>
            </div>
            <div class="col-12 col-md-6 ">
              <div class="form-check">
                  <input class="form-check-input me-2" type="checkbox" value="1" id="matco" name="matco" />
                  <label class="form-check-label" for="matco"> Matco </label>
              </div>
            </div>
          </div>
          <!-- end shop rates row -->
          <!-- Checkbox -->
        
          <p class="h5">Misc Options</p>
          <hr class="hr" />
          <div class="row mb-4">
            <div class="col-12 col-md-6  mobile-mb-4">
              <label class="visually-hidden" for="source">Referral Source</label>
              <select class="select" name="source">
                <option selected disabled value="">Referral Source</option>
                <option value="aapexjoesgarage">AAPEX - Joe's Garage</option>
                <option value="bing">Bing</option>
                <option value="capterra">Capterra</option>
                <option value="facebook">Facebook</option>
                <option value="garagerehab">Garage Rehab</option>
                <option value="google">Google</option>
                <option value="nascar">NASCAR</option>
                <option value="ratchetandwrench">Ratchet and Wrench</option>
                <option value="spotlight">Spotlight Video</option>
                <option value="youtube">YouTube</option>
                <option value="referral">Referral</option>
                <option value="other">Other</option>
              </select>
            </div>
            <!-- Text input -->
            <div class="col-12 col-md-6 ">
              <div class="form-outline">
                <input type="text" id="referralcode" class="form-control" name="referralcode" />
                <label class="form-label" for="referralcode">Referral Code</label>
              </div>
            </div>
            
          </div>

          <div class="row mb-4">
            <div class="col-12 col-md-6 ">
              <div class="form-outline">
                <input type="password" id="salesreppassword" class="form-control" name="salesreppassword" required />
                <div class="invalid-feedback"> Sales Rep Password Required </div>
                <label class="form-label" for="referralcode">Sales Rep Password</label>
              </div>
            </div>

            <div class="col-12 col-md-6 ">
              <label class="visually-hidden" for="package">Package</label>
              <select class="select" name="package">
                <option value="silver">Silver</option>
                <option value="gold">Gold</option>
                <option value="platinum" selected>Platinum</option>
                <option value="premier">Premier</option>
                <option value="premier plus">Premier Plus</option>
              </select>
              </select>
            </div>
            
          </div>

          <!-- Submit button -->
          <button type="submit" class="btn btn-primary btn-block mb-2" id="submit-button">Create Account/Trial</button>
          <center>By clicking this button you are creating a new account in the Shop Boss system</center>
          <div id="loader-container" style="display: none;">
            <div class="overlay">
              <div class="spinner-border"></div>
            </div>
          </div>
        </form>
      </div>
    </div>
    <!-- hidden form field for password create_function -->
    <!-- End your project here-->
  </body>
  <!-- MDB ESSENTIAL -->
  <script src="<?= MDB ?>/js/mdb.min.js"></script>

  <!-- Custom scripts -->
  <script type="text/javascript">
  function toggleStateProvince() {
    const countrySelect = document.getElementById("address_country");
    const stateProvinceSelect = document.getElementById("address_state");
    if (countrySelect.value === "United States") {
      stateProvinceSelect.innerHTML = `
  <option value="">State</option>
        <option value="AL">Alabama</option>
        <option value="AK">Alaska</option>
        <option value="AZ">Arizona</option>
        <option value="AR">Arkansas</option>
        <option value="CA">California</option>
        <option value="CO">Colorado</option>
        <option value="CT">Connecticut</option>
        <option value="DE">Delaware</option>
        <option value="DC">District Of Columbia</option>
        <option value="FL">Florida</option>
        <option value="GA">Georgia</option>
        <option value="HI">Hawaii</option>
        <option value="ID">Idaho</option>
        <option value="IL">Illinois</option>
        <option value="IN">Indiana</option>
        <option value="IA">Iowa</option>
        <option value="KS">Kansas</option>
        <option value="KY">Kentucky</option>
        <option value="LA">Louisiana</option>
        <option value="ME">Maine</option>
        <option value="MD">Maryland</option>
        <option value="MA">Massachusetts</option>
        <option value="MI">Michigan</option>
        <option value="MN">Minnesota</option>
        <option value="MS">Mississippi</option>
        <option value="MO">Missouri</option>
        <option value="MT">Montana</option>
        <option value="NE">Nebraska</option>
        <option value="NV">Nevada</option>
        <option value="NH">New Hampshire</option>
        <option value="NJ">New Jersey</option>
        <option value="NM">New Mexico</option>
        <option value="NY">New York</option>
        <option value="NC">North Carolina</option>
        <option value="ND">North Dakota</option>
        <option value="OH">Ohio</option>
        <option value="OK">Oklahoma</option>
        <option value="OR">Oregon</option>
        <option value="PA">Pennsylvania</option>
        <option value="RI">Rhode Island</option>
        <option value="SC">South Carolina</option>
        <option value="SD">South Dakota</option>
        <option value="TN">Tennessee</option>
        <option value="TX">Texas</option>
        <option value="UT">Utah</option>
        <option value="VT">Vermont</option>
        <option value="VA">Virginia</option>
        <option value="WA">Washington</option>
        <option value="WV">West Virginia</option>
        <option value="WI">Wisconsin</option>
        <option value="WY">Wyoming</option>
  
    `;
      stateProvinceSelect.disabled = false;
    } else if (countrySelect.value === "Canada") {
      stateProvinceSelect.innerHTML = `
        <option value="">Province</option>
        <option value="AB">Alberta</option>
        <option value="BC">British Columbia</option>
        <option value="MB">Manitoba</option>
        <option value="NB">New Brunswick</option>
        <option value="NL">Newfoundland and Labrador</option>
        <option value="NT">Northwest Territories</option>
        <option value="NS">Nova Scotia</option>
        <option value="NU">Nunavut</option>
        <option value="ON">Ontario</option>
        <option value="PE">Prince Edward Island</option>
        <option value="QC">Quebec</option>
        <option value="SK">Saskatchewan</option>
        <option value="YT">Yukon</option>

    `;
      stateProvinceSelect.disabled = false;
    } else {
      stateProvinceSelect.innerHTML = `
      <option value="">State/Province</option>
    `;
      stateProvinceSelect.disabled = true;
    }
  }
  toggleStateProvince();
  </script>
  <script>
  const form = document.querySelector('.needs-validation');
  const submitButton = document.getElementById('submit-button');
  const loaderContainer = document.getElementById('loader-container');
  const zip = document.getElementById('address_zip');

  submitButton.addEventListener('click', (event) => {
    event.preventDefault();
    if (form.checkValidity()) {
      submitButton.disabled = true;
      loaderContainer.style.display = 'flex';
      fetch(form.action, {
        method: 'POST',
        body: new FormData(form)
      }).then(response => response.json()).then(data => {
        const resultDiv = document.getElementById('result');
        if (data.status) {
          const alertDiv = document.createElement('div');
          alertDiv.classList.add('alert', 'alert-success');
          alertDiv.setAttribute('role', 'alert');
          alertDiv.innerHTML = 'Trial Successfully Created! Shop ID: <strong>' + data.shopid + '</strong>';
          resultDiv.innerHTML = '';
          resultDiv.appendChild(alertDiv);
          form.reset(); // Clear form fields
          form.classList.remove('was-validated'); // Remove validation messages
        } else {
          const alertDiv = document.createElement('div');
          alertDiv.classList.add('alert', 'alert-danger');
          alertDiv.setAttribute('role', 'alert');
          alertDiv.textContent = data.message;
          resultDiv.innerHTML = '';
          resultDiv.appendChild(alertDiv);
        }
        submitButton.disabled = false;
        window.scrollTo(0, 0);
      }).catch(error => {
        console.error(error);
        const resultDiv = document.getElementById('result');
        const alertDiv = document.createElement('div');
        alertDiv.classList.add('alert', 'alert-danger');
        alertDiv.setAttribute('role', 'alert');
        alertDiv.textContent = 'An error occurred while processing the request.';
        resultDiv.innerHTML = '';
        resultDiv.appendChild(alertDiv);
      }).finally(() => {
        loaderContainer.style.display = 'none';
        submitButton.disabled = false;
        window.scrollTo(0, 0);
      });
    } else {
      form.classList.add('was-validated');
    }
  });

  zip.addEventListener('blur', (event) => {
    event.preventDefault();

    fetch("https://<?=$_SERVER['SERVER_NAME']?>/src/private/components/customer/getcsfromzip.php?z="+zip.value, {
      }).then(response => response.text()).then(data => {
        if(data != '')
        {
          const dtarr = data.split('|');
          document.getElementById('address_city').value = dtarr[1];
          document.getElementById('address_state').value = dtarr[2];
          setTimeout(function(){
          document.getElementById('address_city').focus();
          document.getElementById('address_city').blur();},100);
        }
      })
  })
  

    var tempdataCheckbox = document.getElementById("tempdata");
    tempdataCheckbox.addEventListener('change', function() {
        if (this.checked) {
            this.value = "YES";
        } else {
            this.value = "";
        }
    });

    var matcoCheckbox = document.getElementById("matco");
    matcoCheckbox.addEventListener('change', function() {
        if (this.checked) {
            this.value = "yes";
        } else {
            this.value = "no";
        }
    });

</script>
  
</html>