<?php

require CONNWOSHOPID;
require(PRIVATE_PATH."/integrations/mandrill/src/Mandrill.php");

if ($_POST['t'] == "shopid"){

	$shopid = trim($_POST['shopid']);
	$newui = $_POST['newui']??'no';

	if (strtolower($shopid) != "demo"){
		$stat = "none";
		$package = "none";
		$r = "unknown";

		$s = $_POST['ip'];
		$stmt = "select ipaddress from shopips where shopid = '$shopid'";
		$iplist = "";
		if ($query = $conn->prepare($stmt)){

			$ar = array();
			$query->execute();
			$r = $query->get_result();
			while ($rs = $r->fetch_array()){
				array_push($ar,$rs['ipaddress']);
			}

		}
		$restrictions = "no";
		$ipcount = count($ar);
		if ($ipcount > 0){

			foreach ($ar as $v){
				if ($v == $s){
					$restrictions = "no";
					break;
				}else{
					$restrictions = "yes";
				}
			}

		}else{

			$restrictions = "no";

		}

		if ($s == "************"){
			$restrictions = "no";
		}

		if ($restrictions == "no"){

			$stmt = "select `status`,`package` from company where shopid = '$shopid'";

			if ($query = $conn->prepare($stmt)){
				$query->bind_result($stat,$package);
				$query->execute();
			    $query->fetch();
			    //echo $stat."|".$package."\r\n";
		    	/*if (strtolower($stat) == "suspended" && strtolower($package) == "paid"){
		    		$r = "paidsuspended";
		    		if (strtolower($package) == "paid"){
		    			$r = "paidsuspended";
		    		}elseif (strtolower($package) == "trial"){
		    			$r = "trialsuspended";
		    		}
		    	}else{
		    		$r = "success";
		    	}*/

		    	if ($stat == "" && $package == ""){
		    		$r = "error";
		    	}
		    	else
		    	$r = "success";

		    	$query->close();
			}else{
				echo $r;
			}


			$rs = array();
			if ($r == "success"){
				// output a list of users
				$stmt = "Select concat(employeefirst,' ',employeelast) as login, id from employees where shopid = '$shopid' and lcase(active) = 'yes' and logintosbp = 'yes' ";
				if($newui == 'yes') $stmt .= "order by employeelast";
				if ($query = $conn->prepare($stmt)){
					if ($query->execute()){
					    $result = $query->get_result();
					    //$rs = array();
						while($row = $result->fetch_assoc()){
							array_push($rs, array("login" => $row['login'], "id" => $row['id']));
						}
						//array_push($rs, array("login" => "Shop Boss Support", "id" => "999999999"));
						//echo json_encode($rs);
						$query->close();
					    //$tquery->close();
					}else{
						echo $query->error;
					}
				}else{
					echo $conn->error;
				}
				//array_push($rs, array("login" => "Shop Boss Support", "id" => "999999999"));
				echo json_encode($rs);
			}else{
				echo $r;
			}
		}else{
			echo "iprestriction";
		}

	}else{

		$rs = array();
		array_push($rs, array("login" => "Demo", "id" => "999999998"));
		echo json_encode($rs);
	}

}elseif ($_POST['t'] == "user"){

	$shopid = trim($_POST['shopid']);
	$id = $_POST['id'];
	$pwd = $_POST['pwd'];

	if ($id == "999999999"){
		if ($pwd == "y@SCren}cE5u\pE9"){
			echo "success";
		}else{
			echo "error";
		}
	}elseif ($id == "999999998"){
		if (strtolower($pwd) == "demo"){
			echo "success";
		}else{
			echo "error";
		}
	}else{

		// check the password and return success
		$stmt = "select id from employees where shopid = '$shopid' and id = $id and password = ?";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("s",$pwd);
		    $query->execute();
		    $query->store_result();
		    $num_rows = $query->num_rows;
		    if ($num_rows > 0){
		    	echo "success";
		    }else{
		    	echo "error";
		    }
		}

	}



}

elseif ($_POST['t'] == "securecheck"){

	$email = filter_var($_POST['email'], FILTER_SANITIZE_STRING);
	$password = filter_var($_POST['password'], FILTER_SANITIZE_STRING);

	
	$stmt = "select e.shopid,e.id,e.passwordenc,c.status,e.active from employees e,company c where c.shopid=e.shopid and e.employeeEmail = ? limit 1";
	if ($query = $conn->prepare($stmt)){
		$query->bind_param("s",$email);
	    $query->execute();
	    $query->store_result();
	    $num_rows = $query->num_rows;
	    if ($num_rows > 0){
	    	$query->bind_result($shopid,$userid,$passwordhash,$shopstatus,$empactive);
		    $query->fetch();
		    if(strtolower($empactive)!='yes')
		    echo json_encode(array("status"=>"error","msg"=>"Your employee profile is inactive. Please contact the shop owner"));
		    elseif(strtolower($shopstatus)!='active')
		    echo json_encode(array("status"=>"error","msg"=>"Your shop is inactive. Please contact support"));
		    elseif(password_verify($password, $passwordhash))
	    	echo json_encode(array("status"=>"success","id"=>$userid,"shopid"=>$shopid));
	        else
            echo json_encode(array("status"=>"error","msg"=>"Invalid Email or Password"));
	    }else{
	    	echo json_encode(array("status"=>"error","msg"=>"Invalid Email or Password"));
	    }
	}


}

elseif ($_POST['t'] == "forgotpass"){

	$email = filter_var($_POST['email'], FILTER_SANITIZE_STRING);

	if(!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL))
	{
		$stmt = "select e.id,c.matco,c.protractor,c.shopid from employees e,company c where c.shopid=e.shopid and e.employeeEmail = ? and c.status='ACTIVE' and e.active='yes' limit 1";
		if ($query = $conn->prepare($stmt)){
			$query->bind_param("s",$email);
		    $query->execute();
		    $query->store_result();
		    $num_rows = $query->num_rows;
		    if ($num_rows > 0){
		    	$query->bind_result($empid,$matco,$protractor,$shopid);
			    $query->fetch();
			    $query->close();
		    	$newpass = generateRandomStr(7).rand(0,9);
		    	$newpassenc = password_hash($newpass, PASSWORD_DEFAULT);
		    	$msg = "Please use the below password for login.<br><br><b>".$newpass."</b>";
		    	$from = ($matco=='yes'?"<EMAIL>":($protractor=='yes'?"<EMAIL>":"<EMAIL>"));
		    	$res = sendEmailMandrill($email,"Reset Password",$msg,"Shop Boss Pro",$from,NULL);
		    	$stmt = "update employees set password=?, passwordenc=? where shopid = ? and id = ?";
			     if ($query = $conn->prepare($stmt))
			     {
			     $query->bind_param("sssi",$newpass, $newpassenc,$shopid,$empid);
				 $query->execute();
				 $conn->commit();
				 $query->close();
				 echo(json_encode(array('status'=>"success")));
			     }
		    }else{
		    	echo json_encode(array("status"=>"error","msg"=>"Invalid Email"));
		    }
		}
	}
	else
	echo json_encode(array("status"=>"error","msg"=>"Invalid Email"));


}

elseif ($_POST['t'] == "checkpasschange")
{
 $shopid = trim($_POST['shopid']);
 $empid = $_POST['empid'];

 $stmt = "select changepass from employees where shopid = ? and id = ?";
 if ($query = $conn->prepare($stmt))
 {
	$query->bind_param("si",$shopid,$empid);
    $query->execute();
    $result = $query->get_result();
	$row = $result->fetch_assoc();
	if($row['changepass']=='1')echo("yes");
 }
}

elseif ($_POST['t'] == "changepass")
{
 $shopid = trim($_POST['shopid']);
 $empid = $_POST['empid'];
 $password = $_POST['password'];
 $passwordhash = password_hash($password, PASSWORD_DEFAULT);

 if(!empty($shopid) && !empty($empid) && !empty($_POST['password']))
 {
  $stmt = "select passwordenc from employees where shopid = ? and id = ?";
  if ($query = $conn->prepare($stmt))
  {
	$query->bind_param("si",$shopid,$empid);
    $query->execute();
    $result = $query->get_result();
	$row = $result->fetch_assoc();
	if(password_verify($password, $row['passwordenc']))
	{
		echo(json_encode(array('status'=>false,'msg'=>"New password cannot be same as the old password.")));
	}
	else
	{
		$stmt = "update employees set password=?, passwordenc=?, changepass='0', datepasschanged=now() where shopid = ? and id = ?";
	     if ($query = $conn->prepare($stmt))
	     {
	     $query->bind_param("sssi",$password, $passwordhash,$shopid,$empid);
		 $query->execute();
		 $conn->commit();
		 $query->close();
		 echo(json_encode(array('status'=>"success")));
	     }
	}
  }
 }
 else
 echo(json_encode(array('status'=>false,'msg'=>"Invalid Parameters")));

}





?>
<?php if(isset($conn)){mysqli_close($conn);} ?>
