<?php
session_start();
require CONNWOSHOPID;
$mylist=array("<",">","'","(",")","]","[","}","{","--","=","|");
foreach($_REQUEST as $key => $value)
{
	if(in_array($key, $mylist) || in_array($value,$mylist))
	{
		header("location:../bad.asp");
		exit;
	}
}

$userip = $_SERVER['REMOTE_ADDR'];
if(isset($_GET['companyID']))
$compid = $_GET['companyID'];
else
$compid = '';

if(isset($_GET['ref']))$ref = $_GET['ref'];
elseif(isset($_GET['redirect']))$ref = $_GET['redirect'];


if($_SERVER['SERVER_NAME'] == "shopbosspro.com")header("location:https://www.shopbosspro.com/login.php".(isset($ref)?"?ref=".$ref:''));

if (isset($_SESSION['mode']) && isset($_SESSION['shopid']) && isset($_SESSION['shopname']) && isset($_SESSION['usr']) && $_COOKIE['interface']!='1')
{
	header("location:/wip/wip.php");
	exit;
}
?>
<!DOCTYPE html>
<html>
<head><meta name="robots" content="noindex,nofollow">
<link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFaviconLogin()?>' type='image/x-icon' />
<meta http-equiv="Content-Type" content="text/html; charset=windows-1252">
<meta name="GENERATOR" content="Microsoft FrontPage 12.0">
<meta name="viewport" content="width=device-width">
<meta name="ProgId" content="FrontPage.Editor.Document">
<script src="<?= SCRIPT ?>/jquery-1.10.2.min.js"></script>
<script src="<?= SCRIPT ?>/jquery/jquery.countdown.js"></script>
<script src="<?= SCRIPT ?>/bootstrap.min.js"></script>
<script src="<?= SCRIPT ?>/bowser.js"></script>
<link rel='shortcut icon' href='<?= IMAGE ?>/<?= getFaviconLogin()?>' type='image/x-icon' />
<script type="text/javascript" src="support/js/jquery.prettyPhoto.js"></script>
<script src="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.js"></script>
<script src="<?= SCRIPT ?>/plugins/webticker/webticker.js"></script>
<script src="<?= SCRIPT ?>/emodal.js?v=6.1"></script>
<link rel="stylesheet" href="<?= SCRIPT ?>/plugins/sweetalert/sweetalert.min.css" type="text/css" media="screen">
<link rel="stylesheet" href="<?= CSS ?>/prettyPhoto.css" type="text/css" media="screen">
<link rel="stylesheet" href="<?= CSS ?>/bootstrap.min.css" type="text/css" media="screen">
<link rel="stylesheet" href="<?= CSS ?>/oneui.css" type="text/css" media="screen">
<link rel="stylesheet" href="<?= CSS ?>/funkycheckboxes.css?v=1.1">
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.3/css/all.css" integrity="sha384-UHRtZLI+pbxtHCWp1t77Bi1L4ZtiqrqD80Kn4Z8NTSRyMA2Fd33n5dQ8lWUE00s/" crossorigin="anonymous">
<title><?= getPageTitleLogin() ?></title>
<style>

body {
	background-color: white !important;
}

.img-responsive {
    width: 50%;
}

.form-control:focus{
	background-color:#FFFF99;
}
.logo{
	max-height:30px;
}
.iconlog{
	padding:0px;
	border:1px silver solid;
	border-radius:4px;
	max-height:50px;
	cursor:pointer;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
}

.imgselected{
	-webkit-box-shadow: 0px 0px 5px 5px rgba(255,119,0,1);
	-moz-box-shadow: 0px 0px 5px 5px rgba(255,119,0,1);
	box-shadow: 0px 0px 5px 5px rgba(255,119,0,1);
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;

}
#loginresults{
	text-align:center
}

#loginparent{
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
}

.sbp-form-control2{
	padding:6px;
	border:1px silver solid;
	border-radius:3px;
}

.sbp-form-control2:focus{
	background-color:#FFFF99
}
.auto-style1 {
	font-size: large;
	color:red;
}
.auto-style2 {
	font-size: large;
	color:#006600;
}

.auto-style3 {
	text-align: center;
	margin-bottom: 0px;
}

.auto-style4 {
	font-size: 12px;
}

.auto-style5 {
	font-size: large;
}

.auto-style11 {
	font-size: x-large;
}

.auto-style12 {
	text-align: left;
	font-size: medium;
}

.auto-style13 {
	text-align: center;
	font-size: large;
}
.box{
	width:60%;
	margin:auto
}

@media only screen and (max-width: 1400px) {

	.box{
		width:90%;
		margin:auto;
	}

}

.auto-style14 {
	color: #286090;
}

.auto-style15 {
	text-indent: -.25in;
	line-height: 107%;
	font-size: 11.0pt;
	font-family: Calibri, sans-serif;
	margin-left: .5in;
	margin-right: 0in;
	margin-top: 0in;
	margin-bottom: .0001pt;
}
.auto-style16 {
	text-indent: -.25in;
	line-height: 107%;
	font-size: 11.0pt;
	font-family: Calibri, sans-serif;
	margin-left: .5in;
	margin-right: 0in;
	margin-top: 0in;
	margin-bottom: 8.0pt;
}

.sbsection {
	text-align: left;
	font-size: large;
}

.auto-style21 {
	position: relative;
	min-height: 1px;
	float: left;
	width: 100%;
	text-align: center;
	padding-left: 15px;
	padding-right: 15px;

}


body {

background-color: white!important;
}

.sb-img {
    max-width: 350px;
}

.demo {
	font-size: 12px;
	color: grey;
}

#btn_new_ui{
    outline: none !important;
}

</style>
</head>
<body>
<?php include(COMPONENTS_PRIVATE_PATH."/shared/analytics.php"); ?>

<div class="container-fluid">
	<div class="content overflow-hidden">
		<div class="row">
			<div class="box">
				<div class="block block-themed animated fadeIn">
					<div class="block-header bg-primary-dark" style="border-radius:4px;">
						<h3 class="block-title">
						<center><?php getLogoLogin() ?> </center>
						</h3>
					</div>
					<div style="padding:10px;" class="block-content block-content-full block-content-narrow">

						<?php
                        if(isset($_REQUEST['newacct']))
						echo("<div style='border:1px silver solid;border-radius:5px;padding:10px;color:green;text-align:center;font-size:large;font-weight:bold'>Thanks for creating your free trial account. You can begin the login below<br>Your new SHOP ID is: ".$_REQUEST['shopid']."<br>Be sure to remember this number or write it down.<br>You will need it each time you login</div>");
					    elseif(isset($_REQUEST['activated']))
						echo("<div style='border:1px silver solid;border-radius:5px;padding:10px;color:green;text-align:center;font-size:large;font-weight:bold'>Account has been activated successfully. You can begin the login below</div>");

					    if(isset($_REQUEST['aid']) && $_REQUEST['aid']=='-1')
					    echo("<div style='border:1px silver solid;border-radius:5px;padding:10px;color:red;text-align:center;font-size:large;font-weight:bold'>You have been logged out of ShopBoss. Please log back in only during business hours.</div>");
						?>


						<form class="form-horizontal push-30-t push-50" action="base_pages_dashboard.php" name="theform" id="theform" method="post">

						<div class="form-group">
							<div class="col-xs-12">
								<div class="form-material form-material-primary floating">
									<h4 style="text-align:center" class="auto-style14">Enter Your Shop ID</h4>
									<input class="form-control" style="font-size:16pt;font-weight:bold;width:50%;margin:auto;text-align:center" type="text" id="shopid" name="shopid" value="">
								</div>
							</div>
						</div>
                            <br>
						<div class="auto-style3">
                            <div>
							<button type="button" onclick="checkForm()" class="btn btn-success text-nowrap mt-2" id="btn-continue"><i class="fas fa-sign-in-alt mr-2"></i> Continue</button>
                            </div>
                            <br>
                            <br>
                            <div>
                            <button type="button" onclick="window.location.href='<?= COMPONENTS_PUBLIC ?>/login/login-v2.php'" class="btn btn-light text-nowrap" style="" id="btn_new_ui">
                                <img src="https://shopboss.net/wp-content/uploads/2022/04/icon2-150x150.png" height="14"> Try the New UI</button>
                            </div>
			            <br><br>
							<!-- <div style="width:100%;text-align:center">
	                    		<strong><span class="style4">
								 Click the flashing icon to login</span></strong>
							</div><br>
							<p style="text-align:center">
								<img class="iconlog" onclick="checkForm('0')" id="img0" alt="" src="<?= IMAGE ?>/bluequestionmark.jpg">
								<img class="iconlog" onclick="checkForm('1')" id="img1" alt="" src="<?= IMAGE ?>/clock2.jpg" >
								<img class="iconlog" onclick="checkForm('2')" id="img2" alt="" src="<?= IMAGE ?>/find.jpg" >
								<img class="iconlog" onclick="checkForm('3')" id="img3" alt="" src="<?= IMAGE ?>/home.jpg">
								<img class="iconlog" onclick="checkForm('4')" id="img4" alt="" src="<?= IMAGE ?>/pencil.jpg" >
								<img class="iconlog" onclick="checkForm('5')" id="img5" alt="" src="<?= IMAGE ?>/redxcancel.jpg">
								<img class="iconlog" onclick="checkForm('6')" id="img6" alt="" src="<?= IMAGE ?>/greenrightarrow.jpg">
								<img class="iconlog" onclick="checkForm('7')" id="img7" alt="" src="<?= IMAGE ?>/padlockunlocked.jpg"><br>
									<span class="auto-style4">By logging in, you agree to our
								</span> -->
								<span class="auto-style4">By logging in, you agree to our </span>
								<span style="color:#336699;cursor:pointer" onclick="showTerms()" >
									<span class="auto-style4">Terms of Service</span></span>

							</p>

<!-- Memorial Day  Message -->
<!--
				<hr style="padding:1px;margin-top:5px;">

				<div class="row text-align:center">
					<br>
						<img class="img-responsive center-block sb-img"src="<?= IMAGE ?>/memorial.png">
						<p style="font-size:20px;">In observance of Memorial Day, Shop Boss's offices will be closed <b>Monday, May 30th, 2022.</b>
						<br>Customer support will be unavailable during this time. Our team will respond to all support requests when we return to the office on Tuesday, May 31st.</p>
				</div>
				<br><br>
				<hr style="padding:1px;margin-top:5px;">
 -->

<!-- Christmas Message -->

				<!-- <hr style="padding:1px;margin-top:5px;">

				<div class="row text-align:center">
					<br>
						<p style="font-size:20px;">In observation of Christmas, our office will be closed<br> <b>Thursday, December 24 and Friday, December 25</b><br><br> We wish you and your family a happy and safe holiday </p>
						<img class="img-responsive center-block img-turkey"src="newimages/christmas.png">
				</div>
				<br><br>
				<hr style="padding:1px;margin-top:5px;"> -->

<!-- New Years message -->

				<!-- <hr style="padding:1px;margin-top:5px;">

				<div class="row text-align:center">
					<br>
						<p style="font-size:20px;">In observation of New Year's Day, our office will be closed<br>  <b>Thursday, December 31 and Friday, January 1</b><br><br> We wish you and your family a happy and safe holiday </p>
						<img class="img-responsive center-block img-turkey"src="newimages/newyear.png">
						<br><br>
						<hr style="padding:1px;margin-top:5px;">
				</div> -->

<!-- Thanksgiving day message -->
				<!-- <hr style="padding:1px;margin-top:5px;">

				<div class="row text-align:center">
					<br>
						<p style="font-size:20px;">In observation of Thanksgiving, our office will be closed <b>Thursday November 26 and Friday November 27</b><br><br> We wish you and your family a happy and safe holiday </p>
						<img class="img-responsive center-block img-turkey"src="newimages/turkey.png">
						<br><br>
						<hr style="padding:1px;margin-top:5px;">
				</div> -->

<!-- Independence day message -->
				<!-- <hr style="padding:1px;margin-top:5px;">

				<div class="row text-align:center">
					<br>
						<p style="font-size:20px;">In observation of Independence Day, our office will be closed <b>Monday July 4th</b><br><br> We wish you and your family a happy and safe holiday </p>
						<img class="img-responsive center-block img-turkey"src="<?= IMAGE ?>/flag.jpg">
						<br><br>
						<hr style="padding:1px;margin-top:5px;">
				</div> -->

								<div class="text-center">
									<span id="browsersupport" style="color:red;font-weight:bold;font-size:medium;display:none">
									Your browser is not supported. You may
									continue however display and functions may
									be affected. We strongly recommend using
									Google Chrome.<br><br><a href="https://www.google.com/chrome/" target="_blank"><img alt="" style="max-width:40px;" src="newimages/Chrome-logo-2011-03-16.jpg"><br>Get Google Chrome</a> <br></span>
									<br>
							</div>
						</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
</div>

		<div id="empmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
			<div class="modal-dialog modal-md">
				<div class="modal-content">
					<div class="block block-themed block-transparent remove-margin-b">
						<div class="block-header bg-primary-dark">
							<ul class="block-options">
								<li>
									<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
								</li>
							</ul>
							<h3 id="modalh3" class="block-title">Click your name
							to login</h3>
						</div>

						<div class="block-content">
							<div class="row">
								<div id="loginparent" class="col-md-12">
								    <div id="loginresults">
								    </div>
							    </div>
							</div>
						</div>
					</div>
					<div style="margin-top:20px;" class="modal-footer">
					<button style="display:none" id="forgotbutton" class='btn btn-warning btn-sm pull-left' onclick="forgotFunc()" type='button'>
					Forgot Password</button>
					<button id="loginbutton" style="display:none" class='btn btn-primary' onclick='login()' type='button'>
					Login</button>
					<button class="btn btn-md btn-info" id="changeuserbutton" type="button" onclick="checkForm()">
					Change User</button>
					<button class="btn btn-md btn-default" type="button" onclick="location.reload()" data-dismiss="modal">
					Cancel</button>
					</div>
				</div>
			</div>
		</div>

		<div id="forgotmodal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
			<div class="modal-dialog modal-md">
				<div class="modal-content">
					<div class="block block-themed block-transparent remove-margin-b">
						<div class="block-header bg-primary-dark">
							<ul class="block-options">
								<li>
									<button data-dismiss="modal" type="button"><i class="si si-close"></i></button>
								</li>
							</ul>
							<h3 id="modalh3" class="block-title">Forgot Password</h3>
						</div>

						<div class="block-content">
							Please enter your registered <b>Email Address</b>. If you do not have an email in the system, contact your shop admin/owner to add one to your account settings.<br><br>
								<input class="form-control" tabindex="1" id="forgotemail" name="forgotemail" type="email" placeholder="Employee Email Address" required>
						</div>
					</div>
					<br>
					<div style="margin-top:20px;" class="modal-footer">
					<button type="button" class="btn btn-warning btn-md" data-dismiss="modal">Cancel</button>
	                <button type="submit" class="btn btn-primary btn-md" id="btn-forgot">Reset Password</button><br>
					</div>
					<div id="forgot-response" style="margin:0 10px;padding-bottom: 5px;"></div>
				</div>
			</div>
		</div>

	<form id="loginform" name="loginform" method="post" action="<?= COMPONENTS_PUBLIC ?>/login/loginsettings.php">
		<input type="hidden" name="loginshopid" id="loginshopid"><input type="hidden" id="loginempid" name="loginempid"><input type="hidden" id="loginpwd" name="loginpwd">
		<input type="hidden" name="referrer" value="<?= $ref??''?>">
		<input type="hidden" name="compid" value="<?= $compid ?>">
	</form>
		<img style="display:none;position:absolute;top:100px;left:48%;max-width:75px;z-index:9999" src="newimages/loaderbig.gif" id="spinner">
	<input type="hidden" id="empid">
	<input type="hidden" id="aid" value="">
	<input type="hidden" id="c" value="0">
<script>

function showTerms(){

	eModal.iframe({
		title:'Terms and Conditions',
		url: "<?= COMPONENTS_PUBLIC ?>/login/terms.php",
		size: eModal.size.xl,
		buttons: [
			{text: 'Close', style: 'warning', close:true}
    	]
	});


}

var is_chrome = navigator.userAgent.toLowerCase().indexOf('chrome') > -1;
if(is_chrome){
	//alert("The current release of Google Chrome (version 37) has an issue with our date selector for reports.  Please be aware of this until we find a workaround.")
}

function checkForm(){

	snum = document.theform.shopid.value
	if (snum == "xxxxx"){
		alert ("This account is frozen.  Please contact the owner of the account")
	}
	else if(snum.length > 0)
	{
    	$('#btn-continue').attr('disabled','disabled')
		// validate the shopid and get a list of users
		if (snum == "demo"){
			location.href='<?= COMPONENTS_PUBLIC ?>/login/loginsettings.php?loginshopid=demo&loginempid=*********&loginpwd=demo'
		}else{
			ip = "<?= $userip?>"
			$.ajax({
				data: "ip="+ip+"&t=shopid&shopid="+snum,
				url: "<?= COMPONENTS_PUBLIC ?>/login/loginaction.php",
				type: "post",
				success: function(r){
					if (r.indexOf('[{"login":') >= 0){
						newhtml = ""
						r = JSON.parse(r)
						$.each(r,function(k,v){
							if (v != null){
								newhtml += "<button onclick='promptPassword(\"" + v.id + "\")' style='min-width:250px;margin:2px;' class='btn btn-success btn-xs' id='btn"+v.id+"' type='button'>"+v.login.toUpperCase()+"</button><br class='bbrk' id='brk"+v.id+"'>"
							}
						})
						newhtml += '<br><br><div id="passwordparent" style="display:none"><h3>Password</h3><input type="password" id="passwd" class="sbp-form-control2">'
						newhtml += '</div>'
						$('#loginresults').html(newhtml)
						$('#modalh3').html("Click your name to login")
						$('#forgotbutton').hide()
						$('#loginbutton').hide()
						$('#changeuserbutton').hide()
						$('#empmodal').modal('show')

					}else if (r == "error"){
						swal({
							title: "Invalid Shop ID",
							text: "Your Shop ID number is incorrect.  Please try again",
							type: "warning",
							confirmButtonClass: "btn-danger",
							confirmButtonText: "Ok",
							closeOnConfirm: false
						},
						function(){
							location.reload()
						});

					}else if (r == "paidsuspended"){
						location.href='https://<?= $_SERVER['SERVER_NAME'] ?>/renewaccount.php?shopid='+$('#shopid').val()

					}else if (r == "trialsuspended"){
						location.href='https://<?= $_SERVER['SERVER_NAME'] ?>/activatetrialaccount.php?shopid='+$('#shopid').val()

					}else if (r == "unknown"){
						swal("Bad Shop Id. Please try again")
					}else if (r == "iprestriction"){
						swal("Your IP Address is restricted from logging in to Shop Boss")
					}

					$('#btn-continue').attr('disabled',false)
				},
				error: function (xhr, ajaxOptions, thrownError) {
					console.log(xhr.status);
					console.log(xhr.responseText);
					console.log(thrownError);
				}
			});
		}

	}else{
		swal({
			title: "Shop ID Required",
			text: "Please enter your shop ID number",
			type: "warning",
			confirmButtonClass: "btn-danger",
			confirmButtonText: "OK",
			closeOnConfirm: true
		},
		function(){
			$('#shopid').focus()
			$('#btn-continue').attr('disabled',false)
		});
	}
}

function promptPassword(empid){

	$('#empid').val(empid)

	$('.btn.btn-success.btn-xs').each(function(){
		btnid = 'btn'+empid
		if ($(this).attr("id") != btnid){
			$(this).remove()
		}else{
			$(this).removeClass("btn-xs").addClass("btn-lg")
		}
	})
	$('.bbrk').each(function(){
		brid = "brk"+empid
		if ($(this).attr("id") != brid){
			$(this).remove()
		}
	})
	setTimeout(function(){
		$('#modalh3').text("Enter Your Password")
		$('#forgotbutton').show()
		$('#passwordparent').fadeIn()
		$('#passwd').focus()
		$('#changeuserbutton').show()
		$('#loginbutton').show()
	},300);
	$('#loginbutton').show()

}

function forgotFunc()
{
	$('#empmodal').modal('hide')
	$('#forgotemail').focus()
	$('#forgotmodal').modal('show')
}

function login(){

	empid = $('#empid').val()
	$('#spinner').show()
	if ($('#passwd').val().length > 0 && empid.length > 0){
		pwd = encodeURIComponent($('#passwd').val())
		id = $('#empid').val()

		$.ajax({
			data: "t=user&id="+id+"&pwd="+pwd+"&shopid="+$('#shopid').val(),
			url: "<?= COMPONENTS_PUBLIC ?>/login/loginaction.php",
			type: "post",
			success: function(r){

				if (r == "success"){
					$('#loginshopid').val($('#shopid').val())
					$('#loginempid').val(id)
					$('#loginpwd').val(pwd)
					$('#loginform').submit()
				}else{
					$('#spinner').hide()
					swal("Invalid Password")
					$('#c').val(parseFloat($('#c').val())+1)
					if ($('#c').val() >= 3){
						location.reload()
					}
				}
			},
			error: function (xhr, ajaxOptions, thrownError) {
				console.log(xhr.status);
				console.log(xhr.responseText);
				console.log(thrownError);
			}

		})
	}else{
		swal("Please enter your password to log in")
	}


}

$(document).on('keypress', 'input', function(e) {

	if(e.keyCode == 13) {
		e.preventDefault();
		if ($('#passwd').val().length > 0){
			pwd = encodeURIComponent($('#passwd').val())
			id = $('#empid').val()

			$.ajax({
				data: "t=securecheck&id="+id+"&pwd="+pwd+"&shopid="+$('#shopid').val(),
				url: "<?= COMPONENTS_PUBLIC ?>/login/loginaction.php",
				type: "post",
				success: function(r){
					if (r == "success"){
						$('#loginshopid').val($('#shopid').val())
						$('#loginempid').val(id)
						$('#loginpwd').val(pwd)
						$('#loginform').submit()

					}
				},
				error: function (xhr, ajaxOptions, thrownError) {
					console.log(xhr.status);
					console.log(xhr.responseText);
					console.log(thrownError);
				}

			})
		}
	}

});


$(document).ready(function(){



	$('#shopid').focus()

	// highlight the active icon

	$('#webticker').webTicker({
		height: "45px",
		speed: 150
	});


	br = bowser.name
	br = br.toLowerCase()
	if (br != "chrome"){
		$('#browsersupport').show()
	}

	$('#btn-forgot').on('click', function (e)
	{
		e.preventDefault()

		const email = document.getElementById('forgotemail').value;

		$('#btn-forgot').attr('disabled','disabled')

		$.post("<?= COMPONENTS_PUBLIC ?>/login/loginaction.php", {
			t:'forgotpass',
			email: email,
		}, function(data) {

			if (data.status == 'success') {
				$('#forgot-response').html("<div class='alert alert-success'>A new password has been sent to your email address.</div>")
			}
			else {
				swal(data.msg);
			}

			$('#btn-forgot').attr('disabled',false)
		}, 'json');
	})

});
</script>
</body>
</html>
