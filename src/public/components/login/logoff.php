<?php
session_start();
require_once CONNWOSHOPID;

if ((strpos($_SERVER['SERVER_NAME'], 'localhost') !== false)) { //for local environments
    $servername = $_SERVER['SERVER_NAME'];
}elseif ((strpos($_SERVER['SERVER_NAME'], 'matcosms.com') !== false)) { //for Matco
    $servername = '.matcosms.com';
}elseif ((strpos($_SERVER['SERVER_NAME'], 'protractorgo.com') !== false)) { //for protractor
    $servername = '.protractorgo.com';
} else {
    $servername = '.shopbosspro.com'; //servername could be used here...
}

$_SESSION = [];
session_destroy();
setcookie(session_name(), '', time() - 3600, '/');

$aid = $_REQUEST['aid']??'0';


$username = (isset($_COOKIE["username"])?$_COOKIE["username"]:'');
$emp = (isset($_COOKIE["empid"])?$_COOKIE["empid"]:'');
$expiration = time() + (24 * 3600 * 30);

if(isset($_COOKIE["shopid"]) && isset($_REQUEST['u']) && $_REQUEST['u']=='y')
{
$stmt = "update company set cookieexpire = 'yes' where shopid = ?";
if ($query = $conn->prepare($stmt))
{
    $query->bind_param("s",$_COOKIE["shopid"]);
    $query->execute();
    $conn->commit();
    $query->close();
}
}
// unset cookies
if (isset($_SERVER['HTTP_COOKIE'])) {
    $cookies = explode(';', $_SERVER['HTTP_COOKIE']);
    foreach($cookies as $cookie) {
        $parts = explode('=', $cookie);
        $name = trim($parts[0]);
        if($name=='hidetrialalert' || $name=='minisidebar')continue;
        setcookie($name, '', time()-1000, '/',$servername);
    }
}

setcookie("username", $username, $expiration, "/",$servername);

if($emp=='Admin')
header("location:https://".$_SERVER["SERVER_NAME"]."/login_sb.php");
else
{
    if(isset($_SERVER['HTTP_REFERER']) && stripos($_SERVER['HTTP_REFERER'], '/v2/') !== false)
    header("location:https://".$_SERVER["SERVER_NAME"]."/login/login-v2.php?logoff=y&aid=".$aid);
    elseif(isset($_SERVER['HTTP_REFERER']) && stripos($_SERVER['HTTP_REFERER'], '/vinscanner/') !== false)
    header("location:https://".$_SERVER["SERVER_NAME"]."/login/login-v2.php?logoff=y&ref=".$_SERVER['HTTP_REFERER']."&aid=".$aid);
    else
    header("location:https://".$_SERVER["SERVER_NAME"]."/login.php?logoff=y&aid=".$aid);
}


?>