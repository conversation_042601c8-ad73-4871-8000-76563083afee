<?php
$compid = $_GET['companyID']??'';
if(isset($_GET['ref']))$ref = $_GET['ref'];
elseif(isset($_GET['redirect']))$ref = $_GET['redirect'];

if (isset($_COOKIE['mode']) && isset($_COOKIE['shopid']) && isset($_COOKIE['shopname']) && isset($_COOKIE['usr']) && $_COOKIE['interface']!='1')
{
	header("location:/v2/wip/wip.php");
	exit;
}

$component = getComponent();
include getHeadGlobal($component); 

if(stripos($_SERVER['SERVER_NAME'],'matcosms') !==false)
{
 $intro = "intro-matco.mp4";
 $tabletlogo = 'logo-tablet-matco.png';
 $logo ='logo2-matco.png';
}
elseif(stripos($_SERVER['SERVER_NAME'],'protractorgo') !==false)
{
 $intro = "intro-protractor.mp4";
 $tabletlogo = 'logo-tablet-protractor.png';
 $logo ='logo2-protractor.png';
}
else
{
 $intro = "intro.mp4";
 $tabletlogo = 'logo-tablet.png';
 $logo ='logo2.png';
}
?>
<body>
	<section class="vh-100 intro overflow-hidden">
    <div class="d-flex align-items-center justify-content-center">
        <div class="card">
            <div class="row">
                <div class="col-lg-6 col-md-0 bg-black p-0">
                    <img src="<?= IMAGE.'/'.$tabletlogo?>" class="img-fluid background-tablet" alt="shop boss"/> 
                </div>
                <div class="col-lg-6 col-md-12" style="z-index: 1000">
                    <div class="row d-flex justify-content-center align-items-center vh-100" data-mdb-toggle="animation" data-mdb-animation="fade-in" data-mdb-animation-start="onLoad" data-mdb-animation-delay="1000">
                        <div class="form-container">
                            <form id="login" class="centered-form">
                                <div class="row">
                                    <div class="mb-4 text-center">
                                        <img src="<?= IMAGE.'/'.$logo?>" class="logo-mobile img-fluid pb-4" alt="shop boss" />
                                        <h1>Welcome</h1>
                                        <hr />
                                    </div>

                                    <!-- Shop ID -->
                                    <div class="mb-4">
                                        <div class="form-outline">
                                            <input type="text" id="shopID" class="form-control"/>
                                            <label class="form-label" for="shopID">Shop ID</label>
                                        </div>
                                    </div>

                                    <!-- User -->
                                    <div class="mb-4">
                                        <select class="select form-control" id="user" data-mdb-filter="true">
                                            <option value="">Select</option>
                                        </select>
                                        <label class="form-label select-label text-secondary" for="user">User</label>
                                    </div>
                                </div>

                                <!-- Password Input -->
                                <div class="form-outline mb-3">
                                    <input type="password" id="password" class="form-control"/>
                                    <label class="form-label" for="password">Password</label>
                                </div>

                                <div class="d-flex justify-content-between mb-5 text-md">
                                    <a href="javascript:void(null)" data-mdb-toggle="modal" data-mdb-target="#forgotModal" class="text-secondary">Forgot Password?</a>
                                    <a href="javascript:void(null)" data-mdb-toggle="modal" data-mdb-target="#termsModal" class="text-secondary">Terms of Service</a>
                                </div>

                                <!-- Submit button -->
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary wave-effect" id="btn-submit">
                                        Log In
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
	
<!-- Background Video -->
	<div>
		 <video id="videoBG" autoplay muted playsinline>
			<source src="<?= IMAGE .'/loginvideos/'.$intro ?>" type="video/mp4" class="img-fluid">
		 </video>
	</div>

	<form id="securelogin" method="post" action="<?= COMPONENTS_PUBLIC ?>/login/loginsettings.php">
		<input type="hidden" name="loginshopid" id="loginshopid">
		<input type="hidden" id="loginempid" name="loginempid">
		<input type="hidden" id="loginpwd" name="loginpwd">
		<input type="hidden" name="referrer" value="<?= $ref??''?>">
		<input type="hidden" name="compid" value="<?= $compid ?>">
		<input type="hidden" name="newui" value="yes">
	</form>

<div id="termsModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
 <div class="modal-dialog modal-xl">
     <div class="modal-content">

         <div class="modal-header">
             <h4 class="modal-title">Terms and Conditions</h4>
             <button type="button" class="btn-close" data-mdb-dismiss="modal"></button>
         </div>

         <div class="modal-body">
             <iframe id="prero-iframe" class="embed-responsive-item" frameborder=0 src="<?= COMPONENTS_PUBLIC ?>/login/terms.php" style="width:100%;height:75vh;display:block;"></iframe>
         </div>

     </div>
 </div>
</div>

<div id="forgotModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content p-4">
            <div class="modal-header ps-1 pe-1">
                <h5 class="modal-title" id="forgotmodalLabel">Reset Password</h5>
                <button type="button" class="btn-close" data-mdb-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            	Please enter your registered <b>Email Address</b>. If you do not have an email in the system, contact your shop admin/owner to add one to your account settings.<br><br>
            	<div class="form-outline">
				 <input class="form-control" tabindex="1" id="forgotemail" name="forgotemail" type="text">
				 <label class="form-label" for="forgotemail">Employee Email Address</label>
				</div>
			</div>
			<div class="modal-footer d-flex justify-content-center">
                <button class="btn btn-primary btn-md" type="button" id="btn-forgot">Reset</button>
            </div>
        </div>
    </div>
</div>


</body>
 <?php include getScriptsGlobal($component);?>


  <script type="text/javascript">
  $(document).ready(function(){

 	 $('#shopID').focus()

 	 $('#shopID').on('blur',function()
 	 {
 	 	$('#response').html('&nbsp;')
 	 	ip = "<?= $_SERVER['REMOTE_ADDR']?>"
		newhtml = "<option value=''>Select</option>"
		$('#user').html(newhtml)
			$.ajax({
				data: "ip="+ip+"&t=shopid&newui=yes&shopid="+$('#shopID').val(),
				url: "<?= COMPONENTS_PUBLIC ?>/login/loginaction.php",
				type: "post",
				success: function(r){
					if (r.indexOf('[{"login":') >= 0){
						r = JSON.parse(r)
						$.each(r,function(k,v){
							if (v != null){
								newhtml += "<option value='"+v.id+"'>"+v.login.toUpperCase()+"</option>"
							}
						})
						$('#user').html(newhtml)

					}else if (r == "error"){
						sbalert("Invalid Shop ID");
					}else if (r == "paidsuspended"){
						location.href='https://<?= $_SERVER['SERVER_NAME'] ?>/renewaccount.php?shopid='+$('#shopid').val()
					}else if (r == "trialsuspended"){
						location.href='https://<?= $_SERVER['SERVER_NAME'] ?>/activatetrialaccount.php?shopid='+$('#shopid').val()

					}else if (r == "unknown"){
						sbalert("Bad Shop Id. Please try again")
					}else if (r == "iprestriction"){
						sbalert("Your IP Address is restricted from logging in to Shop Boss")
					}
				}
			});

 	 })

 	 $('#login').on('submit',function(e){
     
     e.preventDefault()

     $('#response').html('&nbsp;')

     pwd = encodeURIComponent($('#password').val())
	  id = $('#user').val()
	  shopid = $('#shopID').val()

	  if(id=='' || pwd=='' || shopid=='')
	  {
	  	sbalert("Please enter all the fields")
	  	return false
	  }

	  showLoader()

     $('#btn-submit').attr('disabled','disabled')

		$.ajax({
			data: "t=user&id="+id+"&pwd="+pwd+"&shopid="+shopid,
			url: "<?= COMPONENTS_PUBLIC ?>/login/loginaction.php",
			type: "post",
			success: function(r){
				if (r == "success"){
					$('#loginshopid').val(shopid)
					$('#loginempid').val(id)
					$('#loginpwd').val(pwd)
					$('#securelogin').submit()
				}
				else{
					sbalert("Invalid User / Password")
					$('#btn-submit').attr('disabled',false)
				}

				hideLoader()
			},
			error: function (xhr, ajaxOptions, thrownError) {
				console.log(xhr.status);
				console.log(xhr.responseText);
				console.log(thrownError);
			}

		})

 	 })

 	 $('#btn-forgot').on('click', function (e)
	{
		e.preventDefault()

		const email = document.getElementById('forgotemail').value;

		$('#btn-forgot').attr('disabled','disabled')

		showLoader()

		$.post("<?= COMPONENTS_PUBLIC ?>/login/loginaction.php", {
			t:'forgotpass',
			email: email,
		}, function(data) {

			if (data.status == 'success') {
				sbalert("A new password has been sent to your email address.")
				$('#forgotModal').modal('hide')
				document.getElementById('forgotemail').value = ''
			}
			else {
				sbalert(data.msg);
			}

			$('#btn-forgot').attr('disabled',false)
			hideLoader()
		}, 'json');
	})
  
  });

 
  </script>

</html>
