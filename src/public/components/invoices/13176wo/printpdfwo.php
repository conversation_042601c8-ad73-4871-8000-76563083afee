﻿<?php

ini_set("display_errors", "1");
error_reporting(E_ALL);

include INTEGRATIONS_PATH . "/TCPDF/tcpdf.php";

class MYPDF extends TCPDF
{
    public $footerHTML = "";

    public function Header()
    {
        $headerData = $this->getHeaderData();
//    $this->SetFont('helvetica', 'B', 10);
        $this->writeHTML($headerData['string']);
    }

// Page footer
    public function Footer()
    {
        $this->writeHTML($this->footerHTML);
        $this->SetFont('helvetica', 'B', 8);
        $this->SetXY($this->getPageWidth() - 90, $this->getPageHeight() - 25);
        $pageNumTBL = '<table style="width: 100pt"><tr><td>Page ' . $this->getAliasNumPage() . ' OF ' . $this->getAliasNbPages() . '</td></tr></table>';
        $this->writeHTML($pageNumTBL, true, false, true, false, 'R');
    }
}

require CONNWOSHOPID;

if (!function_exists("replace")) {
    function replace($subject, $search, $replace)
    {
        return str_ireplace($search, $replace, $subject);
    }
}

$fsize = "small";
$msize = "small";
$headerMargin = 135;

$shopid = isset($_REQUEST['shopid']) ? filter_var($_REQUEST['shopid'], FILTER_SANITIZE_STRING) : $_COOKIE['shopid'];
$roid = isset($_REQUEST['roid']) ? filter_var($_REQUEST['roid']) : "";

$curr_dir = dirname(__FILE__);
$custom_wo = $curr_dir . DS . $shopid . "wo" . DS;
if (file_exists($custom_wo)) {
    $php_file = $custom_wo . "printpdfwo.php";
    if (file_exists($php_file)) {
        $r = $shopid . "wo" . "/printpdfwo.php?shopid=" . $shopid . "&roid=" . $roid;
    } else {
        $r = $shopid . "wo" . "/printpdfwo.asp?shopid=" . $shopid . "&roid=" . $roid;
    }
    $tmpfolder = $custom_wo . DS . "temp";
    if (!file_exists($tmpfolder)) {
        @mkdir($tmpfolder);
    }
    redirect_to($r);
}

if (strpos($roid, ",") !== false) {
    $rar = explode(",", $roid);
    $roid = $rar[0];
}

$stmt = "select printvitotals, UCASE(CompanyName), companyfax, UCASE(companyaddress), UCASE(CompanyCity), UCASE(companystate), companyzip, CompanyPhone, UCASE(CompanyEMail), UCASE(CompanyURL), UCASE(printadvisorcomments), StorageFee, UCASE(customropage), UCASE(showcustphonewo), EPANo, BarNo, rowarrdisclosure, rodisclosure, UCASE(showpayments), UCASE(printtechstory), UCASE(showlaborhoursonro), UCASE(milesinlabel), UCASE(milesoutlabel), UCASE(replacerowithtag), logo, UCASE(invoicetitle), UCASE(estimatetitle) from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($printvitotals, $shopname, $shopfax, $shopaddress, $companycity, $companystate, $companyzip, $companyphone, $shopemail, $shopurl, $printadvisorcomments, $storagefee, $showtechhours, $showcustphonewo, $epano, $barno, $warrdisc, $sigdisc, $showpayments, $printtechstory, $showlaborhoursonro, $milesinlabel, $milesoutlabel, $replacerowithtag, $logo, $invoicetitle, $estimatetitle);
    $query->fetch();
    $query->close();
}

$shopcsz = $companycity . ", " . $companystate . ". " . $companyzip;
$shopphone = formatPhone($companyphone);
if (is_numeric(trim($storagefee))) {
    $storagefee = asDollars($storagefee);
}


$stmt = "select vehid, warrmos, warrmiles, UCASE(writer), UCASE(customer), UCASE(customeraddress), datein, timein, UCASE(customercity), UCASE(customerstate), UCASE(customerzip), customerphone, cellphone, CustomerWork, UCASE(VehInfo), UCASE(vehlicense), UCASE(vehstate), UCASE(VehEngine), UCASE(VehicleMiles), MilesOut, UCASE(vin), UCASE(fleetno), UCASE(customvehicle1label), UCASE(customvehicle1), UCASE(customvehicle2label), UCASE(customvehicle2), UCASE(customvehicle3label), UCASE(customvehicle3), UCASE(customvehicle4label), UCASE(customvehicle4), DiscountAmt, UCASE(status), tagnumber, datetimepromised, FinalDate from repairorders where shopid = ? and roid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $roid);
    $query->execute();
    $query->bind_result($vehid, $warrmos, $warrmiles, $writer, $cust, $addr, $invdate, $timein, $customercity, $customerstate, $customerzip, $customerphone, $cellphone, $customerwork, $vehinfo, $vehlicense, $vehstate, $vehengine, $milesin, $milesout, $vehvin, $fleetno, $cf1name, $cf1val, $cf2name, $cf2val, $cf3name, $cf3val, $cf4name, $cf4val, $discount, $rostatus, $tagnumber, $datetimepromised, $finaldate);
    $query->fetch();
    $query->close();
}

$warrdisc = replace($warrdisc, "[warrantymonths]", $warrmos);
$warrdisc = replace($warrdisc, "[warrantymiles]", $warrmiles);
$warrdisc = replace($warrdisc, "[storagefee]", $storagefee);

$shopbar = "Writer : " . $writer;

$invdate = date('m/d/Y', strtotime($invdate));

if ($shopid == "15594") {
    $invdate .= " " . date('h:i:s A', strtotime($timein));
}

$csz = $customercity . ", " . $customerstate . ". " . $customerzip;

$cphone = $cell = $wphone = "";

$phones = array();

if ( $showcustphonewo == "YES" ){
    if (!empty($customerphone)) {
        $phones[] = "HOME: " . formatPhone($customerphone);
    }
    if (!empty($cellphone)) {
        $phones[] = "CELL: " . formatPhone($cellphone);
    }
    if (!empty($customerwork)) {
        $phones[] = "WORK: " . formatPhone($customerwork);
    }

    $phonelist = implode('<br>', $phones);
}


$veh = left($vehinfo, 50);
$lic = "LICENSE: " . $vehlicense . " - " . $vehstate . "   Engine:" . $vehengine;
$miles = $milesinlabel . ": " . $milesin . "   " . $milesoutlabel . ": " . $milesout;
$vin = "VIN: " . $vehvin;
$fleet = "";
if (!empty($fleetno)) {
    $fleet = "Fleet#: " . $fleetno;
}

$printtype = "Work Order #";

$shopname = html_entity_decode($shopname);

$shopepa = '' ;
if (!empty($epano)) {
    $shopepa = "<br>EPA# " . $epano;
}

/*if (!empty($barno)) {
    $shopbar = "<br>BAR# " . $barno;
    $headerMargin += 5;
}*/

$headerRight = '<br><br><span style="font-size: x-large;">' . $printtype . $roid . '</span><br>';
$headerRight .= '<span style="font-size: ' . $fsize . '"><br> ' . $shopemail . '<br>' . $shopurl .'<br>'. $shopbar . $shopepa . '</span>';

if (!empty($tagnumber)) {
    $tagnumber = "TAG/HAT #" . $tagnumber;
    $headerRight .= '<br><span style="font-size: medium">' . $tagnumber . '</span>';
    $headerMargin += 10;
} else {
    $tagnumber = "";
}

if (strlen($shopfax) > 3) {
    $shopfax .= " - Fax";
} else {
    $shopfax = "";
}

$headerTable = <<<EOD
<style>
table tr td {
    line-height: 12em;
}
</style>
<table>
<tr>
    <td style="">
    <br><br>
<span style="font-size: medium; font-weight: 700">$shopname</span>
    <br><br style="line-height: 6pt">
<span style="font-size: $fsize;">$shopaddress
    <br>
$shopcsz
<br>
$shopphone
    <br>
$shopfax
    </span>
    </td>
    
    <td style="text-align: right;">
        $headerRight
    </td>
</tr>
</table>
EOD;
if(!empty($datetimepromised) && $datetimepromised != "0000-00-00"){
    $datetimepromised = date("m/d/Y h:i A", strtotime($datetimepromised));
} else {
    $datetimepromised = "";
}

if (!empty($finaldate) && $finaldate != "0000-00-00") {
    $finaldate = date('m/d/Y', strtotime($finaldate));
} else {
    $finaldate = "";
}

$lineval = <<<LINEVAL
     <table style="font-size: $fsize;">
        <tr>
            <td>Date In : $invdate  Promised : $datetimepromised</td>
            <td>Date Out : $finaldate;</td>
        </tr>
        </table>
     LINEVAL;



if ( !empty($cf1name) || !empty($cf1val) || !empty($cf2name) || !empty($cf2val) ){
    $cfvalues = '<br>' . $cf1name . ' ' . $cf1val . ' ' .  $cf2name . ' ' . $cf2val;
    $headerMargin += 10;
}

if ( !empty($cf3name) || !empty($cf3val) || !empty($cf4name) || !empty($cf4val) ){
    $cfvalues .= '<br>' . $cf3name . ' ' . $cf3val . ' ' .  $cf4name . ' ' . $cf4val;
    $headerMargin += 10;
}

$custnVehTable = <<<EOD
<br>
<table style="font-size: $fsize; border-top: 1pt solid black; border-bottom: 1pt solid black;">
    <tr>
        <td>$cust<br>$phonelist </td>
        <td>$veh<br>$lic<br>$miles<br>$vin $fleet $cfvalues</td>
    </tr>
</table>
EOD;

$headerTable .= $custnVehTable;

$stmt = "select complaint, advisorcomments, complaintid, acceptdecline,techreport from complaints where cstatus = 'no' and acceptdecline != 'Declined' and shopid = ? and roid = ? group by complaintid  order by displayorder";


if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $roid);
    $query->execute();
    $results = $query->get_result();
    $query->close();
}

$complaintsHTML = '<tr style="line-height: 1pt"><td style="width: 20%"></td><td style="width: 40%;"></td><td style="width: 10%;"></td><td style="width: 10%"></td><td style="width: 20%"></td></tr>';

if ($results->num_rows > 0) {
    $c = 1;
    while ($crs = $results->fetch_assoc()) {
        $viptotal = 0;
        $viltotal = 0;
        $vistotal = 0;
        $comlabel = $crs['complaint'];
        $advcom = strtoupper($crs['advisorcomments']);
        $complaintid = $crs["complaintid"];

        $complaint = trim("VEHICLE ISSUE #" . $c . ": " . replace(replace(strtoupper($crs["complaint"]), "&#34;", "'"), "&#39;", "'"));
        $itechstory = $crs["techreport"];
        if (strtolower($printtechstory) == "yes" && !empty($itechstory)) {
            $complaint .= " - TECH REPORT: " . nl2br(strtoupper(replace(replace($itechstory, "&#34;", "''"), "&#39;", "'")));
        }

        if (strtolower($printadvisorcomments) == "yes" && !empty($advcom)) {
            $complaint .= " *** ADVISOR NOTES:" . nl2br(strtoupper(replace(replace($advcom, "&#34;", "''"), "&#39;", "'")));
        }
        $complaintsHTML .= '<tr><td colspan="5" style="background-color: silver; font-size: ' . $fsize . '; font-weight: 100; border: 1px solid black;"> ' . $complaint . '</td></tr>';


        $stmt = "select partnumber, partcode, partprice, linettlprice, partdesc, quantity, discount, bin from parts where roid = ? and shopid = ? and complaintid = ? and deleted != 'yes' order by displayorder, partid";

        if ($pquery = $conn->prepare($stmt)) {
            $pquery->bind_param("isi", $roid, $shopid, $complaintid);
            $pquery->execute();
            $pres = $pquery->get_result();
            $pquery->close();
        }

        if ($pres->num_rows > 0) {
            while ($lrs = $pres->fetch_assoc()) {
                $viptotal += $lrs['linettlprice'];
                $cell1 = $cell2 = $cell3 = $cell4 = $cell5 = $cell6 = "";
                $cell1 = "PART: " . strtoupper($lrs["partnumber"]);
                $cell2 = replace(replace(strtoupper($lrs["partdesc"]), "&#34;", "''"), "&#39;", "'");
                $cell3 = $lrs['quantity'];
                $cell5 = strtoupper($lrs['partcode']);
                $cell6 = strtoupper($lrs['bin']);

                $complaintsHTML .= <<<PARTROWS
                    <tr style="font-size: $msize">
                        <td class="border-left" style="">$cell1</td>
                        <td style="">$cell2</td>
                        <td style="text-align: right">$cell3</td>
                        <td>$cell4</td>
                        <td class="border-right">
                            <table>
                                <tr>
                                    <td style="width: 30%">$cell5</td>
                                    <td style="text-align: right; width: 70%">$cell6</td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                PARTROWS;
            } //PARTS LOOP
        } //PARTS ROW CONDITION


        $stmt = "select tech, linetotal, laborhours, hourlyrate, discount, labor from labor where roid = ? and shopid = ? and complaintid = ?";

        if ($lquery = $conn->prepare($stmt)) {
            $lquery->bind_param("isi", $roid, $shopid, $complaintid);
            $lquery->execute();
            $lres = $lquery->get_result();
            $lquery->close();
        }

        if ($lres->num_rows > 0) {
            while ($lrs = $lres->fetch_assoc()) {
                $cell1 = $cell2 = $cell3 = $cell4 = $cell5 = "";
                $viltotal += $lrs['linetotal'];
                if ($showtechhours == "YES") {
                    $laborhours = $lrs['laborhours'] . " hrs";
                } else {
                    $laborhours = "";
                }
                $cell1 = "LABOR: " . left($lrs["tech"], 8);
                $cell2 = strtoupper(replace($lrs["labor"], "&#39;", "'"));
                $cell3 = $laborhours;
                $cell4 = "";
                $cell5 = "LABOR";


                $complaintsHTML .= <<<LABORROWS
                    <tr style="font-size: $msize">
                        <td class="border-left" style="">$cell1</td>
                        <td style="">$cell2</td>
                        <td style="text-align: right;">$cell3</td>
                        <td>$cell4</td>
                        <td style="text-align: right;" class="border-right">$cell5</td>
                    </tr>
                LABORROWS;
            } //LABOR LOOP
        }//LABOR ROW CONDITION

        $stmt = "select subletdesc, subletprice from sublet where roid = ? and shopid = ? and complaintid = ?";

        if ($squery = $conn->prepare($stmt)) {
            $squery->bind_param("isi", $roid, $shopid, $complaintid);
            $squery->execute();
            $sres = $squery->get_result();
            $squery->close();
        }

        if ($sres->num_rows > 0) {
            while ($lrs = $sres->fetch_assoc()) {
                $cell1 = $cell2 = $cell3 = $cell4 = $cell5 = "";
                $vistotal += $lrs['subletprice'];
                $subletprice = asDollars($lrs['subletprice']);

                $cell1 = "SUBLET";
                $cell2 = strtoupper(replace($lrs["subletdesc"], "&#39;", "'"));
                $cell4 = $subletprice;
                $cell5 = "SUBLET";


                $complaintsHTML .= <<<SUBLETROWS
                    <tr style="font-size: $msize">
                        <td class="border-left" style="">$cell1</td>
                        <td style="">$cell2</td>
                        <td style="text-align: right;">$cell3</td>
                        <td>$cell4</td>
                        <td style="text-align: right;" class="border-right">$cell5</td>
                    </tr>
                SUBLETROWS;
            }//SUBLET LOOP
        }//SUBLET ROW CONDITION

        /*
        $vitotal = 0;
        $vitotal = $viptotal + $viltotal + $vistotal;

        if (is_numeric($vitotal)) {
            $vitotal = asDollars($vitotal);
        }

        if (strtolower($printvitotals) == "yes") {
            if ($itemizedItemsOnConcerns) {
                $complaintsHTML .= '<tr>
                    <td style="text-align: right;" class="border-top border-bottom border-left"></td>
                    <td style="text-align: right;" class="border-top border-bottom">Parts: ' . $viptotal . '</td>
                    <td style="text-align: right; border: 1px solid black">Labor: ' . $viltotal . '</td>
                    <td style="text-align: right; border: 1px solid black">Sublet: ' . $vistotal . '</td>
                    <td style="text-align: right; border: 1px solid black"><b>Total: ' . $vitotal . '</b></td>
                    </tr>';
            } else {
                $complaintsHTML .= '<tr><td colspan="5" style="text-align: right; border: 1px solid black">Total for "' . replace(replace(strtoupper($crs['complaint']), "&#34;", "''"), "&#39;", "'") . '": ' . $vitotal . '</td></tr>';
            }
        }
        */
        $c++;

    } //COMPLAINT LOOP
} //COMPLAINT ROW CONDITION

if (!empty($cf1val)) {
    $complaintsHTML .= <<<CFROW
                    <tr>
                        <td class="border-left border-right" colspan="5" style="">$cf1name : $cf1val</td>
                    </tr>
                CFROW;
}

if (!empty($cf2val)) {
    $complaintsHTML .= <<<CFROW
                    <tr>
                        <td class="border-left border-right" colspan="5" style="">$cf2name : $cf2val</td>
                    </tr>
                CFROW;
}

if (!empty($cf3val)) {
    $complaintsHTML .= <<<CFROW
                    <tr>
                        <td class="border-left border-right" colspan="5" style="">$cf3name : $cf3val</td>
                    </tr>
                CFROW;
}

if (!empty($cf4val)) {
    $complaintsHTML .= <<<CFROW
                    <tr>
                        <td class="border-left border-right" colspan="5" style="">$cf4name : $cf4val</td>
                    </tr>
                CFROW;
}

$stmt = "select r.id,comid,r.shopid,r.roid,`desc`,totalrec,originalroid,originalcomplaintid from recommend r left join repairorders ro on r.shopid = ro.shopid and r.roid = ro.roid where r.shopid = ? and ro.vehid = ? order by roid desc";
if ($rquery = $conn->prepare($stmt)) {
    $rquery->bind_param("si", $shopid, $vehid);
    $rquery->execute();
    $results = $rquery->get_result();
    $rquery->close();
}

if ($results && $results->num_rows > 0) {
    $complaintsHTML .= <<<RRROW
                    <tr>
                        <td class="border-left border-right" colspan="5" style=""> ********* PREVIOUS RECOMMENDED REPAIRS ********** </td>
                    </tr>
                RRROW;
    while ($rrrs = $results->fetch_assoc()) {
        $rrdesc = strtoupper(replace($rrrs['desc'], "CUSTOMER DECLINED:", ""));
        $complaintsHTML .= <<<RRROW
                    <tr>
                        <td class="border-left border-right" colspan="5" style="">$rrdesc</td>
                    </tr>
                RRROW;
    }
}


$lineItemsTable = <<<EOD
<style>
   .border-bottom{
        border-bottom: 1px solid black;
   }
   .border-top{
        border-top: 1px solid black;
   }
   .border-left {
        border-left: 1px solid black;
   }
   .border-right{
        border-right: 1px solid black;
   }
   .border{
        border:1px solid black;
   }
</style>
$lineval
<table id="lineitems" style="font-size: $msize; font-weight: lighter; border-bottom: 1px solid black; width: 100%" cellpadding="3">
$complaintsHTML
</table>
EOD;

$pdf = new MYPDF(PDF_PAGE_ORIENTATION, 'pt', array(612, 780), true, 'UTF-8', false);

$pdf->setHeaderData('', 0, '', $headerTable, array(0, 0, 0), array(0, 0, 0));

$pdf->setPrintHeader(true);
$pdf->setPrintFooter(false);
//$pdf->SetFont('arial', 'B', 20);
$pdf->setCellPaddings(0, 0, 0, 0);
$pdf->SetMargins(30, $headerMargin, 30);

$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
// add a page
$pdf->AddPage();

$pdf->writeHTML(utf8_encode($lineItemsTable), true, false, true, false, '');

//$pdf->Write(0, 'Example of independent Multicell() columns', '', 0, 'L', true, 0, false, false, 0);
$newfilename = "TPWO_" . $shopid . "_" . $roid . "_" . date('Y') . "_" . date('n') . "_" . date('j') . "_" . date('H') . "_" . date('i') . "_" . date('s') . "_" . time() . ".pdf";

ob_end_clean();
$pdf->Output(dirname(__FILE__) . DS . 'temp' . DS . $newfilename, 'F');


// echo "/temp/" . $newfilename;

echo "/" . $shopid . "wo/temp/" . $newfilename;
?>