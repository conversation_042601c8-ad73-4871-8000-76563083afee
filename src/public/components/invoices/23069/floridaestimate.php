﻿<?php
ob_start();
ini_set("display_errors", "1");
error_reporting(E_ALL);

include INTEGRATIONS_PATH . "/TCPDF/tcpdf.php";

require CONNWOSHOPID;
$shopid = isset($_REQUEST['shopid']) ? filter_var($_REQUEST['shopid'], FILTER_SANITIZE_STRING) : $_COOKIE['shopid'];
$roid = isset($_REQUEST['roid']) ? filter_var($_REQUEST['roid'], FILTER_SANITIZE_STRING) : "";
?>
    <style type="text/css">
        body {
            font-family: Arial;
            margin: 0px;
            padding: 0px;
        }

        .style1 {
            text-align: right;
            font-size: large;
        }

        .style2 {
            text-align: center;
            font-size: small;
        }

        .style3 {
            font-size: x-small;
        }

        .style5 {
            font-size: medium;
        }

        .style7 {
            border: 1pt solid #000000;
        }

        .style8 {
            text-align: center;
            border: 1pt solid #000000;
        }

        .style10 {
            text-align: center;
            border: 1pt solid #000000;
        }

        .style12 {
            font-size: small;
            line-height: 9pt;
        }

        .style14 {
            text-align: center;
            border: 1pt solid #000000;
        }

        .auto-style1 {
            text-align: right;
        }

        .auto-style2 {
            font-family: Arial, Helvetica, sans-serif;
        }

        .auto-style5 {
            font-family: Arial, Helvetica, sans-serif;
            font-size: small;
        }

        .auto-style6 {
            font-size: medium;
        }

        .auto-style7 {
            font-size: xx-small;
        }
    </style>
    <table style="width: 650px;" cellpadding="0" cellspacing="0">
        <tr>
            <?php
            $stmt = "select hourlyrate, defaultwarrmos, defaultwarrmiles, storagefee, UCASE(companyname), UCASE(companyaddress), UCASE(companycity), UCASE(companystate), companyzip, companyphone, BarNo, rodisclosure from company where shopid = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("s", $shopid);
                $query->execute();
                $query->bind_result($hr, $warrmos, $warrmiles, $storagefee, $companyname, $companyaddress, $companycity, $companystate, $companyzip, $companyphone, $barno, $rodisc);
                $query->fetch();
                $query->close();
            }
            ?>
            <td style="border-bottom:thin black solid; width: 65%;" class="style5" valign="top">
                <span style="font-weight:bold" class="style5"><?= $companyname ?></span>
                <br class="style5"/>
                <span class="style5">
		<?= $companyaddress ?></span><br class="style5"/>
                <span class="style5">
		<?= $companycity . ", " . $companystate . ". " . $companyzip ?>
		</span><br class="style5"/>
                <span class="style5">
		<?= formatPhone($companyphone) ?>
		</span><br/><br/>
            </td>
            <?php
            $stmt = "select totalro, origro, TotalLbr, datepromised, userfee1, userfee2, userfee3, totallbr, totalprts, totalsublet, salestax, subtotal, UCASE(customer), customerphone, customerwork, UCASE(CustomerAddress), UCASE(customercsz), cellphone, UCASE(vehinfo), vehiclemiles, UCASE(vehlicnum), vin, tagnumber, datein, timein, datetimepromised, rodisc from repairorders where shopid = ? and roid = ?";
            if ($query = $conn->prepare($stmt)) {
                $query->bind_param("si", $shopid, $roid);
                $query->execute();
                $query->bind_result($totalro, $origro, $tlabor, $datepromised, $userfee1, $userfee2, $userfee3, $totallbr, $totalprts, $totalsublet, $salestax, $subtotal, $customer, $customerphone, $customerwork, $customeraddress, $customercsz, $cellphone, $vehinfo, $vehiclemiles, $vehlicnum, $vin, $tagnumber, $datein, $timein, $datetimepromised, $rodiscfromro);
                $query->fetch();
                $query->close();
            } else {
                echo "PF 115" . $conn->error;
            }

            $tro = doubleval($totalro);
            $oest = doubleval($origro) + $tro;
            if (is_numeric($oest)) {
                $oest = asDollars($oest);
            } else {
                $oest = asDollars(0);
            }

            if (strtotime($datetimepromised)) {
                $datetimepromised = date('m/d/Y h:i:s A', strtotime($datetimepromised));
            } else {
                $datetimepromised = "";
            }

            if (is_numeric($userfee1)) {
                $shopsuppliesdisplay = asDollars($userfee1);
                $shopsupplies = $userfee1;
            } else {
                $shopsuppliesdisplay = "$0.00";
                $shopsupplies = 0;
            }
            if (is_numeric($userfee2)) {
                $otherfee2display = asDollars($userfee2);
                $otherfee2 = $userfee2;
            } else {
                $otherfee2display = "$0.00";
                $otherfee2 = 0;
            }
            if (is_numeric($userfee3)) {
                $otherfee3 = $userfee3;
                $otherfee3display = asDollars($userfee3);
            } else {
                $otherfee3 = 0;
                $otherfee3display = "$0.00";
            }

            ?>
            <?php
            $tlabor = $totallbr;
            $tparts = $totalprts;
            $tsublet = $totalsublet;
            $tlabordisplay = asDollars($tlabor);
            $tpartsdisplay = asDollars($tparts);
            $tsubletdisplay = asDollars($tsublet);
            $salestax = asDollars($salestax);
            $totalro = asDollars($totalro);
            $totalfees = $shopsupplies + $otherfee2 + $otherfee3;
            $subtotal = asDollars($subtotal + $totalfees);
            ?>
            <td style="border-bottom:thin black solid; width: 35%;" class="style1" valign="top"><strong>
                    ESTIMATE #&nbsp;<?= $roid ?><br/>
                    FL MV#: <span class="style5"> <?= $barno ?>
		
		</span>
                </strong></td>
        </tr>
    </table>

    <table style="width: 650px; border-bottom:2pt black solid">
        <tr>
            <td style="width: 50%;" valign="top"><b><span
                            class="style12">CUSTOMER:</span></b>
                <br class="style12"/>
                <span class="style12"><?= $customer ?></span>
                <br class="style12"/>
                <span class="style12"><?= $customeraddress ?></span>
                <br class="style12"/>
                <span class="style12"><?= $customercsz ?></span>
                <br class="" style="line-height: 11pt"/>
                <span class="style12"><?php
                    if (!empty($customerphone)) {
                        echo formatPhone($customerphone);
                    } else if (!empty($customerwork)) {
                        echo formatPhone($customerwork);
                    } else if (!empty($cellphone)) {
                        echo formatPhone($cellphone);
                    }
                    ?>
		        </span>
            </td>
            <td style="width: 50%;" valign="top"><b><span class="style12">VEHICLE:</span></b>
                <br class="style12"/>
                <span class="style12"><?= $vehinfo ?></span>
                <br class="style12"/>
                <span class="style12">Mileage: <?= $vehiclemiles ?></span>
                <br class="style12"/>
                <span class="style12">License: <?= $vehlicnum . " VIN: " . $vin ?></span>
                <br class="" style="line-height: 11pt"/>
                <span class="style12">Tag # <?= $tagnumber ?></span>
            </td>
        </tr>
    </table>

    <table cellpadding="0" cellspacing="0" style="width: 650px">
        <tr>
            <td style="width: 50%; border-bottom:thin black solid;">
                <strong>
                    <span class="style12">Date In:</span>
                </strong>
                <span class="style12">
                    <?= date('m/d/Y', strtotime($datein)) . " " . date('h:i:s A', strtotime($timein)) ?>
		        </span>
            </td>
            <td style="width: 50%; border-bottom:thin black solid">
                <strong>
                    <span class="style12">Date Promised:</span>
                </strong>
                <span class="style12">
                    <?= $datetimepromised ?>
		        </span>
            </td>
        </tr>
    </table>
    <p style="padding-left:220px;font-weight:bold;width:650px; text-align: center">******* Vehicle Issues *******</p>
    <table cellpadding="2" cellspacing="1" style="width: 650px">
        <?php
        $stmt = "select complaintid, UCASE(complaint) from complaints where cstatus = 'no' and roid = ? and shopid = ?";
        if ($cquery = $conn->prepare($stmt)) {
            $cquery->bind_param("is", $roid, $shopid);
            $cquery->execute();
            $cquery->bind_result($complaintid, $complaint);
            while ($cquery->fetch()) {
                ?>
                <tr>
                    <td style="padding:3px;background-color:#e0e0e0;font-weight:bold;font-size:x-small"><?= $complaint ?></td>
                </tr>
                <?php
            }
        }
        ?>
    </table>

    <br/>
    <br/>

    <?php 
        if (strlen($rodiscfromro) > 10) {
            $roDisclousure = substr($rodiscfromro, 0, 850);
        } else {
            $roDisclousure = substr($rodisc, 0, 850);
        }
        
        $roDisclousure = nl2br($roDisclousure) . "<br><br>X______________________________________________________________                      Date " . date('n/j/Y');
    ?>

    <table style="width: 650px; border: 1pt solid black" cellpadding="0" cellspacing="0">
        <tr>
            <td class="style10">
                <table style="width:100%;" cellpadding="2" cellspacing="0">
                    <tr>
                        <td style="width: 25%" class="">
                            <strong>
                                <span class="style12">Save Old Parts:</span>
                            </strong>
                            <br class=""/>
                            <span class="style12">Yes or No&nbsp; (Core may apply)</span>
                        </td>
                        <td style="width: 25%" class="style10">
                            <strong>
                                <span class="style12">Intended Payment Method:</span>
                            </strong>
                            <br class=""/>
                            <span class="style12">Cash&nbsp; Check&nbsp; Visa&nbsp; MC&nbsp; Amex&nbsp; Discover Other</span>
                        </td>
                        <td style="width: 25%" class="style10">
                            <strong>
                                <span class="style12">Other Authorized Person:</span>
                                <br class=""/>
                            </strong>
                            <br class="style12"/>
                        </td>
                        <td style="width: 25%;text-align:center;font-weight:bold" class="style10">Other Authorized
                            Person Phone #
                            <br class="style12"/>
                            <br class="style12"/>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td class="style10">
                <table cellpadding="2" cellspacing="0" style="width: 100%">
                    <tr>
                        <td class="" style="width: 22%">Labor Charges Based on Hourly
                            Rate:<br/><?= asDollars($tlabor) ?></td>
                        <td class="style10" style="width:20%">Estimate / Diagnostic Fee:<br/>
                            <?= $hr ?> Per hour
                        </td>
                        <td class="style10" style="width:15%">Estimate Amount:<br/><?= $oest ?></td>
                        <td class="style10" style="width:17%"><?= $warrmos ?> Months/<br/>
                            <?= $warrmiles ?> Miles<br/>
                            Warranty
                        </td>
                        <td class="style10" style="width:26%">Storage Fee Per
                            Day: <span class="auto-style7">(begins 72 hours after notification of completed work)</span>
                            <br/><?= asDollars($storagefee) ?>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <br/>
    <br/>
    <table style="width: 650px">
        <tr>
            <td style="width: 310px; border:2pt black solid;padding-right:10px;" valign="top">
                <span class="auto-style5"><strong>PLEASE READ
                    CAREFULLY, CHECK ONE OF THE STATEMENTS
                    BELOW, AND SIGN:</strong><br/>
                I UNDERSTAND THAT, UNDER STATE LAW, I AM ENTITLED TO A WRITTEN
                ESTIMATE IF MY FINAL BILL WILL EXCEED $150.00.<br/>
                _____I REQUEST A WRITTEN ESTIMATE.<br/>
                _____I DO NOT REQUEST A WRITTEN ESTIMATE AS LONG AS THE REPAIR
                COSTS DO NOT EXCEED $_______.<br/>
                THE SHOP MAY NOT EXCEED THIS AMOUNT WITHOUT MY WRITTEN OR ORAL
                APPROVAL.<br/>
                _____I DO NOT REQUEST A WRITTEN ESTIMATE.<br/>
                <br/><br>
                </span>
                <table style="width: 100%" cellpadding="2" cellspacing="0">
                    <tr>
                        <td style="border-bottom:2px black solid">
                            SIGNED
                        </td>
                        <td style="padding-right:80px;border-bottom:2pt black solid" class="auto-style1">
                            DATE
                        </td>
                    </tr>
                </table>
                <br>
            </td>
            <td style="width: 340px;"><strong>NOTE: PARTS REPLACED UNDER WARRANTY OR MECHNICAL CONTRACT SUBJECT TO TERMS
                    OF MFG AND/OR
                    ADMINISTRATOR.</strong>
                <span class="auto-style2">
                    <span class="auto-style5">I understand that all parts and accessories sold or used are subject to the Federal Magnusson-Moss act and the consumer merchandise purchased is under LIMITED WARRANTY by the manufacturer and the written terms and conditions thereof are available for my inspection.
                    </span>
                </span>
                <span class="auto-style2">
		        <span class="auto-style5">
                    **This charge represents costs and profits to the motor vehicle repair facility for miscellaneous shop supplies or waste disposal. ***
                </span>
                </span>
                <span class="auto-style7">F.S. 403.718 mandates a $1.00 fee for each new tire sold in the State of Florida.&nbsp; ***F.S. 403.7185 mandates a $1.50 fee for each new or remanufactured battery sold in the State of Florida</span>
            </td>
        </tr>
        <tr><br><span class="auto-style7"><?= $roDisclousure; ?></span></tr>
    </table>
<?php
$content = ob_get_contents();

$pdf = new TCPDF(PDF_PAGE_ORIENTATION, 'pt', array(612, 780), true, 'UTF-8', false);

$pdf->setPrintHeader(false);
$pdf->setPrintFooter(false);
//$pdf->SetFont('arial', 'B', 20);
$pdf->SetMargins(30, 30, 30);

$pdf->SetAutoPageBreak(true, 10);

$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

//$pdf->setFont('dejavusans');
// add a page
$pdf->AddPage();

$pdf->writeHTML($content, true, false, true, false, '');

//$pdf->Write(0, 'Example of independent Multicell() columns', '', 0, 'L', true, 0, false, false, 0);
$newfilename = "TP_FLEST_" .time()."_". $shopid . "_" . $roid . "_.pdf";

ob_end_clean();

$pdf->Output(dirname(__FILE__) . DS . 'temp' . DS . $newfilename, 'F');

echo "/" . $shopid . "/temp/" . $newfilename;