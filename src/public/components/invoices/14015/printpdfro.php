<?php
ini_set("display_errors", "1");
error_reporting(E_ALL);


include INTEGRATIONS_PATH . "/TCPDF/tcpdf.php";

class MYPDF extends TCPDF
{
    public $footerHTML = "";

    public function Header()
    {
        $headerData = $this->getHeaderData();
        //    $this->SetFont('helvetica', 'B', 10);
        $this->writeHTML($headerData['string']);
    }

    // Page footer
    public function Footer()
    {
        $this->writeHTML($this->footerHTML);
        $this->SetFont('helvetica', 'B', 8);
        $this->SetXY($this->getPageWidth() - 90, $this->getPageHeight() - 25);
        $pageNumTBL = '<table style="width: 100pt"><tr><td>Page '.$this->getAliasNumPage() . ' OF ' . $this->getAliasNbPages().'</td></tr></table>';
        $this->writeHTML($pageNumTBL, true, false, true, false, 'R');
    }
}

$shopid = isset($_REQUEST['shopid']) ? filter_var($_REQUEST['shopid'], FILTER_SANITIZE_STRING) : $_COOKIE['shopid'];
$roid = isset($_REQUEST['roid']) ? filter_var($_REQUEST['roid'], FILTER_SANITIZE_STRING) : "";
$remoteauth = isset($_REQUEST['remoteauth']) ? filter_var($_REQUEST['remoteauth'], FILTER_SANITIZE_STRING) : "";


/*
 * OPTIONS
 */
$showOrigionalEstimate = false;
$showSeparateCanadianTaxes = false;
$showTotalTax = false;
$total_bg = "background-color: white";
$showPartsOnInvoice = true;
$showVehicleColor = false;
$itemizedItemsOnConcerns = false;
/*
 * PAGE OPTIONS
 */
$footer_margin = 235;


if ($shopid == "4631") {
    $showOrigionalEstimate = true;
}
if (strpos($roid, ",") !== false) {
    $roArr = explode(",", $roid);
    $roid = $roArr[0];
}

require CONNWOSHOPID;

if (!function_exists("replace")) {
    function replace($subject, $search, $replace)
    {
        return str_ireplace($search, $replace, $subject);
    }
}


$stmt = "SELECT CompanyName, nexpartusername, CompanyState, hst, pst, gst, qst, chargehst, chargepst, chargegst, chargeqst, invoicedimension, showdeclined, showrevapps, showinvoicenumber, showtechoninvoice, printbar, printvitotals, CompanyName, CompanyAddress, CompanyCity, CompanyState, CompanyZip, CompanyPhone, CompanyFax, CompanyEmail, printcommlog, CompanyURL, printpopup, StorageFee, showpartnumberonprintedro, EPANo, BarNo, rowarrdisclosure, rodisclosure, showpayments, printpayments, printtechstory, printadvisorcomments, showlaborhoursonro, showlaborratesonro, milesinlabel, milesoutlabel, replacerowithtag, logo, invoicetitle, estimatetitle, itemizefeesprintedro, showtirepressure, partsdiscountonro, showsourceonprintedro, showpcodeoninvoice, showadvisoroninvoice  FROM company WHERE shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $res = $query->get_result();
    $rs = $res->fetch_assoc();
    $query->close();
} else {
    echo "Prepare failed " . $conn->error;
}
$showprices = $rs["nexpartusername"];

$showmichmechanic = "no";
if (strtolower($rs["CompanyState"]) == "mi" or strtolower($rs["CompanyState"]) == "michigan") {
    $showmichmechanic = "yes";
}

$hst = doubleval($rs["hst"]);
$pst = doubleval($rs["pst"]);
$gst = doubleval($rs["gst"]);
$qst = doubleval($rs["qst"]);
$chargehst = $rs["chargehst"];
$chargepst = $rs["chargepst"];
$chargegst = $rs["chargegst"];
$chargeqst = $rs["chargeqst"];
$dimension = $rs["invoicedimension"];


$showdeclined = $rs["showdeclined"];
$showrevapps = $rs["showrevapps"];
$showinvoicenumber = $rs["showinvoicenumber"];
$showtech = strtolower($rs["showtechoninvoice"]);
$printbar = strtolower($rs["printbar"]);
$printvitotals = strtolower($rs["printvitotals"]);
$shopname = trim($rs["CompanyName"]);
$shopaddress = $rs["CompanyAddress"];
$shopcsz = $rs["CompanyCity"] . ", " . $rs["CompanyState"] . ". " . $rs["CompanyZip"];
$shopphone = formatphone($rs["CompanyPhone"]);
if (!empty($rs["CompanyFax"])) {
    if ($shopid == "3882") {
        $shopphone .= "  Cell: " . formatphone($rs["CompanyFax"]);
    } else {
        $shopphone .= "  Fax: " . formatphone($rs["CompanyFax"]);
    }
}
$shopemail = $rs["CompanyEmail"];
$printcommlog = strtolower($rs["printcommlog"]);
$shopurl = $rs["CompanyURL"];
$printpopup = $rs["printpopup"];
$storagefee = $rs["StorageFee"];
$showpartnumberonprintedro = $rs["showpartnumberonprintedro"];

if (is_numeric(trim($storagefee))) {
    $storagefee = number_format($storagefee, 2);
}
if (strlen($rs["EPANo"]) > 1) {
    $shopepa = "EPA# " . $rs["EPANo"];
} else {
    $shopepa = "";
}
if (strlen($rs["BarNo"]) > 1) {
    if (strtoupper(left($rs["BarNo"], 3)) == "GST" || strtoupper(left($rs["BarNo"], 3)) == "HST" || strtoupper(left($rs["BarNo"], 3)) == "PST" || strtoupper(left($rs["BarNo"], 3)) == "QST") {
        $shopbar = $rs["BarNo"];
    } else {
        $shopbar = "BAR# " . $rs["BarNo"];
    }
} else {
    $shopbar = "";
}
$warrdisc = $rs["rowarrdisclosure"];

$rodisc = $rs["rodisclosure"];
$showpayments = strtolower($rs["showpayments"]);
$individualpayments = strtolower($rs["printpayments"]);
$techstory = strtolower($rs["printtechstory"]);
$printadvisorcomments = strtolower($rs["printadvisorcomments"]);
$showlaborhours = strtolower($rs["showlaborhoursonro"]);
$showlaborrate = strtolower($rs["showlaborratesonro"]);
$milesinlabel = strtoupper($rs["milesinlabel"]);
$milesoutlabel = strtoupper($rs["milesoutlabel"]);
$replacerowithtag = strtolower($rs["replacerowithtag"]);
$logo = $rs["logo"];
$invoicetitle = strtoupper($rs["invoicetitle"]);
$estimatetitle = strtoupper($rs["estimatetitle"]);
$itemizedfees = strtolower($rs["itemizefeesprintedro"]);
$showtirepressure = strtolower($rs["showtirepressure"]);
$partsdiscountonro = strtolower($rs["partsdiscountonro"]);
$showsourceonprintedro = strtolower($rs["showsourceonprintedro"]);
$showpcodeoninvoice = strtolower($rs["showpcodeoninvoice"]);
$showadvisoroninvoice = $rs["showadvisoroninvoice"];


$printroid = $roid;
if ($replacerowithtag == "yes") {
    //get the tag number
    $stmt = "select COALESCE(UCASE(tagnumber),'') as tagnumber from repairorders where shopid = ? and roid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $roid);
        $query->execute();
        $query->bind_result($tagnum);
        $query->store_result();
        if($query->num_rows > 0) {
            $query->fetch();
        }
        $query->close();
    } else {
        echo "L161 prepare failed ".$conn->error;
    }
}
if(!empty($tagnum)){
    $printroid = $tagnum;
}
// get customer info from repair order
$stmt = "select roid, cb, canadiantax, customerid, Writer, `source`, warrmos, warrmiles, email, rev1amt, rev1date, rev1phone, rev1time, rev1by, rev2amt, rev2date, rev2phone, rev2time, rev2by, tirepressureinlf, tirepressureinrf, tirepressureinlr, tirepressureinrr, tirepressureoutlf, tirepressureoutlr, tirepressureoutrf, tirepressureoutrr, treaddepthlf, treaddepthlr, treaddepthrf, treaddepthrr, rodisc, warrdisc, ponumber, vehinfo, UCASE(vehlicense) as vehlicense, vehstate, vehiclemiles, milesout, vehengine, vin, fleetno, customvehicle1label, customvehicle1, customvehicle2label, customvehicle2, customvehicle3label, customvehicle3, customvehicle4label, customvehicle4, discountamt, `status`, userfee1, userfee1label, userfee2, userfee2label, userfee3, userfee3label, HazardousWaste, storagefee, vehmake, vehmodel, VehTrans, DriveType, Customer, CustomerAddress, DateIn, customercity, customerstate, customerzip, CustomerPhone, CellPhone, CustomerWork, vehstate, StatusDate, vehyear, VehicleMiles, MilesOut, TotalRO, origro, TotalPrts, TotalLbr, TotalSublet, TotalFees, Subtotal, TotalFees, TaxRate, SalesTax, AmtPaid1, AmtPaid2, vehid from repairorders where shopid = ? and roid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $roid);
    $query->fetch();
    $query->execute();
    $res = $query->get_result();
    $rs = $res->fetch_assoc();
    $query->close();
}

$discountlabel = $rs["cb"];
$canadiantax = $rs["canadiantax"];
if (!empty($canadiantax)) {
    $cantar = explode(",", $canadiantax);
    $hst = $cantar[0];
    $pst = $cantar[1];
    $gst = $cantar[2];
    $qst = $cantar[3];
}

$hststr = $gststr = $pststr = $qststr = "";
$cantaxstr = "";
$sepCantaxCharged = array();
if ($chargehst == "yes" && $hst > 0) {
    $hststr = "HST";
    $sepCantaxCharged['hst'] = 'HST';
}

if ($chargegst == "yes" && $gst > 0) {
    $gststr = "GST";
    $sepCantaxCharged['gst'] = 'GST';
}

if ($chargepst == "yes" && $pst > 0) {
    $pststr = "PST";
    $sepCantaxCharged['pst'] = 'PST';
}

if ($chargeqst == "yes" && $qst > 0) {
    $qststr = "QST";
    $sepCantaxCharged['qst'] = 'QST';
}

if (!empty($hststr)) {
    $cantaxstr = $cantaxstr . $hststr;
}

if (!empty($gststr)) {
    if (!empty($cantaxstr)) {
        $cantaxstr = $cantaxstr . "+" . $gststr;
    } else {
        $cantaxstr = $cantaxstr . $gststr;
    }
} else {
    $cantaxstr = $cantaxstr . $gststr;
}

if (!empty($pststr)) {
    if (!empty($cantaxstr)) {
        $cantaxstr = $cantaxstr . "+" . $pststr;
    } else {
        $cantaxstr = $cantaxstr . $pststr;
    }
} else {
    $cantaxstr = $cantaxstr . $pststr;
}

if (!empty($qststr)) {
    if (!empty($cantaxstr)) {
        $cantaxstr = $cantaxstr . "+" . $qststr;
    } else {
        $cantaxstr = $cantaxstr . $qststr;
    }
} else {
    $cantaxstr = $cantaxstr . $qststr;
}
$showCanTax = "";
if (!empty($cantaxstr) && !($showSeparateCanadianTaxes)) {
    $showCanTax = '<tr><td>' . $cantaxstr . ' @ ' . $rs['TaxRate'] . '%</td><td class="text-right">'.asDollars($rs['SalesTax']).'</td></tr>';
} else {
    if ($showSeparateCanadianTaxes) {

        $footer_margin += 15;

        $stmt = "SELECT *  FROM canadiantaxcharged WHERE shopid = ? AND roid = ?";
        if ($ctxquery = $conn->prepare($stmt)) {
            $ctxquery->bind_param("ss", $shopid, $roid);
            $ctxquery->execute();
            $cantaxres = $ctxquery->get_result();
            $cantaxRow = $cantaxres->fetch_assoc();
            $ctxquery->close();
        }

        foreach ($sepCantaxCharged as $key => $val) {
            if(isset($cantaxRow[$key."rate"]) || isset($cantaxRow[$key."amount"])){
                $showCanTax .= '<tr><td>' . $val . ' @ ' . $cantaxRow[$key . "rate"] . '%</td><td class="text-right">' . asDollars($cantaxRow[$key . "amount"]) . '</td></tr>';
            }
        }
    }
}

// get the spouse phone
$spousecell = "";
$spousework = "";
$customertype = "";
$customerid = $rs["customerid"];

$stmt = "select spousecell,spousework,UCASE(customertype) from customer where shopid = ? and customerid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $customerid);
    $query->execute();
    $query->bind_result($spousecell, $spousework, $customertype);
    $query->fetch();
    $query->close();
}
$writer = "";
if ($showadvisoroninvoice == "yes") {
    $writer = "Writer: " . strtoupper($rs["Writer"]);
}

//if settings for source is yes display else dont display
if ($showsourceonprintedro == "yes") {
    $custsource = $rs["source"];
}


$warrmos = $rs["warrmos"];
$warrmiles = $rs["warrmiles"];
$customeremail = $rs["email"];
$veh_id = $rs['vehid'];

$rev1amt = $rs["rev1amt"];
$rev1date = $rs["rev1date"];
$rev1phone = $rs["rev1phone"];
$rev1time = $rs["rev1time"];
$rev1by = $rs["rev1by"];

$rev2amt = $rs["rev2amt"];
$rev2date = $rs["rev2date"];
$rev2phone = $rs["rev2phone"];
$rev2time = $rs["rev2time"];
$rev2by = $rs["rev2by"];

$tirepressureinlf = $rs["tirepressureinlf"] . " PSI";
$tirepressureinrf = $rs["tirepressureinrf"] . " PSI";
$tirepressureinlr = $rs["tirepressureinlr"] . " PSI";
$tirepressureinrr = $rs["tirepressureinrr"] . " PSI";

$tirepressureoutlf = $rs["tirepressureoutlf"] . " PSI";
$tirepressureoutrf = $rs["tirepressureoutrf"] . " PSI";
$tirepressureoutlr = $rs["tirepressureoutlr"] . " PSI";
$tirepressureoutrr = $rs["tirepressureoutrr"] . " PSI";

$treaddepthlf = $rs["treaddepthlf"] . "/32" . chr(34);
$treaddepthrf = $rs["treaddepthrf"] . "/32" . chr(34);
$treaddepthlr = $rs["treaddepthlr"] . "/32" . chr(34);
$treaddepthrr = $rs["treaddepthrr"] . "/32" . chr(34);


if (!empty($warrdisc)) {
    $warrdisc = replace($warrdisc, "[warrantymonths]", $warrmos);
    $warrdisc = replace($warrdisc, "[warrantymiles]", $warrmiles);
    $warrdisc = replace($warrdisc, "[storagefee]", $storagefee);
}

$rodiscfromro = $rs["rodisc"];
$warrdiscfromro = $rs["warrdisc"];

if (!empty($warrdiscfromro)) {
    $warrdiscfromro = replace($warrdiscfromro, "[warrantymonths]", $warrmos);

    if (!empty($warrmiles)) {
        $warrdiscfromro = replace($warrdiscfromro, "[warrantymiles]", $warrmiles);
    }
    if (!empty($storagefee)) {
        $warrdiscfromro = replace($warrdiscfromro, "[storagefee]", $storagefee);
    }
}

$ponumber = $rs["ponumber"];
if (!empty($ponumber)) {
    $ponumber = "   PO Number: " . $ponumber;
} else {
    $ponumber = "";
}

$veh = "" . strtoupper(left($rs["vehinfo"], 50));
$lic = "License: " . strtoupper($rs["vehlicense"]) . " - " . $rs["vehstate"];
$miles = "" . strtoupper($milesinlabel) . ": " . $rs["vehiclemiles"] . "   " . strtoupper($milesoutlabel) . ": " . $rs["milesout"];
$engine = strtoupper(left($rs["vehengine"], 22));
$vin = "VIN: " . strtoupper($rs["vin"]);
$fleet = "";
if (!empty($rs["fleetno"])) {
    //get feetno label from vehicle labels
    $stmt = "select UCASE(fleetlabel) from vehiclelabels where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($fleet);
        $query->fetch();
        $query->close();
    }
}

$cf1name = $rs["customvehicle1label"];
$cf1val = $rs["customvehicle1"];
$cf2name = $rs["customvehicle2label"];
$cf2val = $rs["customvehicle2"];
$cf3name = $rs["customvehicle3label"];
$cf3val = $rs["customvehicle3"];
$cf4name = $rs["customvehicle4label"];
$cf4val = $rs["customvehicle4"];
$discount = $rs["discountamt"];
$rostatus = strtoupper($rs["status"]);

$userfee1 = $rs["userfee1"];
$userfee1label = strtoupper($rs["userfee1label"]);
//$userfee1tax = round($userfee1 * 0.05, 2);

$userfee2 = $rs["userfee2"];
$userfee2label = strtoupper($rs["userfee2label"]);

$userfee3 = $rs["userfee3"];
$userfee3label = strtoupper($rs["userfee3label"]);

$hazwaste = $rs["HazardousWaste"];
$chargedstoragefee = $rs["storagefee"];

if (strtolower($rostatus) == "final" || strtolower($rostatus) == "closed") {
    $printtype = $invoicetitle;
} else {
    $printtype = $estimatetitle;
}
$shopname = html_entity_decode($shopname);

$recrepairs = "";
$stmt = "select UCASE(`desc`), totalrec from recommend where roid = ? and shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("is", $roid, $shopid);
    $query->execute();
    $query->bind_result($rdesc, $totalrec);
    while ($query->fetch()) {
        $recrepairs .= " *** " . $rdesc . " ( $" . number_format($totalrec) . ")";
    }
    $query->close();
}


$logoURL = "";
$logoPath = "";
if (!empty($logo)) {
    $logoPath = "\\fs.shopboss.aws\share\upload\\$shopid\\$logo";
    $logoURL = "https://" . $_SERVER['SERVER_NAME'] . "/sbp/upload/$shopid/$logo";
} else {
    if ($rostatus == "FINAL" || $rostatus == "CLOSED") {
        if (strlen($invoicetitle) > 1) {
            $logoURL = IMAGE . "/newimages/invoicelogo.png";
            $logoPath = $logoURL;
        }
    } else {
        if (strlen($estimatetitle) > 1) {
            if (strtolower($estimatetitle) == "estimate") {
                $logoURL = IMAGE . "/newimages/estimate.png";
                $logoPath = $logoURL;
            }
        }
    }
}


$stmt = "select UCASE(yearlabel), UCASE(makelabel), UCASE(modellabel), UCASE(vinlabel), UCASE(enginelabel), UCASE(translabel), UCASE(drivelabel), UCASE(licenselabel), UCASE(fleetlabel), UCASE(statelabel), UCASE(colorlabel) from vehiclelabels where shopid = ?";
if ($vquery = $conn->prepare($stmt)) {
    $vquery->bind_param("s", $shopid);
    $vquery->execute();
    $vquery->bind_result($yrlbl, $mklbl, $mdlbl, $vinlbl, $englbl, $trlbl, $drlbl, $liclbl, $fltlbl, $stlbl, $clrlbl);
    $vquery->fetch();
    $vquery->close();
}
$vmake = left(strtoupper(replace($rs["vehmake"], "&#39;", "'") . " " . replace($rs["vehmodel"], "&#39;", "'")), 22);
$vtrans = strtoupper(left(trim($rs["VehTrans"]) . "/" . trim($rs["DriveType"]), 18));

if($showVehicleColor){
    $vvstmt = "SELECT UCASE(color) FROM vehicles WHERE shopid = ? AND VehID = ?";
    if($cvquery = $conn->prepare($vvstmt)){
        $cvquery->bind_param("si", $shopid, $veh_id);
        $cvquery->execute();
        $cvquery->bind_result($vehcolor);
        $cvquery->fetch();
        $cvquery->close();
    }
} else {
    $clrlbl = "";
    $vehcolor = "";
}

$headerRight = "";
if ($showinvoicenumber == "no") {
    $headerRight = '<br><br> <b style="font-size: x-large;">' . $printtype . '</font><br>';
} else {
    $headerRight = '<br><br> <b style="font-size: x-large;">' . $printtype . ' # ' . $printroid . '</b><br>';
}

if ($printbar == "yes" && !empty($shopbar)) {
    $headerRight .= '<span style="font-size: small"><br> ' . $shopemail . '<br>' . $shopurl . '<br>' . $shopbar . '<br>' . $shopepa . '</span>';
} else {
    $headerRight .= '<span style="font-size: small"><br>' . $shopemail . '<br>' . $shopurl . '<br>' . $shopepa . '</span>';
}

$sfs = "large";
$cszfs = "small";
if(strlen($shopname) > 25){
    $sfs = "small";
    $cszfs = "x-small";
}else if(strlen($shopname) > 15){
    $sfs = "medium";
}

if(!empty($logoPath)) {
    $logoIMG = '<img src="' . $logoPath . '" style="height: 60pt" width="" />';
}

$headerTable = <<<EOD
<style>
table tr td {
    line-height: 12em;
}
</style>
<table>
<tr>
    <td style="">
    <br><br>
<span style="font-size: $sfs; font-weight: 700">$shopname</span>
    <br>
<span style="font-size: $cszfs;">$shopaddress
    <br>
$shopcsz
<br>
$shopphone
    <br>
$writer
    </span>
    </td>
    
    <td style="text-align: center; height: 70pt; vertical-align: top">
        <div style="vertical-align: top;">
            $logoIMG
        </div>
    </td>
    
    <td style="text-align: right;">
        $headerRight
    </td>
</tr>
</table>
EOD;

$cust = trim(replace(strtoupper(left($rs["Customer"], 32)), "&#39;", "'"));
$addr = strtoupper($rs["CustomerAddress"]);
$invdate = $rs["DateIn"];
$csz = strtoupper($rs["customercity"]) . ", " . strtoupper($rs["customerstate"]) . ". " . $rs["customerzip"];

//flag to add a break after first two phone numbers
$brflag = 0;

if (!empty($rs["CustomerPhone"])) {
    $dphone = "" . "Home: " . formatphone($rs["CustomerPhone"]) . " ";
    $brflag = $brflag + 1;
}

if (!empty($rs["CellPhone"])) {
    $dphone = $dphone . " Cell: " . formatphone($rs["CellPhone"]) . " ";
    $brflag = $brflag + 1;
}

if ($brflag == 2) {
    $dphone = $dphone . "<br>";
}

if (!empty($rs["CustomerWork"])) {
    $dphone .= "Work: " . formatphone($rs["CustomerWork"]) . " ";
    $brflag = $brflag + 1;
}

if ($brflag == 3) {
    $dphone .= "<br>";
}

if (!empty($spousecell)) {
    $dphone .= "Spouse: " . formatphone($spousecell) . " ";
}

if ($remoteauth == "yes") {
    $addr = "  Not Shown for Security";
    $csz = "  Not Shown for Security";
    $dphone = "  Not Shown for Security";
}
$vehState = strtoupper($rs['vehstate']);

$lineval = "In: " . date('n/j/Y', strtotime($rs["DateIn"])) . " Out: " . date('n/j/Y', strtotime($rs["StatusDate"]));
if (!empty($ponumber)) {
    $lineval .= $ponumber;
}
if (!empty($cf1name)) {
    $lineval .= "   " . $cf1name . " " . $cf1val;
}
if (!empty($cf2name)) {
    $lineval .= "  " . $cf2name . " " . $cf2val;
}
if (!empty($cf3name)) {
    $lineval .= "  " . $cf3name . " " . $cf3val;
}
if (!empty($cf4name)) {
    $lineval .= "  " . $cf4name . " " . $cf4val;
}

// only show source when setting is on
if ($showsourceonprintedro == "yes") {
    $lineval .= "  Source: " . $custsource;
}

if (!empty($customeremail)) {
    $lineval .= "  Email: " . $customeremail;
}

$lineval = substr($lineval, 0, 157);

$fleetlabel = (empty($fleet))? $fltlbl : $fleet;
$fleetno = strtoupper($rs['fleetno']);
if(empty($fleetno)){
    $fleetno = $fleetlabel = "";
}

$yrlbl = substr($yrlbl, 0, 12);
$mklbl = substr($mklbl, 0, 6);
$mdlbl = substr($mdlbl, 0, 7);
$vinlbl = substr($vinlbl, 0, 12);
$englbl = substr($englbl, 0, 12);
$trlbl = substr($trlbl, 0, 9);
$drlbl = substr($drlbl, 0, 9);
$liclbl = substr($liclbl, 0, 9);
$stlbl = substr($stlbl, 0, 9);
$fltlbl = substr($fltlbl, 0, 18);
$milesinlabel = substr($milesinlabel, 0, 9);
$milesoutlabel = substr($milesoutlabel, 0, 9);
$milesIn = substr($rs['VehicleMiles'], 0, 9);
$milesOut = substr($rs['MilesOut'], 0, 9);
$vehlicense = substr($rs['vehlicense'], 0, 9);
$vehState = substr($vehState, 0, 9);

$dphone = trim($dphone);
$custnVehTable = <<<EOD
<br>
<table style="font-size: xx-small; border-top: 1pt solid black; border-bottom: 1pt solid black">
    <tr>
        <td style="width: 162pt; line-height: 9pt" rowspan="5"><b>$cust</b><br><span style="line-height: 8pt">$addr<br>$csz<br>$dphone</span></td>
        <td style="text-align: left; width: 70pt">$yrlbl:</td>
        <td style="text-align: left; width: 120pt;">{$rs["vehyear"]}</td>
        <td style="text-align: left; width: 100pt;">$trlbl/$drlbl:</td>
        <td style="text-align: left;width: 100pt;">$vtrans</td>
    </tr>
    <tr>
       <td style="text-align: left;">$mklbl/$mdlbl:</td>
       <td style="text-align: left;">$vmake</td>
       <td style="text-align: left;">$liclbl/$stlbl:</td>
       <td style="text-align: left">{$rs['vehlicense']}/$vehState</td>
    </tr>
    <tr>
       <td style="text-align: left">$vinlbl:</td>
       <td style="text-align: left">{$rs['vin']}</td>
       <td style="text-align: left">$milesinlabel/$milesoutlabel:</td>
       <td style="text-align: left">$milesIn/$milesOut</td>
    </tr>
    <tr>
        <td>$englbl:</td>        
        <td>$engine</td>
       <td style="text-align: left">$fleetlabel</td>
       <td style="text-align: left">$fleetno</td>
    </tr>
    <tr>   
        <td></td>
        <td></td>
          <td style="text-align: left">$clrlbl</td>
       <td style="text-align: left">$vehcolor</td>
    </tr>
</table>
EOD;
//$showtirepressure = "yes";
$tirePressureTable = "";
if($showtirepressure == "yes"){

    $gencarSmall = IMAGE."/newimages/gencarsmall.jpg";
    $underline = IMG_DIR."newimages/underline.jpg";

    $tirePressureTable = <<<TIREPRESSURE
        <table style="font-size: xx-small; font-weight: bold; text-align: center;" cellspacing="3" cellpadding="3">
            <tr>
                <td colspan="9" style="line-height: 1pt"> </td>
            </tr>
            <tr>
                <td style="text-align: center;" colspan="3">AIR PRESSURE IN</td>
                <td style="text-align: center" colspan="3">AIR PRESSURE OUT</td>
                <td style="text-align: center" colspan="3">TREAD DEPTH</td>
            </tr>
            <tr>
                <td style="text-align: right">
                    <div style="border-bottom: 1px solid black;">$tirepressureinlf  &nbsp;&nbsp;&nbsp;</div>
                    <br>
                    <div style="border-bottom: 1px solid black">$tirepressureinlr  &nbsp;&nbsp;&nbsp;</div>
                </td>
                <td style="text-align: center;"><br><br>
                <img src="$gencarSmall" style="height: 50pt;" />
                </td>
                <td style="text-align: right">
                    <div style="border-bottom: 1px solid black;">$tirepressureinrf  &nbsp;&nbsp;&nbsp;</div>
                    <br>
                    <div style="border-bottom: 1px solid black">$tirepressureinrr  &nbsp;&nbsp;&nbsp;</div>
                </td>
                
               <td style="text-align: right">
                    <div style="border-bottom: 1px solid black;">$tirepressureoutlf  &nbsp;&nbsp;&nbsp;</div>
                    <br>
                    <div style="border-bottom: 1px solid black">$tirepressureoutlr  &nbsp;&nbsp;&nbsp;</div>
                </td>
                <td style="text-align: center;"><br><br>
                <img src="$gencarSmall" style="height: 50pt;" /></td>
                <td style="text-align: right">
                    <div style="border-bottom: 1px solid black;">$tirepressureoutrf  &nbsp;&nbsp;&nbsp;</div>
                    <br>
                    <div style="border-bottom: 1px solid black">$tirepressureoutrr  &nbsp;&nbsp;&nbsp;</div>
                </td>
                
                <td style="text-align: right">
                    <div style="border-bottom: 1px solid black;">$treaddepthlf  &nbsp;&nbsp;&nbsp;</div>
                    <br>
                    <div style="border-bottom: 1px solid black">$treaddepthlr  &nbsp;&nbsp;&nbsp;</div>
                </td>
                <td style="text-align: center;"><br><br>
                <img src="$gencarSmall" style="height: 50pt;" /></td>
                <td style="text-align: right">
                    <div style="border-bottom: 1px solid black;">$treaddepthlr  &nbsp;&nbsp;&nbsp;</div>
                    <br>
                    <div style="border-bottom: 1px solid black">$treaddepthrr  &nbsp;&nbsp;&nbsp;</div>
                </td>
            </tr>
        </table>
    TIREPRESSURE;



}//END TIRE PRESSURE


$headerTable .= $custnVehTable;

if (strtolower($showdeclined) == "yes") {
    $stmt = "select complaint, advisorcomments, complaintid, acceptdecline,techreport  from complaints where cstatus = 'no' and shopid = ? and roid = ? group by complaintid  order by displayorder";
} else {
    $stmt = "select complaint, advisorcomments, complaintid, acceptdecline,techreport from complaints where cstatus = 'no' and acceptdecline != 'Declined' and shopid = ? and roid = ? group by complaintid  order by displayorder";
}

if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $roid);
    $query->execute();
    $results = $query->get_result();
    $query->close();
}

$complaintsHTML = '<tr style="line-height: 1pt"><td style="width: 15%"></td><td style="width: 50%;"></td><td style="width: 15%;"></td><td style="width: 10%"></td><td style="width: 10%"></td></tr>';

if ($results->num_rows > 0) {
    $c = 1;
    while ($crs = $results->fetch_assoc()) {
        $viptotal = 0;
        $viltotal = 0;
        $vistotal = 0;
        $comlabel = $crs['complaint'];
        $advcom = strtoupper($crs['advisorcomments']);
        $complaintid = $crs["complaintid"];

        if (strtolower($crs["acceptdecline"]) == "declined") {
            $complaint = trim("VEHICLE ISSUE #" . $c . ": " . replace(replace(strtoupper($crs["complaint"]), "&#34;", "''"), "&#39;", "'")) . " (CUSTOMER DECLINED)";
        } else {
            $complaint = trim("VEHICLE ISSUE #" . $c . ": " . replace(replace(strtoupper($crs["complaint"]), "&#34;", "'"), "&#39;", "'"));
        }
        $itechstory = $crs["techreport"];
        if ($c == 1) {
            $complaintsHTML .= '<tr><td colspan="5" style="background-color: silver; font-size: x-small; font-weight: 100; border: 1px solid black;"> ' . $complaint . '</td></tr>';
        } else {
            $complaintsHTML .= '<tr><td colspan="5" style="background-color: silver; font-size: x-small; font-weight: 100; border: 1px solid black;"> ' . $complaint . '</td></tr>';
        }

        if (strtolower($techstory) == "yes" && !empty($itechstory)) {
            $displaytechstory = "TECH REPORT: " . nl2br(strtoupper(replace(replace($itechstory, "&#34;", "''"), "&#39;", "'")));
            $complaintsHTML .= '<tr><td colspan="5" style="font-size: xx-small; font-weight: 100; border: 1px solid black;"> ' . $displaytechstory . '</td></tr>';
        }

        if (strtolower($printadvisorcomments) == "yes" && !empty($advcom)) {
            $displayadvisor = "ADV NOTES: " . nl2br(strtoupper(replace(replace($advcom, "&#34;", "''"), "&#39;", "'")));
            $complaintsHTML .= '<tr><td colspan="5" style="font-size: x-small; font-weight: 100; border: 1px solid black;"> ' . $displayadvisor . '</td></tr>';
        }
        if ($showPartsOnInvoice) {
            if ($showdeclined == "yes") {
                if ($crs["acceptdecline"] == "Declined") {
                    $stmt = "select partnumber, partcode, partprice, linettlprice, partdesc, quantity, discount from recommendparts where roid = ? and shopid = ? and complaintid = ?";
                } else {
                    $stmt = "select partnumber, partcode, partprice, linettlprice, partdesc, quantity, discount  from parts where roid = ? and shopid = ? and complaintid = ? and deleted != 'yes' order by displayorder, partid";
                }
            } else {
                $stmt = "select partnumber, partcode, partprice, linettlprice, partdesc, quantity, discount from parts where roid = ? and shopid = ? and complaintid = ? and deleted != 'yes' order by displayorder, partid";
            }

            if ($pquery = $conn->prepare($stmt)) {
                $pquery->bind_param("isi", $roid, $shopid, $complaintid);
                $pquery->execute();
                $pres = $pquery->get_result();
                $pquery->close();
            }

            if ($pres->num_rows > 0) {
                while ($lrs = $pres->fetch_assoc()) {
                    $viptotal += $lrs['linettlprice'];
                    $cell1 = $cell2 = $cell3 = $cell4 = $cell5 = "";
                    if (strtolower($showpartnumberonprintedro) == "yes") {
                        if (trim($lrs["partnumber"]) == "JOB") {
                            $cell1 = "JOB: ";
                        } else {
                            $cell1 = "PART: " . strtoupper($lrs["partnumber"]);
                        }
                    } else {
                        if (trim($lrs["partnumber"]) == "JOB") {
                            $cell1 = "JOB: ";
                        } else {
                            $cell1 = "PART";
                        }
                    }

                    $cell2 = replace(replace(strtoupper($lrs["partdesc"]), "&#34;", "''"), "&#39;", "'");

                    if ($partsdiscountonro == "yes") {
                        if (doubleval($lrs["discount"]) > 0) {
                            $discountdisplay = "(" . $lrs["discount"] . "% Discount) ";
                        } else {
                            $discountdisplay = "";
                        }
                    } else {
                        $discountdisplay = "";
                    }

                    if ($crs["acceptdecline"] == "Declined") {
                        $cell3 = $cell4 = "";
                    } else {
                        if (strtolower($showprices) == "no") {
                            $cell3 = $lrs['quantity'];
                        } else {
                            $cell3 = $lrs['quantity'] . " @ " . asDollars($lrs['partprice']);
                            $cell4 = $discountdisplay . asDollars($lrs['linettlprice']);
                        }
                    }

                    $complaintsHTML .= <<<PARTROWS
                    <tr style="font-size: 8pt">
                        <td class="border-left" style="width: 15%;">$cell1</td>
                        <td style="width: 50%;">$cell2</td>
                        <td style="width: 15%; text-align: right">$cell3</td>
                PARTROWS;

                    if ($showpcodeoninvoice == "yes") {
                        $cell5 = strtoupper($lrs['partcode']);
                        $complaintsHTML .= '<td style="width: 10%; text-align: right">' . $cell4 . '</td><td class="border-right" style="width: 10%; text-align: right">' . $cell5 . '</td>';
                    } else {
                        $complaintsHTML .= '<td colspan="2" class="border-right" style="text-align: right">' . $cell4 . '</td>';
                    }
                    $complaintsHTML .= "</tr>";
                } //PARTS LOOP
            } //PARTS ROW CONDITION
        }


        if ($showdeclined == "yes") {
            if ($crs["acceptdecline"] == "Declined") {
                $stmt = "select `desc` labor, total linetotal, hours laborhours, rate hourlyrate, 0 as discount, tech from recommendlabor where roid = ? and shopid = ? and comid = ?";
            } else {
                $stmt = "select tech, linetotal, laborhours, hourlyrate, discount, labor from labor where roid = ? and shopid = ? and complaintid = ?";
            }
        } else {
            $stmt = "select tech, linetotal, laborhours, hourlyrate, discount, labor from labor where roid = ? and shopid = ? and complaintid = ?";
        }

        if ($lquery = $conn->prepare($stmt)) {
            $lquery->bind_param("isi", $roid, $shopid, $complaintid);
            $lquery->execute();
            $lres = $lquery->get_result();
            $lquery->close();
        }

        if ($lres->num_rows > 0) {
            while ($lrs = $lres->fetch_assoc()) {
                $cell1 = $cell2 = $cell3 = $cell4 = $cell5 = "";
                $viltotal += $lrs['linetotal'];
                if ($showlaborhours == "yes") {
                    $laborhours = $lrs['laborhours'] . " hours";
                } else {
                    $laborhours = "";
                }
                $mechanicnumber = "";
                if ($showmichmechanic == "yes") {
                    $tech = $lrs["tech"];
                    if (strpos($tech, ",") !== false) {

                        $tar = explode(",", $tech);
                        $tlast = trim(replace($tar[0], "'", "''"));
                        $tfirst = trim(replace($tar[1], "'", "''"));

                        $stmt = "select mechanicnumber from employees where shopid = ? and employeelast = ? and employeefirst = ? and showtechlist = 'yes'";
                        if ($mquery = $conn->prepare($stmt)) {
                            $mquery->bind_param("sss", $shopid, $tlast, $tfirst);
                            $mquery->execute();
                            $mquery->bind_result($mechanicnumber);
                            $mquery->fetch();
                            $mquery->close();
                        }
                        if (!empty($mechanicnumber)) {
                            $michmechnumber = " (" . $mechanicnumber . ")";
                        }
                    }
                }

                if ($showlaborrate == "yes") {
                    $laborhours = $lrs["laborhours"] . " Hours " . asDollars($lrs["hourlyrate"]);
                }

                if (strtolower($showprices) == "no") {
                    $linettl = "";
                } else {
                    if (doubleval($lrs["discount"])) {
                        $linettl = "(Discount: " . asDollars($lrs["discount"]) . ") " . asDollars($lrs["linetotal"]);
                    } else {
                        $linettl = asDollars($lrs["linetotal"]);
                    }
                }

                if ($showtech == "no") {
                    $cell1 = "LABOR: ";
                } else {
                    $cell1 = "LABOR: " . left($lrs["tech"], 8) . $michmechnumber;
                }

                $cell2 = strtoupper(replace($lrs["labor"], "&#39;", "'"));
                if ($crs["acceptdecline"] == "Declined") {
                    $cell3 = $cell4 = "";
                } else {
                    $cell3 = $laborhours;
                    $cell4 = $linettl;
                }
                if($showpcodeoninvoice == "yes") {
                    $cell5 = "LABOR";
                }


                $complaintsHTML .= <<<LABORROWS
                    <tr>
                        <td class="border-left" style="width: 15%">$cell1</td>
                        <td style=" width: 50%">$cell2</td>
                        <td style="text-align: right; width: 15%">$cell3</td>
                LABORROWS;

                if ($showpcodeoninvoice == "yes") {
                    $cell5 = "LABOR";
                    $complaintsHTML .= '<td style="width: 10%; text-align: right">' . $cell4 . '</td><td class="border-right" style="width: 10%; text-align: right">' . $cell5 . '</td>';
                } else {
                    $complaintsHTML .= '<td colspan="2" class="border-right" style="text-align: right">' . $cell4 . '</td>';
                }
                $complaintsHTML .= "</tr>";
            } //LABOR LOOP
        }//LABOR ROW CONDITION


        if ($showdeclined == "yes") {
            if ($crs["acceptdecline"] == "Declined") {
                $stmt = "select subletdesc, subletprice from recommendsublet where roid = ? and shopid = ? and comid = ?";
            } else {
                $stmt = "select subletdesc, subletprice from sublet where roid = ? and shopid = ? and complaintid = ?";
            }
        } else {
            $stmt = "select subletdesc, subletprice from sublet where roid = ? and shopid = ? and complaintid = ?";
        }

        if ($squery = $conn->prepare($stmt)) {
            $squery->bind_param("isi", $roid, $shopid, $complaintid);
            $squery->execute();
            $sres = $squery->get_result();
            $squery->close();
        }

        if ($sres->num_rows > 0) {
            while ($lrs = $sres->fetch_assoc()) {
                $cell1 = $cell2 = $cell3 = $cell4 = $cell5 = "";
                $vistotal += $lrs['subletprice'];
                if (strtolower($showprices) == "no") {
                    $subletprice = "";
                } else {
                    $subletprice = asDollars($lrs['subletprice']);
                }

                $cell1 = "SUBLET";
                $cell2 = strtoupper(replace($lrs["subletdesc"], "&#39;", "'"));
                if ($crs["acceptdecline"] != "Declined")$cell4 = $subletprice;
                if($showpcodeoninvoice == "yes") {
                    $cell5 = "SUBLET";
                }

                $complaintsHTML .= <<<SUBLETROWS
                    <tr>
                        <td class="border-left" style="width: 15%">$cell1</td>
                        <td style="width: 50%">$cell2</td>
                        <td style="text-align: right; width: 15%">$cell3</td>
                SUBLETROWS;

                if ($showpcodeoninvoice == "yes") {
                    $cell5 = "SUBLET";
                    $complaintsHTML .= '<td style="width: 10%; text-align: right">' . $cell4 . '</td><td class="border-right" style="width: 10%; text-align: right">' . $cell5 . '</td>';
                } else {
                    $complaintsHTML .= '<td colspan="2" class="border-right" style="text-align: right">' . $cell4 . '</td>';
                }
                $complaintsHTML .= "</tr>";
            }//SUBLET LOOP
        }//SUBLET ROW CONDITION


        $vitotal = 0;
        $vitotal = $viptotal + $viltotal + $vistotal;

        if (is_numeric($vitotal)) {
            $vitotal = asDollars($vitotal);
        }


        if ($showdeclined == "yes") {
            if ($crs["acceptdecline"] == "Declined") {
                $complaintsHTML .= "<tr><td colspan=\"5\" style=\"text-align: right; border: 1px solid black\">*** CUSTOMER DECLINED THIS REPAIR AT THIS TIME ***</td></tr>";
            }
        }

        if ($printvitotals == "yes") {
            if ($itemizedItemsOnConcerns) {
                $complaintsHTML .= '<tr>
                    <td style="text-align: right;" class="border-top border-bottom border-left"></td>
                    <td style="text-align: right;" class="border-top border-bottom">Parts: ' . $viptotal . '</td>
                    <td style="text-align: right; border: 1px solid black">Labor: ' . $viltotal . '</td>
                    <td style="text-align: right; border: 1px solid black">Sublet: ' . $vistotal . '</td>
                    <td style="text-align: right; border: 1px solid black"><b>Total: ' . $vitotal . '</b></td>
                    </tr>';
            } else {
                $complaintsHTML .= '<tr><td colspan="5" style="text-align: right; border: 1px solid black">Total for "' . replace(replace(strtoupper($crs['complaint']), "&#34;", "''"), "&#39;", "'") . '": ' . $vitotal . '</td></tr>';
            }
        }
        $c++;

    } //COMPLAINT LOOP
} //COMPLAINT ROW CONDITION


// add revision if dollar amount > 0
$nextrevcntr = 1;
$cell1 = $cell2 = $cell3 = $cell4 = $cell5 = "";
if (strlen($rev1amt) > 1) {
    $nextrevcntr = 2;
    $cell1 = "REVISION 1: " . $rev1amt;
    $cell2 = "Date: " . $rev1date;
    $cell3 = "Time: " . strtoupper($rev1time);
    $cell4 = "Phone: " . strtoupper($rev1phone);
    $cell5 = "By: " . strtoupper($rev1by);
    $complaintsHTML .= <<<REVROWS
                    <tr>
                        <td class="border-left" style="">$cell1</td>
                        <td style="">$cell2</td>
                        <td style="text-align: right">$cell3</td>
                        <td style="text-align: right">$cell4</td>
                        <td class="border-right" style="text-align: right">$cell5</td>
                    </tr>
                REVROWS;
}

if (strlen($rev2amt) > 1) {
    $nextrevcntr = 3;
    $cell1 = "REVISION 2: " . $rev2amt;
    $cell2 = "Date: " . $rev2date;
    $cell3 = "Time: " . strtoupper($rev2time);
    $cell4 = "Phone: " . strtoupper($rev2phone);
    $cell5 = "By: " . strtoupper($rev2by);
    $complaintsHTML .= <<<REVROWS
                    <tr>
                        <td class="border-left" style=""">$cell1</td>
                        <td style="">$cell2</td>
                        <td style="text-align: right">$cell3</td>
                        <td style="text-align: right">$cell4</td>
                        <td class="border-right" style="text-align: right">$cell5</td>
                    </tr>
                REVROWS;
}


$revstmt = "select revamt, revby, revphone, revdate, revtime, revappmethod, revappby from revisions where shopid = ? and roid = ?";
if ($rquery = $conn->prepare($revstmt)) {
    $rquery->bind_param("si", $shopid, $roid);
    $rquery->execute();
    $revres = $rquery->get_result();
    $rquery->close();
}

if ($revres->num_rows > 0) {
    if (strtolower($showrevapps) == "yes") {

        $sumstmt = "select sum(revamt) as totrev from revisions where shopid = ? and roid = ?";
        if ($squery = $conn->prepare($sumstmt)) {
            $squery->bind_param("si", $shopid, $roid);
            $squery->execute();
            $squery->bind_result($totrev);
            $squery->fetch();
            $squery->close();
        } else {
            die("prepare failed " . $conn->error);
        }

        $oriest = doubleval($rs['TotalRO']) - doubleval($totrev);
        $oest = "Original Estimate: " . asDollars($oriest) . "						Total Revisions: " . asDollars($totrev) . "						Total RO: " . asDollars($rs["TotalRO"]);

        $complaintsHTML .= <<<REVROWS
                    <tr>
                        <td colspan="5">REVISIONS</td>
                    </tr>
                    <tr>
                        <td colspan="5">$oest</td>
                    </tr>
        REVROWS;

        while ($revrs = $revres->fetch_assoc()) {
            $cell1 = $cell2 = $cell3 = $cell4 = $cell5 = "";
            $revest = $oriest + doubleval($revrs['revamt']);
            $cell1 = "Additional Cost: <br>" . asDollars($revrs["revamt"]);
            $cell2 = "Revised Estimate: " . asDollars($revest) . "		Authorized By: " . $revrs["revby"] . "<br> Additional Work: " . $revrs["revphone"];
            $cell3 = "Date: " . date('n/j/Y', strtotime($revrs["revdate"])) . "<br>Time: " . date('h:i:s a', strtotime($revrs["revtime"]));
            $cell4 = "Approval Method: " . $revrs["revappmethod"] . "<br>" . $revrs["revappby"];

            $complaintsHTML .= <<<REVROWS
                    <tr>
                        <td class="border-left" style="">$cell1</td>
                        <td style="">$cell2</td>
                        <td style="text-align: right">$cell3</td>
                        <td class="border-right" style="text-align: right" colspan="2">$cell4</td>
                    </tr>
                REVROWS;
            $nextrevcntr++;
        }

    } else {

        while ($revrs = $revres->fetch_assoc()) {
            $cell1 = $cell2 = $cell3 = $cell4 = $cell5 = "";
            $cell1 = "REVISION " . $nextrevcntr . ":<br>" . asDollars($revrs["revamt"]);
            $cell2 = "DESCRIPTION: " . strtoupper($revrs["revdate"]) . " - " . strtoupper($revrs["revphone"]);
            $cell3 = "DATE: " . date('n/j/Y', strtotime($revrs["revdate"]));
            $cell4 = "TIME: " . date('h:i:s a', strtotime($revrs["revtime"]));
            $cell5 = "By: " . strtoupper($revrs["revby"]);

            $complaintsHTML .= <<<REVROWS
                    <tr>
                        <td class="border-left" style="">$cell1</td>
                        <td style="">$cell2</td>
                        <td style="text-align: right">$cell3</td>
                        <td class="border-right" style="text-align: right" colspan="2">$cell4</td>
                    </tr>
                REVROWS;
            $nextrevcntr++;
        }
    }
}//REV ROW CONDITION

if ($itemizedfees == "yes") {

    if ($userfee1 > 0) {
        $userfee1amt = asDollars($userfee1);
        $complaintsHTML .= <<<FEEROWS
                    <tr>
                        <td class="border" style="background-color: silver; text-align: right" colspan="5">$userfee1label : $userfee1amt</td>
                    </tr>
                FEEROWS;
    }
    if ($userfee2 > 0) {
        $userfee2amt = asDollars($userfee2);
        $complaintsHTML .= <<<FEEROWS
                    <tr>
                        <td class="border" style="background-color: silver; text-align: right" colspan="5">$userfee2label
                         : $userfee2amt</td>
                    </tr>
                FEEROWS;
    }
    if ($userfee3 > 0) {
        $userfee3amt = asDollars($userfee3);
        $complaintsHTML .= <<<FEEROWS
                    <tr>
                        <td class="border" style="background-color: silver; text-align: right" colspan="5">$userfee3label
                         : $userfee3amt</td>
                    </tr>
                FEEROWS;
    }
    if ($hazwaste > 0) {
        $hazwasteamt = asDollars($hazwaste);
        $complaintsHTML .= <<<FEEROWS
                    <tr>
                        <td class="border" style="background-color: silver; text-align: right" colspan="5">HAZARDOUS WASTE : $hazwasteamt</td>
                    </tr>
                FEEROWS;
    }
    if ($chargedstoragefee > 0) {
        $chargedstoragefeeamt = asDollars($chargedstoragefee);
        $complaintsHTML .= <<<FEEROWS
                    <tr>
                        <td class="border" style="background-color: silver; text-align: right" colspan="5">STORAGE FEE : $chargedstoragefeeamt</td>
                    </tr>
                FEEROWS;
    }

} //ITEMIZED FEES
$pd = $ld = 0;
$stmt = "select coalesce(sum(linettlprice),0) pd from parts where roid = ? and shopid = ? and partnumber = 'DISCOUNT' and partdesc = 'DISCOUNT' and supplier = 'DISCOUNT'";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("is", $roid, $shopid);
    $query->execute();
    $query->bind_result($pd);
    $query->fetch();
    $query->close();
}
$partsdiscount = doubleval($pd * -1);

$stmt = "select coalesce(sum(linetotal),0) ld from labor where roid = ? and shopid = ? and labor = 'DISCOUNT' and tech = 'DISCOUNT, DISCOUNT'";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("is", $roid, $shopid);
    $query->execute();
    $query->bind_result($ld);
    $query->fetch();
    $query->close();
}
$labordiscount = doubleval($ld * -1);

if ($partsdiscount > 0) {
    $pdl = "PARTS DISCOUNT - " . asDollars($partsdiscount);
}

if ($labordiscount > 0) {
    $ldl = "LABOR DISCOUNT - " . asDollars($labordiscount);
}

$tdl = "YOUR TOTAL SAVINGS TODAY - " . asDollars($partsdiscount + $labordiscount);

if (($partsdiscountonro == "yes" && $partsdiscount > 0) || ($labordiscount > 0)) {
    if ($discountlabel != "" && $discountlabel != "none") {
        $cellval = strtoupper($discountlabel) . "<br>" . $tdl;
    } else {
        $cellval = $pdl . $ldl . "<br>" . $tdl;
    }
    $complaintsHTML .= <<<DISCROW
                    <tr>
                        <td style="background-color: silver; border: 1px solid black; text-align: center; font-size: medium; font-weight: bold" colspan="5">$cellval</td>
                    </tr>
                DISCROW;
}


if ($individualpayments == "yes") {
    $pstmt = "select pnumber, ptype, pnumber, last4, pdate, amt from accountpayments where roid = ? and shopid = ?";
    if ($pquery = $conn->prepare($pstmt)) {
        $pquery->bind_param("is", $roid, $shopid);
        $pquery->execute();
        $pres = $pquery->get_result();
        $pquery->close();
    }
    if ($pres->num_rows > 0) {
        while ($payrs = $pres->fetch_assoc()) {
            $apprcode = "";
            $ctype = $payrs["ptype"];
            $ref = "";

            if (left($payrs["pnumber"], 3) == "360" && strpos($payrs["pnumber"], "~") !== false) {
                $ref = "";
                $payar = explode("~", $payrs["pnumber"]);
                $apprcode = $payar[count($payar) - 1];
                $check360 = $payar[0];
                if ($check360 == "360SYNC") {
                    $ctype = "Synchrony";
                } else {
                    if ($check360 == "360ECP") {
                        $ctype = "EasyPay";
                    } else {
                        if ($check360 == "360WISE") {
                            $ctype = "Wisetack";
                        } else {
                            if ($check360 == "360AFF") {
                                $ctype = "AFF";
                            }
                        }
                    }
                }
            } else {
                if ($payrs["pnumber"] != "") {
                    $ref = "REF # " . $payrs["pnumber"];
                }
            }

            $ptype = $ctype;
            if (!empty($apprcode)) {
                $ptype .= " - AuthCode " . $apprcode;
            }
            if (!empty($payrs["last4"])) {
                $ptype .= " - CC# " . $payrs["last4"];
            }
            $pdate = date('n/j/Y', strtotime($payrs['pdate']));
            $pamt = asDollars($payrs['amt']);
            $complaintsHTML .= <<<REVROWS
                    <tr>
                        <td class="border" style="">Payment Rec'd</td>
                        <td class="border" style="">
                        <table><tr>
                        <td style="text-align: left">$ptype</td>
                        <td style="text-align: right;">$ref</td>
                        </tr></table>
                        </td>
                        <td class="border" style="" colspan="2">Date : $pdate</td>
                        <td class="border" style="text-align: right">$pamt</td>
                    </tr>
                REVROWS;
        }//AC LOOP END
    }//AC ROW CHECK
} //END INDIVIDUAL PAYMENT

if ($printcommlog == "yes") {

    $cstmt = "select `datetime`,UCASE(`by`), UCASE(comm) from repairordercommhistory where roid = ? and shopid = ? AND comm!='Tech Story Updated' AND comm!='Advisor Comments Updated'";
    if ($cquery = $conn->prepare($cstmt)) {
        $cquery->bind_param("is", $roid, $shopid);
        $cquery->execute();
        $cquery->bind_result($datetime, $by, $comm);
        while ($cquery->fetch()) {
            $datetime = date('n/j/Y H:i', strtotime($datetime));
            $complaintsHTML .= <<<REVROWS
                    <tr>
                        <td class="border" style="">$datetime - $by</td>
                        <td class="border" colspan="4">$comm</td>
                    </tr>
                REVROWS;
        }
        $cquery->close();
    }
} //END IF PRINTCOMMLOG


if ($showOrigionalEstimate && doubleval($rs['origro']) > 0) {
    $ttlorigro = asDollars($rs['origro']);
    $complaintsHTML .= <<<REVROWS
                    <tr>
                        <td class="border" style="" colspan="4">ORIGINAL ESTIMATE:</td>
                        <td class="border" style="text-align: right">$ttlorigro</td>
                    </tr>
                REVROWS;
}

$totalRows = "";
$stmt = "select coalesce(sum(linettlprice),0) ttljob from parts where shopid = ? and roid = ? and deleted != 'yes' and partnumber = 'JOB'";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("si", $shopid, $roid);
    $query->execute();
    $query->bind_result($jobtotal);
    $query->fetch();
    $query->close();
}

$ttlparts = $rs['TotalPrts'] - $jobtotal;

$totalRows = '<tr><td>Labor/Jobs</td><td class="text-right">' . asDollars($rs['TotalLbr'] + $jobtotal) . '</td></tr>';
$totalRows .= '<tr><td>Parts</td><td class="text-right">' . asDollars($ttlparts) . '</td></tr>';
$totalRows .= '<tr><td>Sublet</td><td class="text-right">' . asDollars($rs['TotalSublet']) . '</td></tr>';
$totalRows .= '<tr><td>Total Fees</td><td class="text-right">' . asDollars($rs['TotalFees']) . '</td></tr>';
$totalRows .= '<tr><td>Subtotal</td><td class="text-right">' . asDollars($rs['Subtotal'] + $rs['TotalFees']) . '</td></tr>';
if (!empty($showCanTax)) {
    $totalRows .= $showCanTax;
} else {
    $totalRows .= '<tr><td>Tax @ ' . $rs['TaxRate'] . '%</td><td class="text-right">' . asDollars($rs['SalesTax']) . '</td></tr>';
}
//$totalRows .= '<td class="text-right">' . asDollars($rs['SalesTax']) . '</td></tr>';


$paystmt = "select COALESCE(sum(`amt`), 0) as payments from accountpayments where roid = ? and shopid = ?";
if ($query = $conn->prepare($paystmt)) {
    $query->bind_param("is", $roid, $shopid);
    $query->execute();
    $query->bind_result($otherpayments);
    $query->fetch();
    $query->close();
}
$customerpayments = $otherpayments + $rs["AmtPaid1"] + $rs["AmtPaid2"];
$totalrobalance = doubleval($rs["TotalRO"]) - doubleval($customerpayments);
$totalro = doubleval($rs["TotalRO"]);
$showdiscountthisro = "no";
if (doubleval($discount) > 0) {
    $showdiscountthisro = "yes";
    $totalRows .= '<tr><td>Discount</td><td class="text-right">' . asDollars($rs['DiscountAmt']) . '</td></tr>';
}
$totalRows .= '<tr style="' . $total_bg . '"><td>Total</td><td class="text-right">' . asDollars($totalro) . '</td></tr>';

if ($showpayments == "yes") {
    $totalRows .= '<tr><td>Payments</td><td class="text-right">' . asDollars($customerpayments) . '</td></tr>';

    if ($customertype == "NET 10" || $customertype == "NET 15" || $customertype == "NET 30" || $customertype == "NET 60" || $customertype == "NET 90") {
        $totalRows .= "<tr><td>Balance (" . $customertype . ")</td>";
    } else {
        $totalRows .= "<tr><td>Balance</td>";
    }
    $totalRows .= '<td class="text-right">' . asDollars($totalrobalance) . '</td></tr>';
}

$warrantyDisclousure = "";
if (strlen($warrdiscfromro) > 10) {
    $warrantyDisclousure = nl2br($warrdiscfromro);
} else {
    $warrantyDisclousure = nl2br($warrdisc);
}
$warrantyDisclousure = substr($warrantyDisclousure, 0, 900);

$warrantyDisclousure = mb_convert_encoding($warrantyDisclousure, 'UTF-8');

$recrepairs = substr($recrepairs, 0, 600);
$roDisclousure = "";

if (strlen($rodiscfromro) > 10) {
 //   $rodiscfromro = substr($rodiscfromro, 0, 650);
    $roDisclousure = nl2br($rodiscfromro) . "<br><b>The late payment charge is $20/day or 10% of the outstanding balance of the invoice, whichever is greater</b><br><br>X______________________________________________________________                      Date " . date('n/j/Y');
} else {
 //   $rodisc = substr($rodisc, 0, 650);
    $roDisclousure = nl2br($rodisc) . "<br><b>The late payment charge is $20/day or 10% of the outstanding balance of the invoice, whichever is greater</b><br><br>X______________________________________________________________                     Date " . date('n/j/Y');
}

$qrTable = <<<QRTABLE
    <table>
        <tr>
            <td style="text-align: left">
                <img src="qrcode.png" height="75" >
            </td>
        </tr>
        <tr>
            <td style="font-size: xx-small; line-height: 5pt">
              To leave a google review please scan the QR code
              <br>
            </td>
        </tr>
    </table>
QRTABLE;


$footerTable = <<<FOOTERTABLE
    <style>
        .text-right{
            text-align: right;
        }
    </style>
    <table style="font-size:small; border: 1px solid black;">
          <tr>
            <td style="width: 70%;">
                <table cellpadding="3" style="font-size: small">
                    <tr>
                        <td style="height: 50pt; border-bottom: 1px solid black;">Recommended Repairs: $recrepairs</td>
                    </tr>
                    <tr>
                        <td style="font-size: small; line-height: 100%">$warrantyDisclousure</td>
                    </tr>
                </table>
            </td>
            <td style="width: 30%">
                <table cellpadding="3" border="1" style="font-size: small; font-weight: 800">
                    $totalRows
                </table>
            </td>
          </tr>
    </table>
    <table>
          <tr>
            <td style="font-size: 7.5pt;"><p style="line-height: 7.5pt"><br>$roDisclousure</p></td>
          </tr>
    </table>
FOOTERTABLE;


$lineItemsTable = <<<EOD
<style>
   .border-bottom{
        border-bottom: 1px solid black;
   }
   .border-top{
        border-top: 1px solid black;
   }
   .border-left {
        border-left: 1px solid black;
   }
   .border-right{
        border-right: 1px solid black;
   }
   .border{
        border:1px solid black;
   }
</style>
<div style="font-size: xx-small">$lineval</div>
$tirePressureTable
<table id="lineitems" style="font-size: xx-small; line-height: 100%; font-weight: lighter; border-bottom: 1px solid black; width: 100%" cellpadding="3">
$complaintsHTML
</table>
EOD;

$pdf = new MYPDF(PDF_PAGE_ORIENTATION, 'pt', array(612, 780), true, 'UTF-8', false);

$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
$pdf->setHeaderData('', 0, '', $headerTable, array(0, 0, 0), array(0, 0, 0));
$pdf->footerHTML = $footerTable;


$pdf->setPrintHeader(true);
$pdf->setPrintFooter(true);
//$pdf->SetFont('arial', 'B', 20);
$pdf->setCellPaddings(0, 0, 0, 0);
$pdf->SetMargins(30, 150, 30);
$pdf->SetHeaderMargin(10);

$pdf->SetFooterMargin($footer_margin);

$pdf->SetAutoPageBreak(true, $footer_margin + 10);
// add a page
$pdf->AddPage();

$pdf->writeHTML(utf8_encode($lineItemsTable), true, false, true, false, '');


//$pdf->writeHTML($qrTable, true, false, true, false, '');

$pdf->lastPage();

$qr_footer_margin = $footer_margin + 80;

if ($pdf->GetY() > 495){
    $pdf->AddPage();
}

$pdf->setFooterMargin($qr_footer_margin);
$pdf->SetAutoPageBreak(true, $qr_footer_margin + 10);

$pdf->footerHTML = $qrTable.$footerTable;

//$pdf->Write(0, 'Example of independent Multicell() columns', '', 0, 'L', true, 0, false, false, 0);
$newfilename = "TP_".$shopid . "_" . $roid . "_" . date('Y') ."_" . date('n') . "_" .date('j'). "_" . date('H'). "_" . date('i') ."_" .date('s'). "_" .time(). ".pdf";

ob_end_clean();
$pdf->Output(dirname(__FILE__).DS.'temp'.DS.$newfilename, 'F');

echo "/".$shopid."/temp/".$newfilename;