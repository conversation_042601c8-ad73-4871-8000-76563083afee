<?php
require(CONNWOSHOPID);
include INTEGRATIONS_PATH . "/sbp_bucket/sbp_bucket.php";
$protocol = $_SERVER['SERVER_NAME'] == 'localhost' ? 'http' : 'https';
$urlstr = $_SERVER['REQUEST_URI'];

if (isset($_GET['shopid'])) {
    header("Location:https://www.shopboss.net");
    exit;
} else {
    $url = explode("?", $urlstr);
    $qs = $encodedstring = $url[1];

    if (strpos($qs, "&") !== false) {
        $qs = explode("&", $qs);
        $qs = $qs[0];
    }

    $decryptedData = decryptData($qs);

    parse_str($decryptedData, $parsedData);

    $shopid = isset($parsedData['shopid']) ? filter_var($parsedData['shopid'], FILTER_SANITIZE_STRING) : null;

    $roid = isset($parsedData['roid']) ? filter_var($parsedData['roid'], FILTER_SANITIZE_NUMBER_INT) : null;
}
if (!empty($shopid) && !empty($roid)) {
    $ascnt = 0;
// check for MSM
    $msmid = "";
    $stmt = "select password from apilogin where shopid = ? and companyname = 'myshopmanager'";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($msmid);
        $query->fetch();
        $query->close();
    }

    $asid = "";
    $stmt = "select asid from autoserveshop where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($asid);
        $query->fetch();
        $query->close();
    }


    $stmt = "select `ROID`,`CustomerID`,`Writer`,`DateIn`,`TimeIn`,`TaxRate`,`PurchaseOrderNumber`,`VehID`,`Customer`,`VehInfo`,`vehyear`,`vehmake`,`vehmodel`,`vehlicense`,`vehstate`,`VehLicNum`,`WrittenBy`,`Status`,`StatusDate`,`TotalLbrHrs`,`TotalLbr`,`TotalPrts`,`TotalSublet`,`TotalRO`,`CustomerAddress`,`CustomerCSZ`,`CustomerPhone`,`customercity`,`customerstate`,`customerzip`,`VehicleMiles`,`MilesOut`,`Vin`,`CustomerWork`,`MajorComplaint`,`DatePromised`,`Comments`,`DiscountAmt`,`DiscountPercent`,`WarrMos`,`WarrMiles`,`SalesTax`,`PartsCost`,`ROType`,`VehEngine`,`Cyl`,`VehTrans`,`HowPaid`,`AmtPaid1`,`CheckNum1`,`HowPaid2`,`AmtPaid2`,`CheckNum2`,`EstimateAmt`,`NoFollow`,`AccountPaid`,`HazardousWaste`,`Source`,`Rev1Amt`,`Rev1Date`,`Rev1Phone`,`Rev1Time`,`Rev1By`,`Rev2Amt`,"
        . "`Rev2Date`,`Rev2Phone`,`Rev2Time`,`Rev2By`,`CellPhone`,coalesce(`storagefee`,0),`cellprovider`,`UserFee1`,`UserFee2`,`UserFee3`,`userfee1label`,`userfee2label`,`userfee3label`,`UserFee1amount`,`UserFee2amount`,`UserFee3amount`,`userfee1percent`,`userfee2percent`,`userfee3percent`,`userfee1type`,`userfee2type`,`userfee3type`,`LastFirst`,`customerfirst`,`customerlast`,`DriveType`,`TotalFees`,`Fax`,`Subtotal`,`CB`,`DateInspection`,`DateAuthorization`,`DateParts`,`DateWork`,`DateInProcess`,`DateHold`,`DateFinal`,`DateDelivered`,`DateClosed`,`OrigRO`,`OrigTech`,`PartsOrdered`,`FinalDate`,`Exported`,`LaborTaxRate`,`SubletTaxRate`,`complainttable`,`gp`,`contact`,`balance`,`fleetno`,`email`,`recommendedrepairs`,`customvehicle1`,`customvehicle2`,`customvehicle3`,`customvehicle4`,`customvehicle5`,`customvehicle6`,"
        . "`customvehicle7`,`customvehicle8`,`customvehicle1label`,`customvehicle2label`,`customvehicle3label`,`customvehicle4label`,`customvehicle5label`,`customvehicle6label`,`customvehicle7label`,`customvehicle8label`,`tagnumber`,`qb`,`datetimepromised`,`spousename`,`spousework`,`spousecell`,`overridestatusdate`,`tirepressureinlf`,`tirepressureinrf`,`tirepressureinlr`,`tirepressureinrr`,`tirepressureoutlf`,`tirepressureoutrf`,`tirepressureoutlr`,`tirepressureoutrr`,`treaddepthlf`,`treaddepthrf`,`treaddepthlr`,`treaddepthrr`,`discounttaxable`,`ponumber`,`origshopid`,`inspectioncomplete`,`invoiceemailed`,`estimateemailed`,`inspectionemailed`,`updatesent`,`coupon`,`ts`,overridestatusdate,warrdisc,rodisc"
        . " from repairorders where shopid = ? and roid = ?";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $roid);
        $query->execute();
        $query->store_result();
        $num_roid_rows = $query->num_rows;
        //if no rows are there, these variables are left unassigned. without num_rows these variables are assigned NULL values.
        $query->bind_result($ROID, $CustomerID, $Writer, $DateIn, $TimeIn, $TaxRate, $PurchaseOrderNumber, $VehID, $Customer, $VehInfo, $vehyear, $vehmake, $vehmodel, $vehlicense, $vehstate, $VehLicNum, $WrittenBy, $Status, $StatusDate, $TotalLbrHrs, $TotalLbr, $TotalPrts, $TotalSublet, $TotalRO, $CustomerAddress, $CustomerCSZ, $CustomerPhone, $customercity, $customerstate, $customerzip, $VehicleMiles, $MilesOut, $Vin, $CustomerWork, $MajorComplaint, $DatePromised, $Comments, $DiscountAmt, $DiscountPercent, $WarrMos, $WarrMiles, $SalesTax, $PartsCost, $ROType, $VehEngine, $Cyl, $VehTrans, $HowPaid, $AmtPaid1, $CheckNum1, $HowPaid2, $AmtPaid2, $CheckNum2, $EstimateAmt, $NoFollow, $AccountPaid, $HazardousWaste, $Source, $Rev1Amt, $Rev1Date, $Rev1Phone, $Rev1Time, $Rev1By, $Rev2Amt, $Rev2Date, $Rev2Phone, $Rev2Time, $Rev2By, $CellPhone, $storagefee, $cellprovider, $UserFee1, $UserFee2, $UserFee3, $userfee1label, $userfee2label, $userfee3label, $UserFee1amount, $UserFee2amount, $UserFee3amount, $userfee1percent, $userfee2percent, $userfee3percent, $userfee1type, $userfee2type, $userfee3type, $LastFirst, $customerfirst, $customerlast, $DriveType, $TotalFees, $Fax, $Subtotal, $CB, $DateInspection, $DateAuthorization, $DateParts, $DateWork, $DateInProcess, $DateHold, $DateFinal, $DateDelivered, $DateClosed, $OrigRO, $OrigTech, $PartsOrdered, $FinalDate, $Exported, $LaborTaxRate, $SubletTaxRate, $complainttable, $gp, $contact, $balance, $fleetno, $email, $recommendedrepairs, $customvehicle1, $customvehicle2, $customvehicle3, $customvehicle4, $customvehicle5, $customvehicle6, $customvehicle7, $customvehicle8, $customvehicle1label, $customvehicle2label, $customvehicle3label, $customvehicle4label, $customvehicle5label, $customvehicle6label, $customvehicle7label, $customvehicle8label, $tagnumber, $qb, $datetimepromised, $spousename, $spousework, $spousecell, $overridestatusdate, $tirepressureinlf, $tirepressureinrf, $tirepressureinlr, $tirepressureinrr, $tirepressureoutlf, $tirepressureoutrf, $tirepressureoutlr, $tirepressureoutrr, $treaddepthlf, $treaddepthrf, $treaddepthlr, $treaddepthrr, $discounttaxable, $ponumber, $origshopid, $inspectioncomplete, $invoiceemailed, $estimateemailed, $inspectionemailed, $updatesent, $coupon, $ts, $overridestatusdate, $warrdisc, $rodisc);
        $query->fetch();
        $rofetch = "none";
        $rofetch = $conn->error;

        $query->close();
    } else {
        // echo "Prepare failed: (" . $conn->errno . ") " . $conn->error;
    }
    // get company information
    $cstmt = "select terminalid,merchantaccount,showinstockparts,definvmsgemail,companyname,companyphone,chargeshopfeeson,userfee1,userfee2,userfee3,userfee1amount,userfee2amount,userfee3amount,userfee1max"
        . ",userfee2max,userfee3max,userfee1taxable,userfee2taxable,userfee3taxable,requirebalancero,requirepayments,requiresource,requireoutmileage,rodisclosure,rowarrdisclosure,alldatausername"
        . ",alldatapassword,estguide,milesinlabel,milesoutlabel,replacerowithtag,merchantaccount,merchantid,merchantpassword,inspectionaspopup,alldatarepair,requiretechclockout,showgp,hazwastetaxable"
        . ",fullscreenissues,estimatetitle,invoicetitle,companyemail,scrolltotalswindow,hst,pst,gst,qst,chargehst,chargepst,chargegst,chargeqst,cfpid,nexpartusername,showpartsoninvoice,matco from company where shopid = ?";
//echo str_replace('?',"'".$shopid."'",$cstmt);
    if ($cquery = $conn->prepare($cstmt)) {
        $cquery->bind_param("s", $shopid);
        $cquery->execute();
        $cquery->store_result();
        $cquery->bind_result($terminalid, $merchantaccount, $showinstockparts, $definvmsgemail, $shopname, $shopphone, $chargeshopfeeson, $userfee1, $userfee2, $userfee3, $userfee1amount, $userfee2amount, $userfee3amount, $userfee1max, $userfee2max, $userfee3max, $userfee1taxable, $userfee2taxable, $userfee3taxable, $requirebalancero, $requirepayments, $requiresource, $requireoutmileage, $rodisclosure, $rowarrdisclosure, $alldatausername, $alldatapassword, $estguide, $milesinlabel, $milesoutlabel, $replacerowithtag, $merchantaccount, $merchantid, $merchantpassword, $inspectionaspopup, $alldatarepair, $requiretechclockout, $showgp, $hazwastetaxable, $fullscreenissues, $estimatetitle, $invoicetitle, $companyemail, $scrolltotalswindow, $hst, $pst, $gst, $qst, $chargehst, $chargepst, $chargegst, $chargeqst, $cfpid, $printprices, $showpartsoninvoice, $matco);
        $cquery->fetch();
        $companyfetch = "none";
    } else {
        $companyfetch = $conn->error;
    }

    $stmt = "select showcfp,paymentonfinal,printscanresults from settings where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($showcfp, $paymentonfinal, $printscanresults);
        $query->fetch();
        $query->close();
    }

    $diagnose_record_id = '';

    if (strtolower($matco) == 'yes') {
        $stmt = "select diagnose_record_id from matco_records where shopid = ? and roid = ?";
        if ($query = $conn->prepare($stmt)) {
            $query->bind_param("si", $shopid, $roid);
            $query->execute();
            $query->bind_result($diagnose_record_id);
            $query->fetch();
            $query->close();
        }
    }

    $stmt = "SELECT emailinvoice,emailinvoicesubject,emailestimate,emailestimatesubject,emailesigrequest,emailesigrequestsubject from companymessages WHERE shopid = '$shopid'";
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $query->store_result();
        $nummsgrows = $query->num_rows;
        if ($nummsgrows > 0) {
            $query->free_result();
            $query->execute();
            $query->bind_result($emailinvoice, $emailinvoicesubject, $emailestimate, $emailestimatesubject, $emailesigrequest, $emailesigrequestsubject);
            $query->fetch();
        } else {
            $emailinvoice = str_replace("[shopname]", $shopname, $definvmsgemail);
            $emailinvoicesubject = 'Your repair invoice from ' . $shopname;
            $emailestimate = str_replace("[shopname]", $shopname, $definvmsgemail);
            $emailestimatesubject = 'Your repair estimate from ' . $shopname;
            $emailesigrequest = $shopname . ' is requesting an E-Signature on a document. Please click the link below to E-Sign the document. Thank you for your business!';
            $emailesigrequestsubject = 'A Request for an E-Signature from ' . $shopname;
        }
        $query->close();
    }

    $cantaxrate = 0;
    $cantaxstr = "";
    $taxexemptstr = "";
    if ($chargehst == "yes" && $hst > 0 && $taxexemptstr != "<span style='font-weight:bold;color:red;'>TAX EXEMPT</span>") {
        $cantaxrate = $cantaxrate + $hst;
        $hststr = "HST";
    } else {
        $hststr = "";
    }
    if ($chargegst == "yes" && $gst > 0 && $taxexemptstr != "<span style='font-weight:bold;color:red;'>TAX EXEMPT</span>") {
        $cantaxrate = $cantaxrate + $gst;
        $gststr = "GST";
    } else {
        $gststr = "";
    }

    if ($chargepst == "yes" && $pst > 0 && $taxexemptstr != "<span style='font-weight:bold;color:red;'>TAX EXEMPT</span>") {
        $cantaxrate = $cantaxrate + $pst;
        $pststr = "PST";
    } else {
        $pststr = "";
    }

    if ($chargeqst == "yes" && $qst > 0 && $taxexemptstr != "<span style='font-weight:bold;color:red;'>TAX EXEMPT</span>") {
        $cantaxrate = $cantaxrate + $qst;
        $qststr = "QST";
    } else {
        $qststr = "";
    }

    if (strlen($hststr) > 0) {
        $cantaxstr .= $hststr;
    }

    if (strlen($gststr) > 0) {
        if (strlen($cantaxstr) > 0) {
            $cantaxstr .= "+" . $gststr;
        } else {
            $cantaxstr .= $gststr;
        }
    } else {
        $cantaxstr .= $gststr;
    }

    if (strlen($pststr) > 0) {
        if (strlen($cantaxstr) > 0) {
            $cantaxstr .= "+" . $pststr;
        } else {
            $cantaxstr .= $pststr;
        }
    } else {
        $cantaxstr .= $pststr;
    }

    if (strlen($qststr) > 0) {
        if (strlen($cantaxstr) > 0) {
            $cantaxstr .= "+" . $qststr;
        } else {
            $cantaxstr .= $qststr;
        }
    } else {
        $cantaxstr .= $qststr;
    }

    if ($cantaxrate > 0) {
        $TaxRate = $cantaxrate;
        $LaborTaxRate = $cantaxrate;
        $SubletTaxRate = $cantaxrate;
        $userfee1taxable = "taxable";
        $userfee2taxable = "taxable";
        $userfee3taxable = "taxable";
        $hazwastetaxable = "yes";
    }

    $flagstr = "";
    if (strlen($cantaxstr) > 0) {
        $cantaxstr = "<b>(" . $cantaxstr . ")</b>";
        $flagstr = "<img style='max-width:50px' src='../assets/img/canadianflag.png'> ";
    }


    $totalparts = 0;
    $totallabor = 0;
    $totalsublet = 0;
    $partlinediscount = 0;
    $laborlinediscount = 0;

    $laborstmt = "select coalesce(sum(linetotal),0) tlabor, coalesce(sum(laborhours),0) tlaborhours from labor where shopid = '$shopid' and roid = $roid and deleted = 'no'";
//echo $laborstmt."\r\n";
    if ($lquery = $conn->prepare($laborstmt)) {
        //$lquery->bind_param("si",$shopid,$roid);
        $lquery->execute();
        $lquery->store_result();
        $lquery->bind_result($totallabor, $totallaborhours);
        $lquery->fetch();
        $laborfetch = "none";
    } else {
        $laborfetch = $conn->error;
    }
//echo $totallabor."\r\n";
    $partstmt = "select coalesce(sum(linettlprice),0) tprts, coalesce(sum(linettlcost),0) tprtscost from parts where shopid = '$shopid' and roid = $roid and deleted = 'no'";
//echo $partstmt."\r\n";
    if ($pquery = $conn->prepare($partstmt)) {
        $pquery->execute();
        $pquery->store_result();
        $pquery->bind_result($totalparts, $totalpartscost);
        $pquery->fetch();
        $allpartsfetch = "none";
    } else {
        $allpartsfetch = $conn->error;
    }

//echo $totalpartscost."\r\n";

    $partstmt = "select coalesce(sum(linettlprice),0) tprts from parts where tax = 'yes' and shopid = '$shopid' and roid = $roid and deleted = 'no'";
//echo "parts stmt:".$partstmt."\r\n";
    $taxableparts = 0;
    if ($pquery = $conn->prepare($partstmt)) {
        $pquery->execute();
        $pquery->store_result();
        $pquery->bind_result($taxableparts);
        $pquery->fetch();
        $taxpartsfetch = "none";
    } else {
        $taxpartsfetch = $conn->error;
    }

    $substmt = "select coalesce(sum(subletprice),0) tsub from sublet where shopid = ? and roid = ? and deleted = 'no'";
    if ($squery = $conn->prepare($substmt)) {
        $squery->bind_param("si", $shopid, $roid);
        $squery->execute();
        $squery->store_result();
        $squery->bind_result($totalsublet);
        $squery->fetch();
        $subletfetch = "none";
    } else {
        $subletfetch = $conn->error;
    }

    $totalpayments = 0;
    $pmtstmt = "select coalesce(sum(amt),0) tamt from accountpayments where shopid = ? and roid = ?";
    if ($squery = $conn->prepare($pmtstmt)) {
        $squery->bind_param("si", $shopid, $roid);
        $squery->execute();
        $squery->store_result();
        $squery->bind_result($totalpayments);
        $squery->fetch();
        $pmtfetch = "none";
    } else {
        $pmtfetch = $conn->error;
    }
//echo "totalpayments:".$totalpayments."\r\n";
//$LaborTaxRate,$SubletTaxRate
//echo $totalpartscost."-185\r\n";
    $rawlabor = $totallabor;
    $rawparts = $totalparts;
    $rawsublet = $totalsublet;

    $totallabor = sbpround($totallabor, 2);
    $totalparts = sbpround($totalparts, 2);
    $totalsublet = sbpround($totalsublet, 2);

// check for fees with percentages to calculate
    $chargeshopfeeson = strtolower($chargeshopfeeson);
    /*echo "type:".$userfee1type."|".$userfee2type."|".$userfee3type."\r\n";
    echo "fee:".$UserFee1."|".$UserFee2."|".$UserFee3."\r\n";
    echo "amt:".$userfee1amount."|".$userfee2amount."|".$userfee3amount."\r\n";
    echo "percent:".$userfee1percent."|".$userfee2percent."|".$userfee3percent."\r\n";*/

    if (!empty($userfee1type) && ($userfee1type == '%' && $userfee1percent > 0)) {
        if ($chargeshopfeeson == "all") {
            $UserFee1 = ($totallabor + $totalparts + $totalsublet) * ($userfee1amount / 100);
        } else {
            if ($chargeshopfeeson == "labor") {
                $UserFee1 = ($totallabor) * ($userfee1amount / 100);
                //echo $totallabor.":".$userfee1amount.":".$UserFee1."\r\n";
            }
        }
    }
//echo $userfee2type."|".$userfee2percent."\r\n";
    if ($userfee2type == '%' && $userfee2percent > 0) {
        //echo $shopid."|".$UserFee2."|".$userfee2amount."\r\n";
        if ($chargeshopfeeson == "all") {
            $UserFee2 = ($totallabor + $totalparts + $totalsublet) * ($userfee2amount / 100);
        } else {
            if ($chargeshopfeeson == "labor") {
                $UserFee2 = ($totallabor) * ($userfee2amount / 100);
                //echo $totallabor.":".$userfee2amount.":".$UserFee2."\r\n";
            }
        }
        // custom for shop 1734
        if ($shopid == "1734") {
            $UserFee2 = ($totalparts) * ($userfee2amount / 100);
            //echo $totalparts.":".$userfee2amount.":".$UserFee2."\r\n";
        }
    }
    if ($userfee3type == '%' && $userfee3percent > 0) {
        if ($chargeshopfeeson == "all") {
            $UserFee3 = ($totallabor + $totalparts + $totalsublet) * ($userfee3amount / 100);
        } else {
            if ($chargeshopfeeson == "labor") {
                $UserFee3 = ($totallabor) * ($userfee3amount / 100);
            }
        }
    }

    if ($userfee1max < $UserFee1 && $userfee1max > 0) {
        $UserFee1 = $userfee1max;
    }
    if ($userfee2max < $UserFee2 && $userfee2max > 0) {
        $UserFee2 = $userfee2max;
    }
    if ($userfee3max < $UserFee3 && $userfee3max > 0) {
        $UserFee3 = $userfee3max;
    }
//echo $totalpartscost."-229\r\n";

// calculate the sales tax
    $userfee1tax = 0;
    $userfee2tax = 0;
    $userfee3tax = 0;
    $userfee1taxamount = 0;
    $userfee2taxamount = 0;
    $userfee3taxamount = 0;
    $hazwastetaxamount = 0;
    $rawuserfee1tax = 0;
    $rawuserfee2tax = 0;
    $rawuserfee3tax = 0;
    $rawhazwastetax = 0;

    if ($UserFee1 > 0 && strtolower($userfee1taxable) == "taxable") {
        $userfee1tax = sbpround(($TaxRate / 100) * sbpround($UserFee1, 2));
        $rawuserfee1tax = ($TaxRate / 100) * $UserFee1;
        $userfee1taxamount = $UserFee1;
        //echo $rawuserfee1tax."\r\n";
    }
    if ($UserFee2 > 0 && strtolower($userfee2taxable) == "taxable") {
        $userfee2tax = sbpround(($TaxRate / 100) * sbpround($UserFee2, 2));
        $rawuserfee2tax = ($TaxRate / 100) * $UserFee2;
        $userfee2taxamount = $UserFee2;
    }
    if ($UserFee3 > 0 && strtolower($userfee3taxable) == "taxable") {
        $userfee3tax = sbpround(($TaxRate / 100) * sbpround($UserFee3, 2));
        $rawuserfee3tax = ($TaxRate / 100) * $UserFee3;
        $userfee3taxamount = $UserFee3;
    }
//echo strtolower($hazwastetaxable);
    if (strtolower($hazwastetaxable) == "yes") {
        $hazwastetax = sbpround(($TaxRate / 100) * $HazardousWaste, 2);
        $rawhazwastetax = ($TaxRate / 100) * $HazardousWaste;
        $hazwastetaxamount = $HazardousWaste;
    } else {
        $hazwastetax = 0.00;
    }
//echo $userfee1tax.":".$userfee2tax.":".$userfee3tax.":".$hazwastetax."\r\n";
//echo sbpround($userfee1tax).":".sbpround($userfee2tax).":".sbpround($userfee3tax).":".sbpround($hazwastetax)."\r\n";
    $totaltax = 0;
    $labortax = 0;
    $partstax = 0;
    $sublettax = 0;
    $totalro = 0;
    $rawlabortax = $totallabor * ($LaborTaxRate / 100);
    if ($cantaxrate > 0) {
        $rawpartstax = $taxableparts * ($TaxRate / 100);
    } else {
        $rawpartstax = $totalparts * ($TaxRate / 100);
        //echo $rawpartstax."\r\n";
    }
    $rawsublettax = $totalsublet * ($SubletTaxRate / 100);
//echo "raw taxes: ".$rawlabortax." | ".$rawpartstax." | ".$rawsublettax." | ".$rawuserfee1tax." | ".$rawuserfee2tax." | ".$rawuserfee3tax." | ".$rawhazwastetax."\r\n";

    $labortax = sbpround(sbpround($totallabor) * ($LaborTaxRate / 100));

    if ($cantaxrate > 0) {
        $totaltaxableamount = $taxableparts + $userfee1taxamount + $userfee2taxamount + $userfee3taxamount + $hazwastetaxamount;
        $partstax = round(round($totaltaxableamount, 2) * ($TaxRate / 100), 2);
    } else {
        $partstax = sbpround(sbpround($taxableparts) * ($TaxRate / 100));
        $totaltaxableamount = $taxableparts + $userfee1taxamount + $userfee2taxamount + $userfee3taxamount + $hazwastetaxamount;
        //echo $totaltaxableamount."\r\n";
        //echo round(round($totaltaxableamount,2) * ($TaxRate/100),2)."\r\n";
        $partstax = round(round($totaltaxableamount, 2) * ($TaxRate / 100), 2);
        //echo sbpround($taxableparts)."|".$partstax."\r\n";
    }


//echo $partstax."\r\n";
    $sublettax = sbpround(sbpround($totalsublet) * ($SubletTaxRate / 100));
    $totaltax = sbpround($userfee1tax) + sbpround($userfee2tax) + sbpround($userfee3tax) + sbpround($labortax) + sbpround($partstax) + sbpround($sublettax) + sbpround($hazwastetax, 2);
    $totaltax = round($labortax, 2) + round($partstax, 2) + round($sublettax, 2);
//echo $testtotaltax."\r\n";

    $totalfees = sbpround($UserFee1 + $UserFee2 + $UserFee3 + $HazardousWaste + $storagefee, 2);
    $subtotal = sbpround($totallabor + $totalparts + $totalsublet + $totalfees, 2);
    $subtotalfordb = $subtotal - $totalfees;
    $afterdiscount = sbpround($subtotal, 2) - sbpround($DiscountAmt, 2);
    $totalpayments = sbpround($totalpayments, 2);


// This is not needed because you are pulling from the RO in line 539
//$totalro = $subtotal + $totaltax - round($DiscountAmt,2);

    $totalpayments = 0;
    $pmtstmt = "select coalesce(sum(amt),0) tamt from accountpayments where shopid = ? and roid = ?";
    if ($squery = $conn->prepare($pmtstmt)) {
        $squery->bind_param("si", $shopid, $roid);
        $squery->execute();
        $squery->store_result();
        $squery->bind_result($totalpayments);
        $squery->fetch();
    }
// moving this down to line 552
//$balancero = $totalro - $totalpayments;

// Why do you need to update?
    /*
    $roupdatestmt = "update repairorders set taxrate = $TaxRate, labortaxrate = $LaborTaxRate, sublettaxrate = $SubletTaxRate, totallbrhrs = $totallaborhours,totallbr = $totallabor,totalprts = $totalparts,totalsublet = $totalsublet,"
    ."subtotal = $subtotalfordb,totalro = $totalro,totalfees = $totalfees,salestax = $totaltax,partscost = $totalpartscost,userfee1 = $UserFee1, userfee2 = $UserFee2, userfee3 = $UserFee3,"
    ." balance = $balancero where shopid = '$shopid' and roid = $roid";
    //echo $roupdatestmt."\r\n";
    if ($query = $conn->prepare($roupdatestmt)){
        if ($query->execute()){
            $conn->commit();
            $roupdate = "none";
        }else{
            $roupdate = $conn->error;
        }

    }else{
        echo "Update RO Failed: (" . $conn->errno . ") " . $conn->error;
    }

    */

    $truncomttl = 0;
    $stmt = "select complaint,techreport,scanreport,complaintid,acceptdecline,locked from complaints where shopid = ? and roid = ? and cstatus = 'no' order by displayorder";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $roid);
        $query->execute();
        $result = $query->get_result();
        $issues = array();
        while ($row = $result->fetch_array()) {
            $runcomttl = 0;

            $issue = array(
                'id' => $row['complaintid'],
                'status' => strtolower($row['acceptdecline']),
                'complaint' => strtoupper(iconv('UTF-8', 'ASCII//TRANSLIT', $row['complaint'])),
                'techReport' => iconv('UTF-8', 'ASCII//TRANSLIT', $row['techreport']),
                'scanReport' => iconv('UTF-8', 'ASCII//TRANSLIT', $row['scanreport']),
                'isLocked' => $row['locked']
            );

            $parts = [];

            $pstmt = "select partdesc,partprice,quantity,linettlprice,sort_order from parts where shopid = ? and roid = ? and complaintid = ?";
            if ($pquery = $conn->prepare($pstmt)) {
                $parts = array();
                $pquery->bind_param("sii", $shopid, $roid, $row['complaintid']);
                $pquery->execute();
                $presult = $pquery->get_result();

                while ($prow = $presult->fetch_array()) {
                    $runcomttl += $prow['linettlprice'];
                    $truncomttl += $prow['linettlprice'];
                    $part = array(
                        'type' => 'parts',
                        'description' => strtoupper(iconv('UTF-8', 'ASCII//TRANSLIT', $prow['partdesc'])),
                        'quantity' => $prow['quantity'],
                        'ttlprice' => number_format($prow['linettlprice'], 2),
                        'sort_order' => (int)$prow['sort_order']
                    );
                    array_push($parts, $part);
                }
            }

            $labors = [];

            $lstmt = "select labor, linetotal, sort_order from labor where shopid = ? and roid = ? and complaintid = ?";
            if ($lquery = $conn->prepare($lstmt)) {
                $labors = array();
                $lquery->bind_param("sii", $shopid, $roid, $row['complaintid']);
                $lquery->execute();
                $lresult = $lquery->get_result();
                while ($lrow = $lresult->fetch_array()) {
                    $runcomttl += $lrow['linetotal'];
                    $truncomttl += $lrow['linetotal'];
                    $labor = array(
                        'type' => 'labor',
                        'description' => strtoupper(preg_replace('/[\x00-\x1F\x7F-\xFF]/', '', $lrow['labor'])),
                        'ttlprice' => number_format($lrow['linetotal'], 2),
                        'sort_order' => (int)$lrow['sort_order']
                    );
                    array_push($labors, $labor);
                }
            }

            $sublets = [];

            $sstmt = "select subletdesc,subletprice,sort_order from sublet where shopid = ? and roid = ? and complaintid = ?";
            if ($squery = $conn->prepare($sstmt)) {
                $sublets = array();
                $squery->bind_param("sii", $shopid, $roid, $row['complaintid']);
                $squery->execute();
                $sresult = $squery->get_result();
                while ($srow = $sresult->fetch_array()) {
                    $runcomttl += $srow['subletprice'];
                    $truncomttl += $srow['subletprice'];
                    $sublet = array(
                        'type' => 'sublet',
                        'description' => strtoupper($srow['subletdesc']),
                        'ttlprice' => number_format($srow['subletprice'], 2),
                        'sort_order' => (int)$srow['sort_order']
                    );
                    array_push($sublets, $sublet);
                }
            }

            $issueitems = array_merge($parts, $labors, $sublets);

            foreach ($issueitems as $index => &$item) {
                $item['_original_index'] = $index;
            }
            unset($item);

            usort($issueitems, function($a, $b) {
                $cmp = $a['sort_order'] <=> $b['sort_order'];
                if ($cmp === 0) {
                    return $a['_original_index'] <=> $b['_original_index'];
                }
                return $cmp;
            });

            $issue['items'] = $issueitems;

            $issue['ttlprice'] = $runcomttl;
            array_push($issues, $issue);
        }


    }
}

?>
<!DOCTYPE html>
<html>

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <meta name="robots" content="noindex, nofollow">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">

    <title>Current Status</title>
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.11.2/css/all.css"/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap"/>

    <link rel="stylesheet" href="<?= MDB ?>/css/mdb.min.css" type="text/css">
    <link rel="stylesheet" href="<?= CSS ?>/status.css" type="text/css">
</head>

<body>
<?php include(COMPONENTS_PRIVATE_PATH . "/shared/analytics.php"); ?>
<?php
if (!empty($shopid) && !empty($roid)) {
    $stmt = "select logo,printtechstory,companyname,companyaddress,companycity,companystate,companyzip,companyphone,companyemail from company where shopid = ?";
    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("s", $shopid);
        $query->execute();
        $query->bind_result($logo, $printtechstory, $companyname, $companyaddress, $companycity, $companystate, $companyzip, $companyphone, $companyemail);
        $query->fetch();
        $query->close();

    }


    $stmt = "select customerfirst,customerlast,customeraddress,customercity,customerstate,customerzip,customerphone,customerwork,cellphone,email,vehinfo,status,vin,vehlicense,totalfees,salestax,totalro,upper(cb) from repairorders"
        . " where shopid = ? and roid = ?";

    if ($query = $conn->prepare($stmt)) {
        $query->bind_param("si", $shopid, $roid);
        $query->execute();
        $query->bind_result($customerfirst, $customerlast, $customeraddress, $customercity, $customerstate, $customerzip, $customerphone, $customerwork, $customercell, $email, $vehinfo, $status, $vin, $vehlicense, $totalfees, $salestax, $totalro, $disclabel);
        $query->fetch();
        $query->close();

    }

    if (!empty($disclabel) && $disclabel != 'NONE') $discountlabel = $disclabel; else $discountlabel = '';


    $balancero = $totalro - $totalpayments;

    if (strtolower($status) != "final" && strtolower($status) != "closed") {
        $status = strtoupper(substr($status, 1, strlen($status) - 1));
    } else {
        $status = strtoupper($status);
    }

    $logo_url = $protocol . '://' . $_SERVER['SERVER_NAME'] . '/sbp/upload/' . $shopid . '/' . $logo;
    $cfplender = '';

    if ($showcfp == 'yes' && !empty($cfpid)) {
        $cfpphone = $cfpaltphone = '';
        $phones = array($customercell, $customerphone, $spousecell, $spousework);
        foreach ($phones as $phone) {
            if (!empty($phone)) {
                if (empty($cfpphone)) $cfpphone = $phone;
                elseif ($phone != $cfpphone && empty($cfpaltphone)) $cfpaltphone = $phone;
            }
        }

        $data = array("merchantId" => $cfpid, "phone" => $cfpphone, "altphone" => $cfpaltphone, "zip" => $customerzip, "firstName" => $customerfirst, "lastName" => $customerlast, "street" => $customeraddress, "state" => $customerstate, "city" => $customercity, "email" => $email, "middle" => '', 'amount' => $balancero);

        if (in_array($shopid, array('13846', '6062'))) {
            $data["partnerKey"] = "SBPS9teaU";
            $url = "https://us-central1-paymentsdev.cloudfunctions.net/velox/remotelend?" . http_build_query($data); //sandbox
        } else {
            $data["partnerKey"] = "Sb24F110";
            $url = "https://us-central1-payments360-214018.cloudfunctions.net/velox/remotelend?" . http_build_query($data);
        }

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_URL, $url);
        $result = curl_exec($ch);
        curl_close($ch);

        $cfpresult = json_decode($result);

        $cfplender = $cfpresult->lenderName;

        if (empty($cfplender)) {
            if (stripos($cfpresult->result, 'Payment Plans Available') !== false || stripos($cfpresult->result, 'Payments as low') !== false) {
                preg_match_all('#\bhttps?://[^,\s()<>]+(?:\([\w\d]+\)|([^,[:punct:]\s]|/))#', $cfpresult->result, $match);
                $href = $match[0][0] ?? '';
                if (!empty($href))
                    $cfplink = '<sbp-button id="<btn-id>" size="block" v-cloak color="primary" icon="credit-card" href="' . $href . '">Payment Plans Available. Click to Apply</sbp-button>';
            } else {
                $cfplink = '<sbp-button  id="<btn-id>" size="block" v-cloak color="primary" icon="credit-card" @click="$refs[\'financing-modal\'].open()">' . $cfpresult->result . '</sbp-button>';
            }

            $cfplender = 'Synchrony';

        } elseif (isset($cfpresult->applyUrl) && !empty($cfpresult->applyUrl)) {
            $href = $cfpresult->applyUrl;
            $cfplink = '<sbp-button  size="block"  id="<btn-id>" v-cloak color="primary" icon="credit-card" href="' . $href . '">' . $cfpresult->result . ' Click to Apply</sbp-button>';
        } else {
            $cfplink = '<sbp-button  size="block" id="<btn-id>" v-cloak color="primary" icon="credit-card" @click="$refs[\'financing-modal\'].open()">' . $cfpresult->result . '</sbp-button>';
        }

    } else {
        $cfplink = '';
    }
    ?>

    <div id="app">
        <div class="container">
            <sbp-button-container class="mt-4">
                <?php
                $inspections = array();
                $stmt = "select distinct inspectionname from roinspectionheader where shopid = '$shopid' and roid = $roid";
                if ($query = $conn->prepare($stmt)) {
                    $query->execute();
                    $result = $query->get_result();
                    while ($row = $result->fetch_array()) {
                        $inspections[] = array('name' => $row['inspectionname'], 'type' => 'classic');
                        ?>
                        <div class="row mr-md-1">
                            <div class="col-12">
                                <sbp-button size="block" v-cloak color="success" icon="file-alt"
                                            @click="showInspection(<?= htmlspecialchars(json_encode($row['inspectionname']), ENT_QUOTES) ?>)">
                                    <?php echo $row['inspectionname']; ?> Inspection
                                </sbp-button>
                            </div>
                        </div>
                        <?php
                    }
                }

                $stmt = "select d.id,m.name as inspectionname from dvi d,inspection_models m where d.shopid=m.shopid and d.model_id=m.id and d.shopid = '$shopid' and d.roid = $roid and d.estimate_complete='1'";
                if ($query = $conn->prepare($stmt)) {
                    $query->execute();
                    $result = $query->get_result();
                    while ($row = $result->fetch_array()) {
                        $inspections[] = array('name' => $row['inspectionname'], 'type' => 'newdvi', 'param' => encryptData("shopid=" . $shopid . "&dviid=" . $row['id']));
                        ?>
                        <div class="row mr-md-1">
                            <div class="col-12">
                                <sbp-button v-cloak color="success" icon="file-alt" size="block"
                                            href="dvi_report.php?<?= encryptData("shopid=" . $shopid . "&dviid=" . $row['id']) ?>">
                                    <?php echo $row['inspectionname']; ?> Inspection
                                </sbp-button>
                            </div>
                        </div>
                        <?php
                    }
                }

                if ($msmid != "") {
                    // get a list of open inspections
                    $url = "https://myshopmanager.com/api/inspections?filters[repairorders.external]=$roid";
                    $ch = curl_init();

                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
                    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);

                    $headers = array();
                    $headers[] = 'Content-Type: application/json';
                    $headers[] = "X-authorization: $msmid";
                    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

                    $result = curl_exec($ch);
                    //echo $result;
                    if (curl_errno($ch)) {
                        echo 'Error:' . curl_error($ch);
                    }
                    curl_close($ch);
                    $json = json_decode($result);
                    $data = $json->data;
                    foreach ($data as $v) {
                        $id = $v->id;
                        $name = $v->sheet->name;
                        $url = $v->url_external;
                        $inspections[] = array('name' => $name, 'type' => 'MSM', 'param' => $url);
                        ?>
                        <div class="row mr-md-1">
                            <div class="col-12">
                                <sbp-button v-cloak color="success" icon="file-alt" size="block"
                                            @click="openMSMInspection('<?php echo $url; ?>')">
                                    <?php echo $name; ?>
                                </sbp-button>
                            </div>
                        </div>
                        <?php
                    }
                }

                // now get AS Inspections
                if ($asid != "") {
                    $stmt = "select id,roid,ordernumber,asid,inspname from autoserveinspections where shopid = ? and roid = ?";
                    if ($query = $conn->prepare($stmt)) {
                        $query->bind_param("si", $shopid, $roid);
                        $query->execute();
                        $r = $query->get_result();
                        while ($rs = $r->fetch_assoc()) {
                            $url = "https://app.autoserve1.com/$asid/" . $rs['asid'];
                            $inspections[] = array('name' => $rs['inspname'], 'type' => 'AS1', 'param' => $rs['asid']);
                            ?>
                            <div class="row mr-md-1">
                                <div class="col-12">
                                    <sbp-button v-cloak color="success" icon="file-alt" size="block"
                                                @click="openASInspection('<?php echo $rs['asid']; ?>')">
                                        <?php echo $rs['inspname']; ?>
                                    </sbp-button>
                                </div>
                            </div>
                            <?php
                        }
                    }
                }
                ?>
                <div class="row mr-md-1">
                    <div class="col-sm">
                        <sbp-button v-cloak color="info" icon="file-alt" size="block" @click="showInvoice">View Invoice
                        </sbp-button>
                    </div>
                </div>
                <?php
                if (!empty($diagnose_record_id)) {
                    ?>

                    <div class="row mr-md-1">
                        <div class="col-sm">
                            <sbp-button v-cloak color="primary" icon="file-alt" size="block" @click="showDiagnosticReport">Diagnostic Report
                            </sbp-button>
                        </div>
                    </div>

                <?php }

                if (
                    (
                        $merchantaccount == "cardknox"
                        || $merchantaccount == "authorize.net"
                        || $merchantaccount == "stax"
                        || ($merchantaccount == "360" && !empty($terminalid))
                        || ($merchantaccount == "360-3PP" && !empty($terminalid))
                    )
                    && ($paymentonfinal == 'no' || $status == 'FINAL')
                ) {
                    ?>
                    <div class="row mr-md-1">
                        <div class="col-sm">
                            <sbp-button v-cloak color="warning" size="block" icon="credit-card" @click="makePayment">
                                Make Payment
                            </sbp-button>
                        </div>
                    </div>
                    <?php
                }
                ?>
                <div class="row mr-md-1">
                    <div class="col-sm">
                        <sbp-button v-cloak color="success" size="block" icon="thumbs-up" @click="promptTotal">Approve
                            Total / Esignature
                        </sbp-button>
                    </div>
                </div>
                <div class="row mr-md-1">
                    <div class="col-sm">
                        <?= str_ireplace('<btn-id>', 'sendupdatecfplink1', $cfplink) ?>
                    </div>
                </div>
            </sbp-button-container>
        </div>
        <sbp-page-container>
            <template #content>
                <div class="d-block d-md-flex flex-wrap">
                    <h5 class="d-block d-sm-none mb-3 font-weight-bold text-center">
                        <?php if (strtolower($Status) == "closed" || strtolower($Status) == "final") { ?>Invoice<?php } else { ?>Estimate<?php } ?>
                        # <?= $roid ?>
                    </h5>
                    <div class="d-block d-md-flex text-center mr-sm-3 mb-3">
                        <?php
                        if (strlen($logo) > 0) {
                            ?>
                            <sbp-shop-logo url="<?= $logo_url ?>"/>
                            <?php
                        }
                        ?>
                    </div>
                    <div class="d-block d-sm-flex justify-content-between flex-wrap flex-grow-1 mb-4">
                        <sbp-shop-contact :contact="companyContact"></sbp-shop-contact>
                        <div>
                            <div class="d-none d-sm-block text-right">
                                Created: <?= date('m/d/Y', strtotime($DateIn)) ?></div>
                            <div class="d-block d-sm-none text-center">
                                Created: <?= date('m/d/Y', strtotime($DateIn)) ?></div>
                            <h5 class="d-none d-md-block mb-5 font-weight-bold"><?php if (strtolower($Status) == "closed" || strtolower($Status) == "final") { ?>Invoice<?php } else { ?>Estimate<?php } ?>
                                # <?= $roid ?></h5>
                        </div>
                    </div>
                </div>
                <sbp-customer-vehicle-info
                        class="mb-4"
                        firstname="<?= $customerfirst ?>"
                        lastname="<?= $customerlast ?>"
                        info="<?= $vehinfo ?>"
                        vin="<?= $vin ?>"
                        license="<?= $vehlicense ?>"
                ></sbp-customer-vehicle-info>
                <div class="mb-4">
                    <?php
                    // check for signed invoices
                    $path = "d:\\savedinvoices\\$shopid\\$roid\\*.pdf";
                    if (file_exists("d:\\savedinvoices\\$shopid\\$roid\\")) {
                        ?>
                        <div class="border-gray mb-2">
                            <div class="bg-lightgray border-bottom-gray px-3 d-flex align-items-center">
                                <h6 class="font-weight-bold my-3 flex-grow-1">Signed Repair Orders (Click to View)</h6>
                                <div class="badge bg-primary p-2 text-capitalize"></div>
                            </div>
                            <div class="p-3">
                                <?php
                                foreach (glob($path) as $file) {
                                    echo "<a href='#' onclick='showFile(\"" . basename($file) . "\")'>" . basename($file) . "</a><br>";
                                }
                                ?>
                            </div>
                        </div>
                        <?php
                    }
                    ?>
                    <sbp-customer-vehicle-issue
                            v-for="issue in issues"
                            :key="issue.id"
                            :issue="issue"
                            print-tech-story="<?= strtolower($printtechstory) == "yes" ? "true" : "false" ?>"
                            print-scan-result="<?= strtolower($printscanresults) == "yes" && strtolower($matco) == "yes" ? "true" : "false" ?>"
                            @approve-decline-issue="toggleApproveDeclineModal"
                            showprice="<?= strtolower($printprices) ?>"
                            showparts="<?= strtolower($showpartsoninvoice) ?>"

                    ></sbp-customer-vehicle-issue>
                </div>
                <?php
                $stmt = "select sum(linetotal) ldiscount from labor where shopid = '$shopid' and roid = $roid and labor = 'discount' and tech = 'discount, discount'";
                if ($query = $conn->prepare($stmt)) {
                    $query->execute();
                    $query->bind_result($ldiscount);
                    $query->fetch();
                    $query->close();
                }
                $stmt = "select sum(linettlprice) pdiscount from parts where shopid = '$shopid' and roid = $roid and partnumber = 'discount' and partdesc = 'discount'";
                if ($query = $conn->prepare($stmt)) {
                    $query->execute();
                    $query->bind_result($pdiscount);
                    $query->fetch();
                    $query->close();
                }
                $partsdiscountstmt = "select coalesce(round(SUM((discount/100)*partprice*quantity),2),0) partsdiscount from parts where shopid = ? and roid = ?";
                if ($pdquery = $conn->prepare($partsdiscountstmt)) {
                    $pdquery->bind_param("si", $shopid, $roid);
                    $pdquery->execute();
                    $pdquery->store_result();
                    $pdquery->bind_result($partlinediscount);
                    $pdquery->fetch();
                }
                $pdiscount += '-' . $partlinediscount;

                $labordiscountstmt = "select coalesce(sum(discount),0) labordiscount from labor where shopid = ? and roid = ?";
                if ($ldquery = $conn->prepare($labordiscountstmt)) {
                    $ldquery->bind_param("si", $shopid, $roid);
                    $ldquery->execute();
                    $ldquery->store_result();
                    $ldquery->bind_result($laborlinediscount);
                    $ldquery->fetch();
                }

                $ldiscount += '-' . $laborlinediscount;

                ?>
                <sbp-repair-order-aggregation
                        class="mb-4"
                        :data="aggregationData"
                        showitems="<?= strtolower($showpartsoninvoice) ?>"
                >
                </sbp-repair-order-aggregation>

                <?php if (!empty($cfplink)) { ?>

                    <div class="float-right"><?= str_ireplace('<btn-id>', 'sendupdatecfplink2', $cfplink) ?></div><br>

                <?php } ?>

                <?php
                if (!empty($rodisc)) echo("<b>SIGNATURE DISCLOSURE</b><br>" . strtoupper($rodisc) . "<br><br>");
                if (!empty($warrdisc)) echo("<b>WARRANTY DISCLOSURE</b><br>" . strtoupper($warrdisc) . "<br><br>");
                ?>

                <sbp-button-container halign="end">
                    <?php
                    if (strtolower($Status) != "final" || strtolower($Status) != "closed") {
                        ?>
                        <div class="row mr-md-1">
                            <div class="col-sm">
                                <sbp-button v-cloak size="block" color="success" icon="thumbs-up" @click="promptTotal">
                                    Approve Total
                                </sbp-button>
                            </div>
                        </div>
                        <?php
                    }
                    ?>
                </sbp-button-container>
            </template>
            <?php
            $stmt = "select picname,inspitemid,caption, type from repairorderpics where shopid = '$shopid' and roid = $roid order by id, type";
            if ($query = $conn->prepare($stmt)) {
                $query->execute();
                $result = $query->get_result();
                $imageCount = 0;
                $attachmentCount = 0;
                $imageUrls = $attachments = array();
                $imageCaptions = array();
                while ($row = $result->fetch_array()) {
                    if ($row['inspitemid'] == '0') {
                        if (strtolower($row['type']) == "img") {
                            $imageCount++;
                            $img_url = $protocol . "://" . $_SERVER['SERVER_NAME'] . "/sbp/upload/" . $shopid . "/" . $roid . "/" . $row['picname'];
                            if (strpos($row['picname'], "/") !== false) {
                                $filename = basename($row['picname']);
                                $img_url = "https://customers-ss.s3.amazonaws.com/" . $row['picname'];
                            }
                            array_push($imageUrls, $img_url);
                            array_push($imageCaptions, $row['caption']);
                        } else {
                            //attachments
                            $attachmentCount++;
                            $attachment_url = $protocol . "://" . $_SERVER['SERVER_NAME'] . "/sbp/upload/" . $shopid . "/" . $roid . "/" . $row['picname'];
                            if (strpos($row['picname'], "/") !== false) {
                                $filename = basename($row['picname']);
                                $attachment_url = "https://customers-ss.s3.amazonaws.com/" . $row['picname'];
                            }
                            $attachments[] = array(
                                'url' => $attachment_url,
                                'caption' => $row['caption'],
                                'type' => $row['type'],
                                'label' => $row['picname'],
                            );
                        }
                    }
                }
                if ($imageCount > 0) {
                    ?>
                    <template #secondary>
                        <div id="ro-images" class="carousel slide" data-ride="carousel">
                            <ol class="carousel-indicators">
                                <?php
                                for ($index = 0; $index < $imageCount; $index++) {
                                    ?>
                                    <li data-target="#ro-images" data-slide-to="<?= $index ?>"
                                        class="<?php if ($index == 0) {
                                            echo "active";
                                        } else {
                                            echo "";
                                        } ?>"></li>
                                    <?php
                                }
                                ?>
                            </ol>
                            <div class="carousel-inner">
                                <?php
                                foreach ($imageUrls as $index => $imageUrl) {
                                    $pdfurl = '';
                                    $ext = pathinfo($imageUrl, PATHINFO_EXTENSION);
                                    if ($ext == 'pdf') {
                                        $pdfurl = str_replace($shopid . "_", "", $imageUrl);
                                        $imageUrl = ASSETS . "/pdf-icon.png";
                                    }
                                    ?>
                                    <div class="carousel-item <?php if ($index == 0) {
                                        echo "active";
                                    } else {
                                        echo "";
                                    } ?>">
                                        <a href="<?= $ext == 'pdf' ? $pdfurl : 'javascript:void(null)' ?>" <?= $ext == 'pdf' ? "target='_blank'" : '' ?>><img
                                                    src="<?= $imageUrl ?>"
                                                    class="d-block w-100"
                                                    alt="..."
                                            /></a>
                                        <?php
                                        if (!empty($imageCaptions[$index])) {
                                            ?>
                                            <div class="carousel-caption d-none d-md-block">
                                                <h5><?= $imageCaptions[$index] ?></h5>
                                            </div>
                                            <?php
                                        }
                                        ?>
                                    </div>
                                    <?php
                                }
                                ?>
                            </div>
                            <a
                                    class="carousel-control-prev"
                                    href="#ro-images"
                                    role="button"
                                    data-slide="prev"
                            >
                                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                <span class="sr-only">Previous</span>
                            </a>
                            <a
                                    class="carousel-control-next"
                                    href="#ro-images"
                                    role="button"
                                    data-slide="next"
                            >
                                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                <span class="sr-only">Next</span>
                            </a>
                        </div>

                    </template>
                    <?php
                }
                if ($attachmentCount > 0) {
                    ?>
                    <template #attachments>
                        <div class="row" id="attachments">
                            <?php
                            foreach ($attachments as $attachment) {
                                $url = $attachment['url'];
                                $caption = $attachment['caption'];
                                $type = $attachment['type'];
                                $label = $attachment['label'];

                                switch ($type) {
                                    case "pdf":
                                        $icon = "fas fa-file-pdf fa-2x";
                                        break;
                                    case "doc":
                                    case "ocx":
                                    case "docx":
                                        $icon = "fas fa-file-word fa-2x";
                                        break;
                                    case "xls":
                                    case "xlsx":
                                    case "lsx":
                                        $icon = "fas fa-file-excel fa-2x";
                                        break;
                                    default:
                                        $icon = "fas fa-file fa-2x";
                                }
                                ?>
                                <div class="col-6 mb-2">
                                    <a href="<?= $url ?>" target="_blank">
                                        <i style="color: rgb(79 79 79 / 75%);" class="<?= $icon ?>"></i>
                                         <?= basename($label) ?>
                                        <br/>
                                        <small><?= $caption ?></small>
                                    </a>
                                </div>
                                <?php
                            }
                            ?>
                        </div>
                    </template>
                    <?php
                }
            }
            ?>
        </sbp-page-container>

        <sbp-modal-base
                id="approve-decline-modal"
                ref="approve-decline-modal"
                title="Approve or Decline this Repair"
                :show-close="false"
        >
            <template #footer>
                <sbp-button color="success" icon="thumbs-up" @click="approveOrDeclineRepair('approve')"
                            :loading="approvalLoad">Approve
                </sbp-button>
                <sbp-button v-if="approveDeclineModal.declineButtonVisible" color="danger" icon="thumbs-down"
                            @click="approveOrDeclineRepair('decline')" :loading="declineLoad">Decline
                </sbp-button>
            </template>
        </sbp-modal-base>

        <sbp-modal-base
                id="financing-modal"
                ref="financing-modal"
                title="Financing"
        >
            <template #body>
                <div>If you would like to pay with <?= $cfplender ?>, please call <?= $Writer ?> from <?= $shopname ?>
                    at <?= formatPhone($shopphone) ?> or let the shop know when paying and they will apply to the
                    desired amount to your invoice.
                </div>
            </template>

            <template #footer>
            </template>
        </sbp-modal-base>

        <sbp-modal-iframe
                id="invoice-modal"
                ref="invoice-modal"
                title="Repair Order"
                :url="invoiceUrl"
        >
            <template #footer>
                <sbp-button color="success" icon="print" @click="printInvoice($refs['invoice-modal'].getIframeEl())"
                            data-dismiss="modal">Print
                </sbp-button>
            </template>
        </sbp-modal-iframe>

        <sbp-modal-iframe
                id="signed-invoice-modal"
                ref="signed-invoice-modal"
                title="Signed Repair Order"
                :url="signedInvoiceUrl"
        >
            <template #footer>
                <sbp-button color="success" icon="print"
                            @click="printInvoice($refs['signed-invoice-modal'].getIframeEl())" data-dismiss="modal">
                    Print
                </sbp-button>
                <sbp-button color="success" icon="envelope" @click="emailRO" data-dismiss="modal">Email</sbp-button>
            </template>
        </sbp-modal-iframe>

        <sbp-modal-base
                id="approve-total-modal"
                ref="approve-total-modal"
                :title="approveTotalModal.title"
                :action-error="approveTotalModal.actionError"
        >
            <template #body>
                <div>Click the Approve Total button below to proceed</div>
            </template>
            <template #footer>
                <sbp-button color="success" icon="thumbs-up" @click="approveTotal" :loading="approveTotalModal.loading">
                    {{ approveTotalModal.actionLabel }}
                </sbp-button>
            </template>
        </sbp-modal-base>

        <sbp-modal-base
                id="inspection-ready-modal"
                ref="inspection-ready-modal"
                title="There are completed inspection(s) ready"
                :show-close="false"
        >
            <template #body>
                <sbp-button-container>
                    <?php
                    if (!empty($inspections)) {
                        foreach ($inspections as $inspection) {
                            $ascnt += 1;
                            if ($inspection['type'] == 'classic') {
                                ?>
                                <sbp-button color="success" icon="file-alt" size="block"
                                            @click="showInspection(<?= htmlspecialchars(json_encode($inspection['name']), ENT_QUOTES) ?>)">
                                    <?php echo $inspection['name']; ?> Inspection
                                </sbp-button>
                                <?php
                            } elseif ($inspection['type'] == 'newdvi') {
                                ?>
                                <div class="row">
                                    <div class="col-12">
                                        <sbp-button color="success" icon="file-alt" size="block"
                                                    href="dvi_report.php?<?= $inspection['param'] ?>">
                                            <?php echo $inspection['name']; ?> Inspection
                                        </sbp-button>
                                    </div>
                                </div>
                                <?php
                            } elseif ($inspection['type'] == 'MSM') {
                                ?>
                                <div class="row">
                                    <div class="col-12">
                                        <sbp-button color="success" icon="file-alt" size="block"
                                                    @click="openMSMInspection('<?= $inspection['param'] ?>')">
                                            <?php echo $inspection['name']; ?>
                                        </sbp-button>
                                    </div>
                                </div>
                                <?php
                            } elseif ($inspection['type'] == 'AS1') {
                                ?>
                                <div class="row">
                                    <div class="col-12">
                                        <sbp-button color="success" icon="file-alt" size="block"
                                                    @click="openASInspection('<?= $inspection['param'] ?>')">
                                            <?php echo $inspection['name']; ?>
                                        </sbp-button>
                                    </div>
                                </div>
                                <?php
                            }
                        }
                    }
                    ?>
                </sbp-button-container>
                <div class="form-check">
                    <input
                            class="form-check-input"
                            type="checkbox"
                            :value="doNotShowInspectionReadyModal"
                            @input="handleToggleInspectionReadyModalVisibility"
                            id="doNotShowInspectionReadyModal"
                    />
                    <label class="form-check-label" for="doNotShowInspectionReadyModal">
                        Do not show again
                    </label>
                </div>
            </template>
        </sbp-modal-base>

        <sbp-modal-base
                id="verification-modal"
                ref="verification-modal"
                title="Authorization Required"
                :action-error="verificationModal.actionError"
        >
            <template #body>
                <div class="text-wrap">Enter the last 4 digits of a phone number or email address associated with this
                    repair
                </div>
                <form id="verification-form" novalidate>
                    <div class="form-outline">
                        <input type="tel" id="verification-form_phone-number" class="form-control" required/>
                        <label class="form-label" for="verification-form_phone-number">Last 4 of phone number or email
                            address</label>
                        <div class="invalid-feedback">You need to enter the last 4 digits of a phone number on your
                            account to verify this step
                        </div>
                    </div>
                </form>
            </template>
            <template #footer>
                <sbp-button color="success" icon="check" @click="verifyCustomer" :loading="verificationModal.loading">
                    Verify
                </sbp-button>
            </template>
        </sbp-modal-base>

        <sbp-modal-base
                id="emailinvmodal"
                ref="emailinvmodal"
                title="Email Repair Order To Customer"
        >
            <template #body>
                <form id="ro-emailinvmodal-form" class="row g-3" novalidate>
                    <div class="col-12">
                        <div class="form-outline">
                            <input type="email" id="ro-emailinvmodal-form_email" class="form-control" value=""
                                   required/>
                            <label class="form-label" for="ro-emailinvmodal-form_email">Email Address</label>
                            <div class="invalid-feedback">Email is required</div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-outline">
                            <input type="text" id="ro-emailinvmodal-form_subject" class="form-control" value=""
                                   required/>
                            <label class="form-label" for="ro-emailinvmodal-form_subject">Subject</label>
                            <div class="invalid-feedback">Subject is required</div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-outline">
                            <textarea id="ro-emailinvmodal-form_message" class="form-control" required></textarea>
                            <label class="form-label" for="ro-emailinvmodal-form_message">Message</label>
                            <div class="invalid-feedback">Message is required</div>
                        </div>
                    </div>
                </form>
            </template>
            <template #footer>
                <sbp-button color="success" icon="paper-plane" @click="sendEmail">Send</sbp-button>
            </template>
        </sbp-modal-base>
    </div>

    <input type="hidden" id="invpath">
    <input type="hidden" id="complaintId">
    <input type="hidden" id="repairActionType">
    <?php
} else {
    ?>
    <div class="container" style="max-width:900px;margin:auto">
        <div class="alert alert-danger" style="margin-top: 80px;">
            Something went wrong with the URL you provided, please try again later or contact us.
        </div>
    </div>

    <?php
}
?>
</body>

<script src="<?= MDB ?>/js/mdb.min.js"></script>
<script src="<?= SCRIPT ?>/core/jquery.min.js"></script>
<script src="<?= SBPUI ?>/vue.min.js"></script>
<script src="<?= SBPUI ?>/<?= SBPUI_MAIN ?>"></script>

<script>
    const vueApp = new Vue({
        el: '#app',
        components: sbpUI.take([
            'sbp-page-container',
            'sbp-button',
            'sbp-button-container',
            'sbp-shop-logo',
            'sbp-shop-contact',
            'sbp-customer-vehicle-issue',
            'sbp-customer-vehicle-info',
            'sbp-repair-order-aggregation',
            'sbp-modal-base',
            'sbp-modal-iframe'
        ]),
        data() {
            const totalro = "<?= $totalro ?>";
            console.log('total ro', totalro);
            return {
                companyContact: {
                    name: "<?= $companyname ?>",
                    address: "<?= $companyaddress ?>",
                    city: "<?= $companycity ?>",
                    state: "<?= $companystate ?>",
                    zip: "<?= $companyzip ?>",
                    email: "<?= $companyemail ?>",
                    phone: "<?= formatPhone($companyphone) ?>",
                },
                issues: <?= json_encode($issues) ?>,
                aggregationData: {
                    totalFee: "<?= $totalfees ?>",
                    subtotal: "<?= $truncomttl + $totalfees ?>",
                    laborDiscount: "<?= $ldiscount ?>",
                    partDiscount: "<?= $pdiscount ?>",
                    salesTax: "<?= $salestax ?>",
                    total: "<?= $totalro ?>",
                    totalPaid: "<?= $totalpayments ?>",
                    balance: "<?= $balancero ?>",
                    discountLabel: "<?= $discountlabel ?>"
                },
                approveDeclineModal: {
                    declineButtonVisible: true
                },
                invoiceUrl: null,
                signedInvoiceUrl: null,
                approveTotalModal: {
                    title: '',
                    actionLabel: '',
                    actionError: null,
                    loading: false
                },
                verificationModal: {
                    loading: false,
                    actionError: null
                },
                emailModal: {
                    loading: false,
                    actionError: null
                },
                doNotShowInspectionReadyModal: false,
                approvalLoad: false,
                declineLoad: false
            };
        },
        mounted() {
            $(document).on("touchstart", function (event) {
                localStorage.setItem("drawsig", "true");
            })

            localStorage.setItem("xcntr", 0);
            localStorage.setItem("cntr", 0);
            localStorage.setItem("attempt-hack", "false");

            const shopid = "<?= $shopid ?>";
            const roid = "<?= $roid ?>";
            if (localStorage.getItem(`${shopid}-${roid}:do-not-show-inspection-ready-modal`) !== 'true') {
                <?php
                if ($ascnt > 0) {
                ?>
                this.$refs['inspection-ready-modal'].open();
                <?php
                }
                ?>
            }
        },
        methods: {
            handleToggleInspectionReadyModalVisibility(event) {
                const shopid = "<?= $shopid ?>";
                const roid = "<?= $roid ?>";

                this.doNotShowInspectionReadyModal = event.target.checked;
                localStorage.setItem(`${shopid}-${roid}:do-not-show-inspection-ready-modal`, this.doNotShowInspectionReadyModal);
            },
            toggleApproveDeclineModal(complaintId, locked) {
                document.getElementById('complaintId').value = complaintId;
                this.$set(this.approveDeclineModal, 'declineButtonVisible', locked != 'yes');
                this.$refs['approve-decline-modal'].open();
            },
            promptTotal() {
                amt = $('#totalroamt').text();

                this.approveTotalModal = {
                    loading: false,
                    title: `Approve Total Repairs of ${amt}`,
                    actionLabel: `Approve Total ${amt}`
                }
                this.$refs['approve-total-modal'].open();
            },
            approveTotal() {
                this.$set(this.approveTotalModal, 'loading', true);

                if ('<?= $shopid?>' == '10031') {
                    location.href = `<?= STATUS ?>` + 'status-esig.php?<?= $encodedstring?>';
                    return;
                }

                $.ajax({
                    data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
                    url: "https://<?= ROOT ?>/temp/create.php",
                    type: "post",
                    success: (r) => {
                        setTimeout(function () {
                            location.href = "https://<?= ROOT ?>/temp/" + r
                        }, 1500);
                    },
                    error: (xhr, ajaxOptions, thrownError) => {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                        this.$set(this.approveTotalModal, 'loading', false);
                        this.$set(this.approveTotalModal, 'actionError', xhr.responseText);
                    }
                });
            },
            approveOrDeclineRepair(actionType) {
                document.getElementById('repairActionType').value = actionType;
                <?php if(isset($_COOKIE['statusaction']) && $_COOKIE['statusaction'] == $shopid . '-' . $roid){?>
                this.sendApproveOrDeclineRequest('cookie')
                <?php }else{?>
                this.$refs['approve-decline-modal'].close();
                setTimeout(() => {
                    this.$refs['verification-modal'].open();
                    setTimeout(resetVerificationForm, 500);
                }, 500);
                <?php }?>
            },
            verifyCustomer() {
                const form = document.getElementById('verification-form');
                form.classList.add('was-validated');
                if (form.checkValidity()) {
                    const phoneInput = document.getElementById('verification-form_phone-number');
                    this.sendApproveOrDeclineRequest(phoneInput.value);
                }
            },
            sendApproveOrDeclineRequest(phoneNumber) {
                const complaintId = document.getElementById('complaintId').value;
                const actionType = document.getElementById('repairActionType').value;

                if (phoneNumber != 'cookie')
                    this.verificationModal = {loading: true};

                var ds;
                if (actionType == 'approve') {
                    if (phoneNumber == 'cookie') this.approvalLoad = true;
                    ds = "testval=" + phoneNumber + "&t=approvecomplaint&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&comid=" + complaintId;
                } else {
                    if (phoneNumber == 'cookie') this.declineLoad = true;
                    ds = "testval=" + phoneNumber + "&t=declinecomplaint&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&comid=" + complaintId;
                }

                $.ajax({
                    data: ds,
                    url: "https://<?= $_SERVER['SERVER_NAME']; ?>/statusupdates.php",
                    type: "post",
                    success: (r) => {
                        if (actionType == 'approve') {
                            if (r == "success") {
                                location.reload()
                            } else {
                                this.verificationModal = {
                                    loading: false,
                                    actionError: r
                                };
                            }
                        } else {
                            if (r != "") {
                                this.verificationModal = {
                                    loading: false,
                                    actionError: r
                                };
                            } else {
                                $.ajax({
                                    url: "https://<?= $_SERVER['SERVER_NAME']; ?>/totalupdate.php",
                                    data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
                                    type: "get",
                                    success: function (r) {
                                        location.reload()
                                    }
                                });
                            }
                        }
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log(xhr.status);
                        console.log(xhr.responseText);
                        console.log(thrownError);
                    }
                });
            },
            emailRO() {
                this.$refs['signed-invoice-modal'].close();
                setTimeout(() => {
                    this.$refs.emailinvmodal.open();
                    setTimeout(resetEmailRoForm, 500);
                }, 500);
            },
            sendEmail() {
                const form = document.getElementById('ro-emailinvmodal-form');
                form.classList.add('was-validated');
                if (form.checkValidity()) {
                    const emailInput = document.getElementById('ro-emailinvmodal-form_email');
                    const subjectInput = document.getElementById('ro-emailinvmodal-form_subject');
                    const messageInput = document.getElementById('ro-emailinvmodal-form_message');
                    this.sendInvoiceEmail(emailInput.value, subjectInput.value, messageInput.value);
                }
            },
            sendInvoiceEmail(sendto, emailsubject, emailmessage) {
                invpath = $('#invpath').val();
                invpath = "https://<?php echo $_SERVER["SERVER_NAME"]; ?>/sbp/savedinvoices/<?php echo $shopid; ?>/<?php echo $roid; ?>/" + invpath;

                this.emailModal = {
                    loading: true
                };

                if (invpath.length > 0) {
                    if ($("#updatecustomeremail").is(':checked')) {
                        ucstr = "updatecustomer=y&"
                    } else {
                        ucstr = "updatecustomer=n&"
                    }

                    ds = ucstr + "roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&cid=<?php echo $CustomerID; ?>&sendfrom=<?php echo urlencode($shopname);?>&sendto=" + sendto + "&invpath=" + invpath + "&subject=" + emailsubject + "&message=" + emailmessage + "&shopemail=<?php echo urlencode($companyemail); ?>&guest=yes"
                    $.ajax({
                        data: ds,
                        url: "https://<?= COMPONENTS_PRIVATE ?>/ro/roemailinvoice.php",
                        error: (xhr, ajaxOptions, thrownError) => {
                            console.log(xhr.status);
                            console.log(xhr.responseText);
                            console.log(thrownError);
                        },
                        success: (r) => {
                            if (r == "success") {
                                this.emailModal = {
                                    loading: false
                                };
                                this.$refs.emailinvmodal.close();

                                sbpUI.methods.alertPositive('Invoice Sent');
                            } else if (r == "success|") {
                                this.emailModal = {
                                    loading: false
                                };
                                this.$refs.emailinvmodal.close();
                                setTimeout(function () {
                                    location.reload()
                                }, 2000);

                                sbpUI.methods.alertPositive('Invoice Sent.  Reloading with new email address');
                            } else {
                                console.log(r)
                            }

                            printit = $('#printit').val()
                            if (printit == "yes") {
                                location.href = '<?= COMPONENTS_PRIVATE ?>/wip/wip.php'
                            }
                        }
                    });
                } else {
                    $.ajax({
                        data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>",
                        url: "<?= COMPONENTS_PUBLIC ?>/invoices/printpdfro.asp",
                        success: (r) => {
                            $('#invpath').val(r)
                            if ($("#updatecustomeremail").is(':checked')) {
                                ucstr = "updatecustomer=y&"
                            } else {
                                ucstr = "updatecustomer=n&"
                            }

                            ds = ucstr + "roid=<?php echo $roid; ?>&shopid=<?php echo $shopid; ?>&cid=<?php echo $CustomerID; ?>&sendfrom=<?php echo urlencode($shopname);?>&sendto=" + sendto + "&invpath=" + invpath + "&subject=" + emailsubject + "&message=" + emailmessage + "&shopemail=<?php echo urlencode($companyemail); ?>"
                            console.log(ds)
                            $.ajax({
                                data: ds,
                                url: "roemailinvoice.php",
                                success: (r) => {
                                    if (r == "success") {
                                        this.emailModal = {
                                            loading: false
                                        };
                                        this.$refs.emailinvmodal.close();

                                        sbpUI.methods.alertPositive('Invoice Sent.  Reloading with new email address');
                                    } else if (r == "success|") {
                                        this.emailModal = {
                                            loading: false
                                        };
                                        this.$refs.emailinvmodal.close();
                                        setTimeout(function () {
                                            location.reload()
                                        }, 2000)

                                        sbpUI.methods.alertPositive('Invoice Sent.  Reloading with new email address');
                                    } else {
                                        console.log(r)
                                    }
                                }
                            });
                        }
                    });
                }
            }
        }
    });

    function showInspection(v) {
        const sanitized = v.replace(/^"*|"*$/g, '');
        var url = "<?= COMPONENTS_PRIVATE ?>/inspection_classic/customerdvi.php?iname=" + encodeURIComponent(sanitized) + "&shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>"
        location.href = url
    }

    function openMSMInspection(url) {
        window.open(url)
    }

    function openASInspection(uuid) {
        url = "https://app.autoserve1.com/report/" + uuid
        window.open(url)
    }

    function makePayment() {
        location.href = 'status_payment.php?<?= $encodedstring ?>'
    }

    document.addEventListener('DOMContentLoaded', (event) => {
        window.scrollTo(0, 0);
    });

    // Reset the phone number verification form
    function resetVerificationForm() {
        // Reset the form validation
        const phoneVerificationForm = document.getElementById('verification-form');
        phoneVerificationForm.classList.remove('was-validated');

        // Reset the phone number input
        const phoneInput = document.getElementById('verification-form_phone-number');
        phoneInput.value = "";
        phoneInput.classList.remove('active');

        // Dynamically initialize the phone number input
        document.querySelectorAll('#verification-form .form-outline').forEach((formOutline) => {
            new mdb.Input(formOutline).init();
        });
    }

    function showInvoice() {
        $.ajax({
            data: "shopid=<?php echo $shopid; ?>&roid=<?php echo $roid; ?>&i=new",
            url: "<?= COMPONENTS_PUBLIC ?>/invoices/printpdfro.asp",
            error: function (xhr, ajaxOptions, thrownError) {
                console.log(xhr.status);
                console.log(xhr.responseText);
                console.log(thrownError);
            },
            success: function (r) {
                $('#invpath').val(r);

                if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent))
                    var invoiceUrl = `https://docs.google.com/viewerng/viewer?embedded=true&url=<?= COMPONENTS_PUBLIC . '/invoices'?>${r}`;
                else
                    var invoiceUrl = `<?= COMPONENTS_PUBLIC . '/invoices'?>${r}`;

                vueApp.invoiceUrl = invoiceUrl;
                vueApp.$refs['invoice-modal'].open();
            }
        });
    }

    function showDiagnosticReport() {

        vueApp.invoiceUrl = "<?= COMPONENTS_PRIVATE ?>/v2/scan_tool_dashboard/generatepdf.php?id=<?= $diagnose_record_id ?>";
        vueApp.$refs['invoice-modal'].open();

    }

    function printInvoice(iframeEl) {
        iframeEl.focus();
        iframeEl.contentWindow.print();
    }

    function showFile(fname) {
        $('#invpath').val(fname);

        var invoiceUrl = 'https://<?php echo $_SERVER["SERVER_NAME"]; ?>/sbp/savedinvoices/<?php echo $shopid; ?>/<?php echo $roid; ?>/' + fname;
        vueApp.signedInvoiceUrl = invoiceUrl;
        vueApp.$refs['signed-invoice-modal'].open();
    }

    // Reset the email RO form
    function resetEmailRoForm() {
        // Reset the form validation
        const form = document.getElementById('ro-emailinvmodal-form');
        form.classList.remove('was-validated');

        const email = "<?php echo $email; ?>";

        // Reset the email input
        const emailInput = document.getElementById('ro-emailinvmodal-form_email');
        emailInput.value = email;
        emailInput.classList.remove('active');

        const subject = "<?php
            if (strtolower($Status) == "final" || strtolower($Status) == "closed") {
                echo str_replace('"', '', $emailinvoicesubject);
            } else {
                echo str_replace('"', '', $emailestimatesubject);
            }
            ?>";

        // Reset the subject input
        const subjectInput = document.getElementById('ro-emailinvmodal-form_subject');
        subjectInput.value = subject;
        subjectInput.classList.remove('active');

        const message = `<?php
        if (strtolower($Status) == "final" || strtolower($Status) == "closed") {
            echo $emailinvoice;
        } else {
            echo $emailestimate;
        }
        ?>`;

        // Reset the email input
        const messageInput = document.getElementById('ro-emailinvmodal-form_message');
        messageInput.value = message;
        messageInput.classList.remove('active');

        // Dynamically initialize the input elements
        document.querySelectorAll('#ro-emailinvmodal-form .form-outline').forEach((formOutline) => {
            new mdb.Input(formOutline).init();
        });
    }
</script>

</html>
