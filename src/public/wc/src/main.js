//import ImageUploadEditor from "./vuecomponents/vue-imageupload-editor";

// Customer facing
import SBPBackStatus from "./vuecomponents/customer-facing/sbp-back-status";
import SBPCustomerVehicleIssue from "./vuecomponents/customer-facing/sbp-customer-vehicle-issue";
import SBPCustomerVehicleInfo from "./vuecomponents/customer-facing/sbp-customer-vehicle-info";
import SBPRepairOrderAggregation from "./vuecomponents/customer-facing/sbp-repair-order-aggregation";
import SBPShopContact from "./vuecomponents/customer-facing/sbp-shop-contact";
import SBPShopLogo from "./vuecomponents/customer-facing/sbp-shop-logo";
import SBPCard from "./vuecomponents/customer-facing/sbp-card";


// Primitive components
import SBPPageContainer from "./vuecomponents/sbp-page-container";
import SBPButton from "./vuecomponents/sbp-button";
import SBPButtonContainer from "./vuecomponents/sbp-button-container";
import SBPModalBase from "./vuecomponents/sbp-modal-base";
import SBPPortalModalBase from "./vuecomponents/sbp-portal-modal-base";
import SBPModalIframe from "./vuecomponents/sbp-modal-iframe";
import SBPPricingPlanCard from "./vuecomponents/sbp-pricing-plan-card";
import SBPSpinner from "./vuecomponents/sbp-spinner";
import SBPDropdown from "./vuecomponents/dropdown/sbp-dropdown";
import SBPDropdownToggle from "./vuecomponents/dropdown/sbp-dropdown-toggle";
import SBPDropdownMenu from "./vuecomponents/dropdown/sbp-dropdown-menu";
import SBPDropdownItem from "./vuecomponents/dropdown/sbp-dropdown-item";
import SBPResizable from "./vuecomponents/sbp-resizable";

// Report
import SBPTestChartCardChart from "./vuecomponents/report/test-chart-card/chart";
import SBPTestSalesCardDrilldown from "./vuecomponents/report/test-sales-card/drilldown";
import SBPTestKPICardDrilldown from "./vuecomponents/report/test-kpi-card/drilldown";

import SBPDateRange from "./vuecomponents/report/sbp-date-range";
import SBPReportSettingsDialog from "./vuecomponents/report/sbp-report-settings-dialog";
import SBPMetricSummaryCard from "./vuecomponents/report/sbp-metric-summary-card";
import SBPTotalShopsSSChart from "./vuecomponents/report/sbp-total-shop-ss";
import SBPTotalSalesDrilldown from "./vuecomponents/report/total-sales/drilldown";
import SBPRoSalesFullReport from "./vuecomponents/report/ro-sales/drilldown";
import SBPPaymentsDrilldown from "./vuecomponents/report/payments/drilldown";
import SBPPartsSalesDrilldown from "./vuecomponents/report/parts-sales/drilldown";
import SBPPartsSalesCategoryDrilldown from "./vuecomponents/report/parts-sales-category/drilldown";
import SBPPartsProfitMarginDrilldown from "./vuecomponents/report/parts-profit-margin/drilldown";
import SBPRevenueSummaryDrilldown from "./vuecomponents/report/revenue-summary/drilldown";
import SBPRoCountDrilldown from "./vuecomponents/report/ro-count/drilldown";
import SBPARODrilldown from "./vuecomponents/report/aro/drilldown";
import SBPLaborHoursPerRoDrilldown from "./vuecomponents/report/labor-hours-per-ro/drilldown";
import SBPTotalLaborHoursDrilldown from "./vuecomponents/report/total-labor-hours/drilldown";
import SBPAvgHoursPerTechDrilldown from "./vuecomponents/report/avg-hours-per-tech/drilldown";
import SBPAvgRosPerTechDrilldown from "./vuecomponents/report/avg-ros-per-tech/drilldown";
import SBPTechCostPerLaborHourDrilldown from "./vuecomponents/report/tech-cost-per-labor-hour/drilldown";
import SBPLaborCostAsPercentOfSalesDrilldown from "./vuecomponents/report/labor-cost-as-percent-of-sales/drilldown";
import SBPPartsCostAsPercentOfSalesDrilldown from "./vuecomponents/report/parts-cost-as-percent-of-sales/drilldown";
import SBPTouchtimeDrilldown from "./vuecomponents/report/touch-time/drilldown";
import SBPEffectiveLaborRateDrilldown from "./vuecomponents/report/effective-labor-rate/drilldown";
import SBPGrossProfitDrilldown from "./vuecomponents/report/gross-profit/drilldown";
import SBPGrossProfitPerHourDrilldown from "./vuecomponents/report/gross-profit-per-hour/drilldown";
import SBPCustomerConcernsByCategoryDrilldown from "./vuecomponents/report/customer-concerns-by-category/drilldown";
import SBPCustomerConcernsByCategory from "./vuecomponents/report/customer-concerns-by-category/chart";
import SBPShopProductivityDrilldown from "./vuecomponents/report/shop-productivity/drilldown";
import SBPClosingRateDrilldown from "./vuecomponents/report/closing-rate/drilldown";
import SBPCustomerAcquisitionRateDrilldown from "./vuecomponents/report/customer-acquisition-rate/drilldown";
import SBPNewExistingCustomersSalesDrilldown from "./vuecomponents/report/new-existing-customers-sales/drilldown";
import SBPTotalSalesChart from "./vuecomponents/report/total-sales/chart";
import SBPTotalPackagesChart from "./vuecomponents/report/total-packages/chart";
import SBPRevenueSummary from "./vuecomponents/report/revenue-summary/chart";
import SBPAROChart from "./vuecomponents/report/aro/chart";
import SBPEffectiveLaborRateChart from "./vuecomponents/report/effective-labor-rate/chart";
import SBPGrossProfitChart from "./vuecomponents/report/gross-profit/chart";
import SBPLaborHoursPerTech from "./vuecomponents/report/labor-hours-per-tech/chart";
import SBPLaborHoursPerTechDrilldown from "./vuecomponents/report/labor-hours-per-tech/drilldown";
import ImageUploadEditor from "./vuecomponents/vue-imageupload-editor/ImageEditor";
//   VueCollapsiblePanel,
//MDBVue

// DVI
import SBPDVIInspectionItem from "./vuecomponents/dvi/sbp-dvi-inspection-item";
import SBPDVIReportFindingInput from "./vuecomponents/dvi/sbp-dvi-report-finding-input";
import SBPDVISummaryFindingInput from "./vuecomponents/dvi/sbp-dvi-summary-finding-input";
import SBPDVIPublicReport from "./vuecomponents/dvi/sbp-dvi-public-report";
import SBPDVICustomerRequest from "./vuecomponents/dvi/sbp-dvi-customer-request";
import SBPDVIReportCustomerRequest from "./vuecomponents/dvi/sbp-dvi-report-customer-request";
import SBPDVIReportQuote from "./vuecomponents/dvi/sbp-dvi-report-quote";
import SBPDVIPublicReportCustomerRequest from "./vuecomponents/dvi/sbp-dvi-public-report-customer-request";
import ImageEditorProvider from "./vuecomponents/dvi/sbp-dvi-image-editor-provider";
import ImageEditorWrapper from "./vuecomponents/dvi/sbp-dvi-image-wrapper";
import draggable from "vuedraggable";
//import { BulmaAccordion, BulmaAccordionItem } from "vue-bulma-accordion";
import BadgerAccordion from "./vuecomponents/vue-badger-accordion/components/BadgerAccordion";
import BadgerAccordionItem from "./vuecomponents/vue-badger-accordion/components/BadgerAccordionItem";
import SBPLivetextCard from "./vuecomponents/livetext/sbp-livetext-card.vue";
import AIWritingTool from "./vuecomponents/ai-writing-tool/sbp-ai-writing-tool";

export * as methods from "./methods";

const vueCollapsible = {
    BadgerAccordion,
    BadgerAccordionItem,
};
const dviComponents = {
    "sbp-dvi-inspection-item": SBPDVIInspectionItem,
    "sbp-dvi-report-finding-input": SBPDVIReportFindingInput,
    "sbp-dvi-summary-finding-input": SBPDVISummaryFindingInput,
    "sbp-dvi-public-report": SBPDVIPublicReport,
    "sbp-dvi-customer-request": SBPDVICustomerRequest,
    "sbp-dvi-report-customer-request": SBPDVIReportCustomerRequest,
    "sbp-dvi-report-quote": SBPDVIReportQuote,
    "sbp-dvi-public-report-customer-request": SBPDVIPublicReportCustomerRequest,
    "sbp-dvi-image-editor-provider": ImageEditorProvider,
    "sbp-dvi-image-wrapper": ImageEditorWrapper,
};

export const components = {
    "sbp-back-status": SBPBackStatus,
    "sbp-customer-vehicle-issue": SBPCustomerVehicleIssue,
    "sbp-customer-vehicle-info": SBPCustomerVehicleInfo,
    "sbp-repair-order-aggregation": SBPRepairOrderAggregation,
    "sbp-shop-contact": SBPShopContact,
    "sbp-page-container": SBPPageContainer,
    "sbp-button": SBPButton,
    "sbp-button-container": SBPButtonContainer,
    "sbp-shop-logo": SBPShopLogo,
    "sbp-modal-base": SBPModalBase,
    "sbp-portal-modal-base": SBPPortalModalBase,
    "sbp-modal-iframe": SBPModalIframe,
    "sbp-pricing-plan-card": SBPPricingPlanCard,
    "sbp-spinner": SBPSpinner,
    "sbp-imageupload-editor": ImageUploadEditor,
    "sbp-dropdown": SBPDropdown,
    "sbp-dropdown-toggle": SBPDropdownToggle,
    "sbp-dropdown-menu": SBPDropdownMenu,
    "sbp-dropdown-item": SBPDropdownItem,
    "sbp-total-shop-ss-chart": SBPTotalShopsSSChart,
    "sbp-resizable": SBPResizable,
    "sbp-report-settings-dialog": SBPReportSettingsDialog,
    "sbp-metric-summary-card": SBPMetricSummaryCard,
    "sbp-date-range": SBPDateRange,
    "sbp-test-chart-card-chart": SBPTestChartCardChart,
    "sbp-test-sales-card-drilldown": SBPTestSalesCardDrilldown,
    "sbp-test-kpi-card-drilldown": SBPTestKPICardDrilldown,
    "sbp-total-sales-drilldown": SBPTotalSalesDrilldown,
    "sbp-ro-sales-drilldown": SBPRoSalesFullReport,
    "sbp-payments-drilldown": SBPPaymentsDrilldown,
    "sbp-parts-sales-drilldown": SBPPartsSalesDrilldown,
    "sbp-parts-sales-category-drilldown": SBPPartsSalesCategoryDrilldown,
    "sbp-parts-profit-margin-drilldown": SBPPartsProfitMarginDrilldown,
    "sbp-revenue-summary-drilldown": SBPRevenueSummaryDrilldown,
    "sbp-ro-count-drilldown": SBPRoCountDrilldown,
    "sbp-aro-drilldown": SBPARODrilldown,
    "sbp-total-labor-hours-drilldown": SBPTotalLaborHoursDrilldown,
    "sbp-labor-hours-per-ro-drilldown": SBPLaborHoursPerRoDrilldown,
    "sbp-avg-hours-per-tech-drilldown": SBPAvgHoursPerTechDrilldown,
    "sbp-avg-ros-per-tech-drilldown": SBPAvgRosPerTechDrilldown,
    "sbp-tech-cost-per-labor-hour-drilldown": SBPTechCostPerLaborHourDrilldown,
    "sbp-labor-cost-as-percent-of-sales-drilldown": SBPLaborCostAsPercentOfSalesDrilldown,
    "sbp-parts-cost-as-percent-of-sales-drilldown": SBPPartsCostAsPercentOfSalesDrilldown,
    "sbp-touchtime-drilldown": SBPTouchtimeDrilldown,
    "sbp-effective-labor-rate-drilldown": SBPEffectiveLaborRateDrilldown,
    "sbp-gross-profit-drilldown": SBPGrossProfitDrilldown,
    "sbp-gross-profit-per-hour-drilldown": SBPGrossProfitPerHourDrilldown,
    "sbp-customer-concerns-by-category-drilldown": SBPCustomerConcernsByCategoryDrilldown,
    "sbp-shop-productivity-drilldown": SBPShopProductivityDrilldown,
    "sbp-closing-rate-drilldown": SBPClosingRateDrilldown,
    "sbp-customer-acquisition-rate-drilldown": SBPCustomerAcquisitionRateDrilldown,
    "sbp-new-existing-customers-sales-drilldown": SBPNewExistingCustomersSalesDrilldown,
    "sbp-total-sales-chart": SBPTotalSalesChart,
    "sbp-total-packages-chart": SBPTotalPackagesChart,
    "sbp-revenue-summary": SBPRevenueSummary,
    "sbp-customer-concerns-by-category": SBPCustomerConcernsByCategory,
    "sbp-labor-hours-per-tech": SBPLaborHoursPerTech,
    "sbp-aro-chart": SBPAROChart,
    "sbp-effective-labor-rate-chart": SBPEffectiveLaborRateChart,
    "sbp-gross-profit-chart": SBPGrossProfitChart,
    "sbp-labor-hours-per-tech-drilldown": SBPLaborHoursPerTechDrilldown,
    "sbp-livetext-card": SBPLivetextCard,
    "sbp-ai-writing-tool": AIWritingTool,

    ...dviComponents,
    draggable,
    ...vueCollapsible,
};

export const take = (componentNames) =>
    componentNames.reduce(
        (memo, name) => ({...memo, [name]: components[name]}),
        {}
    );
