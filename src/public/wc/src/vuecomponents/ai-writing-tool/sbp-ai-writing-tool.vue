<template>
  <div class="ai-writing-tool-container mb-2">
    <div>
      <div class="action-buttons">
        <button class="btn btn-secondary ai-button" @click="toggleAiTool" v-if="aiAccess">
            <i class="fa-solid fa-microchip-ai"></i>
        </button>
        <button class="btn btn-secondary record-button" @click="toggleRecording">
          <i :class="isRecording ? 'fas fa-microphone-slash' : 'fas fa-microphone'"></i>
        </button>
      </div>

      <div :class="{'form-outline': true, 'loading': isLoading}" ref="mdbInput">
        <textarea class="form-control" v-model="textContent" id="aiTextArea" :rows="rows" :placeholder="placeholder"></textarea>
        <label v-if="label" class="form-label" for="aiTextArea">{{label}}</label>
      </div>
    </div>

    <div class="ai-container" v-if="showAiTool" :class="{ 'ai__fade-in': showAiTool, 'ai__fade-out': !showAiTool }">
      <div class="ai-tool" :class="{ loading: isGenerating }">
        <div class="header">
          <span>Boss AI <span class="badge rounded-pill badge-danger">Beta</span></span>
          <div class="buttons">
            <span class="firstStepBTN" @click="resetToMainMenu">
              <i class="fa-light fa-arrow-left-from-arc"></i>
            </span>
            <span class="closeBTN ms-2" @click="hideAiTool">
              <i class="fa-regular fa-x"></i>
            </span>
          </div>
        </div>
        
        <div class="menu" id="menu">
          <!-- Main Menu -->
          <div class="menu__basic" v-show="currentMenu === 'basic'">
            <div class="menu-item" @click="generateSuggestion('improve')">
              <i class="fas fa-align-left"></i> Summarize Writing
            </div>
            <div class="menu-item" @click="generateSuggestion('summarize')">
              <i class="fas fa-magic"></i> Improve Writing
            </div>
            <div class="menu-item" @click="generateSuggestion('fix_spelling')">
              <i class="fas fa-spell-check"></i> Fix Spelling & Grammar
            </div>
            <div class="menu-item" @click="generateSuggestion('shorten')">
              <i class="fas fa-compress-alt"></i> Make Shorter
            </div>
            <div class="menu-item" @click="generateSuggestion('technical')">
              <i class="fas fa-cogs"></i> Make More Technical
            </div>
            <div class="menu-item" @click="generateSuggestion('adult')">
              <i class="fas fa-user-tie"></i> Simplify For Adult
            </div>
            <div class="menu-item" @click="currentMenu = 'tone'">
              <i class="fa-solid fa-volume-high"></i> Change Tone to
            </div>
            <div class="menu-item" @click="currentMenu = 'translate'">
              <i class="fas fa-language"></i> Translate to
            </div>
          </div>

          <!-- Tone Menu -->
          <div class="menu__tone" v-show="currentMenu === 'tone'">
            <div class="menu-item-wo" @click="currentMenu = 'basic'">
              <i class="fas fa-arrow-left"></i> Back
            </div>
            <div class="menu-item" @click="generateSuggestion('tone_professional')">
              <i class="fa-regular fa-circle"></i> Professional
            </div>
            <div class="menu-item" @click="generateSuggestion('tone_empathetic')">
              <i class="fa-regular fa-circle"></i> Empathetic
            </div>
            <div class="menu-item" @click="generateSuggestion('tone_casual')">
              <i class="fa-regular fa-circle"></i> Casual
            </div>
            <div class="menu-item" @click="generateSuggestion('tone_neutral')">
              <i class="fa-regular fa-circle"></i> Neutral
            </div>
            <div class="menu-item" @click="generateSuggestion('tone_educational')">
              <i class="fa-regular fa-circle"></i> Educational
            </div>
          </div>

          <!-- Translate Menu -->
          <div class="menu__translate" v-show="currentMenu === 'translate'">
            <div class="menu-item-wo" @click="currentMenu = 'basic'">
              <i class="fas fa-arrow-left"></i> Back
            </div>
            <div class="menu-item" @click="generateSuggestion('translate_spanish')">
              <i class="fa-solid fa-globe"></i> Spanish
            </div>
            <div class="menu-item" @click="generateSuggestion('translate_english')">
              <i class="fa-solid fa-globe"></i> English
            </div>
            <div class="menu-item" @click="generateSuggestion('translate_french')">
              <i class="fa-solid fa-globe"></i> French
            </div>
            <div class="menu-item" @click="generateSuggestion('translate_german')">
              <i class="fa-solid fa-globe"></i> German
            </div>
            <div class="menu-item" @click="generateSuggestion('translate_chinese')">
              <i class="fa-solid fa-globe"></i> Chinese
            </div>
            <div class="menu-item" @click="generateSuggestion('translate_japanese')">
              <i class="fa-solid fa-globe"></i> Japanese
            </div>
          </div>
        </div>

        <div class="ai-result" v-show="showResult">
          <p id="aiSuggestion">
            <span v-if="isGenerating" class="typing-animation">
              <span></span><span></span><span></span>Generating
            </span>
            <span v-else>{{ aiSuggestion || 'AI suggestion will appear here.' }}</span>
          </p>
          <button class="btn btn-primary replace" @click="replaceText">Replace</button>
          <button class="btn btn-secondary insert" @click="insertText">Insert Below</button>
          <div class="ai-footer">
            <span>AI can make mistakes, check your work before submitting. | Powered by Boss AI <span class="badge rounded-pill badge-danger">Beta</span></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'sbp-ai-writing-tool',
  props: {
    value: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: 'AI Writing Tool'
    },
    placeholder: String,
    rows: {
      type: Number,
      default: 4
    },
    endpoint: {
      type: String,
      default: '/src/public/js/plugins/ai-writing-tool/index.php'
    }
  },
  data() {
    return {
      textContent: this.value,
      showAiTool: false,
      currentMenu: 'basic',
      showResult: false,
      isGenerating: false,
      isLoading: false,
      aiSuggestion: '',
      currentAction: 'summarize',
      recognition: null,
      isRecording: false,
      aiAccess: false
    }
  },
  watch: {
    value(newVal) {
      this.textContent = newVal;
    },
    textContent(newVal) {
      this.$emit('input', newVal);
    }
  },
  methods: {
    toggleAiTool() {
      this.showAiTool = !this.showAiTool;
      if (this.showAiTool) {
        this.resetToMainMenu();
      }
    },

    hideAiTool() {
      this.showAiTool = false;
    },

    resetToMainMenu() {
      this.currentMenu = 'basic';
      this.showResult = false;
    },

    async generateSuggestion(action) {
      this.currentAction = action;
      this.showResult = true;
      this.isGenerating = true;
      this.currentMenu = 'result'
      
      try {
        const response = await fetch(this.endpoint, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ 
            user_text: this.textContent, 
            user_action: action 
          }),
        });
        
        const data = await response.json();
        this.aiSuggestion = data.content || "Please insert text.";
      } catch (error) {
        this.aiSuggestion = "Error: Unable to fetch AI suggestion.";
        console.error(error);
      } finally {
        this.isGenerating = false;
      }
    },

    replaceText() {
      if (this.aiSuggestion && this.aiSuggestion !== 'AI suggestion will appear here.') {
        this.textContent = this.aiSuggestion;
        this.hideAiTool();
      } else {
        alert('First you need to generate a suggestion!');
      }
    },

    insertText() {
      if (this.aiSuggestion && this.aiSuggestion !== 'AI suggestion will appear here.') {
        this.textContent += `\n\n${this.aiSuggestion}`;
        this.hideAiTool();
      } else {
        alert('First you need to generate a suggestion!');
      }
    },

    post(data, endpoint) {
        return new Promise((resolve, reject) => {
            $.ajax({
                type: 'POST',
                url: `${endpoint}`,
                dataType: 'json',
                data,
                success: function (obj, textstatus) {
                    resolve(obj);
                }
            });
        });
    },

    getAISettings(){
        this.post({}, 
        '/src/private/components/inspections/apis/get-ai-settings.php').then((response) => {
            this.aiAccess = response.aiAccess
        })
    },

    toggleRecording() {
      if (!this.recognition) return;
      this.isRecording ? this.stopRecording() : this.startRecording();
    },

    startRecording() {
      this.textContent = (this.textContent || "") + (this.textContent ? " " : "");
      setTimeout(() => {
        this.recognition.start();
        this.isRecording = true;
      }, 500);
    },

    stopRecording() {
      this.recognition.stop();
      this.isRecording = false;
    },

    updateMdbInput() {
      if (this.mdbInputInstance) {
        setTimeout(() => {
          this.mdbInputInstance.update();
        }, 10);
      }
    },

    initSpeechRecognition(){
      try {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        
        this.recognition = new SpeechRecognition();
        this.recognition.continuous = true;

        this.recognition.onresult = (event) => {
          const current = event.resultIndex;
          const transcript = event.results[current][0].transcript;
          const mobileRepeatBug = current === 1 && transcript === event.results[0][0].transcript;

          if (!mobileRepeatBug) {
            this.textContent += transcript;
          }

          if (this.timeout) {
            clearTimeout(this.timeout);
            this.timeout = null;
          }

          this.updateMdbInput()
        };
      } catch (e) {
        console.error(e);
        alert("Speech recognition is not supported in this browser.");
      }
    }
  },
  mounted() {
    // Initialise as MDB Input
    this.mdbInputInstance = new mdb.Input(this.$refs.mdbInput);
    this.mdbInputInstance.init();

    // Recorder
    this.initSpeechRecognition();

    this.getAISettings()
  }
}
</script>

<style scoped scss>
  :root {
    --bb: var(--bodybackground, white);
    --bodybackground: var(white);
  }

  .ai-writing-tool-container {
    position: relative;
  }

  .action-buttons{
    display: flex;
    align-items: center;
    justify-content: end;
    margin-bottom: .5rem;
    gap: .25rem;

    .btn{
      padding: 5px 15px;
    }

    .btn-secondary{
      font-size: .9rem;
      border: 1px solid var(--primary);
      color: var(--primary);
      transition: .3s;

      &:hover{
        background-color: var(--primary) !important;
        color: #fff !important;

        i{
          color: #fff !important;
        }
      }
    }
  }

  .textarea-wrapper {
    position: relative;
    border-radius: 12px;
    border: 2px solid #e0e0e0;
    transition: border-color 0.3s ease-in-out;
    &.loading {
      border: 2px solid transparent;
      background: linear-gradient(90deg, red, orange, yellow, green, cyan, blue, violet);
      background-size: 200% 200%;
      animation: rainbow-border 2s linear infinite;
    }
  }

  textarea {
    width: 100%;
    border: none;
    border-radius: 10px;
    resize: vertical;
    outline: none;
  }

  .ai-button,
  .record-button {
    z-index: 10;
    box-shadow: none;
    padding: 0;

    &:hover {
      box-shadow: none;
      color: #a01c1c;
    }

    i{
      font-size: 20px;
      width: 25px;
    }
  }

  .ai-container {
    position: absolute;
    width: 100%;
    z-index: 110;
  }

  .ai-tool {
    position: relative;
    margin: 0 auto;
    background-color: var(--bodybackground);
    box-shadow: 0 4px 8px -2px rgba(9, 30, 66, 0.31), 0 0 1px rgba(9, 30, 66, 0.31);
    border-radius: 12px;
    z-index: 110;
    margin-top: 10px;

    &.loading {
      position: relative;
      border: 1px solid transparent;

      &::before {
        content: "";
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        border-radius: 12px;
        border: 1px solid transparent;
        pointer-events: none;
        z-index: 1;
        background: linear-gradient(90deg, red, orange, yellow, green, cyan, blue, violet);
        background-size: 200% 200%;
        animation: rainbow-border 2s linear infinite;
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: exclude;
        -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
      }
    }

    .header {
      display: flex;
      justify-content: space-between;
      color: var(--textColor);
      padding: 6px 12px;
      border-bottom: 1px solid var(--textColor);
    }

    .ai-result {
      padding: 6px 12px;
    }

    .menu-item,
    .menu-item-wo {
      display: grid;
      grid-template-columns: 30px 1fr;
      align-items: center;
      position: relative;
      padding-right: 20px;
      padding: 6px 12px;
      color: var(--textColor);
      cursor: pointer;

      &::after {
        content: "";
        position: absolute;
        right: 10px;
        width: 0;
        height: 0;
        border-top: 5px solid transparent;
        border-left: 8px solid var(--textColor);
        border-bottom: 5px solid transparent;
        transition: transform 0.3s ease;
      }

      &:hover::after {
        transform: translateX(5px);
      }

      &:hover {
        background-color: rgba(223, 223, 223, 0.05);
      }
    }

    p,
    .ai-footer,
    .typing-animation {
      color: var(--textColor);
    }

    .fas,
    .fa-solid {
      color: var(--textColor);
    }
    
    .ai-footer {
      margin-top: 10px;
      border-top: 1px solid var(--textColor);
      padding: 2px 0;

      span {
        font-size: 12px;
        padding: 6px 12px;
      }
    }

    .badge-danger{
      color: #fff;
      background-color: var(--primary);
    }

    .firstStepBTN,
    .closeBTN{
      cursor: pointer;
    }
  }

  .typing-animation {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;

    span {
      width: 8px;
      height: 8px;
      background-color: #007BFF;
      border-radius: 50%;
      display: inline-block;
      animation: bounce 1.5s infinite ease-in-out;
      box-shadow: 0 0 10px rgba(0, 123, 255, 0.6);

      &:nth-child(1) {
        animation-delay: 0s;
      }
      &:nth-child(2) {
        animation-delay: 0.2s;
      }
      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }

  @keyframes bounce {
    0%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-6px);
    }
  }

  .ai__fade-in {
    animation: aiFadeIn 0.1s ease-out;
  }

  @keyframes aiFadeIn {
    0% {
      opacity: 0.2;
      transform: scale(0.6);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  .ai__fade-out {
    animation: aiFadeOut 0.1s ease-in forwards;
  }

  @keyframes aiFadeOut {
    0% {
      opacity: 1;
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(0);
      display: none;
    }
  }

  @keyframes rainbow-border {
    0% {
      background-position: 0% 50%;
    }
    100% {
      background-position: 100% 50%;
    }
  }
</style>