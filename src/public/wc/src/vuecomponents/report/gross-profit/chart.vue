<template>
  <sbp-metric-chart
    v-on="$listeners"
    v-bind="$attrs"
    name="Gross Profit"
    :getLabels="getLabels"
    :getDatasets="getDatasets"
  ></sbp-metric-chart>
</template>

<script>
import numeral from 'numeral';
import SBPSpinner from '../../sbp-spinner';
import SBPMetricChart from '../sbp-metric-chart';

export default {
  name: 'sbp-gross-profit-chart',
  components: {
    'sbp-metric-chart': SBPMetricChart
  },
  methods: {
    getLabels(data) {
      return Object.keys(data)
        .sort((a, b) => data[b].order - data[a].order)
        .map((key) => data[key].label);
    },
    getDatasets(data) {
      const keys = Object.keys(data).sort((a, b) => data[b].order - data[a].order);
      return [
        {
          label: 'Gross Profit',
          data: keys.map((key) => data[key].gp || 0),
          lineTension: 0.4,
          backgroundColor: 'rgba(0, 255, 0, 0.1)',
          borderColor: 'rgb(0, 255, 0)'
        },
      ];
    }
  }
}
</script>

<style>

</style>