<template>
  <div class="main">
    <div class="editor-container">
      <div class="editor" v-show="editable">
        <div class="current-color" :style="{ backgroundColor: color }"></div>

        <Tool :event="() => undo()" :iconClass="'fas fa-undo-alt fa-lg'" />

        <Tool :event="() => redo()" :iconClass="'fas fa-redo-alt fa-lg'" />

        <Tool
          :event="() => clearDrawing()"
          :iconClass="'fas fa-trash-alt fa-lg'"
        />

        <Tool
          :event="() => setTool('freeDrawing')"
          :iconClass="'fas fa-pencil-alt fa-lg'"
          :class="{ 'active-tool': currentActiveMethod === 'freeDrawing' }"
        />

        <Tool
          :event="() => setTool('text')"
          :iconClass="'fas fa-font fa-lg'"
          :class="{ 'active-tool': currentActiveMethod === 'text' }"
        />

        <Tool
          :event="() => setTool('circle')"
          :iconClass="'far fa-circle fa-lg'"
          :class="{ 'active-tool': currentActiveMethod === 'circle' }"
        />

        <Tool
          :event="() => setTool('rect')"
          :iconClass="'far fa-square fa-lg'"
          :class="{ 'active-tool': currentActiveMethod === 'rect' }"
        />

        <Tool
          :event="() => setTool('arrow')"
          :iconClass="'fas fa-long-arrow-alt-down fa-lg'"
          :class="{ 'active-tool': currentActiveMethod === 'arrow' }"
        />

        <Tool
          :event="() => setTool('selectMode')"
          :iconClass="'fas fa-arrows-alt fa-lg'"
          :class="{ 'active-tool': currentActiveMethod === 'selectMode' }"
        />

        <Tool
          :event="(e) => uploadImage(e)"
          :iconClass="'fas fa-file-upload fa-lg'"
          :labelForUploadImage="true"
        />

        <Tool :event="() => saveImage()" :iconClass="'fas fa-save fa-lg'" />
      </div>
      <Editor
        :canvasWidth="canvasWidth"
        :canvasHeight="canvasHeight"
        ref="editor"
        @image-uploaded="handleImageUploaded"
      />
    </div>
    <div class="colors" v-show="editable">
      <ColorPicker :color="'#e40000'" :event="changeColor" />
      <ColorPicker :color="'#e8eb34'" :event="changeColor" />
      <ColorPicker :color="'#a834eb'" :event="changeColor" />
      <ColorPicker :color="'#65c31a'" :event="changeColor" />
      <ColorPicker :color="'#34b7eb'" :event="changeColor" />
      <ColorPicker :color="'#eb34df'" :event="changeColor" />
      <ColorPicker :color="'#1a10ad'" :event="changeColor" />
      <ColorPicker :color="'#000000'" :event="changeColor" />
    </div>
  </div>
</template>

<script>
import Editor from "./components/vue-image-markup/Editor";
import Tool from "./components/Tool/Tool";
import ColorPicker from "./components/ColorPicker/ColorPicker";
//import "@fortawesome/fontawesome-free/css/all.css";
// import "@fortawesome/fontawesome-free/js/all.js";
export default {
  name: "simpleImageEditor",
  components: {
    ColorPicker,
    Tool,
    Editor,
  },
  data() {
    return {
      currentActiveMethod: null,
      params: {},
      color: "black",
      imageUrl: null,
      croppedImage: false,
      editable: false,
    };
  },
  props: {
    placeHolder: {
      type: String,
    },
    canvasWidth: {
      type: Number,
      default: 1200
    },
    event: {
      type: Function,
    },
    labelForUploadImage: {
      type: Boolean,
      default: false,
    },
    iconClass: {
      type: String,
    },
    canvasHeight: {
      type: Number,
      default: 1200
    },
    imgUrl: {
      type: String,
    },
  },
  mounted() {
    //this.setImageUrl(this.imageUrl?this.imageUrl:'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTCluTzCff_cHycJJ5k19lvXUozZOvZy4Mu-g&usqp=CAU');

    if (this.imageUrl) {
      this.$refs.editor.setBackgroundImage(this.imageUrl);
      this.croppedImage = this.$refs.editor.croppedImage;
    }
    this.$watch(
      () => {
        return this.$refs.editor.croppedImage;
      },
      (val) => {
        this.croppedImage = val;
      }
    );
  },
  watch: {},
  methods: {
    setEditable(v) {
      this.editable = v;
    },
    cropImage() {
      this.currentActiveMethod = "crop";
      this.setTool("crop");
    },
    applyCropping() {
      this.currentActiveMethod = "";
      this.$refs.editor.applyCropping();
    },
    changeColor(colorHex) {
      this.color = colorHex;
      this.$refs.editor.changeColor(colorHex);
    },
    saveImage() {
      let image = this.$refs.editor.saveImage();
      this.saveImageAsFile(image);
    },
    imageData() {
      return this.$refs.editor.saveImage();
    },
    saveImageAsFile(base64) {
      const byteString = atob(base64.split(',')[1]);
      const mimeString = base64.split(',')[0].split(':')[1].split(';')[0];
      const ab = new ArrayBuffer(byteString.length);
      const ia = new Uint8Array(ab);
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }
      const blob = new Blob([ab], { type: mimeString });
      const url = URL.createObjectURL(blob);
      let link = document.createElement("a");
      link.href = url;
      link.download = "image-markup.png";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    },
    setTool(type, params) {
      this.currentActiveMethod = type;
      this.$refs.editor.set(type, params);
    },
    setImageUrl(url) {
      this.imgUrl = url;
      this.imageUrl = this.imgUrl;
      this.$refs.editor.setBackgroundImage(this.imageUrl);

      this.$refs.editor.$once('background-image-set', (payload) => {
        this.$emit('background-image-loaded');
      });
    },
    setImageData(dataUrl) {
      // Use the image data directly instead of loading from URL
      this.imgUrl = dataUrl;
      this.imageUrl = dataUrl;
      this.$refs.editor.setBackgroundImageFromData(dataUrl);
    },
    uploadImage(e) {
      // Check if we have a valid editor reference
      if (!this.$refs.editor) {
        return;
      }

      // Check if we have a valid event with files
      if (e && e.target && e.target.files && e.target.files.length > 0) {
        // Make the editor component editable if it's not already
        this.setEditable(true);

        // Pass the event to the editor component
        this.$refs.editor.uploadImage(e);
      }
    },
    clear() {
      this.currentActiveMethod = this.clear;
      this.$refs.editor.clear();
    },
    clearDrawing() {
      this.clear();
    },
    undo() {
      this.currentActiveMethod = this.undo;
      this.$refs.editor.undo();
    },
    redo() {
      this.currentActiveMethod = this.redo;
      this.$refs.editor.redo();
    },

    // Debug method to load a test image
    loadTestImage() {
      console.log('Loading test image for debugging');
      const testImageUrl = '/src/private/components/inspections/apis/test-image.php';
      this.$refs.editor.logToServer('Loading test image', { testImageUrl });
      this.$refs.editor.setBackgroundImage(testImageUrl);
    },

    // Handle the image-uploaded event from the Editor component
    handleImageUploaded() {
      console.log('Image uploaded event received');
      // Emit an event to notify parent components that an image has been uploaded
      this.$emit('image-uploaded');
    }
  },
};
</script>

<style lang="scss">
.main {
  font-family: "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 10px;
  display: flex;
  justify-content: center;
  .editor-container {
    display: flex;
    flex-direction: column;
    height: 700px;
    width: 100%;

    @media (max-width: 600px) {
      height: 500px;
    }

    .editor {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;

      @media (max-width: 768px) {
        gap: 10px;
        margin-left: 58px;
      }

      .current-color {
        border-radius: 5px;
        min-width: 28px;
        min-height: 28px;
      }
      .active-tool {
        cursor: pointer;
        color: #4287f5;
      }
    }
  }

  .colors {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 10px 15px 0;
    transform: translateX(-20px);

    @media (max-width: 768px) {
      transform: translateX(-20px, 25px);
    }
  }
}

.custom-editor {
  margin-top: 20px;
  scale: 0.5;
  transform: translate(-250px,-600px);

  @media (max-width: 768px) {
    scale: 0.40;
    transform: translate(-300px,-760px);
  }

  @media (max-width: 600px) {
    scale: 0.30;
    transform: translate(-400px,-1400px)
  }

  @media (max-width: 480px) {
    scale: 0.2;
    transform: translate(-450px,-2200px)
  }
}

canvas {
  border: 1px solid #00000021;
}

@media (max-width: 992px) {
  .modal-dialog {
    max-width: 100%;
    margin: .5rem;
  }

  @media (max-width: 600px) {

  }
}
</style>