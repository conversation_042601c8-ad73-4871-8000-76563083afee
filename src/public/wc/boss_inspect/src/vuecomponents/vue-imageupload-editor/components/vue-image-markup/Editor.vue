<template>
  <div class="custom-editor">
    <canvas :id="editorId"></canvas>
  </div>
</template>

<script>
import { fabric } from "fabric";
import Shape from "./assets/js/shape";
import Text from "./assets/js/text";
import Arrow from "./assets/js/arrow";
import CropImage from "./assets/js/crop";
import CanvasHistory from "./assets/js/canvasHistory";
import { alertNegative } from '../../../../methods/alert.js';

export default {
  name: "Editor",
  props: {
    canvasWidth: {
      type: [String, Number],
      required: true,
    },
    canvasHeight: {
      type: [String, Number],
      required: true,
    },
    editorId: {
      type: [String, Number],
      default: "c",
      required: false,
    },
  },
  data() {
    return {
      canvas: null,
      pointerX: null,
      pointerY: null,
      createCircle: false,
      createRect: false,
      createArrow: false,
      createText: false,
      circle: null,
      currentActiveMethod: null,
      currentActiveTool: null,
      objects: [],
      width: null,
      height: null,
      params: {},
      color: "#000000",
      strokeWidth: 7,
      fontSize: 32,
      croppedImage: false,
      history: [],
    };
  },
  mounted() {
    this.canvas = new fabric.Canvas(this.editorId);
    this.canvas.setDimensions({
      width: this.canvasWidth,
      height: this.canvasHeight,
    });
    this.canvas.backgroundColor = "#fff";
    let canvasProperties = {
      width: this.canvas.width,
      height: this.canvas.height,
    };
    let currentCanvas = {
      json: this.canvas.toJSON(),
      canvas: canvasProperties,
    };
    new CanvasHistory(this.canvas, currentCanvas);
  },
  methods: {
    // Debug logging utility
    logToServer(message, details = {}) {
      try {
        // Add browser information
        details.userAgent = navigator.userAgent;
        details.url = window.location.href;

        // Send the log to our debug endpoint
        fetch('/src/private/components/inspections/apis/debug-log.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: message,
            details: details
          })
        }).catch(error => {
          console.error('Error sending debug log:', error);
        });
      } catch (e) {
        console.error('Error in logToServer:', e);
      }
    },

    getObjectsById(objectId) {
      let objects = this.canvas.getObjects();
      let findedObject = [];
      objects.map((object) => {
        if (object.id && object.id == objectId) {
          findedObject.push(object);
        }
      });
      return findedObject;
    },
    changeColor(colorProperty) {
      this.color = colorProperty;
      this.set(this.currentActiveTool);
    },
    setBackgroundImage(imageUrl, backgroundColor = "#000") {
      console.log("Loading image:", imageUrl);

      // Log to server for debugging
      this.logToServer('Starting image load', { imageUrl });

      // Show loading state in the canvas
      this.canvas.backgroundColor = "#f0f0f0";
      this.canvas.renderAll();

      // Create a loading indicator text
      const loadingText = new fabric.Text('Loading image...', {
        left: this.canvas.width / 2,
        top: this.canvas.height / 2,
        originX: 'center',
        originY: 'center',
        fontFamily: 'Arial',
        fontSize: 20,
        fill: '#333333'
      });
      this.canvas.add(loadingText);
      this.canvas.renderAll();

      // Check if this is a blob URL (from file uploads)
      const isBlobUrl = typeof imageUrl === 'string' && imageUrl.startsWith('blob:');

      // Check if this is already a proxy URL
      const isProxyUrl = typeof imageUrl === 'string' && imageUrl.includes('image-proxy-simple.php');

      // Determine which URL to use
      let urlToUse = imageUrl;

      // For blob URLs, use them directly - they're already in the browser
      if (isBlobUrl) {
        console.log('Using blob URL directly:', imageUrl);
        // No need to proxy blob URLs as they're already in the browser
      }
      // For non-blob, non-proxy URLs, create a proxy URL
      else if (!isProxyUrl && typeof imageUrl === 'string' && imageUrl.length > 0) {
        // Create a proxy URL
        urlToUse = `/src/private/components/inspections/apis/image-proxy-simple.php?url=${encodeURIComponent(imageUrl)}&_cb=${new Date().getTime()}`;
        console.log('Created proxy URL:', urlToUse);
      }

      // SIMPLIFIED APPROACH: Load the image directly
      const inst = this;

      // First, remove the loading text when we're done (success or failure)
      const cleanupLoading = () => {
        if (loadingText && loadingText.canvas) {
          inst.canvas.remove(loadingText);
          inst.canvas.renderAll();
        }
      };

      // Create a new image with explicit crossOrigin setting
      const img = new Image();
      img.crossOrigin = "anonymous"; // CRITICAL: This is required for S3 images to work in canvas

      // Set up success handler
      img.onload = function() {
        console.log("Image loaded successfully");
        inst.logToServer('Image loaded successfully', {
          imageUrl: urlToUse,
          width: img.width,
          height: img.height
        });
        cleanupLoading();

        // Now that the image is loaded, we can use it in the canvas
        try {
          // Create a fabric image from the loaded image
          const fabricImage = new fabric.Image(img);

          // Calculate the scaling to fit the canvas
          const rate = (fabricImage.height * 1.0) / fabricImage.width;
          const cw = inst.canvas.width;
          const ch = inst.canvas.height;
          let w, h, scale;

          if (cw * rate <= ch) {
            w = cw;
            h = cw * rate;
            scale = (cw * 1.0) / fabricImage.width;
          } else {
            h = ch;
            w = ch / rate;
            scale = (ch * 1.0) / fabricImage.height;
          }

          // Set the image properties
          fabricImage.scaleX = scale;
          fabricImage.scaleY = scale;
          fabricImage.top = (ch - h) / 2;
          fabricImage.left = (cw - w) / 2;

          // Set the background color and image
          inst.canvas.backgroundColor = "white";
          inst.canvas.setBackgroundImage(
            fabricImage,
            inst.canvas.renderAll.bind(inst.canvas)
          );

          // Save canvas state for history
          const canvasProperties = {
            width: inst.canvas.width,
            height: inst.canvas.height,
          };
          const currentCanvas = {
            json: inst.canvas.toJSON(),
            canvas: canvasProperties,
          };
          new CanvasHistory(inst.canvas, currentCanvas);

          inst.$emit('background-image-set');

          console.log("Image successfully set as background");
          inst.logToServer('Image successfully set as background', {
            imageUrl: urlToUse,
            canvasWidth: inst.canvas.width,
            canvasHeight: inst.canvas.height
          });
          inst.canvas.renderAll();
        } catch (e) {
          console.error("Error setting image as background:", e);
          inst.logToServer('Error setting image as background', {
            imageUrl: urlToUse,
            error: e.toString(),
            stack: e.stack
          });
          inst._showErrorMessage("Error loading image");
        }
      };

      // Set up error handler
      img.onerror = function(e) {
        console.error("Failed to load image with crossOrigin=anonymous:", e);
        inst.logToServer('Failed to load image with crossOrigin', {
          imageUrl: urlToUse,
          error: e ? e.toString() : 'Unknown error'
        });
        cleanupLoading();
        
        // If we tried an alternative URL and it failed, try the original
        if (urlToUse !== imageUrl && imageUrl && typeof imageUrl === 'string' && imageUrl !== 'undefined') {
          console.log('Alternative URL failed, trying original URL');
          const originalImg = new Image();
          originalImg.crossOrigin = "anonymous";
          originalImg.onload = function() {
            inst._processLoadedImage(originalImg);
          };
          originalImg.onerror = function() {
            console.error("All URL attempts failed");
            // Show error message
            alertNegative('Failed to load image');
            // Set a blank white background
            inst.canvas.backgroundColor = "#fff";
            inst.canvas.renderAll();
          };
          originalImg.src = imageUrl;
        } else {
          // If the URL is undefined or we're already using the original URL
          console.error("Cannot load image: URL is invalid or undefined");
          alertNegative('Failed to load image');
          // Set a blank white background
          inst.canvas.backgroundColor = "#fff";
          inst.canvas.renderAll();
        }
      };
      
      // Only attempt to load if URL is valid
      if (urlToUse && typeof urlToUse === 'string' && urlToUse !== 'undefined') {
        console.log("Starting image load with crossOrigin=anonymous:", urlToUse);
        img.src = urlToUse;
      } else {
        cleanupLoading();
        // Set a blank white background
        this.canvas.backgroundColor = "#fff";
        this.canvas.renderAll();
      }
    },

    setBackgroundImageFromData(dataUrl) {
      console.log("Setting background image from data URL");

      // Show loading state in the canvas
      this.canvas.backgroundColor = "#f0f0f0";
      this.canvas.renderAll();

      // Create a loading indicator text
      const loadingText = new fabric.Text('Loading image...', {
        left: this.canvas.width / 2,
        top: this.canvas.height / 2,
        originX: 'center',
        originY: 'center',
        fontFamily: 'Arial',
        fontSize: 20,
        fill: '#333333'
      });
      this.canvas.add(loadingText);
      this.canvas.renderAll();

      // Create a new image with proper CORS settings
      const img = new Image();
      img.crossOrigin = "anonymous";

      const inst = this;

      // Set up event handlers
      img.onload = function() {
        // Remove the loading text
        inst.canvas.remove(loadingText);

        // Process the loaded image
        inst._processLoadedImage(img);
      };

      img.onerror = function() {
        console.error("Failed to load image from data URL");
        inst.canvas.remove(loadingText);
        //   inst._showErrorMessage("Failed to load image");
        alertNegative('Failed to load image');
      };

      // Set the source to trigger loading
      img.src = dataUrl;
    },

    _processLoadedImage(img) {
      // Create a fabric image from the loaded image
      let image = new fabric.Image(img);

      // Calculate the scaling to fit the canvas
      let rate = (image.height * 1.0) / image.width;
      let cw = this.canvas.width,
          ch = this.canvas.height;
      let w, h;
      let scale;
      if (cw * rate <= ch) {
        w = cw;
        h = cw * rate;
        scale = (cw * 1.0) / image.width;
      } else {
        h = ch;
        w = ch / rate;
        scale = (ch * 1.0) / image.height;
      }

      // Set the image properties
      image.scaleX = scale;
      image.scaleY = scale;
      image.top = (ch - h) / 2;
      image.left = (cw - w) / 2;

      // Set the background color and image
      this.canvas.backgroundColor = "white";
      this.canvas.setBackgroundImage(
          image,
          this.canvas.renderAll.bind(this.canvas)
      );

      // Save canvas state for history
      let canvasProperties = {
        width: this.canvas.width,
        height: this.canvas.height,
      };
      let currentCanvas = {
        json: this.canvas.toJSON(),
        canvas: canvasProperties,
      };
      new CanvasHistory(this.canvas, currentCanvas);

      console.log("Image loaded successfully");
      this.canvas.renderAll();
    },

    _showErrorMessage(message) {
      this.canvas.backgroundColor = "#ffeeee";

      const errorText = new fabric.Text(message, {
        left: this.canvas.width / 2,
        top: this.canvas.height / 2,
        originX: 'center',
        originY: 'center',
        fontFamily: 'Arial',
        fontSize: 20,
        fill: '#cc0000'
      });

      this.canvas.add(errorText);
      this.canvas.renderAll();
    },
    toDataUrl(url, callback) {
      console.log('toDataUrl called with URL:', url);

      // For data URLs, we can use them directly
      if (url.startsWith('data:')) {
        console.log('URL is already a data URL, using directly');
        callback(url);
        return;
      }

      // For local blob URLs, we can use them directly
      if (url.startsWith('blob:')) {
        console.log('URL is a blob URL, using directly');
        callback(url);
        return;
      }

      // For remote URLs, we need to fetch them first
      console.log('Fetching remote URL with XHR:', url);
      let xhr = new XMLHttpRequest();
      xhr.onload = function() {
        console.log('XHR loaded successfully for URL:', url);
        let reader = new FileReader();
        reader.onloadend = () => {
          console.log('FileReader completed for URL:', url);
          callback(reader.result);
        };
        reader.readAsDataURL(xhr.response);
      };
      xhr.onerror = function(error) {
        console.error('XHR error loading image:', url, error);

        // Try an alternative approach for S3 URLs or any URL that failed
        console.log('Trying alternative Image object approach for URL:', url);
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.onload = function() {
          console.log('Alternative image load successful for URL:', url);
          const canvas = document.createElement('canvas');
          canvas.width = img.width;
          canvas.height = img.height;
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0);
          const dataUrl = canvas.toDataURL('image/jpeg');
          console.log('Canvas data URL created successfully');
          callback(dataUrl);
        };
        img.onerror = function(imgError) {
          console.error('Failed to load image with alternative method:', url, imgError);

          // Last resort: try with a direct image tag and no CORS
          console.log('Trying last resort approach without CORS for URL:', url);
          const imgNoCors = new Image();
          // No crossOrigin setting
          imgNoCors.onload = function() {
            console.log('No-CORS image load successful for URL:', url);
            try {
              const canvas = document.createElement('canvas');
              canvas.width = imgNoCors.width;
              canvas.height = imgNoCors.height;
              const ctx = canvas.getContext('2d');
              ctx.drawImage(imgNoCors, 0, 0);
              const dataUrl = canvas.toDataURL('image/jpeg');
              console.log('No-CORS canvas data URL created successfully');
              callback(dataUrl);
            } catch (canvasError) {
              console.error('Canvas error with no-CORS approach:', canvasError);
              // If we can't create a data URL, at least try to use the original URL
              callback(url);
            }
          };
          imgNoCors.onerror = function(noCorsError) {
            console.error('All approaches failed for URL:', url, noCorsError);
            // As a last resort, just pass back the original URL
            callback(url);
          };
          imgNoCors.src = url;
        };
        img.src = url;
      };
      xhr.open("GET", url);
      xhr.responseType = "blob";
      xhr.withCredentials = false; // Ensure credentials are not sent
      xhr.send();
    },
    clear() {
      this.canvas.clear();
      this.cancelCroppingImage();
    },
    set(type, params) {
      switch (type) {
        case "text":
          this.currentActiveTool = type;
          this.params = {
            fill: params && params.fill ? params.fill : this.color,
            fontFamily:
                params && params.fontFamily ? params.fontFamily : "Arial",
            fontSize:
                params && params.fontSize ? params.fontSize : this.fontSize,
            fontStyle:
                params && params.fontStyle ? params.fontStyle : this.fontStyle,
            fontWeight:
                params && params.fontWeight ? params.fontWeight : this.fontWeight,
            placeholder:
                params && params.placeholder ? params.placeholder : "Add Text",
            id: params && params.id ? params.id : "",
          };
          this.addText(this.params);
          break;

        case "circle":
          this.cancelCroppingImage();
          this.currentActiveTool = type;
          this.params = {
            fill: params && params.fill ? params.fill : "transparent",
            stroke: params && params.stroke ? params.stroke : this.color,
            strokeWidth:
                params && params.strokeWidth
                    ? params.strokeWidth
                    : this.strokeWidth,
            disableCircleEditing:
                params && params.disableCircleEditing
                    ? params.disableCircleEditing
                    : false,
            top: params && params.top ? params.top : 0,
            left: params && params.left ? params.left : 0,
            radius: params && params.radius ? params.radius : 20,
            strokeUniform:
                params && params.strokeUniform ? params.strokeUniform : true,
            noScaleCache:
                params && params.noScaleCache ? params.noScaleCache : false,
            strokeDashArray:
                params && params.strokeDashArray ? params.strokeDashArray : false,
            id: params && params.id ? params.id : "",
          };
          this.customCircle(type, this.params);
          break;
        case "rect":
          this.cancelCroppingImage();
          this.currentActiveTool = type;
          this.params = {
            fill: params && params.fill ? params.fill : "transparent",
            stroke: params && params.stroke ? params.stroke : this.color,
            strokeWidth:
                params && params.strokeWidth
                    ? params.strokeWidth
                    : this.strokeWidth,
            angle: params && params.angle ? params.angle : 0,
            width: params && params.width ? params.width : null,
            height: params && params.height ? params.height : null,
            top: params && params.top ? params.top : 0,
            left: params && params.left ? params.left : 0,
            opacity: params && params.opacity ? params.opacity : 1,
            strokeUniform:
                params && params.strokeUniform ? params.strokeUniform : true,
            noScaleCache:
                params && params.noScaleCache ? params.noScaleCache : false,
            strokeDashArray:
                params && params.strokeDashArray ? params.strokeDashArray : false,
            borderRadius:
                params && params.borderRadius ? params.borderRadius : 0,
            id: params && params.id ? params.id : "",
          };
          this.customRect(type, this.params);
          break;
        case "comment":
          this.cancelCroppingImage();
          this.currentActiveTool = type;
          this.params = {
            fill: params && params.fill ? params.fill : "transparent",
            stroke: params && params.stroke ? params.stroke : this.color,
            strokeWidth:
                params && params.strokeWidth
                    ? params.strokeWidth
                    : this.strokeWidth,
            angle: params && params.angle ? params.angle : 0,
            width: params && params.width ? params.width : null,
            height: params && params.height ? params.height : null,
            top: params && params.top ? params.top : 0,
            left: params && params.left ? params.left : 0,
            opacity: params && params.opacity ? params.opacity : 1,
            strokeUniform:
                params && params.strokeUniform ? params.strokeUniform : true,
            noScaleCache:
                params && params.noScaleCache ? params.noScaleCache : false,
            strokeDashArray:
                params && params.strokeDashArray ? params.strokeDashArray : false,
            borderRadius:
                params && params.borderRadius ? params.borderRadius : 0,
            id: params && params.id ? params.id : "",
          };
          this.customRect(type, this.params);
          break;
        case "line":
          this.cancelCroppingImage();
          this.currentActiveTool = type;
          this.params = {
            fill: params && params.fill ? params.fill : "transparent",
            stroke: params && params.stroke ? params.stroke : this.color,
            strokeWidth:
                params && params.strokeWidth
                    ? params.strokeWidth
                    : this.strokeWidth,
            angle: params && params.angle ? params.angle : 0,
            width: params && params.width ? params.width : null,
            height: params && params.height ? params.height : null,
            top: params && params.top ? params.top : 0,
            left: params && params.left ? params.left : 0,
            opacity: params && params.opacity ? params.opacity : 1,
            strokeUniform:
                params && params.strokeUniform ? params.strokeUniform : true,
            noScaleCache:
                params && params.noScaleCache ? params.noScaleCache : false,
            strokeDashArray:
                params && params.strokeDashArray ? params.strokeDashArray : false,
            id: params && params.id ? params.id : "",
          };
          this.customRect(type, this.params);
          break;
        case "selectMode":
          this.currentActiveTool = type;
          this.drag();
          break;

        case "arrow":
          this.currentActiveTool = type;
          this.params = {
            fill: params && params.fill ? params.fill : "transparent",
            stroke: params && params.stroke ? params.stroke : this.color,
            strokeWidth:
                params && params.strokeWidth
                    ? params.strokeWidth
                    : this.strokeWidth,
            strokeUniform:
                params && params.strokeUniform ? params.strokeUniform : true,
            noScaleCache:
                params && params.noScaleCache ? params.noScaleCache : false,
            strokeDashArray:
                params && params.strokeDashArray ? params.strokeDashArray : false,
            id: params && params.id ? params.id : "",
          };
          this.drawArrow(this.params);
          break;
        case "freeDrawing":
          this.currentActiveTool = type;
          this.params = {
            stroke: params && params.stroke ? params.stroke : this.color,
            strokeWidth:
                params && params.strokeWidth
                    ? params.strokeWidth
                    : this.strokeWidth,
            drawingMode:
                params && params.drawingMode ? params.drawingMode : true,
            id: params && params.id ? params.id : "",
          };
          this.drawing(this.params);
          break;
        case "crop":
          this.currentActiveTool = type;
          this.params = {
            width: params && params.width ? params.width : 200,
            height: params && params.height ? params.height : 200,
            overlayColor:
                params && params.overlayColor ? params.overlayColor : "#000",
            overlayOpacity:
                params && params.overlayOpacity ? params.overlayOpacity : 0.7,
            transparentCorner:
                params && params.transparentCorner
                    ? params.transparentCorner
                    : false,
            hasRotatingPoint:
                params && params.hasRotatingPoint
                    ? params.hasRotatingPoint
                    : false,
            hasControls:
                params && params.hasControls ? params.hasControls : true,
            cornerSize: params && params.cornerSize ? params.cornerSize : 10,
            borderColor:
                params && params.borderColor ? params.borderColor : "#000",
            cornerColor:
                params && params.cornerColor ? params.cornerColor : "#000",
            cornerStyle:
                params && params.cornerStyle ? params.cornerStyle : "circle",
            strokeColor:
                params && params.strokeColor ? params.strokeColor : "#000",
            lockUniScaling:
                params && params.lockUniScaling ? params.lockUniScaling : true,
            noScaleCache:
                params && params.noScaleCache ? params.noScaleCache : false,
            strokeUniform:
                params && params.strokeUniform ? params.strokeUniform : true,
          };
          this.currentActiveMethod = this.cropImage;
          this.drag();
          this.croppedImage = true;
          new CropImage(this.canvas, true, false, false, this.params);
          break;
        case "eraser":
          this.canvas.off("mouse:down");
          this.currentActiveTool = type;
          let inst = this;
          this.canvas.isDrawingMode = false;
          inst.selectable = true;
          this.canvas.on("mouse:down", function() {
            if (inst.canvas.getActiveObject()) {
              inst.canvas.remove(inst.canvas.getActiveObject());
              let canvasProperties = {
                width: inst.canvas.width,
                height: inst.canvas.height,
              };
              let currentCanvas = {
                json: inst.canvas.toJSON(),
                canvas: canvasProperties,
              };
              new CanvasHistory(inst.canvas, currentCanvas);
            }
          });
          break;
        default:
      }
    },
    saveImage() {
      this.cancelCroppingImage();
      return this.canvas.toDataURL("image/jpeg", 1);
    },
    uploadImage(e) {
      // Validate the event object
      if (!e || !e.target || !e.target.files || e.target.files.length === 0) {
        return;
      }

      const file = e.target.files[0];
      this.cancelCroppingImage();
      let inst = this;
      let reader = new FileReader();

      reader.onload = function(event) {
        let imgObj = new Image();
        imgObj.crossOrigin = "anonymous"; // CRITICAL: Set crossOrigin before setting src

        imgObj.onload = function() {
          let image = new fabric.Image(imgObj);

          if (inst.canvas.width <= image.width || inst.canvas.height <= image.height) {
            let canvasAspect = inst.canvas.width / inst.canvas.height;
            let imgAspect = image.width / image.height;
            let top, left, scaleFactor;

            if (canvasAspect >= imgAspect) {
              scaleFactor = inst.canvas.height / image.height;
              top = 0;
              left = -(image.width * scaleFactor - inst.canvas.width) / 2;
            } else {
              scaleFactor = inst.canvas.width / image.width;
              left = 0;
              top = -(image.height * scaleFactor - inst.canvas.height) / 2;
            }

            // Fix: was setting canvas = "black" which is incorrect
            inst.canvas.backgroundColor = "white";

            // Set the background image
            inst.canvas.setBackgroundImage(
                image,
                inst.canvas.renderAll.bind(inst.canvas),
                {
                  top: top,
                  left: left,
                  scaleX: scaleFactor,
                  scaleY: scaleFactor,
                }
            );

            let canvasProperties = {
              width: inst.canvas.width,
              height: inst.canvas.height,
            };

            let currentCanvas = {
              json: inst.canvas.toJSON(),
              croppedImage: inst.canvas.toDataURL(),
              canvas: canvasProperties,
            };

            new CanvasHistory(inst.canvas, currentCanvas);
            inst.canvas.renderAll();

            // Emit an event to notify that an image has been uploaded
            inst.$emit('image-uploaded');
          } else {
            let center = inst.canvas.getCenter();

            // Set the background image
            inst.canvas.setBackgroundImage(
                image,
                inst.canvas.renderAll.bind(inst.canvas),
                {
                  top: center.top,
                  left: center.left,
                  originX: "center",
                  originY: "center",
                }
            );

            let canvasProperties = {
              width: inst.canvas.width,
              height: inst.canvas.height,
            };

            let currentCanvas = {
              json: inst.canvas.toJSON(),
              croppedImage: inst.canvas.toDataURL(),
              canvas: canvasProperties,
            };

            new CanvasHistory(inst.canvas, currentCanvas);
            inst.canvas.renderAll();

            // Emit an event to notify that an image has been uploaded
            inst.$emit('image-uploaded');
          }
        };

        // Set the source to trigger loading
        imgObj.src = event.target.result;
      };

      // Start reading the file
      reader.readAsDataURL(file);
    },
    customCircle(type, params) {
      this.createArrow = false;
      new Arrow(this.canvas, false);
      this.currentActiveMethod = this.customCircle;
      this.createRect = false;
      this.canvas.isDrawingMode = false;
      if (!params.disableCircleEditing) {
        this.createCircle = true;
        new Shape(this.canvas, this.createCircle, type, params);
      } else {
        this.drawCircle(params);
      }
    },
    customRect(type, params) {
      this.createArrow = false;
      new Arrow(this.canvas, false);
      this.currentActiveMethod = this.customRect;
      this.canvas.isDrawingMode = false;
      this.createCircle = false;
      if (params.width && params.height) {
        this.drawRect(params);
      } else {
        this.createRect = true;
        new Shape(this.canvas, this.createRect, type, params);
      }
    },
    drawArrow(params) {
      this.currentActiveMethod = this.drawArrow;
      this.drag();
      this.createArrow = true;
      new Arrow(this.canvas, this.createArrow, params);
    },
    cancelCroppingImage() {
      this.croppedImage = false;
      new CropImage(this.canvas, false, false, true);
    },
    applyCropping() {
      new CropImage(this.canvas, true, true);
      this.cancelCroppingImage();
    },
    drag() {
      this.currentActiveMethod = this.drag;
      this.canvas.isDrawingMode = false;
      this.canvas.forEachObject((object) => {
        object.selectable = true;
        object.evented = true;
      });
      if (this.createArrow) {
        this.createArrow = false;
        new Arrow(this.canvas, false);
      }
      if (this.createRect || this.createCircle) {
        this.createRect = false;
        this.createCircle = false;
        new Shape(this.canvas, false);
      }
      if (this.createText) {
        this.createText = false;
        new Text(this.canvas, false);
      }
      this.cancelCroppingImage();
    },
    addText(params) {
      this.currentActiveMethod = this.addText;
      this.drag();
      this.createText = true;
      new Text(this.canvas, this.createText, params);
    },
    clearDrawing() {
      clear();
    },
    undo() {
      if (this.canvas.getActiveObject()) {
        this.canvas.discardActiveObject().renderAll();
      }
      this.drag();
      this.history = new CanvasHistory();
      if (this.history.length) {
        this.objects.push(this.history.pop());
        if (this.history[this.history.length - 1]) {
          if (this.history[this.history.length - 1].canvas) {
            let lastCanvasProperties = this.history[this.history.length - 1]
                .canvas;
            if (
                lastCanvasProperties.width != this.canvas.width ||
                lastCanvasProperties.height != this.canvas.height
            ) {
              this.canvas.setDimensions({
                width: lastCanvasProperties.width,
                height: lastCanvasProperties.height,
              });
              JSON.parse(JSON.stringify(this.history[this.history.length - 1]));
              this.canvas.loadFromJSON(
                  this.history[this.history.length - 1].json
              );
            } else {
              let canvasObjects = this.history[this.history.length - 1].json
                  .objects;
              if (this.canvas._objects.length > 0) {
                this.objects.push(this.canvas._objects.pop());
              }
            }
          }

          if (
              this.history[this.history.length - 1].croppedImage &&
              this.history[this.history.length - 1].imagePosition
          ) {
            let inst = this;
            fabric.Image.fromURL(
                this.history[this.history.length - 1].croppedImage,
                function(img) {
                  img.set({
                    top: -inst.history[inst.history.length - 1].imagePosition.top,
                    left: -inst.history[inst.history.length - 1].imagePosition
                        .left,
                  });
                  inst.canvas.setBackgroundImage(
                      img,
                      inst.canvas.renderAll.bind(inst.canvas)
                  );
                },
                // CRITICAL: Add crossOrigin option for fabric.Image.fromURL
                { crossOrigin: 'anonymous' }
            );
          } else {
            this.setBackgroundImage(
                this.history[this.history.length - 1].croppedImage
            );
          }
          this.canvas.renderAll();
        }
      }
      return this.history.length;
    },
    redo() {
      this.drag();
      if (this.objects.length > 0) {
        if (this.objects[this.objects.length - 1]) {
          if (
              this.objects[this.objects.length - 1].canvas &&
              !this.objects[this.objects.length - 1].type
          ) {
            let lastCanvasProperties = this.objects[this.objects.length - 1]
                .canvas;
            if (
                lastCanvasProperties.width != this.canvas.width ||
                lastCanvasProperties.height != this.canvas.height
            ) {
              this.canvas.setDimensions({
                width: lastCanvasProperties.width,
                height: lastCanvasProperties.height,
              });
            }
            JSON.parse(JSON.stringify(this.objects[this.objects.length - 1]));
            this.canvas.loadFromJSON(
                this.objects[this.objects.length - 1].json
            );
          } else if (this.objects[this.objects.length - 1].type) {
            this.canvas.add(this.objects.pop());
          }
          if (
              this.objects[this.objects.length - 1].imagePosition &&
              this.objects[this.objects.length - 1].croppedImage
          ) {
            let currentProperties;
            currentProperties = this.objects[this.objects.length - 1]
                .imagePosition;
            let inst = this;
            fabric.Image.fromURL(
                this.objects[this.objects.length - 1].croppedImage,
                function(img) {
                  inst.canvas.setBackgroundImage(
                      img,
                      inst.canvas.renderAll.bind(inst.canvas)
                  );
                },
                // CRITICAL: Add crossOrigin option for fabric.Image.fromURL
                { crossOrigin: 'anonymous' }
            );
          }
        }
        new CanvasHistory(false, false, this.objects.pop());
      }
    },
    drawing(params) {
      if (this.canvas.__eventListeners) {
        this.canvas.__eventListeners["object:added"] = null;
      }
      this.currentActiveMethod = this.drawing;
      this.drag();
      this.canvas.isDrawingMode = params.drawingMode;
      this.canvas.freeDrawingBrush.color = params.stroke;
      this.canvas.freeDrawingBrush.width = params.strokeWidth;
      this.canvas.freeDrawingBrush.shadow = new fabric.Shadow({
        blur: 0,
        affectStroke: true,
        color: params.stroke,
        id: params.id ? params.id : "",
      });
      let inst = this;
      this.canvas.on("object:added", function() {
        if (inst.canvas.isDrawingMode) {
          let canvasProperties = {
            width: inst.canvas.width,
            height: inst.canvas.height,
          };
          let currentCanvas = {
            json: inst.canvas.toJSON(),
            canvas: canvasProperties,
          };
          new CanvasHistory(inst.canvas, currentCanvas);
        }
      });
      this.canvas.renderAll();
    },
    drawRect(params) {
      this.drag();
      this.canvas.discardActiveObject();
      if (!this.canvas.getActiveObject()) {
        this.rectangle = new fabric.Rect({
          width: params.width,
          height: params.height,
          strokeWidth: params.strokeWidth,
          stroke: params.stroke,
          fill: params.fill,
          opacity: params.opacity,
          left: params.left,
          top: params.top,
          noScaleCache: params.noScaleCache,
        });
        this.canvas.add(this.rectangle);
      }
    },
    drawCircle(params) {
      this.drag();
      this.canvas.discardActiveObject();
      this.circle = new fabric.Circle({
        left: params.left,
        top: params.top,
        radius: params.radius,
        strokeWidth: params.strokeWidth,
        stroke: params.stroke,
        fill: params.fill,
        borderColor: "yellow",
        noScaleCache: params.noScaleCache,
      });
      this.canvas.add(this.circle);

      this.canvas.renderAll();
    },
  },
};
</script>
<style>
.upper-canvas {
  z-index: 1;
}
</style>
