<template>
  <div class="tool">
    <div @click="handleToolClick" v-if="!labelForUploadImage">
      <i :class="iconClass"></i>
    </div>
    <div v-if="labelForUploadImage" class="upload-container">
      <label class="upload-label">
        <i :class="iconClass"></i>
        <input
          type="file"
          ref="fileInput"
          accept="image/*"
          @change="handleFileChange"
          class="file-input"
        />
      </label>
    </div>
  </div>
</template>

<script>
export default {
  name: "Tool",
  props: ["event", "iconClass", "labelForUploadImage"],
  data() {
    return {
      isProcessing: false
    };
  },
  methods: {
    handleToolClick(e) {
      if (typeof this.event === 'function') {
        this.event(e);
      }
    },
    
    handleFileChange(e) {
      // Prevent multiple processing
      if (this.isProcessing) return;
      this.isProcessing = true;
      
      // Process the file only if one was selected
      if (e.target.files.length > 0) {
        if (typeof this.event === 'function') {
          this.event(e);
        }
      }
      
      // Reset after processing
      setTimeout(() => {
        this.isProcessing = false;
        e.target.value = ''; // Reset input to allow same file to be selected again
      }, 300);
    }
  }
};
</script>

<style scoped lang="scss">
.tool {
  align-items: center;
  justify-content: space-between;
  display: flex;
  font-size: 16px;
  padding: 2px 5px;
  
  &:hover {
    cursor: pointer;
    color: #4287f5;
  }
}

.upload-container {
  position: relative;
}

.upload-label {
  cursor: pointer;
  display: inline-block;
  
  &:hover i {
    color: #4287f5;
  }
}

.file-input {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
</style>