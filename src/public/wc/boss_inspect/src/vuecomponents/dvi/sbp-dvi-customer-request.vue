<template>
  <div v-if="mode === 'edit'" class="p-2">
    <sbp-ai-writing-tool 
      v-model="reasonForVisit" 
      label="Customer Concern" 
      rows="3">
    </sbp-ai-writing-tool>

    <sbp-ai-writing-tool 
      v-model="note" 
      label="Note" 
      rows="3">
    </sbp-ai-writing-tool>

    <div class="bg-light mt-4">
      <div class="row g-2">
        <div
            class="col-3 text-center"
            v-for="image in images"
            v-show="!image.invisible"
            :key="image.id"
        >
          <sbp-dvi-image-wrapper
              :imgInfo="image"
              imgClass="w-100"
              :updator="() => (image.invisible = false)"
              :update_url="
              'src/private/components/inspections/apis/inspection-customer-request-images/update-image.php'
            "
              :deletor="() => $emit('delete-image', image)"
              :imgUrl="image.image_url"
              editable="true"
              displayInfo="false"
          />
          <button
              type="button"
              class="btn btn-danger mt-1"
              @click="$emit('delete-image', image)"
          >
            <i class="fas fa-trash"></i>
          </button>
        </div>
        <div class="col-3">
          <input
              class="d-none"
              type="file"
              name="file"
              ref="fileInput"
              accept="image/*,android/force-camera-workaround"
              @change="handleFileChange"
          />
          <div class="upload-zone no-print" @click="handleTriggerUpload" title="Image Upload">
            <i class="fas fa-plus"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-else-if="mode === 'prepare'"></div>
</template>

<script>
import SBPModalBase from "../sbp-modal-base";
import SbpDviImageWrapper from "./sbp-dvi-image-wrapper.vue";
import AIWritingTool from "../../../../src/vuecomponents/ai-writing-tool/sbp-ai-writing-tool.vue";

export default {
  props: {
    mode: String,
    reasonForVisit: String,
    note: String,
    images: Array,
  },

  watch: {
    reasonForVisit(newReason){
      this.$emit('set-visit-reason', newReason)
    },
    
    note(newNote){
      this.$emit('set-note', newNote)
    }
  },

  components: {
    "sbp-modal-base": SBPModalBase,
    "sbp-dvi-image-wrapper": SbpDviImageWrapper,
    "sbp-ai-writing-tool" : AIWritingTool
  },

  methods: {
    handleFileChange() {
      const files = this.$refs.fileInput.files;
      this.$emit("add-image", files[0]);
      Vue.prototype.$current_finput = this.$refs.fileInput;
    },

    handleTriggerUpload() {
      this.$refs.fileInput.click();
    }
  },
};
</script>

<style scoped lang="scss">
  .upload-zone {
    border: grey dashed;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    min-height: 200px;

    &:hover {
      border: lightgray dashed;
    }
  }
</style>
