<template>
  <div class="row">
    <div class="col-xxl-3 col-xl-12 inspect-dropdown">
      <div class="form-check form-check-inline mt-2 none-item">
        <input
          class="form-check-input"
          type="radio"
          :name="id"
          :id="`${id}-none`"
          :checked="!status"
          @change="handleChangeStatus('none')"
        />
        <label class="form-check-label" :for="`${id}-none`">None</label>
      </div>
      <div class="form-check form-check-inline mt-2 good-item">
        <input
          class="form-check-input"
          type="radio"
          :name="id"
          :id="`${id}-good`"
          :checked="status === 'good'"
          @change="handleChangeStatus('good')"
        />
        <label class="form-check-label" :for="`${id}-good`">Good</label>
      </div>
      <div class="form-check form-check-inline mt-2 warning-item">
        <input
          class="form-check-input"
          type="radio"
          :name="id"
          :id="`${id}-warning`"
          :checked="status === 'warning'"
          @change="handleChangeStatus('warning')"
        />
        <label class="form-check-label" :for="`${id}-warning`">Warning</label>
      </div>
      <div class="form-check form-check-inline mt-2 bad-item">
        <input
          class="form-check-input"
          type="radio"
          :name="id"
          :id="`${id}-critical`"
          :checked="status === 'critical'"
          @change="handleChangeStatus('critical')"
        />
        <label class="form-check-label" :for="`${id}-critical`">Bad</label>
      </div>

      <div class="d-flex no-print mt-3" v-if="status && status !== 'none'">
        <div class="position-relative">
          <button
            type="button"
            class="btn btn-primary"
            @click="handleOpenImagesDialog"
          >
            <i class="fas fa-camera"></i>
          </button>
          <span
            class="badge bg-success position-absolute"
            style="right: -4px; top: -4px;"
            v-if="input && input.images && input.images.length"
          >{{ input.images.length }}</span
          >
        </div>
        <div class="position-relative ml-2">
          <button
            type="button"
            class="btn btn-primary"
            @click="handleOpenTextDialog"
          >
            <i class="fas fa-pencil-alt"></i>
          </button>
          <span
            class="badge bg-success position-absolute"
            style="right: -4px; top: -4px;"
            v-if="input && input.technician_notes"
          >1</span
          >
        </div>
      </div>
    </div>
    <div class="col-xxl-4 col-xl-12 inspect-dropdown">
      <div v-if="status && status !== 'good' && status !== 'none'">

        <select
          ref="findingsSelect"
          class="select invisible"
          :value="selectedFindingId"
        >
          <option
            :value="finding.id"
            v-for="(finding, index) in enhancedFindings"
            v-bind:key="index"
          >{{ finding.description }}
          </option
          >
        </select>
        <label class="form-label select-label">Finding</label>
        <div
          class="form-outline"
          ref="findingOtherInput"
          v-if="!selectedFindingId"
        >
          <input
            type="text"
            class="form-control"
            v-model="findingOther"
            @blur="handleBlurOther"
          />
        </div>
      </div>
    </div>
    <div class="col-xxl-4 col-xl-12 inspect-dropdown">
      <div v-if="status && status !== 'good' && status !== 'none'">       
        <select
          ref="recommendationsSelect"
          class="select invisible"
          :value="selectedRecommendationId"
          v-if="selectedFindingId"
        >
          <option
            v-if="selectedFindingId"
            :value="recommendation.id"
            v-for="(recommendation, index) in enhancedRecommendations"
            v-bind:key="index">
            {{ recommendation.description }}
          </option
          >
        </select>
        <select v-else ref="recommendationsSelect" class="select invisible">
          <option value="Other">Other</option>
        </select>

        <label class="form-label select-label">Recommendation</label>
        <div
          class="form-outline"
          ref="recommendationOtherInput"
          v-if="!selectedRecommendationId"
        >
          <input
            type="text"
            class="form-control"
            v-model="recommendationOther"
            @blur="handleBlurOther"
          />
        </div>
      </div>
    </div>

    <hr class="my-2"/>

    <sbp-modal-base
      title="Upload"
      size="lg"
      ref="finding-images-dialog"
      :show-close="false"
    >
    <template #body>
      <div class="custom-upload-container">
        <div
          class="text-center"
          v-for="(image, index) in processedImages"
          v-bind:key="image.id"
        >
          <sbp-dvi-image-wrapper
            ref="findingImage{{index}}"
            v-if="image.type == 'img'"
            :imgInfo="image"
            @openEditor="editImage()"
            imgClass="h-250"
            :imgUrl="image.image_url"
            :editable="true"
            :displayInfo="true"
            :update_url="
              'src/private/components/inspections/apis/inspection-items/update-finding-image.php'
            "
          />

          <iframe
            v-if="image.type == 'vid'"
            :src="image.image_url"
            class="w-100"
            style="aspect-ratio: 16 / 9;"
            frameborder="0"
            allowfullscreen
            referrerpolicy="no-referrer-when-downgrade"
          ></iframe>

          <div class="dvi-action-button">
            <button
              type="button"
              class="btn btn-danger btn-sm"
              @click="$emit('delete-image', image)"
            >
              <i class="fas fa-trash"></i> Delete
            </button>
          </div>
        </div>

        <div class="without-shadows">
          <input
            class="d-none"
            type="file"
            name="file"
            ref="fileInput"
            accept="image/*,android/force-camera-workaround"
            multiple
            :key="photoInputKey"
            @change="handleFileChange"
          />

          <div class="upload-zone" @click="handleTriggerUpload">
            <div v-if="isPhotoUploading" class="upload-loading text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <div class="mt-2">Uploading...</div>
            </div>
            <div v-else class="text-center">
              <i class="fas fa-plus"></i><div>Photos</div>
            </div>
          </div>
        </div>

        <div class="without-shadows">
          <input
            class="d-none"
            type="file"
            name="videofile"
            ref="videoInput"
            accept="video/*,android/force-camera-workaround"
            :key="fileInputKey"
            @change="handleVideoChange"
          />

          <div class="upload-zone" @click="handleTriggerVideoUpload">
            <div v-if="isVideoUploading" class="upload-loading text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <div class="mt-2">Uploading...</div>
            </div>
            <div v-else class="text-center">
              <i class="fas fa-plus"></i><div>Videos</div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="$current_inspection.ro_images && $current_inspection.ro_images.length > 0" v-show="false">
        <hr class="mt-4" />
        <div class="text-center">
          <a href="javascript:void(null)" @click="displayRoImages = !displayRoImages">Import from RO</a>
        </div>
        <div class="row g-2 mt-2" v-show="displayRoImages">
          <div
            class="col-6 col-sm-4 col-md-3 col-lg-2 text-center"
            v-for="(image, index) in $current_inspection.ro_images"
            v-bind:key="image.id"
          >
            <div>
              <img
                class="w-100"
                :src="`${$imageBaseUrl+'/sbp/upload/'+image.shopid+'/'+image.roid+'/'+image.picname}`"
              />
            </div>
            <input type="checkbox" class="form-check-input mt-1" v-model="importArr" :value="image.id" />
          </div>
        </div>
        <div class="text-center">
          <sbp-button
            color="primary"
            icon="file-import"
            :loading="loadingImportImg"
            v-show="displayRoImages"
            @click="handleImport"
            size="sm"
            className="mt-4"
          >
            IMPORT
          </sbp-button>
        </div>
      </div>
    </template>

    </sbp-modal-base>
    <sbp-modal-base
      title="Technician Notes"
      ref="finding-text-dialog"
      :show-close="false"
    >
      <template #body>
        <sbp-ai-writing-tool 
          v-model="technicianNotes"
          label="Tech Note"
          rows="4">
        </sbp-ai-writing-tool>
      </template>
    </sbp-modal-base>
  </div>
</template>

<script>
import SBPModalBase from "../sbp-modal-base";
import SbpDviImageWrapper from "./sbp-dvi-image-wrapper.vue";
import SBPButton from "../sbp-button";
import AIWritingTool from "../../../../src/vuecomponents/ai-writing-tool/sbp-ai-writing-tool.vue";

export default {
  name: "sbp-dvi-finding-input",
  props: {
    itemId: Number,
    findings: Array,
    input: Object,
  },
  components: {
    "sbp-button": SBPButton,
    "sbp-modal-base": SBPModalBase,
    "sbp-dvi-image-wrapper": SbpDviImageWrapper,
    "sbp-ai-writing-tool" : AIWritingTool
  },

  data() {
    return {
      id: null,
      status: null,
      selectedFindingId: null,
      selectedRecommendationId: null,
      recommendations: [],
      enhancedFindings: [],
      enhancedRecommendations: [],
      findingOther: null,
      recommendationOther: null,
      technicianNotes: null,
      displayRoImages: false,
      loadingImportImg: false,
      importArr: [],
      recognitionHandler: null,
      isRecording: false,
      isPhotoUploading: false,
      isVideoUploading: false,
      fileInputKey: 0,
      photoInputKey: 0,
    };
  },
  created() {
    this.enhancedFindings = [{id: -1, description: "Multiple"}]
      .concat(this.findings)
      .concat({id: null, description: "Other"});

    if (this.input) {
      this.id = `item-${this.itemId}-finding-input-${this.input.id}`;
      this.findingOther = this.input.finding_other;
      this.recommendationOther = this.input.recommendation_other;
      this.handleChangeInput(
        this.input.status,
        this.input.finding_id,
        this.input.recommendation_id,
        false
      );
      this.technicianNotes = this.input.technician_notes ?? '';
    } else {
      this.id = `item-${this.itemId}-undetermined-finding`;
    }
  },
  beforeDestroy() {
    if (this.recognitionHandler) {
      this.recognitionHandler.abort();
    }
  },
  computed: {
    roImages() {
      return Vue.prototype.$current_inspection.ro_images;
    },
    processedImages() {
      // Return a safe array of images, ensuring we don't have undefined or null values
      if (!this.input || !this.input.images) return [];
      return this.input.images.filter(img => img && img.id && img.image_url);
    }
  },
  methods: {
    editImage() {
      this.$refs.editDialog.open();
    },
    handleBlurOther() {
      this.$emit(
        "input",
        this.status,
        this.selectedFindingId,
        this.selectedRecommendationId,
        this.findingOther,
        this.recommendationOther
      );
    },
    handleChangeInput(status, findingId, recommendationId, emit = true) {
      const previousStatus = this.status;
      this.status = status;
      if ( status !== "good" && status !== 'none' && (!previousStatus || previousStatus === "good" || previousStatus === this.status)) {
        setTimeout(() => {
          this.selectedFindingId = findingId;

          if (typeof findingId === "number") {
            this.recommendations = this.findings.find(
              (f) => f.id === findingId
            ).recommendations;
            if (recommendationId) {
              setTimeout(() => {
                this.selectedRecommendationId = recommendationId;
              }, 0);
            } else {
              this.selectedRecommendationId =
                this.recommendations.length > 0
                  ? this.recommendations[0].id
                  : null;

              if ( this.input && !this.input.recommendation_id ){
                this.selectedRecommendationId = null;
              }
            }
          } else {
            this.recommendations = [];
            this.selectedRecommendationId = null;
          }
          setTimeout(() => {
            if (this.selectedFindingId === null) {
              new mdb.Input(this.$refs.findingOtherInput).init();
            }

            if (this.selectedRecommendationId === null) {
              new mdb.Input(this.$refs.recommendationOtherInput).init();
            }
          }, 0);

          if (emit) {
            this.$emit(
              "input",
              this.status,
              this.selectedFindingId,
              this.selectedRecommendationId,
              this.findingOther,
              this.recommendationOther
            );
          }

          this.enhancedRecommendations = this.recommendations.concat({
            id: null,
            description: "Other",
          });
          if (this.recommendations.length == 0) {
            this.enhancedRecommendations = [
              {id: null, description: 'Diagnose Problem'},
              ...this.enhancedRecommendations
            ]
          }

          const findingSelectEl = this.$refs.findingsSelect;
          const recommendationsSelectEl = this.$refs.recommendationsSelect;

          if (!mdb.Select.getInstance(findingSelectEl)) {
            new mdb.Select(findingSelectEl);
            new mdb.Select(recommendationsSelectEl);

            findingSelectEl.classList.remove("invisible");
            recommendationsSelectEl.classList.remove("invisible");

            findingSelectEl.addEventListener("valueChange.mdb.select", () => {
              const elVal = findingSelectEl.value;

              if (elVal !== "-1") {
                this.handleChangeInput(
                  this.status,
                  elVal !== "" ? parseInt(elVal, 10) : null
                );
              } else {
                const mdbInstance = mdb.Select.getInstance(findingSelectEl);
                mdbInstance.setValue(`${this.findings[0].id}`);
                this.$emit("select-multiple", this.status);
              }
            });

            recommendationsSelectEl.addEventListener(
              "valueChange.mdb.select",
              () => {
                const elVal = recommendationsSelectEl.value;
                this.selectedRecommendationId =
                  elVal !== "" ? parseInt(elVal) : null;

                if (this.selectedRecommendationId === null) {
                  setTimeout(() => {
                    new mdb.Input(this.$refs.recommendationOtherInput).init();
                  }, 0);
                } else
                  emit = true

                if (emit) {
                  this.$emit(
                    "input",
                    this.status,
                    this.selectedFindingId,
                    this.selectedRecommendationId,
                    this.findingOther,
                    this.recommendationOther
                  );
                }
              }
            );
          }
        }, 0);
      } else if (emit) {
        this.$emit(
          "input",
          this.status,
          this.selectedFindingId,
          this.selectedRecommendationId,
          this.findingOther,
          this.recommendationOther
        );
      }
    },
    handleChangeStatus(newStatus) {
      if ((!this.status || this.status === "good") && newStatus !== "good" && newStatus !== "none") {
        if (this.input) {
          this.handleChangeInput(newStatus, this.input.finding_id);
        } else {
          this.handleChangeInput(
            newStatus,
            this.findings.length > 0 ? this.findings[0].id : null
          );
        }
      } else {
        this.handleChangeInput(newStatus);
      }
    },
    handleOpenImagesDialog() {
      this.$refs["finding-images-dialog"].open();
    },
    handleOpenTextDialog() {
      this.$refs["finding-text-dialog"].open();
      setTimeout(() => {
        new mdb.Input(this.$refs["tech-notes-input"]);
      }, 0);
    },
    handleTriggerUpload() {
      this.$refs.fileInput.click();
    },
    handleTriggerVideoUpload() {
      this.$refs.videoInput.click();
    },
    handleImport() {
      this.loadingImportImg = true;
      this.$emit("add-ro-images", this.importArr);
      setTimeout(() => {
        this.loadingImportImg = false;
      }, 2000);
    },
    handleFileChange() {
      this.photoInputKey++;
      const files = this.$refs.fileInput.files;
      if (!files || files.length === 0) return;

      // Show loading indicator in the upload zone
      this.isPhotoUploading = true;

      // Emit event to parent component
      this.$emit("add-finding-photo", files);
      Vue.prototype.$current_finput = this.$refs.fileInput;

      // Hide loading indicator after a short timeout
      setTimeout(() => {
        this.isPhotoUploading = false;
      }, 3000); // 3 second timeout is enough to show feedback without being annoying
    },
    handleVideoChange() {
      this.fileInputKey++;
      const files = this.$refs.videoInput.files;

      if (!files || files.length === 0) return;
      
      const file = files[0];
      const videoExtensions = ['mp4', 'mov', 'avi', 'mkv', 'webm'];
      const extension = file.name.split('.').pop().toLowerCase();
      if (!videoExtensions.includes(extension)) {
        sbpUI.methods.alertNegative('Only video files are allowed.')
        return;
      }

      // Show loading indicator in the upload zone
      this.isVideoUploading = true;

      // Emit event to parent component
      this.$emit("add-finding-video", files);
      Vue.prototype.$current_finput = this.$refs.videoInput;
    }
  },
  watch: {
    'input.images': {
      handler(newVal, oldVal) {
        if (!Array.isArray(newVal)) newVal = [];
        if (!Array.isArray(oldVal)) oldVal = [];
        
        const oldVidCount = oldVal.filter(img => img && img.type === 'vid').length;
        const newVidCount = newVal.filter(img => img && img.type === 'vid').length;
        
        if (newVidCount > oldVidCount) {
          this.isVideoUploading = false;
        }
      },
      deep: true
    },

    technicianNotes(newNote){
      this.$emit("add-tech-notes", newNote);
    }
  }
};
</script>

<style scoped lang="scss">
.none-item {
  color: grey;

  .form-check-input {
    border-color: grey;
  }

  .form-check-input:checked {
    border-color: grey;
  }

  .form-check-input[type="radio"]:checked:after {
    border-color: grey;
    background: grey;
  }
}

.good-item {
  color: #00b74a;

  .form-check-input {
    border-color: #00b74a;
  }

  .form-check-input:checked {
    border-color: #00b74a;
  }

  .form-check-input[type="radio"]:checked:after {
    border-color: #00b74a;
    background: #00b74a;
  }
}

.warning-item {
  color: #ffa900;

  .form-check-input {
    border-color: #ffa900;
  }

  .form-check-input:checked {
    border-color: #ffa900;
  }

  .form-check-input[type="radio"]:checked:after {
    border-color: #ffa900;
    background: #ffa900;
  }
}

.bad-item {
  color: #f93154;

  .form-check-input {
    border-color: #f93154;
  }

  .form-check-input:checked {
    border-color: #f93154;
  }

  .form-check-input[type="radio"]:checked:after {
    border-color: #f93154;
    background: #f93154;
  }
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #4285f4;
  font-weight: bold;
}

.upload-zone {
  border: grey dashed;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  min-height: 200px;

  &:hover {
    border: lightgray dashed;
  }
}

.custom-upload-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 10px;

  > div {
    box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px,
                rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
    overflow: hidden;
    padding: 0;
  }

  .without-shadows {
    box-shadow: none;
    display: grid;
    align-items: center;
    justify-content: center;
    width: 100%;
    grid-template-columns: 1fr;
  }

  .dvi-action-button {
    padding: 7px 0;
    box-shadow: rgba(0, 0, 0, 0.05) 0px -6px 24px 0px,
                rgba(0, 0, 0, 0.08) 0px -1px 0px 0px;
    margin-top: 1px;

    button{
      text-transform: capitalize !important;
    }
  }

  iframe {
    height: 250px !important;
  }
}
</style>
