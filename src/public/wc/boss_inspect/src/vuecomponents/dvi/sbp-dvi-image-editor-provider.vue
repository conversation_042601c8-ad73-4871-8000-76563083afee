<template>
  <sbp-modal-base
    :title="title"
    ref="editorDialog"
    @close="
      imageEditing = false;
      loading = false;
      postClose();
    "
    :size="modalSize"
  >
    <template #body>
      <div class="row">
        <div id="leftpanel" :class="col_left">
          <sbp-imageupload-editor v-show="editable" ref="imageEditor" @image-uploaded="handleImageUploaded">
          </sbp-imageupload-editor>
          <img :src="imgUrl" class="img-fluid" v-show="!editable"/>
        </div>
        <div :class="col_right" id="rightpanel" v-show="displayInfo">
        </div>
      </div>
    </template>
    <template #footer>
      <sbp-button
        @click="saveImage"
        :loading="loading"
        color="success"
        icon="eye"
      >{{ editable ? "Save" : "OK" }}
      </sbp-button>
    </template>
  </sbp-modal-base>
</template>

<script>
import SBPModalBase from "../sbp-modal-base";
import SBPButton from "../sbp-button";
import ImageUploadEditor from "../vue-imageupload-editor/ImageEditor";

export default {
  data() {
    return {
      vehicle_number: "",
      vehicle_createTime: "",
      imageEditing: false,
      carInfo: {},
      inspectionInfo: {},
      imgRef: null,
      editable: false,
      displayInfo: false,
      loading: false,
      loadingMessage: "Loading image...",
      imgUrl: "",
      updator: undefined,
      update_url: undefined,
      delete_url: "",
      closeFlag: false,
    };
  },
  props: {
    root: String,
    roid: String,
    updateurl: String,
    noRefMode: {type: Boolean, default: false},
    uploadUrl: String,
  },
  components: {
    "sbp-modal-base": SBPModalBase,
    "sbp-button": SBPButton,
    "sbp-imageupload-editor": ImageUploadEditor,
  },

  provide() {
    return {
      openImageEditor: () => alert(123),
    };
  },
  mounted() {
    Vue.prototype.$imageEditor ? 0 : (Vue.prototype.$imageEditor = this);
  },
  methods: {
    handleImageUploaded() {
      console.log('Image uploaded event received in provider');     
      // Get the image data from the editor
      const imageData = this.$refs.imageEditor.imageData();
      console.log('Image data obtained from editor');

      // Make sure we have a valid imgRef
      if (!this.imgRef) {
        console.log('Creating temporary imgRef for upload');
        // Create a temporary imgRef if none exists
        this.imgRef = {
          setImageUrl: (url) => {
            console.log('Setting image URL in temporary imgRef:', url);
            // This will be called after the image is uploaded
            // We need to notify the parent component to show a loading indicator
            if (this.updator) {
              this.updator(url);
            }
          },
          // Add isLoading property to show loading indicator
          isLoading: true
        };
      } else {
        // If imgRef exists, set isLoading to true
        if (this.imgRef.isLoading !== undefined) {
          this.imgRef.isLoading = true;
        }
      }
      
      // Automatically save the image when it's uploaded
      this.saveImage();
    },

    createProxyUrl(url) {
      // Create a URL for our server-side proxy
      if (!url) return '';

      // Base URL for the proxy
      const proxyBase = '/src/private/components/inspections/apis/image-proxy.php';

      // Encode the URL to make it safe for query parameters
      const encodedUrl = encodeURIComponent(url);

      // Add a cache buster to avoid caching issues
      const cacheBuster = new Date().getTime();

      // Return the full proxy URL
      return `${proxyBase}?url=${encodedUrl}&_cb=${cacheBuster}`;
    },

    fallbackImageLoading(url, preloadedImage) {
      console.log("Using fallback image loading method");

      // Try with crossOrigin set
      if (typeof url === 'string' && url.length > 0) {
        console.log("Trying with crossOrigin=anonymous");
        const img = new Image();
        img.crossOrigin = "anonymous";

        img.onload = () => {
          console.log("Image loaded successfully with crossOrigin=anonymous");

          try {
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0);

            try {
              const dataUrl = canvas.toDataURL('image/jpeg');
              console.log("Successfully converted image to data URL with crossOrigin");
              this.$refs.imageEditor.setImageData(dataUrl);
            } catch (canvasError) {
              console.error("Error converting to data URL with crossOrigin:", canvasError);
              this.originalImageLoading(url, preloadedImage);
            }
          } catch (e) {
            console.error("Error processing loaded image with crossOrigin:", e);
            this.originalImageLoading(url, preloadedImage);
          }
        };

        img.onerror = (e) => {
          console.error("Error loading image with crossOrigin=anonymous:", e);
          this.originalImageLoading(url, preloadedImage);
        };

        // Try with a modified URL to avoid caching issues
        const cacheBuster = new Date().getTime();
        const urlWithCacheBuster = url.includes('?') ?
          `${url}&_cb=${cacheBuster}` :
          `${url}?_cb=${cacheBuster}`;

        img.src = urlWithCacheBuster;
      } else {
        this.originalImageLoading(url, preloadedImage);
      }
    },

    originalImageLoading(url, preloadedImage) {
      console.log("Using original image loading methods");

      // If we have a preloaded image, use it directly
      if (preloadedImage) {
        console.log("Using preloaded image data");
        this.$refs.imageEditor.setImageData(preloadedImage);
      } else {
        // Otherwise, load the image from the URL
        console.log("Loading image from URL:", url);
        this.$refs.imageEditor.setImageUrl(url);
      }
    },

    open({
           url,
           carinfo,
           inspectInfo,
           imgRef = undefined,
           editable = false,
           displayInfo = false,
           updator = undefined,
           update_url = undefined,
           delete_url = "",
           deletor = undefined,
           preloadedImage = null,
         }) {
      // Show loading state immediately
      this.loading = true;
      this.loadingMessage = "Loading image...";

      this.imgRef = imgRef;
      this.closeFlag = false;
      this.deletor = deletor;
      this.delete_url = delete_url;
      this.updator = updator;
      this.update_url = update_url;

      // Check if this is a blob URL (from file uploads)
      const isBlobUrl = typeof url === 'string' && url.startsWith('blob:');

      // Create a proxy URL to bypass CORS restrictions for non-blob URLs
      let proxyUrl = url;
      if (!isBlobUrl) {
        // Using the full path that matches the one in sbp-dvi-image-wrapper.vue
        proxyUrl = `/src/private/components/inspections/apis/image-proxy.php?url=${encodeURIComponent(url)}&nocache=${Date.now()}`;
        // Log the proxy URL for debugging (only visible in console)
        console.log('Using proxy URL:', proxyUrl);
      } else {
        console.log('Using blob URL directly:', url);
      }

      this.imgUrl = isBlobUrl ? url : proxyUrl; // Use the blob URL directly or the proxy URL
      this.editable = editable;
      this.displayInfo = displayInfo;
      if (this.editable) this.displayInfo = true;
      this.carInfo = carinfo;
      this.inspectionInfo = inspectInfo;

      // First open the dialog
      this.$refs.editorDialog.open();

      // Then initialize the editor with a slight delay to ensure the dialog is fully rendered
      setTimeout(() => {
        if (this.$refs.imageEditor != undefined) {
          console.log("Initializing image editor with URL:", url);
          this.$refs.imageEditor.clear();
          this.$refs.imageEditor.setEditable(editable);

          // Log detailed information about the image being opened
          console.log("Opening image editor with:", {
            originalUrl: url,
            proxyUrl: proxyUrl,
            hasPreloadedImage: !!preloadedImage
          });

          // For blob URLs, use them directly; for other URLs, use the proxy
          if (isBlobUrl) {
            console.log("Using blob URL directly in editor");
            this.$refs.imageEditor.setImageUrl(url);
          } else {
            // Use the proxy URL for non-blob URLs
            // This completely bypasses any CORS issues by serving the image from our domain
            console.log("Using proxy approach to bypass CORS restrictions");
            this.$refs.imageEditor.setImageUrl(proxyUrl);
          }

          this.$refs.imageEditor.$once('background-image-loaded', () => {
            this.loading = false;
          });
        }
      }, 100); // Small delay to ensure dialog is ready
      if (Vue.prototype.$current_finput)
        Vue.prototype.$current_finput.value = null;
      //this.vehicle_number = Vue.prototype.$current_report.vehicleModel;
      //this.vehicle_year = Vue.prototype.$current_report.vehicleYear;
      //this.vehicle_createTime = Vue.prototype.$current_report.vehicleMake;
    },
    clearRef() {
      this.imgRef = undefined;
      this.update_url = undefined;
      this.deletor = undefined;
      //alert(555);
    },
    postClose() {
      let that = this;
      if (this.closeFlag || this.imgRef == undefined) {
        //this.clearRef();
        return;
      }

      if (this.imgRef) this.deletor = this.imgRef.deletor;
      if (this.imgRef.visible) {
        //this.clearRef();
        return;
      }

      if (this.imgRef.delete_url != "")
        $.post(
          "https://" + Vue.prototype.$rootUrl + "/" + this.imgRef.delete_url,
          {
            id: this.imgRef.imgInfo.id,
          }
        );

      setTimeout(function () {
        that.clearRef();
      }, 0);

      if (this.deletor) this.deletor();
    },
    setImageUrl(url) {
      this.imgUrl = url;
    },
    saveImage() {
      this.loading = true;
      this.closeFlag = true;

      // Close the dialog immediately for special cases
      if (this.updator != "" && this.updator != undefined) {
        // this.$refs.editorDialog.close();
      }
      if (
        (this.update_url == "" || this.update_url == undefined) &&
        this.updator == undefined
      ) {
        // this.$refs.editorDialog.close();
        return;
      }


      const rawImage = this.$refs.imageEditor.imageData();
      const that = this;
      var blobBin = atob(rawImage.split(",")[1]);
      var array = [];
      for (var i = 0; i < blobBin.length; i++) {
        array.push(blobBin.charCodeAt(i));
      }
      var file = new Blob([new Uint8Array(array)], {type: "image/png"});
      var formdata = new FormData();
      formdata.append("file", file);
      this.root = "localhost.shopbosspro";
      this.roid = 1;

      $.ajax({
        type: "POST",
        //url: `https://<?= ROOT ?>/src/private/components/inspections/apis/localupload.php?`,
        url: this.uploadUrl,
        data: formdata,
        contentType: false,
        processData: false,
        success: (res) => {
          // Close the dialog immediately
          this.$refs.editorDialog.close();

          if (
            that.updator === undefined &&
            (that.imgRef == null || that.imgRef == undefined)
          )
            return;

          // Set the image URL in the wrapper, which will show the loading indicator
          if (that.imgRef != undefined) {
            console.log('Setting image URL in wrapper:', res);

            // Ensure the loading indicator is shown
            if (that.imgRef.isLoading !== undefined) {
              that.imgRef.isLoading = true;
            }

            // Set the image URL
            that.imgRef.setImageUrl(res);
          }

          if (that.updator != "" && that.updator) {
            that.updator(res);
          }

          if (that.imgRef && that.imgRef.update_url != "")
            $.post(
              "https://" +
              Vue.prototype.$rootUrl +
              "/" +
              that.imgRef.update_url,
              {
                id: this.imgRef.imgInfo.id,
                imageUrl: res,
              }
            );
        },
      });
      setTimeout(() => that.$refs.imageEditor.clear(), 500);
    },
  },
  computed: {
    created_at() {
      return Vue.prototype.$current_inspection
        ? Vue.prototype.$current_inspection.created_at
        : "2021-4-21 11:44:22";
    },
    title() {
      return this.editable ? "Edit Photo" : "View Photo";
    },
    col_left() {
      return this.displayInfo ? {"col-8": true} : {"col-12": true};
    },
    col_right() {
      return this.displayInfo ? {"col-4": true} : {"col-4": false};
    },
    measurementOptions() {
      return [""].concat(this.item.measurement_values.split(", "));
    },
    modalSize() {
      return this.displayInfo ? "lg" : "md";
    },
  },
};
</script>

<style></style>
