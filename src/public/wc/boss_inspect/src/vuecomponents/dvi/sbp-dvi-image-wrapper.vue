<template>
  <div v-if="true" class="position-relative">
    <!-- Loading overlay that appears when the image is being updated -->
    <div v-if="isLoading" class="image-loading-overlay">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
    </div>

    <a href="javascript:;" @click="openEditorClick()">
      <img :class="['w-100', imgClass]" :src="universalUrl" @error="handleImageError" v-if="!imageError"/>
      <div v-else class="image-error-container w-100" @click="retryLoadImage">
        <i class="fas fa-exclamation-triangle"></i>
        <span>Image failed to load. Click to retry.</span>
      </div>
    </a>
  </div>
</template>

<style scoped>
.image-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.image-error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #dc3545;
  min-height: 100px;
  text-align: center;
}

.h-250{
  height: 250px !important;
}
</style>

<script>
import SBPModalBase from "../sbp-modal-base";

export default {
  props: [
    "imgUrl",
    "imgClass",
    "width",
    "height",
    "imgInfo",
    "updator",
    "update_url",
    "delete_url",
    "editable",
    "deletor",
  ],
  components: {
    "sbp-modal-base": SBPModalBase,
  },
  data() {
    return {
      visible: true,
      url: this.imgUrl,
      editing: false,
      alreadyMounted: false,
      updateUrl: this.update_url,
      deleteUrl: this.delete_url,
      imageError: false,
      retryCount: 0,
      maxRetries: 3,
      preloadedImage: null,
      isLoading: false,
    };
  },
  mounted() {
    this.url = this.imgUrl;
    if (
      this.imgInfo != undefined &&
      this.imgInfo.needOpen === true &&
      this.alreadyMounted === false
    ) {
      Vue.prototype.$pendingWrapper = this;
      this.visible = false;
    }
    this.alreadyMounted = true;

    // Preload the image to ensure it's in the browser cache
    this.preloadImage();
  },
  computed: {
    universalUrl() {
      console.log("Image URL format:", this.url);
      if (!this.url) {
        console.error("Image URL is undefined or empty");
        return "";
      }
      let finalUrl;
      if (this.url.startsWith("https://") || this.url.startsWith("http://") || this.url.startsWith("data:")) {
        finalUrl = this.url;
      } else {
        finalUrl = Vue.prototype.$imageBaseUrl + this.url;
      }
      console.log("Final URL used:", finalUrl);
      return finalUrl;
    },
    proxyUrl() {
      // Create a URL for our server-side proxy
      if (!this.universalUrl) return '';

      // Base URL for the proxy
      const proxyBase = '/src/private/components/inspections/apis/image-proxy.php';

      // Encode the URL to make it safe for query parameters
      const encodedUrl = encodeURIComponent(this.universalUrl);

      // Add a cache buster to avoid caching issues
      const cacheBuster = new Date().getTime();

      // Return the full proxy URL
      return `${proxyBase}?url=${encodedUrl}&_cb=${cacheBuster}`;
    },
  },
  methods: {
    setImageUrl(url) {
      console.log('Setting new image URL:', url);
      // Show loading indicator
      this.isLoading = true;

      // Update the URL
      this.url = url;
      this.imageError = false;
      this.retryCount = 0;

      // Preload the new image
      this.preloadImage();

      // Create a new image to detect when it's fully loaded
      const img = new Image();
      img.onload = () => {
        console.log('Image fully loaded, hiding loading indicator');
        // Hide loading indicator after the image is fully loaded
        setTimeout(() => {
          this.isLoading = false;
        }, 1000); // Longer delay to ensure the image is rendered and user sees the indicator
      };

      img.onerror = () => {
        console.error('Error loading image');
        this.isLoading = false;
      };

      // Set the source to trigger loading
      img.src = this.universalUrl;

      // Fallback: hide loading indicator after a timeout in case the onload event doesn't fire
      setTimeout(() => {
        if (this.isLoading) {
          console.log('Fallback: hiding loading indicator after timeout');
          this.isLoading = false;
        }
      }, 5000); // 5 second fallback
    },
    preloadImage() {
      // Create a new image object to preload the image
      if (this.url) {
        console.log('Preloading image:', this.universalUrl);

        // CRITICAL FIX: Always use crossOrigin="anonymous" for images that will be used in canvas
        // This is the key to fixing the blank editor issue
        this.tryPreloadWithCORS();
      }
    },

    // We're skipping the non-CORS approach since it will cause canvas taint issues

    tryPreloadWithCORS() {
      console.log('Trying to preload with CORS (critical for canvas usage)');
      const img = new Image();
      img.crossOrigin = 'anonymous'; // This is critical for S3 images and canvas usage

      img.onload = () => {
        console.log('Image preloaded successfully with CORS');
        this.preloadedImage = img;
      };

      img.onerror = (error) => {
        console.error('Failed to preload with CORS:', error);
        this.tryPreloadWithAlternativeURL();
      };

      // Add a cache buster to avoid caching issues
      const cacheBuster = new Date().getTime();
      const urlWithCacheBuster = this.universalUrl.includes('?') ?
        `${this.universalUrl}&_cb=${cacheBuster}` :
        `${this.universalUrl}?_cb=${cacheBuster}`;

      img.src = urlWithCacheBuster;
    },

    // This method was replaced by the updated version above

    tryPreloadWithAlternativeURL() {
      // Try an alternative approach for old image URLs
      if (this.url.startsWith('/sbp/upload/')) {
        console.log('Trying alternative URL format');

        // For old image URLs, try a different approach
        const alternativeUrl = this.getAlternativeUrl(this.url);
        if (alternativeUrl !== this.url) {
          console.log('Using alternative URL:', alternativeUrl);
          const altImage = new Image();

          altImage.onload = () => {
            console.log('Alternative URL loaded successfully');
            this.preloadedImage = altImage;
          };

          altImage.onerror = () => {
            console.error('Alternative URL also failed to load');
            this.tryPreloadWithCacheBuster();
          };

          altImage.src = alternativeUrl;
        } else {
          this.tryPreloadWithCacheBuster();
        }
      } else {
        this.tryPreloadWithCacheBuster();
      }
    },

    tryPreloadWithCacheBuster() {
      console.log('Trying to preload with cache buster');

      // Add a cache buster to the URL
      const cacheBuster = new Date().getTime();
      const urlWithCacheBuster = this.universalUrl.includes('?') ?
        `${this.universalUrl}&_cb=${cacheBuster}` :
        `${this.universalUrl}?_cb=${cacheBuster}`;

      const img = new Image();

      img.onload = () => {
        console.log('Image preloaded successfully with cache buster');
        this.preloadedImage = img;
      };

      img.onerror = (error) => {
        console.error('All preloading attempts failed:', error);
        // We've tried everything, just keep the reference to the last attempt
        this.preloadedImage = img;
      };

      img.src = urlWithCacheBuster;
    },

    getAlternativeUrl(url) {
      // Handle old image URLs that might be in a different format
      if (url.startsWith('/sbp/upload/')) {
        // Extract the parts of the URL
        const parts = url.split('/');
        if (parts.length >= 5) {
          const shopId = parts[3];
          const roId = parts[4];
          const remainingPath = parts.slice(5).join('/');

          // Try different formats
          // 1. Try with https://shopbosspro.com prefix
          return `https://shopbosspro.com/sbp/upload/${shopId}/${roId}/${remainingPath}`;
        }
      }
      return url; // Return original if no alternative
    },
    openEditorClick() {
      this.visible = true;
      this.openEditor();
    },
    openEditor() {
      //this.$emit('openEditor', this.imgUrl );

      // If we have a preloaded image, use it
      const imageData = this.getImageData();

      // Log what we're doing
      console.log('Opening editor with:', {
        originalUrl: this.url,
        universalUrl: this.universalUrl,
        proxyUrl: this.proxyUrl,
        hasPreloadedImage: !!imageData
      });

      Vue.prototype.$imageEditor.imgRef = this;
      Vue.prototype.$imageEditor.open({
        url: this.proxyUrl, // Use the proxy URL instead of the original URL
        originalUrl: this.url, // Also pass the original URL for reference
        imgRef: this,
        imgInfo: this.imgInfo,
        updator: this.updator,
        update_url: this.updateUrl,
        editable: this.editable,
        delete_url: !this.visible ? this.delete_url : undefined,
        deletor: !this.visible ? this.deletor : undefined,
        preloadedImage: imageData, // Pass the preloaded image data
      });
    },
    getImageData() {
      // If we have a preloaded image that's fully loaded, convert it to a data URL
      if (this.preloadedImage && this.preloadedImage.complete && this.preloadedImage.naturalWidth > 0) {
        try {
          console.log('Converting preloaded image to data URL');

          // CRITICAL: Verify the image has crossOrigin set
          if (!this.preloadedImage.crossOrigin) {
            console.warn('Preloaded image does not have crossOrigin set, this may cause canvas taint issues');
            // We won't return null here, we'll still try to use the image
            // but we'll log a warning for debugging purposes
          }

          const canvas = document.createElement('canvas');
          canvas.width = this.preloadedImage.naturalWidth;
          canvas.height = this.preloadedImage.naturalHeight;
          const ctx = canvas.getContext('2d');

          // Draw the image on the canvas
          ctx.drawImage(this.preloadedImage, 0, 0);

          try {
            // Try to get the data URL - this will fail if the canvas is tainted
            const dataUrl = canvas.toDataURL('image/jpeg');
            console.log('Successfully converted preloaded image to data URL');
            return dataUrl;
          } catch (canvasError) {
            console.error('Canvas tainted, cannot extract data URL:', canvasError);

            // If the canvas is tainted, we need to create a new image with crossOrigin
            console.log('Creating new image with explicit crossOrigin setting');
            return null; // Return null to force the editor to load the image directly
          }
        } catch (e) {
          console.error('Error converting preloaded image to data URL:', e);
          return null;
        }
      } else {
        console.log('No valid preloaded image available');
      }
      return null;
    },
    handleImageError() {
      console.error(`Image failed to load: ${this.universalUrl}`);
      this.imageError = true;

      // Auto-retry once immediately
      if (this.retryCount < 1) {
        this.retryLoadImage();
      }
    },
    retryLoadImage() {
      if (this.retryCount < this.maxRetries) {
        console.log(`Retrying image load (${this.retryCount + 1}/${this.maxRetries}): ${this.url}`);
        this.imageError = false;
        this.retryCount++;

        // Force re-evaluation of the image src by adding a cache-busting parameter
        const cacheBuster = `?cb=${Date.now()}`;
        if (this.url.includes('?')) {
          this.url = this.url.split('?')[0] + cacheBuster;
        } else {
          this.url = this.url + cacheBuster;
        }
      } else {
        console.error(`Max retries (${this.maxRetries}) reached for image: ${this.url}`);
      }
    },
  },
};
</script>

<style>
.image-error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 20px;
  text-align: center;
  min-height: 150px;
  cursor: pointer;
}

.image-error-container i {
  font-size: 2rem;
  margin-bottom: 10px;
}
</style>
