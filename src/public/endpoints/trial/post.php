<?php
header('Access-Control-Allow-Origin: *');

$handle = curl_init();

$url = "https://".$_SERVER['SERVER_NAME']."/src/public/endpoints/trial/createtrial.php";

$postData = array(
  'first_name' => $_POST['first_name'],
  'last_name'  => $_POST['last_name'],
  'email' => $_POST['email'],
  'phone' => $_POST['phone'],
  'shop' => $_POST['shop'],
  'address_street' => $_POST['address_street'],
  'address_city' => $_POST['address_city'],
  'address_state' => $_POST['address_state'],
  'address_zip' => $_POST['address_zip'],
  'shop_timezone' => $_POST['shop_timezone'],
  'laborrate' => $_POST['laborrate'],
  'taxrate' => $_POST['taxrate'],
  'labortaxrate' => $_POST['labortaxrate'],
  'sublettaxrate' => $_POST['sublettaxrate'],
  'address_country' => $_POST['address_country'],
  'tempdata' => $_POST['tempdata'],
  'password' => $_POST['password'],
  'source' => $_POST['source'],
  'matco' => $_POST['matco'] ?? 'no',
  'protractor' => 'no',
  'salesrep' => $_POST['salesrep'],
  'salesreppassword' => $_POST['salesreppassword']??'',
  'referralcode' => $_POST['referralcode']??'',
  'package' => $_POST['package']??'platinum'
);
 
curl_setopt_array($handle,
  array(
    CURLOPT_URL => $url,
    CURLOPT_POST       => true,
    CURLOPT_POSTFIELDS => json_encode($postData),
    CURLOPT_RETURNTRANSFER     => true,
  )
);
 
$data = curl_exec($handle);
 
curl_close($handle);

echo $data;
?>