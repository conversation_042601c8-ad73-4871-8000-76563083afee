<?php
$httpOrigin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : null;
if (in_array($httpOrigin, [
    'https://shopbosspro-com-staging.mb39129p-liquidwebsites.com',
    'https://shopboss:8890',
    'https://shopboss.net',
    'https://matcosms.com',
    'https://learn.matcosms.com',
    'https://protractorgo.com',
])) header("Access-Control-Allow-Origin: ${httpOrigin}");


require(CONNWOSHOPID);
require(PRIVATE_PATH."/integrations/mandrill/src/Mandrill.php");


$json = file_get_contents('php://input');
$data = json_decode($json,true);

$mylist=array("<",">","'","(",")","]","[","}","{","--","=","|");
foreach($data as $key => $value)
{
  if(in_array($key, $mylist) || in_array($value,$mylist))
  {
    echo(json_encode(array("status"=>false,"message"=>"Invalid Parameters")));
    exit;
  }
}

$first_name = (isset($data['first_name'])?filter_var($data['first_name'], FILTER_SANITIZE_STRING):'');
$last_name = (isset($data['last_name'])?filter_var($data['last_name'], FILTER_SANITIZE_STRING):'');
$username = (isset($data['username'])?filter_var($data['username'], FILTER_SANITIZE_STRING):'');
$shopemail = (isset($data['email'])?filter_var(strtolower($data['email']), FILTER_SANITIZE_STRING):'');
$phone = (isset($data['phone'])?filter_var($data['phone'], FILTER_SANITIZE_STRING):'');
$shopname = (isset($data['shop'])?filter_var($data['shop'], FILTER_SANITIZE_STRING):'');
$address = (isset($data['address_street'])?filter_var($data['address_street'], FILTER_SANITIZE_STRING):'');
$country = (isset($data['address_country'])?filter_var($data['address_country'], FILTER_SANITIZE_STRING):'');
$city = (isset($data['address_city'])?filter_var($data['address_city'], FILTER_SANITIZE_STRING):'');
$state = (isset($data['address_state'])?filter_var($data['address_state'], FILTER_SANITIZE_STRING):'');
$zip = (isset($data['address_zip'])?filter_var($data['address_zip'], FILTER_SANITIZE_STRING):'');
$timezone = (isset($data['shop_timezone'])?filter_var($data['shop_timezone'], FILTER_SANITIZE_STRING):'');
$laborrate = (isset($data['laborrate'])?filter_var($data['laborrate'], FILTER_SANITIZE_STRING):'');
$taxrate = (isset($data['taxrate'])?filter_var($data['taxrate'], FILTER_SANITIZE_STRING):'');
$labortaxrate = (isset($data['labortaxrate'])?filter_var($data['labortaxrate'], FILTER_SANITIZE_STRING):'');
$sublettaxrate = (isset($data['sublettaxrate'])?filter_var($data['sublettaxrate'], FILTER_SANITIZE_STRING):'');
$tempdata = (isset($data['tempdata'])?filter_var($data['tempdata'], FILTER_SANITIZE_STRING):'');
$password = (isset($data['password'])?filter_var($data['password'], FILTER_SANITIZE_STRING):'');
$source = (isset($data['source'])?filter_var($data['source'], FILTER_SANITIZE_STRING):'');
$referralcode = (isset($data['referralcode'])?filter_var($data['referralcode'], FILTER_SANITIZE_STRING):'');
$estguide = (isset($data['estguide'])?filter_var($data['estguide'], FILTER_SANITIZE_STRING):'');
$salesrep = (isset($data['salesrep'])?filter_var($data['salesrep'], FILTER_SANITIZE_STRING):'');
$salesreppassword = (isset($data['salesreppassword'])?filter_var($data['salesreppassword'], FILTER_SANITIZE_STRING):'');
$package = (isset($data['package'])?filter_var($data['package'], FILTER_SANITIZE_STRING):'');
$userip = (isset($data['ip'])?filter_var($data['ip'], FILTER_SANITIZE_STRING):'');
$matco = $data['matco']??'no';
$protractor = $data['protractor']??'no';
$shop_type = $data['shop_type']??'';

if(empty($phone))
{
  echo(json_encode(array("status"=>false,"message"=>"EMPTY PHONE")));
  exit;
}

$stmt = "select shopid from company where companyemail = ? limit 1";
if ($query = $conn->prepare($stmt))
{
    $query->bind_param("s",$shopemail);
    $query->execute();
    $query->store_result();
    $numrows = $query->num_rows();
    if ($numrows > 0)
    {
      $query->bind_result($dupshopid);
      $query->fetch();
      echo(json_encode(array("status"=>false,"message"=>"Shop with this email address already exists. (#".$dupshopid.")")));
      exit;
    }
}

$stmt = "select id from employees where username = ? limit 1";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s",$username);
    $query->execute();
    $query->store_result();
    $num_roid_rows = $query->num_rows;
    if ($num_roid_rows > 0)
    {
      echo(json_encode(array("status"=>false,"message"=>"Username already in use. Please enter a different one.")));
      exit;
    }
}

if(in_array($shopemail, array("<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>")))
{
  echo(json_encode(array("status"=>false,"message"=>"Invalid Email")));
  exit;
}

if(!empty($salesreppassword))
{
  $stmt = "select concat(first_name,' ',last_name) from adminlogin where id = ? and password = ?";
  if ($query = $conn->prepare($stmt))
  {
   $query->bind_param("is",$salesrep,$salesreppassword);
   $query->execute();
   $query->store_result();
   $numrows = $query->num_rows();
   if ($numrows<1)
   {
      echo(json_encode(array("status"=>false,"message"=>"Incorrect Sales Rep Password")));
      exit;
   }
   else
   {
    $query->bind_result($salesrep);
    $query->fetch();
   }
  } 
}

$stmt = "select shopid from company where shopid REGEXP '^[0-9]+$' and shopid != '477287' and shopid != '908239' order by CAST(shopid AS UNSIGNED) desc limit 1";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $query->bind_result($lastshopid);
 $query->fetch();
 $query->close();
}

$shopid = $lastshopid + 1;

$stmt = "select shopid from sbpent.company where shopid = ? limit 1";
if ($query = $conn->prepare($stmt))
{
 $query->bind_param("s",$shopid);
 $query->execute();
 $query->store_result();
 $numrows = $query->num_rows();
 if ($numrows>0)
 $shopid = $shopid + 1;
}

$ph = str_replace(array('-',')','(','.'),'',$phone);

$refcode = substr($shopid, 0,2).$referralcode.substr($shopid, -2);

$date=date('Y-m-d');
$expiration = date('m/d/Y',strtotime('+30 days'));

if($matco=='yes' || $protractor=='yes')
$trial_expiration = date('Y-m-d',strtotime('+7 days'));
else
$trial_expiration = date('Y-m-d',strtotime('+30 days'));

$inspectexpiration = date('Y-m-d',strtotime('+7 days'));

$stmt = "select shopid from company where referralcode = ?";
if ($query = $conn->prepare($stmt))
{
 $query->bind_param("s",$refcode);
 $query->execute();
 $query->bind_result($refshopid);
 $query->fetch();
 $query->close();
}

if(empty($refshopid)) $refcode="none";

if(empty($password))
$password = generateRandomStr(8);

$passwordenc = password_hash($password, PASSWORD_DEFAULT);

$rodisclosure = "Replaced parts may be requested by customer. I hereby authorize the above repair work to be done along with all necessary materials. You and your employees may operate the above vehicle for the purposes of testing, inspection or delivery at my risk. An express mechanic's lien is acknowledged on the above vehicle to secure the amount of repairs thereto. The shop will not be held responsible for loss or damage to vehicle or articles left in vehicle in case of fire, theft, accident or any other cause beyond your control. In the event legal action is necessary to enforce this contract, I understand that I am solely responsible for all costs including attorney's fees and court costs. I have read the above and acknowledge receipt of an estimate.";

$rowarrdisclosure = "Warranty: From the date of delivery for a period of [warrantymonths] months or [warrantymiles] miles, whichever comes first, this firm will repair free of charge any defects in material and workmanship to the repairs stated on the invoice. All work to be done in our shop only. This does not include towing charges or customer supplied parts. A storage fee of [storagefee] per day will be charged 24 hours after notification that work is complete. Neglect/abuse of vehicle will immediately void any and all warranties.";

$contact = $first_name.' '.$last_name;

$stmt = "insert into company (shopid,companyname,datestarted,trialexpiration,package,active,referredby,affiliateid,`new`,timezone,status,contact,companycountry,companyaddress,companycity,companystate,companyzip,companyphone,rodisclosure,rowarrdisclosure,companyemail,defaulttaxrate,defaultlabortaxrate,defaultsublettaxrate,hourlyrate,dashexpiration,shopip,inspectexpiration,matco,protractor,salesrep,newpackagetype,userfee1,userfee1type,userfee1amount,userfee1max,userfee1taxable,userfee1applyon,nexpartpassword) values(?,?,?,?,'Trial','yes',?,?,'yes',?,'ACTIVE',?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,'SHOP SUPPLIES','%','3','20','Taxable','all','showcommlogyes')";
if ($query = $conn->prepare($stmt))
{
 $query->bind_param("ssssssssssssssssssssssssssss",$shopid,$shopname,$date,$trial_expiration,$source,$referralcode,$timezone,$contact,strtoupper($country),strtoupper($address),strtoupper($city),strtoupper($state),$zip,$ph,$rodisclosure,$rowarrdisclosure,$shopemail,$taxrate,$labortaxrate,$sublettaxrate,$laborrate,$expiration,$userip,$inspectexpiration,$matco,$protractor,$salesrep,$package);
 $query->execute();
 $conn->commit();
}

$stmt = "insert into companycategories (shopid,category) values(?,?)";
if ($query = $conn->prepare($stmt))
{
 $query->bind_param("ss",$shopid,$shop_type);
 $query->execute();
 $conn->commit();
}

if($matco=='yes')
{
  $today = time();
  $apistr = 'drott;' . $today;
  $apikey = md5($apistr);
  $stmt = "insert into apilogin (shopid,companyname,apikey) values(?,'matco',?)";
  if ($query = $conn->prepare($stmt))
  {
   $query->bind_param("ss",$shopid,$apikey);
   $query->execute();
   $conn->commit();
  }
  $from = "<EMAIL>";
}
elseif($protractor=='yes')
$from = "<EMAIL>";
else
$from = "<EMAIL>";


$stmt = "insert into employees (shopid,employeefirst,employeelast,username,employeeid,defaultwriter,active,password,passwordenc) values(?,?,?,?,'125','Yes','Yes',?,?)";
if ($query = $conn->prepare($stmt))
{
 $query->bind_param("ssssss",$shopid,strtoupper($first_name),strtoupper($last_name),$username,$password,$passwordenc);
 $query->execute();
 $conn->commit();
}


$stmt = "insert into paymentmethods (shopid,method) values ('$shopid', 'Cash'),('$shopid', 'Check'),('$shopid', 'Visa'),('$shopid', 'Mastercard'),('$shopid', 'American Express'),('$shopid', 'Debit Card')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}

$stmt = "insert into settings (shopid) values ('$shopid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}


$stmt = "insert into `category` (`shopid`, `Category`, `Factor`, `Start`, `End`) values('$shopid','GENERAL PART','3','0.01','2.99'),('$shopid','DEALER PART','1.4','0.01','2.99'),('$shopid','DEALER PART','1.4','3','7.99'),('$shopid','DEALER PART','1.4','8','39.99'),('$shopid','DEALER PART','1.4','40','199.99'),('$shopid','DEALER PART','1.4','200','5000'),('$shopid','GENERAL PART','2.5','3','19.99'),('$shopid','GENERAL PART','2.25','20','99.99'),('$shopid','GENERAL PART','2','100','499.99'),('$shopid','GENERAL PART','1.75','500','5000'),('$shopid','NON MATRIX','1','0.01','5999.99'),('$shopid','40% MARKUP','1.40','0.01','5999')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}


$stmt = "insert into `codes` (`shopid`, `Codes`) values('$shopid','New'),('$shopid','Rebuilt'),('$shopid','Re-Manufactured'),('$shopid','Used')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}

$stmt = "insert into `jobdesc` (`shopid`, `JobDesc`) values('$shopid','General Labor'),('$shopid','General Manager'),('$shopid','Office (Clerical)'),('$shopid','Office Manager'),('$shopid','Owner'),('$shopid','Service Advisor'),('$shopid','Service Technician')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}

$stmt = "insert into `rotype` (`shopid`, `ROType`) values('$shopid','Internal'),('$shopid','No Approval'),('$shopid','No Problem'),('$shopid','No Charge'),('$shopid','Customer Pay')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}

$stmt = "insert into `rostatus` (`shopid`, `status`, `colorcode`, `isdefault`, `displayorder`) values('$shopid', '1INSPECTION', '#d5d52a', 'yes', 1),('$shopid','2APPROVAL', '#FFA500', 'yes', 2),('$shopid','3PARTS', '#ff0000', 'yes',3),('$shopid','4ASSEMBLY', '#006400', 'yes',4),('$shopid','5Q-CHECK', '#ffc0cb', 'yes',5),('$shopid','FINAL', '#0000ff', 'yes', 6),('$shopid','CLOSED', '#000000', 'yes',7)";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}


$stmt = "insert into `source` (`shopid`, `Source`) values('$shopid','Direct Mail'),('$shopid','Drive By'),('$shopid','AAA'),('$shopid','Google'),('$shopid','Yelp'),('$shopid','Referral'),('$shopid','Repeat')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}

if(strtoupper($tempdata)=='YES')
{
  $stmt = "insert into `complaints` (`shopid`, `complaintid`, `roid`, `complaint`, `techreport`, `tech`) values('$shopid',1, 1002, 'The Air Filter needs replacement.', NULL, ''),('$shopid',2, 1002, 'The vehicle needs the front wheels aligned.', NULL, ''),('$shopid',3, 1001, 'oil and filter change', '', ''),('$shopid',32, 1003, 'The vehicle needs the front wheels aligned.', NULL, ''),('$shopid',33, 1003, 'The left front wheel bearing needs replacement.', NULL, ''),('$shopid',34, 1003, 'The Accessory Drive Belt needs replacement.', NULL, '')";
    if ($query = $conn->prepare($stmt))
    {
      $query->execute();
      $conn->commit();
    }

    $stmt = "insert into `customer` (`shopid`, `CustomerID`, `LastName`, `FirstName`, `Address`, `City`, `State`, `Zip`, `HomePhone`, `WorkPhone`, `CellPhone`, `Pager`, `Fax`, `EMail`, `Wholesale`, `UserDefined1`, `UserDefined2`, `UserDefined3`, `Comments`, `ServiceReminder`, `Specials`, `Follow`, `Discount`, `contact`) values('$shopid',1, 'SMITH', 'JOHN', '1234 MAIN ST', 'POWAY', 'CA', '92064', '**********', '**********', '1234444441', '', '1235554444', '<EMAIL>', '', '', '', '', NULL, '', '', '', '', ''),('$shopid',3, 'HILL', 'BUNKER', '4444 MAIN ST', 'SAN DIEGO', 'CA', '92101', '6195554111', '', '', '', '', '', '', '', '', '', NULL, '', '', '', '', ''),('$shopid',11, 'WILLIAMS', 'HANK', '7748 WELLINGTON ST', 'LITTLETON', 'CO', '80122', '3035551212', '3112322132', '1321321321', '', '3213213213', '', '', '', '', '', NULL, '', '', '', '', '')";
    if ($query = $conn->prepare($stmt))
    {
      $query->execute();
      $conn->commit();
    }

    $stmt = "insert into `labor` (`shopid`, `LaborID`, `ROID`, `HourlyRate`, `LaborHours`, `Labor`, `Tech`, `LineTotal`, `LaborOp`, `complaintid`, `techrate`) values('$shopid',1, 1001, 89, 2, 'REPLACE FRONT BRAKES', 'SMITH, RON', 178, '', 3, 0),('$shopid',12, 1003, 89, 0.5, 'Replace Air Filter ', 'SMITH, RON', 44.5, '', 32, 0),('$shopid',13, 1003, 89, 0.2, 'Oil and Filter Change', 'SMITH, RON', 17.8, '', 32, 0),('$shopid',14, 1003, 89, 1.5, 'Replace 4 Spark Plugs', 'SMITH, RON', 133.5, '', 32, 0),('$shopid',15, 1003, 89, 0.5, 'Check tires and wheel alignment', 'SMITH, RON', 44.5, '', 32, 0),('$shopid',16, 1003, 89, 1.5, 'REPLACE DRIVE BELT', 'SMITH, RON', 133.5, '', 34, 0)";
    if ($query = $conn->prepare($stmt))
    {
      $query->execute();
      $conn->commit();
    }

    $stmt = "insert into `parts` (`shopid`, `PartID`, `PartNumber`, `PartDesc`, `PartPrice`, `Quantity`, `ROID`, `Supplier`, `Cost`, `PartInvoiceNumber`, `PartCode`, `LineTTLPrice`, `LineTTLCost`, `Date`, `PartCategory`, `complaintid`, `discount`, `net`, `tax`) values('$shopid',1, '2311', 'BRAKE LINING', 76.5000, 1, 1001, 'MY PARTS HOUSE', 34.0000, '', 'NEW', 76.5, 34, '$date', 'BRAKE PART', 3, 0, 76.5, 'YES'),('$shopid',2, '2311', 'BRAKE LINING', 76.5000, 1, 1001, 'MY PARTS HOUSE', 34.0000, '45644', 'NO CODES', 72.67, 34, '$date', 'BRAKE PART', 3, 5, 72.67, 'YES'),('$shopid',10, 'OIL', '10W50', 6.0000, 1, 1003, 'MY PARTS HOUSE', 2.0000, '', 'NEW', 6, 2, '$date', 'GENERAL PART', 32, 0, 6, ''),('$shopid',15, 'BRAKEPADS', 'BRAKE PADS', 60.7500, 1, 1003, 'MY PARTS HOUSE', 27.0000, '', 'NEW', 60.75, 27, '$date', 'BRAKE PART', 34, 0, 60.75, 'YES'),('$shopid',20, 'BRAKEPADS', 'BRAKE PADS', 60.7500, 1, 1003, 'MY PARTS HOUSE', 27.0000, '', 'NEW', 60.75, 27, '$date', 'BRAKE PART', 33, 0, 60.75, 'YES')";
    if ($query = $conn->prepare($stmt))
    {
      $query->execute();
      $conn->commit();
    }

    $stmt = "insert into `partsregistry` (`shopid`, `PartNumber`, `PartDesc`, `PartPrice`, `PartCode`, `PartCost`, `PartSupplier`, `OnHand`, `Allocatted`, `NetOnHand`, `ReOrderLevel`, `MaxOnHand`, `MaintainStock`, `OrderStatus`, `TransitStatus`, `PartCategory`, `discount`, `net`, `tax`) values('$shopid','2311', 'BRAKE LINING', 76.5, 'NEW', 34, 'MY PARTS HOUSE', 0, 0, 0, 0, 0, '', '', '', 'BRAKE PART', 0, 0, 'YES'),('$shopid','*********', 'BRAKE ROTORS', 78.75, 'NEW', 35, 'MY PARTS HOUSE', 0, 0, 0, 0, 0, '', '', '', 'BRAKE PART', 0, 0, 'YES'),('$shopid','OIL1511', 'OIL FILTER FOR AUDO', 10, 'NEW', 4, 'MY PARTS HOUSE', 0, 0, 0, 0, 0, '', '', '', 'GENERAL PART', 0, 0, 'YES'),('$shopid','OIL', '10W50', 6, 'NEW', 2, 'MY PARTS HOUSE', 0, 0, 0, 0, 0, '', '', '', 'GENERAL PART', 0, 0, ''),('$shopid','4744M', 'MUFFLER', 192.5, 'NEW', 55, 'MY PARTS HOUSE', 0, 0, 0, 0, 0, '', '', '', 'EXHAUST PART', 0, 0, 'YES'),('$shopid','BRAKEPADS', 'BRAKE PADS', 60.75, 'NEW', 27, 'MY PARTS HOUSE', 0, 0, 0, 0, 0, '', '', '', 'BRAKE PART', 0, 0, 'YES')";
    if ($query = $conn->prepare($stmt))
    {
      $query->execute();
      $conn->commit();
    }

    $stmt = "insert into `repairorders` (`shopid`, `ROID`, `CustomerID`, `Writer`, `DateIn`, `TimeIn`, `TaxRate`, `PurchaseOrderNumber`, `VehID`, `Customer`, `VehInfo`, `vehyear`, `vehmake`, `vehmodel`, `vehlicense`, `vehstate`, `VehLicNum`, `WrittenBy`, `Status`, `StatusDate`, `TotalLbrHrs`, `TotalLbr`, `TotalPrts`, `TotalSublet`, `TotalRO`, `CustomerAddress`, `CustomerCSZ`, `CustomerPhone`, `customercity`, `customerstate`, `customerzip`, `VehicleMiles`, `MilesOut`, `Vin`, `CustomerWork`, `MajorComplaint`, `DatePromised`, `Comments`, `DiscountAmt`, `DiscountPercent`, `WarrMos`, `WarrMiles`, `SalesTax`, `PartsCost`, `ROType`, `VehEngine`, `Cyl`, `VehTrans`, `HowPaid`, `AmtPaid1`, `CheckNum1`, `HowPaid2`, `AmtPaid2`, `CheckNum2`, `EstimateAmt`, `NoFollow`, `AccountPaid`, `HazardousWaste`, `Source`, `Rev1Amt`, `Rev1Date`, `Rev1Phone`, `Rev1Time`, `Rev1By`, `Rev2Amt`, `Rev2Date`, `Rev2Phone`, `Rev2Time`, `Rev2By`, `CellPhone`, `UserFee1`, `UserFee2`, `UserFee3`, `LastFirst`, `customerfirst`, `customerlast`, `DriveType`, `TotalFees`, `Fax`, `Subtotal`, `CB`, `DateInspection`, `DateAuthorization`, `DateParts`, `DateWork`, `DateInProcess`, `DateHold`, `DateFinal`, `DateDelivered`, `DateClosed`, `OrigRO`, `OrigTech`, `PartsOrdered`, `FinalDate`, `Exported`, `LaborTaxRate`, `SubletTaxRate`, `complainttable`, `gp`, `contact`, `balance`, `fleetno`) values('$shopid',1001, 1, '', '$date', '07:55:00', 7.75, '', 1, 'JOHN SMITH', '2003 MERCEDES-BENZ E-CLASS E320 SEDAN 4-DR', '2003', 'MERCEDES-BENZ', 'E-CLASS E320 SEDAN 4-DR', 'KDKDKDK', 'CA', 'KDKDKDK - CA', 0, 'Final', '$date', 0, 178, 149.17, 0, 338.************, '1234 MAIN ST', 'POWAY, CA.  92064', '**********', 'POWAY', 'CA', '92064', '', '', 'WDBUF65J13A233645', '**********', '', '0000-00-00', '', 0, 0, '0', '0', 11.56, 68, 'Customer Pay', '3.2L V6 SOHC 18V', '6', 'AUTO', '', 0, '', '', 0, '', 0, '', '', 0, '', 0, '', '', '', '', 0, '', '', '', '', '1234444441', 0.0000, 0.0000, 0.0000, 'SMITH, JOHN', 'JOHN', 'SMITH', 'RWD', 0.0000, '', 327.1700, '', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', 0, '', '', '$date', '', 0, 0, 'yes', 0, '', 200.01, ''),('$shopid',1002, 1, 'RON SMITH', '$date', '08:01:00', 7.75, '', 1, 'JOHN SMITH', '2003 MERCEDES-BENZ E-CLASS E320 SEDAN 4-DR', '2003', 'MERCEDES-BENZ', 'E-CLASS E320 SEDAN 4-DR', 'KDKDKDK', 'CA', 'KDKDKDK - CA', 0, '1INSPECTION', '$date', 0, 0, 0, 0, 0, '1234 MAIN ST', 'POWAY, CA.  92064', '**********', 'POWAY', 'CA', '92064', '', '', 'WDBUF65J13A233645', '**********', 'The Air Filter needs replacement., The vehicle needs the front wheels aligned., ', '0000-00-00', '', 0, 0, '0', '0', 0, 0, 'Customer Pay', '3.2L V6 SOHC 18V', '6', 'AUTO', '', 0, '', '', 0, '', 0, '', '', 0, '', 0, '', '', '', '', 0, '', '', '', '', '1234444441', 0.0000, 0.0000, 0.0000, 'SMITH, JOHN', 'JOHN', 'SMITH', 'RWD', 0.0000, '', 0.0000, '', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', 0, '', '', '0000-00-00', '', 0, 0, 'yes', 0, '', 0, ''),('$shopid',1003, 3, 'RON SMITH', '$date', '10:22:00', 7.75, '', 8, 'BUNKER HILL', '2004 JEEP WRANGLER X SPORT UTILITY 2-DR', '2004', 'JEEP', 'WRANGLER X SPORT UTILITY 2-DR', '456211', 'CA', '456211 - CA', 0, '1INSPECTION', '$date', 0, 373.8, 127.5, 0, 510.719987792969, '4444 MAIN ST', 'SAN DIEGO, CA.  92101', '6195554111', 'SAN DIEGO', 'CA', '92101', '', '', '1J4FA39S74P775089', '', 'The vehicle needs the front wheels aligned., The left front wheel bearing needs replacement., The Accessory Drive Belt needs replacement., ', '0000-00-00', '', 0, 0, '3', '3000', 9.42, 56, 'Customer Pay', '4.0L L6 OHV 12V', '6', 'AUTO', '', 0, '', '', 0, '', 0, '', '', 0, '', 0, '', '', '', '', 0, '', '', '', '', '', 0.0000, 0.0000, 0.0000, 'HILL, BUNKER', 'BUNKER', 'HILL', 'RWD', 0.0000, '', 501.3000, '', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', '0000-00-00', 0, '', '', '0000-00-00', '', 0, 0, 'yes', 0, '', 510.72, '')";
    if ($query = $conn->prepare($stmt))
    {
      $query->execute();
      $conn->commit();
    }

    $stmt = "insert into `sublet` (`shopid`, `SubLetID`, `ROID`, `SubletDesc`, `SubletPrice`, `SubletCost`, `SubletInvoiceNo`, `SubletSupplier`, `complaintid`) values('$shopid',1, 1003, 'TOWING', 77.65, 58, '78987', 'TOW PROS', 4)";
    if ($query = $conn->prepare($stmt))
    {
      $query->execute();
      $conn->commit();
    }

}

$stmt = "insert into `complaintcats` (`shopid`, `catname`) VALUES ($shopid, 'Battery'),($shopid, 'Brakes'),($shopid, 'Diagnostic'),($shopid, 'Warranty'),($shopid, 'Alignment'),($shopid, 'Body'),($shopid, 'Detailing'),($shopid, 'Electrical'),($shopid, 'Flush'),($shopid, 'Heating/Cooling'),($shopid, 'Interior'),($shopid, 'Oil Change'),($shopid, 'State Inspection'),($shopid, 'Suspension'),($shopid, 'Tires'),($shopid, 'Transmission'),($shopid, 'Drivetrain')";
if ($query = $conn->prepare($stmt))
{
  $query->execute();
  $conn->commit();
}

$stmt = "insert into `partsinventory` (`shopid`, `PartNumber`, `PartDesc`, `PartCost`, `PartPrice`, `OnHand`, `PartSupplier`) VALUES ($shopid, 'Filter', 'Oil Filter', '0.00', '0.00', '1', 'JUST IN TIME')";
if ($query = $conn->prepare($stmt))
{
  $query->execute();
  $conn->commit();
}

$stmt = "insert into `partsregistry` (`shopid`, `PartNumber`, `PartDesc`, `PartCost`, `PartPrice`, `PartSupplier`) VALUES ($shopid, 'Oil', '10W30 Oil', '0.00', '0.00', 'JUST IN TIME')";
if ($query = $conn->prepare($stmt))
{
  $query->execute();
  $conn->commit();
}

$stmt = "insert into `supplier` (`shopid`, `SupplierID`, `SupplierName`, `SupplierAddress`, `SupplierCity`, `SupplierState`, `SupplierZip`, `SupplierPhone`, `SupplierFax`, `SupplierContact`, `Active`) VALUES ('$shopid', 4, 'O\'Reilly Auto Parts', '13181 BLACK MOUNTAIN RD', 'SAN DIEGO', 'CA', '92129', '5555555555', '', 'REILLY', 'YES'), ('$shopid', 5, 'ADVANCE AUTO PARTS', '920 EAST FIRST STREET', 'SANTA ANA', 'CA', '92701', '5555555555', '', 'AAP', 'YES'), ('$shopid', 6, 'AUTO ZONE', '9152 MIRA MESA BL', 'SAN DIEGO', 'CA', '92126', '5555555555', '', 'AZ', 'YES'), ('$shopid', 7, 'NAPA AUTO', '7440 CONVOY CT', 'SAN DIEGO', 'CA', '92111', '5555555555', '', 'NAPS', 'YES')";


if ($query = $conn->prepare($stmt))
{
  $query->execute();
  $conn->commit();
}

$stmt = "insert into `cannedjobs` (`shopid`, `jobname`, `techstory`, `taxable`) VALUES ($shopid, '10W-30 Oil Change', 'Oil and Filter Change - Also do Inspection', 'yes')";
if ($query = $conn->prepare($stmt))
{
  $query->execute();
  $cannedid = $conn->insert_id;
  $conn->commit();
}

$stmt = "insert into `cannedparts` (`shopid`, `partnumber`, `partdescription`, `partcost`, `partprice`, `supplier`, `cannedjobsid`, `qty`) VALUES ($shopid, 'Oil', '10W30 Oil', '0.00', '0.00', 'JUST IN TIME', $cannedid, '5'),($shopid, 'Filter', 'Oil Filter', '0.00', '0.00', 'JUST IN TIME', $cannedid, '1'),($shopid, 'LOF Fee', 'LOF Disposal Fee', '0.00', '0.00', 'JUST IN TIME', $cannedid, '1')";
if ($query = $conn->prepare($stmt))
{
  $query->execute();
  $conn->commit();
}

$stmt = "insert into `cannedlabor` (`shopid`, `cannedjobsid`, `labor`, `laborhours`) VALUES ($shopid, $cannedid, 'Oil Change', '0.3'),($shopid, $cannedid, 'Courtesy Brake and Multi-Point Inspection performed, lube chassis - where applicable, check and set tire pressure', '0.0')";
if ($query = $conn->prepare($stmt))
{
  $query->execute();
  $conn->commit();
}


$stmt = "insert into `jobs` (`shortdescription`, `description`, `shopid`) values('AC - Inspection', 'The Air Conditioning System needs to be inspected for damage.','$shopid'),('AC - Not Cold', 'The Air Conditioning System isn\'t cooling properly.', '$shopid'),
('AC - Service', 'The Air Conditioning System needs service.', '$shopid'),
('Air Filter - NR', 'The Air Filter needs replacement.', '$shopid'),
('Alarm - Problem', 'The Alarm System is not working properly.', '$shopid'),
('Alarm - Problem', 'The Alarm System is not working properly.', '$shopid'),
('Alignment - 4 Wheel', 'The vehicle needs a four wheel alignment.', '$shopid'),
('Alignment - Front', 'The vehicle needs the front wheels aligned.', '$shopid'),
('Alignment - Pulls Left', 'The vehicle pulls to the left while driving.', '$shopid'),
('Alignment - Pulls Right', 'The vehicle pulls to the right while driving.', '$shopid'),
('Antenna - NR', 'The Radio Antenna needs replacement.', '$shopid'),
('Bearing - Left Front - NR', 'The left front wheel bearing needs replacement.', '$shopid'),
('Bearing - Left Rear Axle - NR', 'The Left Rear Axle Bearing needs replacement.', '$shopid'),
('Bearing - Right Front - NR', 'The Right Front Wheel Bearing needs replacement.', '$shopid'),
('Bearing - Right Rear Axle - NR', 'The Right Rear Axle Bearing needs replacement.', '$shopid'),
('Bearings - Front Wheel - Noise', 'The Front Wheel Bearings are making noise.', '$shopid'),
('Bearings - Rear Axle - Noise', 'The Rear Axle Bearings are making noise.', '$shopid'),
('Belt - Accessory Drive - NR', 'The Accessory Drive Belt needs replacement.', '$shopid'),
('Belt - Air Cond - NR', 'The Air Conditioning Drive Belt needs replacement.', '$shopid'),
('Belt - Alternator - NR', 'The Alternator Drive Belt needs replacement.', '$shopid'),
('Belt - Power Steering - NR', 'The Power Steering Drive belt needs replacement.', '$shopid'),
('Belt - Timing - NR', 'The Timing Belt needs replacement.', '$shopid'),
('Body', 'Paint Damage', '$shopid'),
('Body - Damage', 'There is Body damage on the', '$shopid'),
('Body - Exterior Wash', 'Exterior Wash.', '$shopid'),
('Body - Full Detail', 'Full Detail.', '$shopid'),
('Brake Lights - Problem', 'The Brake Lights are not working properly.', '$shopid'),
('Brake Line - Leaking', 'The Brake Line is leaking on the', '$shopid'),
('Brake Service - Front', 'The Front Brakes need service.', '$shopid'),
('Brake Service - Front/Rear', 'The Front and Rear Brakes need service.', '$shopid'),
('Brake Service - Rear', 'The Rear Brakes need service.', '$shopid'),
('Brakes - Grind', 'The Brakes make a grinding noise when stopping.', '$shopid'),
('Brakes - Inspection', 'The Brake System needs inspection.', '$shopid'),
('Brakes - Pull Left', 'The Brakes pull to the left when stopping.', '$shopid'),
('Brakes - Pull Right', 'The Brakes pull to the right when stopping.', '$shopid'),
('Brakes - Squeal - Driving', 'The Brakes squeal when driving.', '$shopid'),
('Brakes - Squeal - Stopping', 'The Brakes squeal when stopping.', '$shopid'),
('Catalytic Converter - NR', 'The Catalytic Converter needs replacement.', '$shopid'),
('Clutch - Adjustment', 'The Clutch needs adjustment.', '$shopid'),
('Clutch - Chattering', 'The Clutch is chattering upon acceleration.', '$shopid'),
('Clutch - NR', 'The Clutch needs repair or replacement.', '$shopid'),
('Clutch - Slipping', 'The Clutch is slipping.', '$shopid'),
('Code - Alarm', 'The Alarm Code is', '$shopid'),
('Code - Alarm', 'The Alarm Code is', '$shopid'),
('Code - Radio', 'The Radio Code is', '$shopid'),
('Code - Radio', 'The Radio Code is', '$shopid'),
('Cooling Sys - Overheats/Driving', 'The Cooling system overheats when driving.', '$shopid'),
('Cooling Sys - Overheats/Stopped', 'The Cooling System overheats when stopped.', '$shopid'),
('Cooling System - Inspection', 'The Cooling System needs to be inspected for damage.', '$shopid'),
('Cooling System - Service', 'The Cooling System needs service.', '$shopid'),
('CV Joint - Left Front - Noise', 'The Left Front CV Joint is making noise.', '$shopid'),
('CV Joint - Left Front - NR', 'The Left Front CV Joint needs replacement.', '$shopid'),
('CV Joint - Rt Front - Noise', 'The Right Front CV Joint is making noise.', '$shopid'),
('CV Joint - Rt Front - NR', 'The Right Front CV Joint needs replacement.', '$shopid'),
('Differential - Noise', 'The Differential is making noise.', '$shopid'),
('Distributor Cap - NR', 'The Distributor Cap needs replacement.', '$shopid'),
('Drive-ability - Electronic Diagnosis', 'The vehicle needs an Electronic Analysis to diagnose engine running problems and vehicle drive-ability.', '$shopid'),
('Drive-ability - Engine Runs Rough', 'The vehicle has drive-ability problems and the engine isn\'t running smoothly.', '$shopid'),
('Drive-ability - Won\'t Start', 'The vehicle won\'t start.', '$shopid'),
('Engine - Black Smoke', 'Accelerating', '$shopid'),
('Engine - Blue Smoke', 'Accelerating', '$shopid'),
('Engine - Gray Smoke', 'Accelerating', '$shopid'),
('Engine - Knock - Lower End', 'The engine has a knock in the lower end.', '$shopid'),
('Engine - Knock/Tapping - Upper End', 'The engine has a knock or tapping in the upper end.', '$shopid'),
('Engine - Rebuild', 'The engine needs a complete rebuild.', '$shopid'),
('Exhaust Hangers - NR', 'The Exhaust System Hangers need replacement.', '$shopid'),
('Exhaust System - Leak', 'There is a leak in the Exhaust System.', '$shopid'),
('Exhaust System - NR', 'The Exhaust System needs replacement.', '$shopid'),
('Flange Gasket - Leaking', 'The Manifold Flange Gasket is leaking.', '$shopid'),
('Front Shocks - NR', 'The Front Shocks need replacement.', '$shopid'),
('Front Springs - NR', 'The Front Springs need replacement.', '$shopid'),
('Front Windshield - Damaged', 'The Front Windshield is damaged.', '$shopid'),
('Front Windshield - NR', 'The Front Windshield needs replacement.', '$shopid'),
('Fuel Filter - NR', 'The Fuel Filter needs replacement', '$shopid'),
('Fuel Filter - NR', 'The Fuel Filter needs replacement.', '$shopid'),
('Fuel Injection - Service', 'The Fuel Injection system needs to be cleaned and serviced.', '$shopid'),
('Fuel Lines - Leaking', 'The Fuel Lines are leaking.', '$shopid'),
('Fuel Pump - NR', 'The Fuel Pump needs to be repaired or replaced.', '$shopid'),
('Fuel System', 'Clean', '$shopid'),
('Fuel System - Leaking', 'The Fuel System needs to be inspected for leaks.', '$shopid'),
('Headlight - LF - Problem', 'The Left Front Headlight is not working properly.', '$shopid'),
('Headlight - RF - Problem', 'The Right Front Headlight is not working properly.', '$shopid'),
('Headlights - Problem', 'The Headlights are not working properly.', '$shopid'),
('Heater - Not Hot', 'The Heating System not working properly.', '$shopid'),
('Heater Core - Leaking', 'The Heater Core is leaking fluid.', '$shopid'),
('Heater Hose - NR', 'The Heater Hose is bad and needs replacement.', '$shopid'),
('Heater Hoses - Leaking', 'The Heater Hoses are leaking fluid.', '$shopid'),
('Horn - Problem', 'The Horn is not working properly.', '$shopid'),
('Ignition - Problems', 'The vehicle has Ignition problems.  It is not starting and/or running properly.', '$shopid'),
('Interior', 'Full Detail', '$shopid'),
('Interior Lights - Problem', 'The Interior Lights are not working properly.', '$shopid'),
('Labor & Parts', 'Labor Services and Associated Parts.', '$shopid'),
('LOF - Service', 'The vehicle needs a Chassis Lubrication', '$shopid'),
('Manifold - Cracked', 'The Exhaust Manifold is cracked and leaking.', '$shopid'),
('Manifold Gasket - Leaking', 'The Exhaust Manifold Gasket is leaking.', '$shopid'),
('Mirror - Left - NR', 'The Left Outside Mirror needs repair or replacement.', '$shopid'),
('Mirror - Right - NR', 'The Right Outside Mirror needs repair or replacement.', '$shopid'),
('Muffler - NR', 'The Muffler needs replacement.', '$shopid'),
('Oil & Filter - Service', 'The vehicle needs an Oil and Filter Service.', '$shopid'),
('Oxygen Sensor - NR', 'The Oxygen Sensor needs replacement.', '$shopid'),
('Power Steering Hose - NR', 'The Power Steering Hose needs replacement.', '$shopid'),
('Power Steering Pump - NR', 'The Power Steering Pump needs replacement.', '$shopid'),
('Power Window - NR', 'The Power Window mechanism is not working properly on the', '$shopid'),
('Radiator - Leaking', 'The Radiator is leaking and needs inspection to determine the cause.', '$shopid'),
('Radiator - NR', 'The Radiator is bad and needs replacement.', '$shopid'),
('Radiator - Rod Out', 'The Radiator has blocked passages and needs to be rodded out.', '$shopid'),
('Radiator Hose - Bypass NR', 'The Bypass Hose needs replacement.', '$shopid'),
('Radiator Hose - Lower NR', 'The Lower Radiator Hose needs replacement.', '$shopid'),
('Radiator Hose - Upper NR', 'The Upper Radiator Hose needs replacement.', '$shopid'),
('Radio - Problem', 'The Radio is not working properly.', '$shopid'),
('Rear Shocks - NR', 'The Rear Shocks need replacement.', '$shopid'),
('Rear Springs - NR', 'The Rear Springs need replacement.', '$shopid'),
('Rear Window - Damage', 'The Rear Window is damaged.', '$shopid'),
('RFCs & VPs', 'Requests for Service & Vehicle Problems -', '$shopid'),
('Safety Inspection', 'The Vehicle needs a Safety Inspection.', '$shopid'),
('Service - 15K', 'The vehicle needs a 15000 mile Maintenance Service.', '$shopid'),
('Service - 30K', 'The vehicle needs a 30000 mile Maintenance Service.', '$shopid'),
('Service - 60K', 'The vehicle needs a 60000 mile Maintenance Service.', '$shopid'),
('Side Window  - Damaged', 'The Side Window is damaged.', '$shopid'),
('Smog - Inspection/Certification', 'The vehicle needs a Smog Inspection and State Certification.', '$shopid'),
('Smog Inspection', 'The vehicle needs a Smog Inspection and State Certification.', '$shopid'),
('Smog Pump - NR', 'The Smog Pump needs repair or replacement.', '$shopid'),
('Spark Plug Wires - NR', 'The Spark Plug Wires need replacement.', '$shopid'),
('Spark Plugs - NR', 'The Spark Plugs need replacement.', '$shopid'),
('Struts - NR', 'The vehicle needs the Suspension Struts to be replaced.', '$shopid'),
('Tail Light - LR - Problem', 'The Left Rear Tail Light is not working properly.', '$shopid'),
('Tail Light - RR - Problem', 'The Right Rear Tail Light is not working properly.', '$shopid'),
('Tail Lights - Problem', 'The Tail Lights are not working properly.', '$shopid'),
('Thermostat - NR', 'The Thermostat needs replacement.', '$shopid'),
('Throw-Out Bearing - Noise', 'The Clutch throw-out bearing is making noise.', '$shopid'),
('Throw-Out Bearing NR', 'The Throw-Out Bearing needs replacement.', '$shopid'),
('Tire - Repair - LF', 'The Left Front Tire needs repair.', '$shopid'),
('Tire - Repair - LR', 'The Left Rear Tire needs repair.', '$shopid'),
('Tire - Repair - RF', 'The Right Front Tire needs repair.', '$shopid'),
('Tire - Repair - RR', 'The Right Rear Tire needs repair.', '$shopid'),
('Tires - Replace - 2 Front', 'Both front tires need replacement.', '$shopid'),
('Tires - Replace - 2 Rear', 'Both rear tires need replacement.', '$shopid'),
('Tires - Replace - All 4', 'All the tires on the vehicle need replacement.', '$shopid'),
('Tires - Replace - LF', 'The Left Front Tire needs replacement.', '$shopid'),
('Tires - Replace - LR', 'The Left Rear Tire needs replacement.', '$shopid'),
('Tires - Replace - RF', 'The Right Front Tire needs replacement.', '$shopid'),
('Tires - Replace - RR', 'The Right Rear Tire needs replacement.', '$shopid'),
('Tires - Rotate - All 4', 'The Tires need rotating.', '$shopid'),
('Tires - Warranty Repair', 'Tire needs Warranty Repair.', '$shopid'),
('Tires - Warranty Replacement', 'Tire needs Warranty Replacement.', '$shopid'),
('Transaxle - Inspection', 'The Transaxle needs to be inspected for damage.', '$shopid'),
('Transaxle - No Drive', 'The Transaxle doesn\'t move vehicle forward in DRIVE.', '$shopid'),
('Transaxle - No Reverse', 'The Transaxle doesn\'t move vehicle backwards in REVERSE.', '$shopid'),
('Transaxle - Rebuild', 'The Transaxle needs to be rebuilt.', '$shopid'),
('Transaxle - Service', 'The Transaxle needs standard Maintenance Service.', '$shopid'),
('Transaxle - Shifting Problems', 'The Transaxle has shifting problems.', '$shopid'),
('Transaxle - Slips in Drive', 'The Transaxle slips when in DRIVE.', '$shopid'),
('Transaxle - Slips in Reverse', 'The Transaxle slips when in REVERSE.', '$shopid'),
('Transmission - Inspection', 'The Transmission needs to be inspected for damage.', '$shopid'),
('Transmission - No Drive', 'The Transmission doesn\'t move vehicle forward in DRIVE.', '$shopid'),
('Transmission - No Reverse', 'The Transmission doesn\'t move vehicle backwards in REVERSE.', '$shopid'),
('Transmission - Rebuild', 'The Transmission needs to be rebuilt.', '$shopid'),
('Transmission - Service', 'The Transmission needs standard Maintenance Service.', '$shopid'),
('Transmission - Service', 'The Transmission needs a standard maintenance service.', '$shopid'),
('Transmission - Shifting Problems', 'The Transmission has shifting problems.', '$shopid'),
('Transmission - Slips in Drive', 'The Transmission slips when in DRIVE.', '$shopid'),
('Transmission - Slips in Reverse', 'The Transmission slips when in REVERSE.', '$shopid'),
('Turn Signal Blinker - Problem', 'The Turn Signals are not blinking properly.', '$shopid'),
('Turn Signal Lights - Problem', 'The Turn Signal Lights are not working properly.', '$shopid'),
('U Joint - Front - NR', 'The Front U Joint needs replacement.', '$shopid'),
('U Joint - Rear - NR', 'The Rear U Joint needs replacement.', '$shopid'),
('Washer Pump - Problem', 'The Windshield Washer Pump is not working properly.', '$shopid'),
('Wiper Blades - NR', 'The Wiper Blades need replacement.', '$shopid'),
('Wiper Motor - Problem', 'The Wiper Motor is not working properly.', '$shopid'),
('Wiper Switch - Problem', 'The Wiper Switch is not working properly.', '$shopid')";
if ($query = $conn->prepare($stmt))
{
  $query->execute();
  $conn->commit();
}

$stmt = <<<CAMPQUERY
        insert into `campaigns` (`shopid`, `campaign`, `startdate`, `description`, `emailid`, `active`, `type`, `message`) values ($shopid, '1 Day Review Request ', '2012-10-12', '0', 0, 'no', '1', 'html<p>Dear [CustomerName],&nbsp;<br><span style=\"background-color: var(--white); color: var(--textColor); font-family: var(--mdb-body-font-family); font-weight: var(--mdb-body-font-weight); text-align: var(--mdb-body-text-align);\"><br>We want to take a moment to thank you for choosing [ShopName] . We truly appreciate your support and hope you enjoyed your experience with us.&nbsp;</span><span style=\"background-color: var(--white); color: var(--textColor); font-family: var(--mdb-body-font-family); font-weight: var(--mdb-body-font-weight); text-align: var(--mdb-body-text-align);\">If you had a great experience , we’d be incredibly grateful if you could take a minute to leave us a 5-star review HERE. Your feedback not only helps us improve but also helps other customers make informed decisions when looking for their next vehicle repair.&nbsp;<br><br>If for any reason you feel like you did not receive 5 star service please give us a call at [ShopPhone]  and we are more than happy to address any concerns! Thank you again for being a valued customer and we look forward to hearing from you soon!&nbsp;<br><br><br></span></p>'), 
        
         ('$shopid','5 Day Follow Up', '2012-10-12', '0', 0, 'no', '5', 'Dear [CustomerName],\r\nI would like to thank you for the opportunity to work on your [VehicleYear] [VehicleMake] [VehicleModel] recently.  Here at [ShopName] we want to make sure you are completely satisfied with our work.\r\n\r\nTo say thank you in a more tangible way, I am including an offer code for you to redeem at any time.\r\n\r\nOil and Filter Change\r\nCheck Air and Cabin Air Filters\r\nCheck Belts and Hoses\r\nInspect Brakes\r\nRoad Test\r\nTire rotation\r\n\r\nOnly $29.95!  Mention offer code 2995 when calling for your appointment!\r\n\r\nThanks again and we look forward to serving again soon!\r\n\r\nJohn\r\n[ShopName]\r\n[ShopPhone]\r\n[ShopLogo]\r\n\r\n\r\nIf you prefer not to receive our follow up emails, please follow this link to remove your email address from our list.  https://{$_SERVER['SERVER_NAME']}/remove.php?email=[CustomerEmail]'),
         
        ('$shopid', '30 Day Follow Up', '0000-00-00', '0', 0, 'no', '30', 'html<p>Hi&nbsp;[CustomerName],&nbsp;<br><br>It’s been a couple of weeks since your repair on your [VehicleYear] [VehicleMake] [VehicleModel]   , and we wanted to check in to see how everything is going. Here at&nbsp;[ShopName], we strive to ensure your complete satisfaction. If you have any questions or if there is anything we can assist you with, please don\'t hesitate to reach out to us.<br><br>To express our appreciation for you being a valued customer, we\'d like to offer you 10 percent off your next eligible repair on your next visit. Just mention this email when you stop in.&nbsp;<br><br>Thank you for trusting us with your business and we look forward to seeing you again soon!&nbsp;</p>'),
        
         ('$shopid','60 Day Follow Up', '0000-00-00', '0', 0, 'no', '60', 'Dear [CustomerName],\r\nI would like to thank you for the opportunity to work on your [VehicleYear] [VehicleMake] [VehicleModel] recently.  Here at [ShopName] we want to make sure you are completely satisfied with our work.\r\n\r\nTo say thank you in a more tangible way, I am including an offer code for you to redeem at any time.\r\n\r\nOil and Filter Change\r\nCheck Air and Cabin Air Filters\r\nCheck Belts and Hoses\r\nInspect Brakes\r\nRoad Test\r\n\r\nOnly $29.95!  Mention offer code 2995 when calling for your appointment!\r\n\r\nThanks again and we look forward to serving again soon!\r\n\r\nJohn\r\n[ShopName]\r\n[ShopPhone]\r\n[ShopLogo]\r\n\r\nIf you prefer not to receive our follow up emails, please follow this link to remove your email address from our list.  https://{$_SERVER['SERVER_NAME']}/remove.php?email=[CustomerEmail]'),
         
        
       ($shopid, '90 Day Regular Maintenance Reminder ', '0000-00-00', '0', 0, 'no', '90', 'html<p>Hi&nbsp;[CustomerName]  </p><p>We hope you’re enjoying your newly repaired vehicle! It’s been about 90 days since your last visit, and we wanted to check in.</p><p>Regular maintenance is crucial to keeping your car in top shape, and we’re here to help with any services you may need. Whether it’s a routine check-up, an oil change, or any other concern, our team is ready to assist you.</p><p>If you have any questions or would like to schedule an appointment, just reply to this email or give us a call at [ShopPhone]. We’re always here to ensure your vehicle runs smoothly!</p><p>Thank you for choosing [ShopName]. We look forward to seeing you again soon!</p>'),
       
        
        
        ('$shopid','90 Day Service Special', '0000-00-00', '0', 0, 'no', '90', '[CustomerName][CustomerAddress]Dear [CustomerName],\r\nAt [ShopName], we want to be sure to offer our loyal customers with something to help out during the tough times.  Making ends meet in a difficult economy is challenging, so we would like to help.\r\n\r\nTo say thanks, we are including an offer code for you to redeem at any time..\r\n\r\nOil and Filter Change\r\nCheck Air and Cabin Air Filters\r\nCheck Belts and Hoses\r\nInspect Brakes\r\nRoad Test\r\n\r\nOnly $29.95!  Mention offer code 2995 when calling for your appointment!\r\n\r\nThanks again and we look forward to serving again soon!\r\n\r\nJohn\r\n[ShopName]\r\n[ShopPhone]\r\n[ShopLogo]\r\n\r\nIf you prefer not to receive our follow up emails, please follow this link to remove your email address from our list.  https://{$_SERVER['SERVER_NAME']}/remove.php?email=[CustomerEmail]');

    CAMPQUERY;
if ($query = $conn->prepare($stmt))
{
  $query->execute();
  $conn->commit();
}


$stmt = "insert into accounttypes (type,shopid) values ('Current Assets','$shopid'),('Current Liabilities','$shopid'),('Equity','$shopid'),('Current Liabilities','$shopid'),('Income','$shopid'),('Cost of Goods Sold','$shopid'),('Expense','$shopid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}

$stmt = "insert into discountreasons (`shopid`, `discountreason`, `type`, `parts`, `labor`, `partsmax`, `labormax`) values ('$shopid','AAA','percent','0','0','0','0'),('$shopid','Military','percent','10','10','100','100')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}

$stmt = "insert into notification_settings (`shopid`, `notification_type`, `popup_notify`) values ('$shopid','68','1'),('$shopid','178','1'),('$shopid','181','1'),('$shopid','80','1'),('$shopid','83','1'),('$shopid','86','1')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}


$stmt = "insert into `chartofaccounts` (shopid,category,cattype,core) values ('$shopid', 'Accounting and Bookkeeping', 'Expense', 'yes'),
('$shopid', 'Automobile Expenses', 'Expense', 'yes'),
('$shopid', 'Bank Service charges and Fees', 'Expense', 'yes'),
('$shopid', 'Books and Periodicals', 'Expense', 'yes'),
('$shopid', 'Business/Trade Conventions', 'Expense', 'yes'),
('$shopid', 'Business Gifts', 'Expense', 'yes'),
('$shopid', 'Business Meals', 'Expense', 'yes'),
('$shopid', 'Equipment', 'Expense', 'yes'),
('$shopid', 'Computer Hardware', 'Expense', 'yes'),
('$shopid', 'Computer Software', 'Expense', 'yes'),
('$shopid', 'Consulting Fees', 'Expense', 'yes'),
('$shopid', 'Depreciation and Amortization', 'Expense', 'yes'),
('$shopid', 'Dues and Subscriptions', 'Expense', 'yes'),
('$shopid', 'Education Expenses', 'Expense', 'yes'),
('$shopid', 'Internet', 'Expense', 'yes'),
('$shopid', 'Web Hosting and Email', 'Expense', 'yes'),
('$shopid', 'Insurance', 'Expense', 'yes'),
('$shopid', 'Legal and Attorney Fees', 'Expense', 'yes'),
('$shopid', 'License Fees and Taxes', 'Expense', 'yes'),
('$shopid', 'Credit Card Processing Fees', 'Expense', 'yes'),
('$shopid', 'Office Furniture and Equipment', 'Expense', 'yes'),
('$shopid', 'Office Supplies', 'Expense', 'yes'),
('$shopid', 'Parking and Tolls', 'Expense', 'yes'),
('$shopid', 'Postage and Shipping', 'Expense', 'yes'),
('$shopid', 'Printing and Duplication', 'Expense', 'yes'),
('$shopid', 'Self-Employment Taxes', 'Expense', 'yes'),
('$shopid', 'Start-up Expenses', 'Expense', 'yes'),
('$shopid', 'State and Local Business Taxes', 'Expense', 'yes'),
('$shopid', 'Telephone Expense', 'Expense', 'yes'),
('$shopid', 'Travel Expenses', 'Expense', 'yes'),
('$shopid', 'Utilities', 'Expense', 'yes'),
('$shopid', 'Advertising', 'Expense', 'yes'),
('$shopid', 'Rent', 'Expense', 'yes'),
('$shopid', 'Cost of Goods Sold', 'Expense', 'yes'),
('$shopid', 'Income from Work Performed', 'Income', 'yes'),
('$shopid', 'Income from Part Sales', 'Income', 'yes'),
('$shopid', 'Cost of Labor Sold', 'Expense', 'yes'),
('$shopid', 'Other Income', 'Income', 'yes'),
('$shopid', 'Income Taxes', 'Expense', 'yes')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}


$stmt = "insert into inspections (shopid,inspectionname) values ('$shopid','Standard')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $inspid = $conn->insert_id;
 $conn->commit();
}

$stmt = "insert into inspectioncategories (shopid,category,displayorder,inspectionid) values ('$shopid','Interior/Exterior','1','$inspid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $catid = $conn->insert_id;
 $conn->commit();
}

$stmt = "insert into inspectionitems (catid,shopid,`desc`,categoryid) values ('Interior/Exterior','$shopid','Headlights, Tail Lights, Turn Signals','$catid'),('Interior/Exterior','$shopid','Windshield Wipers','$catid'),('Interior/Exterior','$shopid','Horn','$catid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}

$stmt = "insert into inspectioncategories (shopid,category,displayorder,inspectionid) values ('$shopid','Underhood','2','$inspid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $catid = $conn->insert_id;
 $conn->commit();
}

$stmt = "insert into inspectionitems (catid,shopid,`desc`,categoryid) values ('Underhood','$shopid','Belts','$catid'),('Underhood','$shopid','Hoses','$catid'),('Underhood','$shopid','Air Filter','$catid'),('Underhood','$shopid','Visible Leaks','$catid'),('Underhood','$shopid','Radiator and Coolant','$catid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}

$stmt = "insert into inspectioncategories (shopid,category,displayorder,inspectionid) values ('$shopid','Tires','3','$inspid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $catid = $conn->insert_id;
 $conn->commit();
}

$stmt = "insert into inspectionitems (catid,shopid,`desc`,categoryid) values ('Tires','$shopid','R/F Tread Depth','$catid'),('Tires','$shopid','R/F Tire Pressure','$catid'),('Tires','$shopid','R/F Tire','$catid'),('Tires','$shopid','L/F Tread Depth','$catid'),('Tires','$shopid','L/F Tire Pressure','$catid'),('Tires','$shopid','L/F Tire','$catid'),('Tires','$shopid','R/R Tread Depth','$catid'),('Tires','$shopid','R/R Tire Pressure','$catid'),('Tires','$shopid','R/R Tire','$catid'),('Tires','$shopid','L/R Tread Depth','$catid'),('Tires','$shopid','L/R Tire Pressure','$catid'),('Tires','$shopid','L/R Tire','$catid'),('Tires','$shopid','Spare Tire Pressure','$catid'),('Tires','$shopid','Spare Tire Tread Depth','$catid'),('Tires','$shopid','Spare Tire','$catid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}


$stmt = "insert into inspectioncategories (shopid,category,displayorder,inspectionid) values ('$shopid','Steering and Suspension','4','$inspid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $catid = $conn->insert_id;
 $conn->commit();
}

$stmt = "insert into inspectionitems (catid,shopid,`desc`,categoryid) values ('Steering and Suspension','$shopid','CV Boots, Half Shafts, Control Arms','$catid'),('Steering and Suspension','$shopid','Ball Joints, Steering Linkage, Sway bar links','$catid'),('Steering and Suspension','$shopid','Shocks and Struts','$catid'),('Steering and Suspension','$shopid','Alignment','$catid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}


$stmt = "insert into inspectioncategories (shopid,category,displayorder,inspectionid) values ('$shopid','Brakes','5','$inspid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $catid = $conn->insert_id;
 $conn->commit();
}

$stmt = "insert into inspectionitems (catid,shopid,`desc`,categoryid) values ('Brakes','$shopid','Front brake pad LF%','$catid'),('Brakes','$shopid','Front brake pad RF%','$catid'),('Brakes','$shopid','Rear Brake Pads/Shoes LR%','$catid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}


$stmt = "insert into inspectioncategories (shopid,category,displayorder,inspectionid) values ('$shopid','Under Vehicle','6','$inspid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $catid = $conn->insert_id;
 $conn->commit();
}

$stmt = "insert into inspectionitems (catid,shopid,`desc`,categoryid) values ('Under Vehicle','$shopid','u-joints, driveshaft, pinion bearing','$catid'),('Under Vehicle','$shopid','Exhaust Leaks','$catid'),('Under Vehicle','Visible Leaks','$catid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}


$stmt = "insert into priority (shopid,p) values ('$shopid','Waiting'),('$shopid','High'),('$shopid','Low')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}

$stmt = "insert into vehiclelabels (shopid) values ('$shopid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}

$stmt = "insert into reqfields (shopid) values ('$shopid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}

$stmt = "insert into techmodesettings (shopid) values ('$shopid')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}

$chex = generateRandomStr(18);
$stmt = "insert into colorcoding (shopid,title,colorhex) values ('$shopid','DEFAULT LABEL','$chex')";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $conn->commit();
}

$stmt = "select * from inspection_models where preload='1' AND `name`='100 point inspection'";
if ($query = $conn->prepare($stmt))
{
 $query->execute();
 $results = $query->get_result();
 while($rs = $results->fetch_assoc())
 {
  $modelid = $rs['id'];

  $stmt = "insert into inspection_models (shopid,name,isdefault,enabled) values (?,?,1,1)";
  if ($query = $conn->prepare($stmt))
  {
   $query->bind_param("ss", $shopid, $rs['name']);
   $query->execute();
   $newmodelid = $query->insert_id;
   $conn->commit();
   $query->close();
  }

  $stmt = "select id,name,display_order from inspection_categories where model_id = ?";
  if ($query = $conn->prepare($stmt))
  {
   $query->bind_param("i",$modelid);
   $query->execute();
   $catresults = $query->get_result();
   while($catrs = $catresults->fetch_assoc()) //loop thorugh categories
  {
    $modelmap = array();
    $stmt = "insert into inspection_categories (shopid,model_id,name,display_order) values (?,?,?,?)";
    if ($query = $conn->prepare($stmt))
    {
     $query->bind_param("ssss", $shopid, $newmodelid, $catrs['name'],$catrs['display_order']);
     $query->execute();
     $categoryid = $query->insert_id;
     $conn->commit();
     $query->close();
    }

    $stmt = "select id,name,section_type,display_order from inspection_sections where category_id = ? ORDER BY section_type desc";
    if ($query = $conn->prepare($stmt))
    {
     $query->bind_param("i",$catrs['id']);
     $query->execute();
     $secresults = $query->get_result();
     while($secrs = $secresults->fetch_assoc()) //loop thorugh sections
     {
        $stmt = "insert into inspection_sections (shopid,category_id,name,section_type,display_order) values (?,?,?,?,?)";
        if ($query = $conn->prepare($stmt))
        {
         $query->bind_param("ssssi", $shopid, $categoryid, $secrs['name'],$secrs['section_type'],$secrs['display_order']);
         $query->execute();
         $sectionid = $query->insert_id;
         $conn->commit();
         $query->close();
        }

        $stmt = "select id from inspection_item_models where category_id = ? and section_id = ?";
        if ($query = $conn->prepare($stmt))
        {
         $query->bind_param("ii",$catrs['id'],$secrs['id']);
         $query->execute();
         $imresults = $query->get_result();
         while($imrs = $imresults->fetch_assoc()) //loop thorugh sections
         {
            $stmt = "insert into inspection_item_models (shopid,category_id,section_id,name,user_input_type,measurement_values,measurements_count,measurement_name1,measurement_name2,public_measurement_name1,public_measurement_name2,display_order) select '$shopid','$categoryid','$sectionid',name,user_input_type,measurement_values,measurements_count,measurement_name1,measurement_name2,public_measurement_name1,public_measurement_name2,display_order from inspection_item_models where id = ?";
            if ($query = $conn->prepare($stmt))
            {
             $query->bind_param("i", $imrs['id']);
             $query->execute();
             $item_model_id = $query->insert_id;
             $conn->commit();
             $query->close();
            }

            $modelmap[$imrs['id']] = $item_model_id;

            $stmt = "select * from inspection_item_model_measurements where item_model_id = ?";
            if ($query = $conn->prepare($stmt))
            {
             $query->bind_param("i",$imrs['id']);
             $query->execute();
             $iimmresults = $query->get_result();
             while($iimmrs = $iimmresults->fetch_assoc())
             {
              $stmt = "insert into inspection_item_model_measurements (shopid,item_model_id,measurement_item_model_id,which_measurement) values (?,?,?,?)";
              if ($query = $conn->prepare($stmt))
              {
               $query->bind_param("siii", $shopid, $item_model_id,$modelmap[$iimmrs['measurement_item_model_id']],$iimmrs['which_measurement']);
               $query->execute();
               $conn->commit();
               $query->close();
              }
             }
            }

            if($secrs['section_type']=='finding')//finding starts
            {
              $stmt = "select f.id,f.description,iimf.display_order from inspection_item_model_findings iimf,inspection_findings f where iimf.shopid=f.shopid and iimf.finding_id=f.id and iimf.item_model_id = ?";
              if ($query = $conn->prepare($stmt))
              {
               $query->bind_param("i",$imrs['id']);
               $query->execute();
               $iimresults = $query->get_result();
               while($iimrs = $iimresults->fetch_assoc()) //loop thorugh findings
               {

                $stmt = "select id from inspection_findings where shopid = ? and description = ?";
                if ($query = $conn->prepare($stmt))
                {
                 $query->bind_param("ss",$shopid,$iimrs['description']);
                 $query->execute();
                 $query->store_result();
                 $numrows = $query->num_rows();
                 if ($numrows<1)
                 {
                  $stmt = "insert into inspection_findings (shopid,description) values (?,?)";
                  if ($query = $conn->prepare($stmt))
                  {
                   $query->bind_param("ss", $shopid, $iimrs['description']);
                   $query->execute();
                   $findingid = $query->insert_id;
                   $conn->commit();
                   $query->close();
                  }
                 }
                 else
                 {
                  $query->bind_result($findingid);
                  $query->fetch();
                 }
                }

                $stmt = "select description,display_order from inspection_finding_recommendations where finding_id = ?";
                if ($query = $conn->prepare($stmt))
                {
                 $query->bind_param("i",$iimrs['id']);
                 $query->execute();
                 $recresults = $query->get_result();
                 while($recrs = $recresults->fetch_assoc()) //loop thorugh recommendations
                 {
                  $stmt = "select id from inspection_finding_recommendations where shopid = ? and finding_id = ? and description = ?";
                  if ($query = $conn->prepare($stmt))
                  {
                   $query->bind_param("sis",$shopid,$findingid,$recrs['description']);
                   $query->execute();
                   $query->store_result();
                   $numrows = $query->num_rows();
                   if ($numrows<1)
                   {
                    $stmt = "insert into inspection_finding_recommendations (shopid,finding_id,description,display_order) values (?,?,?,?)";
                    if ($query = $conn->prepare($stmt))
                    {
                     $query->bind_param("sisi", $shopid,$findingid,$recrs['description'],$recrs['display_order']);
                     $query->execute();
                     $conn->commit();
                     $query->close();
                    }
                   }
                  }

                 }
                }

                $stmt = "insert into inspection_item_model_findings (shopid,item_model_id,finding_id,display_order) values (?,?,?,?)";
                if ($query = $conn->prepare($stmt))
                {
                 $query->bind_param("siii", $shopid, $item_model_id, $findingid, $iimrs['display_order']);
                 $query->execute();
                 $conn->commit();
                 $query->close();
                }

               }
              }

            }//finding ends

         }
        }

     }//sections while ends
    }


   }//categories while ends
  }
}
}

$message = "<img src='https://".$_SERVER['SERVER_NAME']."/sbp/newimages/shopbosslogo.png'><br />A new account has been created for the following customer:<br><br>Shop Name: $shopname<br>Shop ID: $shopid<br>Contact Name: $first_name $last_name<br>Adrress: $address<br>City: $city<br>State: $state<br>Zip: $zip<br>Phone: $phone<br>Email: $shopemail<br>Start Date: $date<br>Source: $source";



if($matco=="yes")
{
 //$res = sendEmailMandrillTemplate($shopemail,"",$from,"Matco New Account",array('SHOPID'=>$shopid),NULL,"<EMAIL>");
 $res = sendEmailMandrillTemplate('<EMAIL>',"",$from,"Matco DSR New Email",array('SHOPID'=>$shopid),NULL,"<EMAIL>");
 $res = sendEmailMandrillTemplate('<EMAIL>',"",$from,"Matco DSR New Email",array('SHOPID'=>$shopid),NULL,"<EMAIL>");
 $res = sendEmailMandrill($from,"Your Trial Subscription to Shop Boss Pro",$message,"Shop Boss Pro",$from,"<EMAIL>");
}
elseif($protractor=="yes")
{
 //$res = sendEmailMandrillTemplate($shopemail,"",$from,"Protractor New Account",array('SHOPID'=>$shopid),NULL,"<EMAIL>");
 $res = sendEmailMandrill($from,"Your Trial Subscription to Shop Boss Pro",$message,"Shop Boss Pro",$from,"<EMAIL>");
}
else
{
 //$res = sendEmailMandrillTemplate($shopemail,"",$from,"Shop Boss New Account",array('SHOPID'=>$shopid, 'PASSWORD'=>$password));
 $res = sendEmailMandrill($from,"Your Trial Subscription to Shop Boss Pro",$message,"Shop Boss Pro",$from);
}


$first = str_replace("'","",$first_name);
$last =  str_replace("'","",$last_name);
$shopname = strtoupper(str_replace("'","",$shopname));
$address = strtoupper(str_replace("'","",$address));
$city = strtoupper(str_replace("'","",$city));
$state = strtoupper(str_replace("'","",$state));
$zip = strtoupper(str_replace("'","",$zip));

/*$data = array('first' => $first, 'last' => $last, 'shopname' => $shopname,'address'=>$address,'city'=>$city,'state'=>$state,'zip'=>$zip,'shopid'=>$shopid,'phone'=>$phone,'email'=>$shopemail);
$jsonEncodedData = json_encode($data);
$curl = curl_init();
$opts = array(
    CURLOPT_URL             => 'https://hooks.zapier.com/hooks/catch/2237757/o86conv/',
    CURLOPT_RETURNTRANSFER  => true,
    CURLOPT_CUSTOMREQUEST   => 'POST',
    CURLOPT_POST            => 1,
    CURLOPT_POSTFIELDS      => $jsonEncodedData,
    CURLOPT_HTTPHEADER  => array('Content-Type: application/json','Content-Length: ' . strlen($jsonEncodedData))
);
curl_setopt_array($curl, $opts);
$result = curl_exec($curl);
curl_close($curl);*/

$data = array('SHOP_BOSS_LEGACY_CRM_ID__C'=> $shopid ,'Shop_Boss_Shop_Id__c'=> $shopid ,'Name' => $shopname,'BILLINGSTREET' => $address,'Billing City' => $city,'BILLINGSTATECode' => $state,'Billing Postal Code' => $zip,'BillingCountry' => '','Phone' => $phone,'Email' => $shopemail,'SHOP_BOSS_LEGACY_SYSTEM_CREATED_DATE__C' => $date,'Contract_Sign_Date__c' => $date,'Contact' => $first.' '.$last);
$jsonEncodedData = json_encode($data);
$curl = curl_init();
$opts = array(
    CURLOPT_URL             => 'https://endpoint.scribesoft.com/v1/orgs/47078/requests/22702?accesstoken=03fdbdf1-0c25-4fc8-84fb-472f7a90e4d6',
    CURLOPT_RETURNTRANSFER  => true,
    CURLOPT_CUSTOMREQUEST   => 'POST',
    CURLOPT_POST            => 1,
    CURLOPT_POSTFIELDS      => $jsonEncodedData,
    CURLOPT_HTTPHEADER  => array('Content-Type: application/json','Content-Length: ' . strlen($jsonEncodedData))
);
curl_setopt_array($curl, $opts);
$result = curl_exec($curl);
curl_close($curl);

$stmt = "insert into `errors` (`desc`,`fulldesc`,shopid) values ('Salesforce Create Trial',?,?)";
if ($query = $conn->prepare($stmt))
{
 $query->bind_param("ss",$jsonEncodedData,$shopid);
 $query->execute();
 $conn->commit();
}

echo(json_encode(array("status"=>true,"message"=>"Success","shopid"=>$shopid,"ip"=>$userip)));
?>
