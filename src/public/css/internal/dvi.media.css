
.inspect-image {
    max-width: 300px !important;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.card img {
    max-width: 400px !important;
    margin: 0 auto;
}

@media screen and (max-width: 480px) {
    .card img {
        max-width: 300px !important;
    }
}

.card .header-photo-button-container{
    width: 100%;
    display: flex;
    justify-content: center;
}

.inspect-dropdown {
    max-width: 95%;
    margin: 5px;
}

#dvi-preview-col {
    height: calc(100vh - 110px);
    overflow-y: auto;
}

@media print {
    @page {
        size: A4 portrait;
        margin: 0.5cm;
        padding: 0;
    }

    body, h1, h2, h3, h4, h5, h6, ol, ul, div, span {
        width: auto;
        border: 0 !important;
        float: none;
        overflow: visible;
        font-size: 12px;
    }

    body {
        font-size: 12px;
    }

    h1, h2, h3 {
        font-size: 16px !important;
    }

    h4, h5, h6 {
        font-size: 14px !important;
    }

    .table tr td, .table tr th {
        font-size: 12px !important;
        padding: 8px;
    }

    #dvi-inspection-preview, #dvi-preview-col {
        position: relative;
        overflow: visible;
    }

    #dvi-preview-col {
        height: auto !important;
        overflow-y: visible;
    }

    hr {
        margin: 0px;
        margin-top: 2px;
    }

    .border, .bottom-border, .--main {
        border-style: none !important;
        border-bottom-width: 0px !important;
    }

    .card, .inspection-summary-item, .inspection-summary-item div {
        box-shadow: none;
    }

    .card, img, .d-flex {
        display: block !important;
    }

    img {
        display: block !important;
        overflow: hidden;
        break-inside: avoid;
        page-break-inside: avoid;
    }

    .review-item .mt-3 {
        margin: 0px !important;
    }

    #dvi-preview-col span .card:last-child {
        /*   break-after: avoid;
           page-break-after: avoid;
         */
    }

    .card-body .bg-info, .card-body .bg-primary, .card-body .bg-danger, .card-body .bg-warning, .card-body .bg-success, .item-heading, div.--main.--good, .--main.--good .--main-content__item {
        font-size: 16px !important;
        color: black !important;
        display: block;
        border-bottom: black 1px solid !important;
        background: white !important;
        font-weight: bold !important;
    }

    .card .card-body {
        padding: 0px;
    }

    .breadcrumb {
        -webkit-box-pack: center !important;
        -ms-flex-pack: center !important;
        justify-content: center !important;
    }

    #customer_info .col-md-4 {
        width: 33.33%;
    }

    #customer_info .inspect-image {
        margin-bottom: 15px;
    }

    .no-img img, .no-img .hide-image {
        display: none !important;
    }

    button.btn {
        display: none;
    }
}

/* 0px- 480px */
@media screen and (max-width: 480px) {


}


/* 481px - 768px tablets  landscape */
@media screen and (max-width: 768px) and (orientation: landscape) {

}

/* 481px - 768px tablets */
@media screen and (max-width: 768px)  and (orientation: portrait) {

}

/* 769px - 1024px */
@media screen and (max-width: 1024px) {

}

/* 1025px - 1200px */
@media screen and (max-width: 1200px) {

}

/* 1021px and higher */
@media screen and (min-width: 1201px) {
    .report-right {
        height: 98vh;
        overflow-y: auto;
    }
}
