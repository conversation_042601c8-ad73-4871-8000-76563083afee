<!DOCTYPE html>
<?php
require("php/conn.php");
$shopid = $_COOKIE['shopid'];
if($shopid>=4277 || $shopid=="4150")
{
  header("location:myaccount.php");
  exit;
}

$methodlink = '';

$url = "https://api.armatic.com/customers?query=" . $shopid;

$curl = curl_init($url);
curl_setopt($curl, CURLOPT_URL, $url);
curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

$headers = array(
    "Accept: application/json",
    "Authorization: Bearer YcRuU2VlFBs25r99avyhJC1fsV25Uoia8cOJwWHshq9u_sJCCs9y0vJ1sGOjxaiJ8ljKfbgqCrqpW25Z6eCgfAPgfo8VeBE1WXg=",
);
curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

$res = curl_exec($curl);
curl_close($curl);

$json = json_decode($res, true);

$found = false;

if (isset($json['customers'])) {
    foreach ($json['customers'] as $shop) {
        if ($shop['account_number'] === $shopid) {
            $methodlink = $shop['payment_method_link'];
            break;
        }
    }
}
?>
<head>
<meta content="en-us" http-equiv="Content-Language" />
<meta content="text/html; charset=windows-1252" http-equiv="Content-Type" />
<title>Untitled 1</title>
<style type="text/css">
.cmenu{
  background-color:#F0F0F0;
  text-align:center;
  color:#800000;
  font-size:12px;
  font-weight:bold;
  border:1px gray outset;
  cursor:pointer;
  height:80px;
  width:6.0%;
  font-family:Arial;
}

.style4 {
  text-align: center;
  color: #FFFFFF;
  background-image: url('newimages/pageheader.jpg');
  border-bottom: 2px black solid;
  font-family: Arial, Helvetica, sans-serif;
}

.style5 {
  font-family: Arial, Helvetica, sans-serif;
}

.style6 {
  font-size: small;
}

.style7 {
  font-family: Arial, Helvetica, sans-serif;
  color: #800000;
}

.style2 {
  text-align: left;
  font-family: Arial, Helvetica, sans-serif;
}
.style3 {
  text-align: left;
  margin-left: 40px;
}

</style>
<script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>

</head>

<body onload="scrollTo(0,0)">

                <table border="0" cellpadding="0" cellspacing="0" width="100%">
  
      <tr>
        <td onclick="goThere('wip.asp')"  class="cmenu">
        <img onclick="showDiv('wipdata.asp')" id="wipicon" src="newimages/Wrench.png" width="40" height="40"/><br>
        WIP</td>

        <td onclick="goThere('company.asp')"  class="cmenu">
        <img id="companyicon" src="newimages/company.png" width="40" height="40"/><br/>
    Company</td>
        <td onclick="goThere('companycustom.asp')" class="cmenu">
    <img alt="" height="32" src="newimages/mininfo.png" width="32"/><br/>Custom</td>
        <td onclick="goThere('employees.asp')" class="cmenu">
    <img alt="" height="32" src="newimages/person_icon.png" width="32"/><br/>Employee</td>
        <td onclick="goThere('timeclock.asp')" class="cmenu">
    <img alt="" height="32" src="newimages/labortimeclock.gif" width="32"/><br/>Labor<br>Timeclock</td>
        <td onclick="goThere('suppliers.asp')" class="cmenu">
    <img alt="" height="32" src="newimages/supplier.png" width="32"/><br/>Suppliers</td>
        <td onclick="location.href='followup/default.asp'" id="followbutton" onmouseout="changeMenuButtonOut('followbutton','custicon','peopleicon.png','peopleicon.png')" onmouseover="changeMenuButtonOver('followbutton','custicon','peopleicon.png','peopleicon.png')" class="cmenu">
        <img id="custicon" alt="" height="40" src="newimages/follow.png" width="40" /><br/>
        FollowUp</td>

        <td onclick="goThere('accounting/ar/ar.asp')" class="cmenu">
    <img alt="" height="32" src="newimages/accountant-icon_bigger.gif" width="26"/><br/>Accts<br/>
    Recvble</td>
        <td onclick="goThere('partcodes.asp')" class="cmenu">
    <img alt="" height="40" src="newimages/partcodes.jpg" width="40"/><br/>Part<br/>
    Codes</td>
        <td onclick="goThere('partpricematrix.asp')" class="cmenu">
    <img alt="" height="40" src="newimages/matrixicon.png" width="40"/><br/>Parts<br/>
    Matrix</td>
        <td onclick="goThere('jobdescs.asp')" class="cmenu">
    <img alt="" height="40" src="newimages/jobsicon.png" width="40"/><br/>Job Desc.</td>
        <td onclick="goThere('sources.asp')" class="cmenu">
    <img alt="" height="40" src="newimages/sourceicon.gif" width="60"/><br/>Sources</td>
        <td onclick="goThere('rotypes.asp')" class="cmenu">
    <img alt="" height="40" src="newimages/type.png" width="40"/><br/>RO Types</td>
        <td onclick="goThere('reopenro.asp')" class="cmenu">
    <img alt="" height="40" src="newimages/reopenicon.png" width="40"/><br/>Re-Open<br/>
    RO</td>
        <td onclick="goThere('cannedjobs/cannedjobs.asp')" class="cmenu">
    <img alt="" height="40" src="newimages/barrel.gif" width="40"/><br/>Canned<br/>
        Jobs</td>
        <?php
        if(strtolower($_COOKIE['merch']) =='yes'){?>
        <td onclick="goThere('merchantacct.asp')" class="cmenu">
        <img alt="" height="40" src="newimages/vmmc.jpg" width="29" /><br />
        Merchant<br />
        Account</td>
        <?php
        }
        ?>
        <td onclick="goThere('orderform.php')" class="cmenu">
        <img alt="" height="40" src="newimages/convert.gif" width="40" /><br />
        My Account</td>
      </tr>
    </table>
    
<p>
<table cellpadding="6" cellspacing="0" style="width: 100%">
  <tr>
    <td class="style4" style="width: 50%; height: 53px;border-left:0px">
    <strong>Change Payment Method</strong></td>
  </tr>
  <tr>
    <td style="width: 50%; ">
      
  <table style="width: 100%">
    <tr>
      <td class="style5" colspan="2"><?= $cmess?>
<p class="style2">This agreement is made between CSB Technologies Inc. (&quot;Provider&quot;) 
and <?= $sn?> (&quot;Subscriber&quot;) to provide access to the web-based application known as 
ShopBossPro.com (&quot;Service&quot;) in accordance with the Terms Of Service Agreement 
incorporated herein by reference.&nbsp; This Order Form (&quot;Order&quot;) shall define 
the terms under which this agreement is made.</p>
<p class="style2">1.&nbsp; Provider agrees to allow access to the Subscriber to 
its proprietary Service for the purposes of entering data, and creating Repair 
Orders.&nbsp; Repair Orders are defined as customer and vehicle information as 
well as customer repair requests entered into the Service.&nbsp; Once this data is 
entered, a Repair Order is generated indicating that a customer requested and/or 
authorized repair work to their vehicle.</p>
<p class="style2">2.&nbsp; Subscriber agrees to pay a month-to-month 
subscription fee to access the Service.&nbsp; This fee is based on Subscriber useage of the 
Service as described below</p>
<p class="style3"><span class="style5">
<strong>Gold Package</strong> - any calendar month the Subscrsiber creates 1-55 
Repair Orders in the Service, the monthly subscription fee will be $199;</span><br class="style5" />
<br class="style5" />
<span class="style5">
<strong>Platinum Package</strong> - any calendar month the Subscriber creates 55 
or more Repair Orders in the Service, the monthly subscription fee will be $299;</span></p>
    <p class="style3"><strong>I</strong><span class="style5"><strong>ntegrated 
    Estimating and 
    Parts Ordering</strong> - this is an add-on module with Labor Times and 
    Parts pricing, plus online ordering through suppliers like O'Reilly, 
    AutoZone, Advance, Carquest and many more.&nbsp; Parts and Labor are 
    added directly to your Shop Boss Pro repair order.&nbsp; This module is 
    $69.95 per month and is your one-stop estimating and ordering solution!</span><br class="style5" />
    </p>
<p class="style3"><strong>TMPS Speed</strong> - this is an add-on module 
providing extended TPMS information including TPMS re-learn proceedures, when a 
re-learn needs to occur and diagnostic assistance.&nbsp; $18.99 per month.</p>
      <p class="style3"><strong>Integrated Credit Card Processing</strong> 
      - now you can integrate your existing credit card processor right 
      into Shop Boss.&nbsp; With our new Authorize.net integration, your 
      credit card processor can be used with Shop Boss.&nbsp; Just ask 
      your current CC processor to help you setup an Authorize.net account 
      and provide us with your credentials.&nbsp; Then you can use a USB 
      card reader to swipe your customer's credit card, get approval and 
      the amount will be automagically posted to the Shop Boss invoice!&nbsp; 
      $9.99 per month.<br class="style5" />
    </p>

<p class="style5">3.&nbsp; Cancellation:&nbsp; This agreement may be cancelled by either party 
for any reason with 30 days written notice.&nbsp; Upon cancellation, Subscriber 
will be offered the data entered into the Service by the Subscriber in 
comma-delimited format (CSV).&nbsp; Provider will maintain a backup copy for 12 
months after cancellation.</p>
      <p class="style5">&nbsp;</p>
      </td>
    </tr>
<tr><td>
    <?php if (!empty($methodlink)) { ?>

          <center><a href="<?= $methodlink ?>" class="btn btn-success" target="_blank"><input type="button" value="Change Payment Method"></a></center>

      <?php } ?>
</td></tr>
   </td>
  </tr>
</table>
  </p>
  <p><br />
</p>



</body>

</html>
