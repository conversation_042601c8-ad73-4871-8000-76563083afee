<?php
ini_set("display_errors", "1");
error_reporting(E_ALL);

require CONNWOSHOPID;

$fn = isset($_REQUEST['customerfirst']) ? filter_var($_REQUEST['customerfirst'], FILTER_SANITIZE_STRING) : "";
$ln = isset($_REQUEST['customerlast']) ? filter_var($_REQUEST['customerlast'], FILTER_SANITIZE_STRING) : "";
$shopid = isset($_REQUEST['shopid']) ? filter_var($_REQUEST['shopid'], FILTER_SANITIZE_STRING) : $_COOKIE['shopid'];
$customerid = isset($_REQUEST['customerid']) ? filter_var($_REQUEST['customerid'], FILTER_SANITIZE_STRING) : "";

$runpspmts = 0;
$runropmts = 0;
$runro = 0;
$runps = 0;

$sd = isset($_REQUEST['sd']) ? filter_var($_REQUEST['sd'], FILTER_SANITIZE_STRING) : "";
$ed = isset($_REQUEST['ed']) ? filter_var($_REQUEST['ed'], FILTER_SANITIZE_STRING) : "";
$sdate = $edate = "";

if (!empty($sd) && !empty($ed)) {
    $sdate = date('Y-m-d', strtotime($sd));
    $edate = date('Y-m-d', strtotime($ed));
}

$stmt = "select CompanyName, CompanyAddress, CONCAT(CompanyCity, ', ', CompanyState,'. ',CompanyZip) as csz, CompanyPhone, logo from company where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($c, $a, $csz, $p, $logo);
    $query->fetch();
    $query->close();
}

if (!empty($logo)) {
    $shoplogo = $logo;
    $logo = "yes";
    $img = "\\fs.shopboss.aws\share\upload\"" . $shopid . "\"" . $logo;
} else {
    $logo = "no";
    $img = "";
}

$tar = array($customerid);
$shoplist = "";

$stmt = "select shopid,joinedshopid from joinedshops where shopid = ?";
if ($query = $conn->prepare($stmt)) {
    $query->bind_param("s", $shopid);
    $query->execute();
    $query->bind_result($qshopid, $joinedshopid);
    while ($query->fetch()) {
        $shoplist .= "'" . $joinedshopid . "',";
    }
    $query->close();
}

$shoplist .= "'" . $shopid . "'";
?>
<html>
<!-- Copyright 2011 - Boss Software Inc. -->
<head>
    <meta name="robots" content="noindex,nofollow">

    <title><?= $c ?></title>
    <style type="text/css">
        A {
            text-decoration: none;
        }

        A:link {
            text-decoration: none;
        }

        A:visited {
            text-decoration: none;
        }

        A:hover {
            text-decoration: none;
        }

        p, td, th, li, body {
            font-size: 10pt;
            font-family: Verdana, Arial, Helvetica
        }

        .style1 {
            text-align: right;
        }

        P.breakhere {
            page-break-after: always
        }

        .style2 {
            border-top: 1px solid #000000;
            text-align: right;
            border-bottom: 1px solid #000000;
        }

        .style3 {
            border-top: 1px solid #000000;
            border-bottom: 1px solid #000000;
        }

        .style4 {
            font-size: xx-small;
        }
    </style>

</head>
<body>
<style type="text/css">
    #popup {
        position: absolute;
        top: 100px;
        left: 100px;
        width: 600px;
        height: 500px;
        border: medium navy outset;
        text-align: center;
        color: black;
        display: none;
        z-index: 999;
        background-color: white;
    }

    #popupdropshadow {
        position: absolute;
        top: 115px;
        left: 115px;
        width: 600px;
        height: 500px;
        text-align: center;
        color: black;
        display: none;
        z-index: 998;
        background-color: black;
    }

    #popuphider {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 100%;
        height: 300%;
        background-color: gray;
        -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
        filter: alpha(opacity=70);
        -moz-opacity: .70;
        opacity: .7;
        z-index: 997;
        display: none;

    }

    .innerBox1 {
        font-weight: bold;
        background-image: url('<?= IMAGE ?>/newimages/wipheader.jpg');
        color: #FFFFFF;
        text-align: center;
    }

    .innerBox2 {
        background-color: #FFFFFF;
        text-align: center;
    }

    .auto-style1 {
        font-size: small;
        background-color: silver;
        width: 100%;
        text-align: center;
        padding: 10px;
    }
</style>

<?php

foreach ($tar as $j) {
    $stmt = "select firstname, lastname, address, customerid, customertype, CONCAT(city, ', ', state,'. ',zip) as csz   from customer where shopid in ($shoplist) and firstname = '" . $fn . "' and lastname = '" . $ln . "' and customerid = " . $j;
    if ($query = $conn->prepare($stmt)) {
        $query->execute();
        $results = $query->get_result();
        $query->close();
    }

    if ($results->num_rows > 0) {
        $rs = $results->fetch_assoc();
        ?>
        <table style="width: 100%" cellspacing="0">
            <tr>
                <td style="width: 33%; font-size:15px;"><?= strtoupper($c) ?></td>
                <td style="width: 33%" rowspan="5" valign="top">
                    <?php
                    $img = "stmtimg.asp?path=" . $img;
                    if ($logo == "yes") {
                        //    echo '<img src="' . "\\fs.shopboss.aws\share\upload\\$shopid\\$shoplogo" . '" style="height: 70pt;" width="" />';
                        echo '<img src="https://' . $_SERVER['SERVER_NAME'] . '/sbp/upload/' . $shopid . '/' . $shoplogo . '" 
                            style="width: 200px; display: block; margin: 0 auto; text-align: center;" />';
                    }
                    ?>
                </td>
                <td style="width: 33%" class="style1">Statement Date: <?= date('m/d/Y') ?></td>
            </tr>
            <tr>
                <td style="width: 33% ; font-size:12px;"><?= strtoupper($a) ?>&nbsp;</td>
                <td style="width: 33%" class="style1">
                </td>
            </tr>
            <tr>
                <td style="width: 33%; font-size:12px;"><?= strtoupper($csz) ?>&nbsp;</td>
                <td style="width: 33%">&nbsp;</td>
            </tr>
            <tr>
                <td style="width: 33%; font-size:12px;"><?= formatPhone($p) ?>&nbsp;</td>
                <td style="width: 33%">&nbsp;</td>
            </tr>
            <tr>
                <td style="width: 33%; font-size:12px;">&nbsp;</td>
                <td style="width: 33%">&nbsp;</td>
            </tr>
        </table>
        <table style="width: 100%" cellspacing="0">
            <tr>
                <td><?= $rs["firstname"] . (!empty($rs["firstname"]) ? " " : '') . $rs["lastname"] ?>&nbsp;</td>
            </tr>
            <tr>
                <td><?= $rs["address"] ?>&nbsp;</td>
            </tr>
            <tr>
                <td><?= $rs['csz'] ?>&nbsp;</td>
            </tr>
        </table>
        <br>
        <strong>
            <div class="auto-style1">Repair Orders</div>
        </strong>
        <table style="width: 100%" cellspacing="0">
            <tr>
                <td style="width: 25%" class="style3"><strong>RO # / Status</strong></td>
                <td style="width: 13%" class="style3"><strong>Date</strong></td>
                <td style="width: 12%" class="style3"><strong>Due Date</strong>&nbsp;</td>
                <td style="width: 40%" class="style3"><strong>Vehicle</strong></td>
                <td style="width: 10%" class="style2"><strong>Total</strong></td>
            </tr>
            <?php

            $stmt = "select roid, ponumber, totalprts, totallbr, totalsublet, totalfees, salestax, discountamt, fleetno, vehinfo, statusdate, status, finaldate from repairorders where shopid = '" . $shopid . "' and balance > 0.01 and customerid = " . $rs["customerid"] . " and status = 'CLOSED' and ROType != 'NO APPROVAL'";

            if (!empty($sd) && !empty($ed)) {
                $stmt .= " and statusdate >= '" . $sdate . "' and statusdate <= '" . $edate . "'";
            }
            if ($rrquery = $conn->prepare($stmt)) {
                $rrquery->execute();
                $rresults = $rrquery->get_result();
                $rrquery->close();
            }
            $tbal = 0;
            $lcntr = 0;
            if ($rresults->num_rows > 0) {
                while ($rrs = $rresults->fetch_assoc()) {
                    $ttlro = 0;
                    $tpmts = 0;
                    $ttlro = round($rrs["totalprts"] + $rrs["totallbr"] + $rrs["totalsublet"] + $rrs["totalfees"] + $rrs["salestax"] - $rrs["discountamt"], 2);

                    $acstmt = "select coalesce(sum(amt),0) as tp from accountpayments where ptype != 'lien' and shopid = '" . $shopid . "' and roid = " . $rrs["roid"];
                    if ($aquery = $conn->prepare($acstmt)) {
                        $aquery->execute();
                        $aquery->bind_result($tp);
                        $aquery->fetch();
                        $aquery->close();
                    }

                    $calcbal = doubleval($ttlro) - doubleval($tp);

                    if ($calcbal > 0.01) {

                        $runro += $ttlro;

                        if (!empty($rrs["fleetno"]) > 0) {
                            $vehdisplay = "#" . $rrs["fleetno"] . " - " . $rrs["vehinfo"];
                        } else {
                            $vehdisplay = $rrs["vehinfo"];
                        }

                        $ctype = $rs["customertype"];
                        if ($ctype == "" || trim(strtolower($ctype)) == "cash") {
                            $pdd = $rrs["statusdate"];
                        } else {
                            if (strpos(strtolower($ctype), "net") !== false) {
                                $numdays = right($ctype, 2);
                                $statusDateTs = strtotime($rrs["statusdate"]);
                                $pdd = strtotime("+ " . $numdays . " days", $statusDateTs);
                                $pdd = date("n/j/Y", $pdd);
                            } else {
                                $pdd = $ctype;
                            }
                        }
                        $daysold = dateDifference($pdd, date('Y-m-d'), '%R%a');
                        ?>
                        <tr>
                            <td style="width: 25%"><b><?= $rrs["roid"] . " / " . $rrs["status"] ?></b>&nbsp;</td>
                            <td style="width: 13%"><b><?= date('n/j/Y', strtotime($rrs["finaldate"])) ?></b>&nbsp;</td>
                            <td style="width: 12%"><strong><?= date('n/j/Y', strtotime($pdd)) ?></strong>&nbsp;</td>
                            <td style="width: 40%"><b><?= $vehdisplay ?></b>&nbsp;</td>
                            <td style="width: 10%" class="style1"><?= asDollars($ttlro) ?>&nbsp;</td>
                        </tr>
                        <?php

                        $astmt = "select pdate, ptype, pnumber, amt from accountpayments where ptype != 'lien' and shopid = '" . $shopid . "' and roid = " . $rrs["roid"];
                        if ($aquery = $conn->prepare($astmt)) {
                            $aquery->execute();
                            $aresults = $aquery->get_result();
                            $aquery->close();
                        } else {
                            echo "prepare failed : " . $conn->error;
                        }
                        if ($aresults->num_rows > 0) {
                            $tpmts = doubleval(0);
                            while ($ars = $aresults->fetch_assoc()) {
                                $tpmts += $ars["amt"];
                                $runropmts += $ars["amt"];
                                $lcntr++;
                                ?>
                                <tr>
                                    <td style="width: 25%" class="style4">Payment:<?= date('n/j/Y', strtotime($ars["pdate"])) ?>&nbsp;</td>
                                    <td style="width: 25%" class="style4" colspan="2">Paid by:<?= $ars["ptype"] ?>&nbsp;</td>
                                    <td style="width: 40%" class="style4">Number:<?= $ars["pnumber"] ?>&nbsp;</td>
                                    <td style="width: 10%" class="style1">-<span class="style4"><?= asDollars($ars["amt"]) ?>&nbsp;</span></td>
                                </tr>
                                <?php
                            }
                        }

                        if (!empty($rrs["ponumber"]) && !empty($rrs["ponumber"])) {
                            ?>
                            <tr>
                                <td class="style4" colspan="3"><b>PO Number: <?= $rrs["ponumber"] ?></b>&nbsp;</td>
                                <td style="width: 40%" class="style4">&nbsp;</td>
                                <td style="width: 10%" class="style1">&nbsp;</td>
                            </tr>
                            <?php
                        }
                        $tbal += $ttlro - $tpmts;
                        ?>
                        <tr>
                            <td style="width: 20%">&nbsp;</td>
                            <td style="width: 20%" colspan="2">&nbsp;</td>
                            <td style="width: 40%" class="style1"><strong>Balance: </strong></td>
                            <td style="width: 20%" class="style1"><strong><?= asDollars($ttlro - $tpmts) ?>&nbsp;</strong></td>
                        </tr>
                        <tr>
                            <td style="border-bottom:1px black solid;text-align:left" class="style1" colspan="2">
                                <?php
                                if ($shopid <> "7260") {
                                    ?>
                                    (<?= $daysold ?> days past due)
                                    <?php
                                }
                                ?>
                            </td>
                            <td style="border-bottom:1px black solid;" class="style1" colspan="2">
                                <strong>Running Balance:</strong></td>
                            <td style="border-bottom:1px black solid;width: 20%" class="style1"><b><?= asDollars($tbal) ?></b></td>
                        </tr>
                        <?php
                    }
                }
            }
            ?>
        </table>
        <br>
        <?php
        $lcntr = 0;
        
        $stmt = "select * from ps where shopid = '" . $shopid . "' and balance > 0.01 and cid = " . $rs["customerid"] . " and ucase(status) = 'CLOSED' ";
        if (!empty($sd) && !empty($ed)) {
            $stmt .= " and statusdate >= '" . $sd . "' and statusdate <= '" . $ed . "'";
        }
        if ($pquery = $conn->prepare($stmt)) {
            $pquery->execute();
            $presults = $pquery->get_result();

            if ($presults->num_rows > 0) {
                ?>
                <div class="auto-style1"><strong><?php if ($shopid == "6728") { ?>Finance Charges<?php } else { ?>Part Sales<?php } ?>
                    </strong></div>
                <table style="width: 100%" cellspacing="0">
                <tr>
                    <td style="width: 25%" class="style3"><strong>INV # / Status</strong></td>
                    <td style="width: 25%" class="style3"><strong>Date</strong></td>
                    <td style="width: 25%" class="style3"><strong>Due Date</strong></td>
                    <td style="width: 25%" class="style3"><strong>&nbsp;</strong></td>
                    <td style="width: 10%" class="style2"><strong>Total</strong></td>
                </tr>
                <?php
                while ($rrs = $presults->fetch_assoc()) {
    
                    $acstmt = "select coalesce(sum(amt),0) as tp from `accountpayments-ps` where shopid = '" . $shopid . "' and psid = " . $rrs["psid"];
    
                    if ($acquery = $conn->prepare($acstmt)) {
                        $acquery->execute();
                        $acquery->bind_result($tp);
                        $acquery->fetch();
                        $acquery->close();
                    } else {
                        die("407 " . $conn->error);
                    }
    
                    $calcbal = doubleval($rrs["total"]) - doubleval($tp);
                    if ($calcbal > 0.01) {
    
                        $runps += $rrs["total"];
                        $ctype = $rs["customertype"];
    
                        if ($ctype == "" || trim(strtolower($ctype)) == "cash") {
                            $pdd = date('n/j/Y', strtotime($rrs["statusdate"]));
                        } else {
                            if (right($ctype, 2) == "10" || right($ctype, 2) == "15" || right($ctype, 2) == "30") {
                                $numdays = right($ctype, 2);
                                $statusDateTs = strtotime($rrs["statusdate"]);
                                $pdd = strtotime("+ " . $numdays . " days", $statusDateTs);
                                $pdd = date("n/j/Y", $pdd);
                            } else {
                                $pdd = $ctype;
                            }
                        }
                        ?>
                        <tr>
                            <td style="width: 25%"><b><?= $rrs["psid"] . " / " . $rrs["status"] ?></b>&nbsp;</td>
                            <td style="width: 13%"><b><?= date('n/j/Y', strtotime($rrs["statusdate"])) ?></b>&nbsp;</td>
                            <td style="width: 12%"><b><?= $pdd ?></b>&nbsp;</td>
                            <td style="width: 25%"><strong></strong></td>
                            <td style="width: 10%" class="style1"><?= asDollars($rrs["total"]) ?>&nbsp;</td>
                        </tr>
                        <?php
                        $astmt = "select * from `accountpayments-ps` where shopid = '" . $shopid . "' and psid = " . $rrs["psid"];
                        if ($aquery = $conn->prepare($astmt)) {
                            $aquery->execute();
                            $aresults = $aquery->get_result();
                            $aquery->close();
                        } else {
                            die("441 " . $conn->error);
                        }
                        if ($aresults->num_rows > 0) {
                            $tpmts = doubleval(0);
                            while ($ars = $aresults->fetch_assoc()) {
                                $tpmts += $ars["amt"];
                                $runpspmts += $ars["amt"];
                                $lcntr++;
                                ?>
                                <tr>
                                    <td style="width: 25%" class="style4">Payment:<?= date('n/j/Y', strtotime($ars["pdate"])) ?>&nbsp;</td>
                                    <td style="width: 25%" class="style4" colspan="2">Paid by:<?= $ars["ptype"] ?>&nbsp;</td>
                                    <td style="width: 40%" class="style4">Number:<?= $ars["pnumber"] ?>&nbsp;</td>
                                    <td style="width: 10%" class="style1">-<span class="style4"><?= asDollars($ars["amt"]) ?>&nbsp;</span></td>
                                </tr>
                                <?php
                            }
                        }
                        if (!empty($rrs["ponumber"])) {
                            ?>
                            <tr>
                                <td class="style4" colspan="3"><b>PO Number: <?= $rrs["ponumber"] ?></b>&nbsp;</td>
                                <td style="width: 40%" class="style4">&nbsp;</td>
                                <td style="width: 10%" class="style1">&nbsp;</td>
                            </tr>
                            <?php
                        }
                        $tbal += $ttlro - $tpmts;
                        ?>
                        <tr>
                            <td style="border-bottom:1px black solid;width: 25%">&nbsp;</td>
                            <td style="border-bottom:1px black solid;width: 25%" colspan="2">&nbsp;</td>
                            <td style="border-bottom:1px black solid;width: 40%" class="style1"><strong>Balance: </strong></td>
                            <td style="border-bottom:1px black solid;width: 10%" class="style1"><strong><?= asDollars($rrs["balance"]) ?></strong></td>
                        </tr>
                        <?php
                    }
                }
            }

            $pquery->close();
        }
    }
    ?>

    </table>
    <div style="font-size:medium;text-align:center;">Total Due: <?= asDollars($runro + $runps - $runropmts - $runpspmts) ?><br></div>
    <?php
}
?>
</body>
</html>